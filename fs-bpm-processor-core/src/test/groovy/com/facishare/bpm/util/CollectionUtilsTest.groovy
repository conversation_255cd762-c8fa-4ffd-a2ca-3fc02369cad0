package com.facishare.bpm.util

import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification

/**
 * @since 7.5.0* <AUTHOR>
 * @creat_date: 2021/6/28
 * @creat_time: 17:46
 */
class CollectionUtilsTest extends Specification {
  def "测试去交集"() {
    when:
    def result = a
    if (b != null) {
      result = CollectionUtils.disjunction(a, b)
    }
    then:
    result == c
    where:
    a          | b          || c
    ["a", "b"] | []         || ["a", "b"]
    ["a", "b"] | ["b"]      || ["a"]
    ["a", "b"] | ["a"]      || ["b"]
    ["a", "b"] | ["a", "b"] || []
    ["a", "b"] | ["a", "b"] || []
    ["a", "b"] | null       || ["a", "b"]
  }
}
