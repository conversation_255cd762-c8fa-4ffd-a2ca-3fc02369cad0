package com.facishare.bpm;

import com.facishare.rest.core.model.RemoteContext;
import lombok.Setter;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by wangzhx on 2019/11/15.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "applicationContext.xml")
public class BaseTest {
    private static final String configKey = "spring.profiles.active";
    private static final String fs_test = "fstest";

    @Setter
    protected String tenantId = "74203";
    protected RemoteContext context = new RemoteContext("", tenantId, "BPM", "1000");

    @BeforeClass
    public static void setConfig() {
        System.setProperty(configKey, fs_test);
    }

}
