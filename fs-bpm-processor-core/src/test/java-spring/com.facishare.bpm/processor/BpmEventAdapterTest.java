package com.facishare.bpm.processor;

//import com.facishare.bpm.processor.handler.BpmEventAdapter;
import com.facishare.paas.workflow.bus.api.InstanceEndEvent;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.model.MQContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/3/25 12:04 PM
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "applicationContext.xml")
@Slf4j
public class BpmEventAdapterTest {


    /*@Autowired
    private BpmEventAdapter bpmEventAdapter;

    @Test
    public void testInstanceEnd() {

        InstanceEndEvent event = InstanceEndEvent.create(MQContext.create("71557","BPM" ,"" ), FlowTag.workflow_bpm,
                "", "5e7ae1cd319d192a58d7b6fe",null ,"" ,"","");
        bpmEventAdapter.instanceEnd(event);
    }*/
}
