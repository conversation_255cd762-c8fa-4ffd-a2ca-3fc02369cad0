package com.facishare.bpm.processor;


import com.facishare.bpm.model.RelevantTeam;
import com.facishare.bpm.service.BPMSyncDefinitionService;

import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "applicationContext.xml")
@Slf4j
public class BPMSyncDefinitionServiceImplTest {

    @Autowired
    private BPMSyncDefinitionService bpmSyncDefinitionService;


    /**
     * 初始化内容如下
     * 1. 流程定义 ﻿WorkflowOutline
     * 2. 扩展数据 ﻿WorkflowExtension
     *
     *
     * 疑问:
     * 1. 是否初始化已删除的数据(停用)
     * 2. 是否需要校验分版信息
     */
    @Test
    public void syncDefinitionTest() {
        bpmSyncDefinitionService.syncDefinition("73005","73019",false,null);
    }


    @Test
    public void test(){
        Map<String,Object> mdTask = Maps.newHashMap();
        mdTask.put(TransferDataConstants.MDField.relevant_team.getValue(), RelevantTeam.getFromCandidateIds(Lists.newArrayList("1","2")));
        Set<RelevantTeam> set = (Set<RelevantTeam>) mdTask.get(TransferDataConstants.MDField.relevant_team.getValue());
        RelevantTeam outRelevantTeam = new RelevantTeam(Sets.newHashSet("2132149302940"), RelevantTeam.TeamMemberRole.common.getValue(),
                RelevantTeam.TeamMemberPermissionType.readOnly.getValue(), "2021321", RelevantTeam.SourceType.outUser.getValue());
        set.add(outRelevantTeam);
        System.out.println(JsonUtil.toJson(mdTask));
    }
}
