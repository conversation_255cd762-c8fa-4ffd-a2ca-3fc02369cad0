package com.facishare.bpm.processor;

import com.alibaba.fastjson.JSON;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.rest.core.util.JsonUtil;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.bson.types.ObjectId;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 5.7
 */
public class BPMDataChangeProcessor {
    public static AtomicInteger count=new AtomicInteger(0);
    private AutoConfRocketMQProcessor processor;

    public BPMDataChangeProcessor() {
        this.processor = new AutoConfRocketMQProcessor("fs-bpm-task-instance-change-mq", (MessageListenerOrderly) (msgs, consumeOrderlyContext) -> {
            msgs.forEach(msg -> {
                try {
                    process(new String(msg.getBody()));
                } catch (Throwable e) {
                    System.out.println(new String(msg.getBody()));
                }
            });
            return ConsumeOrderlyStatus.SUCCESS;
        }) {
        };
        this.processor.init();
    }

    private void process(String s) {
        System.out.println(count.addAndGet(1)+"---------"+JsonUtil.toPrettyJson(JsonUtil.fromJson(s, HashMap.class)));
    }

    public static void main(String[] args) throws InterruptedException {
        new BPMDataChangeProcessor();
        TimeUnit.HOURS.sleep(10);
    }

    @Test
    public void testParseObject(){
        String msg = "{\"_id\":\""+new ObjectId()+"\"}";
        Map<String, Object> workflowInstance = JSON.parseObject(msg, HashMap.class);
        if(workflowInstance.get("_id") instanceof ObjectId){
            System.out.println(true);
        }else{
            System.out.println(false);
        }
    }
}
