package com.facishare.bpm.processor.handler;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class BpmEventHandlerTest extends BaseTest {

    @Autowired
    private BpmEventHandler bpmEventHandler;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;


    @Test
    public void sync() {
        List<String> taskIds = Lists.newArrayList("5ecc8066e75ae00001fc0255");

        RemoteContext context = new RemoteContext("", "71554", "BPM", "1007");

        for (String taskId : taskIds) {
            Task task = paasWorkflowServiceProxy.getTask(context, taskId);
//            WorkflowInstance instance = paasWorkflowServiceProxy.getWorkflowInstance(context, task.getWorkflowInstanceId());
            bpmEventHandler.findAndModifyTask(context, task, Boolean.FALSE);
        }


    }
}
