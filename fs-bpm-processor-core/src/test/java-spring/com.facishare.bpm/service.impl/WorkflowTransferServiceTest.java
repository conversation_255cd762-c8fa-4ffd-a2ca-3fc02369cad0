package com.facishare.bpm.service.impl;

import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.rest.core.model.RemoteContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/4/14 4:26 PM
 */
@Slf4j
public class WorkflowTransferServiceTest extends BaseTest {

    @Autowired
    private WorkflowTransferService workflowTransferService;

    @Autowired
    private PaasWorkflowServiceProxy workflowServiceProxy;

    private String tenantId = "71557";
    private String instanceId = "5ed73cb7fc4d760001501a61";

    private RemoteContext context = new RemoteContext(null, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);

    //第一个节点未解析到处理人
    //实例未同步到自定义对象
    //实例cancel后同步
    @Test
    public void newMethod() {
        workflowTransferService.transfer(context, instanceId);
    }
}
