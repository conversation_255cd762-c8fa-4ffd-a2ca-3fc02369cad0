package com.facishare.bpm.service.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: WangSong
 * @Date: 2018/8/10 12:05
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("applicationContext.xml")
public class TestWorkflowInitialServiceImpl {
    @Autowired
    private WorkflowInitialServiceImpl service;
    @Test
    public void testDestroy(){
        service.destroyTenant("58335",false);
    }
}
