package com.facishare.bpm.processor.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMInstanceException;
import com.facishare.bpm.manage.TodoSessionKeyManager;
import com.facishare.bpm.model.RelevantTeam;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.meta.BPMTaskHandleTimeDetailObj;
import com.facishare.bpm.model.meta.BPMTaskObj;
import com.facishare.bpm.model.meta.BPMTaskOpinionObj;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.app.GetAppActions;
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate;
import com.facishare.bpm.proxy.FlowServiceSessionProxy;
import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.proxy.OrganizationServiceProxy;
import com.facishare.bpm.remote.app.ErAppProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.CalculateTaskHandleTimeDetailUtil;
import com.facishare.bpm.utils.DataTransferUtils;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.repository.BPMTaskRepository;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.workflow.bus.api.ChangeCandidateIdsEvent;
import com.facishare.paas.workflow.bus.api.CreateTaskEvent;
import com.facishare.paas.workflow.bus.api.MessageEvent;
import com.facishare.paas.workflow.bus.api.TaskHandledEvent;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.SUSPEND_OPERATE;

/**
 * desc: 任务相关封装
 * version: 7.0.0
 * Created by cuiyongxu on 2020/2/17 3:24 PM
 */
@Slf4j
@Component
public class BpmEventHandler {


    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private WorkflowTransferService workflowTransferService;

    @Autowired
    private OrganizationServiceProxy organizationServiceProxy;

    @Autowired
    private DefinitionExtensionDao extensionDao;

    @Autowired
    private BPMTaskRepository bpmTaskRepository;

    @Autowired
    private TodoSessionKeyManager getTodoSessionKeyManager;

    @Autowired
    private ErAppProxy erAppProxy;

    @Autowired
    private FlowServiceSessionProxy sessionProxy;

    @Autowired
    private ManageGroupProxy manageGroupProxy;

    /**
     * 创建流程实例
     * <p>
     * 已存在,返回map
     * 不存在,返回空
     *
     * @param context
     * @param instanceId
     */
    public BPMInstanceObj createInstance(RemoteContext context, String instanceId) {
        BPMInstanceObj mateInstance = findMetaInstanceById(context,  instanceId);
        //不需要重试,如果没有的话 更新任务的时候会创建
        if (Objects.isNull(mateInstance)) {
            return workflowTransferService.transfer(context, instanceId);
        }
        return mateInstance;
    }


    /**
     * 创建任务
     *
     * @param context
     * @param task
     * @param metadataInstance
     * @param getEmployeeFunction
     */
    protected void createTask(
            RemoteContext context,
            Task task,
            BPMInstanceObj metadataInstance,
            Function<List<String>, Map<Integer, Employee>> getEmployeeFunction) {
        //  创建任务
        //2020年05月22日16:56:40   由于终端 转发时,企信测会调用自定义对象查询数据,无人员数据不会同步到自定义对象,导致差不到,页面一直显示 正在加载中,
        // 未解析到人也同步到自定义对象,下发转发按钮的时候  也需要判断
        List<PoolEntity> pools = extensionDao.getPools(context.getTenantId(), task.getWorkflowId());
        BPMTaskObj mdTask = DataTransferUtils.transferTask(task, metadataInstance, pools, true, getEmployeeFunction, getTodoSessionKeyManager.getSessionKey(context, task.getEntityId(), task.getExecutionType(), task.getExternalApplyActionCode(), task.getExternalFlow(), task.getId(), task.getElementApiName()), task.calculateActualDuration(context, manageGroupProxy));
        if(ObjectUtils.allNotNull(task.getEntityId(), task.getObjectId())){
            mdTask.setObject_record_type(getRecordType(context, task.getEntityId(), task.getObjectId()));
            mdTask.setBusiness_code(getTaskBusinessCode(context, task));
        }

        metadataService.createData(context, TransferDataConstants.APINAME_TASK, JSON.toJSONString(mdTask));
        log.info("BPMEventBus createTask : success! TENANT_ID={}, TASK_ID={}", context.getTenantId(), task.getId());
    }

    public String getRecordType(RemoteContext context, String descApiName, String dataId) {
        try {
            return (String) metadataService.findDataById(context, descApiName, dataId, false, false, false, false, false, false, null).getObject_data().get(BPMConstants.RECORD_TYPE);
        }catch (Exception e){

        }
        return null;
    }

    /**
     * 更新任务
     *
     * @param context
     * @param metadataTask
     * @param metadataInstance
     * @param task
     * @param getEmployeeFunction
     */
    protected void updateTask(
            RemoteContext context,
            BPMTaskObj metadataTask,
            BPMInstanceObj metadataInstance,
            Task task,
            Function<List<String>, Map<Integer, Employee>> getEmployeeFunction,
            boolean isBatch) {
        BPMTaskObj mdTask = DataTransferUtils.transferTask(task, metadataInstance, null, false, getEmployeeFunction, null, task.calculateActualDuration(context, manageGroupProxy));
        TraceContext.get().setSourceProcessId(task.getSourceWorkflowId());
        metadataService.updateData(context, TransferDataConstants.APINAME_TASK, task.getId(), JSON.toJSONString(mdTask), false, isBatch);
        log.info("BPMEventBus updateTask Update Task Success! TENANT_ID={}, TASK_ID={}", context.getTenantId(), task.getId());
    }

    /**
     * 创建审批任务回复
     *
     * @param context
     * @param task
     */
    protected void createTaskOpinion(
            RemoteContext context,
            Task task) {
        if(CollectionUtils.isEmpty(task.getOpinions())
                || !ExecutionTypeEnum.approve.equals(task.getExecutionType())){
            return;
        }
        for (Opinion opinion : task.getOpinions()) {
            if(StringUtils.isBlank(opinion.getId())){
                log.info("opinionId is blank, taskId:{}", task.getId());
                continue;
            }
            BPMTaskOpinionObj metadataTaskOpinion = findMetaTaskOpinionById(context, opinion.getId());
            if(Objects.isNull(metadataTaskOpinion)) {
                BPMTaskOpinionObj mdTaskOpinion = DataTransferUtils.transferTaskOpinion(task, opinion);
                metadataService.createData(context, TransferDataConstants.APINAME_TASK_OPINION, JSON.toJSONString(mdTaskOpinion));
            }
        }
    }

    /**
     * 同步暂不处理意见
     * @param context
     * @param task
     */
    protected void createTaskSuspendOpinion(
            RemoteContext context,
            Task task) {
        if(CollectionUtils.isEmpty(task.getOperateLogs())){
            return;
        }
        List<OperateLog> suspendOperate = task.getOperateLogs().stream().filter(o -> SUSPEND_OPERATE.equals(o.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(suspendOperate)) {
            return;
        }
        for (OperateLog operate : suspendOperate) {
            if(StringUtils.isBlank(operate.getId())){
                log.info("operateId is blank, taskId:{}", task.getId());
                continue;
            }
            BPMTaskOpinionObj metadataTaskOpinion = findMetaTaskOpinionById(context, operate.getId());
            if(Objects.isNull(metadataTaskOpinion)) {
                BPMTaskOpinionObj mdTaskSuspendOpinion = DataTransferUtils.transferTaskSuspendOpinion(task, operate);
                metadataService.createData(context, TransferDataConstants.APINAME_TASK_OPINION, JSON.toJSONString(mdTaskSuspendOpinion));
            }
        }
    }

    public void findAndModifyTask(RemoteContext context, Task task, boolean isBatch) {
        String instanceId = task.getWorkflowInstanceId();
        String taskId = task.getId();
        /**
         * 已存在,返回map
         * 不存在,创建并返回空
         */
        BPMInstanceObj mateInstance = createInstance(context, instanceId);
        if(Objects.nonNull(mateInstance) && Boolean.TRUE.equals(mateInstance.getIs_deleted())){
            return;
        }
        //查找实例
        if (Objects.nonNull(mateInstance)) {
            //是否需要更新实例
            if (shouldUpdateInstance(mateInstance, task)) {
                Map<Integer, Employee> employees = organizationServiceProxy.getOutMembersByIds(context, new ArrayList<>(task.getCandidateIds()));

                Set<String> referenceObjectIds = bpmTaskRepository.getTaskObjectIdByInstanceId(context.getTenantId(), instanceId);

                DataTransferUtils.setParticipantsAndObjectIds(mateInstance, Lists.newArrayList(task), employees, referenceObjectIds);
                TraceContext.get().setSourceProcessId(task.getSourceWorkflowId());
                metadataService.updateData(context, TransferDataConstants.APINAME_INSTANCE, instanceId, JSON.toJSONString(mateInstance), false, isBatch);
                log.info("BPMEventBus CreateTaskHandler Update Instance Success! TENANT_ID={}, INSTANCE_ID={}, TASK_ID={}", context.getTenantId(), instanceId, taskId);
            }
            //查询任务是否在自定义对象中已经存在
            BPMTaskObj metadataTask = findMetaTaskById(context, taskId);
            Function<List<String>, Map<Integer, Employee>> getEmployeeFunction = userIds -> organizationServiceProxy.getOutMembersByIds(context, Lists.newArrayList(userIds));
            if (Objects.isNull(metadataTask)) {
                createTask(context, task, mateInstance, getEmployeeFunction);
            } else {
                updateTask(context, metadataTask, mateInstance, task, getEmployeeFunction, isBatch);
                createTaskOpinion(context, task);
                //同步暂不处理意见
                createTaskSuspendOpinion(context, task);
            }
        } else {
            log.warn("当前同步任务:{},实例:{}未被同步成功到元数据,执行重试操作", task.getId(), instanceId);
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SYNCHRONIZATION_TASK_NOT_FOUND_INSTANCE_RETRY_QUEUE);
        }
        //删除完成或终止任务对应草稿
        if(Objects.nonNull(task)
                && (task.getCompleted() || TaskState.cancel.equals(task.getState()))
                && ((ExecutionTypeEnum.update.equals(task.getExecutionType()) && task.isUsedLayout())
                    || (ExecutionTypeEnum.updateLookup.equals(task.getExecutionType()) && task.isUsedLayout())
                    || (ExecutionTypeEnum.approve.equals(task.getExecutionType()) && Boolean.TRUE.equals(task.getFormEditable()) && task.isUsedLayout())
                    || ExecutionTypeEnum.addRelatedObject.equals(task.getExecutionType())
                    || ExecutionTypeEnum.batchAddRelatedObject.equals(task.getExecutionType())
                    || ExecutionTypeEnum.batchEditMasterDetailObject.equals(task.getExecutionType())
                    || ExecutionTypeEnum.addMDObject.equals(task.getExecutionType())
                   )
        ){
            metadataService.deleteTaskDraft(context, task.getId());
        }
    }

    public void updateTaskHandleTimeDetail(RemoteContext context, Task task, MessageEvent event) {
        if(Objects.isNull(task.getVersion()) || task.getVersion() < 880){
            return;
        }

        String operateId = null;
        if(event instanceof CreateTaskEvent){
            operateId = ((CreateTaskEvent)event).getOperateId();
        }else if (event instanceof ChangeCandidateIdsEvent){
            operateId = ((ChangeCandidateIdsEvent)event).getOperateId();
        }else if (event instanceof TaskHandledEvent){
            operateId = ((TaskHandledEvent)event).getOperateId();
        }

        if(StringUtils.isBlank(operateId)){
            return;
        }

        CalculateTaskHandleTimeDetailUtil.TaskInfo taskInfo = CalculateTaskHandleTimeDetailUtil.TaskInfo.transfer(task);
        try {
            CalculateTaskHandleTimeDetailUtil.ModifyInfo modifyInfo = CalculateTaskHandleTimeDetailUtil.getTaskHandleTimeDetailModifyInfo(taskInfo, operateId);
            modifyInfo.fillActualDuration(context, manageGroupProxy);
            if(CollectionUtils.isNotEmpty(modifyInfo.getUpdate())){
                //批量修改
                List<Map<String, Object>> updateList = modifyInfo.getUpdate().stream().map(o -> {
                   Map<String, Object> map = Maps.newHashMap();
                    map.put("_id", o.get_id());
                    map.put(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_END_TIME_FIELD, o.getEnd_time());
                    map.put(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_ACTUAL_DURATION_FIELD, o.getActual_duration());
                   return map;
                }).collect(Collectors.toList());
                metadataService.batchUpdateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, updateList);

            }
            if(CollectionUtils.isNotEmpty(modifyInfo.getAdd())){
                //批量添加
                metadataService.batchCreateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, JSON.toJSONString(modifyInfo.getAdd()));
            }
        }catch (Exception e){
            try {
                //修改或添加数据
                retryUpdateAndAddTaskHandleTimeDetail(context, taskInfo);
            }catch (Exception newException){
                log.info("同步任务耗时信息失败，tenantId:{},taskId:{}",context.getTenantId(), task.getId(),e);
                List<Integer> notifyUsers = SwitchConfigManager.operateFailNoticeSessionUserId();
                if(CollectionUtils.isNotEmpty(notifyUsers)){
                    String content = "业务流任务同步耗时信息失败，请及时手动重新同步。tenantId：" + context.getTenantId() + "，taskId：" + task.getId();//ignoreI18n
                    sessionProxy.sendTextMessage(notifyUsers, content);
                }
            }
        }
    }

    public void retryUpdateAndAddTaskHandleTimeDetail(RemoteContext context, CalculateTaskHandleTimeDetailUtil.TaskInfo taskInfo) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filterList = Lists.newArrayList();

        Filter taskIdFilter = new Filter();
        taskIdFilter.setFieldName(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_TASK_ID_FIELD);
        taskIdFilter.setOperator(Operator.EQ);
        taskIdFilter.setFieldValues(Lists.newArrayList(taskInfo.getId()));
        filterList.add(taskIdFilter);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_TASK_API_NAME_FIELD);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(TransferDataConstants.APINAME_TASK));
        filterList.add(apiNameFilter);

        query.setFilters(filterList);
        query.setLimit(1000);
        FindDataBySearchTemplate.Result result = metadataService.findDataBySearchTemplate(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, query);
        FindDataBySearchTemplate.QueryResultInfo queryResultInfo = result.getData().getQueryResult();
        List<Map<String, Object>> data = queryResultInfo.getData();
        List<BPMTaskHandleTimeDetailObj> originalList =  JacksonUtil.fromJson(JacksonUtil.toJson(data), new TypeReference<List<BPMTaskHandleTimeDetailObj>>() {});

        CalculateTaskHandleTimeDetailUtil.ModifyInfo allModifyInfo = CalculateTaskHandleTimeDetailUtil.getTaskHandleTimeDetailModifyInfo(taskInfo, "");

        CalculateTaskHandleTimeDetailUtil.ModifyInfo modifyInfo = CalculateTaskHandleTimeDetailUtil.comparisonAcquisitionUpdateAndAdd(originalList, allModifyInfo.getAdd());
        modifyInfo.fillActualDuration(context, manageGroupProxy);
        if(CollectionUtils.isNotEmpty(modifyInfo.getUpdate())){
            //批量修改
            List<Map<String, Object>> updateList = modifyInfo.getUpdate().stream().map(o -> {
                Map<String, Object> map = Maps.newHashMap();
                map.put("_id", o.get_id());
                map.put(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_END_TIME_FIELD, o.getEnd_time());
                map.put(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_ACTUAL_DURATION_FIELD, o.getActual_duration());
                return map;
            }).collect(Collectors.toList());
            metadataService.batchUpdateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, updateList);

        }
        if(CollectionUtils.isNotEmpty(modifyInfo.getAdd())){
            //批量添加
            metadataService.batchCreateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, JSON.toJSONString(modifyInfo.getAdd()));
        }
    }


    private Map<String, Object> findDataById(RemoteContext context, String entityId, String objectId) {
        try {
            return metadataService.findDataById(context, entityId, objectId, false, false, false, true, true, false, true, null).getObject_data();
        } catch (Throwable e) {
            log.warn("调用自定义对象查询实例或任务,未查询到:{},{},{}", context, entityId, objectId);
            return null;
        }
    }

    private BPMInstanceObj findMetaInstanceById(RemoteContext context, String objectId) {
        return JacksonUtil.fromJson(JacksonUtil.toJson(findDataById(context,TransferDataConstants.APINAME_INSTANCE,objectId)),BPMInstanceObj.class);
    }

    private BPMTaskObj findMetaTaskById(RemoteContext context, String objectId) {
        return JacksonUtil.fromJson(JacksonUtil.toJson(findDataById(context,TransferDataConstants.APINAME_TASK,objectId)),BPMTaskObj.class);
    }

    private BPMTaskOpinionObj findMetaTaskOpinionById(RemoteContext context, String objectId) {
        return JacksonUtil.fromJson(JacksonUtil.toJson(findDataById(context,TransferDataConstants.APINAME_TASK_OPINION,objectId)),BPMTaskOpinionObj.class);
    }

    /**
     * 判断当前 任务 中的团队成员是否没有全包含在 实例的团队成员中
     * 或者
     * 任务的落地对象没有全部在实例的所涉及的对象列表中
     *
     * @param mateInstance
     * @param task
     * @return
     */
    private boolean shouldUpdateInstance(BPMInstanceObj mateInstance, Task task) {
        Set<RelevantTeam> instanceParticipants = mateInstance.getRelevant_team();
        Set<String> instanceParticipantIds = Sets.newHashSet();

        instanceParticipants.forEach(participant -> {
            if (CollectionUtils.isNotEmpty(participant.getTeamMemberEmployee())) {
                instanceParticipantIds.addAll(participant.getTeamMemberEmployee());
            }
        });

        Collection<String> taskParticipantIds = task.getCandidateIds();

        Set<String> instanceObjectIds = mateInstance.getObjectIds();
        String taskObjectId = task.getObjectId();
        if (CollectionUtils.isEmpty(taskParticipantIds) || Strings.isNullOrEmpty(taskObjectId)) {
            return false;
        }
        return !instanceParticipantIds.containsAll(taskParticipantIds) || !instanceObjectIds.contains(taskObjectId);
    }


    public void findAndModifyInstance(RemoteContext context, WorkflowInstance workflowInstance, List<String> currentCandidateIds) {
        List<PoolEntity> pools = extensionDao.getPools(context.getTenantId(), workflowInstance.getWorkflowId());
        BPMInstanceObj mdInstance;
        BPMInstanceObj instance = findMetaInstanceById(context, workflowInstance.getId());
        if(Objects.nonNull(instance) && Boolean.TRUE.equals(instance.getIs_deleted())){
            return;
        }
        if (Objects.isNull(instance)) {
            mdInstance = getMDInstance(context, workflowInstance, pools, true);
            mdInstance.setCurrent_candidate_ids(currentCandidateIds.stream().collect(Collectors.toSet()));
            metadataService.createData(context, TransferDataConstants.APINAME_INSTANCE, JSON.toJSONString(mdInstance));
            log.info("BPMEventBus findAndModifyInstance instance to createInstance : success! TENANT_ID={}, INSTANCE_ID={}", context.getTenantId(), workflowInstance.getId());
        } else {
            mdInstance = getMDInstance(context, workflowInstance, pools, false);
            mdInstance.setCurrent_candidate_ids(currentCandidateIds.stream().collect(Collectors.toSet()));
            TraceContext.get().setSourceProcessId(workflowInstance.getSourceWorkflowId());
            metadataService.updateData(context, TransferDataConstants.APINAME_INSTANCE, mdInstance.get_id(), JSON.toJSONString(mdInstance), false, Boolean.FALSE);
            log.info("BPMEventBus findAndModifyInstance updateInstance : success! TENANT_ID={}, INSTANCE_ID={}", context.getTenantId(), mdInstance.get_id());
        }
    }


    private BPMInstanceObj getMDInstance(RemoteContext context, WorkflowInstance workflowInstance,
                                              List<PoolEntity> pools, boolean isNewData) {

        Set<String> referenceObjectIds = bpmTaskRepository.getTaskObjectIdByInstanceId(context.getTenantId(), workflowInstance.getId());

        BPMInstanceObj mdInstance = DataTransferUtils.transferInstance(workflowInstance, pools, isNewData, referenceObjectIds, workflowInstance.calculateActualDuration(context, manageGroupProxy));
        if (isNewData) {
            workflowTransferService.setMetadataOwner(context, mdInstance,
                    mdInstance.getApplicantId().stream().findFirst().get(),
                    mdInstance.getObjectApiName(),
                    mdInstance.getObjectDataId());

            //发起人如果是下游  则将下游这个人 添加到实例的团队成员中
            //submitter  业务流中是真正的发起人  可能是上游人员  可能是下游人员  applicantId和submitter 不一定相等
            if (workflowInstance.outerTrigger()) {
                Map<Integer, Employee> employees = organizationServiceProxy.getOutMembersByIds(context, Lists.newArrayList(workflowInstance.getOuterSubmitter()));
                Set<RelevantTeam> teams = mdInstance.getRelevant_team();
                teams.addAll(RelevantTeam.getRelevantTeams(Lists.newArrayList(workflowInstance.getOuterSubmitter()), employees));
            }
        }

        return mdInstance;
    }


    public void updateInstanceState(RemoteContext context, String workflowInstanceId, String state) {
        BPMInstanceObj mateInstance = findMetaInstanceById(context,  workflowInstanceId);
        if (Objects.isNull(mateInstance) || Boolean.TRUE.equals(mateInstance.getIs_deleted())) {
            return;
        }
        Map<String, Object> data = Maps.newHashMap();
        data.put(TransferDataConstants.MDField.state.getValue(), state);
        TraceContext.get().setSourceProcessId(mateInstance.getSourceWorkflowId());
        metadataService.updateData(context,
                TransferDataConstants.APINAME_INSTANCE,
                workflowInstanceId,
                JSON.toJSONString(data),
                true, Boolean.FALSE);

    }

    private String getTaskBusinessCode(RemoteContext context, Task task){
        //是自定义节点走单独的BusinessCode配置
        if(ExecutionTypeEnum.custom.equals(task.getExecutionType())){
            return SwitchConfigManager.getCustomElementCategory(task.getEntityId(), task.getElementApiName());
        }
        //不是应用节点 类型为审批或业务节点
        if(!ExecutionTypeEnum.externalApplyTask.equals(task.getExecutionType())){
            if (ExecutionTypeEnum.approve.equals(task.getExecutionType())){
                return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.APPROVE_NODE;
            }else {
                return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.NORMAL_NODE;
            }
        }
        GetAppActions.Result appActions = erAppProxy.getAppActionsByCache(context.getTenantId(), context.getUserId(), task.getEntityId(), task.isExternalFlow(), task.getLinkApp(), task.getLinkAppType());
        //获取appCode actionCode
        String appCode = task.getExternalApplyAppCode();
        String actionCode = task.getExternalApplyActionCode();
        if(CollectionUtils.isEmpty(appActions.getData()) || StringUtils.isBlank(appCode) || StringUtils.isBlank(actionCode)){
            return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.EXTERNAL_APPLY_NODE;
        }
        List<GetAppActions.AppAction> actions = Lists.newArrayList();
        for (GetAppActions.App app : appActions.getData()) {
            if(appCode.equals(app.getAppCode()) && CollectionUtils.isNotEmpty(app.getActions())){
                actions.addAll(app.getActions());
            }
        }
        //<子节点， 根节点>
        Map<String, String> map = Maps.newHashMap();
        for (GetAppActions.AppAction action : actions) {
            String  root = action.getActionCode();
            fillActionCodeMap(action, map, root);
        }

        return StringUtils.isBlank(map.get(actionCode)) || !SwitchConfigManager.getSupportExternalApplyBusinessCode(task.getEntityId()).contains(map.get(actionCode))
                ? BPMConstants.BPM_INSTANCE_BUSINESS_CODE.EXTERNAL_APPLY_NODE
                : map.get(actionCode);
    }

    private void fillActionCodeMap(GetAppActions.AppAction action, Map map, String root){
        if(Objects.isNull(action)){
            return;
        }
        if (CollectionUtils.isEmpty(action.getChildren())){
            if(StringUtils.isNotBlank(action.getActionCode())){
                map.put(action.getActionCode(), root);
            }
            return;
        }
        for (GetAppActions.AppAction child : action.getChildren()) {
            fillActionCodeMap(child, map, root);
        }
    }
}
