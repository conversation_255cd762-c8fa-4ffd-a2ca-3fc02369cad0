package com.facishare.bpm.processor;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.service.WorkflowInitialService;
import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * @Author: WangSong
 * @Date: 2018/8/8 11:11
 *
 */
@Service
@Lazy(value = false)
@Slf4j
public class WorkflowInitialProcessor extends SandboxEvent {

    @Autowired
    private WorkflowInitialService workflowInitialService;

    @Override
    protected boolean onSandboxCreated(String module, CreateEvent createEvent) {
        log.info("接收到的消息为:{}", createEvent);
        if (!BPMConstants.APP_ID.equals(module)) {
            return false;
        }
        String fromTenantId = String.valueOf(createEvent.getFrom().getEnterpriseId());
        String toTenantId = String.valueOf(createEvent.getTo().getEnterpriseId());
        workflowInitialService.initTenant(
                fromTenantId,
                toTenantId,
                includeDescribe(createEvent.getType()),
                includeData(createEvent.getType()));
        workflowInitialService.copyAllFlowConfig(fromTenantId, toTenantId);
        return true;
    }

    @Override
    protected boolean onSandboxDestroy(String module, DestroyEvent destroyEvent) {
        log.info("接收到的消息为:{}", destroyEvent);
        if (!BPMConstants.APP_ID.equals(module)) {
            return false;
        }
        workflowInitialService.destroyTenant(
                String.valueOf(destroyEvent.getEnterpriseId()),
                includeDescribe(destroyEvent.getType()));
        workflowInitialService.deleteAllFlowConfig(String.valueOf(destroyEvent.getEnterpriseId()));
        return true;
    }

    public WorkflowInitialProcessor() {
        super("fs-bpm-processor_SANDBOX_EVENT_BPM",Module.BPM);
    }


    public boolean includeDescribe(String type) {
        return "Describe".equals(type) || "All".equals(type);
    }

    public boolean includeData(String type) {
        return "Data".equals(type) || "All".equals(type);
    }

}
