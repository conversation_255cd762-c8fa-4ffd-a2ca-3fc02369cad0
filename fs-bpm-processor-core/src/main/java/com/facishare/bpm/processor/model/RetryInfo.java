package com.facishare.bpm.processor.model;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpmn.syn.utils.RetryUtil;
import com.facishare.paas.workflow.bus.api.type.OperationType;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;


@Data
@Slf4j
public class RetryInfo {

    private RemoteContext context;
    private String taskId;
    private String instanceId;
    private String message;
    private OperationType operationType;
    private String id;//或者是instanceId 或者是taskId

    private static RetryInfo create(RemoteContext context, String id, String message, OperationType operationType) {
        RetryInfo retryInfo = new RetryInfo();
        retryInfo.setContext(context);
        retryInfo.setId(id);
        retryInfo.setMessage(message);
        retryInfo.setOperationType(operationType);
        return retryInfo;
    }


    public static void recordFailInstance(RemoteContext context, String instanceId, String errorMsg, OperationType operationType) {
        if (StringUtils.isBlank(instanceId)) {
            log.error("重试实例时,实例Id为空,context :{},instanceId:{},errorMsg:{}", context, instanceId, errorMsg);
            return;
        }
        RetryUtil.getInstance().record(BPMConstants.BIZ, BPMConstants.RETRY_INSTANCE, instanceId, BPMConstants.BPM_INSTANCE, context.getTenantId(), errorMsg, JacksonUtil.toJson(create(context, instanceId, errorMsg, operationType)));
    }

    public static void recordFailTask(RemoteContext context, String taskId, String errorMsg, OperationType operationType) {
        if (StringUtils.isBlank(taskId)) {
            log.error("重试任务时,任务Id为空,context :{},taskId:{},errorMsg:{}", context, taskId, errorMsg);
            return;
        }
        RetryUtil.getInstance().record(BPMConstants.BIZ, BPMConstants.RETRY_TASK, taskId, BPMConstants.BPM_TASK, context.getTenantId(), errorMsg, JacksonUtil.toJson(create(context, taskId, errorMsg, operationType)));
    }

}
