package com.facishare.bpm.processor;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.CancelSourceEnum;
import com.facishare.bpm.model.MetaDataChangeEvent;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.utils.SwitchConfig;
import com.facishare.bpmn.syn.component.RetryHandler;
import com.facishare.bpmn.syn.dao.model.CompensateDataEntity;
import com.facishare.bpmn.syn.utils.RetryUtil;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.common.rocketmq.util.TraceUtils;
import com.facishare.flow.repository.BPMInstanceRepository;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;


/**
 * 监听元数据作废、删除数据消息
 * Created by wangzhx on 2019/7/31.
 */
@Slf4j
@Service
public class MetaDataChangeProcessor extends BPMBaseService {
    private static final String CONFIG_NAME = "fs-bpm-metadata-change-consumer-mq";

    @Autowired
    private BPMInstanceRepository bpmInstanceRepository;

    @Autowired
    private BPMInstanceService instanceService;

    @PostConstruct
    public void init() {
        AutoConfRocketMQProcessor processor = new AutoConfRocketMQProcessor(CONFIG_NAME,
                (MessageListenerConcurrently) (messages, context) -> {
                    messages.forEach(message -> {
                        TraceUtils.setConsumerTrace(message);
                        process(message.getMsgId(), String.valueOf(message.getBornHost()), new String(message.getBody()));
                    });
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
        ) {
            @Override
            //从某个时间点开始消费，默认是半个小时以前
            public String getConsumeTimestamp() {
                return String.valueOf(System.currentTimeMillis());
            }
        };
        processor.init();
        RetryUtil.getInstance().addHandler(new RetryHandler(BPMConstants.BIZ, BPMConstants.SUB_BIZ_DESTROY) {
            @Override
            public void process(CompensateDataEntity entity) {
                log.info("fail destroy process: tenantId:{},entityId:{},objectId:{}", entity.getTenantId(), entity.getEntityId(), entity.getDataId());
                cancelProcess(entity.getTenantId(), entity.getEntityId(), entity.getDataId());
            }
        });
    }

    private void process(String messageId, String host, String message) {
        MetaDataChangeEvent metaDataChangeEvent = JsonUtil.fromJson(message, MetaDataChangeEvent.class);
        String operation = metaDataChangeEvent.getOp();
        /**
         * 如果当前企业在灰度名单中或已全网，不处理删除数据的消息
         */
        if(DataOperation.DELETE.equals(operation) && SwitchConfig.getDeleteDataGray(metaDataChangeEvent.getTenantId())) {
            return;
        }
        if (DataOperation.supportOperation(operation)) {
            String tenantId = metaDataChangeEvent.getTenantId();
            log.info("tenantId:{},messageId:{},host:{}", tenantId, messageId, host);
            List<MetaDataChangeEvent.DataBody> bodyList = metaDataChangeEvent.getBody();
            if (CollectionUtils.isEmpty(bodyList)) {
                return;
            }
            bodyList.forEach(body -> {
                String entityId = body.getEntityId();
                String objectId = body.getObjectId();
                try {
                    cancelProcess(tenantId, entityId, objectId);
                    log.info("cancel process success: tenantId:{},entityId:{},objectId:{}", tenantId, entityId, objectId);
                } catch (Throwable e) {
                    log.error("first cancel execute error,{},{},{},{}", tenantId, objectId, entityId, e.getMessage());
                    RetryUtil.getInstance().record(BPMConstants.BIZ, BPMConstants.SUB_BIZ_DESTROY, objectId, entityId, tenantId, e.getMessage(), null);
                }
            });
        }
    }

    private void cancelProcess(String tenantId, String entityId, String objectId) {
        RemoteContext context = new RemoteContext(null, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        List<String> instanceIds = bpmInstanceRepository.getInstanceIdByObjectIdsV2(context, objectId);
        if (CollectionUtils.isNotEmpty(instanceIds)) {
            RefServiceManager serviceManager = getServiceManager(context);
            String reason = instanceService.getReason(serviceManager, entityId, objectId);
            instanceIds.forEach(instanceId -> instanceService.cancelWorkflowInstances(serviceManager, instanceId, reason, CancelSourceEnum.invalid.name()));
        }
    }

    interface DataOperation {
        /**
         * 作废
         */
        String INVALID = "invalid";
        /**
         * 删除
         */
        String DELETE = "d";

        static boolean supportOperation(String operation) {
            return StringUtils.equals(INVALID, operation) || StringUtils.equals(DELETE, operation);
        }

    }

}
