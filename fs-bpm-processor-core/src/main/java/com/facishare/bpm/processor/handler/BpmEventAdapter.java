package com.facishare.bpm.processor.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.bpm.manager.BPMAsyncDataFailRemindManager;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.processor.model.RetryInfo;
import com.facishare.bpm.producer.OpenAPIProducerManager;
import com.facishare.bpm.producer.model.BPMEventBean;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.service.impl.WorkflowTransferServiceImpl;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpmn.syn.component.RetryHandler;
import com.facishare.bpmn.syn.dao.model.CompensateDataEntity;
import com.facishare.bpmn.syn.utils.RetryUtil;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.mongo.engine.BatchOperationRecordDao;
import com.facishare.paas.workflow.bus.EngineBizEvent;
import com.facishare.paas.workflow.bus.api.*;
import com.facishare.paas.workflow.bus.api.type.BelongTo;
import com.facishare.paas.workflow.bus.api.type.FlowTag;
import com.facishare.paas.workflow.bus.api.type.OperationType;
import com.facishare.paas.workflow.bus.api.type.RemindType;
import com.facishare.paas.workflow.bus.model.BusConstant;
import com.facishare.paas.workflow.bus.model.MQContext;
import com.facishare.paas.workflow.bus.model.adapter.EngineEventHandlerAdapter;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey.externalApply;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/3/4 3:59 PM
 */
@Slf4j
@Component
public class BpmEventAdapter extends EngineEventHandlerAdapter {


    @Autowired
    private BpmEventHandler bpmBusEventHandler;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @Autowired
    private OpenAPIProducerManager openAPIProducerManager;

    @Autowired
    private BPMDefinitionService definitionService;

    @Autowired
    private WorkflowTransferService transferService;

    @Autowired
    private BPMAsyncDataFailRemindManager bpmAsyncDataFailRemindManager;

    @Autowired
    private BatchOperationRecordDao batchOperationRecordDao;

    /**
     * 国际化平台key的tag， 老对象、自定义对象、字段
     */
    public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";

    @PostConstruct
    public void initI18N(){
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);
    }

    @PostConstruct
    public void init() {
        new EngineBizEvent(BPMConstants.FS_BPM_APPLICATION_NAME, FlowTag.workflow_bpm, this);

        RetryUtil.getInstance().addHandler(new RetryHandler(BPMConstants.BIZ, BPMConstants.BUS_INSTANCE) {
            @Override
            public void process(CompensateDataEntity compensateDataEntity) {

                String instanceId = compensateDataEntity.getDataId();
                String tenantId = compensateDataEntity.getTenantId();
                log.info("BpmEventAdapter fail_over instance process:{},{}", tenantId, instanceId);
                if (Strings.isNullOrEmpty(instanceId)) {
                    log.warn("instanceId is null,{}", tenantId);
                    return;
                }
                RemoteContext context = WorkflowTransferServiceImpl.initContext(tenantId, BPMConstants.CRM_SYSTEM_USER);

                String data = compensateDataEntity.getData();
                Integer retryTimes = compensateDataEntity.getRetryTimes();
                String taskId = "";
                OperationType operationType = null;
                //历史消息不存在data
                if (!Strings.isNullOrEmpty(data)) {
                    log.info("retry exists data:{}", data);
                    RetryInfo retryInfo = JacksonUtil.fromJson(compensateDataEntity.getData(), RetryInfo.class);
                    if (Objects.nonNull(retryInfo)) {
                        operationType = retryInfo.getOperationType();
                        taskId = retryInfo.getTaskId();
                        //如果任务id和具体操作为空  则通过实例去同步,否则通过任务去同步
                        if (Objects.isNull(operationType) || Strings.isNullOrEmpty(taskId)) {
                            transferService.transfer(context, instanceId);
                        } else {
                            log.info("retry by task ,tenantId:{},operationType:{},taskId:{}", tenantId, operationType, taskId);
                            //重试一般都为调用自定义对象失败,无需二次同步深研
                            //应用节点也不会同步到自定对象
                            //执行更新任务信息,如果失败了,则
                            bpmBusEventHandler.findAndModifyTask(context, paasWorkflowServiceProxy.getTask(context, taskId), Boolean.FALSE);
                            //同步完任务  需要同步实例
                            bpmBusEventHandler.findAndModifyInstance(context, paasWorkflowServiceProxy.getWorkflowInstance(context, instanceId), getCurrentCandidateIds(context, instanceId));
                        }
                    }
                } else {
                    //根据实例同步所有,如果任务很多,在同步过程中,可能耗时10s,但是10s内 有任务被完成,则之前完成的任务会被修改为in_progress
                    transferService.transfer(context, compensateDataEntity.getDataId());
                }

                if (Objects.nonNull(retryTimes) && retryTimes.equals(SwitchConfigManager.syncDataFailRetryCount())) {
                    bpmAsyncDataFailRemindManager.sendTransferTaskFailNotice(context, instanceId, taskId, retryTimes, operationType, new Date(compensateDataEntity.getCreateTime()));
                }
            }
        });


        RetryUtil.getInstance().addHandler(new RetryHandler(BPMConstants.BIZ, BPMConstants.RETRY_INSTANCE) {
            @Override
            public void process(CompensateDataEntity compensateDataEntity) {
                String instanceId = compensateDataEntity.getDataId();
                String tenantId = compensateDataEntity.getTenantId();
                log.info("BpmEventAdapter fail_over instance retry instance process:{},{}", tenantId, instanceId);
                if (Strings.isNullOrEmpty(instanceId)) {
                    log.error("instanceId is null,{}", tenantId);
                    return;
                }
                RemoteContext context = WorkflowTransferServiceImpl.initContext(tenantId, BPMConstants.CRM_SYSTEM_USER);

                String data = compensateDataEntity.getData();
                OperationType operationType = null;

                if (!Strings.isNullOrEmpty(data)) {
                    log.info("retry instance exists data:{}", data);
                    RetryInfo retryInfo = JacksonUtil.fromJson(compensateDataEntity.getData(), RetryInfo.class);
                    operationType = retryInfo.getOperationType();
                    instanceId = retryInfo.getId();
                    bpmBusEventHandler.findAndModifyInstance(context, paasWorkflowServiceProxy.getWorkflowInstance(context, instanceId), getCurrentCandidateIds(context, instanceId));

                    instanceCancelModifyTask(context, instanceId);
                } else {
                    transferService.transfer(context, compensateDataEntity.getDataId());
                }
                Integer retryTimes = compensateDataEntity.getRetryTimes();
                if (Objects.nonNull(retryTimes) && retryTimes.equals(SwitchConfigManager.syncDataFailRetryCount())) {
                    bpmAsyncDataFailRemindManager.sendTransferFailNotice(context, instanceId, BPMConstants.BPM_INSTANCE, retryTimes, operationType, new Date(compensateDataEntity.getCreateTime()));
                }
            }
        });


        RetryUtil.getInstance().addHandler(new RetryHandler(BPMConstants.BIZ, BPMConstants.RETRY_TASK) {
            @Override
            public void process(CompensateDataEntity compensateDataEntity) {
                String taskId = compensateDataEntity.getDataId();
                String tenantId = compensateDataEntity.getTenantId();
                log.info("BpmEventAdapter fail_over task retry task process:{},{}", tenantId, taskId);
                if (Strings.isNullOrEmpty(taskId)) {
                    log.error("taskId is null,{}", tenantId);
                    return;
                }
                RemoteContext context = WorkflowTransferServiceImpl.initContext(tenantId, BPMConstants.CRM_SYSTEM_USER);

                String data = compensateDataEntity.getData();
                OperationType operationType = null;

                if (!Strings.isNullOrEmpty(data)) {
                    log.info("retry task exists data:{}", data);
                    RetryInfo retryInfo = JacksonUtil.fromJson(compensateDataEntity.getData(), RetryInfo.class);
                    operationType = retryInfo.getOperationType();
                    taskId = retryInfo.getId();
                    bpmBusEventHandler.findAndModifyTask(context, paasWorkflowServiceProxy.getTask(context, taskId), Boolean.FALSE);
                } else {
                    log.error("task retry not found data,tenantId:{},taskId:{}", tenantId, taskId);
                }
                Integer retryTimes = compensateDataEntity.getRetryTimes();
                if (Objects.nonNull(retryTimes) && retryTimes.equals(SwitchConfigManager.syncDataFailRetryCount())) {
                    bpmAsyncDataFailRemindManager.sendTransferFailNotice(context, taskId, BPMConstants.BPM_TASK, retryTimes, operationType, new Date(compensateDataEntity.getCreateTime()));
                }
            }
        });
    }

    public List<String> getCurrentCandidateIds(RemoteContext context, String instanceId) {
        List<Task> sourceTasks = paasWorkflowServiceProxy.getTasksByInstanceId(context, instanceId);
        return sourceTasks.stream()
                .filter(item -> TaskState.in_progress.equals(item.getState()) || TaskState.error.equals(item.getState()))
                .peek(item -> item.getCandidateIds().removeAll(item.getProcessIds()))
                .flatMap(item -> item.getCandidateIds().stream())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public void createInstance(CreateInstanceEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter createInstance :{}", JacksonUtil.toJson(event));
        WorkflowInstance workflowInstance = null;
        try {
            workflowInstance = paasWorkflowServiceProxy.getWorkflowInstance(context, event.getWorkflowInstanceId());
            if (Objects.isNull(workflowInstance)) {
                log.error("eventbus createInstance : failed! TENANT_ID={}, INSTANCE={}, {}", context.getTenantId(), JSON.toJSONString(event), "创建实例_查询实例接口返回为空");
                RetryInfo.recordFailInstance(context, event.getWorkflowInstanceId(), "创建实例_查询实例接口返回为空", event.getType());//ignoreI18n
                return;
            }
            bpmBusEventHandler.findAndModifyInstance(context, workflowInstance, getCurrentCandidateIds(context, event.getWorkflowInstanceId()));
        } catch (Exception e) {
            if (!isBusinessException(e)) {
                log.error("eventbus createInstance : failed! TENANT_ID={}, INSTANCE={}, ", context.getTenantId(), JSON.toJSONString(event), e);
            }
            RetryInfo.recordFailInstance(context, event.getWorkflowInstanceId(), e.getMessage(), event.getType());
        } finally {
            instanceChange(context, event.getWorkflowInstanceId(), false, event.getType(), workflowInstance);
        }
    }

    @Override
    public void instanceEnd(InstanceEndEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter instanceEnd :{}", JacksonUtil.toJson(event));
        WorkflowInstance workflowInstance = null;
        try {
            workflowInstance = paasWorkflowServiceProxy.getWorkflowInstance(context, event.getWorkflowInstanceId());
            if (Objects.isNull(workflowInstance)) {
                log.error("eventbus instanceEnd : failed! TENANT_ID={}, INSTANCE={},{} ", context.getTenantId(), JSON.toJSONString(event), "实例结束_查询引擎实例接口返回为空");
                RetryInfo.recordFailInstance(context, event.getWorkflowInstanceId(), "实例结束_查询引擎实例接口返回为空", event.getType());//ignoreI18n
                return;
            }
            if (StringUtils.isBlank(workflowInstance.getId())){
                log.info("instanceEnd-实例ID是空，实例已被删除，instanceId:{}", event.getWorkflowInstanceId());
                return;
            }
            //终止或者结束  直接清掉即可 无需查询引擎
            bpmBusEventHandler.findAndModifyInstance(context, workflowInstance, Lists.newArrayList());

            instanceCancelModifyTask(context, event.getWorkflowInstanceId());
        } catch (Exception e) {
            if (!isBusinessException(e)) {
                log.error("eventbus instanceEnd : failed! TENANT_ID={}, INSTANCE={}, ", context.getTenantId(), JSON.toJSONString(event), e);
            }
            RetryInfo.recordFailInstance(context, event.getWorkflowInstanceId(), e.getMessage(), event.getType());
        } finally {
            instanceChange(context, event.getWorkflowInstanceId(), false, event.getType(), workflowInstance);
            if(StringUtils.isNotEmpty(event.getRequestId())) {
                batchOperationRecordDao.updateSyncStatus(context.getTenantId(),event.getRequestId(),event.getWorkflowInstanceId());
            }
        }
    }

    private void instanceCancelModifyTask(RemoteContext context, String workflowInstanceId) {
        //获取普通的常规任务
        PageResult<Task> tasksByCondition = paasWorkflowServiceProxy.getTasksByCondition(context,
                null,
                workflowInstanceId,
                new Page(1000, 1, null, true),
                TaskState.cancel, null, null, null, null);
        if (CollectionUtils.isNotEmpty(tasksByCondition.getDataList())) {
            tasksByCondition.getDataList().forEach(item -> {
                String id = item.getId();
                TaskHandledEvent taskHandledEvent = TaskHandledEvent.createBpm(
                        context,
                        FlowTag.workflow_bpm,
                        TraceContext.get().getTraceId(),
                        id,
                        null,
                        null,
                        null,
                        null,
                        item.getWorkflowInstanceId(),
                        null,
                        item.getEntityId(),
                        item.getObjectId(),
                        "cancel"
                );
                taskHandledEvent(taskHandledEvent);
            });
        }
    }


    @Override
    public void createTask(CreateTaskEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter createTask :{}", JacksonUtil.toJson(event));
        //如果是自动等待节点
        Task task;
        WorkflowInstance instance = null;
        try {
            if (BusConstant.LATENCY_TASK_TYPE.equals(event.getNodeType())) {
                task = paasWorkflowServiceProxy.getLatencyTask(context, event.getTaskId());
                openAPIProducerManager.sendLatencyEvent(1, task,event.getType().toString());
                return;
            }
            instance = paasWorkflowServiceProxy.getWorkflowInstance(context, event.getWorkflowInstanceId());
            if(BusConstant.AUTO_TASK_TYPE.equals(event.getNodeType()) || BusConstant.DELAY_TASK_TYPE.equals(event.getNodeType())){
                bpmBusEventHandler.findAndModifyInstance(context, instance, getCurrentCandidateIds(context, event.getWorkflowInstanceId()));
                return;
            }

            task = paasWorkflowServiceProxy.getTask(context, event.getTaskId());
            //通知深研
            taskChange(context,event.getType(), task, true);

            //如果是业务节点处理方式
            bpmBusEventHandler.findAndModifyTask(context, task, event.isBatch());
            bpmBusEventHandler.updateTaskHandleTimeDetail(context, task, event);
        } catch (Exception e) {
            if (!isBusinessException(e)) {
                log.error("eventbus createTask : failed! TENANT_ID={}, INSTANCE={}, ", context.getTenantId(), JSON.toJSONString(event), e);
            }
            RetryInfo.recordFailTask(context, event.getTaskId(), e.getMessage(), event.getType());
        } finally {
            instanceChange(context, event.getWorkflowInstanceId(), true, event.getType(), instance);
        }
    }

    @Override
    public void changeCandidateIdsEvent(ChangeCandidateIdsEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter changeCandidateIdsEvent :{}", JacksonUtil.toJson(event));
        WorkflowInstance instance = null;
        try {
            instance = paasWorkflowServiceProxy.getWorkflowInstance(context, event.getWorkflowInstanceId());
            Task task = paasWorkflowServiceProxy.getTask(context, event.getTaskId());
            //处理人变了,通知深研
            taskChange(context,event.getType(), task, false);

            bpmBusEventHandler.findAndModifyTask(context, task, event.isBatch());
            bpmBusEventHandler.updateTaskHandleTimeDetail(context, task, event);
        } catch (Exception e) {
            if (!isBusinessException(e)) {
                log.error("eventbus changeCandidateIdsEvent : failed! TENANT_ID={}, INSTANCE={}, ", context.getTenantId(), JSON.toJSONString(event), e);
            }
            RetryInfo.recordFailTask(context, event.getTaskId(), e.getMessage(), event.getType());
        } finally {
            if(!Strings.isNullOrEmpty(event.getRequestId())){
                batchOperationRecordDao.updateSyncStatus(context.getTenantId(),event.getRequestId(),event.getTaskId());
            }
            instanceChange(context, event.getWorkflowInstanceId(), true, event.getType(), instance);
        }
    }

    @Override
    public void taskHandledEvent(TaskHandledEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter taskHandledEvent :{}", JacksonUtil.toJson(event));
        Task task;
        WorkflowInstance instance = null;

        try {
            instance = paasWorkflowServiceProxy.getWorkflowInstance(context, event.getWorkflowInstanceId());
            //等待节点
            if (BusConstant.LATENCY_TASK_TYPE.equals(event.getNodeType())) {
                task = paasWorkflowServiceProxy.getLatencyTask(context, event.getTaskId());
                openAPIProducerManager.sendLatencyEvent(2, task,event.getType().toString());
                return;
            }

            //如果末尾节点为自动节点,当自动节点可能执行时间过长,例如函数 执行1分钟,则自动节点完成时,需要更新下实例的状态
            if (BusConstant.AUTO_TASK_TYPE.equals(event.getNodeType()) || BusConstant.DELAY_TASK_TYPE.equals(event.getNodeType())) {

                bpmBusEventHandler.findAndModifyInstance(context, instance, getCurrentCandidateIds(context, event.getWorkflowInstanceId()));
                return;
            }

            //查询任务详情
            task = paasWorkflowServiceProxy.getTask(context, event.getTaskId());
            //通知深研
            taskChange(context,event.getType(), task, false);
            //如果是业务节点处理方式
            bpmBusEventHandler.findAndModifyTask(context, task, StringUtils.isNotBlank(event.getRequestId()) ? Boolean.TRUE : Boolean.FALSE);
            bpmBusEventHandler.updateTaskHandleTimeDetail(context, task, event);
        } catch (Exception e) {
            if (!isBusinessException(e)) {
                log.error("eventbus taskHandledEvent : failed! TENANT_ID={}, INSTANCE={}, ", context.getTenantId(), JSON.toJSONString(event), e);
            }
            RetryInfo.recordFailTask(context, event.getTaskId(), e.getMessage(), event.getType());
        } finally {
            instanceChange(context, event.getWorkflowInstanceId(), true, event.getType(), instance);
        }


    }

    //发送error的事件
    //发送重试成功的事件  引擎在加一下
    @Override
    public void afterErrorEvent(AfterErrorEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter afterErrorEvent :{}", JacksonUtil.toJson(event));
        WorkflowInstance instance = null;

        try {
            instance = paasWorkflowServiceProxy.getWorkflowInstance(context, event.getWorkflowInstanceId());
            TraceContext.get().setSourceProcessId(instance.getSourceWorkflowId());
            if (BelongTo.task == event.getBelongTo()) {
                Task task = paasWorkflowServiceProxy.getTask(context, event.getId());
                if(NodeType.executionTask.equals(task.getType())){
                    return;
                }
                //通知深研
                taskChange(context,event.getType(), task, false);
                bpmBusEventHandler.findAndModifyTask(context, task, event.isBatch());
            } else {
                bpmBusEventHandler.updateInstanceState(context, event.getId(), event.getState());
            }
        } catch (Exception e) {
            if (!isBusinessException(e)) {
                log.error("eventbus afterErrorEvent : failed! TENANT_ID={}, INSTANCE={}, ", context.getTenantId(), JSON.toJSONString(event), e);
            }
            if (BelongTo.task == event.getBelongTo()) {
                RetryInfo.recordFailTask(context, event.getId(), e.getMessage(), event.getType());
            } else {
                RetryInfo.recordFailInstance(context, event.getId(), e.getMessage(), event.getType());
            }
        } finally {
            instanceChange(context, event.getWorkflowInstanceId(), true, event.getType(), instance);
        }
    }

    @Override
    public void deleteInstance(DeleteInstanceEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        BPMEventBean instanceChange = BPMEventBean.builder()
                .subType(event.getType().toString())
                .data(BPMEventBean.InstanceData.builder()
                        .instanceId(event.getWorkflowInstanceId())
                        .tenantId(context.getTenantId())
                        .entityId(event.getEntityId())
                        .externalFlow(event.getExternalFlow())
                        .objectId(event.getObjectId()).build())
                .tag("instance_deleted").id(event.getWorkflowInstanceId()).build();

        openAPIProducerManager.send(instanceChange, context.getTenantId());
    }

    @Override
    public void recoveryInstance(RecoveryInstanceEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        BPMEventBean instanceChange = BPMEventBean.builder()
                .subType(event.getType().toString())
                .data(BPMEventBean.InstanceData.builder()
                        .instanceId(event.getWorkflowInstanceId())
                        .tenantId(context.getTenantId())
                        .entityId(event.getEntityId())
                        .externalFlow(event.getExternalFlow())
                        .objectId(event.getObjectId()).build())
                .tag("instance_recovery").id(event.getWorkflowInstanceId()).build();

        openAPIProducerManager.send(instanceChange, context.getTenantId());
    }


    @Override
    public void deleteTask(DeleteTaskEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        openAPIProducerManager.send(BPMEventBean.builder()
                .subType(event.getType().toString())
                .data(BPMEventBean.TaskInstanceData.builder()
                        .instanceId(event.getWorkflowInstanceId())
                        .tenantId(context.getTenantId())
                        .taskId(event.getTaskId())
                        .entityId(event.getEntityId())
                        .objectId(event.getObjectId()).build())
                .tag("task_deleted").id(event.getWorkflowInstanceId()).build(), context.getTenantId());
    }

    @Override
    public void recoveryTask(RecoveryTaskEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        openAPIProducerManager.send(BPMEventBean.builder()
                .subType(event.getType().toString())
                .data(BPMEventBean.TaskInstanceData.builder()
                        .instanceId(event.getWorkflowInstanceId())
                        .tenantId(context.getTenantId())
                        .taskId(event.getTaskId())
                        .entityId(event.getEntityId())
                        .objectId(event.getObjectId()).build())
                .tag("task_recovery").id(event.getWorkflowInstanceId()).build(), context.getTenantId());
    }

    @Override
    public void overtime(OvertimeEvent event) {
        if(!RemindType.task.equals(event.getRemindType())){
            return;
        }
        RemoteContext context = getRemoteContext(event.getContext());
        Task task = paasWorkflowServiceProxy.getTask(context, event.getTaskId());
        if(Objects.isNull(task) || StringUtils.isBlank(task.getId())){
            return;
        }
        taskChange(context,event.getType(), task, false);
    }

    @Override
    public void remindTask(RemindTaskEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        log.info("BpmEventAdapter remindTask :{}", JacksonUtil.toJson(event));
        try {
            Task task = paasWorkflowServiceProxy.getTask(context, event.getTaskId());
            taskChange(context,event.getType(), task, false);
        }catch (Exception e){
            log.info("BpmEventAdapter remindTask error, taskId :{}", event.getTaskId(), e);
        }

    }

    @Override
    public void autoTaskCreateEvent(AutoTaskCreateEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        try {
            PageResult<Task> taskPageResult = paasWorkflowServiceProxy.getAutomaticAndQuartzTasks(context, event.getWorkflowInstanceId(), null, null, null, event.getTaskId(), null);
            if(Objects.isNull(taskPageResult) || CollectionUtils.isEmpty(taskPageResult.getResult())){
                return;
            }
            autoTaskChange(context, event.getType(), taskPageResult.getResult().get(0));
        }catch (Exception e){
            log.info("BpmEventAdapter autoTaskCreateEvent error,tenantId :{}, taskId :{}", context.getTenantId(), event.getTaskId(), e);
        }
    }

    @Override
    public void autoTaskHandleEvent(AutoTaskHandleEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        try {
            PageResult<Task> taskPageResult = paasWorkflowServiceProxy.getAutomaticAndQuartzTasks(context, event.getWorkflowInstanceId(), null, null, null, event.getTaskId(), null);
            if(Objects.isNull(taskPageResult) || CollectionUtils.isEmpty(taskPageResult.getResult())){
                return;
            }
            autoTaskChange(context, event.getType(), taskPageResult.getResult().get(0));
        }catch (Exception e){
            log.info("BpmEventAdapter autoTaskHandleEvent error,tenantId :{}, taskId :{}", context.getTenantId(), event.getTaskId(), e);
        }
    }

    @Override
    public void autoTaskEndEvent(AutoTaskEndEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        try {
            PageResult<Task> taskPageResult = paasWorkflowServiceProxy.getAutomaticAndQuartzTasks(context, event.getWorkflowInstanceId(), null, null, null, event.getTaskId(), null);
            if(Objects.isNull(taskPageResult) || CollectionUtils.isEmpty(taskPageResult.getResult())){
                return;
            }
            autoTaskChange(context, event.getType(), taskPageResult.getResult().get(0));
        }catch (Exception e){
            log.info("BpmEventAdapter autoTaskEndEvent error,tenantId :{}, taskId :{}", context.getTenantId(), event.getTaskId(), e);
        }
    }

    @Override
    public void delayTaskCreateEvent(DelayTaskCreateEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        try {
            PageResult<Task> taskPageResult = paasWorkflowServiceProxy.getAutomaticAndQuartzTasks(context, event.getWorkflowInstanceId(), null, null, null, event.getTaskId(), null);
            if(Objects.isNull(taskPageResult) || CollectionUtils.isEmpty(taskPageResult.getResult())){
                return;
            }
            autoTaskChange(context, event.getType(), taskPageResult.getResult().get(0));
        }catch (Exception e){
            log.info("BpmEventAdapter delayTaskCreateEvent error,tenantId :{}, taskId :{}", context.getTenantId(), event.getTaskId(), e);
        }
    }

    @Override
    public void delayTaskHandleEvent(DelayTaskHandleEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        try {
            PageResult<Task> taskPageResult = paasWorkflowServiceProxy.getAutomaticAndQuartzTasks(context, event.getWorkflowInstanceId(), null, null, null, event.getTaskId(), null);
            if(Objects.isNull(taskPageResult) || CollectionUtils.isEmpty(taskPageResult.getResult())){
                return;
            }
            autoTaskChange(context, event.getType(), taskPageResult.getResult().get(0));
        }catch (Exception e){
            log.info("BpmEventAdapter delayTaskHandleEvent error,tenantId :{}, taskId :{}", context.getTenantId(), event.getTaskId(), e);
        }
    }

    @Override
    public void delayTaskEndEvent(DelayTaskEndEvent event) {
        RemoteContext context = getRemoteContext(event.getContext());
        try {
            PageResult<Task> taskPageResult = paasWorkflowServiceProxy.getAutomaticAndQuartzTasks(context, event.getWorkflowInstanceId(), null, null, null, event.getTaskId(), null);
            if(Objects.isNull(taskPageResult) || CollectionUtils.isEmpty(taskPageResult.getResult())){
                return;
            }
            autoTaskChange(context, event.getType(), taskPageResult.getResult().get(0));
        }catch (Exception e){
            log.info("BpmEventAdapter delayTaskEndEvent error,tenantId :{}, taskId :{}", context.getTenantId(), event.getTaskId(), e);
        }
    }


    private static boolean isExternalApplyTask(Integer externalApplyTask) {
        return externalApplyTask != null && "1".equals(externalApplyTask + "");
    }

    private static RemoteContext getRemoteContext(MQContext mqContext) {
        return new RemoteContext("", mqContext.getTenantId(), mqContext.getAppId(), BPMConstants.CRM_SYSTEM_USER);
    }


    private void taskChange(RemoteContext context,OperationType subType, Task task, boolean isInsert) {
        WorkflowExtension extension = definitionService.getWorkflowExtensionByWorkflowId(context, task.getWorkflowId());
        List<PoolEntity> pools = extension == null ? Lists.newArrayList() : extension.getPools();
        LaneEntity lane = PoolEntity.getLane(pools, task.getActivityId());
        log.info("taskChange open api sync task ,taskId:{}, insert:{},externalApplyTask:{}", task.getId(), isInsert, task.isExternalApplyTask());
        if (isInsert && task.isExternalApplyTask()) {
            openAPIProducerManager.sendAppEvent(
                    context,
                    task.getWorkflowInstanceId(),
                    task.getId(),
                    BPMTask.getAppCode(task.getBpmExtension()),
                    BPMTask.getAppActionCode(task.getBpmExtension()),
                    task.getEntityId(),
                    task.getObjectId(),
                    task.getWorkflowName(),
                    task.getName(),
                    task.getCreateTime(),
                    BPMTask.getChildrenActionCode(task.getBpmExtension()),
                    BPMTask.getChildrenActionName(task.getBpmExtension()),
                    task.getSourceWorkflowId(),
                    task.getActivityId(),
                    subType.toString(),lane.getId(),lane.getName());
        }

        openAPIProducerManager.send(BPMEventBean.builder()
                .subType(subType.toString())
                .data(BPMEventBean.TaskInstanceData.builder()
                        .instanceId(task.getWorkflowInstanceId())
                        .laneName(lane.getName())
                        .laneId(lane.getId())
                        .timeout(task.isTimeout())
                        .candidateIds(task.getCandidateIdNotExcludeExternalApplyTasks())
                        .workflowId(task.getWorkflowId())
                        .sourceWorkflowId(task.getSourceWorkflowId())
                        .activityId(task.getActivityId())
                        .taskType(task.getTaskType())
                        .executionType(BPMTask.getExecutionType(task.getBpmExtension()).name())
                        .activityInstanceId(task.getActivityInstanceId())
                        .tenantId(context.getTenantId()).taskId(task.getId()).taskName(task.getName())
                        .workflowName(task.getWorkflowName())
                        .opinions(task.getOpinions())
                        .entityId(task.getEntityId())
                        .objectId(task.getObjectId())
                        .childrenActionCode(BPMTask.getChildrenActionCode(task.getBpmExtension()))
                        .childrenActionName(BPMTask.getChildrenActionName(task.getBpmExtension()))
                        .appDetail(task.getBpmExtension().get(externalApply))
                        .taskState(task.getState())
                        .suspendAccumulateDuration(task.getRegularSuspendDuration())
                        .elementApiName(task.getElementApiName())
                        .operateLogs(task.getOperateLogs()).build())
                .tag("task_change").id(task.getWorkflowInstanceId()).build(), context.getTenantId());
    }

    private void instanceChange(RemoteContext context, String instanceId, boolean syncInstance, OperationType operationType, WorkflowInstance instance) {

        if (Objects.isNull(instance)) {
            instance = paasWorkflowServiceProxy.getWorkflowInstance(context, instanceId);
        }
        if(Objects.nonNull(instance) && StringUtils.isBlank(instance.getId())){
            log.info("instanceChange-实例ID是空，实例已被删除，instanceId:{}", instanceId);
            return;
        }

        //给深研发送消息
        BPMEventBean instanceChange = BPMEventBean.builder().subType(operationType.toString())
                .data(BPMEventBean.InstanceData.builder()
                        .instanceId(instanceId)
                        .tenantId(context.getTenantId())
                        .entityId(instance.getEntityId())
                        .state(instance.getState())
                        .workflowName(instance.getWorkflowName())
                        .workflowId(instance.getWorkflowId())
                        .sourceWorkflowId(instance.getSourceWorkflowId())
                        .externalFlow(instance.getExternalFlow())
                        .objectId(instance.getObjectId())
                        .applicantId(instance.getApplicantId())
                        .build())
                .tag("instance_change").id(instanceId).build();

        openAPIProducerManager.send(instanceChange, context.getTenantId());

        /**
         * 此行代码放到此处的原因:
         *
         * 原老逻辑是监控引擎workflowInstances表,即当tasks表发生变更, workflowInstances表也同样
         * 发生变更,他们各自有单独的listener进行处理各自的变更
         *
         * eventbus事件,任务发生变更,不会发出实例的变更消息,即任务的相关处理逻辑,需要同步实例的相关信息
         *
         * 同步任务时,会优先判断下实例是否需要进行同步,判断依据是当前任务的处理人是否在实例的团队成员中
         *  - 如果在团队成员中,则只更新任务,不更新实例,这就会导致实例上的当期任务,当期阶段可能发生不准确
         *  - 目前任务的相关变更,都会执行下此逻辑,为了保证实例的正确性
         *  - 需要在给深研发送完数据后进行同步处理
         *  - 如果一下操作失败了,则需要进行重试操作
         */
        if (syncInstance) {
            try {
                bpmBusEventHandler.findAndModifyInstance(context, instance, getCurrentCandidateIds(context, instance.getId()));
            } catch (Exception e) {
                if (!isBusinessException(e)) {
                    log.error("eventbus instanceChange findAndModifyInstance : failed! TENANT_ID={}, INSTANCE={},OPERATION_TYPE:{} ",
                            context.getTenantId(), instanceId, operationType.name(), e);
                }
                RetryInfo.recordFailInstance(context, instanceId, e.getMessage(), operationType);
            }
        }
    }

    private boolean isBusinessException(Exception e) {
        if (e instanceof RestProxyBusinessException) {
            RestProxyBusinessException restProxyBusinessException = (RestProxyBusinessException) e;
            return 301129007 == restProxyBusinessException.getCode();
        }
        return false;
    }

    private void autoTaskChange(RemoteContext context,OperationType subType, Task task) {
        WorkflowExtension extension = definitionService.getWorkflowExtensionByWorkflowId(context, task.getWorkflowId());
        List<PoolEntity> pools = extension == null ? Lists.newArrayList() : extension.getPools();
        LaneEntity lane = PoolEntity.getLane(pools, task.getActivityId());
        openAPIProducerManager.send(BPMEventBean.builder()
                .subType(subType.toString())
                .data(BPMEventBean.TaskInstanceData.builder()
                        .instanceId(task.getWorkflowInstanceId())
                        .laneName(lane.getName())
                        .laneId(lane.getId())
                        .workflowId(task.getWorkflowId())
                        .sourceWorkflowId(task.getSourceWorkflowId())
                        .activityId(task.getActivityId())
                        .taskType(task.getTaskType())
                        .executionType(BPMTask.getExecutionType(task.getBpmExtension()).name())
                        .activityInstanceId(task.getActivityInstanceId())
                        .tenantId(context.getTenantId())
                        .taskId(task.getId())
                        .taskName(task.getName())
                        .workflowName(task.getWorkflowName())
                        .entityId(task.getEntityId())
                        .objectId(task.getObjectId())
                        .taskState(task.getState()).build())
                .tag("auto_task_change").id(task.getWorkflowInstanceId()).build(), context.getTenantId());
    }
}
