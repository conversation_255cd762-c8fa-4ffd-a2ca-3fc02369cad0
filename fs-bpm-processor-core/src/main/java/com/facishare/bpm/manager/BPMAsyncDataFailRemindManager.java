package com.facishare.bpm.manager;

import com.facishare.bpm.proxy.FlowServiceSessionProxy;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.paas.workflow.bus.api.type.OperationType;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 业务流同步待办失败后  如果重试次数大于10次  需要看下具体原因
 * @IgnoreI18n
 */
@Component
@Slf4j
public class BPMAsyncDataFailRemindManager {

    @Autowired
    private FlowServiceSessionProxy sessionProxy;

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Async
    public void sendTransferTaskFailNotice(RemoteContext context, String instanceId, String taskId, Integer retryCount, OperationType operationType, Date date) {
        try {
            Map<String, Object> data = Maps.newHashMap();
            data.put("企业Id", context.getTenantId());
            data.put("实例Id", instanceId);
            data.put("重试次数", retryCount);
            data.put("任务Id", taskId);
            if (Objects.nonNull(operationType)) {
                data.put("操作类型", operationType.name());
            }
            if (Objects.nonNull(date)) {
                data.put("创建时间", simpleDateFormat.format(date));
            }
            List<Integer> employee = SwitchConfigManager.syncDataFailRemindEmpleyee();
            Integer maxRetryCount = SwitchConfigManager.syncDataFailRetryCount();
            if (CollectionUtils.isNotEmpty(employee)) {
                sessionProxy.sendMessage(context.getTenantId() + "任务或实例同步到待办平台失败重试大于" + maxRetryCount + "次,请检查日志", data, employee, true);
            }
        } catch (Exception e) {
            log.warn("任务或实例同步到待办平台失败重试大于10次,tenantId:{},instanceId{},taskId:{},retryCount:{}", context.getTenantId(), instanceId, taskId, retryCount);
        }
    }


    @Async
    public void sendTransferFailNotice(RemoteContext context, String id, String type, Integer retryCount, OperationType operationType, Date date) {
        try {
            Map<String, Object> data = Maps.newHashMap();
            data.put("企业Id", context.getTenantId());
            data.put("id", id);
            data.put("重试次数", retryCount);
            data.put("类型", type);
            if (Objects.nonNull(operationType)) {
                data.put("操作类型", operationType.name());
            }
            if (Objects.nonNull(date)) {
                data.put("创建时间", simpleDateFormat.format(date));
            }
            List<Integer> employee = SwitchConfigManager.syncDataFailRemindEmpleyee();
            Integer maxRetryCount = SwitchConfigManager.syncDataFailRetryCount();
            if (CollectionUtils.isNotEmpty(employee)) {
                sessionProxy.sendMessage(context.getTenantId() + type + "同步到待办平台失败重试大于" + maxRetryCount + "次,请检查日志", data, employee, true);
            }
        } catch (Exception e) {
            log.warn(type + "同步到待办平台失败重试大于10次,tenantId:{},id{},retryCount:{}", context.getTenantId(), id, type, retryCount);
        }
    }
}
