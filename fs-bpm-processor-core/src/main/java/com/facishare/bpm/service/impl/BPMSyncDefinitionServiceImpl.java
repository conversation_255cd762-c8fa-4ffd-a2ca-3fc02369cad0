package com.facishare.bpm.service.impl;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMSyncDefinitionService;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.rest.core.model.RemoteContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

//TODO 是否删除 确认下
@Slf4j
@Data
@Service
public class BPMSyncDefinitionServiceImpl extends BPMBaseService implements BPMSyncDefinitionService {

    @Autowired
    private BPMDefinitionService bpmDefinitionService;
    @Autowired
    private BpmSimpleDefinitionDao outlineDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;

    @Override
    public void syncDefinition(String fromTenantId, String toTenantId, Boolean isDelete, Boolean isEnable) {
        // 初始化来源企业
        log.info("来源企业:{},目标企业:{}", fromTenantId, toTenantId);
        RemoteContext fromContext = getContext(fromTenantId);
        // 获取来源企业的流城定义
        WorkflowOutlineQuery query = new WorkflowOutlineQuery();
        query.setEnable(null);
        List<WorkflowOutlineEntity> outlines = outlineDao.find(fromTenantId, query, new Page(2000, 1, null,
                false),true, null, Boolean.TRUE).getDataList();
        log.info("查询来源企业的BPM定义个数为:{}", outlines.size());
        RemoteContext toContext = getContext(toTenantId);

        outlines.forEach(outline -> {
            WorkflowOutline workflowOutline = bpmDefinitionService.getWorkflowOutlineByIdOfClearRule(getServiceManager(fromContext), outline.getId());
            workflowOutline.clearWorkflowOutline(workflowOutline, toTenantId);
            try {
                String outlineId = bpmDefinitionService.deployWorkflow(getServiceManager(toContext), workflowOutline, false, false, true);
                log.info("数据同步成功:将 {} 同步与 {} ,新流程定义Id为:{} name:{}", fromTenantId, toTenantId, outlineId, workflowOutline.getName());
            } catch (Exception e) {
                log.error("同步失败:{},{},{},{}", fromTenantId, toTenantId, workflowOutline.getId(), e.getMessage());
            }
        });
    }

    private RemoteContext getContext(String tenantId) {
        return new RemoteContext("", tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
    }
}
