<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
  <bean id="transactionManager"
        class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="datasource"/>
  </bean>
  <bean id="datasource" class="com.github.mybatis.spring.DynamicDataSource"
        p:configName="fs-approval-mysql-config"/>

  <!-- define the SqlSessionFactory -->
  <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    <property name="dataSource" ref="datasource"/>
    <property name="typeAliasesPackage" value="com.facishare.workflow.crm.model"/>
    <property name="configLocation" value="classpath:spring/fs-bpm-fail-over/fs-bpm-fail-over-mybatis.xml"/>
  </bean>
  <!-- scan for mapper and let them be autowired -->
  <bean class="com.github.mybatis.spring.ScannerConfigurer">
    <property name="basePackage" value="com.facishare.bpmn.syn.dao.mapper"/>
    <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
  </bean>

  <bean class="com.facishare.paas.pod.client.DbRouterClient" init-method="init"/>
  <bean id="compensateDataDao" class="com.facishare.bpmn.syn.dao.impl.CompensateDataDaoImpl" />
  <bean id="mongoShardManager" class="com.facishare.bpmn.syn.manger.MongoShardManager" />
</beans>
