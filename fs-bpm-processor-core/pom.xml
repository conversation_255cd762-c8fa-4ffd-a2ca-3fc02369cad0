<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-bpm</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.5.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-bpm-processor-core</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-workflow-bus-api</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.flow.public</groupId>
            <artifactId>fs-bpmn-syn-data-compensate</artifactId>
            <version>${fs-flow-public.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-transport-native-unix-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-spring</artifactId>
            <version>${jboss-resteasy.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jackson2-provider</artifactId>
            <version>${jboss-resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
            <version>${jboss-resteasy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-multipart-provider</artifactId>
            <version>${jboss-resteasy.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>resteasy-jaxrs</artifactId>
                    <groupId>org.jboss.resteasy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cglib-nodep</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- exeception aop-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>cglib-nodep</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bpm-core</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>self-registry-service</artifactId>
                    <groupId>com.fxiaoke.service</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-common-mq</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-bpm-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-sandbox-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- End Spring -->
        <!-- Mandatory dependencies for using Spock -->
        <!-- Optional dependencies for using Spock -->
        <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- enables mocking of classes without default constructor (together with CGLIB) -->
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <version>2.5.1</version>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- only required if Hamcrest matchers are used -->
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bpm-api</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
