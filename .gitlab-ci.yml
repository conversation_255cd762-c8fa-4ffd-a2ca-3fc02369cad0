variables:
  GIT_DEPTH: 0
  MAVEN_OPTS: >-
    -Dserver
    -Dhttps.protocols=TLSv1.2
    -Dorg.slf4j.simpleLogger.showDateTime=true
    -Djava.awt.headless=true
    -Xmx2048m

  MAVEN_CLI_OPTS: >-
    --batch-mode
    --errors
    --fail-at-end
    --show-version
    --no-transfer-progress

default:
  interruptible: true
  tags:
    - jdk11
    - sonar
    - k8s
  artifacts:
    expire_in: 1 week
    exclude:
      - /**/*.class
      - /**/*.war
      - /**/lib/*.jar

stages:
  - i18nScan
  - verify

i18nScan:
  stage: i18nScan
  timeout: 20m
  script:
    - 'python3 /scripts/i18n_scan.py $CI_PROJECT_DIR'
  only:
    - main
    - master
    - /develop\/.*/
    - /release\/.*/
    - /hotfix\/.*/
    - /feature\/.*/
    
verify:
  stage: verify
  timeout: 100m
  variables:
    CI_JOB_LOG_LEVEL: "info"
  only:
    - main
    - master
    - /develop\/.*/
    - /release\/.*/
    - /hotfix\/.*/
    - /feature\/.*/
  before_script:
    - export SONAR_OPTS=$(grep '^sonar.' sonar-project.properties | sed -e 's|^|-D|g' | grep "sonar." | tr '\r\n' ' ')
  script:
    - pwd
    - FORMATTED_VERSION=`date +%Y%m%d`
    - 'mvn $MAVEN_CLI_OPTS clean verify sonar:sonar -q -Dorg.slf4j.simpleLogger.defaultLogLevel=info -Dprocess.profile=fstest -Dspring.profiles.active=fstest -Dfile.encoding=UTF-8 -Dmaven.test.failure.ignore=true $SONAR_OPTS -Dsonar.branch.name=${CI_COMMIT_REF_NAME} -Dsonar.projectVersion=${FORMATTED_VERSION}#${CI_BUILD_ID}'
