1. 函数作用
   获取业务流定义列表
2. 函数使用
   Fx.bpm.getDefinitionList(\<String entityId>, \<int page>, \<int pageSize>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>entityId</td>
            <td>String</td>
            <td>对象ApiName</td>
        </tr>
        <tr>
            <td>page</td>
            <td>int</td>
            <td>页码</td>
        </tr>
        <tr>
            <td>pageSize</td>
            <td>int</td>
            <td>每页数量</td>
        </tr>
    </table>
4. 返回值说明
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>返回值</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>data</td>
            <td>定义信息List</td>
        </tr>
    </table>
5. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#8
6. 对应接口：
[定义相关.md](../2.接口文档/rest/定义相关.md)
```text
/plugins/definition/list
```

