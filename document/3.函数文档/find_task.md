1. 函数作用
   查找业务流任务的详细信息
2. 函数使用
   Fx.bpm.findTask(\<String taskId>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>taskId</td>
            <td>String</td>
            <td>任务Id</td>
        </tr>
    </table>
4. 返回值说明
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>返回值</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>name</td>
            <td>任务名称</td>
        </tr>
        <tr>
            <td>state</td>
            <td>状态</td>
        </tr>
        <tr>
            <td>applicantId</td>
            <td>发起人</td>
        </tr>
        <tr>
            <td>entityId</td>
            <td>任务关联对象</td>
        </tr>
        <tr>
            <td>objectId</td>
            <td>任务关联数据id</td>
        </tr>
        <tr>
            <td>candidateIds</td>
            <td>待处理人</td>
        </tr>
         <tr>
            <td>processIds</td>
            <td>已处理人</td>
        </tr>
         <tr>
            <td>activityInstanceId</td>
            <td>任务节点编号</td>
        </tr>
    </table>
5. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#2
6. 对应接口：
[任务相关.md](../2.接口文档/rest/任务相关.md)
```text
/task/getTask
```

