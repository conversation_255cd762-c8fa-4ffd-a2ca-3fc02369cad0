1. 函数作用
   更换任务处理人
2. 函数使用
   Fx.bpm.changeCandidateIds(\<String taskId>, \<List candidateIds>, \<String opinion>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>taskId</td>
            <td>String</td>
            <td>taskId</td>
        </tr>
        <tr>
            <td>taskId</td>
            <td>List</td>
            <td>要改为的处理人</td>
        </tr>
        <tr>
            <td>opinion</td>
            <td>String</td>
            <td>可选参数，更换处理人意见</td>
        </tr>
    </table>
4. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#6
5. 对应接口：
[任务相关.md](../2.接口文档/rest/任务相关.md)
```text
/task/changeTaskHandler
```