1. 函数作用
   查找指定对象数据的所有业务流实例
2. 函数使用
   Fx.bpm.findInstances(\<String objectApiName>, \<String state>, \<String dataId>, \<int pageSize>, \<int pageNumber>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>objectApiName</td>
            <td>String</td>
            <td>对象ApiName</td>
        </tr>
        <tr>
            <td>state</td>
            <td>String</td>
            <td>业务流实例状态：in_progress(进行中), pass(通过), cancel(取消), error(异常)</td>
        </tr>
        <tr>
            <td>dataId</td>
            <td>String</td>
            <td>数据id</td>
        </tr>
        <tr>
            <td>pageSize</td>
            <td>int</td>
            <td>一页的数据数量，最大100条</td>
        </tr>
        <tr>
            <td>pageNumber</td>
            <td>int</td>
            <td>当前页数</td>
        </tr>
    </table>
4. 返回值说明
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>返回值</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>instanceId</td>
            <td>业务流程id</td>
        </tr>
        <tr>
            <td>workflowName</td>
            <td>业务流名称</td>
        </tr>
        <tr>
            <td>state</td>
            <td>业务流状态</td>
        </tr>
        <tr>
            <td>applicantId</td>
            <td>发起人id</td>
        </tr>
        <tr>
            <td>start</td>
            <td>开始时间戳</td>
        </tr>
        <tr>
            <td>end</td>
            <td>结束时间戳</td>
        </tr>
    </table>
5. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#0
6. 对应接口：
[实例相关.md](../2.接口文档/rest/实例相关.md)
```text
/instance/list/data
```