# 根据objectId或instanceId查询延迟节点
> Fx.bpm.findDelayTask(String objectId, String workflowInstanceId, Integer pageNumber,Integer pageSize)

参数说明

| 参数                 | 类型    | 说明                                                         |
|--------------------| ------- | ------------------------------------------------------------ |
| objectId           | String  | 数据id                                              |
| workflowInstanceId | String  | 实例id                                         |
| pageNumber         | Integer  | 分页查询 第几页                                                     |
| pageSize           | Integer  | 分页查询 每次查询最大记录数                                                    |

对应接口：
[任务相关.md](../2.接口文档/REST/任务相关.md)
```text
/approval/task/findDelayTask
```

返回值类型
> APIResult

返回值说明
```text
 
```

Groovy举例
```groovy
def objectId = "63a403eb314aa80001bd9b82";
def instanceId = "63a406a35513f84c58c2b67e";
// 从第几页开始查
def pageNumber = 1;
// 每页最大记录数
def pageSize = 3;
//根据数据ID查延迟节点，查询前三条记录
Fx.bpm.findDelayTask(objectId, null, pageNumber,pageSize);
//根据实例ID查延迟节点，pageNumber默认为1，pageSize默认为20
Fx.bpm.findDelayTask(null, instanceId,null,null);
```