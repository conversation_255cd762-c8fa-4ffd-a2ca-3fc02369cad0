1. 函数作用
   取消流程实例
2. 函数使用
   Fx.bpm.cancel(\<String instanceId>, \<String reason>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>instanceId</td>
            <td>String</td>
            <td>业务流实例Id</td>
        </tr>
        <tr>
            <td>reason</td>
            <td>String</td>
            <td>取消原因</td>
        </tr>
    </table>
4. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#3
5. 对应接口：
[实例相关.md](../2.接口文档/rest/实例相关.md)
```text
/instance/cancel
```