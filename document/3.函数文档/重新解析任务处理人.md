1. 函数作用

   根据taskId重新解析任务的处理人
2. 函数使用
   Fx.bpm.refreshHandler(\<String taskId>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>taskId</td>
            <td>String</td>
            <td>任务id</td>
        </tr>
    </table>
4. 返回值说明
```text
[false, {}, ]
```
5. groovy举例
```groovy
def taskId = "6343e4d780f4841c79cbcae0"
def res = Fx.bpm.refreshHandler(taskId)
log.info(res)
```
6. 对应接口：
   [任务相关.md](../2.接口文档/rest/任务相关.md)
```text
/plugins/task/refreshHandlerByTaskId
```

