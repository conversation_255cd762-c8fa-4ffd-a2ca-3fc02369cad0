1. 函数作用
   对审批节点进行操作
2. 函数使用
   Fx.bpm.approval(\<String taskId>, \<String userId>, \<String action>, \<String opinion>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>taskId</td>
            <td>String</td>
            <td>审批节点Id</td>
        </tr>
        <tr>
            <td>userId</td>
            <td>String</td>
            <td>审批人</td>
        </tr>
        <tr>
            <td>action</td>
            <td>String</td>
            <td>操作类型，支持的类型有agree（同意）, reject（拒绝）</td>
        </tr>   
        <tr>
            <td>opinion</td>
            <td>String</td>
            <td>审批意见</td>
        </tr>
    </table>
4. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#4
5. 对应接口：
[任务相关.md](../2.接口文档/rest/任务相关.md)
```text
/task/approvalTask
```