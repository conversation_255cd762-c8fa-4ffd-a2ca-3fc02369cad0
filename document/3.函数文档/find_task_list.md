1. 函数作用
   查找业务流实例的所有任务节点
2. 函数使用
   Fx.bpm.findTaskList(\String instanceId>, \<int pageSize>, \<int pageNumber>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>instanceId</td>
            <td>String</td>
            <td>String</td>
        </tr>
        <tr>
            <td>pageSize</td>
            <td>int</td>
            <td>一页的数据数量，最大100条</td>
        </tr>
        <tr>
            <td>pageNumber</td>
            <td>int</td>
            <td>当前页数</td>
        </tr>
    </table>
4. 返回值说明
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>返回值</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>taskId</td>
            <td>任务Id</td>
        </tr>
        <tr>
            <td>taskName</td>
            <td>任务名称</td>
        </tr>
        <tr>
            <td>state</td>
            <td>任务状态</td>
        </tr>
        <tr>
            <td>executionType</td>
            <td>执行类型： update(更新类型的节点) approve(审批节点) externalApplyTask(应用节点) operation(业务操作节点) addRelatedObject(添加关联对象) batchAddRelatedObject(批量添加关联对象) batchEditMasterDetailObject(批量编辑从对象) addMDObject(新建从对象) latency(定时等待节点) 节点类型(taskType)</td>
        </tr>
        <tr>
            <td>assignNextTask</td>
            <td>是否需要指定下一节点处理人:1 指定，非1不指定</td>
        </tr>
        <tr>
            <td>linkApp</td>
            <td>应用appId</td>
        </tr>
         <tr>
            <td>linkAppType</td>
            <td>应用类型</td>
        </tr>
         <tr>
            <td>Opinion</td>
            <td>节点的处理意见(List)，包含的字段有: tenantId 节点处理人的企业Id、userId 节点处理人的userId、actionType 处理类型、opinion 处理意见、replyTime 处理时间</td>
        </tr>
    </table>
5. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#1
6. 对应接口：
[实例相关.md](../2.接口文档/rest/实例相关.md)
```text
/instance/progress
```

