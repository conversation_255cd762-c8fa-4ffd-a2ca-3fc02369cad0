1. 函数作用
   完成业务流任务
2. 函数使用
   Fx.bpm.complete(\<String taskId>, \<String userId>, \<String opinion>, \<Map map>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>taskId</td>
            <td>String</td>
            <td>业务节点Id</td>
        </tr>
        <tr>
            <td>userId</td>
            <td>String</td>
            <td>处理人</td>
        </tr>
        <tr>
            <td>opinion</td>
            <td>String</td>
            <td>可选参数，与map一并使用，处理意见</td>
        </tr>
         <tr>
            <td>map</td>
            <td>Map</td>
            <td>可选参数，与opinion一并使用，其他参数</td>
        </tr>
    </table>
4. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#5
5. 对应接口：
[任务相关.md](../2.接口文档/rest/任务相关.md)
```text
/task/complete
```