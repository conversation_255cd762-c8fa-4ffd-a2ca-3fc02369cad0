1. 函数作用
   触发业务流
2. 函数使用
   Fx.bpm.startInstance(\<String id>, \<String objectId>, \<String entityId>)
3. 参数列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>参数</th>
            <th>类型	</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>id</td>
            <td>String</td>
            <td>业务流定义id</td>
        </tr>
        <tr>
            <td>objectId</td>
            <td>String</td>
            <td>对象数据Id</td>
        </tr>
        <tr>
            <td>entityId</td>
            <td>String</td>
            <td>对象apiName</td>
        </tr>
    </table>
4. 返回值说明
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>返回值</th>
            <th>说明</th>
        </tr>
        <tr>
            <td>result</td>
            <td>创建业务流实例的id</td>
        </tr>
    </table>
5. 详细介绍
   https://www.fxiaoke.com/mob/guide/crmdoc/src/8-4-20bpm.html#7
6. 对应接口：
[实例相关.md](../2.接口文档/rest/实例相关.md)
```text
/plugins/instance/start
```

