// 见: <函数:返回指定对象和指定数据（可选）的所有流程实例>
// 查询一条进行中的实例
QueryResult data = Fx.bpm.findInstances((String) context.data.object_describe_api_name, "in_progress", (String) context.data._id, 10, 1)[1]
//log.info(data)
if (data != null && data.size > 0) {
    List<Map> dataList = data.dataList;
    //获取实例id,dataList有多条  举例我们暂使用第一条,这里instanceId 一定是有值的
    String instanceId = dataList[0].instanceId
    def ret = Fx.bpm.findTaskList(instanceId, 10, 1)
    log.info(ret)
    //防止有实例  但是没任务的情况,如果出现此问题 那就是bug  需要我们研发人员看下了 实施同学您辛苦了
    if (ret != null) {
        //ret[1] 这里是获取的所有的任务信息  并不是任务的第一条
        List<Map> tasks = (List<Map>) ret[1]
        //也可以获取第一条
        Map first = tasks[0];
        //打印下第一条的任务id
        String taskId = (String) first.taskId

        def taskDetail = Fx.bpm.findTask(taskId)
        if (taskDetail != null) {
            log.info(taskDetail)


            def state = taskDetail["state"]
            def executionType = taskDetail["executionType"]
            def assignNextTask = taskDetail["assignNextTask"]
            List candidateIds = (List) taskDetail["candidateIds"]
            List processIds = (List) taskDetail["processIds"]
            String apiName = taskDetail["apiName"]
            String dataId = taskDetail["dataId"]
            String name = taskDetail["name"]


            //审批节点中完成任务  当前人可能因为权限的缘故  可能存在没有按钮的情况,推荐页面执行此操作


            //目前只针对  普通的业务活动节点进行完成任务 以下字段都是通过获取任务详情接口获取
            //1. 任务必须是进行中
            //2. executionType==approve
            //3. 业务活动  没有配置指定下一节点处理人  assignNextTask!=1
            //4. 当前完成人  必须在待处理人中  candidateIds.contains(当前完成任务的人)
            //7. 当前完成人  不能在已处理人名单中   !processIds.contains(当前完成任务的人)

            //有了以上前提   咱们的完成任务 才能生效  否则 ,也会被后台给拦住,不要抱侥幸心理,如果同一个实例的多个任务,同一时间完成任务过于频繁,会被锁10s中,如果通过函数完成任务,而影响了用户页面
            //上正常的操作,那bug会被拒掉的 ,  不要写在for循环中完成任务,一定要添加匹配条件才可以


            String userId = "1000"
            if (state == 'in_progress' && executionType == 'approve' && assignNextTask != 1 && candidateIds.contains(userId) && !processIds.contains(userId)) {
                def approvalRst = Fx.bpm.approval(taskId, userId, "agree", "同意")
                log.info(approvalRst)
            }
        }
    }
} else {
    log.info("请检查参数:" + data)
}







