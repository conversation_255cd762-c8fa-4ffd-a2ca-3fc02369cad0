// 见: <函数:返回指定对象和指定数据（可选）的所有流程实例>
// 查询一条进行中的实例
QueryResult data = Fx.bpm.findInstances((String) context.data.object_describe_api_name, "in_progress", (String) context.data._id, 10, 1)[1]
//log.info(data)
if (data != null && data.size > 0) {
    List<Map> dataList = data.dataList;
    //获取实例id,dataList有多条  举例我们暂使用第一条,这里instanceId 一定是有值的
    String instanceId = dataList[0].instanceId
    def ret = Fx.bpm.findTaskList(instanceId, 10, 1)
    log.info(ret)
    //防止有实例  但是没任务的情况,如果出现此问题 那就是bug  需要我们研发人员看下了 实施同学您辛苦了
    if (ret != null) {
        //ret[1] 这里是获取的所有的任务信息  并不是任务的第一条
        List<Map> tasks = (List<Map>) ret[1]
        //也可以获取第一条
        Map first = tasks[0];
        //打印下第一条的任务id
        String taskId = first.taskId
        //打印下任务id
        log.info(first)

        // 添加团队成员在页面上可以由用户选择人员(选人控件),但是在函数中是没有的,需要自行设置负责人

        def taskResult = Fx.bpm.findTask(taskId)
        if (taskResult != null) {
            def taskDetail = taskResult[1]
            log.info(taskDetail)
            def state = taskDetail["state"]
            def executionType = taskDetail["executionType"]
            def assignNextTask = taskDetail["assignNextTask"]
            List candidateIds = (List) taskDetail["candidateIds"]
            List processIds = (List) taskDetail["processIds"]
            String apiName = taskDetail["apiName"]
            String dataId = taskDetail["dataId"]
            String name = taskDetail["name"]

            log.info("state:" + state + ",executionType:" + executionType + ",assignNextTask:" + assignNextTask + ",candidateIds:" + candidateIds + ",processIds:" + processIds)
            //这个人可以手动指定人 也可以用candidateIds中的任意一个人去完成任务
            String userId = candidateIds.get(0)

            if ('in_progress' == state && 'operation' == executionType && 1 != assignNextTask && !processIds.contains(userId)) {


                //此处没有选人控件,故写死了数据值
                //参数依次为:

                //对象apiname
                //数据id
                //要添加到团队成员中的人员id
                //将要添加的团队成员设置为什么类型的人员(1:负责人;2:联合跟进人;3:售后服务人员;4:普通人员)
                //权限类型(1:只读;2:读写)
                def rst = Fx.object.addTeamMember(apiName, dataId, ["1000", "1002"], 4, 1)
                log.info(rst)
                //执行完成任务
                def ret2 = Fx.bpm.complete(taskId, userId, "我是审批意见:添加团队成员", [:])
                if (ret2[0] == false) {
                    log.info("任务完成成功:" + ret2)
                } else {
                    log.info("任务完成失败:" + ret2)
                }
            }
        }


    }
} else {
    log.info("未查询当当前数据存在业务流程:" + data)
}