// 见: <函数:返回指定对象和指定数据（可选）的所有流程实例>
// 查询一条进行中的实例
QueryResult data = Fx.bpm.findInstances((String) context.data.object_describe_api_name, "in_progress", (String) context.data._id, 10, 1)[1]
//log.info(data)
if (data != null && data.size > 0) {
    List<Map> dataList = data.dataList;
    //获取实例id,dataList有多条  举例我们暂使用第一条,这里instanceId 一定是有值的
    String instanceId = dataList[0].instanceId
    def ret = Fx.bpm.findTaskList(instanceId, 10, 1)
    log.info(ret)
    //防止有实例  但是没任务的情况,如果出现此问题 那就是bug  需要我们研发人员看下了 实施同学您辛苦了
    if (ret != null) {
        //ret[1] 这里是获取的所有的任务信息  并不是任务的第一条
        List<Map> tasks = (List<Map>) ret[1]

        tasks.each {
            task ->
                task
                //也可以获取第一条
                Map first = task;
                //打印下第一条的任务id
                String taskId = first.taskId
                //打印下任务id
                log.info(first)

                // 会签节点存在多人审批, 在配置流程定义时,如果节点上勾选了[此审批需要所有审批人批复(勾选后即使有审批人不同意，节点也会等待所有审批人批复后才结束)],则需要遍历candidateIds中所有
                //人员进行处理

                //函数中处理会签节点 由于没有人为操作,状态要不同意,要不不同意,只有这两种状态

                def taskResult = Fx.bpm.findTask(taskId)
                if (taskResult != null) {
                    def taskDetail = taskResult[1]
                    log.info(taskDetail)
                    def state = taskDetail["state"]
                    def executionType = taskDetail["executionType"]
                    def assignNextTask = taskDetail["assignNextTask"]
                    List<String> candidateIds = (List) taskDetail["candidateIds"]
                    List processIds = (List) taskDetail["processIds"]
                    String apiName = taskDetail["apiName"]
                    String dataId = taskDetail["dataId"]
                    String name = taskDetail["name"]
                    String taskType = taskDetail["taskType"]

                    log.info("state:" + state + ",executionType:" + executionType + ",assignNextTask:" + assignNextTask + ",candidateIds:" + candidateIds + ",processIds:" + processIds)
                    String userId = "";
                    // 进行中 并且是审批 且类型为会签
                    if ('in_progress' == state && 'approve' == executionType && 'all' == taskType && 1 != assignNextTask) {
                        candidateIds.each {
                            user ->
                                user
                                //如果当前人已处理了任务 则不需要再次处理 跳过本次处理
                                if (processIds.contains(user)) {
                                    return;
                                }
                                log.info("处理人为:" + user)
                                //最后需要一个结果,第3个参数是审批意见,result:[agree(同意),reject(不同意)]
                                def ret2 = Fx.bpm.complete(taskId, user + "", "我是审批意见:会签节点" + user, ["result": "agree"])
                                if (ret2[0] == false) {
                                    log.info("任务完成成功:" + ret2)
                                } else {
                                    log.info("任务完成失败:" + ret2)
                                }
                        }
                    }
                }
        }
    }
} else {
    log.info("未查询当当前数据存在业务流程:" + data)
}