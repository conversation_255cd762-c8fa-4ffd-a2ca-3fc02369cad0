// 见: <函数:返回指定对象和指定数据（可选）的所有流程实例>
// 查询一条进行中的实例
QueryResult data = Fx.bpm.findInstances((String) context.data.object_describe_api_name, "in_progress", (String) context.data._id, 10, 1)[1]
//log.info(data)
if (data != null && data.size > 0) {
    List<Map> dataList = data.dataList;
    //获取实例id,dataList有多条  举例我们暂使用第一条,这里instanceId 一定是有值的
    String instanceId = dataList[0].instanceId
    def ret = Fx.bpm.findTaskList(instanceId, 10, 1)
    log.info(ret)
    //防止有实例  但是没任务的情况,如果出现此问题 那就是bug  需要我们研发人员看下了 实施同学您辛苦了
    if (ret != null) {
        //ret[1] 这里是获取的所有的任务信息  并不是任务的第一条
        List<Map> tasks = (List<Map>) ret[1]

        tasks.each {
            task ->
                task
                //也可以获取第一条
                Map first = task;
                //打印下第一条的任务id
                String taskId = first.taskId
                //打印下任务id
                log.info(first)

                // 退回公海无特殊逻辑,只是退回公海与流程联合操作而已,此处逻辑不会有退回公海的原因及自定义的表单可填写
                def taskResult = Fx.bpm.findTask(taskId)
                if (taskResult != null) {
                    def taskDetail = taskResult[1]
                    log.info(taskDetail)
                    def state = taskDetail["state"]
                    def executionType = taskDetail["executionType"]
                    def assignNextTask = taskDetail["assignNextTask"]
                    List<String> candidateIds = (List) taskDetail["candidateIds"]
                    List processIds = (List) taskDetail["processIds"]
                    String apiName = taskDetail["apiName"]
                    String dataId = taskDetail["dataId"]
                    String name = taskDetail["name"]
                    String taskType = taskDetail["taskType"]

                    log.info("state:" + state + ",executionType:" + executionType + ",assignNextTask:" + assignNextTask + ",candidateIds:" + candidateIds + ",processIds:" + processIds)
                    String userId = candidateIds.get(0);
                    if ('in_progress' == state && 'operation' == executionType && 1 != assignNextTask && !processIds.contains(userId)) {
                        log.info("处理人为:" + userId)

                        //客户id,公海id(退回到哪个公海,需要自行设置)
                        Fx.crm.account.giveBack(dataId, "公海Id,请配置我")

                        def ret2 = Fx.bpm.complete(taskId, userId, "我是审批意见,(退回公海)虽然页面不会显示" + userId, [:])
                        if (ret2[0] == false) {
                            log.info("任务完成成功:" + ret2)
                        } else {
                            log.info("任务完成失败:" + ret2)
                        }
                    }
                }
        }
    }
} else {
    log.info("未查询当当前数据存在业务流程:" + data)
}