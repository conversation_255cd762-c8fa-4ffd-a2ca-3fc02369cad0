## 流程实例接口

>最后一次更新时间 


1. 获取配置信息

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetSomeConfig</td>
            <td>MGetSomeConfig$Arg</td>
            <td>MGetSomeConfig$Result</td>
            <td>获取配置信息</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>html5Url</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取企业自定义配置信息

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetCustomRedirectConfig</td>
            <td>MGetCustomRedirectConfig$Arg</td>
            <td>MGetCustomRedirectConfig$Result</td>
            <td>获取企业自定义配置信息</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>--</td>
                        <td>id  当前任务Id</td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>--</td>
                        <td>objectApiName,对象api_name,CasesObj,只有是工单对象时 才会调用此接口</td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>--</td>
                        <td>objectDataId  数据id</td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>linkAppId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>--</td>
                        <td>link_app_id</td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>--</td>
                        <td>link_app_type</td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>downstream</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td>--</td>
                        <td>downstream,卡梅隆 weex无法通过cep获取是上游还是下游  需要单独加参数接入</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>appRedirectUrl</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 停止流程实例

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/CancelInstance</td>
            <td>MCancelInstance$Arg</td>
            <td>MCancelInstance$Result</td>
            <td>停止流程实例</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>reason</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 启动流程实例

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/StartInstance</td>
            <td>MStartInstance$Arg</td>
            <td>MStartInstance$Result</td>
            <td>启动流程实例</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>variables</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 启动流程实例

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/TriggerInstance</td>
            <td>MTriggerInstance$Arg</td>
            <td>MTriggerInstance$Result</td>
            <td>启动流程实例</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>variables</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取某个流程的实例列表

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetInstanceList</td>
            <td>MGetInstanceList$Arg</td>
            <td>MGetInstanceList$Result</td>
            <td>获取某个流程的实例列表</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>asc</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>circleType</td>
                        <td>com.facishare.bpm.model.CircleType</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>workflowName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MWorkflowInstance&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取某个对象的实例列表

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetInstanceListByObject</td>
            <td>MGetInstanceListByObject$Arg</td>
            <td>MGetInstanceListByObject$Result</td>
            <td>获取某个对象的实例列表</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>asc</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>asc</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MWorkflowInstance&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取流程log数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetWorkflowInstanceLog</td>
            <td>MGetWorkflowInstanceLog$Arg</td>
            <td>MGetWorkflowInstanceLog$Result</td>
            <td>获取流程log数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>startTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>endTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>logs</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MGetWorkflowInstanceLog$UserTaskLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>cancelPersonId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>reason</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>execution</td>
                        <td>java.util.Map&lt;java.lang.String, AfterActionExecution$SimpleAfterActionExecution&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>11</td>
                        <td>submitter</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 后动作重新试或忽略

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/AfterActionRetry</td>
            <td>MInstanceAfterActionRetry$Arg</td>
            <td>MInstanceAfterActionRetry$Result</td>
            <td>后动作重新试或忽略</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>rowNum</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>executeType</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>success</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>message</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取流程log数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetLog</td>
            <td>MGetLog$Arg</td>
            <td>MGetLog$Result</td>
            <td>获取流程log数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>datas</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MTaskLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 通过任务或者实例id 获取当前关联的对象  770 根据对象灰度小程序使用

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetTaskOrInstanceRelatedEntityId</td>
            <td>GetTaskOrInstanceRelatedEntityId$Arg</td>
            <td>GetTaskOrInstanceRelatedEntityId$Result</td>
            <td>通过任务或者实例id 获取当前关联的对象  770 根据对象灰度小程序使用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>实例id</td>
                        <td>实例id及任务id而且其一,如果都为空,则会提示异常</td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>任务id</td>
                        <td>如果任务id和实例id同时传递,则优先查询任务关联的对象apiname</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取完整流程数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MInstance/GetEntireWorkflowInstance</td>
            <td>MGetEntireWorkflow$Arg</td>
            <td>MGetEntireWorkflow$Result</td>
            <td>获取完整流程数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflow</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>workflowInstance</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>excludeWorkflowFields</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>excludeInstanceFields</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
-------

涉及到的对象


1. AfterActionExecution$AfterActionOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>retry</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ignore</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskTypeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionState</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionErrorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateFieldJson</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>requestId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionMapping</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>recipients</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>title</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>content</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerParam</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.ChangeCandidateLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>operatorId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>from</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>to</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. TaskState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeout</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>normal</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. InstanceState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.UserTaskType
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskTypes</td>
                <td>java.util.List&lt;com.facishare.bpm.model.UserTaskType&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>type</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>subType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.TaskDelegateViewLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delegateId</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>trusteeId</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.manage.MoreOperationManager$MoreOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.mobile.model.MTaskLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assigeeId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>excutionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>result</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>time</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>preName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterActionExecution$SimpleAfterActionExecution
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>executionId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actions</td>
                <td>java.util.List&lt;AfterAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>java.util.List&lt;AfterActionExecution$AfterActionOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. Opinion
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>replyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.TaskLog$NodeAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.rule.RuleMessage
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tip</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditionPattern</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditions</td>
                <td>java.util.List&lt;java.util.List&lt;com.facishare.bpm.rule.RuleMessage$Condition&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.mobile.model.MWorkflowInstance
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>start</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>end</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerSource</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. NodeType
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>userTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.mobile.model.MGetWorkflowInstanceLog$UserTaskLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>desc</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>type</td>
                <td>com.facishare.bpm.model.UserTaskType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>endTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinions</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeoutExecution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>unCompletedPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>remindLatency</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyUnit</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateLogs</td>
                <td>java.util.List&lt;com.facishare.bpm.model.ChangeCandidateLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskDelegateLogs</td>
                <td>java.util.List&lt;com.facishare.bpm.model.TaskDelegateViewLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nextTaskAssignee</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nodeType</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delay</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operations</td>
                <td>java.util.List&lt;com.facishare.bpm.model.TaskLog$NodeAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>
