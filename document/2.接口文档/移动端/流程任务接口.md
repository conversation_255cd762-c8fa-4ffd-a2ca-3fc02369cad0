## 流程任务接口

>最后一次更新时间 


1. 新-获取任务详情,任务详情页使用

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetTaskDetail</td>
            <td>MGetTaskDetail$Arg</td>
            <td>MGetTaskInfo$Result</td>
            <td>新-获取任务详情,任务详情页使用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>laneName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>laneId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>state</td>
                        <td>TaskState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>11</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>12</td>
                        <td>objectName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>13</td>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>14</td>
                        <td>processIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>15</td>
                        <td>latencyUnit</td>
                        <td>int</td>
                        <td>--</td>
                        <td>1-天；2-小时；3-分钟</td>
                    </tr>
                                    <tr>
                        <td>16</td>
                        <td>remindLatency</td>
                        <td>java.lang.Object</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>17</td>
                        <td>isTimeout</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>18</td>
                        <td>executionType</td>
                        <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>19</td>
                        <td>taskType</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>20</td>
                        <td>execution</td>
                        <td>AfterActionExecution$SimpleAfterActionExecution</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>21</td>
                        <td>errorMsg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>22</td>
                        <td>assignNextTask</td>
                        <td>int</td>
                        <td>--</td>
                        <td>是否需要指定下一节点处理人1 指定，非1 不指定</td>
                    </tr>
                                    <tr>
                        <td>23</td>
                        <td>nextTaskAssigneeScope</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>24</td>
                        <td>button</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>25</td>
                        <td>data</td>
                        <td>com.facishare.bpm.handler.task.detail.model.StandardData</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>26</td>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>27</td>
                        <td>createTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>28</td>
                        <td>modifyTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>29</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>30</td>
                        <td>activityInstanceId</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>31</td>
                        <td>opinions</td>
                        <td>java.util.List&lt;Opinion&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>32</td>
                        <td>candidateModifyLog</td>
                        <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>33</td>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>34</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>35</td>
                        <td>isOwner</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>36</td>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>37</td>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>38</td>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>39</td>
                        <td>todoJumpUrl</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>40</td>
                        <td>flowConfig</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>41</td>
                        <td>pools</td>
                        <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.PoolEntity&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 后动作重新试或忽略

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/AfterActionRetry</td>
            <td>MAfterActionRetry$Arg</td>
            <td>MAfterActionRetry$Result</td>
            <td>后动作重新试或忽略</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>rowNum</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>executeType</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>success</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>message</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取当前人待办任务

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetHandleTaskList</td>
            <td>MGetHandleTaskList$Arg</td>
            <td>MGetHandleTaskList$Result</td>
            <td>获取当前人待办任务</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>isCompleted</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>keyWord</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MSimpleTask&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 任务执行

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/CompleteTask</td>
            <td>MCompleteTask$Arg</td>
            <td>MCompleteTask$Result</td>
            <td>任务执行</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>msg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 更改任务处理人

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/ChangeTaskHandler</td>
            <td>MChangeTaskHandler$Arg</td>
            <td>MChangeTaskHandler$Result</td>
            <td>更改任务处理人</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>modifyOpinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 清除提醒

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/ClearCrmRemind</td>
            <td>MClearCrmRemindOfTask$Arg</td>
            <td>MClearCrmRemindOfTask$Result</td>
            <td>清除提醒</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 新-获取任务详情,任务详情页使用，data，opinions，candidateModifyLog提出

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetTaskInfo</td>
            <td>MGetTaskInfo$Arg</td>
            <td>MGetTaskInfo$Result</td>
            <td>新-获取任务详情,任务详情页使用，data，opinions，candidateModifyLog提出</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>isH5</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>includeTaskFeedDetailConfig</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>laneName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>laneId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>state</td>
                        <td>TaskState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>11</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>12</td>
                        <td>objectName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>13</td>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>14</td>
                        <td>processIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>15</td>
                        <td>latencyUnit</td>
                        <td>int</td>
                        <td>--</td>
                        <td>1-天；2-小时；3-分钟</td>
                    </tr>
                                    <tr>
                        <td>16</td>
                        <td>remindLatency</td>
                        <td>java.lang.Object</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>17</td>
                        <td>isTimeout</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>18</td>
                        <td>executionType</td>
                        <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>19</td>
                        <td>taskType</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>20</td>
                        <td>execution</td>
                        <td>AfterActionExecution$SimpleAfterActionExecution</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>21</td>
                        <td>errorMsg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>22</td>
                        <td>assignNextTask</td>
                        <td>int</td>
                        <td>--</td>
                        <td>是否需要指定下一节点处理人1 指定，非1 不指定</td>
                    </tr>
                                    <tr>
                        <td>23</td>
                        <td>nextTaskAssigneeScope</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>24</td>
                        <td>button</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>25</td>
                        <td>data</td>
                        <td>com.facishare.bpm.handler.task.detail.model.StandardData</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>26</td>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>27</td>
                        <td>createTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>28</td>
                        <td>modifyTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>29</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>30</td>
                        <td>activityInstanceId</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>31</td>
                        <td>opinions</td>
                        <td>java.util.List&lt;Opinion&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>32</td>
                        <td>candidateModifyLog</td>
                        <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>33</td>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>34</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>35</td>
                        <td>isOwner</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>36</td>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>37</td>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>38</td>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>39</td>
                        <td>todoJumpUrl</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>40</td>
                        <td>flowConfig</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>41</td>
                        <td>pools</td>
                        <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.PoolEntity&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 保存任务相关数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/CreateTaskData</td>
            <td>MCreateTaskData$Arg</td>
            <td>MCreateTaskData$Result</td>
            <td>保存任务相关数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务对应的activityId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.Integer</td>
                        <td>true</td>
                        <td>任务对应的activityInstanceId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>true</td>
                        <td>数据内容</td>
                        <td>batchAddRelatedObject(entityId: 批量创建数据的apiname,objectId: 批量创建数据的数据Id)</td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>executionType</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务类型</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 通过任务id批量获取按钮

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetButtonByTaskId</td>
            <td>MGetButtonByTaskIds$Arg</td>
            <td>MGetButtonByTaskIds$Result</td>
            <td>通过任务id批量获取按钮</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskIds</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>buttons</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.List&lt;com.facishare.bpm.model.ActionButton&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>errorMsgs</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 重新解析业务流任务处理人

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/RefreshHandlerByTaskId</td>
            <td>RefreshHandlerByTaskId$Arg</td>
            <td>RefreshHandlerByTaskId$Result</td>
            <td>重新解析业务流任务处理人</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>
    
1. 获取任务详情

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetTask</td>
            <td>MGetTask$Arg</td>
            <td>MGetTask$Result</td>
            <td>获取任务详情</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>source</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>hasOptionTypes</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>taskType</td>
                        <td>com.facishare.bpm.util.verifiy.TaskType</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>opinions</td>
                        <td>java.util.List&lt;Opinion&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>assignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>state</td>
                        <td>TaskState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>isTaskOwner</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>executionType</td>
                        <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>11</td>
                        <td>data</td>
                        <td>com.facishare.bpm.handler.task.form.model.CustomerData</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>12</td>
                        <td>errorMsg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>13</td>
                        <td>createTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>14</td>
                        <td>taskExtension</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>15</td>
                        <td>buttons</td>
                        <td>java.util.Collection&lt;com.facishare.bpm.model.ActionButton&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>16</td>
                        <td>assigneeIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>17</td>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>18</td>
                        <td>modifyTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>19</td>
                        <td>externalApplyTask</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>20</td>
                        <td>assignNextTask</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>21</td>
                        <td>reminders</td>
                        <td>java.lang.Object</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>22</td>
                        <td>execution</td>
                        <td>AfterActionExecution$SimpleAfterActionExecution</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>23</td>
                        <td>unProcessIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>24</td>
                        <td>processIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>25</td>
                        <td>nextTaskAssigneeScope</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td>下一节点处理人范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>26</td>
                        <td>relatedDescribe</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>6.6 新增,只新建时下发要新建的对象的describe</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>27</td>
                        <td>describe</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>665 新增,添加当前对象描述,目前只针对销售线索,无效原因 @李汝健</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>28</td>
                        <td>remindLatency</td>
                        <td>java.lang.Object</td>
                        <td>允许停留时长</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>29</td>
                        <td>latencyUnit</td>
                        <td>java.lang.Integer</td>
                        <td>unit:1-天；2-小时；3-分钟</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>30</td>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td>h互联应用</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>31</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td>人员信息</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>32</td>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>33</td>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>34</td>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>35</td>
                        <td>todoJumpUrl</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>36</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>37</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 修改数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/Edit</td>
            <td>Edit$Arg</td>
            <td>Edit$Result</td>
            <td>修改数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>details</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectData</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>optionInfo</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>notValidate</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>originalData</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>originalDetails</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>
    
1. 获取某对象下待办任务列表,7.6.0

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetUncompletedTaskInfoByObject</td>
            <td>MGetUncompletedTaskInfosByObject$Arg</td>
            <td>MGetUncompletedTaskInfosByObject$Result</td>
            <td>获取某对象下待办任务列表,7.6.0</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>apiName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>notGetDatas</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>isMobile</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.task.LaneTask&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 任务执行

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/UpdateDataAndCompleteTask</td>
            <td>MUpdateDataAndCompleteTask$Arg</td>
            <td>MUpdateDataAndCompleteTask$Result</td>
            <td>任务执行</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>validationRule</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>ignoreNonBlocking</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>data</td>
                        <td>java.util.Map</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取当前人的某对象下待办任务列表

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/GetUncompletedTasksByObject</td>
            <td>MGetUncompletedTasksByObject$Arg</td>
            <td>MGetUncompletedTasksByObject$Result</td>
            <td>获取当前人的某对象下待办任务列表</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>apiName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MGetUncompletedTasksByObject$ObjectUnCompletedTask&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 完成并保存任务相关数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/MTask/CompleteAndCreateTaskData</td>
            <td>MCompleteAndCreateTaskData$Arg</td>
            <td>MCompleteTask$Result</td>
            <td>完成并保存任务相关数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>needGetNextTaskInfo</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务对应的activityId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.Integer</td>
                        <td>true</td>
                        <td>任务对应的activityInstanceId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>true</td>
                        <td>数据内容</td>
                        <td>batchAddRelatedObject(entityId: 批量创建数据的apiname,objectId: 批量创建数据的数据Id)</td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>executionType</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务类型</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>msg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
-------

涉及到的对象


1. com.facishare.flow.mongo.bizdb.entity.PoolEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lanes</td>
                <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.LaneEntity&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterActionExecution$AfterActionOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>retry</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ignore</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskTypeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionState</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionErrorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateFieldJson</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>requestId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionMapping</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>recipients</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>title</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>content</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerParam</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. TaskState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeout</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>normal</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.mobile.model.MSimpleTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>processName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>instanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.ActionButton
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>action</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>right</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>event</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>btnClass</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>order</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.handler.task.form.model.CustomerData
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>data</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layout</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describeExt</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.task.NextTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>nextTaskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.task.LaneTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTaskOwner</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>hasForm</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>processIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionType</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinions</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateModifyLog</td>
                <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>moreOperations</td>
                <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>externalApply</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppType</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkApp</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppEnable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>button</td>
                <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>data</td>
                <td>com.facishare.bpm.handler.task.detail.model.StandardData</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assignNextTask</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nextTaskAssigneeScope</td>
                <td>java.util.Set&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>externalApplyTask</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>approve</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>create</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>update</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>addRelatedObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>batchAddRelatedObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>batchEditMasterDetailObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>addMDObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latency</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateAndComplete</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateLookup</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operationMulti</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.manage.MoreOperationManager$MoreOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.mobile.model.MGetUncompletedTasksByObject$ObjectUnCompletedTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>workflowName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTaskOwner</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>instanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTimeout</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionType</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>belongToCurrentObj</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterActionExecution$SimpleAfterActionExecution
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>executionId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actions</td>
                <td>java.util.List&lt;AfterAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>java.util.List&lt;AfterActionExecution$AfterActionOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.handler.task.detail.model.StandardData
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>appCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionLabel</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>object_describe_api_name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedObjectId</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedObjectName</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>target_related_list_name</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>onlyRelatedObject</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>reminders</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedDescribe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>data</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layout</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describeExt</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectFlowLayoutExists</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layoutType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layoutApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>hasPermissions</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enableLayoutRules</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectPermissions</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Boolean&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isAfterActionWaiting</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>joiner</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldObjectHasValue</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldObjectIsIdentical</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>commonButtonApiNames</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>formEditable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. Opinion
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>replyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. Task$ApproverModifyLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyOpinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>beforeModifyPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>afterModifyPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.rule.RuleMessage
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tip</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditionPattern</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditions</td>
                <td>java.util.List&lt;java.util.List&lt;com.facishare.bpm.rule.RuleMessage$Condition&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.flow.mongo.bizdb.entity.LaneEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activities</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>
