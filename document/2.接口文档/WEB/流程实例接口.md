## 流程实例接口

>最后一次更新时间 


1. 停止流程实例

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/CancelInstance</td>
            <td>CancelInstance$Arg</td>
            <td>CancelInstance$Result</td>
            <td>停止流程实例</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>reason</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 启动流程实例

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/StartInstance</td>
            <td>StartInstance$Arg</td>
            <td>StartInstance$Result</td>
            <td>启动流程实例</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 启动流程实例

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/TriggerInstance</td>
            <td>TriggerInstance$Arg</td>
            <td>TriggerInstance$Result</td>
            <td>启动流程实例</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取某个流程的实例列表

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetInstanceList</td>
            <td>GetInstanceList$Arg</td>
            <td>PageResult</td>
            <td>获取某个流程的实例列表</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>circleType</td>
                        <td>com.facishare.bpm.model.CircleType</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>workflowName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>asc</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;E&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取某个对象的实例列表

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetInstanceListByObject</td>
            <td>GetInstanceListByObject$Arg</td>
            <td>GetInstanceListByObject$Result</td>
            <td>获取某个对象的实例列表</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>asc</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.mobile.model.MWorkflowInstance&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 阶段视图，当前流程正在运行的任务统计

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetWorkflowStatsData</td>
            <td>GetWorkflowStatsData$Arg</td>
            <td>GetWorkflowStatsData$Result</td>
            <td>阶段视图，当前流程正在运行的任务统计</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>data</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.WorkflowStats&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取流程实例日志

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetWorkflowInstanceLog</td>
            <td>GetWorkflowInstanceLog$Arg</td>
            <td>GetWorkflowInstanceLog$Result</td>
            <td>获取流程实例日志</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>startTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>endTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>logs</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.TaskLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>cancelPersonId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>reason</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>11</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>12</td>
                        <td>submitter</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>13</td>
                        <td>execution</td>
                        <td>java.util.Map&lt;java.lang.String, AfterActionExecution$SimpleAfterActionExecution&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 查询实例阶段信息

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetInstancesByObject</td>
            <td>GetInstancesByObject$Arg</td>
            <td>GetInstancesByObject$Result</td>
            <td>查询实例阶段信息</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.instance.GetInstanceByObject&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 后动作重新试或忽略

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/AfterActionRetry</td>
            <td>InstanceAfterActionRetry$Arg</td>
            <td>InstanceAfterActionRetry$Result</td>
            <td>后动作重新试或忽略</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>rowNum</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>executeType</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>success</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>message</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取完整流程数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetEntireWorkflowInstance</td>
            <td>GetEntireWorkflowInstance$Arg</td>
            <td>EntireWorkflowInstance</td>
            <td>获取完整流程数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instance</td>
                        <td>com.facishare.bpm.model.instance.EntireWorkflowInstance$Instance</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityInstances</td>
                        <td>java.util.List&lt;ActivityInstance&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>currentActivityInstances</td>
                        <td>java.util.List&lt;ActivityInstance&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>currentTransitions</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.instance.EntireWorkflowInstance$CurrentTransition&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>rule</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>moreOperations</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取完整流程数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessInstance/GetNewEntireWorkflowInstance</td>
            <td>GetEntireWorkflowInstance$Arg</td>
            <td>GetEntireWorkflowInstance$Result</td>
            <td>获取完整流程数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflow</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>workflowInstance</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>excludeWorkflowFields</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>excludeInstanceFields</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
-------

涉及到的对象


1. AfterActionExecution$AfterActionOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>retry</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ignore</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskTypeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionState</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionErrorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateFieldJson</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>requestId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionMapping</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>recipients</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>title</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>content</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerParam</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.ChangeCandidateLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>operatorId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>from</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>to</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. TaskState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeout</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>normal</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.TaskLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>desc</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>type</td>
                <td>com.facishare.bpm.model.UserTaskType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>endTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinions</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeoutExecution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>unCompletedPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>remindLatency</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyUnit</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateLogs</td>
                <td>java.util.List&lt;com.facishare.bpm.model.ChangeCandidateLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskDelegateLogs</td>
                <td>java.util.List&lt;com.facishare.bpm.model.TaskDelegateViewLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nextTaskAssignee</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nodeType</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delay</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operations</td>
                <td>java.util.List&lt;com.facishare.bpm.model.TaskLog$NodeAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executor</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.instance.EntireWorkflowInstance$CurrentTransition
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>fromId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>toId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>status</td>
                <td>com.facishare.bpm.model.instance.EntireWorkflowInstance$CurrentActivityInstanceState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. InstanceState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. ActivityInstance
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>start</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>end</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>duration</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ended</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>open</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.instance.GetInstanceByObject
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lanes</td>
                <td>java.util.List&lt;com.facishare.bpm.model.Pool$Lane&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>start</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>moreOperations</td>
                <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.instance.EntireWorkflowInstance$CurrentActivityInstanceState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>PROGRESS</td>
                <td>com.facishare.bpm.model.instance.EntireWorkflowInstance$CurrentActivityInstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>DONE</td>
                <td>com.facishare.bpm.model.instance.EntireWorkflowInstance$CurrentActivityInstanceState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.instance.EntireWorkflowInstance$Instance
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>businessObjectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>businessObjectApiLabel</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>businessObjectApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>businessObjectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>endTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>reason</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.WorkflowStats
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>inProgressTaskCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>normalTaskCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeoutTaskCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorTaskCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>inProgressWorkflowCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>passWorkflowCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>canceledWorkflowCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>totalWorkflowCount</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.UserTaskType
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskTypes</td>
                <td>java.util.List&lt;com.facishare.bpm.model.UserTaskType&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>type</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>subType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.TaskDelegateViewLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delegateId</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>trusteeId</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.manage.MoreOperationManager$MoreOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterActionExecution$SimpleAfterActionExecution
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>executionId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actions</td>
                <td>java.util.List&lt;AfterAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>java.util.List&lt;AfterActionExecution$AfterActionOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. Opinion
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>replyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.TaskLog$NodeAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.rule.RuleMessage
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tip</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditionPattern</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditions</td>
                <td>java.util.List&lt;java.util.List&lt;com.facishare.bpm.rule.RuleMessage$Condition&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.mobile.model.MWorkflowInstance
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>start</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>end</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerSource</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. NodeType
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>userTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.Pool$Lane
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>current</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>
