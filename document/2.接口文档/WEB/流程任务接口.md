## 流程任务接口

>最后一次更新时间 


1. 后动作重新试或忽略

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/AfterActionRetry</td>
            <td>AfterActionRetry$Arg</td>
            <td>AfterActionRetry$Result</td>
            <td>后动作重新试或忽略</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>rowNum</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>executeType</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>success</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>message</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取当前人待办任务

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetHandleTaskList</td>
            <td>GetHandleTaskList$Arg</td>
            <td>GetHandleTaskList$Result</td>
            <td>获取当前人待办任务</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>isCompleted</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>taskName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.TaskOutline&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 按阶段获取流程定义下所有的任务

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetHandleTaskListByLane</td>
            <td>GetHandleTaskListByLane$Arg</td>
            <td>PageResult</td>
            <td>按阶段获取流程定义下所有的任务</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>laneId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>circleType</td>
                        <td>com.facishare.bpm.model.CircleType</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>asc</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>dataList</td>
                        <td>java.util.List&lt;E&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 任务执行

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/CompleteTask</td>
            <td>CompleteTask$Arg</td>
            <td>CompleteTask$Result</td>
            <td>任务执行</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>needGetNextTaskInfo</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>msg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 根据workflowInstanceId和activityInstanceId查询任务详情

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetTasksByInstanceIds</td>
            <td>GetTasksByInstanceIds$Arg</td>
            <td>GetTasksByInstanceIds$Result</td>
            <td>根据workflowInstanceId和activityInstanceId查询任务详情</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>activityInstanceIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>bpmTasks</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.task.BPMTask&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 查看一个流程实例的处理日志

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetTaskLogs</td>
            <td>GetTaskLogs$Arg</td>
            <td>GetTaskLogs$Result</td>
            <td>查看一个流程实例的处理日志</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskLogs</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.task.TaskLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 更改任务处理人

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/ChangeTaskHandler</td>
            <td>ChangeTaskHandler$Arg</td>
            <td>ChangeTaskHandler$Result</td>
            <td>更改任务处理人</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>modifyOpinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 阶段视图任务查询-新,支持分页

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetWorkflowTasks</td>
            <td>GetWorkflowTasks$Arg</td>
            <td>GetWorkflowTasks$Result</td>
            <td>阶段视图任务查询-新,支持分页</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>type</td>
                        <td>com.facishare.bpm.model.task.LaneBriefTaskVO$QueryType</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>state</td>
                        <td>com.facishare.bpm.model.task.LaneBriefTaskVO$QueryState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskList</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.task.LaneBriefTaskVO&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>totalCount</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 清除提醒 

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/ClearCrmRemind</td>
            <td>ClearCrmRemindOfTask$Arg</td>
            <td>ClearCrmRemindOfTask$Result</td>
            <td>清除提醒 </td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>remindCount</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 新-获取任务详情,任务详情页使用,data，opinions，candidateModifyLog提出

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetTaskInfo</td>
            <td>GetTaskInfo$Arg</td>
            <td>GetTaskInfo$Result</td>
            <td>新-获取任务详情,任务详情页使用,data，opinions，candidateModifyLog提出</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>notGetData</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>includeTaskFeedDetailConfig</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>description</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>laneName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>laneId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>state</td>
                        <td>TaskState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>10</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>11</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>12</td>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>13</td>
                        <td>processIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>14</td>
                        <td>latencyUnit</td>
                        <td>int</td>
                        <td>--</td>
                        <td>1-天；2-小时；3-分钟</td>
                    </tr>
                                    <tr>
                        <td>15</td>
                        <td>remindLatency</td>
                        <td>java.lang.Object</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>16</td>
                        <td>isTimeout</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>17</td>
                        <td>executionType</td>
                        <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>18</td>
                        <td>taskType</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>19</td>
                        <td>execution</td>
                        <td>AfterActionExecution$SimpleAfterActionExecution</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>20</td>
                        <td>opinions</td>
                        <td>java.util.List&lt;Opinion&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>21</td>
                        <td>candidateModifyLog</td>
                        <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>22</td>
                        <td>errorMsg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>23</td>
                        <td>assignNextTask</td>
                        <td>int</td>
                        <td>--</td>
                        <td>是否需要指定下一节点处理人1 指定，非1 不指定</td>
                    </tr>
                                    <tr>
                        <td>24</td>
                        <td>nextTaskAssigneeScope</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>25</td>
                        <td>button</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>26</td>
                        <td>data</td>
                        <td>com.facishare.bpm.handler.task.detail.model.StandardData</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>27</td>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>28</td>
                        <td>createTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>29</td>
                        <td>modifyTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>30</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>31</td>
                        <td>activityInstanceId</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>32</td>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>33</td>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>34</td>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>35</td>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>36</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>37</td>
                        <td>isOwner</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>38</td>
                        <td>todoJumpUrl</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>39</td>
                        <td>externalApply</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>40</td>
                        <td>pools</td>
                        <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.PoolEntity&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>41</td>
                        <td>config</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取阶段下的任务,data，opinions，candidateModifyLog提出

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetTaskInfoByLaneId</td>
            <td>GetTaskInfoByLaneId$Arg</td>
            <td>GetTaskInfoByLaneId$Result</td>
            <td>获取阶段下的任务,data，opinions，candidateModifyLog提出</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>laneId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>notGetDatas</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.util.List&lt;com.facishare.bpm.controller.web.model.GetTaskInfoByLaneId$LaneTaskInfo&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>dataMap</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.Map&lt;java.lang.String, java.lang.Object&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>descMap</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.Map&lt;java.lang.String, java.lang.Object&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>descExtMap</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.Map&lt;java.lang.String, java.lang.Object&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 保存任务相关数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/CreateTaskData</td>
            <td>CreateTaskData$Arg</td>
            <td>CreateTaskData$Result</td>
            <td>保存任务相关数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务对应的activityId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.Integer</td>
                        <td>true</td>
                        <td>任务对应的activityInstanceId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>true</td>
                        <td>数据内容</td>
                        <td>batchAddRelatedObject(entityId: 批量创建数据的apiname,objectId: 批量创建数据的数据Id)</td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>executionType</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务类型</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 通过任务id批量获取按钮

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetButtonByTaskId</td>
            <td>GetButtonByTaskIds$Arg</td>
            <td>GetButtonByTaskIds$Result</td>
            <td>通过任务id批量获取按钮</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskIds</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>buttons</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.List&lt;com.facishare.bpm.model.ActionButton&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>errorMsgs</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 重新解析业务流任务处理人

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/RefreshHandlerByTaskId</td>
            <td>RefreshHandlerByTaskId$Arg</td>
            <td>RefreshHandlerByTaskId$Result</td>
            <td>重新解析业务流任务处理人</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>
    
1. 获取任务详情

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetTask</td>
            <td>GetTask$Arg</td>
            <td>GetTask$Result</td>
            <td>获取任务详情</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>bussCustomFlag</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>bpmTask</td>
                        <td>com.facishare.bpm.model.task.BPMTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 修改数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/Edit</td>
            <td>Edit$Arg</td>
            <td>Edit$Result</td>
            <td>修改数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>details</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectData</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>optionInfo</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>notValidate</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>originalData</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>originalDetails</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>
    
1. 更新并完成任务

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/UpdateDataAndCompleteTask</td>
            <td>UpdateDataAndCompleteTask$Arg</td>
            <td>UpdateDataAndCompleteTask$Result</td>
            <td>更新并完成任务</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>needGetNextTaskInfo</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>9</td>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>data</td>
                        <td>java.util.Map</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取当前人的某对象下待办任务列表

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetUncompletedTasksByObject</td>
            <td>GetUncompletedTasksByObject$Arg</td>
            <td>GetUncompletedTasksByObject$Result</td>
            <td>获取当前人的某对象下待办任务列表</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>apiName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskOutlines</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.TaskOutline&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 阶段视图任务查询

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/GetWorkflowTasks_BACK</td>
            <td>GetWorkflowTasks$Arg</td>
            <td>GetWorkflowTasks$Result</td>
            <td>阶段视图任务查询</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>type</td>
                        <td>com.facishare.bpm.model.task.LaneBriefTaskVO$QueryType</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>state</td>
                        <td>com.facishare.bpm.model.task.LaneBriefTaskVO$QueryState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>taskList</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.task.LaneBriefTaskVO&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>totalCount</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 完成任务并保存任务相关数据

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/ProcessTask/CompleteAndCreateTaskData</td>
            <td>CompleteAndCreateTaskData$Arg</td>
            <td>CompleteTask$Result</td>
            <td>完成任务并保存任务相关数据</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>needGetNextTaskInfo</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务对应的activityId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>activityInstanceId</td>
                        <td>java.lang.Integer</td>
                        <td>true</td>
                        <td>任务对应的activityInstanceId</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>true</td>
                        <td>数据内容</td>
                        <td>batchAddRelatedObject(entityId: 批量创建数据的apiname,objectId: 批量创建数据的数据Id)</td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>executionType</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务类型</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>msg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
-------

涉及到的对象


1. com.facishare.flow.mongo.bizdb.entity.PoolEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lanes</td>
                <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.LaneEntity&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterActionExecution$AfterActionOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>retry</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ignore</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.task.BPMTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>unProcessIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>processIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assigneeIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>completed</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>canceled</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>remindLatency</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyUnit</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateCount</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinions</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assignee</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTaskOwner</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>extension</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>buttonMap</td>
                <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rule</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rejectRule</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assignNextTask</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>reminders</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateModifyLog</td>
                <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>data</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>transitions</td>
                <td>java.util.List&lt;com.facishare.bpm.model.SimpleTransition&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>currentEntityDescribe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nextTaskAssigneeScope</td>
                <td>java.util.Set&lt;java.lang.String&gt;</td>
                <td>指定下一节点处理人范围</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>relatedDescribe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td>6.6 新增,只新建时下发要新建的对象的describe</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>describe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td>6.6 新增,添加当前任务的describe,销售线索终端使用</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>layout</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td>6.7,由于支持了布局规则,需要在老接口中添加此字段</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>enableLayoutRules</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>showDataChangeLog</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppType</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkApp</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppEnable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delay</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyInfo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.task.TaskLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>time</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assigeeId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>excutionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>result</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>oldData</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>newData</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. TaskState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeout</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>normal</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskTypeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionState</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionErrorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateFieldJson</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>requestId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionMapping</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>recipients</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>title</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>content</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerParam</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.Pool
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lanes</td>
                <td>java.util.List&lt;com.facishare.bpm.model.Pool$Lane&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.ActionButton
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>action</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>right</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>event</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>btnClass</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>order</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.task.NextTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>nextTaskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.task.LaneBriefTaskVO
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entryApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entryName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entryObjectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>detailEntityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>detailEntityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>detailObjectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>detailObjectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>instanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>externalApplyTask</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>approve</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>create</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>update</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>addRelatedObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>batchAddRelatedObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>batchEditMasterDetailObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>addMDObject</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latency</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateAndComplete</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateLookup</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operationMulti</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.manage.MoreOperationManager$MoreOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.controller.web.model.GetTaskInfoByLaneId$LaneTaskInfo
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>processIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionType</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>button</td>
                <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>data</td>
                <td>com.facishare.bpm.handler.task.detail.model.StandardData</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinions</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateModifyLog</td>
                <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppType</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkApp</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppEnable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>moreOperations</td>
                <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>externalApply</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assignNextTask</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nextTaskAssigneeScope</td>
                <td>java.util.Set&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTaskOwner</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>hasForm</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.SimpleTransition
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>toActivityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>serialNumber</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isDefault</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. AfterActionExecution$SimpleAfterActionExecution
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>executionId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actions</td>
                <td>java.util.List&lt;AfterAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>java.util.List&lt;AfterActionExecution$AfterActionOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.handler.task.detail.model.StandardData
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>appCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionLabel</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>object_describe_api_name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedObjectId</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedObjectName</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>target_related_list_name</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>onlyRelatedObject</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>reminders</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedDescribe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>data</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layout</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describeExt</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectFlowLayoutExists</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layoutType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layoutApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>hasPermissions</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enableLayoutRules</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectPermissions</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Boolean&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isAfterActionWaiting</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>joiner</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldObjectHasValue</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldObjectIsIdentical</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>commonButtonApiNames</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>formEditable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. Opinion
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>replyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.rule.RuleMessage
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tip</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditionPattern</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditions</td>
                <td>java.util.List&lt;java.util.List&lt;com.facishare.bpm.rule.RuleMessage$Condition&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. Task$ApproverModifyLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyOpinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>beforeModifyPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>afterModifyPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.Pool$Lane
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>current</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.model.TaskOutline
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>processIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionTypeEnum</td>
                <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>moreOperations</td>
                <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkApp</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppEnable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTaskOwner</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>processName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>endTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entryType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>completed</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>belongToCurrentObj</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pools</td>
                <td>java.util.List&lt;com.facishare.bpm.model.Pool&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assignee</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assigneeIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>unProcessIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyUnit</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>remindLatency</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isTimeout</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.flow.mongo.bizdb.entity.LaneEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activities</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
            </table>
