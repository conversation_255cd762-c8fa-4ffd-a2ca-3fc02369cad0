## 组织架构相关接口

>最后一次更新时间 


1. 获取组织结构信息

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/BPMOrganization/GetOrganization</td>
            <td>GetOrganization$Arg</td>
            <td>GetOrganization$Result</td>
            <td>获取组织结构信息</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>type</td>
                        <td>java.lang.Object</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>1</td>
                        <td>person</td>
                        <td>java.util.List&lt;java.lang.Integer&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>CRMGroup</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>role</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>dept</td>
                        <td>java.util.List&lt;java.lang.Integer&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>externalPerson</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>linkAppId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>7</td>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>8</td>
                        <td>externalRole</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>person</td>
                        <td>java.util.Map&lt;java.lang.Integer, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>CRMGroup</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.CRMGroup&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>role</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Role&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>dept</td>
                        <td>java.util.Map&lt;java.lang.Integer, com.facishare.bpm.remote.model.org.Department&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>5</td>
                        <td>externalPerson</td>
                        <td>java.util.Map&lt;java.lang.Integer, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>6</td>
                        <td>externalRole</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.rest.api.model.enterpriserelation.GetOutRolesByTenantId$SimpleRoleResult&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
-------

涉及到的对象


1. com.facishare.bpm.remote.model.org.Role
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.rest.api.model.enterpriserelation.GetOutRolesByTenantId$SimpleRoleResult
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>roleId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>roleName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.CRMGroup
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
            </table>

1. com.facishare.bpm.remote.model.org.Department
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>parentId</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>status</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
            </table>
