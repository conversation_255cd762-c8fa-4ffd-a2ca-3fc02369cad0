## 企业相关接口

>最后一次更新时间 


1. 查询是否还有配额

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/Tenant/GetDefinitionConfig</td>
            <td>GetDefinitionConfig$Arg</td>
            <td>GetDefinitionConfig$Result</td>
            <td>查询是否还有配额</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>supportExternalFlow</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 获取配额详细信息

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/Tenant/GetTenantQuota</td>
            <td></td>
            <td>GetTenantQuota$Result</td>
            <td>获取配额详细信息</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>allowable</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>2</td>
                        <td>currentNumber</td>
                        <td>long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>3</td>
                        <td>limitedNumber</td>
                        <td>long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>4</td>
                        <td>marketNumber</td>
                        <td>long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
1. 查询是否还有配额

    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>QueryName</th>
            <th>ArgumentType</th>
            <th>ResultType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>FHH/EM1HBPM/Tenant/HasQuota</td>
            <td>HasQuota$Arg</td>
            <td>HasQuota$Result</td>
            <td>查询是否还有配额</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>#</th>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>1</td>
                        <td>hasQuota</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>
    
