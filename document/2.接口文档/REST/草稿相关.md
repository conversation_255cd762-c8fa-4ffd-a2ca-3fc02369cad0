## 草稿相关

>[Rest消息头和返回值外壳参考公司rest规范](http://wiki.firstshare.cn/pages/viewpage.action?pageId=39586926)
>最后一次更新时间 
---

1.  --
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/draft//ping</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>value</td>
                        <td>char[]</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>hash</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>serialVersionUID</td>
                        <td>long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>serialPersistentFields</td>
                        <td>java.io.ObjectStreamField[]</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>CASE_INSENSITIVE_ORDER</td>
                        <td>java.util.Comparator&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  保存流程定义草稿
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/draft/create</td>
            <td>POST</td>
            <td>application/json</td>
            <td>新增接口,暂无复用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>sourceName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>草稿来源名称</td>
                        <td>定义保存草稿后,记录老版本定义的名称</td>
                    </tr>
                                    <tr>
                        <td>outlineId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>outlineId</td>
                        <td>业务流程自存储Id</td>
                    </tr>
                                    <tr>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>outlineId</td>
                        <td>业务流程自存库数据id</td>
                    </tr>
                                    <tr>
                        <td>tenantId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>企业Id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>userId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>用户id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>流程定义唯一标识</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>流程定义版本id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>流程名</td>
                        <td>流程名称不允许重复</td>
                    </tr>
                                    <tr>
                        <td>count</td>
                        <td>int</td>
                        <td>false</td>
                        <td>启动次数</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>enabled</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>是否激活</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>description</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>定义描述</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>entryType</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>入口对象类型</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>entryTypeName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>入口对象名称</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeEmployeeIds</td>
                        <td>java.util.List&lt;java.lang.Integer&gt;</td>
                        <td>false</td>
                        <td>可见人范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeCircleIds</td>
                        <td>java.util.List&lt;java.lang.Integer&gt;</td>
                        <td>false</td>
                        <td>可见部门范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeGroupIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td>可见组范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeRoleIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td>角色范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>createdBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>创建人</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>createTime</td>
                        <td>long</td>
                        <td>false</td>
                        <td>创建时间</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>lastModifiedBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>更新人</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>lastModifiedTime</td>
                        <td>long</td>
                        <td>false</td>
                        <td>更新时间</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>workflow</td>
                        <td>com.facishare.bpm.bpmn.ExecutableWorkflowExt</td>
                        <td>false</td>
                        <td>存储的是引擎的定义结构</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rule</td>
                        <td>com.facishare.bpm.bpmn.WorkflowRule</td>
                        <td>false</td>
                        <td>触发规则</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>保存定义的svg文件</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>extension</td>
                        <td>com.facishare.bpm.model.WorkflowExtension</td>
                        <td>false</td>
                        <td>保存扩展信息,泳道泳池</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>templateId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>模板id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>externalFlow</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>外部流程:1,非外部流程:0或空</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>singleInstanceFlow</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>单实例流程:1,非单实例流程:0或空</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>isDeleted</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>删除状态</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>hasInstance</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>流程定义是否被启用,更新的时候必填字段</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>draftId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>草稿id</td>
                        <td>草稿id,保存草稿->保存定义时需要知道是哪个草稿专的定义,要删除</td>
                    </tr>
                                    <tr>
                        <td>supportFlow</td>
                        <td>com.facishare.flow.mongo.bizdb.entity.SupportFlow</td>
                        <td>false</td>
                        <td>扩展业务流程标识,目前支持营销流程:1</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td>是否启用互联</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>互联应用id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>互联应用名称，如PRM</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>互联应用类型，1:企业互联，2:客户互联</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  更新流程定义草稿
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/draft/update</td>
            <td>POST</td>
            <td>application/json</td>
            <td>新增接口,暂无复用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>sourceName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>草稿来源名称</td>
                        <td>定义保存草稿后,记录老版本定义的名称</td>
                    </tr>
                                    <tr>
                        <td>outlineId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>outlineId</td>
                        <td>业务流程自存储Id</td>
                    </tr>
                                    <tr>
                        <td>log</td>
                        <td>org.slf4j.Logger</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>outlineId</td>
                        <td>业务流程自存库数据id</td>
                    </tr>
                                    <tr>
                        <td>tenantId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>企业Id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>userId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>用户id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>流程定义唯一标识</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>流程定义版本id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>流程名</td>
                        <td>流程名称不允许重复</td>
                    </tr>
                                    <tr>
                        <td>count</td>
                        <td>int</td>
                        <td>false</td>
                        <td>启动次数</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>enabled</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>是否激活</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>description</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>定义描述</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>entryType</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>入口对象类型</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>entryTypeName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>入口对象名称</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeEmployeeIds</td>
                        <td>java.util.List&lt;java.lang.Integer&gt;</td>
                        <td>false</td>
                        <td>可见人范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeCircleIds</td>
                        <td>java.util.List&lt;java.lang.Integer&gt;</td>
                        <td>false</td>
                        <td>可见部门范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeGroupIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td>可见组范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rangeRoleIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td>角色范围</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>createdBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>创建人</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>createTime</td>
                        <td>long</td>
                        <td>false</td>
                        <td>创建时间</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>lastModifiedBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>更新人</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>lastModifiedTime</td>
                        <td>long</td>
                        <td>false</td>
                        <td>更新时间</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>workflow</td>
                        <td>com.facishare.bpm.bpmn.ExecutableWorkflowExt</td>
                        <td>false</td>
                        <td>存储的是引擎的定义结构</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>rule</td>
                        <td>com.facishare.bpm.bpmn.WorkflowRule</td>
                        <td>false</td>
                        <td>触发规则</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>保存定义的svg文件</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>extension</td>
                        <td>com.facishare.bpm.model.WorkflowExtension</td>
                        <td>false</td>
                        <td>保存扩展信息,泳道泳池</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>templateId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>模板id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>externalFlow</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>外部流程:1,非外部流程:0或空</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>singleInstanceFlow</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>单实例流程:1,非单实例流程:0或空</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>isDeleted</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>删除状态</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>hasInstance</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td>流程定义是否被启用,更新的时候必填字段</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>draftId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>草稿id</td>
                        <td>草稿id,保存草稿->保存定义时需要知道是哪个草稿专的定义,要删除</td>
                    </tr>
                                    <tr>
                        <td>supportFlow</td>
                        <td>com.facishare.flow.mongo.bizdb.entity.SupportFlow</td>
                        <td>false</td>
                        <td>扩展业务流程标识,目前支持营销流程:1</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td>是否启用互联</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>互联应用id</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>互联应用名称，如PRM</td>
                        <td>--</td>
                    </tr>
                                    <tr>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>互联应用类型，1:企业互联，2:客户互联</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  删除流程定义草稿
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/draft/delete</td>
            <td>POST</td>
            <td>application/json</td>
            <td>新增接口,暂无复用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>outlineId</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  查询草稿列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/draft/getList</td>
            <td>POST</td>
            <td>application/json</td>
            <td>新增接口,暂无复用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>keyWord</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>supportFlow</td>
                        <td>com.facishare.flow.mongo.bizdb.entity.SupportFlow</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>totalCount</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>outlines</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.WorkflowOutlineDraft&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  查询流程的草稿
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/draft/getDraftById</td>
            <td>POST</td>
            <td>application/json</td>
            <td>新增接口,暂无复用</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>workflowOutline</td>
                        <td>com.facishare.bpm.model.WorkflowOutlineDraft</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


-------

涉及到的对象


1. com.facishare.bpm.model.WorkflowExtension
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pools</td>
                <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.PoolEntity&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>diagram</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>svg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.bpmn.TransitionExt
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>CONDITION</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>FROM_ID</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>TO_ID</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>SERIAL_NUMBER</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.flow.mongo.bizdb.entity.PoolEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lanes</td>
                <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.LaneEntity&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.bpmn.VariableExt
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
        
    </table>

1. com.facishare.bpm.bpmn.ExecutableWorkflowExt
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activities</td>
                <td>java.util.List&lt;com.facishare.bpm.bpmn.ActivityExt&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>transitions</td>
                <td>java.util.List&lt;com.facishare.bpm.bpmn.TransitionExt&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>variables</td>
                <td>java.util.List&lt;com.facishare.bpm.bpmn.VariableExt&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.WorkflowOutlineDraft
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceName</td>
                <td>java.lang.String</td>
                <td>草稿来源名称</td>
                <td>定义保存草稿后,记录老版本定义的名称</td>
            </tr>
                    <tr>
                <td>outlineId</td>
                <td>java.lang.String</td>
                <td>outlineId</td>
                <td>业务流程自存储Id</td>
            </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td>outlineId</td>
                <td>业务流程自存库数据id</td>
            </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td>企业Id</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td>用户id</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td>流程定义唯一标识</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td>流程定义版本id</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td>流程名</td>
                <td>流程名称不允许重复</td>
            </tr>
                    <tr>
                <td>count</td>
                <td>int</td>
                <td>启动次数</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>enabled</td>
                <td>boolean</td>
                <td>是否激活</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td>定义描述</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>entryType</td>
                <td>java.lang.String</td>
                <td>入口对象类型</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>entryTypeName</td>
                <td>java.lang.String</td>
                <td>入口对象名称</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>rangeEmployeeIds</td>
                <td>java.util.List&lt;java.lang.Integer&gt;</td>
                <td>可见人范围</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>rangeCircleIds</td>
                <td>java.util.List&lt;java.lang.Integer&gt;</td>
                <td>可见部门范围</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>rangeGroupIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td>可见组范围</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>rangeRoleIds</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td>角色范围</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>createdBy</td>
                <td>java.lang.String</td>
                <td>创建人</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>long</td>
                <td>创建时间</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>lastModifiedBy</td>
                <td>java.lang.String</td>
                <td>更新人</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>lastModifiedTime</td>
                <td>long</td>
                <td>更新时间</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>workflow</td>
                <td>com.facishare.bpm.bpmn.ExecutableWorkflowExt</td>
                <td>存储的是引擎的定义结构</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>rule</td>
                <td>com.facishare.bpm.bpmn.WorkflowRule</td>
                <td>触发规则</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>svg</td>
                <td>java.lang.String</td>
                <td>保存定义的svg文件</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>extension</td>
                <td>com.facishare.bpm.model.WorkflowExtension</td>
                <td>保存扩展信息,泳道泳池</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>templateId</td>
                <td>java.lang.String</td>
                <td>模板id</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>externalFlow</td>
                <td>java.lang.Integer</td>
                <td>外部流程:1,非外部流程:0或空</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>singleInstanceFlow</td>
                <td>java.lang.Integer</td>
                <td>单实例流程:1,非单实例流程:0或空</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>isDeleted</td>
                <td>boolean</td>
                <td>删除状态</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>hasInstance</td>
                <td>boolean</td>
                <td>流程定义是否被启用,更新的时候必填字段</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>draftId</td>
                <td>java.lang.String</td>
                <td>草稿id</td>
                <td>草稿id,保存草稿->保存定义时需要知道是哪个草稿专的定义,要删除</td>
            </tr>
                    <tr>
                <td>supportFlow</td>
                <td>com.facishare.flow.mongo.bizdb.entity.SupportFlow</td>
                <td>扩展业务流程标识,目前支持营销流程:1</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>linkAppEnable</td>
                <td>java.lang.Boolean</td>
                <td>是否启用互联</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>linkApp</td>
                <td>java.lang.String</td>
                <td>互联应用id</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td>互联应用名称，如PRM</td>
                <td>--</td>
            </tr>
                    <tr>
                <td>linkAppType</td>
                <td>java.lang.Integer</td>
                <td>互联应用类型，1:企业互联，2:客户互联</td>
                <td>--</td>
            </tr>
        
    </table>

1. com.facishare.bpm.bpmn.WorkflowRule
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>ruleId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>deleted</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>appId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ruleType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerTypes</td>
                <td>java.util.List&lt;java.lang.Integer&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditionPattern</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditions</td>
                <td>java.util.List&lt;com.facishare.bpm.bpmn.WorkflowRule$RuleCondition&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>tip</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowSrcId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>createTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>creator</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifier</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.flow.mongo.bizdb.entity.LaneEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activities</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.bpmn.WorkflowRule$ConditionField
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>fieldName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>fieldSrc</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>fieldType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>value</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>metadata</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.bpmn.ActivityExt
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
        
    </table>

1. com.facishare.bpm.bpmn.WorkflowRule$RuleCondition
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>leftSide</td>
                <td>com.facishare.bpm.bpmn.WorkflowRule$ConditionField</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operator</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rightSide</td>
                <td>com.facishare.bpm.bpmn.WorkflowRule$ConditionField</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>int</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

