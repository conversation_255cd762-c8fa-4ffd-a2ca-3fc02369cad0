## 任务相关

>[Rest消息头和返回值外壳参考公司rest规范](http://wiki.firstshare.cn/pages/viewpage.action?pageId=39586926)
>最后一次更新时间 
---

1.  只支持完成任务，和接收审批意见，不支持选择和创建类型的任务处理
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/complete</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>approvalResult</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>assignNextTaskProcessors</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  获取任务
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/getTask</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>applyButtons</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>notGetData</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>includeTaskFeedDetailConfig</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>description</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>laneName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>laneId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>state</td>
                        <td>TaskState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>processIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>latencyUnit</td>
                        <td>int</td>
                        <td>--</td>
                        <td>1-天；2-小时；3-分钟</td>
                    </tr>
                                    <tr>
                        <td>remindLatency</td>
                        <td>java.lang.Object</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>isTimeout</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>executionType</td>
                        <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>taskType</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>execution</td>
                        <td>AfterActionExecution$SimpleAfterActionExecution</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>opinions</td>
                        <td>java.util.List&lt;Opinion&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>candidateModifyLog</td>
                        <td>java.util.List&lt;Task$ApproverModifyLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>errorMsg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>assignNextTask</td>
                        <td>int</td>
                        <td>--</td>
                        <td>是否需要指定下一节点处理人1 指定，非1 不指定</td>
                    </tr>
                                    <tr>
                        <td>nextTaskAssigneeScope</td>
                        <td>java.util.Set&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>button</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.model.ActionButton&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>data</td>
                        <td>com.facishare.bpm.handler.task.detail.model.StandardData</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>moreOperations</td>
                        <td>java.util.List&lt;com.facishare.bpm.manage.MoreOperationManager$MoreOperation&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>createTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>modifyTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>activityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>activityInstanceId</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>linkAppName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>linkAppType</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>linkApp</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>linkAppEnable</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>isOwner</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>todoJumpUrl</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>externalApply</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pools</td>
                        <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.PoolEntity&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>config</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  获取任务详情
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/detail</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>activityInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>taskType</td>
                        <td>com.facishare.bpm.util.verifiy.TaskType</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>opinions</td>
                        <td>java.util.List&lt;Opinion&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>assignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>state</td>
                        <td>TaskState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>isTaskOwner</td>
                        <td>java.lang.Boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>executionType</td>
                        <td>com.facishare.bpm.model.paas.engine.bpm.BPMConstants$ExecutionTypeEnum</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>data</td>
                        <td>com.facishare.bpm.handler.task.form.model.CustomerData</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>buttons</td>
                        <td>java.util.Collection&lt;com.facishare.bpm.model.ActionButton&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>modifyTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>externalApplyTask</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>assignNextTask</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>createTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>errorMsg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>afterActionExecution</td>
                        <td>AfterActionExecution$SimpleAfterActionExecution</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  更换任务处理人
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/changeTaskHandler</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>modifyOpinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  替换任务处理人
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/replaceTaskHandler</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>candidateIds</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>modifyOpinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>result</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  完成任务
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/completeTask</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>assignNextTaskProcessors</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  审批任务  处理审批节点的任务
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/approvalTask</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>approvalResult</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>assignNextTaskProcessors</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  --
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/after/action/retry</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>rowNum</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>executeType</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>success</td>
                        <td>boolean</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>message</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  重新解析任务处理人
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task//refreshHandlerByTaskId</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>任务id</td>
                        <td>--</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  根据objectId获取instanceId查询等待节点
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task//findDelayTask</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>objectId</td>
                        <td>数据id</td>
                    </tr>
                                    <tr>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td>workflowInstanceId</td>
                        <td>实例id</td>
                    </tr>
                                    <tr>
                        <td>pageNumber</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>pageNumber</td>
                        <td>分页查询 当前页数</td>
                    </tr>
                                    <tr>
                        <td>pageSize</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td>pageSize</td>
                        <td>分页查询 当前页最大任务数</td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  更新并完成任务,  布局规则 &验证规则不会校验  需要调用方自行校验
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/task/updateDataAndCompleteTask</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>taskId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>data</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>nextTaskAssignee</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>addOrReplaceNextTaskAssignee</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>needGetNextTaskInfo</td>
                        <td>java.lang.Boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>result</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>opinion</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>data</td>
                        <td>java.util.Map</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>ruleMessage</td>
                        <td>com.facishare.bpm.rule.RuleMessage</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>sleepTime</td>
                        <td>java.lang.Integer</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>nextTask</td>
                        <td>com.facishare.bpm.model.task.NextTask</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


-------

涉及到的对象


1. com.facishare.flow.mongo.bizdb.entity.PoolEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lanes</td>
                <td>java.util.List&lt;com.facishare.flow.mongo.bizdb.entity.LaneEntity&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. AfterActionExecution$AfterActionOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>retry</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ignore</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. AfterAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskTypeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionState</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionErrorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateFieldJson</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>requestId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionMapping</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>recipients</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>title</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>content</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerParam</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.ActionButton
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>action</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>right</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>event</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>btnClass</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>order</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.handler.task.form.model.CustomerData
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>data</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layout</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describeExt</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.task.NextTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>nextTaskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityInstanceId</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowInstanceId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.manage.MoreOperationManager$MoreOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>label</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>code</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. AfterActionExecution$SimpleAfterActionExecution
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>executionId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actions</td>
                <td>java.util.List&lt;AfterAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>java.util.List&lt;AfterActionExecution$AfterActionOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.handler.task.detail.model.StandardData
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>appCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionLabel</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>object_describe_api_name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectName</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedObjectId</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedObjectName</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>target_related_list_name</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedEntityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>onlyRelatedObject</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>reminders</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedDescribe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>data</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layout</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describe</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>describeExt</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectFlowLayoutExists</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layoutType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>layoutApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>hasPermissions</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enableLayoutRules</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectPermissions</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Boolean&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isAfterActionWaiting</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>todoJumpUrl</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>joiner</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldObjectHasValue</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>relatedFieldObjectIsIdentical</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>commonButtonApiNames</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>formEditable</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. Opinion
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>replyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. Task$ApproverModifyLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyOpinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>beforeModifyPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>afterModifyPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.rule.RuleMessage
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tip</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditionPattern</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>conditions</td>
                <td>java.util.List&lt;java.util.List&lt;com.facishare.bpm.rule.RuleMessage$Condition&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.flow.mongo.bizdb.entity.LaneEntity
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>description</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activities</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

