## 实例相关

>[Rest消息头和返回值外壳参考公司rest规范](http://wiki.firstshare.cn/pages/viewpage.action?pageId=39586926)
>最后一次更新时间 
---

1.  --
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/detail</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>workflow</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowInstance</td>
                        <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>svg</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  --
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/cancel</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>reason</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


1.  查询实例列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/list</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>outlineId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowInstances</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.instance.WorkflowInstanceVO&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  获取流程实例日志根据流程实例id
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/getWorkflowInstanceLog</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>workflowInstanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>name</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>startTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>endTime</td>
                        <td>java.lang.Long</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>logs</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.TaskLog&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>cancelPersonId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>reason</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>employeeInfo</td>
                        <td>java.util.Map&lt;java.lang.String, com.facishare.bpm.remote.model.org.Employee&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>submitter</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>execution</td>
                        <td>java.util.Map&lt;java.lang.String, AfterActionExecution$SimpleAfterActionExecution&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  查询实例列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/getInstanceList</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>outlineId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowInstances</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.instance.WorkflowInstanceVO&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  根据entityId和objectId 查询实例列表
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance//list/data</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>outlineId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>state</td>
                        <td>InstanceState</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageNumber</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageSize</td>
                        <td>int</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>orderBy</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>total</td>
                        <td>int</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowInstances</td>
                        <td>java.util.List&lt;com.facishare.bpm.model.instance.WorkflowInstanceVO&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  --
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/progress</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageSize</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>pageNumber</td>
                        <td>java.lang.Integer</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>instanceId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>workflowName</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>applicantId</td>
                        <td>java.lang.String</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>tasks</td>
                        <td>java.util.List&lt;com.facishare.bpm.plugins.model.GetInstance$PluginTask&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


1.  --
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/instance/start</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>id</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>业务流id</td>
                        <td>要触发的业务流outlineId</td>
                    </tr>
                                    <tr>
                        <td>objectId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>数据Id</td>
                        <td>基于哪条数据触发业务流</td>
                    </tr>
                                    <tr>
                        <td>entityId</td>
                        <td>java.lang.String</td>
                        <td>true</td>
                        <td>对象apiname</td>
                        <td>触发哪个对象的业务流</td>
                    </tr>
                                    <tr>
                        <td>source</td>
                        <td>TriggerSource</td>
                        <td>true</td>
                        <td>触发来源</td>
                        <td>person(手动触发), bpm(业务流程), workflow(工作流), approval(审批流), openapi(外部调用), market(营销流程)</td>
                    </tr>
                                    <tr>
                        <td>ignoreMultiInstanceException</td>
                        <td>boolean</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>sourceWorkflowId</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                            <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                    </table>


-------

涉及到的对象


1. com.facishare.bpm.model.instance.WorkflowInstanceVO
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>applicantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>start</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>end</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerSource</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppType</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>workflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>sourceWorkflowId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>canceler</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. AfterActionExecution$AfterActionOperation
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>retry</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>ignore</td>
                <td>AfterActionExecution$AfterActionOperation</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.ChangeCandidateLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>operatorId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>from</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>to</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. TaskState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeout</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>normal</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. AfterAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskTypeName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionState</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>rowNo</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionErrorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>modifyTime</td>
                <td>long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>updateFieldJson</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>requestId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionMapping</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>recipients</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>title</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>content</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>triggerParam</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.TaskLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>desc</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>type</td>
                <td>com.facishare.bpm.model.UserTaskType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>startTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>endTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinions</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>execution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeoutExecution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>errorMsg</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>unCompletedPersons</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>entityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objectId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>remindLatency</td>
                <td>java.lang.Object</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyUnit</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>candidateLogs</td>
                <td>java.util.List&lt;com.facishare.bpm.model.ChangeCandidateLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskDelegateLogs</td>
                <td>java.util.List&lt;com.facishare.bpm.model.TaskDelegateViewLog&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nextTaskAssignee</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>nodeType</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>linkAppName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>activityId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delay</td>
                <td>java.lang.Boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executeTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operations</td>
                <td>java.util.List&lt;com.facishare.bpm.model.TaskLog$NodeAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executor</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. InstanceState
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>in_progress</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>cancel</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>error</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>in_progress_or_error</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>pass_or_cancel</td>
                <td>InstanceState</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.plugins.model.GetInstance$PluginTask
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>taskName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>laneId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>state</td>
                <td>TaskState</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>timeout</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.util.List&lt;Opinion&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>afterActionExecution</td>
                <td>AfterActionExecution$SimpleAfterActionExecution</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>extension</td>
                <td>java.util.Map&lt;java.lang.String, java.lang.Object&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>assignee</td>
                <td>java.util.Map&lt;java.lang.String, java.util.List&lt;java.lang.String&gt;&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.TaskDelegateViewLog
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>log</td>
                <td>org.slf4j.Logger</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>delegateId</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>trusteeId</td>
                <td>java.util.List&lt;java.lang.String&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.UserTaskType
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>taskTypes</td>
                <td>java.util.List&lt;com.facishare.bpm.model.UserTaskType&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>type</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>subType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. AfterActionExecution$SimpleAfterActionExecution
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>executionId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actions</td>
                <td>java.util.List&lt;AfterAction&gt;</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>operation</td>
                <td>java.util.List&lt;AfterActionExecution$AfterActionOperation&gt;</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.remote.model.org.Employee
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>id</td>
                <td>java.lang.Integer</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>name</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>isStop</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>profileImage</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outTenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>outUser</td>
                <td>boolean</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>enterpriseShortName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. com.facishare.bpm.model.TaskLog$NodeAction
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>actionCode</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. Opinion
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>tenantId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>userId</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>actionType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>opinion</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>replyTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

1. NodeType
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>userTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>executionTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>latencyTask</td>
                <td>NodeType</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

