## 元数据相关

>[Rest消息头和返回值外壳参考公司rest规范](http://wiki.firstshare.cn/pages/viewpage.action?pageId=39586926)
>最后一次更新时间 
---

1.  获取对象描述
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Path</th>
            <th>Method</th>
            <th>ContentType</th>
            <th>Description</th>
        </tr>
        <tr>
            <td>plugins/metadata/getDescribeList</td>
            <td>POST</td>
            <td>application/json</td>
            <td>--</td>
        </tr>
    </table>

    1. 参数
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>packageName</td>
                        <td>java.lang.String</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    <tr>
                        <td>apiNames</td>
                        <td>java.util.List&lt;java.lang.String&gt;</td>
                        <td>false</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>

    1. 返回值
        <table class="table table-bordered table-striped table-condensed">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Label</th>
                <th>Description</th>
            </tr>
                                                <tr>
                        <td>descs</td>
                        <td>java.util.List&lt;com.facishare.bpm.plugins.model.GetDescribeList$PluginSimpleMetaDesc&gt;</td>
                        <td></td>
                        <td></td>
                    </tr>
                                    </table>


-------

涉及到的对象


1. com.facishare.bpm.plugins.model.GetDescribeList$PluginSimpleMetaDesc
    <table class="table table-bordered table-striped table-condensed">
        <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Label</th>
            <th>Description</th>
        </tr>
                    <tr>
                <td>displayName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>objApiName</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>defineType</td>
                <td>java.lang.String</td>
                <td></td>
                <td></td>
            </tr>
                    <tr>
                <td>lastModifiedTime</td>
                <td>java.lang.Long</td>
                <td></td>
                <td></td>
            </tr>
        
    </table>

