{"result": {"describe": {"fields": {"error_reason": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1661356005027, "is_encrypted": false, "pattern": "", "description": "异常原因", "is_unique": false, "label": "异常原因", "type": "long_text", "api_name": "error_reason", "define_type": "package", "_id": "630647e58f1a5700019ec088", "is_index_field": false, "is_single": false, "index_name": "t_6", "max_length": 5000, "status": "new"}, "cancel_reason": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1661356109118, "is_encrypted": false, "pattern": "", "description": "终止原因", "is_unique": false, "label": "终止原因", "type": "long_text", "api_name": "cancel_reason", "define_type": "package", "_id": "6306484d8f1a5700019ec09c", "is_index_field": false, "is_single": false, "index_name": "t_7", "max_length": 5000, "status": "new"}, "cancel_from_person": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1661356219846, "is_encrypted": false, "description": "终止人", "is_unique": false, "label": "终止人", "type": "employee", "api_name": "cancel_from_person", "define_type": "package", "_id": "630648bb8f1a5700019ec0b3", "is_index_field": false, "is_single": false, "index_name": "a_6", "max_length": 5000, "status": "new"}, "relevant_team": {"describe_api_name": "BpmInstance", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1546430083589, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "627bd4c1956f75000148e07c", "is_index_field": false, "is_single": false, "index_name": "a_team", "help_text": "相关团队", "status": "released"}, "applicantId": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083594, "description": "业务流程实例的发起人", "is_unique": false, "label": "流程发起人", "type": "employee", "is_required": false, "api_name": "applicantId", "define_type": "package", "_id": "627bd4c1956f75000148e07d", "is_index_field": false, "is_single": false, "index_name": "a_1", "status": "released"}, "owner": {"describe_api_name": "BpmInstance", "description": "负责人", "is_unique": false, "type": "employee", "decimal_places": 0, "is_required": false, "define_type": "package", "is_single": true, "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1546430083589, "length": 8, "label": "负责人", "is_need_convert": true, "api_name": "owner", "_id": "627bd4c1956f75000148e07e", "is_index_field": false, "round_mode": 4, "status": "released"}, "sourceWorkflowId": {"describe_api_name": "BpmInstance", "is_index": false, "is_active": true, "create_time": 1546430083589, "pattern": "", "description": "业务流程唯一标示", "is_unique": false, "label": "流程apiName", "type": "text", "is_required": false, "api_name": "sourceWorkflowId", "define_type": "package", "_id": "627bd4c1956f75000148e07f", "is_index_field": false, "is_single": false, "index_name": "t_2", "max_length": 256, "status": "released"}, "taskNames": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "description": "实例任务所属", "is_unique": false, "label": "当期任务", "type": "tag", "is_required": false, "api_name": "taskNames", "define_type": "package", "_id": "627bd4c1956f75000148e080", "is_index_field": false, "is_single": false, "index_name": "a_5", "status": "released"}, "lastModifyTime": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "description": "业务流程实例最后更新时间", "is_unique": false, "label": "最后更新时间", "type": "date_time", "time_zone": "GMT+8", "is_required": false, "api_name": "lastModifyTime", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "627bd4c1956f75000148e081", "is_index_field": false, "is_single": false, "index_name": "l_1", "status": "released"}, "name": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "pattern": "", "description": "业务流程实例主题:流程名称(发起时间)", "is_unique": false, "label": "流程主题", "type": "text", "is_required": false, "api_name": "name", "define_type": "system", "_id": "627bd4c1956f75000148e082", "is_index_field": false, "is_single": false, "index_name": "name", "max_length": 256, "status": "released"}, "objectApiName": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "pattern": "", "description": "业务流程发起对象", "is_unique": false, "label": "流程发起对象", "type": "text", "is_required": false, "api_name": "objectApiName", "define_type": "package", "_id": "627bd4c1956f75000148e083", "is_index_field": false, "is_single": false, "index_name": "t_3", "max_length": 256, "status": "released"}, "startTime": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "description": "业务流程实例发起时间", "is_unique": false, "label": "流程发起时间", "type": "date_time", "time_zone": "GMT+8", "is_required": false, "api_name": "startTime", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "627bd4c1956f75000148e084", "is_index_field": false, "is_single": false, "index_name": "l_2", "status": "released"}, "endTime": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "description": "业务流程实例结束时间", "is_unique": false, "label": "流程结束时间", "type": "date_time", "time_zone": "GMT+8", "is_required": false, "api_name": "endTime", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "627bd4c1956f75000148e085", "is_index_field": false, "is_single": false, "index_name": "l_3", "status": "released"}, "state": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083594, "description": "流程实例的状态", "is_unique": false, "label": "流程状态", "type": "select_one", "is_required": false, "api_name": "state", "options": [{"label": "进行中", "value": "in_progress"}, {"label": "已完成", "value": "pass"}, {"label": "已终止", "value": "cancel"}, {"label": "异常", "value": "error"}], "define_type": "package", "option_id": "2433d4f9e1d4781688b4c9a3582b82d8", "_id": "627bd4c1956f75000148e086", "is_index_field": false, "is_single": false, "index_name": "s_3", "config": {}, "status": "released"}, "triggerSource": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083599, "description": "流程是由谁发起的", "is_unique": false, "label": "发起类型", "type": "select_one", "is_required": false, "api_name": "triggerSource", "options": [{"label": "手动发起", "value": "person"}, {"label": "业务流程触发", "value": "bpm"}, {"label": "工作流触发", "value": "workflow"}, {"label": "审批流触发", "value": "approval"}], "define_type": "package", "option_id": "3a8a7edc114a85f5e91d27d8058a48f9", "_id": "627bd4c1956f75000148e087", "is_index_field": false, "is_single": false, "index_name": "s_4", "config": {}, "status": "released"}, "workflowId": {"describe_api_name": "BpmInstance", "is_index": false, "is_active": true, "create_time": 1546430083599, "pattern": "", "description": "业务流程版本", "is_unique": false, "label": "流程定义", "type": "text", "is_required": false, "api_name": "workflowId", "define_type": "package", "_id": "627bd4c1956f75000148e088", "is_index_field": false, "is_single": false, "index_name": "t_5", "max_length": 256, "status": "released"}, "objectDataId": {"describe_api_name": "BpmInstance", "is_index": false, "is_active": true, "create_time": 1546430083599, "pattern": "", "description": "业务流程所发起的数据id", "is_unique": false, "label": "流程发起数据", "type": "text", "is_required": false, "api_name": "objectDataId", "define_type": "package", "_id": "627bd4c1956f75000148e089", "is_index_field": false, "is_single": false, "index_name": "t_4", "max_length": 256, "status": "released"}, "duration": {"expression_type": "js", "return_type": "number", "describe_api_name": "BpmInstance", "is_index": false, "is_active": true, "expression": "CASE($state$,'进行中', NOW().toTimeStamp()-$startTime$.toTimeStamp(),'已完成',IF($endTime$!=0&&$endTime$!=null,$endTime$.toTimeStamp()-$startTime$.toTimeStamp(),null),null)", "create_time": 1617379280395, "is_unique": false, "label": "流程耗时", "type": "formula", "is_abstract": true, "default_to_zero": true, "api_name": "duration", "define_type": "package", "_id": "627bd4c1956f75000148e08a", "is_index_field": false, "is_single": false, "index_name": "d_1", "status": "new"}, "lastModifyBy": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "description": "业务流程实例最后修改人", "is_unique": false, "label": "最后更新人", "type": "employee", "is_required": false, "api_name": "lastModifyBy", "define_type": "package", "_id": "627bd4c1956f75000148e08b", "is_index_field": false, "is_single": true, "index_name": "a_2", "status": "released"}, "relatedObject": {"describe_api_name": "BpmInstance", "description": "流程发起对象,包含对象类型和对象数据", "is_unique": true, "group_type": "what", "type": "group", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_1", "is_index": true, "is_active": true, "create_time": 1546430083589, "label": "发起对象", "api_name": "relatedObject", "_id": "627bd4c1956f75000148e08c", "is_index_field": false, "fields": {"id_field": "objectDataId", "api_name_field": "objectApiName"}, "help_text": "", "status": "released"}, "workflowName": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "pattern": "", "description": "业务流程名称", "is_unique": false, "label": "流程名称", "type": "text", "is_required": false, "api_name": "workflowName", "define_type": "package", "_id": "627bd4c1956f75000148e08d", "is_index_field": false, "is_single": false, "index_name": "t_1", "max_length": 256, "status": "released"}, "stageNames": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "create_time": 1546430083589, "description": "当前所属阶段", "is_unique": false, "label": "当期阶段", "type": "tag", "is_required": false, "api_name": "stageNames", "define_type": "package", "_id": "627bd4c1956f75000148e08e", "is_index_field": false, "is_single": false, "index_name": "a_3", "status": "released"}, "objectIds": {"describe_api_name": "BpmInstance", "is_index": false, "is_active": true, "create_time": 1546430083589, "description": "节点上对象id", "is_unique": false, "label": "节点流转对象Id", "type": "tag", "is_required": false, "api_name": "objectIds", "define_type": "package", "_id": "627bd4c1956f75000148e08f", "is_index_field": false, "is_single": false, "index_name": "a_4", "status": "released"}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "is_active": true, "api_name": "_id", "description": "_id", "status": "released", "index_name": "_id", "create_time": 1546430083588}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "is_active": true, "api_name": "tenant_id", "description": "tenant_id", "status": "released", "create_time": 1546430083588, "index_name": "ei"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name", "create_time": 1546430083588}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "index_name": "version", "create_time": 1546430083588}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by", "create_time": 1546430083588}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人", "create_time": 1546430083588}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "is_active": true, "api_name": "package", "description": "package", "status": "released", "create_time": 1546430083588, "index_name": "pkg"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time", "create_time": 1546430083588}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time", "create_time": 1546430083588}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "index_name": "is_del", "create_time": 1546430083588}, "out_tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "外部企业", "is_active": true, "api_name": "out_tenant_id", "description": "out_tenant_id", "status": "released", "index_name": "o_ei", "create_time": 1546430083588, "config": {"display": 0}}, "out_owner": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "index_name": "o_owner", "status": "released", "label": "外部负责人", "is_active": true, "config": {"display": 1}, "create_time": 1546430083588}, "data_own_department": {"type": "department", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "data_own_department", "status": "released", "label": "归属部门", "is_active": true, "index_name": "data_owner_dept_id", "create_time": 1546430083588}, "order_by": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "order_by", "api_name": "order_by", "description": "order_by", "status": "released", "index_name": "l_by", "create_time": 1546430083588}}, "actions": {}, "index_version": 1, "_id": "627bd4c1956f75000148e07b", "tenant_id": "-100", "is_udef": false, "api_name": "BpmInstance", "display_name": "业务流程实例", "package": "CRM", "is_active": true, "version": 5, "release_version": "6.4", "define_type": "package", "is_deleted": false, "last_modified_time": 1546430083588, "create_time": 1546430083588, "store_table_name": "bpm_instance", "description": "业务流程实例"}, "message": "OK"}}