{"result": {"describe": {"fields": {"link_app_type": {"describe_api_name": "BpmTask", "description": "外部业务类型", "is_unique": false, "type": "number", "decimal_places": 0, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_6", "is_index": false, "is_active": true, "create_time": 1574353386015, "length": 18, "label": "外部业务类型", "api_name": "link_app_type", "_id": "627bd4c0956f75000148dd51", "is_index_field": false, "round_mode": 4, "status": "new"}, "error_reason": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1661355907557, "is_encrypted": false, "pattern": "", "description": "异常原因", "is_unique": false, "label": "异常原因", "type": "long_text", "api_name": "error_reason", "define_type": "package", "_id": "630647838f1a5700019ec076", "is_index_field": false, "is_single": false, "index_name": "t_14", "max_length": 5000, "status": "new"}, "execution_type": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1661441452281, "is_encrypted": false, "is_unique": false, "label": "类型", "type": "select_one", "is_required": false, "api_name": "execution_type", "options": [{"label": "更新", "value": "update"}, {"label": "审批", "value": "approve"}, {"label": "选择新建关联对象", "value": "addRelatedObject"}, {"label": "添加团队成员", "value": "operation-addteammember"}, {"label": "更换负责人", "value": "operation-changeowner"}, {"label": "退回", "value": "operation-return"}, {"label": "确认收货", "value": "operation-confirmreceive"}, {"label": "确认发货", "value": "operation-confirmdelivery"}, {"label": "线索一转三", "value": "operation-HandleThree"}, {"label": "应用节点", "value": "externalApplyTask"}, {"label": "新建从对象", "value": "addMDObject"}, {"label": "批量新建关联对象", "value": "batchAddRelatedObject"}, {"label": "编辑从对象", "value": "batchEditMasterDetailObject"}, {"label": "签到", "value": "operation-signin"}, {"label": "签退", "value": "operation-signout"}, {"label": "编辑关联对象", "value": "updateLookup"}, {"label": "操作对象(多选)", "value": "operationMulti"}], "define_type": "package", "option_id": "0ca6513f258d5fb5bcbca7cbed0ef6a0", "_id": "630795ac84acc400016073d1", "is_index_field": false, "is_single": false, "index_name": "s_7", "config": {}, "status": "new"}, "current_candidate_ids": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1627463664997, "is_encrypted": false, "is_unique": false, "label": "当前待处理人", "type": "employee_many", "is_required": false, "api_name": "current_candidate_ids", "define_type": "package", "_id": "627bd4c0956f75000148dd52", "is_index_field": false, "is_single": false, "index_name": "a_6", "status": "new"}, "startTime": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134906, "is_unique": false, "label": "任务开始时间", "type": "date_time", "time_zone": "GMT+8", "is_required": false, "api_name": "startTime", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "627bd4c0956f75000148dd53", "is_index_field": false, "is_single": false, "index_name": "l_1", "status": "new"}, "state": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134911, "is_unique": false, "label": "任务状态", "type": "select_one", "is_required": false, "api_name": "state", "options": [{"label": "进行中", "value": "in_progress"}, {"label": "已完成", "value": "pass"}, {"label": "已终止", "value": "cancel"}, {"label": "异常", "value": "error"}], "define_type": "package", "option_id": "2433d4f9e1d4781688b4c9a3582b82d8", "_id": "627bd4c0956f75000148dd54", "is_index_field": false, "is_single": false, "index_name": "s_2", "config": {}, "status": "new"}, "activityId": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1546430134906, "pattern": "", "is_unique": false, "label": "流程节点定义Id", "type": "text", "is_required": false, "api_name": "activityId", "define_type": "package", "_id": "627bd4c0956f75000148dd55", "is_index_field": false, "is_single": false, "index_name": "t_2", "max_length": 256, "status": "new"}, "isTimeout": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1546430134917, "is_unique": false, "default_value": false, "label": "是否超时", "type": "true_or_false", "is_required": false, "api_name": "isTimeout", "define_type": "package", "_id": "627bd4c0956f75000148dd56", "is_index_field": false, "is_single": false, "index_name": "b_1", "status": "new"}, "link_app_id": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1574353386015, "pattern": "", "description": "外部所属业务", "is_unique": false, "label": "外部所属业务", "type": "text", "is_required": false, "api_name": "link_app_id", "define_type": "package", "_id": "627bd4c0956f75000148dd57", "is_index_field": false, "is_single": false, "index_name": "t_10", "max_length": 256, "status": "new"}, "candidateIds": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1557241973919, "is_unique": false, "label": "任务待处理人", "type": "employee", "is_required": false, "api_name": "candidateIds", "define_type": "package", "_id": "627bd4c0956f75000148dd58", "is_index_field": false, "is_single": false, "index_name": "a_3", "status": "new"}, "remindLatency": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1546430134906, "length": 15, "is_unique": false, "label": "允许停留时长", "type": "number", "decimal_places": 0, "is_required": false, "api_name": "remindLatency", "define_type": "package", "_id": "627bd4c0956f75000148dd59", "is_index_field": false, "is_single": false, "index_name": "d_3", "round_mode": 4, "status": "new"}, "applicantId": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1584022180201, "quote_field_type": "employee", "is_unique": false, "label": "流程发起人", "type": "quote", "quote_field": "workflowInstanceId__r.applicantId", "is_required": false, "api_name": "applicantId", "define_type": "package", "_id": "627bd4c0956f75000148dd5a", "is_index_field": false, "is_single": false, "index_name": "a_5", "status": "new"}, "assignees": {"describe_api_name": "BpmTask", "embedded_fields": {"role": {"is_index": false, "is_active": true, "is_required": false, "api_name": "role", "is_unique": false, "define_type": "package", "label": "角色", "type": "tag"}, "dept_leader": {"is_index": false, "is_active": true, "is_required": false, "api_name": "dept_leader", "is_unique": false, "define_type": "package", "label": "部门领导人", "type": "tag"}, "person": {"is_index": false, "is_active": true, "is_required": false, "api_name": "person", "is_unique": false, "define_type": "package", "label": "处理人", "type": "tag"}, "dept": {"is_index": false, "is_active": true, "is_required": false, "api_name": "dept", "is_unique": false, "define_type": "package", "label": "部门", "type": "tag"}, "ext_bpm": {"is_index": false, "is_active": true, "is_required": false, "api_name": "ext_bpm", "is_unique": false, "define_type": "package", "label": "流程相关人员", "type": "tag"}, "group": {"is_index": false, "is_active": true, "is_required": false, "api_name": "group", "is_unique": false, "define_type": "package", "label": "组", "type": "tag"}}, "is_index": true, "is_active": true, "create_time": 1546430134906, "is_unique": false, "label": "流程定义处理人", "type": "embedded_object", "is_required": false, "api_name": "assignees", "define_type": "package", "_id": "627bd4c0956f75000148dd5b", "is_index_field": false, "is_single": false, "index_name": "s_1", "status": "new"}, "stageId": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134911, "pattern": "", "is_unique": false, "label": "当前阶段Id", "type": "text", "is_required": false, "api_name": "stageId", "define_type": "package", "_id": "627bd4c0956f75000148dd5c", "is_index_field": false, "is_single": false, "index_name": "t_4", "max_length": 256, "status": "new"}, "owner": {"describe_api_name": "BpmTask", "description": "负责人", "is_unique": false, "type": "employee", "decimal_places": 0, "is_required": false, "define_type": "package", "is_single": true, "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1546430134911, "length": 8, "label": "负责人", "is_need_convert": true, "api_name": "owner", "_id": "627bd4c0956f75000148dd5d", "is_index_field": false, "round_mode": 4, "status": "released"}, "relatedObject": {"describe_api_name": "BpmTask", "description": "所属对象,包含对象类型和对象数据", "is_unique": true, "group_type": "what", "type": "group", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_3", "is_index": true, "is_active": true, "create_time": 1546430134912, "label": "所属对象", "api_name": "relatedObject", "_id": "627bd4c0956f75000148dd5e", "is_index_field": false, "fields": {"id_field": "objectDataId", "api_name_field": "objectApiName"}, "help_text": "", "status": "new"}, "name": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "pattern": "", "description": "业务流程任务主题:任务名称(开始时间)", "is_unique": false, "label": "任务主题", "type": "text", "is_required": false, "api_name": "name", "define_type": "system", "_id": "627bd4c0956f75000148dd5f", "is_index_field": false, "is_single": false, "index_name": "name", "max_length": 256, "status": "released"}, "taskName": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "pattern": "", "is_unique": false, "label": "任务名称", "type": "text", "is_required": false, "api_name": "taskName", "define_type": "package", "_id": "627bd4c0956f75000148dd60", "is_index_field": false, "is_single": false, "index_name": "t_5", "max_length": 256, "status": "new"}, "endTime": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "is_unique": false, "label": "任务结束时间", "type": "date_time", "time_zone": "GMT+8", "is_required": false, "api_name": "endTime", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "_id": "627bd4c0956f75000148dd61", "is_index_field": false, "is_single": false, "index_name": "l_2", "status": "new"}, "workflowInstanceName": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "pattern": "", "description": "业务流程实例主题:流程名称(发起时间)", "is_unique": false, "label": "流程主题", "type": "text", "is_required": false, "api_name": "workflowInstanceName", "define_type": "package", "_id": "627bd4c0956f75000148dd62", "is_index_field": false, "is_single": false, "index_name": "t_7", "max_length": 256, "status": "new"}, "workflowId": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "pattern": "", "is_unique": false, "label": "流程定义Id", "type": "text", "is_required": false, "api_name": "workflowId", "define_type": "package", "_id": "627bd4c0956f75000148dd63", "is_index_field": false, "is_single": false, "index_name": "t_8", "max_length": 256, "status": "new"}, "objectDataId": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1546430134917, "pattern": "", "is_unique": false, "label": "所属业务记录", "type": "text", "is_required": false, "api_name": "objectDataId", "define_type": "package", "_id": "627bd4c0956f75000148dd64", "is_index_field": false, "is_single": false, "index_name": "t_9", "max_length": 256, "status": "new"}, "workflowInstanceId": {"describe_api_name": "BpmTask", "is_unique": false, "type": "object_reference", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_5", "is_index": true, "is_active": true, "create_time": 1546430134911, "label": "所属业务流程", "target_api_name": "BpmInstance", "target_related_list_name": "instance_task", "target_related_list_label": "任务", "action_on_target_delete": "do_not_allow", "api_name": "workflowInstanceId", "_id": "627bd4c0956f75000148dd65", "is_index_field": false, "status": "new"}, "activity_instance_id": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1591286562206, "length": 18, "is_unique": false, "label": "节点实例id", "type": "number", "decimal_places": 0, "is_required": false, "api_name": "activity_instance_id", "define_type": "package", "_id": "627bd4c0956f75000148dd66", "is_index_field": false, "is_single": false, "index_name": "d_5", "round_mode": 4, "status": "new"}, "task_type": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1591286586849, "is_unique": false, "label": "任务类型", "type": "select_one", "api_name": "task_type", "options": [{"label": "多人审批", "value": "one_pass"}, {"label": "会签审批", "value": "all_pass"}], "define_type": "package", "option_id": "c43441afacbab5286a6ea76f05862300", "_id": "627bd4c0956f75000148dd67", "is_index_field": false, "is_single": false, "index_name": "s_6", "config": {}, "status": "new"}, "sourceWorkflowId": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134906, "pattern": "", "description": "业务流程唯一标示", "is_unique": false, "label": "流程srcId", "type": "text", "is_required": false, "api_name": "sourceWorkflowId", "define_type": "package", "_id": "627bd4c0956f75000148dd68", "is_index_field": false, "is_single": false, "index_name": "t_1", "max_length": 256, "status": "new"}, "relevant_team": {"describe_api_name": "BpmTask", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1546430134912, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "627bd4c0956f75000148dd69", "is_index_field": false, "is_single": false, "index_name": "a_team", "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "BpmTask", "description": "record_type", "is_unique": false, "type": "record_type", "is_required": false, "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "option_id": "504eae3cf17c3d7223be4a3e0e893ecb", "is_single": false, "is_extend": false, "index_name": "r_type", "is_index": true, "is_active": true, "create_time": 1546430134917, "label": "业务类型", "is_need_convert": false, "api_name": "record_type", "_id": "627bd4c0956f75000148dd6a", "is_index_field": false, "config": {}, "help_text": "", "status": "released"}, "processorIds": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "is_unique": false, "label": "任务执行人", "type": "employee", "is_required": false, "api_name": "processorIds", "define_type": "package", "_id": "627bd4c0956f75000148dd6b", "is_index_field": false, "is_single": false, "index_name": "a_1", "status": "new"}, "objectIds": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134906, "is_unique": false, "label": "节点流转对象Id", "type": "tag", "is_required": false, "api_name": "objectIds", "define_type": "package", "_id": "627bd4c0956f75000148dd6c", "is_index_field": false, "is_single": false, "index_name": "a_2", "status": "new"}, "action_code": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1612364825802, "pattern": "", "is_unique": false, "label": "操作类型", "type": "text", "is_required": false, "api_name": "action_code", "define_type": "package", "_id": "627bd4c0956f75000148dd6d", "is_index_field": false, "is_single": false, "index_name": "t_11", "max_length": 256, "status": "new"}, "duration": {"expression_type": "js", "return_type": "number", "describe_api_name": "BpmTask", "is_index": false, "is_active": true, "expression": "CASE($state$,'进行中', NOW().toTimeStamp()-$startTime$.toTimeStamp(),'已完成',IF($endTime$!=0&&$endTime$!=null,$endTime$.toTimeStamp()-$startTime$.toTimeStamp(),null),null)", "create_time": 1617379250633, "is_unique": false, "label": "任务耗时", "type": "formula", "is_abstract": true, "default_to_zero": true, "api_name": "duration", "define_type": "package", "_id": "627bd4c0956f75000148dd6e", "is_index_field": false, "is_single": false, "index_name": "d_2", "status": "new"}, "timeoutTime": {"expression_type": "js", "return_type": "number", "describe_api_name": "BpmTask", "is_index": false, "is_active": true, "expression": "IF($isTimeout$ && $duration$!=0 && $remindLatency$!=0,$duration$-$remindLatency$,0)", "create_time": 1617379263033, "is_unique": false, "label": "超时时长", "type": "formula", "is_abstract": true, "default_to_zero": true, "api_name": "timeoutTime", "define_type": "package", "_id": "627bd4c0956f75000148dd6f", "is_index_field": false, "is_single": false, "index_name": "d_1", "status": "new"}, "stageName": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1617379436831, "pattern": "", "is_unique": false, "label": "阶段名称", "type": "text", "is_required": false, "api_name": "stageName", "define_type": "package", "_id": "627bd4c0956f75000148dd70", "is_index_field": false, "is_single": false, "index_name": "t_3", "max_length": 256, "status": "new"}, "session_key": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1622648541802, "is_encrypted": false, "pattern": "", "is_unique": false, "label": "会话分组", "type": "text", "is_required": false, "api_name": "session_key", "define_type": "package", "_id": "627bd4c0956f75000148dd71", "is_index_field": false, "is_single": false, "index_name": "t_13", "max_length": 256, "status": "new"}, "read_employee": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "create_time": 1577372367533, "description": "已读人员", "is_unique": false, "label": "已读人员", "type": "employee", "is_required": false, "api_name": "read_employee", "define_type": "package", "_id": "627bd4c0956f75000148dd72", "is_index_field": false, "is_single": false, "index_name": "a_4", "status": "new"}, "objectApiName": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "create_time": 1546430134917, "pattern": "", "is_unique": false, "label": "所属业务对象", "type": "text", "is_required": false, "api_name": "objectApiName", "define_type": "package", "_id": "627bd4c0956f75000148dd73", "is_index_field": false, "is_single": false, "index_name": "t_6", "max_length": 256, "status": "new"}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "is_active": true, "api_name": "_id", "description": "_id", "status": "released", "index_name": "_id", "create_time": 1546430134906}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "is_active": true, "api_name": "tenant_id", "description": "tenant_id", "status": "released", "create_time": 1546430134906, "index_name": "ei"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name", "create_time": 1546430134906}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "index_name": "version", "create_time": 1546430134906}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by", "create_time": 1546430134906}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人", "create_time": 1546430134906}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "is_active": true, "api_name": "package", "description": "package", "status": "released", "create_time": 1546430134906, "index_name": "pkg"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time", "create_time": 1546430134906}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time", "create_time": 1546430134906}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "index_name": "is_del", "create_time": 1546430134906}, "out_tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "外部企业", "is_active": true, "api_name": "out_tenant_id", "description": "out_tenant_id", "status": "released", "index_name": "o_ei", "create_time": 1546430134906, "config": {"display": 0}}, "out_owner": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "index_name": "o_owner", "status": "released", "label": "外部负责人", "is_active": true, "config": {"display": 1}, "create_time": 1546430134906}, "data_own_department": {"type": "department", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "data_own_department", "status": "released", "label": "归属部门", "is_active": true, "index_name": "data_owner_dept_id", "create_time": 1546430134906}, "order_by": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "order_by", "api_name": "order_by", "description": "order_by", "status": "released", "index_name": "l_by", "create_time": 1546430134906}}, "actions": {}, "index_version": 1, "_id": "627bd4c0956f75000148dd50", "tenant_id": "-100", "is_udef": false, "api_name": "BpmTask", "display_name": "业务流程任务", "package": "CRM", "is_active": true, "version": 16, "release_version": "6.4", "define_type": "package", "is_deleted": false, "last_modified_time": 1546430134906, "create_time": 1546430134906, "store_table_name": "bpm_task", "description": "业务流程任务"}, "message": "OK"}}