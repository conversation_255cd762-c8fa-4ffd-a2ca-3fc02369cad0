{"index_version": 1, "is_active": true, "package": "CRM", "api_name": "BpmInstance", "description": "业务流程", "define_type": "package", "visible_scope": "bi", "is_deleted": false, "display_name": "业务流程", "fields": {"sourceWorkflowId": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "sourceWorkflowId", "is_unique": false, "define_type": "package", "label": "流程srcId"}, "name": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "name", "is_unique": false, "define_type": "system", "description": "name", "label": "流程主题"}, "workflowName": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "workflowName", "is_unique": false, "define_type": "package", "label": "流程名称"}, "relatedObject": {"type": "group", "group_type": "what", "api_name": "relatedObject", "is_required": false, "help_text": "", "label": "发起对象", "is_unique": true, "status": "new", "is_index": false, "define_type": "package", "is_active": true, "is_readonly": false, "description": "流程发起对象,包含对象类型和对象数据", "fields": {"id_field": "objectDataId", "api_name_field": "objectApiName"}}, "objectApiName": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "objectApiName", "is_unique": false, "define_type": "package", "label": "流程发起对象"}, "objectDataId": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "objectDataId", "is_unique": false, "define_type": "package", "label": "流程发起记录"}, "stageNames": {"type": "tag", "is_index": false, "is_active": true, "is_required": false, "api_name": "stageNames", "is_unique": false, "define_type": "package", "label": "当前阶段"}, "workflowId": {"is_index": true, "is_active": true, "is_required": false, "api_name": "workflowId", "is_unique": false, "define_type": "package", "label": "流程定义Id", "type": "text"}, "startTime": {"is_index": true, "is_active": true, "is_required": false, "api_name": "startTime", "is_unique": false, "define_type": "package", "label": "流程发起时间", "type": "date_time"}, "endTime": {"is_index": false, "is_active": true, "is_required": false, "api_name": "endTime", "is_unique": false, "define_type": "package", "label": "流程结束时间", "type": "date_time"}, "lastModifyTime": {"is_index": false, "is_active": true, "is_required": false, "api_name": "lastModifyTime", "is_unique": false, "define_type": "package", "label": "最后更新时间", "type": "date_time"}, "applicantId": {"is_index": true, "is_active": true, "is_required": false, "api_name": "applicantId", "is_unique": false, "define_type": "package", "label": "流程发起人", "is_single": false, "type": "employee"}, "lastModifyBy": {"is_index": false, "is_active": true, "is_required": false, "api_name": "lastModifyBy", "is_unique": false, "define_type": "package", "label": "最后更新人", "is_single": false, "type": "employee"}, "taskNames": {"is_index": false, "is_active": true, "is_required": false, "api_name": "taskNames", "is_unique": false, "define_type": "package", "label": "当前任务", "type": "tag"}, "objectIds": {"is_index": true, "is_active": true, "is_required": false, "api_name": "objectIds", "is_unique": false, "define_type": "package", "label": "节点流转对象Id", "type": "tag"}, "duration": {"is_index": false, "is_active": true, "is_required": false, "api_name": "duration", "is_unique": false, "define_type": "package", "label": "流程耗时", "type": "number", "decimal_places": 0, "length": 15, "round_mode": 4}, "state": {"is_index": true, "is_active": true, "is_required": false, "api_name": "state", "options": [{"label": "进行中", "value": "in_progress"}, {"label": "已完成", "value": "pass"}, {"label": "已终止", "value": "cancel"}, {"label": "异常", "value": "error"}], "is_unique": false, "define_type": "package", "label": "流程状态", "type": "select_one"}, "triggerSource": {"is_index": true, "is_active": true, "is_required": false, "api_name": "triggerSource", "options": [{"label": "手动发起", "value": "person"}, {"label": "业务流程触发", "value": "bpm"}, {"label": "工作流触发", "value": "workflow"}, {"label": "审批流触发", "value": "approval"}], "is_unique": false, "define_type": "package", "label": "发起类型", "type": "select_one"}, "owner": {"type": "employee", "define_type": "package", "is_index": false, "is_need_convert": true, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "负责人", "api_name": "owner", "is_unique": false, "description": "负责人", "status": "released", "is_single": true, "is_required": false}, "relevant_team": {"type": "embedded_object_list", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "embedded_fields": {"teamMemberEmployee": {"type": "employee", "define_type": "package", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "teamMemberEmployee", "description": "成员员工", "help_text": "成员员工", "label": "成员员工"}, "teamMemberRole": {"type": "select_one", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "options": [{"label": "负责人", "value": "1", "resource_bundle_key": null}, {"label": "普通成员", "value": "4", "resource_bundle_key": null}], "api_name": "teamMemberRole", "description": "成员角色", "help_text": "成员角色", "label": "成员角色"}, "teamMemberPermissionType": {"type": "select_one", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "options": [{"label": "只读", "value": "1", "resource_bundle_key": null}, {"label": "读写", "value": "2", "resource_bundle_key": null}], "api_name": "teamMemberPermissionType", "description": "成员权限类型", "help_text": "成员权限类型", "label": "成员权限类型"}}, "label": "相关团队", "help_text": "相关团队", "api_name": "relevant_team"}, "record_type": {"is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "index_name": "record_type", "help_text": "", "status": "released", "is_extend": false}}}