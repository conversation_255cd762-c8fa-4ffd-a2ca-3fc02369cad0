## 节点实例id

> 节点实例id,每个任务对应着节点实例id,需要刷全网数据
 
 ```json
{
    "activity_instance_id":{
        "describe_api_name":"BpmTask",
        "is_index":false,
        "is_active":true,
        "is_unique":false,
        "label":"节点实例id",
        "type":"number",
        "is_required":false,
        "api_name":"activity_instance_id",
        "define_type":"package"
    }
}
```



## 任务类型

> 判断当前节点是否为会签节点,如果是会签节点,处理结果为reject并且拒绝,后面的人员无需完成任务时,进行判断

```json
  {
      "task_type": {
          "describe_api_name": "BpmTask",
          "is_index": false,
          "is_active": true,
          "label": "任务类型",
          "type": "select_one",
          "api_name": "task_type",
          "options": [
            {
              "label": "多人审批",
              "value": "one_pass"
            },
            {
              "label": "会签审批",
              "value": "all_pass"
            }
          ],
          "define_type": "package"
        }
}
```

### 添加计算字段,判断是否超时

```json
{
    "is_timeout":{
        "describe_api_name":"BpmTask",
        "expression_type":"js",
        "return_type":"true_or_false",
        "is_index":false,
        "expression":"$remindLatency$>0&&(NOW()-$startTime$)*3600*1000*24>$remindLatency$",
        "is_active":true,
        "create_time":1588938260136,
        "is_unique":false,
        "label":"验证是否超时",
        "type":"formula",
        "is_abstract":true,
        "api_name":"is_timeout",
        "define_type":"package",
        "is_index_field":false,
        "is_single":false
    }
}
```

