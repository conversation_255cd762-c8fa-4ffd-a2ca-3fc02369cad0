{"current_candidate_ids": {"describe_api_name": "BpmInstance", "is_index": true, "is_active": true, "is_unique": false, "label": "当前处理人", "type": "employee", "is_abstract": null, "is_required": false, "api_name": "current_candidate_ids", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}, "duration": {"describe_api_name": "BpmInstance", "expression_type": "js", "return_type": "number", "is_index": false, "expression": "CASE($state$,'进行中', NOW().toTimeStamp()-$startTime$.toTimeStamp(),'已完成',IF($endTime$!=0&&$endTime$!=null,$endTime$.toTimeStamp()-$startTime$.toTimeStamp(),null),null)", "is_active": true, "is_unique": false, "label": "流程耗时", "type": "formula", "is_abstract": true, "default_to_zero": true, "api_name": "duration", "define_type": "package"}}