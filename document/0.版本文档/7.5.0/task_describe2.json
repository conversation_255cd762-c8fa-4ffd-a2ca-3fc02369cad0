{"action_code": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "is_unique": false, "label": "操作类型", "type": "text", "is_abstract": null, "is_required": false, "api_name": "action_code", "define_type": "package", "is_single": false, "status": "new"}, "execution_type": {"describe_api_name": "BpmTask", "is_index": true, "is_active": true, "is_unique": false, "label": "类型", "type": "select_one", "is_abstract": null, "is_required": false, "api_name": "execution_type", "define_type": "package", "is_single": false, "status": "new", "options": [{"label": "更新", "value": "update", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "审批", "value": "approve", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "选择新建关联对象", "value": "addRelatedObject", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "添加团队成员", "value": "operation-addteammember", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "更换负责人", "value": "operation-changeowner", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "退回", "value": "operation-return", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "确认收货", "value": "operation-confirmreceive", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "确认发货", "value": "operation-confirmdelivery", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "线索一转三", "value": "operation-HandleThree", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "操作", "value": "operation", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "应用节点", "value": "externalApplyTask", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "新建从对象", "value": "addMDObject", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "批量新建关联对象", "value": "batchAddRelatedObject", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"label": "编辑从对象", "value": "batchEditMasterDetailObject", "config": {"edit": 1, "enable": 1, "remove": 1}}]}, "duration": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "label": "任务耗时", "api_name": "duration", "define_type": "package", "default_to_zero": "true", "expression": "CASE($state$,'进行中', NOW().toTimeStamp()-$startTime$.toTimeStamp(),'已完成',IF($endTime$!=0&&$endTime$!=null,$endTime$.toTimeStamp()-$startTime$.toTimeStamp(),null),null)", "expression_type": "js", "return_type": "number", "type": "formula", "is_abstract": true}, "timeoutTime": {"describe_api_name": "BpmTask", "is_index": false, "is_active": true, "label": "超时时长", "api_name": "timeoutTime", "define_type": "package", "default_to_zero": "true", "expression": "IF($isTimeout$ && $duration$!=0 && $remindLatency$!=0,$duration$-$remindLatency$,0)", "expression_type": "js", "return_type": "number", "type": "formula", "is_abstract": true}}