{"index_version": 1, "is_active": true, "package": "CRM", "api_name": "BpmTask", "description": "业务流程任务", "store_table_name": "bpm_task", "define_type": "package", "is_deleted": false, "display_name": "业务流程任务", "fields": {"sourceWorkflowId": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "sourceWorkflowId", "is_unique": false, "define_type": "package", "description": "业务流程唯一标示", "label": "流程srcId"}, "name": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "name", "is_unique": false, "define_type": "system", "description": "业务流程任务主题:任务名称(开始时间)", "label": "任务主题"}, "relatedObject": {"type": "group", "group_type": "what", "api_name": "relatedObject", "is_required": false, "help_text": "", "label": "所属对象", "is_unique": true, "status": "new", "is_index": true, "define_type": "package", "is_active": true, "is_readonly": false, "description": "所属对象,包含对象类型和对象数据", "fields": {"id_field": "objectDataId", "api_name_field": "objectApiName"}}, "taskName": {"is_index": true, "is_active": true, "is_required": false, "api_name": "taskName", "is_unique": false, "define_type": "package", "label": "任务名称", "type": "text"}, "activityId": {"is_index": false, "is_active": true, "is_required": false, "api_name": "activityId", "is_unique": false, "define_type": "package", "label": "流程节点定义Id", "type": "text"}, "workflowInstanceName": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "workflowInstanceName", "is_unique": false, "define_type": "package", "description": "业务流程实例主题:流程名称(发起时间)", "label": "流程主题"}, "objectApiName": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "objectApiName", "is_unique": false, "define_type": "package", "label": "所属业务对象"}, "objectDataId": {"type": "text", "is_index": false, "is_active": true, "is_required": false, "api_name": "objectDataId", "is_unique": false, "define_type": "package", "label": "所属业务记录"}, "stageName": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "stageName", "is_unique": false, "define_type": "package", "label": "当前阶段"}, "stageId": {"type": "text", "is_index": true, "is_active": true, "is_required": false, "api_name": "stageId", "is_unique": false, "define_type": "package", "label": "当前阶段Id"}, "workflowId": {"is_index": true, "is_active": true, "is_required": false, "api_name": "workflowId", "is_unique": false, "define_type": "package", "label": "流程定义Id", "type": "text"}, "workflowInstanceId": {"is_index": true, "is_active": true, "is_required": false, "api_name": "workflowInstanceId", "is_unique": false, "define_type": "package", "label": "所属业务流程", "type": "object_reference", "target_api_name": "BpmInstance", "action_on_target_delete": "do_not_allow", "target_related_list_label": "任务", "target_related_list_name": "instance_task"}, "startTime": {"is_index": true, "is_active": true, "is_required": false, "api_name": "startTime", "is_unique": false, "define_type": "package", "label": "任务开始时间", "type": "date_time"}, "endTime": {"is_index": true, "is_active": true, "is_required": false, "api_name": "endTime", "is_unique": false, "define_type": "package", "label": "任务结束时间", "type": "date_time"}, "processorIds": {"is_index": true, "is_active": true, "is_required": false, "api_name": "processorIds", "is_unique": false, "define_type": "package", "label": "任务执行人", "is_single": false, "type": "employee"}, "candidateIds": {"is_index": true, "is_active": true, "is_required": false, "api_name": "candidateIds", "is_unique": false, "define_type": "package", "label": "任务待处理人", "is_single": false, "type": "employee"}, "objectIds": {"is_index": true, "is_active": true, "is_required": false, "api_name": "objectIds", "is_unique": false, "define_type": "package", "label": "节点流转对象Id", "type": "tag"}, "duration": {"is_index": false, "is_active": true, "is_required": false, "api_name": "duration", "is_unique": false, "define_type": "package", "label": "任务耗时", "type": "number", "decimal_places": 0, "length": 15, "round_mode": 4}, "remindLatency": {"is_index": false, "is_active": true, "is_required": false, "api_name": "remindLatency", "is_unique": false, "define_type": "package", "label": "允许停留时长", "type": "number", "decimal_places": 0, "length": 15, "round_mode": 4}, "timeoutTime": {"is_index": false, "is_active": true, "is_required": false, "api_name": "timeoutTime", "is_unique": false, "define_type": "package", "label": "超时时长", "type": "number", "decimal_places": 0, "length": 15, "round_mode": 4}, "isTimeout": {"is_index": true, "is_active": true, "is_required": false, "api_name": "isTimeout", "is_unique": false, "define_type": "package", "label": "是否超时", "type": "true_or_false", "default_value": false}, "state": {"is_index": true, "is_active": true, "is_required": false, "api_name": "state", "options": [{"label": "进行中", "value": "in_progress"}, {"label": "已完成", "value": "pass"}, {"label": "已终止", "value": "cancel"}, {"label": "异常", "value": "error"}], "is_unique": false, "define_type": "package", "label": "任务状态", "type": "select_one"}, "assignees": {"is_index": true, "is_active": true, "is_required": false, "api_name": "assignees", "embedded_fields": {"person": {"is_index": false, "is_active": true, "is_required": false, "api_name": "person", "is_unique": false, "define_type": "package", "label": "处理人", "type": "tag"}, "dept": {"is_index": false, "is_active": true, "is_required": false, "api_name": "dept", "is_unique": false, "define_type": "package", "label": "部门", "type": "tag"}, "dept_leader": {"is_index": false, "is_active": true, "is_required": false, "api_name": "dept_leader", "is_unique": false, "define_type": "package", "label": "部门领导人", "type": "tag"}, "group": {"is_index": false, "is_active": true, "is_required": false, "api_name": "group", "is_unique": false, "define_type": "package", "label": "组", "type": "tag"}, "role": {"is_index": false, "is_active": true, "is_required": false, "api_name": "role", "is_unique": false, "define_type": "package", "label": "角色", "type": "tag"}, "ext_bpm": {"is_index": false, "is_active": true, "is_required": false, "api_name": "ext_bpm", "is_unique": false, "define_type": "package", "label": "流程相关人员", "type": "tag"}}, "is_unique": false, "define_type": "package", "label": "流程定义处理人", "type": "embedded_object"}, "owner": {"type": "employee", "define_type": "package", "is_index": true, "is_need_convert": true, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "负责人", "api_name": "owner", "is_unique": false, "description": "负责人", "status": "released", "is_single": true, "is_required": false}, "relevant_team": {"type": "embedded_object_list", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "embedded_fields": {"teamMemberEmployee": {"type": "employee", "define_type": "package", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "teamMemberEmployee", "description": "成员员工", "help_text": "成员员工", "label": "成员员工"}, "teamMemberRole": {"type": "select_one", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "options": [{"label": "负责人", "value": "1", "resource_bundle_key": null}, {"label": "普通成员", "value": "4", "resource_bundle_key": null}], "api_name": "teamMemberRole", "description": "成员角色", "help_text": "成员角色", "label": "成员角色"}, "teamMemberPermissionType": {"type": "select_one", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "options": [{"label": "只读", "value": "1", "resource_bundle_key": null}, {"label": "读写", "value": "2", "resource_bundle_key": null}], "api_name": "teamMemberPermissionType", "description": "成员权限类型", "help_text": "成员权限类型", "label": "成员权限类型"}}, "label": "相关团队", "help_text": "相关团队", "api_name": "relevant_team"}, "record_type": {"is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "index_name": "record_type", "help_text": "", "status": "released", "is_extend": false}}}