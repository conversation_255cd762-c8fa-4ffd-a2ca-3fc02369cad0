{"displayFields": [{"orderType": 0, "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "type": "text", "aggrType": "0", "groupSeq": 1, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 0, "fieldLocation": 7, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "BpmInstance", "isSingle": 1, "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_352fa95391992844345d6f6c01ab2240", "columnName": "bpminstance_sourceworkflowid"}, {"orderType": 0, "fieldName": "流程状态", "dbFieldName": "state", "type": "select_one", "aggrType": "0", "groupSeq": 2, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 1, "fieldLocation": 13, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["filter", "group"], "dbObjName": "BpmInstance", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "fixed": 0, "enumName": "Bi_pre_0f3150f7ce4573d55d69289413d4cdd6", "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_0f3150f7ce4573d55d69289413d4cdd6", "columnName": "bpminstance_state"}, {"formatStr": "yyyy-MM-dd HH:mm", "orderType": 0, "fieldName": "流程发起时间", "dbFieldName": "startTime", "type": "datetime", "aggrType": "1", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_Time", "group": "DateTime"}, "isPerm": 1, "seq": 2, "fieldLocation": 11, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "BpmInstance", "isSingle": 1, "subFieldType": "DateTime", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "fixed": 0, "isGroup": 0, "fieldType": "Date", "isCalc": 0, "fieldID": "Bi_pre_5414ef2374d774b4b6432ba0cefd4270", "columnName": "bpminstance_starttime"}], "filterList": [{"filters": [{"dbFieldName": "sourceWorkflowId", "dbObjName": "BpmInstance", "fieldID": "Bi_pre_352fa95391992844345d6f6c01ab2240", "refObjName": "", "fieldName": "流程apiName", "fieldType": "String", "isLock": 1, "subFieldType": "", "value1": "408236016097951744", "value2": null, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "cascadeFieldID": "", "isPre": 0, "objName": "业务流程实例", "operator": 3, "operatorLabel": "是"}]}], "isView": 0, "isEdit": 2, "labelAndOptions": [{"defaultFilterOptionType": "UDF", "label": "场景", "defaultFilterOptions": [{"optionName": "全部", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db1"}, {"optionName": "我负责的", "isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db2"}, {"optionName": "我参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3"}, {"optionName": "我负责部门的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4"}, {"optionName": "我下属负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5"}, {"optionName": "我下属参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6"}, {"optionName": "共享给我的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7"}]}], "showMode": 0, "isSourceOfCustomObj": true, "businessObjects": [{"objectId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "objectShowName": "业务流程实例", "isPre": 0, "sequence": 0, "relationType": 1, "fieldId": "", "fieldName": ""}], "defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db1"], "isPreview": 0, "templateName": "业务流程实例报表", "pageSize": 2000, "pageNumber": 1, "globalFilter": null}