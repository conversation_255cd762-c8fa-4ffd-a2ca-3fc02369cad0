{"displayFields": [{"fieldID": "BI_9ac15f5bf57b45edc9852f8d11313e03", "dbObjName": "bpm_instance", "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "fieldLocation": -1, "fieldType": "String", "subFieldType": "", "objId": "BI_ebe54822d9581abf4d87560b4027b78e", "objName": "业务流程实例", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_instance", "aggrType": "0", "columnName": "bpm_instance_sourceworkflowid", "fixed": 0, "groupSeq": 1, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 0, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "isSingle": 0, "type": "text", "operateMenu": ["order", "filter", "group"], "relationObjName": "bpm_instance"}, {"fieldID": "BI_42b406b5ee10834f85a15fdeb70bc59f", "dbObjName": "bpm_instance", "fieldName": "流程状态", "dbFieldName": "state", "fieldLocation": -1, "fieldType": "String", "subFieldType": "SingleSelectEnum", "objId": "BI_ebe54822d9581abf4d87560b4027b78e", "objName": "业务流程实例", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_instance", "aggrType": "0", "columnName": "bpm_instance_state", "fixed": 0, "groupSeq": 2, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 1, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_MultiSelect", "justLeafNodeSelect": 0, "data": [{"nodeName": "进行中", "isSelected": 0, "optionCode": "in_progress", "parentID": "-2", "isHaveChild": 0, "enumId": "in_progress"}, {"nodeName": "已完成", "isSelected": 0, "optionCode": "pass", "parentID": "-2", "isHaveChild": 0, "enumId": "pass"}, {"nodeName": "已终止", "isSelected": 0, "optionCode": "cancel", "parentID": "-2", "isHaveChild": 0, "enumId": "cancel"}, {"nodeName": "异常", "isSelected": 0, "optionCode": "error", "parentID": "-2", "isHaveChild": 0, "enumId": "error"}]}, "isSingle": 0, "type": "select_one", "operateMenu": ["filter", "group"], "relationObjName": "bpm_instance"}, {"fieldID": "BI_3087a994711e0f1974effbd95472dabb", "dbObjName": "bpm_instance", "fieldName": "流程发起时间", "dbFieldName": "startTime", "fieldLocation": -1, "fieldType": "Date", "subFieldType": "DateTime", "objId": "BI_ebe54822d9581abf4d87560b4027b78e", "objName": "业务流程实例", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "yyyy-MM-dd HH:mm", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_instance", "aggrType": "1", "columnName": "bpm_instance_starttime", "fixed": 0, "groupSeq": 0, "isGroup": 0, "isVisible": 1, "orderType": 0, "seq": 2, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_Time", "group": "DateTime", "justLeafNodeSelect": 0}, "isSingle": 0, "type": "date_time", "operateMenu": ["order", "filter", "group", "aggr"], "relationObjName": "bpm_instance"}], "filterList": [{"filters": [{"dbFieldName": "sourceWorkflowId", "dbObjName": "bpm_instance", "fieldID": "BI_9ac15f5bf57b45edc9852f8d11313e03", "fieldId": "BI_9ac15f5bf57b45edc9852f8d11313e03", "refObjName": "", "fieldName": "流程apiName", "fieldType": "String", "isLock": 1, "subFieldType": "", "value1": "432016600838307840", "value2": null, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "cascadeFieldID": "", "isPre": 2, "objName": "业务流程实例", "operator": 3, "operatorLabel": "是"}]}], "isView": 0, "isEdit": 2, "labelAndOptions": [{"defaultFilterOptionType": "UDF", "label": "场景", "defaultFilterOptions": [{"optionName": "全部", "isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db1"}, {"optionName": "我负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db2"}, {"optionName": "我参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3"}, {"optionName": "我负责部门的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4"}, {"optionName": "我下属负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5"}, {"optionName": "我下属参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6"}, {"optionName": "共享给我的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7"}]}], "showMode": 0, "isSourceOfCustomObj": true, "businessObjects": [{"objectId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "objectShowName": "业务流程实例", "isPre": 1, "sequence": 0, "relationType": 1, "fieldId": "", "fieldName": ""}, {"objectId": "BI_063ead907951ceed329dcded9bafbf6d", "objectName": "bpm_task", "objectShowName": "业务流程任务", "isPre": 1, "sequence": 1, "relationType": 1, "fieldId": "", "fieldName": ""}], "defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db1"], "isPreview": 0, "templateName": "业务流程实例报表", "pageSize": 20, "pageNumber": 1, "globalFilter": null}