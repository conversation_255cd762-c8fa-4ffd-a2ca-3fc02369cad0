{"biInstanceArg": {"displayFields": [{"orderType": 0, "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "type": "text", "aggrType": "0", "groupSeq": 1, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 0, "fieldLocation": 7, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "bpm_instance", "isSingle": 1, "objId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "BI_9ac15f5bf57b45edc9852f8d11313e03", "columnName": "bpm_instance_sourceworkflowid"}, {"orderType": 0, "fieldName": "流程状态", "dbFieldName": "state", "type": "select_one", "aggrType": "0", "groupSeq": 2, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 1, "fieldLocation": 13, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["filter", "group"], "dbObjName": "bpm_instance", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "fixed": 0, "enumName": "BI_42b406b5ee10834f85a15fdeb70bc59f", "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "BI_42b406b5ee10834f85a15fdeb70bc59f", "columnName": "bpm_instance_state"}, {"formatStr": "yyyy-MM-dd HH:mm", "orderType": 0, "fieldName": "流程发起时间", "dbFieldName": "startTime", "type": "datetime", "aggrType": "1", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_Time", "group": "DateTime"}, "isPerm": 1, "seq": 2, "fieldLocation": 11, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "bpm_instance", "isSingle": 1, "subFieldType": "DateTime", "objId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "fixed": 0, "isGroup": 0, "fieldType": "Date", "isCalc": 0, "fieldID": "BI_3087a994711e0f1974effbd95472dabb", "columnName": "bpm_instance_starttime"}], "filterList": [{"filters": [{"dbFieldName": "sourceWorkflowId", "dbObjName": "bpm_instance", "fieldID": "BI_9ac15f5bf57b45edc9852f8d11313e03", "fieldId": "BI_9ac15f5bf57b45edc9852f8d11313e03", "refObjName": "", "fieldName": "流程apiName", "fieldType": "String", "isLock": 1, "subFieldType": "", "value1": "%s", "value2": null, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "cascadeFieldID": "", "isPre": 0, "objName": "业务流程实例", "operator": 3, "operatorLabel": "是"}]}], "isView": 0, "isEdit": 2, "labelAndOptions": [{"defaultFilterOptionType": "UDF", "label": "场景", "defaultFilterOptions": [{"optionName": "全部", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db1"}, {"optionName": "我负责的", "isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db2"}, {"optionName": "我参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3"}, {"optionName": "我负责部门的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4"}, {"optionName": "我下属负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5"}, {"optionName": "我下属参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6"}, {"optionName": "共享给我的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7"}]}], "showMode": 0, "isSourceOfCustomObj": true, "businessObjects": [{"objectId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "objectShowName": "业务流程实例", "isPre": 0, "sequence": 0, "relationType": 1, "fieldId": "", "fieldName": ""}], "defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db1"], "isPreview": 0, "templateName": "业务流程实例报表", "pageSize": 2000, "pageNumber": 1, "globalFilter": null}, "biTaskArg": {"displayFields": [{"orderType": 0, "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "type": "text", "aggrType": "0", "groupSeq": 1, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 0, "fieldLocation": 5, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "bpm_task", "isSingle": 1, "objId": "BI_063ead907951ceed329dcded9bafbf6d", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "BI_6990e9bef2a2d6cfecbdfaef1b4387b0", "columnName": "bpm_task_sourceworkflowid"}, {"orderType": 0, "fieldName": "当期阶段Id", "dbFieldName": "stageId", "type": "text", "aggrType": "0", "groupSeq": 2, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 1, "fieldLocation": 11, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "bpm_task", "isSingle": 1, "objId": "BI_063ead907951ceed329dcded9bafbf6d", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "BI_1257c29cd084da36927be2e10c3f3cb8", "columnName": "bpm_task_stageid"}, {"orderType": 0, "fieldName": "是否超时", "dbFieldName": "isTimeout", "type": "true_or_false", "aggrType": "0", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 3, "fieldLocation": 15, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "bpm_task", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "fixed": 0, "enumName": "BI_1c69023bd52847712f2086cf6233ede8", "isGroup": 0, "fieldType": "Boolean", "isCalc": 0, "fieldID": "BI_1c69023bd52847712f2086cf6233ede8", "columnName": "bpm_task_istimeout"}, {"orderType": 0, "fieldName": "任务状态", "dbFieldName": "state", "type": "select_one", "aggrType": "0", "groupSeq": 3, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 2, "fieldLocation": 9, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["filter", "group"], "dbObjName": "bpm_task", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "fixed": 0, "enumName": "BI_041d79a2bd4bfba67c4a27aa8d3305ab", "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "BI_041d79a2bd4bfba67c4a27aa8d3305ab", "columnName": "bpm_task_state"}, {"orderType": 0, "fieldName": "流程节点定义Id", "dbFieldName": "activityId", "type": "text", "aggrType": "0", "groupSeq": 4, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 3, "fieldLocation": 6, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "bpm_task", "isSingle": 1, "objId": "BI_063ead907951ceed329dcded9bafbf6d", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "BI_4725a8402242501e04f88648f0f48d29", "columnName": "bpm_task_activityid"}, {"formatStr": "yyyy-MM-dd HH:mm", "orderType": 0, "fieldName": "创建时间", "dbFieldName": "create_time", "type": "datetime", "aggrType": "1", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_Time", "group": "DateTime"}, "isPerm": 1, "seq": 4, "fieldLocation": -1, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "bpm_task", "isSingle": 1, "subFieldType": "DateTime", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "fixed": 0, "isGroup": 0, "fieldType": "Date", "isCalc": 0, "fieldID": "BI_706a6de8d14f9f93f5e5a743fc3d9941", "columnName": "bpm_task_create_time"}], "filterList": [{"filters": [{"dbFieldName": "sourceWorkflowId", "dbObjName": "bpm_instance", "fieldID": "BI_9ac15f5bf57b45edc9852f8d11313e03", "fieldId": "BI_9ac15f5bf57b45edc9852f8d11313e03", "refObjName": "", "fieldName": "流程apiName", "fieldType": "String", "isLock": 1, "subFieldType": "", "value1": "%s", "value2": null, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "cascadeFieldID": "", "isPre": 0, "objName": "业务流程实例", "operator": 3, "operatorLabel": "是"}]}], "isView": 0, "isEdit": 2, "labelAndOptions": [{"defaultFilterOptionType": "UDF", "label": "场景", "defaultFilterOptions": [{"optionName": "全部", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db1"}, {"optionName": "我负责的", "isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db2"}, {"optionName": "我参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3"}, {"optionName": "我负责部门的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4"}, {"optionName": "我下属负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5"}, {"optionName": "我下属参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6"}, {"optionName": "共享给我的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7"}]}], "showMode": 0, "isSourceOfCustomObj": true, "businessObjects": [{"objectId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "objectShowName": "业务流程实例", "isPre": 0, "sequence": -1, "relationType": 1, "fieldId": "", "fieldName": ""}, {"objectId": "BI_063ead907951ceed329dcded9bafbf6d", "objectName": "bpm_task", "objectShowName": "业务流程任务", "isPre": 0, "sequence": 0, "relationType": 1, "fieldId": "", "fieldName": ""}], "defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db2"], "isPreview": 0, "templateName": "业务流程任务报表", "pageSize": 20, "pageNumber": 1, "globalFilter": null}}