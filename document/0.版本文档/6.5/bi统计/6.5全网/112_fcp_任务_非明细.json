{"displayFields": [{"fieldID": "BI_6990e9bef2a2d6cfecbdfaef1b4387b0", "dbObjName": "bpm_task", "fieldName": "流程srcId", "dbFieldName": "sourceWorkflowId", "fieldLocation": -1, "fieldType": "String", "subFieldType": "", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_task", "aggrType": "0", "columnName": "bpm_task_sourceworkflowid", "fixed": 0, "groupSeq": 1, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 0, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "isSingle": 0, "type": "text", "operateMenu": ["order", "filter", "group"], "relationObjName": "bpm_task"}, {"fieldID": "BI_1257c29cd084da36927be2e10c3f3cb8", "dbObjName": "bpm_task", "fieldName": "当前阶段Id", "dbFieldName": "stageId", "fieldLocation": -1, "fieldType": "String", "subFieldType": "", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_task", "aggrType": "0", "columnName": "bpm_task_stageid", "fixed": 0, "groupSeq": 2, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 1, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "isSingle": 0, "type": "text", "operateMenu": ["order", "filter", "group"], "relationObjName": "bpm_task"}, {"fieldID": "BI_1c69023bd52847712f2086cf6233ede8", "dbObjName": "bpm_task", "fieldName": "是否超时", "dbFieldName": "isTimeout", "fieldLocation": -1, "fieldType": "Boolean", "subFieldType": "SingleSelectEnum", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_task", "aggrType": "0", "columnName": "bpm_task_istimeout", "fixed": 0, "groupSeq": 3, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 2, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_MultiSelect", "justLeafNodeSelect": 0, "data": [{"nodeName": "是", "isSelected": 0, "optionCode": "1", "parentID": "-2", "isHaveChild": 0, "enumId": "1"}, {"nodeName": "否", "isSelected": 0, "optionCode": "0", "parentID": "-2", "isHaveChild": 0, "enumId": "0"}]}, "isSingle": 0, "type": "true_or_false", "operateMenu": ["order", "filter", "group"], "relationObjName": "bpm_task"}, {"fieldID": "BI_041d79a2bd4bfba67c4a27aa8d3305ab", "dbObjName": "bpm_task", "fieldName": "任务状态", "dbFieldName": "state", "fieldLocation": -1, "fieldType": "String", "subFieldType": "SingleSelectEnum", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_task", "aggrType": "0", "columnName": "bpm_task_state", "fixed": 0, "groupSeq": 4, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 3, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_MultiSelect", "justLeafNodeSelect": 0, "data": [{"nodeName": "进行中", "isSelected": 0, "optionCode": "in_progress", "parentID": "-2", "isHaveChild": 0, "enumId": "in_progress"}, {"nodeName": "已完成", "isSelected": 0, "optionCode": "pass", "parentID": "-2", "isHaveChild": 0, "enumId": "pass"}, {"nodeName": "已终止", "isSelected": 0, "optionCode": "cancel", "parentID": "-2", "isHaveChild": 0, "enumId": "cancel"}, {"nodeName": "异常", "isSelected": 0, "optionCode": "error", "parentID": "-2", "isHaveChild": 0, "enumId": "error"}]}, "isSingle": 0, "type": "select_one", "operateMenu": ["filter", "group"], "relationObjName": "bpm_task"}, {"fieldID": "BI_4725a8402242501e04f88648f0f48d29", "dbObjName": "bpm_task", "fieldName": "流程节点定义Id", "dbFieldName": "activityId", "fieldLocation": -1, "fieldType": "String", "subFieldType": "", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 0, "formatStr": "", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_task", "aggrType": "0", "columnName": "bpm_task_activityid", "fixed": 0, "groupSeq": 5, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 4, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "isSingle": 0, "type": "text", "operateMenu": ["order", "filter", "group"], "relationObjName": "bpm_task"}, {"fieldID": "BI_706a6de8d14f9f93f5e5a743fc3d9941", "dbObjName": "bpm_task", "fieldName": "创建时间", "dbFieldName": "create_time", "fieldLocation": -1, "fieldType": "Date", "subFieldType": "DateTime", "objId": "BI_063ead907951ceed329dcded9bafbf6d", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 0, "formatStr": "yyyy-MM-dd HH:mm", "refObjName": "", "refKeyField": "", "refTargetField": "", "isCalc": 0, "objectName": "bpm_task", "aggrType": "1", "columnName": "bpm_task_create_time", "fixed": 0, "groupSeq": 0, "isGroup": 0, "isVisible": 1, "orderType": 0, "seq": 5, "isPerm": 1, "isPre": 2, "ui": {"type": "UI_Time", "group": "DateTime", "justLeafNodeSelect": 0}, "isSingle": 1, "type": "date_time", "operateMenu": ["order", "filter", "group", "aggr"], "relationObjName": "bpm_task"}], "filterList": [{"filters": [{"dbFieldName": "sourceWorkflowId", "dbObjName": "bpm_task", "fieldID": "BI_6990e9bef2a2d6cfecbdfaef1b4387b0", "fieldId": "BI_6990e9bef2a2d6cfecbdfaef1b4387b0", "refObjName": "", "fieldName": "流程srcId", "fieldType": "String", "isLock": 1, "subFieldType": "", "value1": "432016600838307840", "value2": null, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "cascadeFieldID": "", "isPre": 2, "objName": "业务流程任务", "operator": 3, "operatorLabel": "是"}]}], "isView": 0, "isEdit": 2, "labelAndOptions": [{"defaultFilterOptionType": "UDF", "label": "场景", "defaultFilterOptions": [{"optionName": "全部", "isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db1"}, {"optionName": "我负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db2"}, {"optionName": "我参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3"}, {"optionName": "我负责部门的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4"}, {"optionName": "我下属负责的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5"}, {"optionName": "我下属参与的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6"}, {"optionName": "共享给我的", "isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7"}]}], "showMode": 0, "isSourceOfCustomObj": true, "businessObjects": [{"objectId": "BI_ebe54822d9581abf4d87560b4027b78e", "objectName": "bpm_instance", "objectShowName": "业务流程实例", "isPre": 1, "sequence": -1, "relationType": 1, "fieldId": "", "fieldName": ""}, {"objectId": "BI_063ead907951ceed329dcded9bafbf6d", "objectName": "bpm_task", "objectShowName": "业务流程任务", "isPre": 1, "sequence": 0, "relationType": 1, "fieldId": "", "fieldName": ""}], "defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db1"], "isPreview": 0, "templateName": "业务流程任务报表", "pageSize": 20, "pageNumber": 1, "globalFilter": null}