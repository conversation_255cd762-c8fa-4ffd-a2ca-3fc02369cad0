{"biInstanceArg": {"defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db1"], "pageNumber": 1, "isPreview": 0, "isView": 0, "pageSize": 2000, "showMode": 0, "isSourceOfCustomObj": true, "displayFields": [{"orderType": 0, "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "type": "text", "aggrType": "0", "groupSeq": 1, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 0, "fieldLocation": 7, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "BpmInstance", "isSingle": 1, "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_352fa95391992844345d6f6c01ab2240", "columnName": "bpminstance_sourceworkflowid"}, {"orderType": 0, "fieldName": "流程状态", "dbFieldName": "state", "type": "select_one", "aggrType": "0", "groupSeq": 2, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 1, "fieldLocation": 13, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["filter", "group"], "dbObjName": "BpmInstance", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "fixed": 0, "enumName": "Bi_pre_0f3150f7ce4573d55d69289413d4cdd6", "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_0f3150f7ce4573d55d69289413d4cdd6", "columnName": "bpminstance_state"}, {"formatStr": "yyyy-MM-dd HH:mm", "orderType": 0, "fieldName": "流程发起时间", "dbFieldName": "startTime", "type": "datetime", "aggrType": "1", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_Time", "group": "DateTime"}, "isPerm": 1, "seq": 2, "fieldLocation": 11, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程实例", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "BpmInstance", "isSingle": 1, "subFieldType": "DateTime", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objectName": "BpmInstance", "fixed": 0, "isGroup": 0, "fieldType": "Date", "isCalc": 0, "fieldID": "Bi_pre_5414ef2374d774b4b6432ba0cefd4270", "columnName": "bpminstance_starttime"}], "labelAndOptions": [{"defaultFilterOptionType": "UDF", "defaultFilterOptions": [{"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db1", "optionName": "全部"}, {"isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db2", "optionName": "我负责的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3", "optionName": "我参与的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4", "optionName": "我负责部门的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5", "optionName": "我下属负责的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6", "optionName": "我下属参与的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7", "optionName": "共享给我的"}], "label": "场景"}], "businessObjects": [{"relationType": 1, "sequence": 0, "fieldName": "", "isPre": 0, "objectName": "BpmInstance", "objectShowName": "业务流程实例", "objectId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "fieldId": ""}], "templateName": "业务流程实例报表", "isEdit": 2, "filterList": [{"filters": [{"refObjName": "", "fieldName": "流程apiName", "isPre": 0, "value1": "%s", "objName": "业务流程实例", "dbFieldName": "sourceWorkflowId", "operator": 3, "isLock": 1, "cascadeFieldID": "", "dbObjName": "BpmInstance", "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "operatorLabel": "是", "subFieldType": "", "fieldType": "String", "fieldID": "Bi_pre_352fa95391992844345d6f6c01ab2240"}]}]}, "biTaskArg": {"defaultFilterOptionIDs": ["BI_59a3e59333b39e09b44e9db1"], "pageNumber": 1, "isPreview": 0, "isView": 0, "pageSize": 2000, "showMode": 0, "isSourceOfCustomObj": true, "displayFields": [{"orderType": 0, "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "type": "text", "aggrType": "0", "groupSeq": 1, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 0, "fieldLocation": 5, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "BpmTask", "isSingle": 1, "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_bd7e29962a892b56231a2912b6763cf9", "columnName": "bpmtask_sourceworkflowid"}, {"orderType": 0, "fieldName": "当期阶段Id", "dbFieldName": "stageId", "type": "text", "aggrType": "0", "groupSeq": 2, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 1, "fieldLocation": 11, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "BpmTask", "isSingle": 1, "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_dad0c90249015120b944eae2fb394313", "columnName": "bpmtask_stageid"}, {"orderType": 0, "fieldName": "是否超时", "dbFieldName": "isTimeout", "type": "true_or_false", "aggrType": "0", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 3, "fieldLocation": 15, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "BpmTask", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fixed": 0, "enumName": "Bi_pre_9723b0ba4af75cefbb31c99919d8a420", "isGroup": 0, "fieldType": "Boolean", "isCalc": 0, "fieldID": "Bi_pre_9723b0ba4af75cefbb31c99919d8a420", "columnName": "bpmtask_istimeout"}, {"orderType": 0, "fieldName": "任务状态", "dbFieldName": "state", "type": "select_one", "aggrType": "0", "groupSeq": 3, "ui": {"justLeafNodeSelect": 0, "type": "UI_MultiSelect"}, "isPerm": 1, "seq": 2, "fieldLocation": 9, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["filter", "group"], "dbObjName": "BpmTask", "isSingle": 1, "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fixed": 0, "enumName": "Bi_pre_409d6d8cb7f4498da04fd223a444dd31", "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_409d6d8cb7f4498da04fd223a444dd31", "columnName": "bpmtask_state"}, {"orderType": 0, "fieldName": "流程节点定义Id", "dbFieldName": "activityId", "type": "text", "aggrType": "0", "groupSeq": 4, "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "isPerm": 1, "seq": 3, "fieldLocation": 6, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group"], "dbObjName": "BpmTask", "isSingle": 1, "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fixed": 0, "isGroup": 1, "fieldType": "String", "isCalc": 0, "fieldID": "Bi_pre_b1672080db3f0dc01a760160ef84975a", "columnName": "bpmtask_activityid"}, {"formatStr": "yyyy-MM-dd HH:mm", "orderType": 0, "fieldName": "创建时间", "dbFieldName": "create_time", "type": "datetime", "aggrType": "1", "groupSeq": 0, "ui": {"justLeafNodeSelect": 0, "type": "UI_Time", "group": "DateTime"}, "isPerm": 1, "seq": 4, "fieldLocation": -1, "isPre": 0, "ei": 71567, "isKey": 0, "objName": "业务流程任务", "isIndex": 1, "isVisible": 1, "isShow": 1, "operateMenu": ["order", "filter", "group", "aggr"], "dbObjName": "BpmTask", "isSingle": 1, "subFieldType": "DateTime", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fixed": 0, "isGroup": 0, "fieldType": "Date", "isCalc": 0, "fieldID": "Bi_pre_2c5bcf7511fff1c660833df3f80cfd07", "columnName": "bpmtask_create_time"}], "labelAndOptions": [{"defaultFilterOptionType": "UDF", "defaultFilterOptions": [{"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db1", "optionName": "全部"}, {"isDefault": 1, "optionID": "BI_59a3e59333b39e09b44e9db2", "optionName": "我负责的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db3", "optionName": "我参与的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db4", "optionName": "我负责部门的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db5", "optionName": "我下属负责的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db6", "optionName": "我下属参与的"}, {"isDefault": 0, "optionID": "BI_59a3e59333b39e09b44e9db7", "optionName": "共享给我的"}], "label": "场景"}], "businessObjects": [{"relationType": 1, "sequence": -1, "fieldName": "", "isPre": 0, "objectName": "BpmInstance", "objectShowName": "业务流程实例", "objectId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "fieldId": ""}, {"relationType": 1, "sequence": 0, "fieldName": "", "isPre": 0, "objectName": "BpmTask", "objectShowName": "业务流程任务", "objectId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "fieldId": ""}], "templateName": "业务流程任务报表", "isEdit": 2, "filterList": [{"filters": [{"refObjName": "", "fieldName": "流程apiName", "isPre": 0, "value1": "%s", "objName": "业务流程实例", "dbFieldName": "sourceWorkflowId", "operator": 3, "isLock": 1, "cascadeFieldID": "", "dbObjName": "BpmInstance", "ui": {"format": "String", "justLeafNodeSelect": 0, "type": "UI_Input"}, "operatorLabel": "是", "subFieldType": "", "fieldType": "String", "fieldID": "Bi_pre_352fa95391992844345d6f6c01ab2240"}]}]}}