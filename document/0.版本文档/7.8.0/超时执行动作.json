{"workflow": {"activities": [{"id": "省略不少参数,应用节点、自动节点不支持超时执行", "type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "update", "executionName": "编辑对象", "entityId": "object_UPfNh__c", "entityName": "自行车(INAG)", "objectId": {"expression": "activity_0##object_UPfNh__c"}}, "assignee": {"ext_bpm": ["${instance##owner}"]}, "timeoutExecution": [{"id": 1, "time": "负数:指超时前;0:超时(默认);正数:超时后;暂不支持公式", "timeUnit": "1:天;2:小时(默认);3:分钟,需要校验超时时长", "execution": [{"rowNo": 0, "描述": "其他字段,参照后动作函数逻辑,此处rowNo必需存在,防止后期用户存在拖拽需求或顺序执行需求,暂不支持函数异步"}]}], "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}]}}