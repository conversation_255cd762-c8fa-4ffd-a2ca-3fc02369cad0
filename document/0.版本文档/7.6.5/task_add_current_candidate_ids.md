## 任务增加当前待处理人

>> 目标: 用空间换时间,提高待办列表查询效率

1. 增加字段
```sql
alter table bpm_task add column "current_candidate_ids" varchar(2000) [];
```
2. 描述中增加字段

```
{
  "current_candidate_ids": {
    "describe_api_name": "BpmTask",
    "is_index": true,
    "is_active": true,
    "is_unique": false,
    "label": "当前待处理人",
    "type": "employee_many",
    "is_required": false,
    "api_name": "current_candidate_ids",
    "define_type": "package",
    "status": "new"
  }
}
```

3. 同步时增加current_candidate_ids字段的维护
