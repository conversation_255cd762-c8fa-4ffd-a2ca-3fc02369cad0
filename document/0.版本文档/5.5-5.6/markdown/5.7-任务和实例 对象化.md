
|n| apiName |label|type|备注|
|:---|:--|:--|:--|:--|
|1|sourceWorkflowId|流程apiName|text|流程定义唯一标识,流程定义更新,此字段不变|	 
|2|name|流程主题|text| 格式 : 流程名称(发起时间)|
|3|workflowName|流程名称|text ||
|4|relatedObject| 流程发起对象|group.what|多lookup字段,指定api_name和id字段|
|5|objectApiName|流程发起对象|text|由relatedObject指定的api_name_field|	 
|6|objectDataId|流程发起数据|text| 由relatedObject指定的id_field|
|7|stageNames|当前阶段|tag|当前正在执行的任务所在的阶段名称(数组)|
|8|taskNames| 当前任务|tag|当前正在执行的任务名称(数组)|
|9|workflowId|流程定义id|text|流程定义id,流程定义更新,workflowId变化(一个新的数据),但sourceWorkflowId字段不变|	 
|10|startTime|流程发起时间|date_time| |
|11|endTime|流程结束时间|date_time ||
|12|lastModifyTime| 流程最后更新时间|date_time|暂定 最新 任务的完成时间|
|13|lastModifyBy| 流程最后更人|employee|暂定 最新 任务的处理人|	 
|14|applicantId|流程发起人|employee| |
|15|objectIds|所有任务的所属对象列表|tag|这个字段BPM自身业务会用到,BI暂时不需要|
|16|duration| 流程耗时|number(long)|已完成的流程实例,从发起到结束的时间(毫秒)|
|17|state| 流程状态|select_one|option:{in_progress,pass,cancel,error}|	 
|18|triggerSource|触发类型|select_one|option:{person(手动发起),bpm(bpm触发),workflow(工作流触发)，approval} |
|19|owner|负责人|employee|流程发起人 做为负责人|
|20|relevant_team| 相关团队|embedded_object_list|所有任务上的处理人 做为相关团队成员(实际就是流程参与人)|