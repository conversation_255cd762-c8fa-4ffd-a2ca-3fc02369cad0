
1. 在配置fs-bpm-object-support-config 中添加配置
    1. 产品提供apiName
    2. 产品提供哪些对象下可以创建该对象
    3. 产品确认该对象是否除作废时不能发起流程，还有没有别的限制
2. 预设对象 支持 需要和 SFA确认是否支持更新
3. 功能权限添加  刘勋和卢鑫（老对象需要执行此过程）
    ``` demo
            /// <summary>
            /// [6.2Added]市场活动-查看完整流程
            /// </summary>
            MarketingEvent_ViewEntireBPM = 10020,
            /// <summary>
            /// [6.2Added]市场活动-终止业务流程
            /// </summary>
            MarketingEvent_StopBPM = 10021,
            /// <summary>
            /// [6.2Added]市场活动-更换流程处理人
            /// </summary>
            MarketingEvent_ChangeBPMApprover = 10022,

            /// <summary>
            /// [6.2Added]市场活动-发起流程
            /// </summary>
            MarketingEventt_StartBPM = 10023,
    ```
    刘勋提供上面这个定义给 卢鑫，卢鑫刷功能权限

4. 查看  objDetail接口返回的layout中的buttons是否有流程相关按钮返回
5. 在页面上展示发起流程按钮
6. 预设自定义对象 和 老对象需要 提供哪些字段允许用户看到
7. 在proxy上添加entityId
