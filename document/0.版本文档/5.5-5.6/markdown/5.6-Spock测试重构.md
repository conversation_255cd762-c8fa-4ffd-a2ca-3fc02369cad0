# fs-bpm

## fs-bpm-biz

1. 流程定义(ProcessDefinitionAction)
1. 获取流程定义(getDefinitionList)

    ```
    分页,关键字,流程停用启用状态
    ```
1. 查询当前用户下可用的流程(getAllAvailableWorkflows)
    ```
    数据转换
    ```
1. 获取最新的流程定义的阶段信息(getLanesOfWorkflowSourceId)
    ```
    设置阶段上任务的相关数据
    ```
1. 流程任务(ProcessTaskAction)
1. 获取某对象下待办任务列表(getUncompletedTasksByObject)
    ```
    1.获取当前人的某对象下待办任务列表
    2.数据权限的匹配
    ```

## fs-bpm-common

1. [yongxu] 流程定义校验(VerifyManager)
1. [yongxu] ~~扩展工具类(BPMExtensionUtils)~~
1. [yongxu] ~~执行接口工具类(执行任务工具类)~~
1. [wansong] ~~内存分页(MemoryPageUtil)~~
1. [wansong] 获取引擎所需要的参数(MetaDataToWorkflowVariableUtil)
1. [wansong] ~~元数据类型转换工具(MetaDataToWorkflowVariableUtil)~~
1. [wansong] ~~表单数据与元数据之间转换(TaskFormToMetaDataUtil)~~

## fs-bpm-core

### dao

#### 配额

1. [wansong] ~~创建或查询配额(findAndModify)~~
1. [wansong] ~~增加流程数量 不包括版本(incSourceWorkflowCount)~~
1. [wansong] ~~查询企业有关流程的信息(find)~~
1. [wansong] ~~更新是否验证quota开关(updateValidateFlag)~~

#### 流程定义扩展

1. [yongxu] ~~查询扩展详情(find)~~
1. [yongxu] ~~根据workflowIds查询扩展列表(find)~~
1. [yongxu] ~~根据workflowIds查询扩展列表,返回workflowid:扩展(findToMap)~~
1. [yongxu] ~~更新流程定义扩展(update)~~
1. [yongxu] ~~保存流程定义扩展(save)~~

#### 流程定义概要
1. [wangz] 创建或更新(createOrUpdate)
1. [wangz] 根据流程名称查询是否能够查询到(findByWorkflowName)
1. [wangz] 通过sourceIds查询outlines(getWorkflowEntryTypeNameMap)
1. [wangz] 通过tenantId,id删除概要信息(delete)
1. [wangz] 查询某个企业下的某个概要信息(find)
1. [wangz] 通过sourceId获取流程实体(findBySourceWorkflowId)
1. [wangz] 设置流程是否可用(enable)
1. [wangz] 查询流程入口(findByEntryType)
1. [wangz] 管理员查询流程入口(findByEntryType)
1. [wangz] 分页查询流程定义(find)
1. [wangz] 当启动流程后,递增一次启动次数(incCount)
1. [wangz] 查询所有流程定义概要(findAll)
1. [wangz] 通过tenantId,ruleId,删除状态,启用状态删除概要信息(delete)
1. [wangz] 根据id直接删除概要(delete)

#### 任务历史数据

1. [wangz] 存储task历史数据(snapshotTaskData)
1. [wangz] 通过企业id及id查询数据(find)
1. [wangz] 通过企业id和多条记录的id查询历史数据(find)
#### ~~模板~~

1. 删除模板(disable)
1. 创建或更新模板(createOrUpdate)
1. 查询模板列表,可以包括已删除(getPageResult)
1. 查询模板详情,可以包括已删除(find)

### service

#### 流程定义

1. [wansong] 获取可用流程,根据主部门,组,角色进行筛查(getAvailableWorkflows)
1. [wansong] 发布流程定义(deployWorkflow)
1. [wansong] 删除流程定义(deleteWorkflowById)
1. [wansong] 根据id获取流程定义详情(getWorkflowOutlineById)
1. [wansong] 获取流程定义扩展(getWorkflowExtensionByWorkflowId)
1. [wansong] 根据sourceWorkflowId查询流程定义(getWorkflowOutlineBySourceId)
1. [wansong] 设置流程停用启用(enableWorkflow)
1. [wansong] 根据sourceIds 批量查询入口对象(getWorkflowEntryTypeNameMap)
1. [wansong] 通过workflowId查询流程描述ExecutableWorkflow(getWorkflowById)
1. [wansong] 通过对象objectId,entityId 获取正在运行的流程实例(getSourceWorkflowIdsByObject)
#### 流程实例

1. [yongxu] ~~启动流程实例(startWorkflow)~~
1. [yongxu] ~~取消流程实例(cancelWorkflowInstance)~~
1. [yongxu] ~~获取流程实例详情(getWorkflowInstance)~~
1. [yongxu] ~~获取全部流程实例(getWorkflowInstances)~~
1. [yongxu] ~~获取完整流程(getEntireWorkflowInstance)~~
1. [yongxu] ~~阶段视图显示任务详情(getWorkflowStatsData)~~

#### 组织架构

1. [wansong] ~~查询组织架构(getOrganization)~~

#### 任务

1. [wangz] 查询任务列表(getTasksByPage)

     ```
     可以按照流程实例,审批人,sourceWorkflowId进行查询
     ```
1. [wangz] 获取任务历史记录(getTaskLogs)
1. [wangz] changeTaskHanlders
1. [wangz] 阶段视图中,获取tasks中的业务活动(getWorkflowcompletedTasks)
     ```
     只显示进行中,异常,超时的
     ```
1. [wangz] 根据阶段查询任务(getTasksByLane)
1. [wangz] 根据taskId查询任务详情(getTask)
1. [wangz] 根据instanceId和activityInstanceId查询任务详情(getTask)
1. [wangz] 终端获取任务详情(getMTask)
1. [wangz] 根据流程实例id和activityInstanceIds 查询任务详情(getTasksByInstanceIds)

     ```
     业务流程-点击任务节点时获取
     ```
1. [wangz] 任务执行(completeTask)
1. [wangz]获取当前人待办任务(getHandleTaskList)
1. [wangz]获取当前人的某对象下待办任务列表(getUncompletedTasksByObject)

#### 模板

1. 获取模板列表(getTemplateList)
1. 获取模板详情(getTemplateDetail)

#### 企业

1. [wansong] ~~企业配额校验(hasQuota)~~


#### 更多操作服务+权限

1. ~~[wansong]任务的更多操作~~
1. ~~[wansong]实例的更多操作~~
1. ~~[wansong]任务和实例的更多操作~~

#### 表单按钮下发服务

1. ~~[wansong] 更新~~
2. ~~[wansong] action~~
3. ~~[wansong] 审批和会签~~

### fs-bpm-metadata(元数据服务)

1. [yongx] 查询描述(findDescribe)
1. [yongx] 查询actionName(getActionNameByActionCode)
1. [yongx] 创建数据(createData)
1. [yongx] 更新数据(updateData)
1. [yongx] 查询数据详情(findDataById)
1. [yongx] 根据产品,查询对象的简要描述列表(findDescsByTenantId)
1. [yongx] 查询对象下动作列表(findActionsByDesc)
1. [yongx] 执行一个对象的action(executeAction)
1. [yongx] 查询关联对象列表(findReferences)
1. [yongx] 根据objectId,查询objectName值(getPaaSObjectName)
1. [yongx] 根据objectIds查询记录详情,返回map<objectid,data>(findDatas)
1. [yongx] 根据apiNames查询描述返回Map<apiname,desc>(getDescMap)
1. [yongx] 根据ids查询记录详情列表(findByIds)
1. [yongx] 根据objectIds查询objectName(getPaaSObjectNames)
1. [yongx] 查询团队成员(getTeamMember)

### fs-bpm-processor
