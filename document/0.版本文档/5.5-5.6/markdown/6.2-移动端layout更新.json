//任务
{
  "agent_type": "agent_type_mobile",
  "components": [
    {
      "api_name": "table_component",
      "ref_object_api_name": "BpmTask",
      "include_fields": [
        {
          "api_name": "workflowInstanceName",
          "label": "流程主题",
          "render_type": "text"
        },
        {
          "api_name": "objectDataId",
          "label": "任务关联数据",
          "render_type": "text"
        },
        {
          "api_name": "name",
          "label": "任务主题",
          "render_type": "text"
        },
        {
          "api_name": "stageName",
          "label": "阶段名称",
          "render_type": "text"
        },
        {
          "api_name": "startTime",
          "label": "任务开始时间",
          "render_type": "date_time"
        },
        {
          "api_name": "state",
          "label": "状态",
          "render_type": "select_one"
        }
      ],
      "type": "table"
    }
  ],
  "package": "CRM",
  "api_name": "layout_BpmTask_mobile",
  "ref_object_api_name": "BpmTask",
  "layout_type": "list",
  "is_default": false,
  "display_name": "待处理任务列表",
  "version": "1",
  "is_show_fieldname": true
}


