## 获取前置节点算法

1. 去环 同时 输出 所有到目的节点的路径
2. 查找出并行节点 的开始节点 fork
3. 查出并行节点的节点 join
4. 查出 并行节点内的 前置节点

    1. 遍历所有开始并行节点
    2. 取出对应的结束 并行节点
    3. 获取之间的所有线，按第一个节点进行将线分组
    4. 将同组中的线进行取交集
    5. 将所有组各自的交集结果取并集
    6. 将该开始与结束的并行节点中得到的结果 进行保存
    7. 上面这个步骤结果得所有结果进行取并集

5. 并行节点外的所有节点取交集
6. 第四步和第五步的结果取并集


## 算法缺陷


![Screen_Shot_2017-08-11_at_17.58.39](/uploads/99085ffb9b78f70722ebb2547b4e8988/Screen_Shot_2017-08-11_at_17.58.39.png)
