# 需求：

节点1（客户）--节点2（联系人）--节点3（客户）

节点2 的处理人选择的是 节点1 的客户对象的 负责人 或 对象的负责团队人员 或 负责人 上级 或对象负责团队人员 的上级

# 流程定义中

## 记录负责人
    assignee:
    {
        bpm:["activity_0##AccountObj##owner","activity_0##AccountObj##leader",...]
    }
    具体数组中的内容引擎不用关心，主要是由业务方去使用
    activity_0##AccountObj##owner 记录负责人
    activity_0##AccountObj##leader 记录负责人上级  ----
    activity_0##AccountObj##group  记录相关团队成员
    activity_0##AccountObj##group_leader 记录相关团队成员上级 ----
    activity_0##assigneeId 节点处理人
    activity_0##assigneeId##leader 节点处理人上级  ---

    activity_0##assigneeId##dept_leader 节点处理人所属主部门负责人
    activity_0##AccountObj##dept_leader 记录负责人所在主部门负责人
    activity_0##AccountObj##group_dept_leader 记录相关团队成员所在主部门负责人


`instance##owner
流程发起人   这是特殊的审批人  我和@宋根磊 解析时会用到，定义规定
是  标示$$描述
如：activity_0##assigneeId$$节点处理人`


# BPM实现

接口名称：
/plugins/workflow?action=getAssignees

参数：

        private Map<String,String> context;
        private Map<String, Object> variables;
        private String entityId;
        private String objectId;
        private List<String> assigneesType;
        private String taskId;
        private String instanceId;

        context:
            private String tenantId;
            private String appId;
            private String userId;


返回值：

         private int status=0;
         private String errorMessage;
         public List<String> result;


