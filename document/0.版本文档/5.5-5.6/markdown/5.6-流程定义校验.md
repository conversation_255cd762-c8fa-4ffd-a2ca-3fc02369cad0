# 流程定义校验

#### 导语

> 本文档主要针对BPM流程定义进行校验封装
> 如需要查看前端结构图,请查看[流程定义JSON数据格式](http://git.firstshare.cn/bpm/fs-bpm/wikis/docs/data-structure/process "流程定义JSON数据格式")

1. 基础字段校验
   - 必填项
     - name
     - entryType
     - entryTypeName
     - _rangeEmployeeIds[]_ *
     - _rangeCircleIds[]_ *
     - _rangeGroupIds[]_ *
     - _rangeRoleIds[]_ *
     - workflow
       - activities[]
         - 节点出入线的校验
           - 开始节点有且只能有一根连出的线， 不允许有连入的线
           - 业务活动节点有且只能有一根连出的线， 至少有一根连入的线
           - 审批/会签节点至少有一根连入的线， 有且只有两个连出的线
           - 分支节点， 至少有一根连入的线， 至少有一个连出的线
           - 结束节点， 有至少有一根连入的线， 不允许有连出的线
         - type:userTask
           - assignee中必需有审批人
         - bpmExtension
           - entityId不能为空
           - entityName不能为空
           - objectId不能为空
           - executionType不能为空
           - 校验表达式是否在variables中存在
           - 如果是审批节点(approve) 则必需有taskType
           - 如果是对象操作(operation),则必需包含actionCode
           - 校验form中的result在variables是否存在
       - transitions[]
         - 线上的表达式,必需在variables中存在
       - variables[]
     - extension
       - 校验所有节点是否都在泳池中


