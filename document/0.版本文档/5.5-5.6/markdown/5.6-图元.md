# 图元

## 阶段
### 泳池
    目前我们这里只有一个大的泳池，泳池中有多个泳道

### 泳道
    泳道中包含多个活动

## 自定义页面
    对象的创建和对象的更新会用到此自定义页面

## 独占网关


    此节点为判断分支，通过条件来获取一个路径进行继续下一节点
    条件处理：
        选择第一条满足条件的路径
        当都不满足时看是否有defaultTransition 默认路径，若有就走默认
        当都不满足时，若只有一条路就选择这条路径
        当都不满足且大于一条路径时 就看是否有一个没有条件的路径 若有就走没有条件的路径
        若都有条件且都不满足时 如果条件都不满足，实例结束  已与 @王海潮 确认


## 通知
    目前支持企信通知

## 更新任务
    用来更新对象时使用，其会展示一个对象表单，进行对象的修改
    
## 创建任务
    用来创建新的对象，此任务会跳转到其对象创建的一个页面进行对象创建，当对象创建成功时
    元数据平台会向对象监听的队列发送消息，然后流程监听创建结果，进行流程的驱动

## 会签
    审批人：支持人、记录负责人
    条件如何使用：action : agree,reject

