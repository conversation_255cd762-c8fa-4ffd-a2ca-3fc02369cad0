====113 环境===

http://10.112.32.60:8009/API/v1/inner/rest/layout/update
curl -X PUT \
  http://10.113.32.46:8014/API/v1/inner/rest/layout/update \
  -H 'cache-control: no-cache' \
  -H 'content-type: application/json' \
  -H 'postman-token: fb387efb-e4ea-3b31-61e1-df09590dca9e' \
  -H 'x-fs-ei: 54821' \
  -H 'x-fs-userinfo: 2025' \
  -H 'x-tenant-id: 54821' \
  -H 'x-user-id: 2025' \
  -d '{
  "agent_type": "agent_type_mobile",
  "components": [
    {
      "api_name": "table_component",
      "ref_object_api_name": "BpmTask",
      "include_fields": [
        {
          "api_name": "workflowInstanceName",
          "label": "流程名称",
          "render_type": "text"
        },
        {
          "api_name": "taskName",
          "label": "任务名称",
          "render_type": "text"
        },
        {
          "api_name": "startTime",
          "label": "任务开始时间",
          "render_type": "date_time"
        },
        {
          "api_name": "remindLatency",
          "label": "停留时长",
          "render_type": "number"
        }
      ],
      "type": "table"
    }
  ],
  "package": "CRM",
  "api_name": "layout_BpmTask_mobile",
  "ref_object_api_name": "BpmTask",
  "layout_type": "list",
  "is_default": true,
  "display_name": "待处理业务流程列表",
  "version": "1",
  "is_show_fieldname": true,
  "_id": "5b4ffb48bab09cfea45c9559"
}
'
