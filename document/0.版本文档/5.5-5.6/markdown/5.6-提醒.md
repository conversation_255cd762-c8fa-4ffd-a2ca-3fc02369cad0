1. 提醒模板变量

    ```
    流程名称：${workflowName}
    任务名称：${taskName}
    超时时限：${latencyTime}
    流程开始时间：${workflowInstanceStartTime}
    流程主题：${workflowName} ${workflowInstanceStartTime}
    任务完成时间：${taskCompletedTime}
    流程开始时间：${workflowInstanceStartTime}
    超时前/后 ** 小时: ${remindTime}
    ```


1. 模型

    ```
    @Data
    public class RemindEntity {
        private int remindStrategy;        // 提醒类型：超时前或超时后(1),流程结束后(2)，
        private Long remindTime; // 提醒时间:具体的小时说如：-3（超时前3小时），3（超时后3小时）
        private Map<String, List<String>> remindTarget;  // 格式与审批人格式相同
        private String remindContent;   // 变量格式${workflowStartTime}
    }
    ```