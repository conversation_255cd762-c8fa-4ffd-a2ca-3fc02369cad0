#!/bin/sh
tenant_id='53694,53555'
allNet=false
url='http://10.112.32.60:8003/metadata'
#url='http://10.113.32.46:8003/metadata'
#url='http://172.17.35.223:8002/crm-metadata'
#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------
#
#分割线    下面是layout的
#
#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------



 curl -X POST \
$url'/crmrest/tenant_init/initOrUpdateLayoutByJson?apiName=BpmInstance&allNet='$allNet'&tenantIds='$tenant_id \
-H 'cache-control: no-cache' \
  -H 'x-fs-ei: 53395' \
  -H 'x-fs-userinfo: 1000' \
  -d '{
    "components": [
        {
            "buttons": [],
            "type": "table",
            "ref_object_api_name": "BpmInstance",
            "include_fields": [
                {
                    "render_type": "text",
                    "field_name": "name",
                    "label": "流程主题"
                },
                {
                    "render_type": "text",
                    "field_name": "objectApiName",
                    "label": "流程发起对象"
                },
                {
                    "render_type": "text",
                    "field_name": "objectDataId",
                    "label": "流程发起记录"
                },
                {
                    "render_type": "employee",
                    "field_name": "applicantId",
                    "label": "流程发起人"
                },
                {
                    "render_type": "select_one",
                    "field_name": "state",
                    "label": "流程状态"
                },
                {
                    "render_type": "tag",
                    "field_name": "stageNames",
                    "label": "当前阶段"
                },
                {
                    "render_type": "tag",
                    "field_name": "taskNames",
                    "label": "当前任务"
                },
                {
                    "render_type": "select_one",
                    "field_name": "triggerSource",
                    "label": "发起类型"
                },
                {
                    "render_type": "date_time",
                    "field_name": "startTime",
                    "label": "流程发起时间"
                },
                {
                    "render_type": "date_time",
                    "field_name": "endTime",
                    "label": "流程结束时间"
                },
                {
                    "render_type": "number",
                    "field_name": "duration",
                    "label": "流程耗时"
                }
            ]
        },
        {
            "buttons": [],
            "type": "form",
            "field_section": [
                {
                    "api_name": "base_field_section__c",
                    "header": "流程实例列表",
                    "show_header": true,
                    "form_fields": [
                        {
                            "field_name": "name",
                            "render_type": "text",
                            "is_readonly": false,
                            "api_name": "name"
                        },
                        {
                            "field_name": "objectApiName",
                            "render_type": "text",
                            "is_readonly": false,
                            "api_name": "objectApiName"
                        },
                        {
                            "field_name": "objectDataId",
                            "render_type": "text",
                            "is_readonly": false,
                            "api_name": "objectDataId"
                        },
                        {
                            "field_name": "applicantId",
                            "render_type": "employee",
                            "is_readonly": false,
                            "api_name": "applicantId"
                        },
                        {
                            "field_name": "state",
                            "render_type": "select_one",
                            "is_readonly": false,
                            "api_name": "state"
                        },
                        {
                            "field_name": "stageNames",
                            "render_type": "tag",
                            "is_readonly": false,
                            "api_name": "stageNames"
                        },
                        {
                            "field_name": "taskNames",
                            "render_type": "tag",
                            "is_readonly": false,
                            "api_name": "taskNames"
                        },
                        {
                            "field_name": "triggerSource",
                            "render_type": "select_one",
                            "is_readonly": false,
                            "api_name": "triggerSource"
                        },
                        {
                            "field_name": "startTime",
                            "render_type": "date_time",
                            "is_readonly": false,
                            "api_name": "startTime"
                        },
                        {
                            "field_name": "endTime",
                            "render_type": "date_time",
                            "is_readonly": false,
                            "api_name": "endTime"
                        },
                        {
                            "field_name": "duration",
                            "render_type": "number",
                            "is_readonly": false,
                            "api_name": "duration"
                        }
                    ]
                }
            ]
        }
    ],
    "buttons": [],
    "package": "CRM",
    "api_name": "BpmInstance",
    "ref_object_api_name": "BpmInstance",
    "layout_type": "detail",
    "is_default": true,
    "api_version": 1
}'


 curl -X POST \
  $url'/crmrest/tenant_init/initOrUpdateLayoutByJson?apiName=BpmInstance&allNet='$allNet'&tenantIds='$tenant_id \
-H 'cache-control: no-cache' \
  -H 'x-fs-ei: 53395' \
  -H 'x-fs-userinfo: 1000' \
  -d '{
    "agent_type": "agent_type_mobile",
    "components": [
        {
            "api_name": "table_component",
            "ref_object_api_name": "BpmInstance",
            "include_fields": [
                {
                    "api_name": "name",
                    "label": "流程主题",
                    "render_type": "text"
                },
                {
                    "api_name": "taskNames",
                    "label": "当前任务",
                    "render_type": "tag"
                },
                {
                    "api_name": "applicantId",
                    "label": "发起人",
                    "render_type": "employee"
                },
                {
                    "api_name": "objectDataId",
                    "label": "发起数据",
                    "render_type": "text"
                },
                {
                    "api_name": "state",
                    "label": "状态",
                    "render_type": "select_one"
                }
            ],
            "type": "table"
        }
    ],
    "package": "CRM",
    "api_name": "layout_BpmInstance_mobile",
    "ref_object_api_name": "BpmInstance",
    "layout_type": "list",
    "is_default": false,
    "display_name": "移动端默认列表页",
    "version": "1",
    "is_show_fieldname": true
}'


 curl -X POST \
  $url'/crmrest/tenant_init/initOrUpdateLayoutByJson?apiName=BpmTask&allNet='$allNet'&tenantIds='$tenant_id \
-H 'cache-control: no-cache' \
  -H 'x-fs-ei: 53395' \
  -H 'x-fs-userinfo: 1000' \
  -d '{
  "components": [
    {
      "buttons": [],
      "type": "table",
      "ref_object_api_name": "BpmTask",
      "include_fields": [
        {
          "render_type": "text",
          "field_name": "workflowInstanceName",
          "label": "流程主题"
        },
        {
          "render_type": "text",
          "field_name": "name",
          "label": "任务主题"
        },
        {
          "render_type": "number",
          "field_name": "remindLatency",
          "label": "允许停留时长"
        },
        {
          "render_type": "group",
          "field_name": "relatedObject",
          "label": "所属对象"
        },
        {
          "render_type": "number",
          "field_name": "duration",
          "label": "任务耗时"
        },
        {
          "render_type": "number",
          "field_name": "timeoutTime",
          "label": "超时时长"
        },
        {
          "render_type": "text",
          "field_name": "stageName",
          "label": "当前阶段"
        },
        {
          "render_type": "employee",
          "field_name": "processorIds",
          "label": "任务执行人"
        },
        {
          "render_type": "true_or_false",
          "field_name": "isTimeout",
          "label": "是否超时"
        },
        {
          "render_type": "text",
          "field_name": "objectApiName",
          "label": "所属业务对象"
        },
        {
          "render_type": "date_time",
          "field_name": "startTime",
          "label": "任务开始时间"
        },
        {
          "render_type": "date_time",
          "field_name": "endTime",
          "label": "任务结束时间"
        },
        {
          "render_type": "select_one",
          "field_name": "state",
          "label": "任务状态"
        },
        {
          "render_type": "text",
          "field_name": "objectDataId",
          "label": "所属业务记录"
        },
        {
          "render_type": "object_reference",
          "field_name": "workflowInstanceId",
          "label": "所属业务流程"
        }
      ]
    },
    {
      "buttons": [],
      "type": "form",
      "field_section": [
        {
          "api_name": "base_field_section__c",
          "header": "任务详情",
          "show_header": true,
          "form_fields": [
            {
              "field_name": "workflowInstanceName",
              "render_type": "text",
              "is_readonly": false,
              "api_name": "workflowInstanceName"
            },
            {
              "field_name": "name",
              "render_type": "text",
              "is_readonly": false,
              "api_name": "name"
            },

            {
              "field_name": "stageName",
              "render_type": "text",
              "is_readonly": false,
              "api_name": "stageName"
            },
            {
              "field_name": "objectApiName",
              "render_type": "text",
              "is_readonly": false,
              "api_name": "objectApiName"
            },

            {
              "field_name": "objectDataId",
              "render_type": "text",
              "is_readonly": false,
              "api_name": "objectDataId"
            },
            {
              "field_name": "state",
              "render_type": "select_one",
              "is_readonly": false,
              "api_name": "state"
            },
            {
              "field_name": "remindLatency",
              "render_type": "number",
              "is_readonly": false,
              "api_name": "remindLatency"
            },
            {
              "field_name": "duration",
              "render_type": "number",
              "is_readonly": false,
              "api_name": "duration"
            },
            {
              "field_name": "timeoutTime",
              "render_type": "number",
              "is_readonly": false,
              "api_name": "timeoutTime"
            },
            {
              "field_name": "processorIds",
              "render_type": "employee",
              "is_readonly": false,
              "api_name": "processorIds"
            },
            {
              "field_name": "isTimeout",
              "render_type": "true_or_false",
              "is_readonly": false,
              "api_name": "isTimeout"
            },
            {
              "field_name": "startTime",
              "render_type": "date_time",
              "is_readonly": false,
              "api_name": "startTime"
            },
            {
              "field_name": "endTime",
              "render_type": "date_time",
              "is_readonly": false,
              "api_name": "endTime"
            },
            {
              "field_name": "workflowInstanceId",
              "render_type": "object_reference",
              "is_readonly": false,
              "api_name": "workflowInstanceId"
            }
          ]
        }
      ]
    }
  ],
  "buttons": [],
  "package": "CRM",
  "api_name": "BpmTask",
  "ref_object_api_name": "BpmTask",
  "layout_type": "detail",
  "is_default": true,
  "api_version": 1.0
}'




 curl -X POST \
  $url'/crmrest/tenant_init/initOrUpdateLayoutByJson?apiName=BpmTask&allNet='$allNet'&tenantIds='$tenant_id \
-H 'cache-control: no-cache' \
  -H 'x-fs-ei: 53395' \
  -H 'x-fs-userinfo: 1000' \
  -d '{
    "agent_type": "agent_type_mobile",
    "components": [
        {
            "api_name": "table_component",
            "ref_object_api_name": "BpmTask",
            "include_fields": [
                {
                    "api_name": "workflowInstanceName",
                    "label": "流程主题",
                    "render_type": "text"
                },
                {
                    "api_name": "name",
                    "label": "任务主题",
                    "render_type": "text"
                },
                {
                    "api_name": "stageName",
                    "label": "阶段名称",
                    "render_type": "text"
                },
                {
                    "api_name": "startTime",
                    "label": "任务开始时间",
                    "render_type": "date_time"
                },
                {
                    "api_name": "state",
                    "label": "状态",
                    "render_type": "select_one"
                }
            ],
            "type": "table"
        }
    ],
    "package": "CRM",
    "api_name": "layout_BpmTask_mobile",
    "ref_object_api_name": "BpmTask",
    "layout_type": "list",
    "is_default": false,
    "display_name": "待处理任务列表",
    "version": "1",
    "is_show_fieldname": true
}'


#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------
#
##分割线    下面是initSearchTemplateByJson    【【【【【BpmTask对象】】】】】
#
#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------


 curl -X POST \
  $url'/crmrest/tenant_init/initSearchTemplateByJson?apiName=BpmTask&allNet='$allNet'&tenantIds='$tenant_id \
-H 'cache-control: no-cache' \
  -H 'x-fs-ei: 53395' \
  -H 'x-fs-userinfo: 1000' \
  -d '[
    {
        "type":"default",
        "created_by":"system",
        "object_describe_api_name":"BpmTask",
        "filters":[
             {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmTask"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "in_progress"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":false,
        "is_hidden":false,
        "label":"待办",
        "order":22,
        "package":"CRM"
    },
    {
        "type":"default",
        "created_by":"system",
        "object_describe_api_name":"BpmTask",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmTask"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "pass"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":false,
        "is_hidden":false,
        "label":"已办",
        "order":23,
        "package":"CRM"
    },
    {
        "type":"default",
        "created_by":"system",
        "object_describe_api_name":"BpmTask",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmTask"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "cancel"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },

        ],
        "is_default":false,
        "is_hidden":false,
        "label":"已终止",
        "order":24,
        "package":"CRM"
    },
    {
        "type":"default",
        "created_by":"system",
        "object_describe_api_name":"BpmTask",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmTask"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "error"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":false,
        "is_hidden":false,
        "label":"异常",
        "order":25,
        "package":"CRM"
    }
]'

#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------
#
#分割线    下面是initSearchTemplateByJson    【【【【【BpmInstance对象】】】】】
#
#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------



 curl -X POST \
  $url'/crmrest/tenant_init/initSearchTemplateByJson?apiName=BpmInstance&allNet='$allNet'&tenantIds='$tenant_id \
-H 'cache-control: no-cache' \
  -H 'x-fs-ei: 53395' \
  -H 'x-fs-userinfo: 1000' \
  -d '[
    {
        "type":"default",
        "object_describe_api_name":"BpmInstance",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmInstance"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "in_progress"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":true,
        "is_hidden":false,
        "label":"进行中",
        "order":2,
        "package":"CRM"
    },
    {
        "type":"default",
        "object_describe_api_name":"BpmInstance",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmInstance"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "pass"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":false,
        "is_hidden":false,
        "label":"已完成",
        "order":4,
        "package":"CRM"
    },
    {
        "type":"default",
        "object_describe_api_name":"BpmInstance",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmInstance"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "error"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":false,
        "is_hidden":false,
        "label":"异常",
        "order":3,
        "package":"CRM"
    },
    {
        "type":"default",
        "object_describe_api_name":"BpmInstance",
        "filters":[
        {
                "field_name":"object_describe_api_name",
                "field_values":[
                    "BpmInstance"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"state",
                "field_values":[
                    "cancel"
                ],
                "operator":"EQ",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            },
            {
                "field_name":"relevant_team.teamMemberEmployee",
                "field_values":[
                    "${current_user}"
                ],
                "operator":"IN",
                "connector":"AND",
                "fieldNum":0,
                "isObjectReference":false,
                "isIndex":false
            }
        ],
        "is_default":false,
        "is_hidden":false,
        "label":"终止",
        "order":3,
        "package":"CRM"
    }
]'

echo 'success'
