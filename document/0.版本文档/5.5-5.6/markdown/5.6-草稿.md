# 草稿

流程定义草稿，每个流程对应一个草稿，此与sourceWorkflowId,没有发布过的流程这里有可能出现多个草稿，其实也无所谓。

## 业务场景
###产生
当用户在使用过程中会保存为草稿，待下次接着编辑。
###使用
当再次进行编辑相同 sourceWorkflowId 的流程时，进行查询该操作人是否存在此 sourceWorkflowId 的草稿，若存在就给用户以提示，
是否要基于上次草稿接着编辑，若同意，就将其草稿进行编辑，若不同意就基于最新的流程定义进行编辑。
###删除
当流程进行发布时将草稿中与该操作人并 sourceWorkflowId 相同的草稿进行删除。
###注意
若上次修改后有新的修改后，别人对草稿进行了修改怎么办？
1. 可以无论是谁进行了发布都将 sourceWorkflowId 相同的草稿删除
2. 可以在进行编辑时取出的草稿的workflowId与当前的workflowId进行比较
   1. 若不同 就询问是对比查看，还是继续草稿编辑
   2. 若相同 就直接询问 是否要基于上次草稿接着编辑
   