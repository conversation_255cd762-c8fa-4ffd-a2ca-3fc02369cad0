#!/bin/sh
tenant_id='53694,53555'
url='http://10.112.32.60:8003/metadata'
allNet=false
#url='http://10.113.32.46:8003/metadata'
#url='http://172.17.35.223:8002/crm-metadata'
#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------
#
#分割线    下面是layout的
#
#---------------------------------------------------------------------------------------------------------
#---------------------------------------------------------------------------------------------------------

curl -X POST \
  $url'/crmrest/tenant_init/init_bpm?allNet=true&&tenantIds='+tenant_id \
  -H 'cache-control: no-cache' \
  -H 'postman-token: b7b9fd8d-018d-5d7d-5a2e-8b7e192cd9f6' \
  -H 'x-fs-ei: 55732' \
  -H 'x-fs-userinfo: 1000'

