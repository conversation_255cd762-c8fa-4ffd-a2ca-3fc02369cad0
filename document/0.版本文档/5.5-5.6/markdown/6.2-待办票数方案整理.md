## 票数机制

1. 需求整理

    1. 移动端首先显示未读数`红底白字`，当点击进入列表时，进行未读数复位；
    2. web端一直显示红底白字的待办数。

        |三端|创建了一个待办|创建了第二个待办|进入列表|
        |:--|:--|:--|:--|
        |web|红底白字1|红底白字2|红底白字2|
        |ios|红底白字1|红底白字2|白底红字2|
        |android|红底白字1|红底白字2|白底红字2|

1. 问题跟进

    ```
    db.getCollection('tasks').find({"appId":"BPM","tenantId":"501449","candidateIds":{$in:["501449"]},"state":{$in:["in_progress","error"]},"opinions":{$exists:true},"opinions.userId":{$ne:"501449"}}).count()
    ```

1. 服务器端处理方式

 1. 生成任务时，推送未读1和重新计算待办
 1. 完成任务时，重新计算所有人的待办
 1. 实例取消时，重新计算所有的人待办