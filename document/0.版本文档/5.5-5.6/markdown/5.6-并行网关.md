{"activities": [{"type": "startEvent", "ruleId": "start"}, {"type": "parallelGateway", "ruleId": "fork"}, {"type": "userTask", "ruleId": "t1"}, {"type": "userTask", "ruleId": "t2"}, {"type": "userTask", "ruleId": "t3"}, {"type": "parallelGateway", "ruleId": "join"}, {"type": "endEvent", "ruleId": "end"}], "transitions": [{"fromId": "start", "toId": "fork"}, {"fromId": "fork", "toId": "t1"}, {"fromId": "fork", "toId": "t2"}, {"fromId": "fork", "toId": "t3"}, {"fromId": "t1", "toId": "join"}, {"fromId": "t2", "toId": "join"}, {"fromId": "t3", "toId": "join"}, {"fromId": "join", "toId": "end"}]}