##WEB端
    1、接口：MetadataAction.FindObjActions(定义时，操作类型列表)
    结构：[
            {
                "actionCode": "signin",
                "actionName": "签到"
            },
            {
                "actionCode": "signout",
                "actionName": "签退",
                "isOpenSignOut" : true
            }
         ]

    2、接口：ProcessTask.GetTaskInfoByLaneId（待处理信息的展示)
    结构：result.date.actionCode 是 singin/signout     state 是 in_progress    待处理信息展示	“请到移动端签到或签退”
	     result.date.actionCode 是 singin/signout     state 是 pass 			待处理信息展示     result.data.signInInfoApiName（Map，保存签到/签退字段ApiName-签到地点（signInPlace）、签到时间（signInTime）、签退地点（signOutPlace）、签退时间（signOutTime））

    3、接口：ProcessTaskAction.GetTaskInfo（任务详情页）
    结构：展示result.data.signInInfoApiName信息（同上）
##手机端
    1、接口：MTask.GetButtonByTaskId（待办列表页获取按钮）
    结构："buttons": {
			"61dbd899f70bd906cf8f6bfb":{
				errorMsg : "错误信息",
				[
					{
					"action": "operation",
					"label": "签到",
					"code": "signin",
					},
					{
					"action": "operation",
					"label": "签退",
					"code": "signout",
					}

				]
			}
	    }
        2、接口：MTask.GetTaskInfo（查看已签到的任务，未签到任务只显示定位组件和按钮）
        结构：result.date.actionCode   signin/signout
             已签到/已签退任务展示result.data.signInInfoApiName （Map，保存签到/签退字段ApiName-签到地点（signInPlace）、签到时间（signInTime）、签退地点（signOutPlace）、签退时间（signOutTime））
             未签到/未签退任务显示按钮
        3、接口：MTaskAction.GetUncompletedTaskInfoByObject（对象详情页的业务流程列表）
        结构：dataList.data.actionCode   signin/signout
             错误信息（未签到先签退）在 dataList.errorMsg


