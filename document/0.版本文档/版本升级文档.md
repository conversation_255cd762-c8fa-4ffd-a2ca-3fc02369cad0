## 840 版本 2022.11

1. 审批节点支持编辑 高俊
2. 功能权限支持忽略和重试 梁楠

## 830 版本 2022.11

1. 多语 高俊
2. 委托日志下发 崔永旭
3. 流程定义外部通知v2保存 姬惠丰
4. 重新解析任务处理人rest接口 梁楠
5. 应用节点支持根据操作下发更换处理人 高俊

## 820 版本 2022.11

1. 业务节点，支持编辑关联对象字段·UE
2. 流程节点，支持绑定对象多自定义按钮
3. 业务流程和审批流后动作支持函数异步配置;默认 同步
4. 流程布局支持主从一起编辑

## 810 版本 2022.5-6

1. 业务流程实例终止后动作重试
2. 节点当不有人时支持重新解析处理人
3. UE 完成任务自动跳转(重点)
4. 业务流程待办 增加 待接单的工单
5. 延迟节点
6. 复制流程 优化

## 800 版本 2022.2-4

1. 更多提醒_待办提醒，支持短信提醒
2. 触发条件支持外部人员字段为空不为空(包括外部负责人字段)
3. 互联节点 流程处理人/CRM提醒，支持选择外部人员字段
4. 【移动端业务流-】-只创建关联对象 优化
5. 表单更新支持千分位

## 795 版本 2022.2-3

1. 业务流程支持签到签退操作 高俊
2. 移动端待办支持筛选 文浩
3. 业务流程审批节点支持审批角色

## 790 版本 2021.12

1. 【UI/UE】业务流程落地页-优化专项
2. 【接入翻译工作台】流程接入翻译工作台
3. 【UI/UE】业务流-待办列表，支持卡片式展示
4. 批量新建关联对象，支持保存并完成
5. 终止流程-后动作，支持自定义函数

## 785 版本 2022.12

1. 业务流编辑从对象，支持选择 默认进入的从对象
2. 流程待办迁移 文案优化
3. 业务流(小程序)应用节点支持多按钮
4. 【业务流小程序-详情页】- 阶段希望跟任务一起联动上
5. 业务流编辑从对象，支持选择 默认进入的从对象

## 780 版本 2022.11

1. [包括终端]业务流函数超时支持触发
2. [支持单点灰度,防止内容崩掉]基于小程序的待办列表 增加任务处理功能
3. 业务流程任务落地页转h5
4. 【UI优化】详情页中的业务流程版块，操作按钮被挤掉，展示变形

## 770 版本 2021.7-9

1. 终端: 业务流任务详情页优化 
2. H5 业务流程任务 按钮自定义(目前labelserver已经下发,需要h5展示)
3. 通用功能 定义的启用停用删除增加到CRM日志中心 
4. 业务流程 变更处理人原因 
5. 分支条件支持lookup对象字段 
6. 过滤器和分支 多选字段作为分支条件新增等于、不等于操作符 
7. 下游发起业务流将下游人员添加到实例的团队成员中

## 760 版本 2021.5-6

1. 批量更换处理人pg负载增高,导致同步较慢 引发问题, 待办业务流程问题。 此处优化方案,将批量更换处理人 放到单独的队列
2. 业务流功能权限支持设置 
3. 预设服务通新的待办类别 
4. 对象详情页业务流区域显示操作按钮 
5. 任务节点编辑页面支持签到
6. 业务流流程定义列表支持展示配额接口 
7. 流程待办迁移：支持选择待办数据
8. 流程后动作执行函数时增加是否异步的方式(透传到函数侧进行异步执行函数)
9. 流程增加当前处理人字段及数据同步
10. 流程日志日程的重构

## 750 版本 2021.3

1. 业务流运维监控支持当前处理人 
2. 服务通应用节点待办跳转优化 
3. 业务流运维监控 
4. 业务流程支持在函数中进行操作和处理 依赖函数团队的需求 
5. 业务流程日志中显示应用节点日志 
6. 触发条件和完成条件不满足时 增加一句化描述, 重点UE设计, 函数的不需要这样的一句话 
7. 流程日志日程的重构


## 740 版本 2020.11

1. 应用节点label从深研获取后下发
2. H5 填写任务和审批任务 的直接处理支持 
3. 应用节点处理 终端去处理
4. [独立]业务流程第一个节点将自动节点放开(同一个流程,同一数据连续触发 即进行 停止)
5. [独立]业务流程/阶段推进器 互联更换处理人 支持选择有关应用下所有的企业和人 临时权限的支持
6. 审批流 业务流程 同意和完成条件支持 函数校验
7. 更改集流程侧支持

## 730 版本 2020.9

1. 实例及任务同步到自定义对象异常时,重试机制优化
2. 终端业务流CML支持多语
3. 业务流程任务落地页走布局规则 
4. 终端任务处理完后获取当前人下一个任务 
5. 业务流优化-完成任务时,减少描述和数据查询的rpc请求次数 
6. 图片字段实现连拍功能 
7. 业务流程审批节点按钮支持修改名称（同意、不同意)
8. [依赖自定义对象，联调中]批量编辑从对象[对标阶段推进器的批量创建从对象]

## 720 版本 2020.6

1. 已上线 函数支持流程变量 流程发起人,流程发起时间,流程名称
2. 业务流草稿详情 功能按钮 只保留删除 
3. 业务流待办业务流 添加统计入口 
4. 支持批量创建关联对象 
5. 业务流终止流程后动作支持字段更新 
6. 业务流程 开票申请、退款支持
7. 引擎为分库
8. 专属云后动作黑名单支持
9. BPM详情页需要自动刷新

## 710 版本 2020.4

1. fs-flow 临时权限对接,将任务创建和处理人变更信息 同步到临时权限 
2. 业务流程支持线索转换按钮的标准化 
3. 业务流程的版本定义，后动作，完成条件展示 
4. 业务流程点击保存时不校验必填


## 700 版本 2020.2 @王志祥 @宋冀壵 
1. VIP-业务流程的节点完成后无法选择触发相同对象上的其他业务流程 
2. 服务通应用节点任务处理入口 
3. 新版BPM交互改造，数据详情页中支持转发和更换处理人 
4. BPM下游企业现实下游流程信息，不显示上游流程信息 
5. 业务流程定义：支持设置终止流程后动作
6. [跨迭代优化项]引擎BPM appId调整及type统一刷库
7. 业务流点击 "填写" 按钮,重新获取一次数据
8. 接入 引擎消息总线

## 690 版本 2019.12 

1. 函数支持审批意见变量
2. 业务流后台配置页优化
3. 待办巡检,将结果进行汇总 通知
4. 流程待办列表增加已读未读标识


## 680 版本 2019.12 

1. [独立上线,仅是web端]业务流程后台配置增加 默认跳转任务处理页面 配置
2. [独立]BPM任务在详情页的处理优化
3. [终端] BPM互联支持
4. [终端]新建或关联对象交互优化，移动端与Web端操作体验不一致 
5. 业务流程完成条件的比较符支持变量
6. [终端]BPM中操作按钮的显示名称支持自定义（本期单点修改）
7. [审批流/BPM]流程后动作支持排序

## 675 版本 2019.12

1. [独立上线,仅是web端]业务流程后台配置增加 默认跳转任务处理页面 配置
2. [独立]BPM任务在详情页的处理优化
3. [终端] BPM互联支持
4. [终端]新建或关联对象交互优化，移动端与Web端操作体验不一致
5. 业务流程完成条件的比较符支持变量
6. [终端]BPM中操作按钮的显示名称支持自定义（本期单点修改）
7. [审批流/BPM]流程后动作支持排序

## 670 2019.06-08
1. 营销流程实例分页
2. 任务详情页-查询对象描述,layout,数据信息
3. crm新模块调用方式迁移(交互改告迁移出去的任务处理部分)
4. BPM终端交互改版优化
5. 业务流程 form支持布局规则
6. 待处理业务流程列表列不可编辑,且三列提前
7. 应用节点支持超时提醒
8. BPM前台交互改版优化
9. 任务&后动作异步化
10. 审批任务的审批信息与编辑节点的配置进行区分
11. 业务流程 应用节点选项通用化
12. 设计器添加任务的铵钮自定义（中）
13. 任务处理完成后，回到待办列表时需要自动刷新

## 665 2019.05

1. 业务流程后动作异常,流程日志中重试和忽略按钮展示优化
1. 业务流流程日志中将普通业务节点后动作异常和等待的后动作展示出来
1. 业务流程模板的展示优化
1. 功能权限修改actionCode为驼峰命名


## 660 2019.01-03

1. [独立上线] 完整流程视图任务分页展示
2. 【和产品沟通后暂保持不变】主从关系当是从对象随主对象一起新建的关系时 业务流程定义时不能选择此类从对象
3. 【独立上线】会签待办处理完后在待办列表中一直存在的问题
4. [引擎 大迭代 延期一周 预估3.6] 自动节点; 在前端进行展示
5. [引擎 大迭代 延期一周 预估3.6] 支持定时等待节点[BPM设计器暂时不支持]
6. [独立灰度上线]业务流程代码重构 生成任务 执行任务逻辑
7. [独立上线]业务流程更新按钮 过一下 功能权限
8. [独立上线-产品讨论结果不做]任务和实例设置成MD
9. [独立上线]数据负责人也可以终止流程
10. [引擎] 业务流程审批节点支持前置条件

## 650 2018.11-12

1. 添加业务流程删除接口
2. BPM流程中，手机端数据无法正常显示
3. 深研依赖需求，电子签章
4. 业务流程-后动作 和 自动节点 加锁,解锁
5. 业务流-编辑任务上可以放统计和计算字段
6. 业务流- 实例详情，节点没有显示完全（分支，自动节点，审批）
7. 业务流-并行节点名称，可以自定义
8. 业务流程-指定一下节点处理人，支持选择范围
9. 深研依赖需求-预制bpm流程-处理人使用角色
10. 超时提醒支持变量（公式），支持分钟，小时，天
11. 当发起流程后查找关联字段被删除web端有提示，手机端没有提示
12. 企业微信-业务流程及审批流待办箱开发
13. 审批流，业务流待办列表支持过滤和对象属性的显示
14. 审批流，业务流，终端的待办列表显示的字段支持自定义

## 6.3.4 2018.09

1. 自定义对象的验证规则在BPM流程内不生效
2. 管理后台改造-提供一个可以查询当前企业使用了多少业务流程的接口
3. 点击完成任务，如果下一个任务的处理人还是我，则待处理列表没有刷新】-安卓和ios优化项
4. 产品优化项：编辑任务，如果没有字段需要填写，则在点击填写按钮时提示：没有需要填写的字段
5. 业务流程任务执行时,记录指定的上一节点处理人
6. 业务流程后动作字段更新人员类型的字段，值可以选择执行人变量-深研依赖
7. 深研依赖-bpm分支条件人员字段支持为空不为空的过滤
8. 业务流程终止，需要填写终止原因
9. 业务流程-移动端任务详情支持预览图片和预览附件，下载附件
10. 协同深研依赖需求-应用节点增加新建外勤计划事件后选择分类
11. 业务流程单选字段使用详情页通用组件，支持搜索
12. 业务流程任务编辑对象时支持验证规则
13. 流程-业务流程任务新增或关联支持从对象
14. 高级筛选不支持所属业务对象
15. 业务流程form中进行编辑时需要走数据权限

## 6.3.2 2018.7-8

1. 业务流程-定义配置可以暂存
2. 业务流程列表场景支持自定义对象的默认场景
3. [BPM]签到组件，签到时，可以选择附近的公司或者是商圈
4. 深研依赖需求：bpm应用节点支持流程后动作

## 6.3.1 2018.6-7

1. 流程过滤条件和分支条件选择字段提供搜索能力—三个流程都需要

## 6.3.0 2018.2-5

1. 业务流程后台校验整理
2. 流程后动作触发自定义函数
3. 代码重构
4. 流程创建和更新时记录操作人到流程定义的版本信息中
5. 业务流程h5地址服务器端下发
6. 后动作支持任务，日程，销售记录
7. 显示他人正在处理中，显示哪些人处理把人员显示出来，点击姓名可以调到企信
8. 添加附件可以选择网盘里的文件
9. 自动节点,使用工作流已有的executionTask
10. 审批节点后动作分开,pass和reject后动作执行情况不同
11. 支持签到，支付组件
12. 提醒变量增加所有前置节点处理人


## 6.2.0

1. 停留时长支持字段
2. 审批/会签节点支持表单
3. 流程定义列表支持筛选
4. 上一节点选择下一节点处理人
5. 支持外部节点和外部联系人
6. 添加同一条数据对同一个流程只能发起一次实例或多次的配置
7. 操作增加： 支持签到，支付
8. 作废数据时同时终止业务流程实例

## 6.1.0

1. 节点后动作
2. 对象作废
3. 节点会签驳回
4. 过滤条件支持统计
5. 企信转发

## 5.5 

1. 支持：客户、自定义对象 
2. 设计器支持： 开始，结束，人工，分支，并行
3. 流程统计视图
4. 待处理
5. 我发起的业务流程

