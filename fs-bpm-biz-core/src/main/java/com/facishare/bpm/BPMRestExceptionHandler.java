package com.facishare.bpm;

import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpmn.definition.exception.WorkflowValidateException;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.ext.RestRequest;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.rest.ext.handler.CepExtExceptionMapper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.ValidationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.SocketTimeoutException;
import java.util.Objects;
@Slf4j
@Component
public class BPMRestExceptionHandler extends CepExtExceptionMapper {
    @Override
    public Response handler(RestRequest request, Throwable throwable) {
        if(UndeclaredThrowableException.class.isInstance(throwable)){
            throwable = Objects.nonNull(throwable.getCause()) ? throwable.getCause() : throwable;
        }

        Response.ResponseBuilder builder;
        if (throwable instanceof BPMBusinessException) {
            BPMBusinessException businessException = (BPMBusinessException) throwable;
            String failMsg = StringUtils.isNotBlank(businessException.getMessage()) && businessException.getMessage().length() > 500
                    ? businessException.getMessage().substring(0, 500)
                    : businessException.getMessage();
            builder = Response.ok()
                    .header(RestConstant.CepHeader.FAILURE_CODE.getName(), businessException.getErrorCode())
                    .header(RestConstant.CepHeader.FAILURE_MESSAGE.getName(), failMsg)
                    .type(MediaType.TEXT_PLAIN);
        } else if (RestProxyBusinessException.class.isInstance(throwable)) {
            RestProxyBusinessException businessException = (RestProxyBusinessException) throwable;
            builder = Response.ok()
                    .header(RestConstant.CepHeader.FAILURE_CODE.getName(), businessException.getCode())
                    .header(RestConstant.CepHeader.FAILURE_MESSAGE.getName(), businessException.getMessage())
                    .type(MediaType.TEXT_PLAIN);
        } else if (ValidationException.class.isInstance(throwable)) {
            ValidationException businessException = (ValidationException) throwable;
            builder = Response.ok()
                    .header(RestConstant.CepHeader.FAILURE_CODE.getName(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY.getCode())
                    .header(RestConstant.CepHeader.FAILURE_MESSAGE.getName(), businessException.getMessage())
                    .type(MediaType.TEXT_PLAIN);
        } else if (throwable instanceof WorkflowValidateException) {
            WorkflowValidateException businessException = (WorkflowValidateException) throwable;
            builder = Response.ok()
                    .header(RestConstant.CepHeader.FAILURE_CODE.getName(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY.getCode())
                    .header(RestConstant.CepHeader.FAILURE_MESSAGE.getName(), businessException.getMessage())
                    .type(MediaType.TEXT_PLAIN);
        } else if (RestProxyRuntimeException.class.isInstance(throwable) || SocketTimeoutException.class.isInstance(throwable)) {
            String traceId = RestRequest.getLocal().getContext().getTraceId();
            builder = Response.ok()
                    .header(RestConstant.CepHeader.FAILURE_CODE.getName(), "s210000001")
                    .header(RestConstant.CepHeader.FAILURE_MESSAGE.getName(), BPMI18N.PAAS_FLOW_BPM_SYSTEM_ERROR_CONTACT_SERVICER.text() + "：" + traceId)
                    .type(MediaType.TEXT_PLAIN);
        } else {
            log.error("", throwable);
            builder = Response.ok()
                    .header(RestConstant.CepHeader.FAILURE_CODE.getName(), Response.Status.INTERNAL_SERVER_ERROR.getStatusCode())
                    .header(RestConstant.CepHeader.FAILURE_MESSAGE.getName(), BPMI18N.PAAS_FLOW_BPM_SYSTEM_ERROR_CONTACT_SERVICER.text() + ": " + TraceContext.get().getTraceId())
                    .type(MediaType.TEXT_PLAIN);
        }
        return builder.build();
    }

    @Override
    public String getFlowType() {
        return RestConstant.FlowType.BPM;
    }
}
