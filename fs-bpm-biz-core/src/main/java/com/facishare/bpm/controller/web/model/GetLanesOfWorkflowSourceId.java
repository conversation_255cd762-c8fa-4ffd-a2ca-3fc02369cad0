package com.facishare.bpm.controller.web.model;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 24/02/2017.
 */
public interface GetLanesOfWorkflowSourceId {
    @Data
    class Arg {
        private String sourceWorkflowId;
    }

    class Result extends ArrayList<LaneDetail> {

    }

    @Data
    class LaneDetail {
        private String laneId;
        private String name;
        private List<LaneActivity> activities;

        public List<LaneActivity> getActivities() {
            if (activities == null) {
                activities = Lists.newArrayList();
            }
            return activities;
        }
    }
    @Data
    class LaneActivity {
        private String activityId;
        private String name;
    }
}
