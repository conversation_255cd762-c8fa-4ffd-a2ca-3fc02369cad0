package com.facishare.bpm.controller.mobile.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.mobile.MDefinitionAction;
import com.facishare.bpm.controller.mobile.model.MGetAllDefinitionList;
import com.facishare.bpm.controller.mobile.model.MGetAvailableWorkflowOfObject;
import com.facishare.bpm.controller.mobile.model.MGetAvailableWorkflows;
import com.facishare.bpm.controller.mobile.model.MGetBPMUseApiNames;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.util.I18NParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 12/04/2017.
 */
@Service
@Slf4j
public class MDefinitionActionImpl extends BPMBaseAction implements MDefinitionAction {
    @Autowired
    private BPMDefinitionService definitionService;

    @Override
    public MGetAvailableWorkflowOfObject.Result getAvailableWorkflowOfObject(MGetAvailableWorkflowOfObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> outlines = definitionService.getAvailableWorkflows(
                serviceManager, arg.getEntryType(), arg.getObjectId(), true, 0, null, Boolean.FALSE);
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        return new MGetAvailableWorkflowOfObject.Result(outlines);
    }

    @Override
    public MGetAvailableWorkflows.Result getAllAvailableWorkflows(MGetAvailableWorkflows.Arg arg) {
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> outlines = definitionService.getAvailableWorkflows(serviceManager, null, null, false, null, null, Boolean.TRUE);
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        return new MGetAvailableWorkflows.Result(outlines);
    }

    @Override
    public MGetAllDefinitionList.Result getAllDefinitionList() {
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> outlines = definitionService.getWorkflowOutlines(serviceManager, null, new Page(100, 1, null,
                false), null, Boolean.TRUE).getDataList();
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        List<MGetAllDefinitionList.SimpleWorkflow> simpleWorkflows = outlines
                .stream()
                .map(MGetAllDefinitionList.SimpleWorkflow::getSimpleWorkflow)
                .collect(Collectors.toList());

        return new MGetAllDefinitionList.Result(simpleWorkflows);
    }

    @Override
    public MGetBPMUseApiNames.Result getBPMUseApiNames() {
        RefServiceManager serviceManager = getManager();
        Map<String, String> useApinames = definitionService.getUseApiNames(serviceManager);
        return new MGetBPMUseApiNames.Result(useApinames);
    }
}
