package com.facishare.bpm.controller.mobile.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.mobile.MInstanceAction;
import com.facishare.bpm.controller.mobile.model.*;
import com.facishare.bpm.controller.web.model.GetInstancesByObject;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMMobileUpgradeException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.WorkflowInstanceLog;
import com.facishare.bpm.model.instance.EntireWorkflow;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import com.facishare.bpm.model.instance.TriggerWorkflow;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.TaskLog;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.I18NParser;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.ValidationException;
import java.util.List;
import java.util.Objects;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY;

/**
 * Created by Aaron on 12/04/2017.
 */
@Component
@Slf4j
public class MInstanceActionImpl extends BPMBaseAction implements MInstanceAction {
    @Autowired
    private BPMInstanceService instanceService;
    @Autowired
    private BPMTaskService taskService;


    @Override
    public MStartInstance.Result startInstance(MStartInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String instanceId = instanceService.startWorkflow(serviceManager, TriggerSource.person, arg.getId(), arg.getObjectId());
        return new MStartInstance.Result(instanceId);
    }

    @Override
    public MCancelInstance.Result cancelInstance(MCancelInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        instanceService.cancelWorkflowInstance(serviceManager, arg.getId(), arg.getReason());
        return new MCancelInstance.Result();
    }

    @Override
    public MGetInstanceList.Result getInstanceList(MGetInstanceList.Arg arg) {
        throw new BPMMobileUpgradeException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION);
    }

    @Override
    public MGetInstanceListByObject.Result getInstanceListByObject(MGetInstanceListByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getInstanceListByWorkflowSourceId : OBJECT_ID={}, STATE={}", arg.getObjectId(), arg.getState());
        PageResult<WorkflowInstanceVO> instancePage = instanceService.getWorkflowInstances(serviceManager, arg.getState(), arg.getObjectId(),
                arg.getPageSize(), arg.getPageNumber(), arg.getOrderBy(), null);
        List<WorkflowInstanceVO> dataList = instancePage.getDataList();
        I18NParser.parse(serviceManager.getTenantId(),dataList);
        return new MGetInstanceListByObject.Result(instancePage.getTotal(), dataList,
                serviceManager.getEmployeeInfo(WorkflowInstanceVO.getPersons(instancePage.getDataList())));
    }

    @Override
    public MGetEntireWorkflow.Result getEntireWorkflowInstance(MGetEntireWorkflow.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        EntireWorkflow instance = instanceService.getEntireWorkflowInstance(serviceManager, arg.getInstanceId());
        I18NParser.parse(serviceManager.getTenantId(),instance);
        return new MGetEntireWorkflow.Result(instance);
    }

    @Override
    public MGetLog.Result getLog(MGetLog.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        List<TaskLog> taskLogs = taskService.getTaskLogs(serviceManager, arg.getWorkflowInstanceId());
        I18NParser.parse(serviceManager.getTenantId(),taskLogs);
        return new MGetLog.Result(taskLogs);
    }

    @Override
    public MGetWorkflowInstanceLog.Result getWorkflowInstanceLog(MGetWorkflowInstanceLog.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        WorkflowInstanceLog workflowInstanceLog = instanceService.getWorkflowInstanceLog(serviceManager, arg.getWorkflowInstanceId());
        I18NParser.parse(serviceManager.getTenantId(),workflowInstanceLog);
        return new MGetWorkflowInstanceLog.Result(workflowInstanceLog);
    }

    @Override
    public MGetSomeConfig.Result getSomeConfig(MGetSomeConfig.Arg arg) {
//        RefServiceManager serviceManager = getManager();
//        String url = BPMSwitchConfigHelper.getHtml5Url(serviceManager.getTenantId());
        return new MGetSomeConfig.Result();
    }

    @Override
    public MGetCustomRedirectConfig.Result getCustomRedirectConfig(MGetCustomRedirectConfig.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        try {
            log.debug("bpmTodo arg:{}", JacksonUtil.toJson(arg));
            RemoteContext context = serviceManager.getContext();

            Boolean downstream = arg.getDownstream();
            if(Objects.isNull(downstream)){
                downstream = context.isOuterPerson();
            }
            String bpmTodoUrl = eServiceResourceProxy.getBpmTodoUrl(context, arg.getEntityId(), arg.getObjectId(), arg.getLinkAppId(), downstream, context.getClientInfo().getFullStr());
            log.debug("bpmTodoUrl:{}", bpmTodoUrl);
            return new MGetCustomRedirectConfig.Result(bpmTodoUrl);
        } catch (Exception e) {
            log.error("getCustomRedirectConfig:{},{}", JacksonUtil.toJson(arg), e);
        }
        return new MGetCustomRedirectConfig.Result();
    }

    @Override
    public MTriggerInstance.Result triggerInstance(MTriggerInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        TriggerWorkflow triggerWorkflow = instanceService.triggerWorkflow(serviceManager, TriggerSource.person, null, arg.getId(), arg.getObjectId());
        return new MTriggerInstance.Result(triggerWorkflow.getResult(), triggerWorkflow.getRuleMessage());
    }

    @Override
    public MInstanceAfterActionRetry.Result afterActionRetry(MInstanceAfterActionRetry.Arg arg) {
        validateArg(arg);
        return new MInstanceAfterActionRetry.Result(instanceService.afterActionRetry(getManager(), arg.getInstanceId(), arg.getRowNum(), arg.getExecuteType()));
    }

    @Override
    public GetTaskOrInstanceRelatedEntityId.Result getTaskOrInstanceRelatedEntityId(GetTaskOrInstanceRelatedEntityId.Arg arg) {
        validateArg(arg);
        if (Strings.isNullOrEmpty(arg.getTaskId()) && Strings.isNullOrEmpty(arg.getInstanceId())) {
            log.warn("实例id及任务id为空");
            throw new ValidationException(BPMI18N.getI18NByName(PAAS_FLOW_BPM_PARAMETER_ANOMALY));
        }
        return new GetTaskOrInstanceRelatedEntityId.Result(instanceService.getTaskOrInstanceRelatedEntityId(getManager(), arg.getInstanceId(), arg.getTaskId()));
    }

    @Override
    public GetInstancesByObject.Result getInstancesByObject(GetInstancesByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        List<GetInstanceByObject> instances = instanceService.getInstancesByObject(serviceManager, arg.getEntityId(), arg.getObjectId());
        I18NParser.parse(serviceManager.getTenantId(),instances);
        return new GetInstancesByObject.Result(instances);
    }
}
