package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMParamsException;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/6/4 11:44 上午
 */
public interface MCreateTaskData {

    @Slf4j
    @Data
    class Arg {

        @DocDescribe(label = "任务id", required = true)
        @NotEmpty(message = "任务id不能为空")
        @JSONField(name = "M1")
        private String taskId;

        @JSONField(name = "M2")
        @DocDescribe(label = "任务对应的activityId", required = true)
        @NotEmpty(message = "任务对应的activityId不能为空")
        private String activityId;

        @JSONField(name = "M3")
        @NotNull(message = "任务对应的activityInstanceId不能为空")
        @DocDescribe(label = "任务对应的activityInstanceId", required = true)
        private Integer activityInstanceId;
        /**
         * 任务关联的数据
         *
         * <pre>
         *     batchAddRelatedObject: 批量创建关联对象
         *          entityId: 关联对象apiname
         *          objectId: 关联对象数据id
         * </pre>
         */
        @JSONField(name = "M4")
        @NotEmpty(message = "数据内容不能为空")
        @DocDescribe(label = "数据内容", desc = "batchAddRelatedObject(entityId: 批量创建数据的apiname,objectId: 批量创建数据的数据Id)", required = true)
        private Map<String, Object> data;

        @NotEmpty(message = "任务类型不能为空")
        @DocDescribe(label = "任务类型", required = true)
        private String executionType;

        public ExecutionTypeEnum getExecutionType() {
            if (Strings.isNullOrEmpty(executionType)) {
                log.error("executionType 为空,taskId:{}", taskId);
                throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_INCORRECT);
            }
            try {
                return ExecutionTypeEnum.valueOf(executionType);
            } catch (Exception e) {
                log.error("executionType 枚举不存在,taskId:{},executionType:{}", taskId, executionType);
                throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_INCORRECT);
            }
        }

    }

    @Data
    class Result {

        private Boolean result;

        public Result(Boolean result) {
            this.result = result;
        }
    }
}
