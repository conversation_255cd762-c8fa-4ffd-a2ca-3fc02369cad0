package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-1-6.
 */
public interface GetAvailableWorkflows {
    @Data
    class Arg {
        @NotEmpty(message = "入口对象类型不能为空")
        private String entryType;
        private String objectId;
        /**
         *  是否校验发起人范围
         *  false:不校验
         *  true/null:校验
         */
        private Boolean validateScope;
    }

    @Data
    @NoArgsConstructor
    class Result {
        protected List<SimpleOutline> outlines;

        public Result(List<WorkflowOutline> outlines) {
            this.outlines=outlines.stream().map(outline-> BeanUtils.transfer(outline,SimpleOutline.class,(src,rst)->{
                rst.setSingleInstanceFlow(src.getSingleInstanceFlow()==null?0:src.getSingleInstanceFlow());
            })).collect(Collectors.toList());
        }
    }
    @Data
    class SimpleOutline{
        private String id;

        private String sourceWorkflowId; //关联workflow soureid

        private String name; //流程名

        private String description;

        private String entryType; //入口对象类型

        private String createdBy;

        private long createTime;

        private Integer singleInstanceFlow;

        private String lastModifiedBy;

        private long lastModifiedTime;


    }
}
