package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/18 2:35 PM
 */
public interface GetTaskDetail {

    @Data
    class Arg {
        private String id;
    }

    @Data
    class Result {
        private String id;
        private String name;
        private String description;
        private String laneName;
        private String laneId;
        private TaskState state;
        private String applicantId;
        private String workflowId;
        private String workflowInstanceId;
        private String entityId;
        private String objectId;
        private List<String> candidateIds;
        private List<String> processIds;
        @DocDescribe(desc = "1-天；2-小时；3-分钟")
        private int latencyUnit;
        private Object remindLatency;
        private boolean isTimeout;
        private ExecutionTypeEnum executionType;
        private String taskType;
        private AfterActionExecution.SimpleAfterActionExecution execution;


        private String errorMsg;
        @DocDescribe(desc = "是否需要指定下一节点处理人1 指定，非1 不指定")
        private int assignNextTask;
        private Set<String> nextTaskAssigneeScope;
        /**
         * addteammember:[
         * actionCode:addteammember
         * actionlable:添加团队成员
         * actionRight:true/false
         * ]
         */
        private Map<String, ActionButton> button;

        private Map<ExecutionTypeEnum, StandardData> data;

        private List<MoreOperationManager.MoreOperation> moreOperations;

        private Long createTime;

        private Long modifyTime;
        private String activityId;

        private int activityInstanceId;
        private String todoJumpUrl;


    }


}
