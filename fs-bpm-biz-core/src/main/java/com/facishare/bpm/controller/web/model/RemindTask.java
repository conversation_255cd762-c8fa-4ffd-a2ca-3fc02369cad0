package com.facishare.bpm.controller.web.model;

import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

public interface RemindTask {
    @Data
    class Arg{
        @DocDescribe(label = "任务id", required = true)
        @NotBlank(message = "任务id不能为空")
        private String taskId;

        @DocDescribe(label = "催办内容")
        private String content;

        @DocDescribe(label = "催办人员")
        private List<String> remindPersons;

    }

    @Data
    class Result{

    }
}
