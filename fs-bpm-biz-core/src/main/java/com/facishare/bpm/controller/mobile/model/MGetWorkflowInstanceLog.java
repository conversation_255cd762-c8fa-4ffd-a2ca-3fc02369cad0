package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.*;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface MGetWorkflowInstanceLog {
    @Data
    class Arg {
        @NotEmpty(message = "workflowInstanceId 不能为空")
        @JSONField(name = "M1")
        private String workflowInstanceId;
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private String name;
        @JSONField(name = "M2")
        private InstanceState state;
        @JSONField(name = "M3")
        private String applicantId;
        @JSONField(name = "M4")
        private Long startTime;
        @JSONField(name = "M5")
        private Long endTime;
        @JSONField(name = "M6")
        private List<UserTaskLog> logs;
        @JSONField(name = "M7")
        private String cancelPersonId;
        @JSONField(name = "M8")
        private String reason;
        @JSONField(name = "M9")
        private Map<String, Employee> employeeInfo;
        @JSONField(name = "M10")
        private Map<String, AfterActionExecution.SimpleAfterActionExecution> execution;
        @JSONField(name = "M11")
        private String submitter;
        @JSONField(name = "M12")
        private String cancelSourceMessage;

        public Result(WorkflowInstanceLog instance) {
            this.setName(instance.getName());
            this.setState(instance.getState());
            this.setCancelPersonId(instance.getCancelPersonId());
            this.setApplicantId(instance.getApplicantId());
            this.setStartTime(instance.getStartTime());
            this.setEndTime(instance.getEndTime());
            this.setSubmitter(instance.getSubmitter());

            List<TaskLog> logs = instance.getLogs();
            if(CollectionUtils.isNotEmpty(logs)){
                this.setLogs(logs.stream().map(key -> {
                    UserTaskLog userTaskLog = BeanUtils.transfer(key, UserTaskLog.class);
                    userTaskLog.setNextTaskAssignee(key.getNextTaskAssignee());
                    userTaskLog.setNewCandidateLogs(key.getCandidateLogs());
                    userTaskLog.setNewUnCompletedPersons(key.getUnCompletedPersons());
                    userTaskLog.setTaskDelegateLogs(key.getTaskDelegateLogs());
                    return userTaskLog;
                }).collect(Collectors.toList()));
            }
            this.setReason(instance.getReason());
            this.setEmployeeInfo(instance.getEmployeeInfo());
            this.setExecution(instance.getExecution());
            this.setCancelSourceMessage(instance.getCancelSourceMessage());
        }

    }

    @Data
    class UserTaskLog {
        private String id;
        private String name;
        private String desc;
        private TaskState state;
        private UserTaskType type;
        private Long startTime;
        private Long endTime;
        private List<Opinion> opinions;
        private AfterActionExecution.SimpleAfterActionExecution execution;
        private AfterActionExecution.SimpleAfterActionExecution timeoutExecution;
        private String errorMsg;
        private List<String> unCompletedPersons;
        private String entityId;
        private String objectId;
        private Object remindLatency;
        //unit:1-天；2-小时；3-分钟
        private Integer latencyUnit;
        private List<ChangeCandidateLog> candidateLogs;
        private List<TaskDelegateViewLog> taskDelegateLogs;
        private Map<String, Object> nextTaskAssignee;
        //6.6 新加  用于区分 是自动节点  定时节点 还是普通的业务节点
        private NodeType nodeType;
        private String linkAppName;
        private Boolean delay;//810 是否是延迟节点标识 true-是
        private Long executeTime;//预计执行时间   810添加的延迟执行节点执行时间需要在日志上显示
        private List<TaskLog.NodeAction> operations;//添加原因 810添加的延迟节点有刷新和立即执行的操作
        private String executor;
        private List<OperateLog> operateLogs;
        private List<Task.RemindLog> remindLogs;
        private List<MoreOperationManager.MoreOperation> moreOperations;

        public void setNewCandidateLogs(List<ChangeCandidateLog> candidateModifyLog) {
            if (CollectionUtils.isNotEmpty(candidateModifyLog)) {
                this.candidateLogs = candidateModifyLog.stream().map(item -> BeanUtils.transfer(item, ChangeCandidateLog.class, (src, rst) -> {
                    rst.setFrom(src.getFrom());
                    rst.setTo(src.getTo());
                    rst.setOperatorId(src.getOperatorId());
                    rst.setModifyTime(src.getModifyTime());
                    rst.setOpinion(src.getOpinion());
                })).collect(Collectors.toList());
            } else {
                this.candidateLogs = Lists.newArrayList();
            }
        }

        public void setNewUnCompletedPersons(List<String> unCompletedPersons) {
            this.unCompletedPersons = unCompletedPersons;
        }
    }

}
