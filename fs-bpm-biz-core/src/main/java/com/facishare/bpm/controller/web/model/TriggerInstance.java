package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.rule.RuleMessage;
import lombok.Data;
import lombok.ToString;

public interface TriggerInstance {
    @Data
    @ToString(callSuper = true)
    class Arg extends StartInstance.Arg {
    }

    @Data
    @ToString(callSuper = true)
    class Result extends StartInstance.Result {
        private RuleMessage ruleMessage;

        public Result(String result, RuleMessage ruleMessage) {
            this.result = result;
            this.ruleMessage = ruleMessage;
        }

    }
}
