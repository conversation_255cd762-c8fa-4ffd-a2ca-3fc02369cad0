package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-2-8.
 */
public interface MGetUncompletedTasksByObject {
    @Data
    class Arg {
        @Descriable(value = "自定义对象apiName", required = true)
        @JSONField(name = "M1")
        @NotEmpty(message = "对象类型不能为空")
        private String apiName;
        @Descriable(value = "自定义对象id", required = true)
        @NotEmpty(message = "记录id不能为空")
        @JSONField(name = "M2")
        private String objectId;
    }

    @Data
    class Result {
        @Descriable(value = "对象下的待办任务列表")
        @JSONField(name = "M1")
        private List<ObjectUnCompletedTask> dataList;

        public Result(List<TaskOutline> outlines) {
            this.dataList = outlines.stream().map(item -> ObjectUnCompletedTask.fromOutline(item)).collect(Collectors.toList());
        }
    }

    @Setter
    @Getter
    class ObjectUnCompletedTask {
        @Descriable(value = "流程名称")
        @JSONField(name = "M1")
        private String workflowName;
        @Descriable(value = "任务id")
        @JSONField(name = "M2")
        private String taskId;
        @Descriable(value = "任务名称")
        @JSONField(name = "M3")
        private String taskName;
        @Descriable(value = "任务描述")
        @JSONField(name = "M4")
        private String description;
        @Descriable(value = "任务状态")
        @JSONField(name = "M5")
        private TaskState state;
        @Descriable(value = "是否是当前人员的待办")
        @JSONField(name = "M6")
        private Boolean isTaskOwner;
        @Descriable(value = "任务创建时间")
        @JSONField(name = "M7")
        private Long createTime;
        @Descriable(value = "节点实例id,进行完成视图时使用其来获取初始化信息")
        @JSONField(name = "M8")
        private int activityInstanceId;
        @Descriable(value = "实例id")
        @JSONField(name = "M9")
        private String instanceId;
        @Descriable(value = "是否超时")
        @JSONField(name = "M10")
        private Boolean isTimeout = false;
        @Descriable(value = "任务类型")
        @JSONField(name = "M11")
        private ExecutionTypeEnum executionType;
        @Descriable(value = "关联对象apiName")
        @JSONField(name = "M12")
        private String relatedEntityId;
        @Descriable(value = "阶段名称")
        @JSONField(name = "M13")
        private String laneName;
        @Descriable(value = "流程启动时间")
        @JSONField(name = "M14")
        private Long startTime;
        @Descriable(value = "是否属于当前业务记录")
        @JSONField(name = "M15")
        boolean belongToCurrentObj = true;
        @Descriable(value = "阶段id")
        @JSONField(name = "M16")
        private String laneId;
        @Descriable(value = "互联应用名称")
        @JSONField(name = "M17")
        private String linkAppName;
        @Descriable(value = "具体操作类型,一转三时使用")
        @JSONField(name = "M18")
        private String actionCode;

        public static ObjectUnCompletedTask fromOutline(TaskOutline item) {
            ObjectUnCompletedTask task = new ObjectUnCompletedTask();
            task.setWorkflowName(item.getProcessName());
            task.setTaskId(item.getTaskId());
            task.setTaskName(item.getTaskName());
            task.setDescription(item.getDescription());
            task.setState(item.getState());
            task.setIsTaskOwner(item.getIsTaskOwner());
            task.setCreateTime(item.getCreateTime());
            task.setActivityInstanceId(item.getActivityInstanceId());
            task.setInstanceId(item.getWorkflowInstanceId());
            task.setIsTimeout(item.getIsTimeout());
            task.setLaneName(item.getLaneName());
            task.setLaneId(item.getLaneId());
            task.setStartTime(item.getStartTime());
            task.setBelongToCurrentObj(item.isBelongToCurrentObj());
            task.setLinkAppName(item.getLinkAppName());
            task.setActionCode(item.getActionCode());
            return task;
        }

    }
}
