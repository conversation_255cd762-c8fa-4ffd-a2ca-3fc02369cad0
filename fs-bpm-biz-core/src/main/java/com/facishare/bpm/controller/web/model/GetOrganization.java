package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.OrgOutline;
import com.facishare.bpm.model.org.BPMOrg;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by cuiyongxu on 17/5/4.
 */
public interface GetOrganization {
    @Data
    class Arg extends OrgOutline {
        private Object type;
    }

    @Data
    @AllArgsConstructor
    class Result extends BPMOrg {
        public static Result create(BPMOrg bpmOrg) {
            GetOrganization.Result result = new GetOrganization.Result();
            result.setDept(bpmOrg.getDept());
            result.setRole(bpmOrg.getRole());
            result.setCRMGroup(bpmOrg.getCRMGroup());
            result.setPerson(bpmOrg.getPerson());
            result.setExternalPerson(bpmOrg.getExternalPerson());
            result.setExternalRole(bpmOrg.getExternalRole());
            return result;
        }
    }
}
