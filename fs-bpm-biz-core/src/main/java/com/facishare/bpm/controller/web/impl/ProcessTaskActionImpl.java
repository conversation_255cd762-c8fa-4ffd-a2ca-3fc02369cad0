package com.facishare.bpm.controller.web.impl;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.ProcessTaskAction;
import com.facishare.bpm.controller.web.model.CompleteTask;
import com.facishare.bpm.controller.web.model.GetTask;
import com.facishare.bpm.controller.web.model.OperateTask;
import com.facishare.bpm.controller.web.model.RemindTask;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.newmetadata.UpdateData;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.*;
import com.facishare.bpm.remote.crmremind.CrmRemindServiceProxy;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.CompleteTaskFormValidateManager;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.util.memory.page.Comparation;
import com.facishare.bpm.util.memory.page.ComparationType;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.common.StopWatch;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by cuiyongxu on 16/12/27.
 */
@Slf4j
@Service
public class ProcessTaskActionImpl extends BPMBaseAction implements ProcessTaskAction {

    @Autowired
    private BPMTaskService taskService;
    @Autowired
    private MoreOperationManager moreOperationManager;
    @Autowired
    private CrmRemindServiceProxy crmRemindServiceProxy;


    /**
     * 获取个人任务列表
     */
    @Override
    public GetHandleTaskList.Result getHandleTaskList(GetHandleTaskList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("GetHandleTaskList start , EID={}, PAGE_NO.={}, PAGE_SIZE={}, IS_COMPELTED={}, TASK_NAME={}", serviceManager
                .getUserId(), arg.getPageNumber(), arg.getPageSize(), arg.getIsCompleted(), arg.getTaskName());
        PageResult<TaskOutline> pageResult = taskService.getHandleTaskList(serviceManager, arg.getIsCompleted(), arg.getTaskName(), arg.createPage());
        I18NParser.parse(serviceManager.getTenantId(),pageResult.getDataList());
        return new GetHandleTaskList.Result(pageResult.getTotal(), pageResult.getDataList());
    }


    /**
     * 获取流程定义下所有任务
     */
    @Override
    public PageResult<LaneBriefTaskVO> getHandleTaskListByLane(GetHandleTaskListByLane.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String sourceWorkflowId = arg.getSourceWorkflowId();
        log.debug("GetHandleTaskListByLane start , sourceWorkflowId={}", sourceWorkflowId);
        List<Comparation> comparations = Lists.newArrayList();
        if (Objects.nonNull(arg.getLaneId())) {
            comparations.add(new Comparation("laneId", ComparationType.EQUAL, arg.getLaneId()));
        }
        if (Objects.nonNull(arg.getActivityId())) {
            comparations.add(new Comparation("activityId", ComparationType.EQUAL, arg.getActivityId()));
        }
        PageResult<LaneBriefTaskVO> tasksByLane = taskService.getTasksByLane(serviceManager, arg.getCircleType(), arg.getSourceWorkflowId(), comparations, arg);
        I18NParser.parse(serviceManager.getTenantId(),tasksByLane.getDataList());
        return tasksByLane;
    }

    /**
     * 执行任务
     */
    @Override
    public CompleteTask.Result completeTask(CompleteTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String taskId = arg.getTaskId();
        String objectId = arg.getObjectId();
        log.debug("completeTask start , CONTEXT={}, TASK_ID={}, OBJECT_ID={}, OPINION={}, DATA={}", serviceManager.getContext(), taskId, objectId, arg.getOpinion(), arg.getData());
        CompleteTaskResult completeTaskResult = null;
        try {
            completeTaskResult = taskService.completeTask(serviceManager, taskId, arg.getOpinion(), arg.getData(),
                    arg.getAddOrReplaceNextTaskAssignee(), arg.getNextTaskAssignee(), true,arg.getNeedGetNextTaskInfo(), arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            if(e.getErrorCode() == 301090061) {
                return CompleteTask.Result.fail(null, e.getMessage());
            }
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return CompleteTask.Result.fail(e.getMessage(), null);
            }
            //将后动作异常直接放在消息体中返回
            if (e.getErrorCode() == 301080003) {
                return new CompleteTask.Result(false, e.getMessage(), completeTaskResult);
            }
            throw e;
        }
        return new CompleteTask.Result(true, "", completeTaskResult);
    }

    @Override
    public UpdateDataAndCompleteTask.Result updateDataAndCompleteTask(UpdateDataAndCompleteTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        Task task = taskService.getPaaSTask(serviceManager, arg.getTaskId(),true);
        Map<String, Object> bpmExtension = task.getBpmExtension();
        List<List<Map<String, Object>>> form = MapUtil.instance.getList(bpmExtension, BPMConstants.FORM);
        Map<String, Object> purgedData = CompleteTaskFormValidateManager.getLegalUpdateFields(arg.getData(), form, serviceManager, arg.getEntityId(), arg.getRemoveVersion());

        String dataString = JacksonUtil.toJson(purgedData);

        Map metaData = null;
        if(!Boolean.TRUE.equals(arg.getIgnoreNoBlockValidate())) {
            TraceContext.get().setSourceProcessId(task.getSourceWorkflowId());
            UpdateData.UpdateDataResultDetail updateDataResultDetail = serviceManager.updateDataCheckConflicts(serviceManager.getContext(), arg.getEntityId(), arg.getObjectId(), dataString, false, true, "workflow_bpm");
            if(Boolean.TRUE.equals(updateDataResultDetail.getVersionCheckBlocked())){
                return new UpdateDataAndCompleteTask.Result(updateDataResultDetail.getDataConflicts());
            }
            metaData = updateDataResultDetail.getObject_data();
        }


        Map<String, Object> data = arg.getData();
        //如果任务是审批任务修改下opinion和data
        if(ExecutionTypeEnum.approve.equals(task.getExecutionType())){
            data = Maps.newHashMap();
            data.put(BPMConstants.ApproveResult.RESULT, arg.getResult());
        }
        CompleteTaskResult completeTaskResult;
        try {
            completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(),
                    arg.getOpinion(),
                    data,
                    arg.getAddOrReplaceNextTaskAssignee(),
                    arg.getNextTaskAssignee(),
                    false,arg.getNeedGetNextTaskInfo(), arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            if(MapUtils.isEmpty(metaData)){
                metaData = serviceManager.findDataById(arg.getEntityId(), arg.getObjectId());
            }
            if(e.getErrorCode() == 301090061) {
                return new UpdateDataAndCompleteTask.Result(metaData, e.getMessage());
            }
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return new UpdateDataAndCompleteTask.Result(e.getMessage(), metaData);
            }
            return new UpdateDataAndCompleteTask.Result(e.getMessage(), metaData);
        }
        if(Objects.nonNull(completeTaskResult) && Objects.nonNull(completeTaskResult.getRuleMessage()) && MapUtils.isEmpty(metaData)){
            metaData = serviceManager.findDataById(arg.getEntityId(), arg.getObjectId());
        }
        return new UpdateDataAndCompleteTask.Result(metaData, completeTaskResult);
    }

    @Deprecated
    @Override
    public GetTask.Result getTask(GetTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String taskId = arg.getTaskId();
        log.info("getTask start , TASK_ID={}", taskId);
        BPMTask task = taskService.getTask(serviceManager, arg.getTaskId());
        I18NParser.parse(serviceManager.getTenantId(),task);
        return new GetTask.Result(task, serviceManager.getEmployeeInfo(task.getPersons()));
    }

    @Override
    public GetUncompletedTasksByObject.Result getUncompletedTasksByObject(GetUncompletedTasksByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        StopWatch stopWatch = StopWatch.createUnStarted("getUncompletedTasksByObject");
        log.info("getUncompletedTasksByObject start : ENTITY_ID={}, OBJECT_ID={}", arg.getApiName(), arg.getObjectId());
        List<TaskOutline> outlines = taskService.getUncompletedTasksByObject(serviceManager, arg.getObjectId());
        stopWatch.lap("getUncompletedTaskOutlines");
        //如果获取到的任务为空,则直接返回,不需要添加操作按钮
        if (CollectionUtils.isEmpty(outlines)) {
            return new GetUncompletedTasksByObject.Result(outlines);
        }
        boolean isOwner= serviceManager.isDataOwnerByQueryMetadata(arg.getApiName(), arg.getObjectId());
        moreOperationManager.setTaskMoreOperationsWithInstanceOperation(serviceManager, outlines, isOwner);
        stopWatch.lap("setTaskMoreOperationsWithInstanceOperation");
        stopWatch.logSlow(100);
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        return new GetUncompletedTasksByObject.Result(outlines);
    }

    @Override
    public GetTasksByInstanceIds.Result getTasksByInstanceIds(GetTasksByInstanceIds.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getTasksByInstanceIds start : CONTEXT={}, INSTANCE_ID={}, ACTIVITY_INSTANCE_IDS={}", serviceManager.getContext(), arg.getWorkflowInstanceId(), arg.getActivityInstanceIds());
        List<BPMTask> tasks = taskService.getTasksByInstanceIds(serviceManager, arg.getWorkflowInstanceId(), arg.getActivityInstanceIds());
        I18NParser.parse(serviceManager.getTenantId(),tasks);
        return new GetTasksByInstanceIds.Result(tasks, serviceManager.getEmployeeInfo(BPMTask.getPersons(tasks)));
    }

    @Override
    public GetTaskLogs.Result getTaskLogs(GetTaskLogs.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getTaskLogs start : CONTEXT={}, INSTANCE_ID={}", serviceManager.getContext(), arg.getWorkflowInstanceId());
        List<TaskLog> taskLogs = taskService.getTaskLogs(serviceManager, arg.getWorkflowInstanceId());
        I18NParser.parse(serviceManager.getTenantId(),taskLogs);
        return new GetTaskLogs.Result(taskLogs);
    }

    @Override
    public ChangeTaskHandler.Result changeTaskHandler(ChangeTaskHandler.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("changeTaskHandler start : CONTEXT={}, TASK_ID={}, CANDIDATE_IDS={} , MODIFY_OPINION = {}", serviceManager.getContext(), arg.getTaskId(), arg.getCandidateIds(),arg.getModifyOpinion());
        Boolean ret = taskService.changeTaskHandlers(serviceManager, arg.getTaskId(), arg.getCandidateIds(),arg.getModifyOpinion());
        return new ChangeTaskHandler.Result(ret);
    }

    @Override
    public GetWorkflowTasks.Result getWorkflowUncompletedTasks(GetWorkflowTasks.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getWorkflowUncompletedTasks start : CONTEXT={}, SOURCE_ID={}, ID={}, TYPE={}, STATE={}", serviceManager.getContext(), arg.getSourceWorkflowId(), arg.getId(), arg.getType(), arg.getState());
        List<LaneBriefTaskVO> ret = taskService.getWorkflowUncompletedTasks(serviceManager, arg.getSourceWorkflowId(), arg.getType(), arg.getId(), arg.getState());
        return new GetWorkflowTasks.Result(ret);
    }

    @Override
    public GetWorkflowTasks.Result getAllWorkflowTasks(GetWorkflowTasks.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getAllWorkflowTasks start : CONTEXT={}, SOURCE_ID={}, ID={}, TYPE={}, STATE={}", serviceManager.getContext(), arg.getSourceWorkflowId(), arg.getId(), arg.getType(), arg.getState());

        PageResult<LaneBriefTaskVO> pageResult = taskService.getAllWorkflowTasks(serviceManager, arg.getSourceWorkflowId(), arg.getType(), arg.getId(), arg.getState(), new Page(arg.getPageSize(), arg.getPageNumber(), "", false));
        I18NParser.parse(serviceManager.getTenantId(),pageResult.getDataList());
        return new GetWorkflowTasks.Result(pageResult.getTotal(), pageResult.getDataList());
    }

    @Override
    public ClearCrmRemindOfTask.Result clearCrmRemind(ClearCrmRemindOfTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        boolean result = crmRemindServiceProxy.clearTaskRemind(serviceManager, arg.getRemindCount());
        return new ClearCrmRemindOfTask.Result(result);
    }

    @Override
    public AfterActionRetry.Result afterActionRetry(AfterActionRetry.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        AfterRetry.RetryResult result = taskService.afterActionRetry(serviceManager, arg.getTaskId(), arg.getRowNum(), arg.getExecuteType());
        return new AfterActionRetry.Result(result);
    }

    @Override
    public GetTaskInfo.Result getTaskInfo(GetTaskInfo.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        TaskDetail task = taskService.getBpmTaskDetail(serviceManager, arg.getId(), null, null, null, TaskParams.create().isTaskDetail(true)
                .applyButtons(arg.getApplyButtons())
                .includeTaskFeedDetailConfig(arg.getIncludeTaskFeedDetailConfig())
                .isTaskDetail(true));
        I18NParser.parse(serviceManager.getTenantId(),task);
        return new GetTaskInfo.Result(serviceManager, task);
    }

    @Override
    public GetTaskInfoByLaneId.Result getTaskInfoByLaneId(GetTaskInfoByLaneId.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        List<LaneTask> tasksByLaneId = taskService.getTasksByLaneId(serviceManager, arg.getLaneId(), arg.getInstanceId(), arg.getEntityId(), arg.getObjectId(),arg.getApplyButtons(), arg.getNotGetDatas());
        I18NParser.parse(serviceManager.getTenantId(),tasksByLaneId);
        GetTaskInfoByLaneId.Result result = new GetTaskInfoByLaneId.Result(serviceManager, tasksByLaneId);
        result.mergeTaskDataAndDesc(serviceManager.getTenantId());
        return result;
    }

    @Override
    public CreateTaskData.Result createTaskData(CreateTaskData.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        Boolean result = taskService.createTaskData(serviceManager,
                arg.getTaskId(),
                arg.getData(),
                arg.getActivityId(),
                arg.getActivityInstanceId(),
                arg.getExecutionType());
        return new CreateTaskData.Result(result);
    }

    @Override
    public CompleteTask.Result completeAndCreateTaskData(CompleteAndCreateTaskData.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        //创建任务数据
        if(!Boolean.TRUE.equals(arg.getIgnoreNoBlockValidate())) {
            taskService.createTaskData(serviceManager,
                    arg.getTaskId(),
                    arg.getData(),
                    arg.getActivityId(),
                    arg.getActivityInstanceId(),
                    arg.getExecutionType());
        }

        //完成任务
        String taskId = arg.getTaskId();
        String objectId = arg.getObjectId();
        log.debug("completeAndCreateTaskData start , CONTEXT={}, TASK_ID={}, OBJECT_ID={}, OPINION={}, DATA={}", serviceManager.getContext(), taskId, objectId, arg.getOpinion(), arg.getData());
        CompleteTaskResult completeTaskResult = null;
        try {
            completeTaskResult = taskService.completeTask(serviceManager, taskId, arg.getOpinion(), arg.getData(),
                    arg.getAddOrReplaceNextTaskAssignee(), arg.getNextTaskAssignee(), true,arg.getNeedGetNextTaskInfo(), arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            if(e.getErrorCode() == 301090061) {
                return CompleteTask.Result.fail(null, e.getMessage());
            }
            // 捕获函数阻断异常
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return CompleteTask.Result.fail(e.getMessage(), null);
            }
            //将后动作异常直接放在消息体中返回
            if (e.getErrorCode() == 301080003) {
                return new CompleteTask.Result(false, e.getMessage(), completeTaskResult);
            }
            throw e;
        }
        return new CompleteTask.Result(true, "", completeTaskResult);
    }

    @Override
    public GetButtonByTaskIds.Result getButtonByTaskIds(GetButtonByTaskIds.Arg arg) {
        validateArg(arg);

        Set<String> taskIds = arg.getTaskIds();
        if (CollectionUtils.isEmpty(taskIds)) {
            return new GetButtonByTaskIds.Result();
        }
        RefServiceManager serviceManager = getManager();
        TaskButtonList taskButtonList = taskService.getButtonByTaskIds(serviceManager, taskIds, TaskParams.create().applyButtons(true));
        return new GetButtonByTaskIds.Result(taskButtonList);
    }

    @Override
    public RefreshHandlerByTaskId.Result refreshHandlerByTaskId(RefreshHandlerByTaskId.Arg arg){
        validateArg(arg);
        taskService.refreshHandlerByTaskId(getManager(), arg.getTaskId());
        return new RefreshHandlerByTaskId.Result();

    }

    @Override
    public Edit.Result edit(Edit.Arg arg){
        validateArg(arg);
        Edit.Result result = new Edit.Result();
        Map editRes = taskService.edit(getManager(), arg.getTaskId(), arg.getObjectData(), arg.getDetails(), arg.getOptionInfo(), arg.getNotValidate(), arg.getOriginalData(), arg.getOriginalDetails());
        if(MapUtils.isNotEmpty(editRes)){
            result.putAll(editRes);
        }
        return result;
    }

    @Override
    public OperateTask.Result operateTask(OperateTask.Arg arg){
        validateArg(arg);
        taskService.operateTask(getManager(), arg.getTaskId(), arg.getType(), arg.getOpinion(), arg.getTagInfo());
        return new OperateTask.Result();
    }

    @Override
    public RemindTask.Result remindTask(RemindTask.Arg arg){
        validateArg(arg);
        taskService.remindTask(getManager(), arg.getTaskId(), arg.getContent(), arg.getRemindPersons());
        return new RemindTask.Result();
    }

}
