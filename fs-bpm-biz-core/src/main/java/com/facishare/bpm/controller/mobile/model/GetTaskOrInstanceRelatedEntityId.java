package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

public interface GetTaskOrInstanceRelatedEntityId {
    @Data
    class Arg {

        @DocDescribe(label = "实例id",desc = "实例id及任务id而且其一,如果都为空,则会提示异常")
        @JSONField(name = "M1")
        private String instanceId;

        @DocDescribe(label = "任务id",desc = "如果任务id和实例id同时传递,则优先查询任务关联的对象apiname")
        @JSONField(name = "M2")
        private String taskId;

    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private String entityId;

        public Result(String entityId) {
            this.entityId = entityId;
        }
    }
}
