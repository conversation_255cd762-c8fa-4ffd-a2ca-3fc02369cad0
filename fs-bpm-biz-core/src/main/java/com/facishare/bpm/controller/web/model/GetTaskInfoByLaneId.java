package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by wangzhx on 2019/4/18.
 */
public interface GetTaskInfoByLaneId {

    @Data
    class Arg {
        @Descriable(value = "阶段Id", required = true)
        @NotBlank(message = "获取阶段下的任务 阶段Id不能空")
        private String laneId;
        @Descriable(value = "流程Id", required = true)
        @NotBlank(message = "获取阶段下的任务 流程Id不能空")
        private String workflowId;
        @Descriable(value = "实例Id", required = true)
        @NotBlank(message = "获取阶段下的任务 实例Id不能空")
        private String instanceId;
        //@Descriable(value = "数据Id", required = true)
        //@NotBlank(message = "获取阶段下的任务 数据Id不能空")
        private String objectId;
        //@Descriable(value = "对象Id", required = true)
        //@NotBlank(message = "获取阶段下的任务 对象Id不能空")
        private String entityId;
        //不需要下发数据、描述、描述扩展及form的标识位 true-不获取；false或null 获取
        private Boolean notGetDatas;

        /**
         * 780 新增,应用节点下发多个按钮
         */
        private Boolean applyButtons;
        /**
         * 960新增，技术方案：https://wiki.firstshare.cn/pages/viewpage.action?pageId=591593567
         */
        private int page = 1;
        private int pageSize = 15;

        public Boolean getApplyButtons() {
            return applyButtons != null && applyButtons;
        }
    }

    @Data
    class Result {
        private List<LaneTaskInfo> result;
        private Map<String, Employee> employeeInfo;
        private Map<String,Map<String,Object>> dataMap;
        private Map<String,Map<String,Object>> descMap;
        private Map<String,Map<String,Object>> descExtMap;
        private Boolean hasMore;

        public Result(RefServiceManager refServiceManager, List<LaneTask> result) {
            this.result = result.stream().map(k -> BeanUtils.transfer(k, LaneTaskInfo.class, (src, rst) -> {
                rst.setCandidateIds(src.getCandidateIds());
                rst.setProcessIds(src.getProcessIds());
                rst.setButton(src.getButton());
                rst.setNextTaskAssigneeScope(src.getNextTaskAssigneeScope());
                rst.setAssignNextTask(src.getAssignNextTask());
                rst.setIsTaskOwner(src.getIsTaskOwner());
                //如果是应用节点，将linkAppName隐藏
                if(src.isExternalApplyTask()) {
                    rst.setLinkAppName(null);
                } else {
                    rst.setLinkAppName(refServiceManager.getI18nLinkAppName(src.getLinkApp(), src.getLinkAppName()));
                }
                if (Objects.isNull(src.getData())) {
                    return;
                }
                rst.setOpinions(src.getOpinions());
                rst.setCandidateModifyLog(src.getCandidateModifyLog());
                rst.setRemindLogs(src.getRemindLogs());
            })).collect(Collectors.toList());
            this.employeeInfo = refServiceManager.getEmployeeInfo(LaneTask.getPersons(result));
        }

        /**
         * 2022.7.4
         * <AUTHOR> ,gaojun
         * 锐捷 当前阶段下面任务有 7个， 字段有1000+，导致当前请求下发数据达 18M
         * 这里进行描述和数据的合并， 以减少数据的传输
         */
        public void mergeTaskDataAndDesc(String tenantId) {
            if (SwitchConfigManager.isMergeTasksByLaneDataAndDesc(tenantId).gray()) {
                dataMap = Maps.newHashMap();
                descMap = Maps.newHashMap();
                descExtMap = Maps.newHashMap();
                this.getResult().forEach(item -> {
                    StandardData data = item.getData();
                    if (Objects.nonNull(data)){
                        if (Objects.nonNull(data.getData())) {
                            dataMap.put(item.getObjectId(), data.getData());
                            data.setData(null);
                        }
                        if (Objects.nonNull(data.getDescribe())) {
                            descMap.put(item.getEntityId(), data.getDescribe());
                            data.setDescribe(null);
                        }
                        if (Objects.nonNull(data.getDescribeExt())) {
                            descExtMap.put(item.getEntityId(), data.getDescribeExt());
                            data.setDescribeExt(null);
                        }
                    }
                });
            }
        }

        public Result() {
        }


    }

    @Data
    class LaneTaskInfo {
        private String id;
        private String taskName;
        private TaskState state;
        private List<String> candidateIds;
        private List<String> processIds;
        private String taskType;
        private ExecutionTypeEnum executionType;
        private Map<String, ActionButton> button;
        private StandardData data;
        private String errorMsg;
        private Long createTime;
        private Long modifyTime;
        private AfterActionExecution.SimpleAfterActionExecution execution;

        private List<Opinion> opinions;
        private List<Task.ApproverModifyLog> candidateModifyLog;
        private String entityId;
        private String objectId;
        private String linkAppName;
        private Integer linkAppType;
        private String linkApp;
        private Boolean linkAppEnable;
        private List<MoreOperationManager.MoreOperation> moreOperations;
        private String workflowInstanceId;
        private int activityInstanceId;
        private Map<String, Object> externalApply;
        private int assignNextTask;
        private Set<String> nextTaskAssigneeScope;
        private String todoJumpUrl;
        private Boolean isTaskOwner;
        private Boolean hasForm;
        private String message;
        private Map customExtension;
        private FlowElementPluginWrapper elementPluginConfig;
        private String nodeType;
        private List<Task.RemindLog> remindLogs;

        public void setCandidateIds(List<String> candidateIds) {
            this.candidateIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(candidateIds)) {
                return;
            }
            for (String candidateId : candidateIds) {
                if (!this.candidateIds.contains(candidateId)) {
                    this.candidateIds.add(candidateId);
                }
            }
        }
    }

}
