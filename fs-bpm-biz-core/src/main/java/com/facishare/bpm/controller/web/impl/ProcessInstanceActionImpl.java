package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.ProcessInstanceAction;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMInstanceException;
import com.facishare.bpm.model.WorkflowInstanceLog;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.WorkflowStats;
import com.facishare.bpm.model.instance.*;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.model.Pair;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 流程实例接口实现 Created by cuiyongxu on 16/12/26.
 */
@Slf4j
@Service
public class ProcessInstanceActionImpl extends BPMBaseAction implements ProcessInstanceAction {

    @Autowired
    private BPMInstanceService instanceService;

    /**
     * 取消流程实例
     */
    @Override
    public CancelInstance.Result cancelInstance(CancelInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        String workflowInstanceId = arg.getId();
        log.debug("cancelInstance start : workflowInstanceId={}", workflowInstanceId);
        if (Strings.isNullOrEmpty(workflowInstanceId)) {
            log.error("workflowInstanceId is null.");
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_ID_NOT_FOUND);
        }
        instanceService.cancelWorkflowInstance(serviceManager, workflowInstanceId, arg.getReason());
        return new CancelInstance.Result();
    }

    /**
     * 启动流程实例
     */
    @Override
    public StartInstance.Result startInstance(StartInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String id = arg.getId();
        String objectId = arg.getObjectId();
        log.debug("StartInstance start : CONTEXT={}, OUTLINE_ID={}, OBJECT_ID={}", serviceManager.getContext(), id, objectId);

        return new StartInstance.Result(instanceService.startWorkflow(serviceManager, TriggerSource.person, id, objectId));
    }

    /**
     * 获取某个流程的实例列表
     */
    @Override
    public PageResult<WorkflowInstanceVO> getInstanceList(GetInstanceList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getInstanceListByWorkflowSourceId: sourceWorkflowId={}", arg.getSourceWorkflowId());
        PageResult<WorkflowInstanceVO> workflowInstances = instanceService.getWorkflowInstances(
                serviceManager,
                arg.getState(),
                null,
                arg.getPageSize(),
                arg.getPageNumber(),
                arg.getOrderBy(),
                arg.getSourceWorkflowId()
        );
        I18NParser.parse(serviceManager.getTenantId(),workflowInstances.getDataList());
        return workflowInstances;
    }

    @Override
    public GetInstanceListByObject.Result getInstanceListByObject(GetInstanceListByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getInstanceListByWorkflowSourceId : OBJECT_ID={}, STATE={}", arg.getObjectId(), arg.getState());
        PageResult<WorkflowInstanceVO> workflowInstances = instanceService.getWorkflowInstances(
                serviceManager,
                arg.getState(),
                arg.getObjectId(),
                arg.getPageSize(),
                arg.getPageNumber(),
                arg.getOrderBy(),
                null
        );
        List<WorkflowInstanceVO> dataList = workflowInstances.getDataList();
        I18NParser.parse(serviceManager.getTenantId(),dataList);

        return new GetInstanceListByObject.Result(workflowInstances.getTotal(),dataList,
                serviceManager.getEmployeeInfo(WorkflowInstanceVO.getPersons(workflowInstances.getDataList())));
    }

    @Override
    public GetEntireWorkflowInstance.Result getNewEntireWorkflowInstance(GetEntireWorkflowInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        return new GetEntireWorkflowInstance.Result(
                instanceService.getEntireWorkflowInstance(
                        serviceManager,
                        arg.getInstanceId()
                ));
    }

    @Override
    public EntireWorkflowInstance getEntireWorkflowInstance(GetEntireWorkflowInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        EntireWorkflow workflow = instanceService.getEntireWorkflowInstance(serviceManager, arg.getInstanceId());
        EntireWorkflowInstance instance = EntireWorkflowInstance.create(serviceManager.getTenantId(),workflow);
        I18NParser.parse(serviceManager.getTenantId(),instance);
        instance.setMoreOperations(workflow.getMoreOperations());
        instance.setEmployeeInfo(workflow.getEmployeeInfo());
        return instance;
    }

    @Override
    public GetWorkflowStatsData.Result getWorkflowStatsData(GetWorkflowStatsData.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getWorkflowStatsData : CONTEXT={}, SOURCE_WORKFLOW_ID={}", serviceManager.getContext(), arg.getSourceWorkflowId());
        Pair<Pair<WorkflowOutline, String>, List<WorkflowStats>> ret = instanceService.getWorkflowStatsDataByPG(serviceManager, arg.getSourceWorkflowId());
        return new GetWorkflowStatsData.Result(ret.getKey().getValue(), ret.getValue(), ret.getKey().getKey());
    }

    @Override
    public GetWorkflowInstanceLog.Result getWorkflowInstanceLog(GetWorkflowInstanceLog.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        WorkflowInstanceLog workflowInstanceLog = instanceService.getWorkflowInstanceLog(serviceManager, arg.getWorkflowInstanceId());
        I18NParser.parse(serviceManager.getTenantId(),workflowInstanceLog);
        return BeanUtils.transfer(workflowInstanceLog, GetWorkflowInstanceLog.Result.class);
    }

    @Override
    public TriggerInstance.Result triggerInstance(TriggerInstance.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String id = arg.getId();
        String objectId = arg.getObjectId();
        log.debug("StartInstance start : CONTEXT={}, OUTLINE_ID={}, OBJECT_ID={}", serviceManager.getContext(), id, objectId);
        TriggerWorkflow triggerWorkflow = instanceService.triggerWorkflow(serviceManager, TriggerSource.person,null, id, objectId);
        if (Objects.isNull(triggerWorkflow)) {
            return new TriggerInstance.Result(StringUtils.EMPTY, null);
        }
        return new TriggerInstance.Result(triggerWorkflow.getResult(), triggerWorkflow.getRuleMessage());
    }

    @Override
    public GetInstancesByObject.Result getInstancesByObject(GetInstancesByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        List<GetInstanceByObject> instances = instanceService.getInstancesByObject(serviceManager, arg.getEntityId(), arg.getObjectId());
        I18NParser.parse(serviceManager.getTenantId(),instances);
        return new GetInstancesByObject.Result(instances);
    }

    @Override
    public InstanceAfterActionRetry.Result afterActionRetry(InstanceAfterActionRetry.Arg arg) {
        validateArg(arg);
        return new InstanceAfterActionRetry.Result(instanceService.afterActionRetry(getManager(),arg.getInstanceId(),arg.getRowNum(),arg.getExecuteType()));
    }
}
