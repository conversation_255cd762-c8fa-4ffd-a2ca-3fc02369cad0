package com.facishare.bpm.controller.web.model;

import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

public interface RefreshHandlerByTaskId {

    @Data
    class Arg{
        @DocDescribe(label = "任务id", required = true)
        @NotEmpty(message = "任务id不能为空")
        private String taskId;
    }

    @Data
    class Result {

    }
}
