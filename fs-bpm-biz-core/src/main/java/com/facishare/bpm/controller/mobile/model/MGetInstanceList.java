package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.CircleType;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface MGetInstanceList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private int pageSize = 10;
        @JSONField(name = "M2")
        private int pageNumber = 1;
        @JSONField(name = "M3")
        private String orderBy;
        @JSONField(name = "M4")
        private boolean asc;
        @JSONField(name = "M5")
        private String sourceWorkflowId;
        @JSONField(name = "M6")
        private CircleType circleType = CircleType.ALL;
        @JSONField(name = "M7")
        private InstanceState state;
        @J<PERSON>NField(name = "M8")
        private String workflowName;
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private int total;
        @J<PERSON>NField(name = "M2")
        private List<MWorkflowInstance> dataList;
        public Result(int total,List<WorkflowInstanceVO> dataList){
            this.total=total;
            this.dataList=dataList.stream().map(item->MWorkflowInstance.fromWorkflowInstance(item)).collect(Collectors.toList());
        }
    }
}
