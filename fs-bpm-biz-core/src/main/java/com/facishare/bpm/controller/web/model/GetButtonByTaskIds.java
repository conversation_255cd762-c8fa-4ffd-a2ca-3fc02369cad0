package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.controller.mobile.model.MGetButtonByTaskIds;
import com.facishare.bpm.model.task.TaskButtonList;
import lombok.Data;
import lombok.ToString;

/**
 * desc:
 * author: cuiyongxu
 * create_time: 2021/9/23-4:47 PM
 **/
public interface GetButtonByTaskIds {

    @Data
    @ToString(callSuper = true)
    class Arg extends MGetButtonByTaskIds.Arg {
    }

    @Data
    @ToString(callSuper = true)
    class Result extends MGetButtonByTaskIds.Result {
        public Result(TaskButtonList taskButtonList) {
            super.buttons = taskButtonList.getButtons();
            super.customElementDataMap=taskButtonList.getCustomElementDataMap();
            super.errorMsgs = taskButtonList.getErrorMsgs();
            super.moreOperations = taskButtonList.getMoreOperations();
        }

        public Result() {
        }
    }

}
