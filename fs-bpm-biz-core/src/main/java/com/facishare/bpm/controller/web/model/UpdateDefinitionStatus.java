package com.facishare.bpm.controller.web.model;


import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

public interface UpdateDefinitionStatus {
    @Data
    class Arg {
        @NotEmpty(message = "ids不能为空")
        String ids;//单个id

        boolean enabled; //状态
    }

    @Data
    class Result {
        boolean result = true;

        public Result(boolean result) {
            this.result = result;
        }
    }
}
