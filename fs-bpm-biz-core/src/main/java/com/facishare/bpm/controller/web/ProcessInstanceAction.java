package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.model.instance.EntireWorkflowInstance;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 流程实例接口
 * Created by cuiyongxu on 16/12/21.
 */
@Path("BPM/ProcessInstance")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface ProcessInstanceAction {

    /**
     * 停止流程实例
     * @param arg
     * @return
     */
    @Path("CancelInstance")
    @POST
    CancelInstance.Result cancelInstance(CancelInstance.Arg arg);


    /**
     * 启动流程实例
     * @param arg
     * @return
     */
    @Path("StartInstance")
    @POST
    StartInstance.Result startInstance(StartInstance.Arg arg);



    /**
     * 启动流程实例
     *
     * @param arg
     * @return
     */
    @Path("TriggerInstance")
    @POST
    TriggerInstance.Result triggerInstance(TriggerInstance.Arg arg);

    /**
     * 获取某个流程的实例
     * @param arg
     * @return
     */
    @Path("GetInstanceList")
    @POST
    PageResult<WorkflowInstanceVO> getInstanceList(GetInstanceList.Arg arg);


    /**
     * 获取某个对象下的已完成流程实例列表（包含已完成和已取消）
     * @param arg
     * @return
     */
    @Path("GetInstanceListByObject")
    @POST
    GetInstanceListByObject.Result getInstanceListByObject(GetInstanceListByObject.Arg arg);

    /**
     * 获取完整流程数据
     * @param arg
     * @return
     */
    @Path("GetNewEntireWorkflowInstance")
    @POST
    GetEntireWorkflowInstance.Result getNewEntireWorkflowInstance(GetEntireWorkflowInstance.Arg arg);

    /**
     * 获取完整流程数据
     * @param arg
     * @return
     */
    @Path("GetEntireWorkflowInstance")
    @POST
    EntireWorkflowInstance getEntireWorkflowInstance(GetEntireWorkflowInstance.Arg arg);

    /**
     * 阶段视图，当前流程正在运行的任务统计
     * @param arg
     * @return
     */
    @Path("GetWorkflowStatsData")
    @POST
    GetWorkflowStatsData.Result getWorkflowStatsData(GetWorkflowStatsData.Arg arg);

    @Path("GetWorkflowInstanceLog")
    @POST
    GetWorkflowInstanceLog.Result getWorkflowInstanceLog(GetWorkflowInstanceLog.Arg arg);

    @Path("GetInstancesByObject")
    @POST
    GetInstancesByObject.Result getInstancesByObject(GetInstancesByObject.Arg arg);

    @Path("AfterActionRetry")
    @POST
    InstanceAfterActionRetry.Result afterActionRetry(InstanceAfterActionRetry.Arg arg);
}
