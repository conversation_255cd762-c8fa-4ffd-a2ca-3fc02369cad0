package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.controller.mobile.model.MWorkflowInstance;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.remote.model.org.Employee;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Created by wangz on 17-2-23.
 */
public interface GetInstanceListByObject {
    @Data
    class Arg extends Page {
        @NotEmpty(message = "记录objectId不能为空")
        private String objectId;
        private InstanceState state;
    }

    @Data
    class Result {
        private int total;
        private List<MWorkflowInstance> dataList;
        private Map<String, Employee> employeeInfo;

        public Result(int total, List<WorkflowInstanceVO> dataList, Map<String, Employee> employeeInfo) {
            this.total = total;
            this.dataList = dataList.stream().map(item -> MWorkflowInstance.fromWorkflowInstance(item)).collect(Collectors.toList());
            this.employeeInfo = employeeInfo;
        }
    }
}
