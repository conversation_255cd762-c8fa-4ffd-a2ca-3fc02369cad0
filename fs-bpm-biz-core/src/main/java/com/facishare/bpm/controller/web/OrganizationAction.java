package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.GetOrganization;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by cuiyongxu on 17/5/4.
 */
@Path("BPM/BPMOrganization")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface OrganizationAction {

    @Path("GetOrganization")
    @POST
    GetOrganization.Result getOrganization(GetOrganization.Arg arg);
}
