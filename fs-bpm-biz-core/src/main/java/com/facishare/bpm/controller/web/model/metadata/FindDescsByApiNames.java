package com.facishare.bpm.controller.web.model.metadata;

import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-1-10.
 */
public interface FindDescsByApiNames {
    @Data
    class Arg {
        @NotEmpty(message = "参数不能为空")
        private List<String> apiNames = Lists.newArrayList();
        private boolean includeLookups = false;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<SimpleMetadataDesc> customObjects;
    }
}
