package com.facishare.bpm.controller.web.model.metadata;

import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by wangz on 17-1-10.
 */
public interface FindCustomObjs {
    @Data
    class Arg {
        @Deprecated
        private String packageName; //元数据 业务路由用

        private boolean includeFieldsDesc = true; //是否包含字段描述
        private List<String> apiNames = Lists.newArrayList();

        private Boolean useGroupManager;

        public Boolean getUseGroupManager() {
            return Boolean.TRUE.equals(useGroupManager);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<SimpleMetadataDesc> customObjects;
    }
}
