package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.web.model.metadata.GetDataOwner;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by wangz on 17-1-4.
 */
@Path("BPM/MMetadata")
@Produces({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@Consumes({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface MMetadataAction {

    /**
     * 查询数据负责人
     *
     * @param arg
     * @return
     */
    @Path("GetDataOwner")
    @POST
    GetDataOwner.Result getDataOwner(GetDataOwner.Arg arg);
}
