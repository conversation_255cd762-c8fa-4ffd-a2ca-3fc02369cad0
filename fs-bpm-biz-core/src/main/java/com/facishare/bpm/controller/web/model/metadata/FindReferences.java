package com.facishare.bpm.controller.web.model.metadata;

import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-1-13.
 */
public interface FindReferences {
    @Data
    class Arg{
        @NotEmpty(message = "apiName不能为空")
        private String apiName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        List<SimpleMetadataDesc> customObjects;
    }
}
