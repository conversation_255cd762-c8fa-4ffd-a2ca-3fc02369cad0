package com.facishare.bpm.controller.web.model.metadata;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

public interface GetBpmTaskBusinessCodeFilterCriteria {

    @Data
    class Result{
        Map<String, List<String>> filterCriteria;

        public Result(Map<String, List<String>> res){
            this.filterCriteria = MapUtils.isEmpty(res) ? Maps.newHashMap() : res;
        }
    }
}
