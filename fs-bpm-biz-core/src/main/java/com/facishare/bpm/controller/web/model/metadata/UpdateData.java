package com.facishare.bpm.controller.web.model.metadata;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.DataConflicts;
import com.facishare.rest.core.util.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Created by wangz on 17-1-4.
 */
public interface UpdateData {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @NotEmpty(message = "apiName不能为空")
        String apiName;
        @JSONField(name = "M2")
        @NotEmpty(message = "记录id不能为空")
        String dataId;
        @JSONField(name = "M3")
        @NotNull(message = "数据内容不能为空")
        Object jsonData;

        @Descriable(value = "是否走校验规则,0:不校验;1:校验")
        @JSONField(name = "M4")
        Integer validationRule = 1;

        @JSONField(name = "M5")
        boolean ignoreNonBlocking;

        @Descriable(value = "任务id", required = true)
        @NotEmpty(message = "任务id不能为空")
        @JSONField(name = "M6")
        String taskId;

        @Descriable(value = "是否忽略版本更新")
        @JSONField(name = "M7")
        Boolean removeVersion;

        public String getDataString() {
            if (jsonData instanceof String) {
                return (String) jsonData;
            } else {
                return JacksonUtil.toJson(jsonData);
            }
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @JSONField(name = "M1")
        Map result;
        @JSONField(name = "M2")
        Boolean versionCheckBlocked;
        @JSONField(name = "M3")
        DataConflicts dataConflicts;

        public Result(Map data) {
            this.result = data;
        }
        public Result(DataConflicts dataConflicts) {
            this.versionCheckBlocked = true;
            this.dataConflicts = dataConflicts;
        }
    }
}
