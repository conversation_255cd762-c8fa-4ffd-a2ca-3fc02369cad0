package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.*;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

@Path("BPM/ProcessDraft")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface ProcessDraftAction {

    /**
     * 流程保存草稿
     */
    @Path("CreateDraft")
    @POST
    CreateDraft.Result createDraft(CreateDraft.Arg arg);

    /**
     * 流程更新草稿
     */
    @Path("UpdateDraft")
    @POST
    UpdateDraft.Result updateDraft(UpdateDraft.Arg arg);

    /**
     * 查询特定流程的草稿
     */
    @Path("GetDraft")
    @POST
    GetDraft.Result getDraftById(GetDraft.Arg arg);

    /**
     * 查询所有草稿
     */
    @Path("GetDraftList")
    @POST
    GetDraftList.Result getDraftList(GetDraftList.Arg arg);

    /**
     * 删除流程定义
     */
    @Path("DeleteDraft")
    @POST
    DeleteDefinition.Result deleteDraft(DeleteDefinition.Arg arg);
}
