package com.facishare.bpm.controller.mobile.model;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/6/4 5:05 PM
 */
public interface MGetTaskInfo {
    @Data
    class Arg {
//        @JSONField(name = "M1")
        private String id;

        /**
         * 为终端适配 instanceId,activityInstanceId
         */
        @Descriable(value = "实例id")
        private String instanceId;
        @Descriable(value = "节点实例id")
        private String activityInstanceId;
        @Descriable(value = "节点id")
        private String activityId;
        /**
         * 780 新增,应用节点下发多个按钮
         */
        private Boolean applyButtons;

        /**
         * 820 H5需要下发对象数据，编辑从对象
         */
        private Boolean isH5;
        /**
         * 是否包括流程Feed相关配置
         */
        private Boolean includeTaskFeedDetailConfig;

        public Boolean getApplyButtons() {
            return applyButtons != null && applyButtons;
        }


    }

    @Data
    class Result {
        private String id;
        private String name;
        private String description;
        private String laneName;
        private String laneId;
        private TaskState state;
        private String applicantId;
        private String workflowId;
        private String workflowInstanceId;
        private String entityId;
        private String objectId;
        private String objectName;
        private List<String> candidateIds;
        private List<String> processIds;
        @DocDescribe(desc = "1-天；2-小时；3-分钟")
        private int latencyUnit;
        private Object remindLatency;
        private boolean isTimeout;
        private ExecutionTypeEnum executionType;
        private String taskType;
        private AfterActionExecution.SimpleAfterActionExecution execution;
        private String errorMsg;
        @DocDescribe(desc = "是否需要指定下一节点处理人1 指定，非1 不指定")
        private int assignNextTask;
        private Set<String> nextTaskAssigneeScope;
        /**
         * addteammember:[
         * actionCode:addteammember
         * actionlable:添加团队成员
         * actionRight:true/false
         * ]
         */
        private Map<String, ActionButton> button;
        private StandardData data;
        private List<MoreOperationManager.MoreOperation> moreOperations;

        private Long createTime;

        private Long modifyTime;
        private String activityId;
        private int activityInstanceId;
        private List<Opinion> opinions;
        private List<Task.ApproverModifyLog> candidateModifyLog;
        private String linkAppName;
        private Map<String, Employee> employeeInfo;
        private boolean isOwner;

        private Integer linkAppType;
        private String linkApp;
        private Boolean linkAppEnable;
        private String todoJumpUrl;
        /**
         * 840 增加 获取 落地页类似的配置信息
         */
        private Map<String,Object> flowConfig;
        private List<PoolEntity> pools;
        private String message;
        private Long suspendAccumulateTime;
        private Long lastSuspendTime;
        public Map customExtension;
        public FlowElementPluginWrapper elementPluginConfig;
        public String nodeType;
        public List<Task.RemindLog> remindLogs;
        public void setCandidateIds(List<String> candidateIds) {
            this.candidateIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(candidateIds)) {
                return;
            }
            for (String candidateId : candidateIds) {
                if (!this.candidateIds.contains(candidateId)) {
                    this.candidateIds.add(candidateId);
                }
            }
        }



        public Result(RefServiceManager refServiceManager, TaskDetail taskDetail, Map<String, Object> flowConfig) {
            this.id = taskDetail.getId();
            this.name = taskDetail.getName();
            this.description = taskDetail.getDescription();
            this.laneName = taskDetail.getLaneName();
            this.state = taskDetail.getState();
            this.applicantId = taskDetail.getApplicantId();
            this.workflowId = taskDetail.getWorkflowId();
            this.workflowInstanceId = taskDetail.getWorkflowInstanceId();
            this.entityId = taskDetail.getEntityId();
            this.objectId = taskDetail.getObjectId();
            setCandidateIds(taskDetail.getCandidateIds());
            this.processIds = taskDetail.getProcessIds();
            this.latencyUnit = taskDetail.getLatencyUnit();
            this.remindLatency = taskDetail.getRemindLatency();
            this.isTimeout = taskDetail.isTimeout();
            this.executionType = taskDetail.getExecutionType();
            this.taskType = taskDetail.getTaskType();
            this.execution = taskDetail.getExecution();
            this.errorMsg = taskDetail.getErrorMsg();
            this.assignNextTask = taskDetail.getAssignNextTask();
            this.nextTaskAssigneeScope = taskDetail.getNextTaskAssigneeScope();
            this.button = taskDetail.getButton();
            this.moreOperations = taskDetail.getMoreOperations();

            this.createTime = taskDetail.getCreateTime();
            this.modifyTime = taskDetail.getModifyTime();
            this.activityId = taskDetail.getActivityId();
            this.laneId = taskDetail.getLaneId();
            this.activityInstanceId = taskDetail.getActivityInstanceId();
            // 审批意见和修改记录提取出来
            this.opinions = taskDetail.getOpinions();
            this.candidateModifyLog = taskDetail.getCandidateModifyLog();
            this.linkAppName = refServiceManager.getI18nLinkAppName(taskDetail.getLinkApp(), taskDetail.getLinkAppName());
            this.employeeInfo = taskDetail.getEmployeeInfo();
            this.isOwner = taskDetail.getIsOwner();
            this.linkApp = taskDetail.getLinkApp();
            this.linkAppType = taskDetail.getLinkAppType();
            this.linkAppEnable = taskDetail.getLinkAppEnable();
            this.message = taskDetail.getMessage();
            this.suspendAccumulateTime = taskDetail.getSuspendAccumulateTime();
            this.lastSuspendTime = taskDetail.getLastSuspendTime();
            this.customExtension = taskDetail.getCustomExtension();
            this.elementPluginConfig = taskDetail.getElementPluginConfig();

            this.data = taskDetail.getData();
            if(Objects.nonNull(data)){
                this.todoJumpUrl = data.getTodoJumpUrl();
                this.objectName = (String) data.getObjectName();
            }

            this.setFlowConfig(flowConfig);
            addFlowConfig(taskDetail.getConfig());
            this.pools = taskDetail.getPools();
            //如果是应用节点，将linkAppName隐藏
            if(taskDetail.isExternalApplyTask()) this.linkAppName = null;
            this.nodeType = taskDetail.getNodeType();
            this.remindLogs = taskDetail.getRemindLogs();
        }

        private void addFlowConfig(Map<String, Object> config) {
            if(MapUtils.isEmpty(flowConfig)){
                flowConfig= Maps.newHashMap();
            }
            if(MapUtils.isNotEmpty(config)){
                flowConfig.putAll(config);
            }
        }

    }

}
