package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.controller.mobile.model.MGetEntireWorkflow;
import com.facishare.bpm.model.instance.EntireWorkflow;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 *
 * <AUTHOR>
 * @date 24/02/2017
 */

public interface GetEntireWorkflowInstance {
    @Data
    class Arg{
        @NotEmpty(message = "流程实例id不能为空")
        private String instanceId;
    }
    @Data
    class Result extends MGetEntireWorkflow.Result{
        public Result(EntireWorkflow entireWorkflow) {
            super(entireWorkflow);
        }
    }
}
