package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;

/**
 * Created by <PERSON> on 17/2/23.
 */
public interface GetAllAvailableWorkflows {
    class Arg{}
    @Getter
    @Setter
    class Result extends ArrayList{
    }
    @Getter
    @Setter
    class AvailableWorkflow{
        private String workflowId;
        private String sourceWorkflowId;
        private String name;
        private String description;
        public static AvailableWorkflow fromWorkflowOutline(WorkflowOutline outline){
            AvailableWorkflow availableWorkflow=new AvailableWorkflow();
            availableWorkflow.setSourceWorkflowId(outline.getSourceWorkflowId());
            availableWorkflow.setDescription(outline.getDescription());
            availableWorkflow.setName(outline.getName());
            availableWorkflow.setWorkflowId(outline.getWorkflowId());
            return availableWorkflow;
        }
    }
}
