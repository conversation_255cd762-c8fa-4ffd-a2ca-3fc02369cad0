package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.resource.app.GetAppActions;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface GetActivityAppActions {


    @Data
    class Arg {
        private String apiName;
        private Boolean externalFlow;
        private String appId;
        private Integer appType;
    }

    @Data
    class Result {
        private List<GetAppActions.App> data;

        public Result(GetAppActions.Result appActions) {
            this.data = appActions.getData();
        }
    }
}
