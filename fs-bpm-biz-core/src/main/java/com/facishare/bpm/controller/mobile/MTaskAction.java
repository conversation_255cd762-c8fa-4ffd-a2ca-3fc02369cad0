package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.mobile.model.*;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by <PERSON> on 11/04/2017.
 */
@Path("BPM/MTask")
@Produces({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@Consumes({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface MTaskAction {
    /**
     * 1.分配给当前人的待办
     * 2.分配给当前人部门的待办
     * 3.分配给当前人组的待办
     *   多用于使用流程
     * @param arg
     * @return
     */
    @Path("GetHandleTaskList")
    @POST
    MGetHandleTaskList.Result getHandleTaskList(MGetHandleTaskList.Arg arg);


    @Path("GetTaskInfoByLaneId")
    @POST
    GetTaskInfoByLaneId.Result getTaskInfoByLaneId(GetTaskInfoByLaneId.Arg arg);

    /**
     * 任务执行
     * @param arg
     * @return
     */
    @Path("CompleteTask")
    @POST
    MCompleteTask.Result completeTask(MCompleteTask.Arg arg);

    /**
     * 更新并完成任务
     * @param arg
     * @return
     */
    @Path("UpdateDataAndCompleteTask")
    @POST
    MUpdateDataAndCompleteTask.Result updateDataAndCompleteTask(MUpdateDataAndCompleteTask.Arg arg);

    /**
     * 获取任务详情
     * @param arg
     * @return
     */
    @Path("GetTask")
    @POST
    MGetTask.Result getTask(MGetTask.Arg arg);

    /**
     * 获取当前人的某对象下待办任务列表
     * @param arg
     * @return
     */
    @Path("GetUncompletedTasksByObject")
    @POST
    MGetUncompletedTasksByObject.Result getUncompletedTasksByObject(MGetUncompletedTasksByObject.Arg arg);

    /**
     * 更改任务处理人
     * @param arg
     * @return
     */
    @Path("ChangeTaskHandler")
    @POST
    MChangeTaskHandler.Result changeTaskHandler(MChangeTaskHandler.Arg arg);

    /**
     * 清除提醒
     * @param arg
     * @return
     */
    @Path("ClearCrmRemind")
    @POST
    MClearCrmRemindOfTask.Result clearCrmRemind(MClearCrmRemindOfTask.Arg arg);


    @Path("AfterActionRetry")
    @POST
    MAfterActionRetry.Result afterActionRetry(MAfterActionRetry.Arg arg);

    @Deprecated
    @Path("GetTaskDetail")
    @POST
    MGetTaskInfo.Result getTaskDetail(MGetTaskDetail.Arg arg);


    @Path("GetTaskInfo")
    @POST
    MGetTaskInfo.Result getTaskInfo(MGetTaskInfo.Arg arg);


    @Path("CreateTaskData")
    @POST
    MCreateTaskData.Result createTaskData(MCreateTaskData.Arg arg);


    @Path("GetUncompletedTaskInfoByObject")
    @POST
    MGetUncompletedTaskInfosByObject.Result getUncompletedTaskInfoByObject(MGetUncompletedTaskInfosByObject.Arg arg);

    @Path("GetButtonByTaskId")
    @POST
    MGetButtonByTaskIds.Result getButtonByTaskIds(MGetButtonByTaskIds.Arg arg);


    @Path("CompleteAndCreateTaskData")
    @POST
    MCompleteTask.Result completeAndCreateTaskData(MCompleteAndCreateTaskData.Arg arg);


    @Path("RefreshHandlerByTaskId")
    @POST
    RefreshHandlerByTaskId.Result refreshHandlerByTaskId(RefreshHandlerByTaskId.Arg arg);

    @Path("Edit")
    @POST
    Edit.Result edit(Edit.Arg arg);

    @Path("OperateTask")
    @POST
    OperateTask.Result operateTask(OperateTask.Arg arg);

    @Path("Remind")
    @POST
    RemindTask.Result remindTask(RemindTask.Arg arg);

}
