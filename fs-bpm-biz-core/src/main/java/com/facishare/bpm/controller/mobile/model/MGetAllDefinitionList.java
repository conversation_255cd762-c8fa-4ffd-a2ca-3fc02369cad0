package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.WorkflowOutline;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Created by wangz on 17-8-2.
 */
public interface MGetAllDefinitionList {
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    class Result {
        @Descriable(value = "流程列表",required = true)
        @JSONField(name = "M1")
        List<SimpleWorkflow> workflowList;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    class SimpleWorkflow {
        @Descriable(value = "流程outlineId",required = true)
        @JSONField(name = "M1")
        String id;
        @Descriable(value = "流程名称",required = true)
        @JSONField(name = "M2")
        String name;
        @Descriable(value = "流程sourceWorkflowId",required = true)
        @JSONField(name = "M3")
        String sourceWorkflowId;

        public static SimpleWorkflow getSimpleWorkflow(WorkflowOutline outline) {
            if (outline == null) {
                return null;
            }

            return new SimpleWorkflow(outline.getId(), outline.getName(), outline.getSourceWorkflowId());
        }
    }
}
