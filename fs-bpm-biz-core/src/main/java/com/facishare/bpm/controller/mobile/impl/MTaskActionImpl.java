package com.facishare.bpm.controller.mobile.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.mobile.MTaskAction;
import com.facishare.bpm.controller.mobile.model.*;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMParamsException;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.*;
import com.facishare.bpm.remote.crmremind.CrmRemindServiceProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.CompleteTaskFormValidateManager;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.flow.mongo.bizdb.model.FlowConfigTerminal;
import com.facishare.flow.repository.FlowConfigRepository;
import com.facishare.rest.core.model.ClientInfo;
import com.facishare.rest.core.model.ClientType;
import com.facishare.rest.core.util.JacksonUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by Aaron on 13/04/2017.
 *
 */
@Component
@Slf4j
public class MTaskActionImpl extends BPMBaseAction implements MTaskAction {
    @Autowired
    private BPMTaskService taskService;
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;
    @Autowired
    private CrmRemindServiceProxy crmRemindServiceProxy;

    @Autowired
    private FlowConfigRepository flowConfigRepository;


    @Override
    public MGetHandleTaskList.Result getHandleTaskList(MGetHandleTaskList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getHandleTaskList start , EID={}, PAGE_NO.={}, PAGE_SIZE={}, IS_COMPELTED={}, TASK_NAME={}", serviceManager
                .getUserId(), arg.getPageNumber(), arg.getPageSize(), arg.getIsCompleted(), arg.getKeyWord());
        Page page = arg.createPage();

        PageResult<TaskOutline> pageResult = taskService.getHandleTaskList(serviceManager, arg.getIsCompleted(), arg.getKeyWord(),
                page);

        I18NParser.parse(serviceManager.getTenantId(),pageResult.getDataList());
        return new MGetHandleTaskList.Result(pageResult.getTotal(), pageResult.getDataList());
    }

    @Override
    public GetTaskInfoByLaneId.Result getTaskInfoByLaneId(GetTaskInfoByLaneId.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        ClientInfo clientInfo = serviceManager.getClientInfo();
        if(Objects.isNull(clientInfo.getClientType())) {
            clientInfo.setClientType(ClientType.android);
        }
        List<LaneTask> tasksByLaneId = taskService.getTasksByLaneId(serviceManager, arg.getLaneId(), arg.getInstanceId(), arg.getEntityId(), arg.getObjectId(),arg.getApplyButtons(), arg.getNotGetDatas());
        I18NParser.parse(serviceManager.getTenantId(),tasksByLaneId);
        GetTaskInfoByLaneId.Result result = new GetTaskInfoByLaneId.Result(serviceManager, tasksByLaneId);
        result.mergeTaskDataAndDesc(serviceManager.getTenantId());
        return result;
    }

    @Override
    public MCompleteTask.Result completeTask(MCompleteTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String taskId = arg.getTaskId();
        String objectId = arg.getObjectId();
        log.debug("completeTask start , CONTEXT={}, TASK_ID={}, OBJECT_ID={}, OPINION={}, DATA={}", serviceManager.getContext(), taskId,
                objectId, arg.getOpinion(), arg.getData());

        CompleteTaskResult completeTaskResult;
        try {
            completeTaskResult = taskService.completeTask(
                    serviceManager,
                    taskId,
                    arg.getOpinion(),
                    arg.getData(),
                    arg.getAddOrReplaceNextTaskAssignee(),
                    arg.getNextTaskAssignee(),
                    true,
                    true, arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            // 捕获非阻断异常
            if(e.getErrorCode() == 301090061) {
                return MCompleteTask.Result.fail(null, e.getMessage());
            }
            // 捕获函数阻断异常
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return MCompleteTask.Result.fail(e.getMessage(), null);
            }
            //将后动作异常直接放在消息体中返回
            if (e.getErrorCode() == 301080003) {
                return new MCompleteTask.Result(false, e.getMessage(), null);
            }
            throw e;
        }
        return new MCompleteTask.Result(true, "", completeTaskResult);
    }

    @Override
    public MUpdateDataAndCompleteTask.Result updateDataAndCompleteTask(MUpdateDataAndCompleteTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        Task task = taskService.getPaaSTask(serviceManager, arg.getTaskId(),true);
        Map<String, Object> bpmExtension = task.getBpmExtension();
        Map<String, Object> purgedData = CompleteTaskFormValidateManager.getLegalUpdateFields(serviceManager,
                arg.getEntityId(),
                arg.getData(),
                MapUtil.instance.getList(bpmExtension, BPMConstants.FORM), serviceManager.getFields(arg.getEntityId()), false);
        String dataString = JacksonUtil.toJson(purgedData);

        TraceContext.get().setSourceProcessId(task.getSourceWorkflowId());
        Map metaData = serviceManager.updateData(serviceManager.getContext(), arg.getEntityId(), arg.getObjectId(), dataString, false, true, "workflow_bpm");

        String opinion = null;
        Map<String, Object> data = null;
        //如果任务是审批任务修改下opinion和data
        if(ExecutionTypeEnum.approve.equals(task.getExecutionType())){
            opinion = arg.getOpinion();
            data = Maps.newHashMap();
            data.put(BPMConstants.ApproveResult.RESULT, arg.getResult());
        }
        CompleteTaskResult completeTaskResult;
        try {
            completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(), opinion, data, arg.getAddOrReplaceNextTaskAssignee()
                    , arg.getNextTaskAssignee(), true,true, arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            if(MapUtils.isEmpty(metaData)){
                metaData = serviceManager.findDataById(arg.getEntityId(), arg.getObjectId());
            }
            // 捕获非阻断异常
            if(e.getErrorCode() == 301090061) {
                return new MUpdateDataAndCompleteTask.Result(metaData, e.getMessage());
            }
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return new MUpdateDataAndCompleteTask.Result(e.getMessage(), metaData);
            }
            return new MUpdateDataAndCompleteTask.Result(e.getMessage(), metaData);
        }
        if(Objects.nonNull(completeTaskResult) && Objects.nonNull(completeTaskResult.getRuleMessage()) && MapUtils.isEmpty(metaData)){
            metaData = serviceManager.findDataById(arg.getEntityId(), arg.getObjectId());
        }
        return new MUpdateDataAndCompleteTask.Result(metaData, completeTaskResult);
    }

    @Override
    public MGetTask.Result getTask(MGetTask.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        String instanceId = arg.getInstanceId();
        String activityInstanceId = arg.getActivityInstanceId();
        log.info("getTask , instanceId:{},activityInstanceId:{}", instanceId, activityInstanceId);
        MTask task = taskService.getMTask(serviceManager, instanceId, activityInstanceId,arg.getSource(),arg.getApplyButtons());
        I18NParser.parse(serviceManager.getTenantId(),task);
        return new MGetTask.Result(task);
    }

    @Override
    public MGetUncompletedTasksByObject.Result getUncompletedTasksByObject(MGetUncompletedTasksByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.info("getUncompletedTasksByObject start : ENTITY_ID={}, OBJECT_ID={}", arg.getApiName(), arg.getObjectId());
        List<TaskOutline> outlines = taskService.getUncompletedTasksByObject(serviceManager, arg.getObjectId());
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        return new MGetUncompletedTasksByObject.Result(outlines);
    }

    @Override
    public MChangeTaskHandler.Result changeTaskHandler(MChangeTaskHandler.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("changeTaskHandler start : CONTEXT={}, TASK_ID={}, CANDIDATE_IDS={}, MODIFY_OPINION = {}", serviceManager.getContext(), arg.getTaskId(),
                arg.getCandidateIds(),arg.getModifyOpinion());
        Boolean ret = taskService.changeTaskHandlers(serviceManager, arg.getTaskId(), arg.getCandidateIds(), arg.getModifyOpinion());
        return new MChangeTaskHandler.Result(ret);
    }

    @Override
    public MClearCrmRemindOfTask.Result clearCrmRemind(MClearCrmRemindOfTask.Arg arg) {
        RefServiceManager serviceManager = getManager();
        boolean result = crmRemindServiceProxy.clearTaskRemind(serviceManager, null);
        return new MClearCrmRemindOfTask.Result(result);
    }

    @Override
    public MAfterActionRetry.Result afterActionRetry(MAfterActionRetry.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        return new MAfterActionRetry.Result(
                taskService.afterActionRetry(
                        serviceManager,
                        arg.getTaskId(),
                        arg.getRowNum(),
                        arg.getExecuteType()
                )
        );
    }



    /**
     * //755 添加  1. 查看业务流任务,默认跳转到任务详情页还是数据详情页  2, 移动端待办完成后,默认在任务完成页停留还是返回到之前的页面
     *
     * @param serviceManager
     */
    private Map<String, Object> getFlowConfig(RefServiceManager serviceManager) {
        try {
            return flowConfigRepository.queryOld(serviceManager.getContext(), FlowType.workflow_bpm, FlowConfigTerminal.MOBILE, Lists.newArrayList(BPMConstants.SKIP_PAGE_FROM_MOBILE_COMPLETE_TASK));
        } catch (Exception e) {
            log.warn("ignore,", e);
            Map<String, Object> query = Maps.newHashMap();
            query.put(BPMConstants.SKIP_PAGE_FROM_MOBILE_COMPLETE_TASK, BPMConstants.TASK_PAGE);
            return query;
        }
    }


    /**
     * 2021-09-28 15:41:20 查询线上服务调用 该接口最近一个月没调用
     * @param arg
     * @return
     */
    @Deprecated
    @Override
    public MGetTaskInfo.Result getTaskDetail(MGetTaskDetail.Arg arg) {
        return getTaskInfo(arg.getTaskInfoArg());
    }


    @Override
    public MGetTaskInfo.Result getTaskInfo(MGetTaskInfo.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        if (StringUtils.isBlank(arg.getId())
                && !(StringUtils.isNotBlank(arg.getInstanceId())
                && (StringUtils.isNotBlank(arg.getActivityId()) || StringUtils.isNotBlank(arg.getActivityInstanceId())))) {
            log.error("arg is null,taskId:{},instanceId:{}", arg.getId(), arg.getInstanceId());
            throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_INCORRECT);
        }
        TaskDetail task = taskService.getBpmTaskDetail(serviceManager, arg.getId(), arg.getInstanceId(), arg.getActivityInstanceId(), arg.getActivityId(),TaskParams.create().isMobile(true).applyButtons(arg.getApplyButtons()).includeTaskFeedDetailConfig(arg.getIncludeTaskFeedDetailConfig()).addH5(arg.getIsH5()).isTaskDetail(true));
        I18NParser.parse(serviceManager.getTenantId(), task);
        return new MGetTaskInfo.Result(serviceManager, task,getFlowConfig(serviceManager));
    }

    @Override
    public MCreateTaskData.Result createTaskData(MCreateTaskData.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        Boolean result = taskService.createTaskData(serviceManager,
                arg.getTaskId(),
                arg.getData(),
                arg.getActivityId(),
                arg.getActivityInstanceId(),
                arg.getExecutionType());
        return new MCreateTaskData.Result(result);
    }

    @Override
    public MGetUncompletedTaskInfosByObject.Result getUncompletedTaskInfoByObject(MGetUncompletedTaskInfosByObject.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        //815 添加isMobile  判断是H5过来的请求  815之前通过getClientType为空来判断是安卓
        //小程序H5 clientType是web（chrome），原生H5为null
        if (serviceManager.getContext().getClientInfo().getClientType() == null || Boolean.TRUE.equals(arg.getIsMobile())) {
            serviceManager.getContext().getClientInfo().setClientType(ClientType.android);
        }
        log.info("getUncompletedTaskInfoByObject start : ENTITY_ID={}, OBJECT_ID={}", arg.getApiName(), arg.getObjectId());
        List<LaneTask> laneTasks = taskService.getUncompletedTaskInfosByObject(serviceManager,arg.getApiName(), arg.getObjectId(),arg.getApplyButtons(), arg.getNotGetDatas());
        I18NParser.parse(serviceManager.getTenantId(),laneTasks);
        return new MGetUncompletedTaskInfosByObject.Result(laneTasks, arg.getIsMobile());
    }

    @Override
    public MGetButtonByTaskIds.Result getButtonByTaskIds(MGetButtonByTaskIds.Arg arg) {
        validateArg(arg);

        Set<String> taskIds = arg.getTaskIds();
        if (CollectionUtils.isEmpty(taskIds)) {
            return new MGetButtonByTaskIds.Result();
        }
        RefServiceManager serviceManager = getManager();
        TaskButtonList TaskButtonList = taskService.getButtonByTaskIds(serviceManager, taskIds, TaskParams.create().isMobile(true).applyButtons(true));
        return new MGetButtonByTaskIds.Result(TaskButtonList);
    }

    @Override
    public MCompleteTask.Result completeAndCreateTaskData(MCompleteAndCreateTaskData.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        //创建任务数据
        taskService.createTaskData(serviceManager,
                arg.getTaskId(),
                arg.getData(),
                arg.getActivityId(),
                arg.getActivityInstanceId(),
                arg.getExecutionType());

        String taskId = arg.getTaskId();
        String objectId = arg.getObjectId();
        log.debug("completeAndCreateTaskData start , CONTEXT={}, TASK_ID={}, OBJECT_ID={}, OPINION={}, DATA={}", serviceManager.getContext(), taskId,
                objectId, arg.getOpinion(), arg.getData());

        CompleteTaskResult completeTaskResult;
        try {
            completeTaskResult = taskService.completeTask(
                    serviceManager,
                    taskId,
                    arg.getOpinion(),
                    arg.getData(),
                    arg.getAddOrReplaceNextTaskAssignee(),
                    arg.getNextTaskAssignee(),
                    true,
                    true,
                    arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            // 捕获非阻断异常
            if(e.getErrorCode() == 301090061) {
                return MCompleteTask.Result.fail(null, e.getMessage());
            }
            // 捕获函数阻断异常
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return MCompleteTask.Result.fail(e.getMessage(), null);
            }
            //将后动作异常直接放在消息体中返回
            if (e.getErrorCode() == 301080003) {
                return new MCompleteTask.Result(false, e.getMessage(), null);
            }
            throw e;
        }
        return new MCompleteTask.Result(true, "", completeTaskResult);
    }

    @Override
    public RefreshHandlerByTaskId.Result refreshHandlerByTaskId(RefreshHandlerByTaskId.Arg arg){
        validateArg(arg);
        taskService.refreshHandlerByTaskId(getManager(), arg.getTaskId());
        return new RefreshHandlerByTaskId.Result();
    }
    @Override
    public Edit.Result edit(Edit.Arg arg){
        validateArg(arg);
        Edit.Result result = new Edit.Result();
        Map editRes = taskService.edit(getManager(), arg.getTaskId(), arg.getObjectData(), arg.getDetails(), arg.getOptionInfo(), arg.getNotValidate(), arg.getOriginalData(), arg.getOriginalDetails());
        if(MapUtils.isNotEmpty(editRes)){
            result.putAll(editRes);
        }
        return result;
    }

    @Override
    public OperateTask.Result operateTask(OperateTask.Arg arg){
        validateArg(arg);
        OperateTask.Result result = new OperateTask.Result();
        taskService.operateTask(getManager(), arg.getTaskId(), arg.getType(), arg.getOpinion(), arg.getTagInfo());
        return result;
    }

    @Override
    public RemindTask.Result remindTask(RemindTask.Arg arg){
        validateArg(arg);
        RemindTask.Result result = new RemindTask.Result();
        taskService.remindTask(getManager(), arg.getTaskId(), arg.getContent(), arg.getRemindPersons());
        return result;
    }

}
