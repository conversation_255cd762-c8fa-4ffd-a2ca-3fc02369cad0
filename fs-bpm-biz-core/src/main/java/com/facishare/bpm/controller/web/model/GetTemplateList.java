package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import lombok.Data;


/**
 * Created by <PERSON> on 06/01/2017.
 */
public interface GetTemplateList {
    @Data
    class Arg{
        private Page page;
    }

    @Data
    class Result extends PageResult<Template> {
        public Result(PageResult<Template> result){
            this.setTotal(result.getTotal());
            this.setDataList(result.getDataList());
        }
    }
    @Data
    class Template extends WorkflowOutline{
    }
}
