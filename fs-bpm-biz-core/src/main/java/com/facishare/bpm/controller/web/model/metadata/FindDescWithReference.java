package com.facishare.bpm.controller.web.model.metadata;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;

/**
 * Created by wangz on 17-9-12.
 */
public interface FindDescWithReference {

    class Arg {
        @Getter
        @Setter
        @NotEmpty(message = "apiName不能为空")
        String apiName;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List refObjectDescribeList;
        Map describe;
        public Result(Map<String,Object> result){
            refObjectDescribeList = (List)result.get("refObjectDescribeList");
            describe = (Map<String,Object>)result.get("currentEntityDescribe");
        }
    }
}
