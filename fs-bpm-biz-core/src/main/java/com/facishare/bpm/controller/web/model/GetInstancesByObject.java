package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * Created by wangzhx on 2019/4/18.
 */
public interface GetInstancesByObject {

    @Data
    class Arg {
        @Descriable(value = "对象Id", required = true)
        @NotBlank(message = "查询实例阶段信息 对象Id不能空")
        private String entityId;
        @Descriable(value = "数据Id", required = true)
        @NotBlank(message = "查询实例阶段信息 数据Id不能空")
        private String objectId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<GetInstanceByObject> result;
    }
}
