package com.facishare.bpm.controller.web.model.metadata;

import com.facishare.bpm.remote.model.SimpleMetadataAction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-1-11.
 */
public interface FindObjActions {
    @Data
    class Arg{
        @NotEmpty(message = "apiName不能为空")
        String apiName;
        //自定义按钮获取传common，操作按钮获取传flow或空
        String actionType;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        List<SimpleMetadataAction> actions;
    }
}
