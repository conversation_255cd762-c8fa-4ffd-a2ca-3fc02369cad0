package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.WorkflowOutline;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.stream.Collectors;


/**
 * Created by <PERSON> on 17/2/23.
 */
public interface MGetAvailableWorkflows {
    class Arg{}
    @Getter
    @Setter
    class Result {
        @JSONField(name = "M1")
        private List<MAvailableWorkflow> dataList;

        public Result(List<WorkflowOutline> outlines) {
            this.dataList=outlines.stream().map(
                    outline-> MGetAvailableWorkflows.MAvailableWorkflow.fromWorkflowOutline(outline)
            ).collect(Collectors.toList());
        }
    }

    @Getter
    @Setter
    class MAvailableWorkflow{
        @JSONField(name = "M1")
        private String outlineId;
        @JSONField(name = "M2")
        private String sourceWorkflowId;
        @JSONField(name = "M3")
        private String name;
        public static MAvailableWorkflow fromWorkflowOutline(WorkflowOutline outline){
            MAvailableWorkflow availableWorkflow=new MAvailableWorkflow();
            availableWorkflow.setOutlineId(outline.getId());
            availableWorkflow.setSourceWorkflowId(outline.getSourceWorkflowId());
            availableWorkflow.setName(outline.getName());
            return availableWorkflow;
        }
    }
}
