package com.facishare.bpm.controller.mobile.model;

import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.task.TaskDetail;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/6/4 5:05 PM
 */
public interface MGetTaskDetail {
    @Deprecated
    @Data
    class Arg {
//        @JSONField(name = "M1")
        private String id;

        /**
         * 为终端适配 instanceId,activityInstanceId
         */
        @Descriable(value = "实例id")
        private String instanceId;
        @Descriable(value = "节点实例id")
        private String activityInstanceId;
        @Descriable(value = "节点id")
        private String activityId;

        /**
         * 780 新增,应用节点下发多个按钮
         */
        private Boolean applyButtons;

        public Boolean getApplyButtons() {
            return applyButtons != null && applyButtons;
        }

        /**
         * TODO 780 下线
         * @return
         */
        @Deprecated
        public MGetTaskInfo.Arg getTaskInfoArg(){
            MGetTaskInfo.Arg arg = new MGetTaskInfo.Arg();
            arg.setId(id);
            arg.setInstanceId(instanceId);
            arg.setActivityInstanceId(activityInstanceId);
            arg.setActivityId(activityId);
            arg.setApplyButtons(applyButtons);
            return arg;
        }
    }
    @Deprecated
    @ToString(callSuper = true)
    class Result extends MGetTaskInfo.Result{
        public Result(TaskDetail taskDetail, Map<String, Object> flowConfig) {
            super(null, taskDetail,flowConfig);
        }
    }

}
