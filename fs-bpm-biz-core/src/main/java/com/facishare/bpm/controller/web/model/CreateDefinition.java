package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import lombok.Data;

/**
 * 创建流程定义 Created by cuiyongxu on 16/12/21.
 */
public interface CreateDefinition {
    @Data
    class Arg extends WorkflowOutline {
        @Override
        public String toString() {
            return super.toString();
        }
    }

    @Data
    class Result {
        String id;

        public Result(String id) {
            this.id = id;
        }
    }
}
