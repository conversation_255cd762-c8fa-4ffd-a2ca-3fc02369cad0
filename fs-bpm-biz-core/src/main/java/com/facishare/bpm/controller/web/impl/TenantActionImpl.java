package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.TenantAction;
import com.facishare.bpm.controller.web.model.GetDefinitionConfig;
import com.facishare.bpm.controller.web.model.GetTenantQuota;
import com.facishare.bpm.controller.web.model.HasQuota;
import com.facishare.bpm.model.GetBPMLicense;
import com.facishare.bpm.model.tenant.DefinitionConfig;
import com.facishare.bpm.service.BPMTenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by cuiyongxu on 17/5/9.
 */
@Slf4j
@Service
public class TenantActionImpl extends BPMBaseAction implements TenantAction {

    @Autowired
    private BPMTenantService bpmTenantService;


    @Override
    public HasQuota.Result hasQuota(HasQuota.Arg arg) {
        RefServiceManager serviceManager = getManager();
        HasQuota.Result result = new HasQuota.Result();
        result.setHasQuota(bpmTenantService.hasQuota(serviceManager));
        return result;
    }

    @Override
    public GetDefinitionConfig.Result getDefinitionConfig(GetDefinitionConfig.Arg arg) {
        RefServiceManager serviceManager = getManager();
        DefinitionConfig config = bpmTenantService.getDefinitionConfig(serviceManager);
        return new GetDefinitionConfig.Result(config);
    }

    @Override
    public GetTenantQuota.Result getTenantQuota() {
        GetBPMLicense quotaDetail = bpmTenantService.getQuotaDetail(getManager());
        return new GetTenantQuota.Result(
                quotaDetail.getSurplus() > 0,
                quotaDetail.getTotal() - quotaDetail.getSurplus(),
                quotaDetail.getTotal() ,
                quotaDetail.getTotal() - quotaDetail.getSurplus() - quotaDetail.getMarket(),
                quotaDetail.getMarket(),
                quotaDetail.isBelongManageGroup());
    }


}
