package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.mobile.model.MGetAllDefinitionList;
import com.facishare.bpm.controller.mobile.model.MGetAvailableWorkflowOfObject;
import com.facishare.bpm.controller.mobile.model.MGetAvailableWorkflows;
import com.facishare.bpm.controller.mobile.model.MGetBPMUseApiNames;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by <PERSON> on 11/04/2017.
 */
@Path("BPM/MDefinition")
@Produces({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@Consumes({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface MDefinitionAction {
    /**
     * 查询当前对象（入口）下可用的流程（同时 对该用户可用）
     *
     * @param arg
     * @return
     */
    @Path("GetAvailableWorkflowOfObject")
    @POST
    MGetAvailableWorkflowOfObject.Result getAvailableWorkflowOfObject(MGetAvailableWorkflowOfObject.Arg arg);


    /**
     * 查询当前人下定义的流程（同时 对该用户可用）,阶段视图
     *
     * @param arg
     * @return
     */
    @Path("GetAllAvailableWorkflows")
    @POST
    MGetAvailableWorkflows.Result getAllAvailableWorkflows(MGetAvailableWorkflows.Arg arg);

    /**
     * 查询一个企业的所有流程定义
     * 在终端 业务流程列表 筛选条件中使用到
     *
     * @return
     */
    @Path("MGetAllDefinitionList")
    @POST
    MGetAllDefinitionList.Result getAllDefinitionList();

    /**
     * 终端在业务流程实例列表页顶部,用来进行对象筛选
     * @return
     */
    @Path("MGetBPMUseApiNames")
    @POST
    MGetBPMUseApiNames.Result getBPMUseApiNames();
}
