package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;


public interface MCancelInstance {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @NotEmpty(message = "流程id不能为空")
        String id;//实例Id,取消实例时使用

        @JSONField(name = "M2")
        //@FcpField(description = "流程中止原因")
        String reason;
    }

    @Data
    class Result {
        @JSONField(name = "M2")
        boolean result = true;
    }
}
