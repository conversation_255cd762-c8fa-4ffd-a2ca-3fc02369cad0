package com.facishare.bpm.controller.web.impl;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.ProcessDraftAction;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.service.impl.BPMWorkflowDraftServiceImpl;
import com.facishare.bpm.utils.BeanConvertUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.REPLACE_WHEN_NOT_FOUND;

@Slf4j
@Service
public class ProcessDraftActionImpl extends BPMBaseAction implements ProcessDraftAction {

    @Autowired
    private BPMWorkflowDraftServiceImpl draftService;

    /**
     * 创建草稿
     */
    @Override
    public CreateDraft.Result createDraft(CreateDraft.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        log.debug("createDraft start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        String draftId = arg.getDraftId();
        // id不为空,但是draftd为空,表示有定义,保存草稿
        if (Strings.isNullOrEmpty(draftId) && !Strings.isNullOrEmpty(arg.getId())) {
            arg.setOutlineId(arg.getId());
        }
        arg.setId(null);
        return new CreateDraft.Result(draftService.saveWorkflowDraft(serviceManager, arg, true));
    }

    /**
     * 流程更新草稿
     */
    @Override
    public UpdateDraft.Result updateDraft(UpdateDraft.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("updateDraft start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        //删除流程定义后要删除草稿,如果用户打开两个tabs,A页签将定义删除,因为定义删除后,草稿也同样删除了
        //B业务提交草稿保存时,需要查一下当前的草稿是否还存在,因为定义的删除导致草稿删除了,故添加此校验
        arg.setId(arg.getDraftId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        draftService.updateWorkflowDraft(serviceManager, arg);
        return new UpdateDraft.Result();
    }


    /**
     * 查询特定流程的草稿
     */
    @Override
    public GetDraft.Result getDraftById(GetDraft.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        WorkflowOutlineDraft outlineDraft = draftService.getWorkflowDraftById(serviceManager, arg.getId());
        if (REPLACE_WHEN_NOT_FOUND.equals(outlineDraft.getEntryTypeName()) || StringUtils.isBlank(outlineDraft.getEntryTypeName())) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_INLET_OBJECT_DISABLE_OR_DELETE);
        }
        return new GetDraft.Result(outlineDraft);
    }


    @Override
    public GetDraftList.Result getDraftList(GetDraftList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getDraftList start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        PageResult<WorkflowOutlineDraft> ret = draftService.getWorkflowDrafts(serviceManager, arg.createWorkflowOutlineQuery(), arg.getPage(), arg.getSupportPagingQuery());
        return new GetDraftList.Result(ret.getTotal(), ret.getDataList());
    }

    /**
     * 删除流程定义
     */
    @Override
    public DeleteDefinition.Result deleteDraft(DeleteDefinition.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        log.debug("deleteDraft start : tenantId={}, appId={}, userId={}, outlineId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId(), arg.getId());
        boolean flag = false;
        if (StringUtils.isNotEmpty(arg.getId())) {
            draftService.delete(serviceManager, arg.getId());
            flag = true;
        } else {
            log.error("流程定义草稿Id为空");
        }
        return new DeleteDefinition.Result(flag);
    }
}
