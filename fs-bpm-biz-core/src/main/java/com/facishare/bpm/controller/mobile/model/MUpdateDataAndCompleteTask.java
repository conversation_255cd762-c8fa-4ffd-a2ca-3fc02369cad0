package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.model.task.CompleteTaskResult;
import com.facishare.bpm.model.task.NextTask;
import com.facishare.bpm.rule.RuleMessage;
import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;
import java.util.Objects;

public interface MUpdateDataAndCompleteTask {
    @Data
    class Arg {
        @Descriable(value = "任务id", required = true)
        @JSONField(name = "M1")
        @NotEmpty(message = "任务id不能为空")
        String taskId;
        @Descriable(value = "意见")
        @JSONField(name = "M2")
        String opinion;
        @Descriable(value = "节点业务对象类型", required = true)
        @JSONField(name = "M3")
        @NotEmpty(message = "对象类型不能为空")
        String entityId;
        @Descriable(value = "节点业务对象", required = true)
        @JSONField(name = "M4")
        @NotEmpty(message = "记录id不能为空")
        String objectId;
        @Descriable(value = "节点其它数据", required = true)
        @JSONField(name = "M5")
        Map<String, Object> data;

        @Descriable(value = "指它下一节点处理人", required = true)
        @JSONField(name = "M6")
        Map<String, Object> nextTaskAssignee; //指定下一节点处理人 参数为{person:["1232"]}

        @Descriable(value = " (1:添加/0：替换)", required = true)
        @JSONField(name = "M7")
        Integer addOrReplaceNextTaskAssignee;

        @Descriable(value = "是否走校验规则,0:不校验;1:校验")
        @JSONField(name = "M8")
        Integer validationRule = 1;

        @JSONField(name = "M9")
        boolean ignoreNonBlocking;

        @Descriable(value = "审批节点的审批结果-840")
        @JSONField(name = "M10")
        String result;

        @Descriable(value = "是否忽略非阻断异常，true——忽略，false或null——不忽略")
        @JSONField(name = "M11")
        Boolean ignoreNoBlockValidate;

        public String getObjectData(Map<String, Object> data) {
            if (MapUtils.isEmpty(data)) {
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_FORM_FORMAT_DATA_NULL);
            }
            Map<String, Object> temp = new HashedMap();
            data.forEach((k, v) -> temp.put(k, v));
            data.put("_id", objectId);
            return JsonUtil.toJson(temp);
        }

        public boolean getValidationRule() {
//            return this.validationRule != null && this.validationRule == 1;
            return true;
        }
    }

    @Data
    class Result {
        @Descriable(value = "是否完成")
        @JSONField(name = "M1")
        Map data;
        @Descriable(value = "不满足条件异常信息")
        @JSONField(name = "M2")
        RuleMessage ruleMessage;
        @JSONField(name = "M3")
        Integer sleepTime;
        @Descriable(value = "下一个任务")
        @JSONField(name = "M4")
        NextTask nextTask;
        @Descriable(value = "非阻断异常")
        @JSONField(name = "M5")
        private String nonBlockMessage;
        @Descriable(value = "阻断异常")
        @JSONField(name = "M6")
        private String blockMessage;

        public Result(Map data, String nonBlockMessage) {
            this.data = data;
            this.nonBlockMessage = nonBlockMessage;
        }
        public Result(String blockMessage, Map data) {
            this.data = data;
            this.blockMessage = blockMessage;
        }

        public Result(Map data, CompleteTaskResult completeTaskResult) {
            this.data = data;
            if (Objects.nonNull(completeTaskResult)) {
                this.ruleMessage = completeTaskResult.getRuleMessage();
                this.sleepTime = completeTaskResult.getSleepTime();
                this.nextTask = completeTaskResult.getNextTask();
            }
        }
    }

}
