package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.task.TaskLog;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-3-7.
 */
public interface MGetLog {
    @Getter
    @Setter
    class Arg {
        @JSONField(name = "M1")
        @NotEmpty(message = "流程id不能为空")
        String workflowInstanceId;
    }

    @Setter
    @Getter
    class Result {
        @JSONField(name = "M1")
        List<MTaskLog> datas;

        public Result(List<TaskLog> taskLogList) {
            this.datas = taskLogList.stream().map(MTaskLog::fromTaskLog).collect(Collectors.toList());
        }
    }
}
