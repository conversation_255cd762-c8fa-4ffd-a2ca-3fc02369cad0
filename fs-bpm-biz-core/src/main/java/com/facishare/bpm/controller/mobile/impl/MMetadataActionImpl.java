package com.facishare.bpm.controller.mobile.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.mobile.MMetadataAction;
import com.facishare.bpm.controller.web.model.metadata.GetDataOwner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * Created by wangz on 17-1-4.
 */
@Slf4j
@Controller
public class MMetadataActionImpl extends BPMBaseAction implements MMetadataAction {

    @Override
    public GetDataOwner.Result getDataOwner(GetDataOwner.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("getDataOwner start : CONTEXT={}, ARG={} ", serviceManager.getContext().toString(), arg.toString());
        List<String> owner = serviceManager.getDataOwner(arg.getApiName(), arg.getId());
        return new GetDataOwner.Result(owner);
    }
}
