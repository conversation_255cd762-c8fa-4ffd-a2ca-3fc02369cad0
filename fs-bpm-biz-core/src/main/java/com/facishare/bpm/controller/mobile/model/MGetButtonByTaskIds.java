package com.facishare.bpm.controller.mobile.model;

import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.task.TaskButtonList;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 * author: cuiyongxu
 * create_time: 2021/9/23-4:47 PM
 **/
public interface MGetButtonByTaskIds {

    @Data
    class Arg {
        protected Set<String> taskIds;
    }

    @Data
    class Result {
        //taskId:button
        protected Map<String, List<ActionButton>> buttons;
        /**
         * 880 业务定制元素，下发配置信息
         */
        protected Map<String, TaskButtonList.CustomElementData> customElementDataMap;
        protected Map<String, String> errorMsgs;
        protected Map<String, List> moreOperations;

        public Result(TaskButtonList taskButtonList){
            this.buttons = taskButtonList.getButtons();
            this.customElementDataMap=taskButtonList.getCustomElementDataMap();
            this.errorMsgs = taskButtonList.getErrorMsgs();
            this.moreOperations = taskButtonList.getMoreOperations();
        }

        public Result(){}
    }

}
