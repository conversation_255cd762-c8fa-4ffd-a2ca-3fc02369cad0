package com.facishare.bpm.controller.web.model.metadata;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;


/**
 * Created by wangz on 17-5-23.
 */
public interface GetDataOwner {
    @Data
    class Arg{
        @JSONField(name = "M1")
        @NotEmpty(message = "apiName不能为空")
        private String apiName;
        @JSONField(name = "M2")
        @NotEmpty(message = "数据不存在")
        private String id;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        @JSONField(name = "M1")
        private List<String> owner;
    }
}
