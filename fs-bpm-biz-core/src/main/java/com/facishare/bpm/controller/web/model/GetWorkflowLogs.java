package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowLog;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowLogs;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangzhx on 2019/6/17.
 */
public interface GetWorkflowLogs {
    @Data
    class Arg {
        @NotEmpty(message = "sourceWorkflowId不能为空")
        private String sourceWorkflowId;
        private int pageSize = 10;
        private int pageNumber = 1;

        public Page getPage() {
            return new Page(pageSize, pageNumber, null, false);
        }
    }

    @Data
    class Result {
        private List<WorkflowLog> workflowLogs;
        private int totalCount;

        public Result(WorkflowLogs.WorkflowLogsPage workflowLogsPage) {
            this.workflowLogs = workflowLogsPage.getFlowList().stream().map(workflowLog ->
                    BeanUtils.transfer(workflowLog, WorkflowLog.class)).collect(Collectors.toList());
            this.totalCount = workflowLogsPage.getTotal();
        }
    }

}
