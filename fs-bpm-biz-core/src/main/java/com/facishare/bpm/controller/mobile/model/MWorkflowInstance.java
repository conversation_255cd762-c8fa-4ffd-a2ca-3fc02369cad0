package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.utils.DateUtil;
import lombok.Data;

import java.util.List;


/**
 * Created by <PERSON> on 19/04/2017.
 */
@Data
public class MWorkflowInstance {

    /**
     * 流程编号
     */
    @JSONField(name = "M1")
    private String id;
    /**
     * 流程名称
     */
    @JSONField(name = "M2")
    private String workflowName;
    /**
     * 所属业务对象名称
     */
    @JSONField(name = "M3")
    private String entityName;
    /**
     *  所属业务对象id
     */
    @JSONField(name = "M4")
    private String entityId;

    /**
     * 记录id
     */
    @JSONField(name = "M5")
    private String objectId;

    /**
     * 业务记录主属性
     */
    @JSONField(name = "M6")
    private String objectName;
    /**
     * 状态
     */
    @JSONField(name = "M7")
    private InstanceState state;
    /**
     * 阶段
     */
    @JSONField(name = "M8")
    private String laneName;
    /**
     * 当前任务名称
     */
    @JSONField(name = "M9")
    private String taskName;
    /**
     * 当前流程中发起人
     */
    @JSONField(name = "M10")
    private String applicantId;
    /**
     * 实例创建时间
     */
    @JSONField(name = "M11")
    private Long start;
    /**
     * 实例结束时间
     */
    @JSONField(name = "M12")
    private Long end;

    /**
     * 触发来源
     */
    @JSONField(name = "M13")
    private String triggerSource;

    //实例当前待处理人
    @JSONField(name = "M14")
    private List<String> currentCandidateIds;

    //最后修改时间
    @JSONField(name = "M15")
    private Long lastModifiedTime;

    //终止人
    @JSONField(name = "M16")
    private String canceler;

    //终止原因
    @JSONField(name = "M17")
    private String cancelReason;

    //异常原因
    @JSONField(name = "M18")
    private String errorReason;

    @JSONField(name = "M19")
    private String outerSubmitter;
    public void setEnd(Long end){
        if(end!=null&&end==0){
            this.end=null;
        }
        this.end=end;
    }

    public static MWorkflowInstance fromWorkflowInstance(WorkflowInstanceVO item) {
        MWorkflowInstance vo=new MWorkflowInstance();
        vo.setId(item.getId());
        vo.setWorkflowName(item.getWorkflowName()+"("+ DateUtil.instance.format(item.getStart())+")");
        vo.setState(item.getState());
        vo.setApplicantId(item.getApplicantId());
        vo.setStart(item.getStart());
        vo.setEnd(item.getEnd());
        vo.setTriggerSource(item.getTriggerSource());
        vo.setCurrentCandidateIds(item.getCurrentCandidateIds());
        vo.setLastModifiedTime(item.getLastModifiedTime());
        vo.setCanceler(item.getCanceler());
        vo.setCancelReason(item.getCancelReason());
        vo.setErrorReason(item.getErrorReason());
        vo.setOuterSubmitter(item.getOuterSubmitter());
        return vo;
    }

}
