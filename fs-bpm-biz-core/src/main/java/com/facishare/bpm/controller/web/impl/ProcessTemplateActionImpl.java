package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.ProcessTemplateAction;
import com.facishare.bpm.controller.web.model.GetTemplateDetail;
import com.facishare.bpm.controller.web.model.GetTemplateList;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.service.BPMTemplateService;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 06/01/2017.
 */
@Slf4j
@Service
public class ProcessTemplateActionImpl extends BPMBaseAction implements ProcessTemplateAction {

    @Autowired
    private BPMTemplateService templateService;

    @Override
    public GetTemplateList.Result getTemplateList(GetTemplateList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        /**
         * 换用是否支持外部流程，不支持就不把外部流程模板放出去
         */
        PageResult<WorkflowOutline> result = templateService.getTemplateList(serviceManager, arg.getPage());
        PageResult<GetTemplateList.Template> data = new PageResult<>();
        data.setDataList(result.getDataList().stream().map(item -> BeanUtils.transfer(item, GetTemplateList.Template.class)).collect(Collectors.toList()));
        data.setTotal(result.getTotal());
        return new GetTemplateList.Result(data);

    }

    @Override
    public WorkflowOutline getTemplate(GetTemplateDetail.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        return templateService.getTemplateDetail(serviceManager, arg.getId());
    }
}
