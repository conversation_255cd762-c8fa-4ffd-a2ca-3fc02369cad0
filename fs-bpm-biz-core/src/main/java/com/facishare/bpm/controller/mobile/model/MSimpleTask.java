package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import lombok.Data;

@Data
public class MSimpleTask {
    @Descriable(value = "任务id")
    @JSONField(name = "M1")
    String taskId;
    @Descriable(value = "工作流名称")
    @JSONField(name = "M2")
    String processName;
    @Descriable(value = "任务名称")
    @JSONField(name = "M3")
    String taskName;
    @Descriable(value = "阶段名称")
    @JSONField(name = "M4")
    String laneName;
    @Descriable(value = "任务状态")
    @JSONField(name = "M5")
    TaskState state;
    @Descriable(value = "实例id")
    @JSONField(name = "M6")
    String instanceId;
    @Descriable(value = "开始时间")
    @JSONField(name = "M7")
    Long startTime;
    @Descriable(value = "任务实例id")
    @JSONField(name = "M8")
    Integer activityInstanceId;
    @Descriable(value = "落地对象id")
    @JSONField(name = "M9")
    String objectId;
    @Descriable(value = "落地对象apiName")
    @JSONField(name = "M10")
    String entityId;
    @Descriable(value = "阶段id")
    @JSONField(name = "M11")
    String laneId;

    @Descriable(value = "落地对象name")
    @JSONField(name = "M12")
    String objectName;


    public static MSimpleTask fromOutline(TaskOutline item) {
        MSimpleTask task=new MSimpleTask();
        task.setTaskId(item.getTaskId());
        task.setProcessName(item.getProcessName());
        task.setTaskName(item.getTaskName());
        task.setLaneName(item.getLaneName());
        task.setState(item.getState());
        task.setInstanceId(item.getWorkflowInstanceId());
        task.setStartTime(item.getStartTime());
        task.setActivityInstanceId(item.getActivityInstanceId());
        task.setEntityId(item.getEntityId());
        task.setObjectId(item.getObjectId());
        task.setLaneId(item.getLaneId());
        task.setObjectName(item.getObjectName());
        return task;


    }
}
