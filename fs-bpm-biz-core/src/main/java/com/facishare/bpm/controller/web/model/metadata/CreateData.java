package com.facishare.bpm.controller.web.model.metadata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * Created by wangz on 17-1-4.
 */
public interface CreateData {
    @Data
    class Arg{
        @NotEmpty(message = "apiName不能为空")
        String apiName;
        @NotEmpty(message = "内容不能为空")
        String jsonData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        Map result;
    }
}
