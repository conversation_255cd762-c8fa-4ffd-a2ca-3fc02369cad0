package com.facishare.bpm.controller.web.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


public interface GetTenantQuota {


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{

        /**
         * 是否有可用的配额
         */
        boolean allowable ;
        /**
         * 当前使用数
         */
        private long currentNumber;
        /**
         * 总数
         */
        private long limitedNumber;
        /**
         * 业务流程使用配额数
         */
        private long bpmUsedCount;
        /**
         * 营销流程数量
         */
        private long marketNumber;
        /**
         * 当前用户是否属于分管小组
         */
        private boolean belongManageGroup;
    }
}
