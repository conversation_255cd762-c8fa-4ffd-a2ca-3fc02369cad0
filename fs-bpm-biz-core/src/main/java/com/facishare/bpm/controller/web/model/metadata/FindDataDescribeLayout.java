package com.facishare.bpm.controller.web.model.metadata;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.HashMap;
import java.util.Map;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/5/22 5:38 PM
 */
public interface FindDataDescribeLayout {

    @Data
    class Arg{
        @NotEmpty(message = "entityId不能为空")
        String entityId;

        @NotEmpty(message = "objectId不能为空")
        String objectId;

        String recordType;

        String layoutType ;//list
    }

    @Data
    class Result extends HashMap{
        public Result(Map map){
            super(map);
        }

        public Result(){}
    }
}
