package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

public interface MGetHandleTaskList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private int pageNumber ;
        @JSONField(name = "M2")
        private int pageSize ;
        @JSONField(name = "M3")
        private Boolean isCompleted; //true已完成，false未完成，null全部
        @JSONField(name = "M4")
        private String keyWord;

        public Page createPage() {
            Page page = new Page();
            page.setPageSize(this.getPageSize());
            page.setPageNumber(this.getPageNumber());
            page.setOrderBy("createTime");
            page.setAsc(false);
            return page;
        }
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private int total;
        @JSONField(name = "M2")
        private List<MSimpleTask> dataList;
        public Result(int total, List<TaskOutline> dataList) {
            this.setTotal(total);
            this.setDataList(dataList.stream().map(item->MSimpleTask.fromOutline(item)).collect(Collectors.toList()));
        }
    }
}
