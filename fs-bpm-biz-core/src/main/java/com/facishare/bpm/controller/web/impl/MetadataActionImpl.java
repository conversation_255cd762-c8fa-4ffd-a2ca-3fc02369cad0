package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.MetadataAction;
import com.facishare.bpm.controller.web.model.metadata.*;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.SimpleMetadataAction;
import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.CompleteTaskFormValidateManager;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.rest.core.model.ClientType;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-1-4.
 */
@Slf4j
@Controller
public class MetadataActionImpl extends BPMBaseAction implements MetadataAction {

    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private BPMTaskService taskService;

    @Override
    public FindDescribe.Result findDescribe(FindDescribe.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("findDescribe start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        Map describe = metadataService.findDescribe(context, arg.getApiName(), false,false);
        return new FindDescribe.Result(describe);
    }

    @Override
    public FindDataById.Result findDataById(FindDataById.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("findDataById start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        Map data = metadataService.findDataById(context,arg.getApiName(),arg.getDataId(),false,false,false,true,true,true,null).getObject_data();
        return new FindDataById.Result(data);
    }

    @Override
    public CreateData.Result createData(CreateData.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("createData start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        Map data = metadataService.createData(context, arg.getApiName(), arg.getJsonData());
        return new CreateData.Result(data);
    }

    @Override
    public UpdateData.Result updateData(UpdateData.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("updateData start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());

        RefServiceManager serviceManager = getServiceManager(context);
        Task task = taskService.getPaaSTask(serviceManager, arg.getTaskId());
        Map<String, Object> bpmExtension = task.getBpmExtension();
        Map<String, Object> purgedData = CompleteTaskFormValidateManager.getLegalUpdateFields(serviceManager, arg.getApiName(),
                JacksonUtil.fromJson(arg.getDataString(), new TypeReference<Map<String, Object>>() {
                }), MapUtil.instance.getList(bpmExtension, BPMConstants.FORM), serviceManager.getFields(arg.getApiName()), arg.getRemoveVersion());
        String dataString  = JacksonUtil.toJson(purgedData);

        TraceContext.get().setSourceProcessId(task.getSourceWorkflowId());
        com.facishare.bpm.model.resource.newmetadata.UpdateData.UpdateDataResultDetail updateDataResultDetail = metadataService.updateDataSkipRequired(context,
                arg.getApiName(),
                arg.getDataId(),
                dataString,
                true,
                !arg.isIgnoreNonBlocking(),
                true);
        if(Boolean.TRUE.equals(updateDataResultDetail.getVersionCheckBlocked())){
            if(Objects.nonNull(context.getClientInfo())
                    && (ClientType.web.equals(context.getClientInfo().getClientType()) || ClientType.desktop.equals(context.getClientInfo().getClientType()))){
                return new UpdateData.Result(updateDataResultDetail.getDataConflicts());
            }else {
                throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_DATA_MODIFIED_RETRY_ERROR);
            }
        }
        return new UpdateData.Result(updateDataResultDetail.getObject_data());
    }

    @Override
    public FindCustomObjs.Result findCustomObjs(FindCustomObjs.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("findCustomObjs start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        List<SimpleMetadataDesc> objs = metadataService.findDescsByTenantId(context, arg.getApiNames(), arg.isIncludeFieldsDesc(),arg.getUseGroupManager());
        return new FindCustomObjs.Result(objs);
    }

    @Override
    public FindDescsByApiNames.Result findDescsByApiNames(FindDescsByApiNames.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("FindDescsByApiNames start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        List<String> apiNames=arg.getApiNames().stream().filter(item-> !Strings.isNullOrEmpty(item)).collect(Collectors.toList());
        List<SimpleMetadataDesc> objs = metadataService.findDescsByApiNames(context, apiNames, arg.isIncludeLookups());

        return new FindDescsByApiNames.Result(objs);
    }

    @Override
    public FindObjActions.Result findObjActions(FindObjActions.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("findObjActions start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        List<SimpleMetadataAction> actions = metadataService.findActionsByDesc(context, arg.getApiName(), arg.getActionType());
        log.info("findObjActions end : RET={}", JsonUtil.toJson(actions));
        return new FindObjActions.Result(actions);
    }

    @Override
    public FindReferences.Result findReferences(FindReferences.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("executeAction start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());
        List<SimpleMetadataDesc> objs = metadataService.findReferences(context, arg.getApiName());

        return new FindReferences.Result(objs);
    }

    @Override
    public GetDataOwner.Result getDataOwner(GetDataOwner.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        log.debug("getDataOwner start : CONTEXT={}, ARG={} ", context.toString(), arg.toString());

        List<String> owner = metadataService.getDataOwner(context, arg.getApiName(), arg.getId());
        return new GetDataOwner.Result(owner);
    }

    @Override
    public FindDescWithReference.Result findDescWithReference(FindDescWithReference.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        Map<String, Object> result = metadataService.findDescWithReference(context, arg.getApiName());
        return new FindDescWithReference.Result(result);
    }

    @Override
    public FindDataDescribeLayout.Result findDataDescribeLayout(FindDataDescribeLayout.Arg arg) {
        validateArg(arg);
        RemoteContext context = initRemoteContext();
        Map<String, Object> result = metadataService.findDataDescribeLayout(context, arg.getEntityId(),arg.getObjectId(),arg.getRecordType(),arg.getLayoutType());
        return new FindDataDescribeLayout.Result(result);
    }

    @Override
    public GetBpmTaskBusinessCodeFilterCriteria.Result getBpmTaskBusinessCodeFilterCriteria(){
        return new GetBpmTaskBusinessCodeFilterCriteria.Result(SwitchConfigManager.getSupportExternalApplyBusinessCodeFilter());
    }
}
