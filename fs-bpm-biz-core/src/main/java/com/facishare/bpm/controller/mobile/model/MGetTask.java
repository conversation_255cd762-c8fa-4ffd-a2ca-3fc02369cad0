package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.task.MTask;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface MGetTask {
    @Data
    class Arg {
        @Descriable(value = "实例id",required = true)
        @JSONField(name = "M1")
        @NotEmpty(message = "实例id不能为空")
        private String instanceId;
        @Descriable(value = "节点实例id",required = true)
        @JSONField(name = "M2")
        @NotEmpty(message = "节点实例id不能为空")
        private String activityInstanceId;
        @JSONField(name = "M3")
        private String source = "h5";
        /**
         * 780 新增,应用节点下发多个按钮
         */

        /**
         * 780 新增,应用节点下发多个按钮
         */
        @JSONField(name = "M4")
        private Boolean applyButtons;

        public Boolean getApplyButtons() {
            return applyButtons != null && applyButtons;
        }
    }

    @Data
    class Result extends MTask {

        public Result(MTask task) {
            BeanUtils.copy(task,this);
        }
        //TODO 终端优化,给终端下发数据时,将没有选择的option去掉
        static List<String> hasOptionTypes= Lists.newArrayList("country","city","province","district","select_one");
        private static void removeNoUseOptions(List<List<Map<String, Object>>> forms) {
            if(CollectionUtils.isNotEmpty(forms)){
                forms.forEach(form-> form.forEach(field->{
                    String type= (String) field.get(BPMConstants.MetadataKey.type);
                    if(hasOptionTypes.contains(type)){
                        Object value=field.get("value");
                        List<Map<String,Object>> options= (List<Map<String, Object>>) field.get("options");
                        options.removeAll(options.stream().filter(item->!item.get("value").equals(value)).collect(Collectors.toList()));
                    }
                }));
            }
        }
    }
}
