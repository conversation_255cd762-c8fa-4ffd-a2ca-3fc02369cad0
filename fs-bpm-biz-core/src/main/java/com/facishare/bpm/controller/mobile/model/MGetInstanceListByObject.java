package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.remote.model.org.Employee;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface MGetInstanceListByObject {
    @Data
    class Arg extends Page {
        @JSONField(name = "M1")
        private int pageSize = 10;
        @JSONField(name = "M2")
        private int pageNumber = 1;
        @JSONField(name = "M3")
        private String orderBy;
        @JSONField(name = "M4")
        private boolean asc;
        @JSONField(name = "M5")
        @NotEmpty(message = "数据记录id不能为空")
        private String objectId;
        @JSONField(name = "M6")
        private InstanceState state;
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        private int total;
        @JSONField(name = "M2")
        private List<MWorkflowInstance> dataList;
        @JSONField(name = "M3")
        private Map<String, Employee> employeeInfo;

        public Result(int total, List<WorkflowInstanceVO> dataList, Map<String, Employee> employeeInfo) {
            this.total = total;
            this.dataList = dataList.stream().map(item -> MWorkflowInstance.fromWorkflowInstance(item)).collect(Collectors.toList());
            this.employeeInfo = employeeInfo;
        }
    }
}
