package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.controller.mobile.model.MAfterActionRetry;
import com.facishare.bpm.model.paas.engine.bpm.AfterRetry;
import lombok.Data;

public interface AfterActionRetry {
    @Data
    class Arg extends MAfterActionRetry.Arg{
    }
    @Data
    class Result extends MAfterActionRetry.Result {
        public Result(AfterRetry.RetryResult result) {
            super(result);
        }
    }
}
