package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.task.LaneTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import java.util.List;

/**
 * Created by wangzhx on 2019/4/18.
 */
public interface GetTasksByLaneId {

    @Data
    class Arg {
        @Descriable(value = "阶段Id", required = true)
        @NotBlank(message = "获取阶段下的任务 阶段Id不能空")
        private String laneId;
        @Descriable(value = "流程Id", required = true)
        @NotBlank(message = "获取阶段下的任务 流程Id不能空")
        private String workflowId;
        @Descriable(value = "实例Id", required = true)
        @NotBlank(message = "获取阶段下的任务 实例Id不能空")
        private String instanceId;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<LaneTask> result;
    }

}
