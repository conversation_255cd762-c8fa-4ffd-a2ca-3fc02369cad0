package com.facishare.bpm.controller.web.model.metadata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * Created by wangz on 17-1-4.
 */
public interface FindDataById {
    @Data
    class Arg{
        @NotEmpty(message = "数据类型不能为空")
        String apiName;
        @NotEmpty(message = "数据id不能为空")
        String dataId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result{
        Map data;
    }

}
