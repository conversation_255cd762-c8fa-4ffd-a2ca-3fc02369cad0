package com.facishare.bpm.controller.web.model;

import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.HashMap;
import java.util.Map;

public interface Edit {

    @Data
    class Arg{
        @DocDescribe(label = "任务id", required = true)
        @NotEmpty(message = "任务id不能为空")
        private String taskId;
        private String details;
        private String objectData;
        Map<String,Object> optionInfo;
        /**
         *  传递 notValidate = true 则不做校验;如果不传递或者传递false,校验必填
         */
        private Boolean notValidate = Boolean.FALSE;
        /**
         * 多人编辑支持保存场景新增参数 830
         */
        private String originalData;
        private String originalDetails;
    }

    @Data
    class Result extends HashMap {

    }
}
