package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.mobile.model.*;
import com.facishare.bpm.controller.web.model.GetInstancesByObject;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 流程实例接口
 */
@Path("BPM/MInstance")
@Produces({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@Consumes({MediaType.APPLICATION_JSON, RestConstant.MediaType.SIMPLE_JSON})
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface MInstanceAction {

    /**
     * 停止流程实例
     * @param arg
     * @return
     */
    @Path("CancelInstance")
    @POST
    MCancelInstance.Result cancelInstance(MCancelInstance.Arg arg);


    /**
     * 启动流程实例
     * @param arg
     * @return
     */
    @Path("StartInstance")
    @POST
    MStartInstance.Result startInstance(MStartInstance.Arg arg);


    /**
     * 获取某个流程的实例
     * @param arg
     * @return
     */
    @Deprecated
    @Path("GetInstanceList")
    @POST
    MGetInstanceList.Result getInstanceList(MGetInstanceList.Arg arg);


    /**
     * 获取某个对象下的已完成流程实例列表（包含已完成和已取消）
     * @param arg
     * @return
     */
    @Path("GetInstanceListByObject")
    @POST
    MGetInstanceListByObject.Result getInstanceListByObject(MGetInstanceListByObject.Arg arg);

    /**
     * 获取完整流程数据
     * @param arg
     * @return
     */
    @Path("GetEntireWorkflowInstance")
    @POST
    MGetEntireWorkflow.Result getEntireWorkflowInstance(MGetEntireWorkflow.Arg arg);

    /**
     * 获取流程实例记录
     * @param arg
     * @return
     */
    @Deprecated
    @Path("GetLog")
    @POST
    MGetLog.Result getLog(MGetLog.Arg arg);

    @Path("GetWorkflowInstanceLog")
    @POST
    MGetWorkflowInstanceLog.Result getWorkflowInstanceLog(MGetWorkflowInstanceLog.Arg arg);



    @Deprecated
    @Path("GetSomeConfig")
    @POST
    MGetSomeConfig.Result getSomeConfig(MGetSomeConfig.Arg arg);


    @Path("GetCustomRedirectConfig")
    @POST
    MGetCustomRedirectConfig.Result getCustomRedirectConfig(MGetCustomRedirectConfig.Arg arg);

    /**
     * 启动流程实例
     * 不满足条件返回异常信息
     *
     * @param arg
     * @return
     */
    @Path("TriggerInstance")
    @POST
    MTriggerInstance.Result triggerInstance(MTriggerInstance.Arg arg);

    @Path("AfterActionRetry")
    @POST
    MInstanceAfterActionRetry.Result afterActionRetry(MInstanceAfterActionRetry.Arg arg);

    @Path("GetTaskOrInstanceRelatedEntityId")
    @POST
    GetTaskOrInstanceRelatedEntityId.Result getTaskOrInstanceRelatedEntityId(GetTaskOrInstanceRelatedEntityId.Arg arg);

    @Path("GetInstancesByObject")
    @POST
    GetInstancesByObject.Result getInstancesByObject(GetInstancesByObject.Arg arg);
}
