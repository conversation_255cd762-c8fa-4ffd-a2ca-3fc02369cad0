package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

public interface GetDefinitionByWorkflowId {
    @Data
    class Arg {
        @NotEmpty(message = "流程定义id不能为空")
        private String workflowId;
    }

    @Data
    class Result {
        WorkflowOutline outline;


        public Result(WorkflowOutline workflowOutline) {
            this.outline = workflowOutline;
        }
    }
}
