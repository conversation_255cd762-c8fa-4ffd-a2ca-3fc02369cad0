package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.remote.model.org.Employee;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * Created by wangz on 17-1-18.
 */
public interface GetTask {
    @Deprecated
    @Data
    class Arg {
        @NotEmpty(message = "taskId不能为空")
        private String taskId;

        /**
         * 获取任务详情,单独给web端提供,主要解决统计字段,计算字段,定义中的from填写的type=number,
         * 后端对其进行处理,国家省市区原来是select_one  现在是什么类型下发什么类型
         * 前端有部分特殊逻辑,例如国家省市区下发的时候,下发为select_one,需要处理掉,计算字段统计字段的处理
         */
        private Boolean bussCustomFlag;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private BPMTask bpmTask;
        private Map<String, Employee> employeeInfo;
    }
}
