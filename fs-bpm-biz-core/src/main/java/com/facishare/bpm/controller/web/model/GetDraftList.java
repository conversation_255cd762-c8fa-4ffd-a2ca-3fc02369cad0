package com.facishare.bpm.controller.web.model;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.base.Strings;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 获取流程定义,列表或单一对象详情 Created by cuiyongxu on 16/12/22.
 */
public interface GetDraftList {
    @Data
    class Arg {
        private int pageNumber;
        private int pageSize;
        private String keyWord;
        private String orderBy;//排序,lastModifiedTime,createTime
        private String entityId;//过滤对象id
        private WorkflowOutlineQuery workflowOutlineQuery;
        @DocDescribe(label = "是否查询所有定义，true 分页查询，false 全量查询")
        private Boolean supportPagingQuery;

        /**
         * 是否支持分页查询，如果为空，默认支持分页
         * @return
         */
        public boolean getSupportPagingQuery() {
            return Objects.isNull(supportPagingQuery) ? Boolean.TRUE : supportPagingQuery;
        }

        public Page getPage() {
            Page page = new Page(pageSize, pageNumber, orderBy, false);
            String orderBy = page.getOrderBy();
            if (StringUtils.isEmpty(orderBy)) {
                page.setOrderBy(WorkflowOutlineEntity.Fields.lastModifiedTime);
            }
            return page;
        }

        public WorkflowOutlineQuery createWorkflowOutlineQuery() {
            WorkflowOutlineQuery workflowOutlineQuery = new WorkflowOutlineQuery();
            workflowOutlineQuery.setEntityId(this.getEntityId());

            String keyword = this.getKeyWord();

            boolean isValid = !Strings.isNullOrEmpty(keyword) && (keyword.startsWith("?") || keyword.startsWith("*") || keyword.startsWith("+"));
            if (isValid) {
                workflowOutlineQuery.setName("\\" + keyword);
            }
            return workflowOutlineQuery;
        }
    }

    @Data
    class Result {
        int totalCount;
        List<WorkflowOutlineDraft> outlines;

        public Result(int totalCount, List<WorkflowOutlineDraft> outlines) {
            this.totalCount = totalCount;
            this.outlines = outlines;

        }

    }
}
