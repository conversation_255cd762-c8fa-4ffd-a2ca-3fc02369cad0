package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.task.CompleteTaskResult;
import com.facishare.bpm.model.task.NextTask;
import com.facishare.bpm.rule.RuleMessage;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;
import java.util.Objects;

public interface MCompleteTask {
    @Data
    class Arg {
        @Descriable(value = "任务id", required = true)
        @NotEmpty(message = "任务id不能为空")
        @JSONField(name = "M1")
        String taskId;
        @Descriable(value = "意见")
        @JSONField(name = "M2")
        String opinion;
        @Descriable(value = "节点业务对象", required = true)
        @JSONField(name = "M3")
        String objectId;
        @Descriable(value = "节点其它数据", required = true)
        @JSONField(name = "M4")
        Map<String, Object> data;
        @Descriable(value = "指定下一节点处理人", required = true)
        @JSONField(name = "M5")
        Map<String, Object> nextTaskAssignee;
        @Descriable(value = " (1:添加/0：替换)", required = true)
        @JSONField(name = "M6")
        Integer addOrReplaceNextTaskAssignee;
        @Descriable(value = "是否忽略非阻断异常，true——忽略，false或null——不忽略")
        @JSONField(name = "M7")
        Boolean ignoreNoBlockValidate;

    }

    @Data
    @NoArgsConstructor
    class Result {
        @Descriable(value = "是否完成")
        @JSONField(name = "M1")
        boolean result;
        @Descriable(value = "异常信息")
        @JSONField(name = "M2")
        String msg;
        @Descriable(value = "不满足条件异常信息")
        @JSONField(name = "M3")
        RuleMessage ruleMessage;
        @Descriable(value = "前端或终端刷新停留时间")
        @JSONField(name = "M4")
        Integer sleepTime;
        @Descriable(value = "下一个任务")
        @JSONField(name = "M5")
        NextTask nextTask;
        @Descriable(value = "非阻断异常")
        @JSONField(name = "M6")
        private String nonBlockMessage;
        @Descriable(value = "阻断异常")
        @JSONField(name = "M7")
        private String blockMessage;

        public static Result fail(String blockMessage, String nonBlockMessage) {
            Result result = new Result();
            result.setBlockMessage(blockMessage);
            result.setNonBlockMessage(nonBlockMessage);
            result.setResult(false);
            return result;
        }

        public Result(boolean result, String msg, CompleteTaskResult completeTaskResult) {
            this.result = result;
            this.msg = msg;
            if (Objects.nonNull(completeTaskResult)) {
                this.ruleMessage = completeTaskResult.getRuleMessage();
                this.sleepTime = completeTaskResult.getSleepTime();
                this.nextTask = completeTaskResult.getNextTask();
            }
        }
    }

}
