package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.controller.mobile.model.MInstanceAfterActionRetry;
import com.facishare.bpm.model.paas.engine.bpm.AfterRetry;
import lombok.Data;

public interface InstanceAfterActionRetry {
    @Data
    class Arg extends MInstanceAfterActionRetry.Arg{
    }
    @Data
    class Result extends MInstanceAfterActionRetry.Result {
        public Result(AfterRetry.RetryResult result) {
            super(result);
        }
    }
}
