package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.task.TaskTagInfoArg;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

import javax.validation.constraints.NotBlank;

public interface OperateTask {

    @Data
    class Arg{
        @DocDescribe(label = "任务id", required = true)
        @NotBlank(message = "任务id不能为空")
        private String taskId;
        @DocDescribe(label = "操作类型（suspend-暂停，resume-继续）", required = true)
        @NotBlank(message = "操作类型不能为空")
        private String type;
        @DocDescribe(label = "原因")
        private String opinion;
        @DocDescribe(label = "加签信息")
        private TaskTagInfoArg tagInfo;

    }

    @Data
    class Result{

    }
}
