package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.instance.EntireWorkflow;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by <PERSON> on 12/04/2017.
 */
public interface MGetEntireWorkflow {
    @Data
    class Arg{
        @Descriable(value = "流程实例id",required = true)
        @NotEmpty(message = "流程实例id不能为空")
        @JSONField(name = "M1")
        private String instanceId;
    }
    @Data
    class Result{
        @JSONField(name = "M1")
        private Map<String,Object> workflow;
        @JSONField(name = "M2")
        private Map<String,Object> workflowInstance;
        @JSONField(name = "M3")
        private String svg;
        @JSONField(name = "M4")
        private List<MoreOperationManager.MoreOperation> moreOperations;
        @JSONField(name = "M5")
        private Map<String, Employee> employeeInfo;

        public Result(EntireWorkflow entireWorkflow) {
            this.setWorkflow(entireWorkflow.getWorkflow());
            this.setWorkflowInstance(JsonUtil.fromJson(JsonUtil.toJson(entireWorkflow.getWorkflowInstance()), HashMap.class));
            this.setCurrentLaneId(entireWorkflow.getCurrentLaneId());
            this.svg = entireWorkflow.getSvg();
            this.moreOperations=entireWorkflow.getMoreOperations();
            this.employeeInfo=entireWorkflow.getEmployeeInfo();
        }

        private static List<String> excludeWorkflowFields= Lists.newArrayList("variables","transitions","type",
                "appId","tenantId","id");
        public void setWorkflow(Map<String,Object> workflow) {
            this.workflow = workflow;
            if(workflow!=null){
                excludeWorkflowFields.forEach(item->workflow.remove(item));
            }
        }

        private static List<String> excludeInstanceFields= Lists.newArrayList("variables","sourceWorkflowId","workflowName","workflowDescription","tenantId");
        public  void setWorkflowInstance(Map<String,Object> workflowInstance) {
            this.workflowInstance = workflowInstance;
            if(workflowInstance!=null){
                excludeInstanceFields.forEach(item->workflowInstance.remove(item));
            }
        }

        public void setCurrentLaneId(String currentLaneId) {
            this.getWorkflowInstance().put("currentLaneId",currentLaneId);
        }
    }
}
