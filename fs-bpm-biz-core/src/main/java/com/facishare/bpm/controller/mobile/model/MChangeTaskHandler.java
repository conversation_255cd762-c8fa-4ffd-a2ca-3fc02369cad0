package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-5-2.
 */
public interface MChangeTaskHandler {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @NotEmpty(message = "任务标示不能为空")
        String taskId;
        @JSONField(name = "M2")
        @NotEmpty(message = "没有填写处理人")
        List<String> candidateIds;
        @JSONField(name = "M3")
        String modifyOpinion;
    }

    @Data
    class Result {
        @JSONField(name = "M1")
        boolean result;

        public Result(boolean result) {
            this.result = result;
        }
    }
}
