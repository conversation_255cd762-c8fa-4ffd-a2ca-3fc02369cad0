package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.GetDefinitionConfig;
import com.facishare.bpm.controller.web.model.GetTenantQuota;
import com.facishare.bpm.controller.web.model.HasQuota;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by cuiyongxu on 17/5/9.
 */
@Path("BPM/Tenant")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface TenantAction {

    @Path("HasQuota")
    @POST
    HasQuota.Result hasQuota(HasQuota.Arg arg);

    @Path("GetDefinitionConfig")
    @POST
    GetDefinitionConfig.Result getDefinitionConfig(GetDefinitionConfig.Arg arg);

    @Path("GetTenantQuota")
    @POST
    GetTenantQuota.Result getTenantQuota();

}
