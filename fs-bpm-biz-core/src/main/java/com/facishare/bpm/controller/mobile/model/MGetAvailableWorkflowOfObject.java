package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.WorkflowOutline;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 11/04/2017.
 */
public interface MGetAvailableWorkflowOfObject {
    @Getter
    @Setter
    class Arg{
        @Descriable(value = "入口对象类型",required = true)
        @JSONField(name = "M1")
        @NotEmpty(message = "入口对象类型不能为空")
        private String entryType;
        @Descriable(value = "入口对象id",required = true)
        @JSONField(name = "M2")
        private String objectId;
    }
    @Getter
    @Setter
    class Result{
        @Descriable(value = "当前对象可用的流程列表",required = true)
        @JSONField(name = "M1")
        private List<MGetAvailableWorkflows.MAvailableWorkflow> dataList;

        public Result(List<WorkflowOutline> outlines) {
            this.dataList=outlines.stream().map(
                    outline-> MGetAvailableWorkflows.MAvailableWorkflow.fromWorkflowOutline(outline)
            ).collect(Collectors.toList());
        }
    }
}
