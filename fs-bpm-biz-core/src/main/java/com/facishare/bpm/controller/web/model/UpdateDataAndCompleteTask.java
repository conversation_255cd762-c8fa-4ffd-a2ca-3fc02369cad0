package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.model.DataConflicts;
import com.facishare.bpm.model.task.CompleteTaskResult;
import com.facishare.bpm.model.task.NextTask;
import com.facishare.bpm.rule.RuleMessage;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2019/1/17 2:39 PM
 */
public interface UpdateDataAndCompleteTask {

    @Data
    class Arg {
        @Descriable(value = "任务id", required = true)
        @NotEmpty(message = "任务id不能为空")
        String taskId;
        @Descriable(value = "节点业务对象类型", required = true)
        @NotEmpty(message = "对象类型不能为空")
        String entityId;
        @Descriable(value = "节点业务对象", required = true)
        @NotEmpty(message = "记录id不能为空")
        String objectId;
        @Descriable(value = "节点其它数据", required = true)
        Map<String, Object> data;

        Map<String,Object> nextTaskAssignee;
        /**
         *  (1:添加/0：替换)
         */
        Integer addOrReplaceNextTaskAssignee;

        /**
         * 是否要获取下一个任务（true-是，false或null-否）
         */
        Boolean needGetNextTaskInfo;
        /**
         * 审批节点的结果
         */
        String result;
        /**
         * 审批节点的审批意见
         */
        String opinion;

        /**
         * true:忽略非阻断异常，false或null：不忽略
         */
        Boolean ignoreNoBlockValidate;

        Boolean removeVersion;

        public String getObjectData(Map<String, Object> data) {
            if (MapUtils.isEmpty(data)) {
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_FORM_FORMAT_DATA_NULL);
            }
            Map<String, Object> temp = Maps.newHashMap();
            data.forEach(temp::put);
            data.put("_id", objectId);
            return JsonUtil.toJson(temp);
        }

        public Boolean getNeedGetNextTaskInfo() {
            if (Objects.isNull(needGetNextTaskInfo)) {
                return Boolean.FALSE;
            }
            return needGetNextTaskInfo;
        }

        public void setNeedGetNextTaskInfo(Boolean needGetNextTaskInfo) {
            this.needGetNextTaskInfo = needGetNextTaskInfo;
        }
    }

    @Data
    class Result {
        @Descriable(value = "是否完成")
        Map data;
        @Descriable(value = "不满足条件异常信息")
        RuleMessage ruleMessage;
        @Descriable(value = "等待时长")
        private Integer sleepTime;
        @Descriable(value = "下一个需要处理的任务信息")
        private NextTask nextTask;
        @Descriable(value = "非阻断异常")
        private String nonBlockMessage;
        @Descriable(value = "修改数据是否冲突")
        private Boolean versionCheckBlocked;
        @Descriable(value = "冲突明细")
        private DataConflicts dataConflicts;
        @Descriable(value = "阻断异常")
        private String blockMessage;

        public Result(Map data, CompleteTaskResult completeTaskResult) {
            this.data = data;
            if(Objects.nonNull(completeTaskResult)){
                this.ruleMessage = completeTaskResult.getRuleMessage();
                this.sleepTime = completeTaskResult.getSleepTime();
                this.nextTask = completeTaskResult.getNextTask();
            }

        }

        public Result(String nonBlockMessage) {
            this.nonBlockMessage = nonBlockMessage;
        }

        public Result(Map data, String nonBlockMessage) {
            this.data = data;
            this.nonBlockMessage = nonBlockMessage;
        }
        public Result(String blockMessage, Map data) {
            this.blockMessage = blockMessage;
            this.data = data;

        }

        public Result(DataConflicts dataConflicts) {
            this.versionCheckBlocked = true;
            this.dataConflicts = dataConflicts;
        }
    }
}
