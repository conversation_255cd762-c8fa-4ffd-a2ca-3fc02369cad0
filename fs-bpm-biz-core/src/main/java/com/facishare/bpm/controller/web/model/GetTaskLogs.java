package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.task.TaskLog;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-3-7.
 */
public interface GetTaskLogs {
    @Getter
    class Arg {
        @NotEmpty(message = "实例标识不能空")
        String workflowInstanceId;
    }

    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        List<TaskLog> taskLogs;
    }
}
