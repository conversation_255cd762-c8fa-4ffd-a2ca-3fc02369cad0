package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.GetTemplateDetail;
import com.facishare.bpm.controller.web.model.GetTemplateList;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by <PERSON> on 06/01/2017.
 */
@Path("BPM/ProcessTemplate")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface ProcessTemplateAction {
    /**
     * 获取模板列表
     * @param arg
     * @return
     */
    @Path("GetTemplateList")
    @POST
    GetTemplateList.Result getTemplateList(GetTemplateList.Arg arg);

    /**
     * 获取模板详情
     * @param arg
     * @return
     */
    @Path("GetTemplateDetail")
    @POST
    WorkflowOutline getTemplate(GetTemplateDetail.Arg arg);
}
