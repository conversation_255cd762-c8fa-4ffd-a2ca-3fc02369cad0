package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @creat_date: 2018/10/23
 * @creat_time: 16:52
 * @since 6.4
 */
public interface GetDefinitionHistoryList {
    @Data
    class Arg {
        String sourceWorkflowId;
    }
    @Data
    class Result {
        List<WorkflowOutline> outlines;

        public Result(List<WorkflowOutline> outlines) {
            this.outlines=outlines;
        }
    }
}
