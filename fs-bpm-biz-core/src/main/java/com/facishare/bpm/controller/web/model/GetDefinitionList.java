package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.utils.StringManipulationUtils;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 获取流程定义,列表或单一对象详情 Created by cuiyongxu on 16/12/22.
 */
public interface GetDefinitionList {
    @Data
    class Arg {
        private int pageNumber;
        private int pageSize;
        private String keyWord;
        private String enabled; //1代表启用,2代表未启用,0查全部
        private String orderBy;//排序,lastModifiedTime,createTime
        private String entityId;//过滤对象id
        private List<String> entityIds;//过滤对象id,有此值时会忽略entityId
        private Integer externalFlow;//1代表外部流程  0 不是外部流程   -1 所有的
        private SupportFlow supportFlowType;//营销流程 或 简易流程
        /**
         * 请求来源：manageGroup——请求来自分管小组，不受分管小组不受分管小组约束，null——受分管小组约束
         */
        private String from;
        @DocDescribe(label = "是否查询所有定义，true 分页查询，false 全量查询")
        private Boolean supportPagingQuery;

        public boolean isSupportPagingQuery() {
            return this.getSupportPagingQuery();
        }

        public boolean getSupportPagingQuery() {
            return Objects.isNull(supportPagingQuery) ? Boolean.TRUE : supportPagingQuery;
        }

        public Page getPage() {
            Page page = new Page();
            page.setPageNumber(pageNumber);
            page.setPageSize(pageSize);
            page.setOrderBy(orderBy);

            String orderBy = page.getOrderBy();
            if (StringUtils.isEmpty(orderBy)) {
                page.setOrderBy(WorkflowOutlineEntity.Fields.lastModifiedTime);
            }

            return page;
        }

        public Boolean getEnabled() {
            if ("1".equals(this.enabled)) {
                return true;
            } else if ("2".equals(this.enabled)) {
                return false;
            } else {
                return null;
            }
        }

        public Integer getExternalFlow(){
            if(externalFlow==null||externalFlow==-1){
                return null;
            }
            return externalFlow;
        }

        public WorkflowOutlineQuery createWorkflowOutlineQuery() {
            String keyword = getKeyWord();
            setKeyWord(StringManipulationUtils.changeSpecialCharacter(keyword));
            WorkflowOutlineQuery workflowOutlineQuery = new WorkflowOutlineQuery();
            workflowOutlineQuery.setEntityId(getEntityId());
            workflowOutlineQuery.setEntityIds(getEntityIds());
            workflowOutlineQuery.setExternalFlow(getExternalFlow());
            workflowOutlineQuery.setEnable(getEnabled());

            if (!StringUtils.isEmpty(getKeyWord())) {
                workflowOutlineQuery.setName(getKeyWord());
            }
            if(Objects.nonNull(getSupportFlowType())){
                workflowOutlineQuery.setSupportFlowType(getSupportFlowType().code);
            }
            return workflowOutlineQuery;
        }
    }


    @Data
    class Result {
        int totalCount;
        List<WorkflowOutline> outlines;


        public Result(int totalCount, List<WorkflowOutline> definitions) {
            this.totalCount = totalCount;
            this.outlines = definitions;

        }

    }
}
