package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.CircleType;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;


/**
 * 获取流程定义下所有任务
 * Created by <PERSON> on 16/12/22.
 */
public interface GetHandleTaskListByLane {
    @Data
    class Arg extends Page {
        @NotEmpty(message = "请选择流程")
        private String sourceWorkflowId;
        private String laneId;
        private String activityId;
        private CircleType circleType=CircleType.ALL;
    }

}
