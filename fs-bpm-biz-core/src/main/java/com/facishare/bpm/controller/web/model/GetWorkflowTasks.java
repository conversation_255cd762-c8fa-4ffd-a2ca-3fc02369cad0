package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.task.LaneBriefTaskVO;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-5-6.
 */
public interface GetWorkflowTasks {
    @Data
    class Arg {
        @NotEmpty(message = "sourceWorkflowId 不能为空")
        String sourceWorkflowId;
        String id;
        LaneBriefTaskVO.QueryType type;
        LaneBriefTaskVO.QueryState state;
        private int pageNumber;
        private int pageSize;
    }

    @Getter
    @Setter
    class Result {
        List<LaneBriefTaskVO> taskList;
        int totalCount;

        public Result(List<LaneBriefTaskVO> taskList, int totalCount) {
            this.taskList = taskList;
            this.totalCount = totalCount;
        }

        public Result() {
        }

        public Result(List<LaneBriefTaskVO> taskList) {
            this.taskList = taskList;
        }

        public Result(int totalCount, List<LaneBriefTaskVO> taskList) {
            this.totalCount = totalCount;
            this.taskList = taskList;
        }
    }
}
