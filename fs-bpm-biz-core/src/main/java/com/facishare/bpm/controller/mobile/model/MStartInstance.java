package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

public interface MStartInstance {
    @Data
    class Arg {
        @NotEmpty(message = "流程id不能为空")
        @JSONField(name = "M1")
        String id;
        @JSONField(name = "M2")
        @NotEmpty(message = "入口对象不能为空")
        String objectId;// 流程入口对象
        //todo 未使用
        @NotEmpty(message = "入口对象类型不能为空")
        @JSONField(name = "M3")
        String entityId;// apiName
        @JSONField(name = "M4")
        Map<String,String> variables;
    }

    @Data
    @NoArgsConstructor
    class Result {
        @JSONField(name = "M1")
        protected String result;
        public Result(String result){
            this.result = result;
        }
    }
}
