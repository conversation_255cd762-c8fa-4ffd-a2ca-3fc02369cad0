package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.remote.model.org.Employee;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;

/**
 * Created by wangz on 17-2-9.
 */
public interface GetTasksByInstanceIds {
    @Data
    class Arg {
        @NotEmpty(message = "workflowInstanceId 不能为空")
        String workflowInstanceId;
        @NotEmpty(message = "activityInstanceIds不能为空")
        List<String> activityInstanceIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<BPMTask> bpmTasks;
        private Map<String, Employee> employeeInfo;

    }
}
