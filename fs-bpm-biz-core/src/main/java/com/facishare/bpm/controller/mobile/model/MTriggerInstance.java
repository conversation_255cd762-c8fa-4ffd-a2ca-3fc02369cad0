package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.rule.RuleMessage;
import lombok.Data;

public interface MTriggerInstance {

    @Data
    class Arg extends MStartInstance.Arg{
        @Override
        public String toString() {
            return super.toString();
        }
    }

    @Data
    class Result extends MStartInstance.Result{
        @JSONField(name = "M2")
        RuleMessage ruleMessage;

        public Result(String result, RuleMessage ruleMessage) {
            super.result = result;
            this.ruleMessage = ruleMessage;
        }
    }
}
