package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.TaskOutline;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-2-8.
 */
public interface GetUncompletedTasksByObject {
    @Data
    class Arg {
        private String apiName;
        @NotEmpty(message = "待办流程列表查询 数据Id不能空")
        private String objectId;
    }

    @Data
    class Result {
        private List<TaskOutline> taskOutlines;

        public Result(List<TaskOutline> taskOutlines) {
            this.taskOutlines = taskOutlines;
        }
    }
}
