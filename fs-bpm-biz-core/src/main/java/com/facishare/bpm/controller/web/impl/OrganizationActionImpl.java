package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.OrganizationAction;
import com.facishare.bpm.controller.web.model.GetOrganization;
import com.facishare.bpm.model.org.BPMOrg;
import com.facishare.bpm.service.BPMOrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.Objects;

/**
 * Created by cuiyongxu on 17/5/4.
 */
@Slf4j
@Controller
public class OrganizationActionImpl extends BPMBaseAction implements OrganizationAction {

    @Autowired
    private BPMOrganizationService bpmOrganizationService;

    @Override
    public GetOrganization.Result getOrganization(GetOrganization.Arg arg) {
        validateArg(arg);
        if (Objects.nonNull(arg.getType())) {
            log.info("获取组织架构调用方type为:{}", arg.getType());
        }
        BPMOrg bpmOrg = bpmOrganizationService.getOrganization(getManager(),arg);
        return GetOrganization.Result.create(bpmOrg);
    }
}
