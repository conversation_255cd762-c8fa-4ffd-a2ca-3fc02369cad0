package com.facishare.bpm.controller.web.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-4-24.
 */
public interface ChangeTaskHandler {
    @Data
    class Arg {
        @NotEmpty(message = "任务id不能为空")
        String taskId;
        @NotEmpty(message = "没有填写处理人")
        List<String> candidateIds;
        //770 意见
        String modifyOpinion;
    }

    @Data
    class Result {
        boolean result;
        public Result(boolean result){
            this.result = result;
        }
    }
}
