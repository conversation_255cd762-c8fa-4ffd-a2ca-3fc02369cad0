package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.task.TaskLog;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class MTaskLog implements Comparable<MTaskLog> {
    @JSONField(name = "M1")
    private String taskName;
    @JSONField(name = "M2")
    private String assigeeId;
    @JSONField(name = "M3")
    private String excutionType; //approve,update/opration/addRelatedObject/start/end
    @JSONField(name = "M4")
    private String taskType; //anyone, all
    @JSONField(name = "M5")
    private String opinion;
    @JSONField(name = "M6")
    private String result; //auto-agree,agree,reject
    @JSONField(name = "M7")
    private long time;
    @JSONField(name = "M8")
    private String preName;

    @Override
    public int compareTo(@NotNull MTaskLog o) {
        return this.getTime() > o.getTime() ? 1 : -1;
    }

    public static MTaskLog fromTaskLog(TaskLog taskLog){
        return BeanUtils.transfer(taskLog,MTaskLog.class,(src,rst)->{
            if(ExecutionTypeEnum.approve.name().equals(src.getExcutionType())){
                if("agree".equals(src.getResult())){
                    rst.preName= BPMI18N.PAAS_FLOW_BPM_APPROVAL_PASSED.text();
                }else{
                    rst.preName=BPMI18N.PAAS_FLOW_BPM_REJECT_APPROVE.text();
                }
            } else if("end".equals(src.getExcutionType())){
                rst.preName= BPMI18N.PAAS_FLOW_BPM_INSTANCE_END.text();
            }else if("start".equals(src.getExcutionType())){
                rst.preName=BPMI18N.PAAS_FLOW_BPM_INSTANCE_STARTED.text();
            }else{
                rst.preName=BPMI18N.PAAS_FLOW_BPM_FINISHED_TASK.text();
            }
        });
    }
}
