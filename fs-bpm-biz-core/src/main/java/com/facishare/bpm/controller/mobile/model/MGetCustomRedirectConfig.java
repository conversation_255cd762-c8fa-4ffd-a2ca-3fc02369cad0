package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 745
 */
public interface MGetCustomRedirectConfig {
    @Data
    class Arg {
        @DocDescribe(desc = "id  当前任务Id")
        @JSONField(name = "M1")
        private String id;

        @DocDescribe(desc = "objectApiName,对象api_name,CasesObj,只有是工单对象时 才会调用此接口")
        @JSONField(name = "M2")
        private String entityId;
        @DocDescribe(desc = "objectDataId  数据id")
        @JSONField(name = "M3")
        private String objectId;

        @DocDescribe(desc = "link_app_id")
        @JSONField(name = "M4")
        private String linkAppId;
        @DocDescribe(desc = "link_app_type")
        @JSONField(name = "M5")
        private Integer linkAppType;
        @DocDescribe(desc = "downstream,卡梅隆 weex无法通过cep获取是上游还是下游  需要单独加参数接入")
        @JSONField(name = "M6")
        private Boolean downstream;

    }

    @Data
    class Result {
        @JSONField(name = "M1")
        String appRedirectUrl;

        public Result(String appRedirectUrl) {
            this.appRedirectUrl = appRedirectUrl;
        }

        public Result() {
        }
    }
}
