package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.CircleType;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import lombok.Data;

import java.util.List;

/**
 * 获取某个流程的实例
 * Created by cuiyongxu on 16/12/22.
 */
public interface GetInstanceList {
    @Data
    class Arg extends Page {
        private String sourceWorkflowId;
        private CircleType circleType = CircleType.ALL;
        private InstanceState state;
        private String workflowName;
    }

    @Data
    class Result {
        private int total;
        private List<WorkflowInstanceVO> dataList;
    }
}
