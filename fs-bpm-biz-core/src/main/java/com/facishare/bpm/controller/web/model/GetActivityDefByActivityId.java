package com.facishare.bpm.controller.web.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * Created by <PERSON> on 03/07/2017.
 */
public interface GetActivityDefByActivityId {
    @Data
    class Arg{
        @NotEmpty(message = "workflowId不能为空")
        String workflowId;
        @NotEmpty(message = "activityId不能为空")
        String activityId;
    }
    @Data
    class Result{
        Map definition;
    }

}
