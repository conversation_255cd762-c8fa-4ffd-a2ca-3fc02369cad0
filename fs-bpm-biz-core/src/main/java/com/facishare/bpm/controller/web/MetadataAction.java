package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.metadata.*;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by wangz on 17-1-4.
 */
@Path("BPM/Metadata")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface MetadataAction {

    /**
     * 通过descApiName查找对象描述
     * @param arg
     * @return
     */
    @Path("FindDescribe")
    @POST
    FindDescribe.Result findDescribe(FindDescribe.Arg arg);


    /**
     * 通过Id查找元数据端自定义数据
     * @param arg
     * @return
     */
    @Path("FindDataById")
    @POST
    FindDataById.Result findDataById(FindDataById.Arg arg);

    /**
     * 创建数据
     * @param arg
     * @return
     */
    @Path("CreateData")
    @POST
    CreateData.Result createData(CreateData.Arg arg);

    /**
     * 更新数据
     * @param arg
     * @return
     */
    @Path("UpdateData")
    @POST
    UpdateData.Result updateData(UpdateData.Arg arg);

    /**
     * 查询一个企业下的可用 自定义对象 列表
     * @param arg
     * @return
     */
    @Path("FindCustomObjs")
    @POST
    FindCustomObjs.Result findCustomObjs(FindCustomObjs.Arg arg);

    /**
     * 通过apiName列表,查询多个对象描述(包含字段)
     * @param arg
     * @return
     */
    @Path("FindDescsByApiNames")
    @POST
    FindDescsByApiNames.Result findDescsByApiNames(FindDescsByApiNames.Arg arg);

    /**
     * 获取自定义对象的所有可操作类型
     * @param arg
     * @return
     */
    @Path("FindObjActions")
    @POST
    FindObjActions.Result findObjActions(FindObjActions.Arg arg);

    /**
     * 查询关联当前对象的对象列表(如 订单中关联客户, 此处 "当前对象" 为客户, 查找到的列表包含 "订单")
     *
     * @param arg
     * @return
     */
    @Path("FindReferences")
    @POST
    FindReferences.Result findReferences(FindReferences.Arg arg);

    /**
     * 查询数据负责人
     *
     * @param arg
     * @return
     */
    @Path("GetDataOwner")
    @POST
    GetDataOwner.Result getDataOwner(GetDataOwner.Arg arg);

    /**
     * 查询描述，包含关联对象
     *
     * @param arg
     * @returns
     */
    @Path("FindDescWithReference")
    @POST
    FindDescWithReference.Result findDescWithReference(FindDescWithReference.Arg arg);


    @Path("FindDataDescribeLayout")
    @POST
    FindDataDescribeLayout.Result findDataDescribeLayout(FindDataDescribeLayout.Arg arg);

    @Path("GetBpmTaskBusinessCodeFilterCriteria")
    @POST
    GetBpmTaskBusinessCodeFilterCriteria.Result getBpmTaskBusinessCodeFilterCriteria();
}
