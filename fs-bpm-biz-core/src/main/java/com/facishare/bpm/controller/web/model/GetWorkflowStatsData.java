package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.WorkflowStats;
import com.facishare.bpm.utils.i18n.CustomI18NHandler;
import com.facishare.bpm.utils.i18n.I18NExpression;
import lombok.*;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * Created by wangz on 17-5-3.
 */
public interface GetWorkflowStatsData {
    @Data
    class Arg {
        @NotEmpty(message = "流程id不能为空")
        String sourceWorkflowId;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @I18NExpression(custom = CustomI18NHandler.svg)
        private String svg;
        private List<WorkflowStats> data;
        private WorkflowOutline outline;
        public Result(List<WorkflowStats> data){
            this.data = data;
        }
    }

}
