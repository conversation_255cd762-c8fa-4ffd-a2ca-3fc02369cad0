package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/18 2:35 PM
 */
public interface GetTaskInfo {

    @Data
    class Arg {
        private String id;

        /**
         * 780 新增,应用节点下发多个按钮
         */
        private Boolean applyButtons;

        private Boolean notGetData;
        /**
         * 是否包含任务Feed详情是否展示的配置
         */
        private Boolean includeTaskFeedDetailConfig;

        public Boolean getApplyButtons() {
            return applyButtons != null && applyButtons;
        }
    }

    @Data
    class Result {
        private String id;
        private String name;
        private String description;
        private String laneName;
        private String laneId;
        private TaskState state;
        private String applicantId;
        private String workflowId;
        private String workflowInstanceId;
        private String entityId;
        private String objectId;
        private List<String> candidateIds;
        private List<String> processIds;
        @DocDescribe(desc = "1-天；2-小时；3-分钟")
        private int latencyUnit;
        private Object remindLatency;
        private boolean isTimeout;
        private ExecutionTypeEnum executionType;
        private String taskType;
        private AfterActionExecution.SimpleAfterActionExecution execution;
        private List<Opinion> opinions;
        private List<Task.ApproverModifyLog> candidateModifyLog;

        private String errorMsg;
        @DocDescribe(desc = "是否需要指定下一节点处理人1 指定，非1 不指定")
        private int assignNextTask;
        private Set<String> nextTaskAssigneeScope;
        /**
         * addteammember:[
         * actionCode:addteammember
         * actionlable:添加团队成员
         * actionRight:true/false
         * ]
         */
        private Map<String, ActionButton> button;

        private StandardData data;

        private List<MoreOperationManager.MoreOperation> moreOperations;

        private Long createTime;

        private Long modifyTime;
        private String activityId;

        private int activityInstanceId;

        private String linkAppName;
        private Integer linkAppType;
        private String linkApp;
        private Boolean linkAppEnable;

        private Map<String, Employee> employeeInfo;

        private boolean isOwner;
        private String todoJumpUrl;

        private Map<String, Object> externalApply;
        private List<PoolEntity> pools;
        /**
         * 流程有关的配置
         */
        private Map<String,Object> config;
        private String message;

        private Long suspendAccumulateTime;
        private Long lastSuspendTime;
        /**
         * 元素配置信息 880
         */
        public Map customExtension;
        public FlowElementPluginWrapper elementPluginConfig;
        public String nodeType;
        private String workflowName;
        private Long instanceStartTime;
        private List<Task.RemindLog> remindLogs;

        public void elementConfigData(Map customExtension,FlowElementPluginWrapper elementConfig){
            this.customExtension=customExtension;
            this.elementPluginConfig = elementConfig;
        }

        public void setCandidateIds(List<String> candidateIds) {
            this.candidateIds = Lists.newArrayList();
            if (CollectionUtils.isEmpty(candidateIds)) {
                return;
            }
            for (String candidateId : candidateIds) {
                if (!this.candidateIds.contains(candidateId)) {
                    this.candidateIds.add(candidateId);
                }
            }
        }

        public Result(RefServiceManager refServiceManager, TaskDetail taskDetail) {
            this.id = taskDetail.getId();
            this.name = taskDetail.getName();
            this.description = taskDetail.getDescription();
            this.laneName = taskDetail.getLaneName();
            this.state = taskDetail.getState();
            this.applicantId = taskDetail.getApplicantId();
            this.workflowId = taskDetail.getWorkflowId();
            this.workflowInstanceId = taskDetail.getWorkflowInstanceId();
            this.entityId = taskDetail.getEntityId();
            this.objectId = taskDetail.getObjectId();
            setCandidateIds(taskDetail.getCandidateIds());
            this.processIds = taskDetail.getProcessIds();
            this.latencyUnit = taskDetail.getLatencyUnit();
            this.remindLatency = taskDetail.getRemindLatency();
            this.isTimeout = taskDetail.isTimeout();
            this.executionType = taskDetail.getExecutionType();
            this.taskType = taskDetail.getTaskType();
            this.execution = taskDetail.getExecution();
            this.errorMsg = taskDetail.getErrorMsg();
            this.assignNextTask = taskDetail.getAssignNextTask();
            this.nextTaskAssigneeScope = taskDetail.getNextTaskAssigneeScope();
            this.button = taskDetail.getButton();
            this.moreOperations = taskDetail.getMoreOperations();
            this.data = taskDetail.getData();
            this.createTime = taskDetail.getCreateTime();
            this.modifyTime = taskDetail.getModifyTime();
            this.activityId = taskDetail.getActivityId();
            this.laneId = taskDetail.getLaneId();
            this.activityInstanceId = taskDetail.getActivityInstanceId();
            // 审批意见和修改记录提取出来
            this.opinions = taskDetail.getOpinions();
            this.candidateModifyLog = taskDetail.getCandidateModifyLog();
            //如果是应用节点，将linkAppName隐藏
            if(!taskDetail.isExternalApplyTask()) {
                this.linkAppName = refServiceManager.getI18nLinkAppName(taskDetail.getLinkApp(), taskDetail.getLinkAppName());
            }
            this.linkApp = taskDetail.getLinkApp();
            this.linkAppType = taskDetail.getLinkAppType();
            this.linkAppEnable = taskDetail.getLinkAppEnable();
            this.employeeInfo = taskDetail.getEmployeeInfo();
            this.isOwner = taskDetail.getIsOwner();
            this.externalApply = taskDetail.getExternalApply();
            this.pools = taskDetail.getPools();
            this.config=taskDetail.getConfig();
            if (Objects.nonNull(data)) {
                this.todoJumpUrl = data.getTodoJumpUrl();
            }
            this.message=taskDetail.getMessage();
            this.suspendAccumulateTime = taskDetail.getSuspendAccumulateTime();
            this.lastSuspendTime = taskDetail.getLastSuspendTime();
            this.elementConfigData(taskDetail.getCustomExtension(),taskDetail.getElementPluginConfig());
            this.nodeType = taskDetail.getNodeType();
            this.workflowName = taskDetail.getWorkflowName();
            this.instanceStartTime = taskDetail.getInstanceStartTime();
            this.remindLogs = taskDetail.getRemindLogs();
        }
    }


}
