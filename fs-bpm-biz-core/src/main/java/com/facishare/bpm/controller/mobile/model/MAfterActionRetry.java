package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.model.paas.engine.bpm.AfterRetry;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

public interface MAfterActionRetry {
    @Data
    class Arg{
        @JSONField(name = "M1")
        @NotEmpty(message = "任务id不能为空")
        private String taskId;
        @JSONField(name = "M2")
        private int rowNum;
        /**
         * 0:忽略 * 1：执行
         */
        @JSONField(name = "M3")
        private int executeType;
    }
    @Data
    class Result{
        @J<PERSON>NField(name = "M1")
        private boolean success;
        @JSONField(name = "M2")
        private String message;

        public Result(AfterRetry.RetryResult result) {
            this.success=result.isSuccess();
            this.message=result.getMessage();
        }
    }
}
