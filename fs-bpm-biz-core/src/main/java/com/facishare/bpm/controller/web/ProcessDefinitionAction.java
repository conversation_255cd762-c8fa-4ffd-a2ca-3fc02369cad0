package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.web.model.*;
import com.facishare.rest.ext.annotation.CepAPI;
import com.facishare.rest.ext.common.RestConstant;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * 流程定义接口
 * Created by cuiyongxu on 16/12/21.
 */
@Path("BPM/ProcessDefinition")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@CepAPI(flowType = RestConstant.FlowType.BPM)
public interface ProcessDefinitionAction {

    /**
     * 流程的创建
     * @param arg
     * @return
     */
    @Path("CreateDefinition")
    @POST
    CreateDefinition.Result createDefinition(CreateDefinition.Arg arg);


    /**
     * 流程的更新
     * @param arg
     * @return
     */
    @Path("UpdateDefinition")
    @POST
    UpdateDefinition.Result updateDefinition(UpdateDefinition.Arg arg);

    /**
     * 流程的更新
     * @param arg
     * @return
     */
    @Path("UpdateHistory")
    @POST
    UpdateDefinition.Result updateHistory(UpdateDefinition.Arg arg);


    /**
     * 删除流程定义
     * @param arg
     * @return
     */
    @Path("DeleteDefinition")
    @POST
    DeleteDefinition.Result deleteDefinition(DeleteDefinition.Arg arg);

    /**
     * 查询流程定义列表
     * @param arg
     * @return
     */
    @Path("GetDefinitionList")
    @POST
    GetDefinitionList.Result getDefinitionList(GetDefinitionList.Arg arg);

    /**
     * 查询流程定义列表
     * @param arg
     * @return
     */
    @Path("GetDefinitionHistoryList")
    @POST
    GetDefinitionHistoryList.Result getHistoryList(GetDefinitionHistoryList.Arg arg);



    /**
     * 查询流程定义列表
     * @param arg
     * @return
     */
    @Path("GetDefinitionByWorkflowId")
    @POST
    GetDefinitionByWorkflowId.Result getDefinitionByWorkflowId(GetDefinitionByWorkflowId.Arg arg);

    /**
     * 查询流程定义详情
     * @param arg
     * @return
     */
    @Path("GetDefinition")
    @POST
    GetDefinition.Result getDefinition(GetDefinition.Arg arg);


    /**
     * 更新流程定义状态
     * @param arg
     * @return
     */
    @Path("UpdateDefinitionStatus")
    @POST
    UpdateDefinitionStatus.Result updateDefinitionStatus(UpdateDefinitionStatus.Arg arg);


    /**
     * 查询当前对象（入口）下可用的流程（同时 对该用户可用）
     * @param arg
     * @return
     */
    @Path("GetAvailableWorkflows")
    @POST
    GetAvailableWorkflows.Result getAvailableWorkflows(GetAvailableWorkflows.Arg arg);



    /**
     * 查询当前人下定义的流程（同时 对该用户可用）,阶段视图
     * @param arg
     * @return
     */
    @Path("GetAllAvailableWorkflows")
    @POST
    GetAllAvailableWorkflows.Result getAllAvailableWorkflows(GetAllAvailableWorkflows.Arg arg);


    /**
     * 获取最新的流程定义的阶段信息
     * @param arg
     * @return
     */
    @Path("GetLanesOfWorkflowSourceId")
    @POST
    GetLanesOfWorkflowSourceId.Result getLanesOfWorkflowSourceId(GetLanesOfWorkflowSourceId.Arg arg);

    /**
     * 查询App activity 上相关的应用和事件
     * @return
     */
    @Path("GetAppActions")
    @POST
    GetActivityAppActions.Result getAppActions(GetActivityAppActions.Arg arg);


    /**
     * 查询当前用户的所有配额
     * @param arg
     * @return
     */
    @Path("GetQuota")
    @POST
    GetQuota.Result getQuota(GetQuota.Arg arg);


    /**
     * 查询节点的定义
     * @param arg
     * @return
     */
    @Path("GetActivityDefByActivityId")
    @POST
    GetActivityDefByActivityId.Result getActivityDefByActivityId(GetActivityDefByActivityId.Arg arg);


    /**
     * 业务流程实例列表页顶部,用来进行对象筛选
     * @return
     */
    @Path("GetBPMUseApiNames")
    @POST
    GetBPMUseApiNames.Result getBPMUseApiNames();

    /**
     * 查询流程定义列表(简要信息)
     *
     * @param arg
     * @return
     */
    @Path("GetWorkflowLogs")
    @POST
    GetWorkflowLogs.Result getWorkflowLogs(GetWorkflowLogs.Arg arg);

    @Path("GetSkipPageFromTodo")
    @POST
    GetSkipPageFromTodo.Result getSkipPageFromTodo(GetSkipPageFromTodo.Arg arg);

    @Path("UpdateSkipPageFromTodo")
    @POST
    UpdateSkipPageFromTodo.Result updateSkipPageFromTodo(UpdateSkipPageFromTodo.Arg arg);
}
