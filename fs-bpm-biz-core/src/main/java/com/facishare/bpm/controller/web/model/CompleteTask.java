package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.task.CompleteTaskResult;
import com.facishare.bpm.model.task.NextTask;
import com.facishare.bpm.rule.RuleMessage;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;
import java.util.Objects;

/**
 * Created by cuiyongxu on 17/1/3.
 */
public interface CompleteTask {
    @Data
    class Arg {
        @NotEmpty(message = "taskId不能为空")
        String taskId;
        String opinion;
        String objectId;
        Map<String, Object> data;
        Map<String, Object> nextTaskAssignee;
        /**
         * (1:添加/0：替换)
         */
        Integer addOrReplaceNextTaskAssignee;
        /**
         * 是否要获取下一个任务（true-是，false或null-否）
         */
        Boolean needGetNextTaskInfo;
        /**
         * 忽略非阻断异常，true——忽略非阻断异常
         */
        Boolean ignoreNoBlockValidate;

        public Boolean getNeedGetNextTaskInfo() {
            if (Objects.isNull(needGetNextTaskInfo)) {
                return Boolean.FALSE;
            }
            return needGetNextTaskInfo;
        }
    }

    @Data
    @NoArgsConstructor
    class Result {
        boolean result;
        String msg;
        RuleMessage ruleMessage;
        Integer sleepTime;
        NextTask nextTask;
        String nonBlockMessage;
        String blockMessage;

        public static Result fail(String blockMessage, String nonBlockMessage) {
            Result result = new Result();
            result.setBlockMessage(blockMessage);
            result.setNonBlockMessage(nonBlockMessage);
            result.setResult(false);
            return result;
        }

        public Result(boolean result, String msg, CompleteTaskResult completeTaskResult) {
            this.result = result;
            this.msg = msg;
            if (Objects.nonNull(completeTaskResult)) {
                this.ruleMessage = completeTaskResult.getRuleMessage();
                this.sleepTime = completeTaskResult.getSleepTime();
                this.nextTask = completeTaskResult.getNextTask();
            }
        }
    }

}
