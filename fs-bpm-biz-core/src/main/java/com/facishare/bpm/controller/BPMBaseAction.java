package com.facishare.bpm.controller;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.ext.RestRequest;
import com.fxiaoke.i18n.client.I18nClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.annotation.PostConstruct;
import javax.validation.ValidationException;
import java.util.Objects;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY;

/**
 * action 基类 Created by cuiyongxu on 16/12/23.
 */
@Slf4j
public class BPMBaseAction extends BPMBaseService {

    /**
     * 国际化平台key的tag， 老对象、自定义对象、字段
     */
    public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";

    @PostConstruct
    public void init(){
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);
    }


    public RemoteContext initRemoteContext() {
        RestRequest restRequest = RestRequest.getLocal();
        RemoteContext remoteContext = restRequest.getCepContext().getRemoteContext();
        remoteContext.setAppId(BPMConstants.APP_ID);
        if(Objects.nonNull(remoteContext.getClientInfo())
                && StringUtils.isBlank(remoteContext.getClientInfo().getFullStr())){
            remoteContext.getClientInfo().setFullStr(restRequest.getCepContext().getClientInfo());
        }
        String upstreamOwnerId = remoteContext.getUpstreamOwnerId();
        long outerUserId = remoteContext.getOuterUserId();
        long outerTenantId = remoteContext.getOuterTenantId();
        if(StringUtils.isNotBlank(upstreamOwnerId)
                && !"0".equals(upstreamOwnerId)
                && outerUserId != 0
                && outerTenantId != 0){
            remoteContext.setUserId(upstreamOwnerId);
        }
        return remoteContext;
    }

    public RefServiceManager getManager(){
        return getServiceManager(initRemoteContext());
    }


    protected void validateArg(Object arg){
        if(Objects.isNull(arg)){
            throw new ValidationException(BPMI18N.getI18NByName(PAAS_FLOW_BPM_PARAMETER_ANOMALY));
        }
    }
}
