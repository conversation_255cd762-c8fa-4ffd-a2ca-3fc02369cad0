package com.facishare.bpm.controller.web.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;


/**
 * 启动流程实例 Created by cuiyongxu on 16/12/22.
 */
public interface StartInstance {
    @Data
    class Arg {
        @NotEmpty(message = "流程定义id不能为空")
        String id;//定义id,启动流程实例时需要
        @NotEmpty(message = "入口对象id不能为空")
        String objectId;// 流程入口对象
    }

    @Data
    @NoArgsConstructor
    class Result {
        protected String result;
        public Result(String result){
            this.result = result;
        }
    }
}
