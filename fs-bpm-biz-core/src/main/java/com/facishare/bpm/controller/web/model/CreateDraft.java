package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 创建流程定义 Created by cuiyongxu on 16/12/21.
 */
public interface CreateDraft {
    @Data
    class Arg extends WorkflowOutlineDraft {
        public static Arg getInstance(WorkflowOutlineDraft outline) {
            return BeanUtils.transfer(outline, Arg.class);
        }

        @Override
        public String toString() {
            return super.toString();
        }
    }

    @Data
    class Result {
        String id;

        public Result(String id) {
            this.id = id;
        }
    }
}
