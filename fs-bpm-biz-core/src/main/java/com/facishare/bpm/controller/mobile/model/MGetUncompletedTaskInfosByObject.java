package com.facishare.bpm.controller.mobile.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.bpm.annotations.Descriable;
import com.facishare.bpm.model.task.LaneTask;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Objects;

/**
 * Created by cuiyongxu on 2020-04-13.
 */
public interface MGetUncompletedTaskInfosByObject {
    @Data
    class Arg {
        @Descriable(value = "自定义对象apiName", required = true)
        @JSONField(name = "M1")
        @NotEmpty(message = "对象类型不能为空")
        private String apiName;
        @Descriable(value = "自定义对象id", required = true)
        @NotEmpty(message = "记录id不能为空")
        @JSONField(name = "M2")
        private String objectId;
        /**
         * 780 新增,应用节点下发多个按钮
         */
        private Boolean applyButtons;
        //不需要下发数据、描述、描述扩展及form的标识位 true-不获取；false或null 获取
        private Boolean notGetDatas;

        private Boolean isMobile;//是否是端上过来的 true-是

        public Boolean getApplyButtons() {
            return applyButtons != null && applyButtons;
        }
    }

    @Data
    class Result {
        @Descriable(value = "对象下的待办任务列表")
        @JSONField(name = "M1")
        private List<LaneTask> dataList;

        public Result(List<LaneTask> outlines, Boolean isMobile) {
            if (Boolean.TRUE.equals(isMobile) && CollectionUtils.isNotEmpty(outlines)){
                for (LaneTask outline : outlines) {
                    if(Objects.nonNull(outline) && Objects.nonNull(outline.getStandardData())){
                        outline.getStandardData().setData(null);
                        outline.getStandardData().setDescribe(null);
                        outline.getStandardData().setDescribeExt(null);
                    }
                }
            }
            this.dataList = outlines;
        }
    }
}
