package com.facishare.bpm.controller.web.model;

import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 7.9.0
 * Created by cuiyongxu on 2021年11月23日10:28:02
 */
public interface CompleteAndCreateTaskData {

    @Slf4j
    @Data
    @ToString(callSuper = true)
    class Arg extends CreateTaskData.Arg {
        String opinion;
        String objectId;
        Map<String, Object> nextTaskAssignee;
        /**
         * (1:添加/0：替换)
         */
        Integer addOrReplaceNextTaskAssignee;
        /**
         * 是否要获取下一个任务（true-是，false或null-否）
         */
        Boolean needGetNextTaskInfo;
        /**
         * true:忽略非阻断异常，false或null：不忽略
         */
        Boolean ignoreNoBlockValidate;

        public Boolean getNeedGetNextTaskInfo() {
            if (Objects.isNull(needGetNextTaskInfo)) {
                return Boolean.FALSE;
            }
            return needGetNextTaskInfo;
        }
    }
}
