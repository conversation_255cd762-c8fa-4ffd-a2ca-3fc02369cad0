package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BPMBaseAction;
import com.facishare.bpm.controller.web.ProcessDefinitionAction;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMParamsException;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowLogs;
import com.facishare.bpm.model.resource.app.GetAppActions;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.proxy.I18NServiceProxy;
import com.facishare.bpm.remote.app.ErAppProxy;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMTenantService;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.BeanConvertUtil;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.REPLACE_WHEN_NOT_FOUND;

/**
 * 流程定义实现 Created by cuiyongxu on 16/12/26.
 */
@Slf4j
@Service
public class ProcessDefinitionActionImpl extends BPMBaseAction implements ProcessDefinitionAction {
    @Autowired
    private BPMDefinitionService definitionService;

    @Autowired
    private BPMTenantService tenantService;

    @Autowired
    private ErAppProxy erAppProxy;

    @Autowired
    private I18NServiceProxy i18NServiceProxy;


    /**
     * 发布流程定义
     */
    @Override
    public CreateDefinition.Result createDefinition(CreateDefinition.Arg arg) {
        validateArg(arg);
        //调用创建接口如果id不为空,则可能是草稿转过来的
        RefServiceManager serviceManager = getManager();
        if (!Strings.isNullOrEmpty(arg.getId())) {
            //如果草稿不为空,则表示有草稿
            arg.setDraftId(arg.getId());
        }
        arg.setId(null);
        log.debug("createDefinition start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());

        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        return new CreateDefinition.Result(definitionService.deployWorkflow(serviceManager, arg, true, true));
    }

    /**
     * 更新流程定义
     */
    @Override
    public UpdateDefinition.Result updateDefinition(UpdateDefinition.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        Workflow.isBlank(arg.getId(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        log.debug("updateDefinition start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        definitionService.updateWorkflow(serviceManager, arg, true, true);
        return new UpdateDefinition.Result();
    }

    @Override
    public UpdateDefinition.Result updateHistory(UpdateDefinition.Arg arg) {
        validateArg(arg);
        Workflow.isBlank(arg.getId(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        RefServiceManager serviceManager = getManager();
        log.debug("updateHistoryDefinition start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        definitionService.updateHistory(serviceManager, arg);
        return new UpdateDefinition.Result();
    }

    /**
     * 删除流程定义
     */
    @Override
    public DeleteDefinition.Result deleteDefinition(DeleteDefinition.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("deleteDefinition start : tenantId={}, appId={}, userId={}, outlineId={}", serviceManager.getTenantId(),
                serviceManager.getAppId(), serviceManager.getUserId(), arg.getId());
        boolean flag = false;
        if (StringUtils.isNotEmpty(arg.getId())) {
            flag = definitionService.deleteWorkflowById(serviceManager, arg.getId());
        } else {
            log.error("流程定义Id为空");
        }
        return new DeleteDefinition.Result(flag);
    }

    /**
     * 获取流程定义
     */
    @Override
    public GetDefinitionList.Result getDefinitionList(GetDefinitionList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("GetDefinitionList start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        PageResult<WorkflowOutline> ret = definitionService.getWorkflowOutlines(serviceManager, arg.createWorkflowOutlineQuery(), arg.getPage(), arg.getFrom(), arg.isSupportPagingQuery());
        return new GetDefinitionList.Result(ret.getTotal(), ret.getDataList());
    }

    /**
     * 查询流程定义列表
     */
    @Override
    public GetDefinitionHistoryList.Result getHistoryList(GetDefinitionHistoryList.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        // 920 使用了 flow 项目的  GetDefinitionHistoryBySrcId 接口
        return new GetDefinitionHistoryList.Result(Lists.newArrayList());
    }

    /**
     * 查询流程定义列表
     */
    @Override
    public GetDefinitionByWorkflowId.Result getDefinitionByWorkflowId(GetDefinitionByWorkflowId.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("GetDefinitionByWorkflowId start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        WorkflowOutline workflowOutline = definitionService.getDefinitionByWorkflowId(serviceManager, arg.getWorkflowId());
        I18NParser.parse(serviceManager.getTenantId(),workflowOutline);
        return new GetDefinitionByWorkflowId.Result(workflowOutline);
    }

    /**
     * 查询流程定义详情
     */
    @Override
    public GetDefinition.Result getDefinition(GetDefinition.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        log.debug("GetDefinition start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        String id = arg.getId();
        if (StringUtils.isEmpty(id)) {
            log.info("流程定义id不能为空");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        WorkflowOutline workflowOutline = definitionService.getWorkflowOutlineById(serviceManager, id);
        if (REPLACE_WHEN_NOT_FOUND.equals(workflowOutline.getEntryTypeName()) || StringUtils.isBlank(workflowOutline.getEntryTypeName())) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_INLET_OBJECT_DISABLE_OR_DELETE);
        }
        Pair<Map<String, String>, Map<String, String>> translate = i18NServiceProxy.getDefTranslateList(serviceManager.getTenantId(), workflowOutline.getSourceWorkflowId());
        workflowOutline.setNameTranslateInfo(translate.getKey());
        workflowOutline.setDescTranslateInfo(translate.getValue());
        return new GetDefinition.Result(workflowOutline);
    }


    @Override
    public GetQuota.Result getQuota(GetQuota.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("getQuota start : tenantId={}, userId={}", serviceManager.getTenantId(), serviceManager.getUserId());

        return new GetQuota.Result(tenantService.getQuota(serviceManager));
    }

    /**
     * 更新流程定义状态
     */
    @Override
    public UpdateDefinitionStatus.Result updateDefinitionStatus(UpdateDefinitionStatus.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        //目前不支持批量修改状态get(0)
        boolean flag = definitionService.enableWorkflow(serviceManager, arg.getIds(), arg.isEnabled());
        return new UpdateDefinitionStatus.Result(flag);
    }


    /**
     * 查询当前对象下可用的流程
     */
    @Override
    public GetAvailableWorkflows.Result getAvailableWorkflows(GetAvailableWorkflows.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        if (Objects.isNull(arg)) {
            throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_INCORRECT);
        }

        List<WorkflowOutline> outlines = definitionService.getAvailableWorkflows(serviceManager, arg.getEntryType(), arg.getObjectId(), true, 0, arg.getValidateScope(), Boolean.FALSE);
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        return new GetAvailableWorkflows.Result(outlines);
    }


    /**
     * 查询当前用户下可用的流程
     */
    @Override
    public GetAllAvailableWorkflows.Result getAllAvailableWorkflows(GetAllAvailableWorkflows.Arg arg) {
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> outlines = definitionService.getAvailableWorkflows(serviceManager, null, null, false, null, null, Boolean.TRUE);
        GetAllAvailableWorkflows.Result result = new GetAllAvailableWorkflows.Result();
        I18NParser.parse(serviceManager.getTenantId(),outlines);
        if (!CollectionUtils.isEmpty(outlines)) {
            outlines.forEach(item -> result.add(GetAllAvailableWorkflows.AvailableWorkflow.fromWorkflowOutline(item)));
        }
        log.debug("getAllAvailableWorkflows:{}", JsonUtil.toJson(outlines));
        return result;
    }

    @Override
    public GetLanesOfWorkflowSourceId.Result getLanesOfWorkflowSourceId(GetLanesOfWorkflowSourceId.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();

        String sourceWorkflowId = arg.getSourceWorkflowId();
        if (Strings.isNullOrEmpty(sourceWorkflowId)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY);
        }
        WorkflowOutline outline = definitionService.getWorkflowOutlineBySourceId(serviceManager, sourceWorkflowId);
        List<PoolEntity> list = outline.getExtension().getPools();
        GetLanesOfWorkflowSourceId.Result result = new GetLanesOfWorkflowSourceId.Result();
        List<Map<String, Object>> activities = (List<Map<String, Object>>) outline.getWorkflow().get("activities");
        Map<String, Map<String, Object>> activitiesMap = Maps.newHashMap();
        activities.forEach(item -> {
            activitiesMap.put(item.get("id") + "", item);
        });
        for (PoolEntity poolEntity : list) {
            poolEntity.getLanes().forEach(laneEntity -> {
                GetLanesOfWorkflowSourceId.LaneDetail laneDetail = new GetLanesOfWorkflowSourceId.LaneDetail();
                laneDetail.setLaneId(laneEntity.getId());
                laneDetail.setName(laneEntity.getName());
                result.add(laneDetail);

                laneEntity.getActivities().forEach(item -> {
                    Map<String, Object> activity = activitiesMap.get(item);
                    GetLanesOfWorkflowSourceId.LaneActivity laneActivity = new GetLanesOfWorkflowSourceId.LaneActivity();
                    laneActivity.setName(activity.get("name") + "");
                    laneActivity.setActivityId(activity.get("id") + "");
                    if ("userTask".equals(activity.get("type"))) {
                        laneDetail.getActivities().add(laneActivity);
                    }
                });
            });
        }

        return result;
    }

    @Override
    public GetActivityAppActions.Result getAppActions(GetActivityAppActions.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        GetAppActions.Result appActions = new GetAppActions.Result();

        if (StringUtils.isBlank(arg.getApiName())) {
            return new GetActivityAppActions.Result(appActions);
        }
        return new GetActivityAppActions.Result(erAppProxy.getAppActions(serviceManager.getTenantId(),serviceManager.getUserId(), arg.getApiName(), arg.getExternalFlow(), arg.getAppId(), arg.getAppType()));
    }

    @Override
    public GetActivityDefByActivityId.Result getActivityDefByActivityId(GetActivityDefByActivityId.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        GetActivityDefByActivityId.Result result = new GetActivityDefByActivityId.Result();
        result.setDefinition(definitionService.getActivityDefByActivityId(serviceManager, arg.getWorkflowId(), arg.getActivityId()));
        return result;
    }

    @Override
    public GetBPMUseApiNames.Result getBPMUseApiNames() {
        RefServiceManager serviceManager = getManager();
        Map<String, String> useApinames = definitionService.getUseApiNames(serviceManager);
        return new GetBPMUseApiNames.Result(useApinames);
    }

    @Override
    public GetWorkflowLogs.Result getWorkflowLogs(GetWorkflowLogs.Arg arg) {
        RefServiceManager serviceManager = getManager();
        validateArg(arg);
        WorkflowLogs.WorkflowLogsPage workflowLogs = definitionService.getWorkflowLogs(serviceManager, arg.getSourceWorkflowId(), arg.getPage());
        I18NParser.parse(serviceManager.getTenantId(),workflowLogs.getFlowList());
        return new GetWorkflowLogs.Result(workflowLogs);
    }

    @Override
    public GetSkipPageFromTodo.Result getSkipPageFromTodo(GetSkipPageFromTodo.Arg arg) {
        RefServiceManager serviceManager = getManager();
        return new GetSkipPageFromTodo.Result(tenantService.getSkipPageFromToDo(serviceManager.getTenantId()));
    }

    @Override
    public UpdateSkipPageFromTodo.Result updateSkipPageFromTodo(UpdateSkipPageFromTodo.Arg arg) {
        validateArg(arg);
        RefServiceManager serviceManager = getManager();
        tenantService.updateSkipPageFromToDo(serviceManager.getTenantId(),arg.getSkipPageFormToDo());
        return new UpdateSkipPageFromTodo.Result();
    }
}
