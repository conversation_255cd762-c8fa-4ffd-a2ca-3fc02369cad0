package com.facishare.bpm.controller.web.model;

import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import lombok.Data;

import java.util.List;

/**
 * 获取人的待办任务列表
 * Created by cuiyongxu on 16/12/22.
 */
public interface GetHandleTaskList {
    @Data
    class Arg {
        private int pageNumber ;
        private int pageSize ;
        private Boolean isCompleted; //true已完成，false未完成，null全部
        private String taskName;

        public Page createPage(){
            Page page = new Page();
            page.setPageSize(this.getPageSize());
            page.setPageNumber(this.getPageNumber());
            page.setOrderBy("createTime");
            page.setAsc(false);
            return page;
        }
    }

    @Data
    class Result {
        List<TaskOutline> dataList;
        int total;

        public Result(int total, List<TaskOutline> dataList) {
            this.total = total;
            this.dataList = dataList;
        }
    }
}
