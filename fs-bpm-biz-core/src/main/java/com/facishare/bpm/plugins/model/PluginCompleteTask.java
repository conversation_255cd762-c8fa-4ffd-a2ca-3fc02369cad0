package com.facishare.bpm.plugins.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface PluginCompleteTask {
    @Data
    class Arg {
        @NotEmpty(message = "任务id不能为空")
        String taskId;
        String opinion;
        /**
         * reject/agree
         */
        String approvalResult;
        Map<String, Object> assignNextTaskProcessors;
        /**
         * (1:添加/0：替换)
         */
        Integer addOrReplaceNextTaskAssignee;

        Map<String,Object> data;
        /**
         * true:忽略非阻断异常，false或null：不忽略
         */
        Boolean ignoreNoBlockValidate;

    }


    @Data
    class CompleteTaskArg {
        @NotEmpty(message = "任务id不能为空")
        String taskId;

        Map<String, Object> assignNextTaskProcessors;
        /**
         * (1:添加/0：替换)
         */
        Integer addOrReplaceNextTaskAssignee;
        /**
         * true:忽略非阻断异常，false或null：不忽略
         */
        Boolean ignoreNoBlockValidate;

    }

}
