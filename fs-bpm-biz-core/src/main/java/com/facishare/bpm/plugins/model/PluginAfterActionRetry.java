package com.facishare.bpm.plugins.model;

import com.facishare.bpm.controller.web.model.AfterActionRetry;
import com.facishare.bpm.model.paas.engine.bpm.AfterRetry;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface PluginAfterActionRetry {
    @Data
    class Arg extends AfterActionRetry.Arg {
    }
    @Data
    class Result extends AfterActionRetry.Result {
        public Result(AfterRetry.RetryResult result) {
            super(result);
        }
    }
}
