package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.Opinion;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public interface GetInstance {
    static Result fromEntireInstance(WorkflowInstance workflowInstance, List<BPMTask> tasks, List<PoolEntity> pools) {
        Result result = new Result();
        result.setWorkflowName(workflowInstance.getWorkflowName());
        result.setApplicantId(workflowInstance.getApplicantId());
        result.setEntityId(workflowInstance.getEntityId());
        result.setObjectId(workflowInstance.getObjectId());
        result.setInstanceId(workflowInstance.getId());
        result.setTasks(PluginTask.from(tasks, pools));
        return result;
    }

    @Data
    class Arg {
        @NotEmpty(message = "实例id为空")
        String instanceId;

        private Integer pageSize;
        private Integer pageNumber;

        public Integer getPageSize() {
            return Objects.isNull(pageSize) ? 100 : pageSize;
        }

        public Integer getPageNumber() {
            return Objects.isNull(pageNumber) ? 1 : pageNumber;
        }
    }

    @Data
    class Result {
        private String instanceId;
        private String workflowName;
        private String objectId;
        private String entityId;
        private String applicantId;
        List<PluginTask> tasks;
    }

    @Data
    class PluginTask {
        private String taskId;
        private String taskName;
        private String laneName;// 阶段名称
        private String laneId;// 阶段名称
        private TaskState state;
        private boolean timeout;
        private List<Opinion> opinion;
        private AfterActionExecution.SimpleAfterActionExecution afterActionExecution;
        private Map<String,Object> extension;
        private Map<String, List<String>> assignee;

        static List<PluginTask> from(List<BPMTask> tasks, List<PoolEntity> pools) {
            return tasks.stream().map(task -> {
                PluginTask temp = new PluginTask();
                temp.setTaskName(task.getName());
                temp.setTimeout(task.isTimeout());
                LaneEntity lane = PoolEntity.getLane(pools, task.getActivityId());
                temp.setLaneName(lane.getName());
                temp.setLaneId(lane.getId());
                temp.setOpinion(task.getOpinions());
                temp.setState(task.getState());
                if(Optional.ofNullable(task.getCompleted()).orElse(false).booleanValue()){
                    temp.setState(TaskState.pass);
                }
                temp.setAfterActionExecution(task.getExecution());
                temp.setExtension(task.getExtension());
                temp.setTaskId(task.getId());
                temp.setAssignee(task.getAssignee());
                return temp;
            }).collect(Collectors.toList());
        }
    }
}
