package com.facishare.bpm.plugins.model;

import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

import javax.validation.constraints.NotBlank;

public interface ConvertDefinitionFormOtherType {

    @Data
    class Arg {
        @DocDescribe(label = "outlineId")
        @NotBlank(message = "流程定义id不能为空")
        String id;

        @DocDescribe(label = "流程名称")//todo sourceWorkflowId 高俊
        @NotBlank(message = "流程定义名称不能为空")
        String name;
    }


    @Data
    class Result {
        @DocDescribe(label = "outlineId")
        String id;

        public Result(String id) {
            this.id = id;
        }
    }
}
