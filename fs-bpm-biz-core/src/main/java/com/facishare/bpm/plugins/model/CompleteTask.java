package com.facishare.bpm.plugins.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * Created by cuiyongxu on 17/1/3.
 */
public interface CompleteTask {
    @Data
    class Arg {
        @NotEmpty(message = "taskId不能为空")
        String taskId;
        String opinion;
        String objectId;
        Map<String,Object> data;
        Map<String,Object> nextTaskAssignee;
        /**
         *  (1:添加/0：替换)
         */
        Integer addOrReplaceNextTaskAssignee;
    }

    @Data
    class Result {
        boolean result;
        String msg;
        public Result(boolean result,String msg){
            this.result = result;
            this.msg=msg;
        }
    }

}
