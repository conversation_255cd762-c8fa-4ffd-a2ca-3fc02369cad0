package com.facishare.bpm.plugins.model;

import com.facishare.bpm.handler.task.form.model.CustomerData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.task.MTask;
import com.facishare.bpm.util.verifiy.TaskType;
import com.facishare.bpm.utils.ObjectId;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface GetPluginTask {
    @Data
    class Arg {
        @NotEmpty(message = "实例id不能为空")
        private String instanceId;
        @NotEmpty(message = "任务实例id不能为空")
        private String activityInstanceId;
    }

    @Data
    class Result {
        /**
         * 任务Id
         */
        private String id;
        /**
         * 候选人
         */
        private List<String> candidateIds;
        /**
         * 任务类型
         */
        private TaskType taskType;
        /**
         * 当前任务结点对象数据Id
         */
        private String objectId;
        /**
         * 当前任务结点对象类型")
         */
        private String entityId;
        /**
         * 审批意见
         **/
        private List<Opinion> opinions;
        /**
         * 审批人
         */
        private Map<String, List<String>> assignee;
        /**
         * 任务状态 in_progress,pass,cancel,error
         */
        private TaskState state;
        /**
         * 是否是处理人
         */
        private Boolean isTaskOwner; //是否是负责人
        /**
         * 执行类型
         */
        private ExecutionTypeEnum executionType;
        /**
         * 自定义对象数据，更新时会有值
         */
        private CustomerData data;

        /**
         * 任务扩展信息
         */
        private Collection<ActionButton> buttons;
        /**
         * 最后更新时间
         */
        private Long modifyTime;
        /**
         * 应用节点标示 1 是应用节点  0 null 为非应用节点
         */
        private Integer externalApplyTask;
        /**
         * 1 需要指定下一节点处理人   0 null 为不需要指定下一节点处理人
         */
        private Integer assignNextTask;
        /**
         * 当前任务创建时间
         */
        private Long createTime;
        /**
         * 异常提示
         */
        private String errorMsg;

        private AfterActionExecution.SimpleAfterActionExecution afterActionExecution;

        public Result(MTask task) {
            BeanUtils.copy(task, this);
            this.setAfterActionExecution(task.getExecution());
            this.setData(task.getData());
            this.setAssignee(task.getAssignee());
        }
    }
    @DocDescribe(label = "批量获取任务")
    @Data
    class GetTaskByIdsArg {
        @DocDescribe(label = "任务id列表， 最大100个")
        @NotEmpty
        Set<String> taskIds;

        public Set<String> getTaskIds() {
            taskIds=taskIds.stream().filter(taskId-> Objects.nonNull(taskId)&& ObjectId.isValid(taskId)).limit(100).collect(Collectors.toSet());
            return taskIds;
        }
    }
    @Data
    class TaskByIdsResult {
        private String id;
        private String name;
        private String description;
        private String applicantId;
        private Long createTime;
        private Long modifyTime;
        private List<String> assigneeIds;
        private String workflowInstanceId;
        private String workflowId;
        private String entityId;
        private String objectId;
        private String taskType;
        private Map<String, List<String>> assignee;
        private List<Opinion> opinions;
        private List<String> candidateIds;
        private String workflowName;
        private String workflowDescription;
        private TaskState state;
        private Integer assignNextTask;
        private String errMsg;//延迟节点错误信息

        public static List<TaskByIdsResult> of(List<Task> taskFromEngine) {
            return taskFromEngine.stream().map(item->BeanUtils.transfer(item, TaskByIdsResult.class)).collect(Collectors.toList());
        }
    }
}
