package com.facishare.bpm.plugins.model;


import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.resource.paas.PageResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;


public interface GetDefinitions {
    @Data
    class Arg {
        private int page;
        private int pageSize;
        private String entityId;
        private Long startQueryTime;
        private Boolean containAllFlowType;
    }

    @Data
    class Result {
        int total;
        int page;
        List<SimpleDefinition> data;

        public static Result fromOutline(PageResult<WorkflowOutline> outline) {
            Result result = new Result();
            result.setTotal(outline.getTotal());
            result.setData(SimpleDefinition.fromOutline(outline.getResult()));
            return result;
        }

        public static Result fromOutline(List<WorkflowOutline> outlines, int total) {
            Result result = new Result();
            result.setTotal(total);
            result.setData(SimpleDefinition.fromOutline(outlines));
            return result;
        }


        public Result setPage(int page) {
            this.page = page;
            return this;
        }
    }

    @Data
    @Builder
    class SimpleDefinition {
        private String id;
        private String workflowName;
        private String description;
        private String entityId;
        private String entityName;
        private Circle circle;
        private String createBy;
        private String lastModifiedBy;
        private Integer singleInstanceFlow;
        private Integer externalFlow;
        private int count;
        private boolean enable;
        private long createTime;
        private long lastModifiedTime;
        private String sourceWorkflowId;

        public static List<SimpleDefinition> fromOutline(List<WorkflowOutline> result) {
            return result.stream().map(
                    outline -> SimpleDefinition.builder().workflowName(outline.getName())
                            .id(outline.getId())
                            .entityId(outline.getEntryType())
                            .createBy(outline.getCreatedBy())
                            .enable(outline.isEnabled())
                            .createTime(outline.getCreateTime())
                            .singleInstanceFlow(outline.getSingleInstanceFlow())
                            .externalFlow(outline.getExternalFlow())
                            .circle(
                                    Circle.builder()
                                            .deptIds(outline.getRangeCircleIds())
                                            .employeeIds(outline.getRangeEmployeeIds())
                                            .crmGroupIds(outline.getRangeGroupIds())
                                            .crmRoleIds(outline.getRangeRoleIds())
                                            .build())
                            .entityName(outline.getEntryTypeName())
                            .count(outline.getCount())
                            .lastModifiedBy(outline.getLastModifiedBy()).lastModifiedTime(outline.getLastModifiedTime())
                            .sourceWorkflowId(outline.getSourceWorkflowId())
                            .description(outline.getDescription()).build()).collect(Collectors.toList());
        }
    }

    @Builder
    class Circle {
        private List<Integer> employeeIds;//可见人范围

        private List<Integer> deptIds;//可见部门范围

        private List<String> crmGroupIds; //可见组范围

        private List<String> crmRoleIds;//角色范围
    }

    @AllArgsConstructor
    @NoArgsConstructor
    class SimplestDefinition {
        String sourceWorkflowId;
        String workflowName;

        public static SimplestDefinition fromOutline(WorkflowOutline outline) {
            return new SimplestDefinition(outline.getSourceWorkflowId(), outline
                    .getName());
        }
    }
}