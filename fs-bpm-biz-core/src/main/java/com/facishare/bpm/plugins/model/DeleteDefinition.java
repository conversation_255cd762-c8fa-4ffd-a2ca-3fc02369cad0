package com.facishare.bpm.plugins.model;


import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 删除流程定义 Created by cuiyongxu on 16/12/22.
 */
public interface DeleteDefinition {
    @Data
    class Arg {
        @DocDescribe(label = "outlineId")
        @NotEmpty(message = "流程定义id不能为空")
        String id;
    }

    @Data
    class Result {
        boolean result = true;

        public Result(){}
        public Result(boolean result) {
            this.result = result;
        }
    }
}
