package com.facishare.bpm.plugins.model;

import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.util.WorkflowJsonUtil;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface GetLanes {
    @Data
    class Arg {
        /**
         * 流程outline id
         */
        @NotEmpty(message = "流程id不能为空")
        private String id;
    }

    @Data
    class Result {
        List<PluginLane> lanes;

        public Result(Map workflowMap, WorkflowExtension extension) {
            ExecutableWorkflowExt workflow = WorkflowJsonUtil.convertExecutableWorkflowFromJson(JsonUtil.toJson(workflowMap));
            List<PluginActivity> activities = workflow
                    .getActivities()
                    .stream()
                    .filter(activity -> activity.instanceOf(UserTaskExt.class))
                    .map(activity -> new PluginActivity(activity.getId(), activity.getName(), activity.getDescription(), activity.getExtension()))
                    .collect(Collectors.toList());

            this.lanes = extension
                    .getPools()
                    .stream()
                    .map(pool -> new PluginLane(pool.getLanes().get(0), activities))
                    .collect(Collectors.toList());
        }
    }

    @Data
    class PluginLane {
        private String id;
        private String name;
        private String description;
        List<PluginActivity> activities;

        public PluginLane(LaneEntity lane, List<PluginActivity> activities) {
            this.name = lane.getName();
            this.id = lane.getId();
            this.description = lane.getDescription();
            this.activities = activities.stream()
                    .filter(activity -> lane.getActivities().contains(activity.getActivityId()))
                    .collect(Collectors.toList());
        }
    }

    @Data
    class PluginActivity {
        private String activityId;
        private String activityName;
        private String description;
        private Map<String, Object> bpmExtension;

        public PluginActivity(String activityId, String activityName, String description,
                              Map<String, Object> bpmExtension) {
            this.activityId = activityId;
            this.activityName = activityName;
            this.description = description;
            this.bpmExtension = bpmExtension;
        }
    }
}
