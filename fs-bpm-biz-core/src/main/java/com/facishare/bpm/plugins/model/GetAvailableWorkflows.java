package com.facishare.bpm.plugins.model;

import com.facishare.bpm.controller.web.model.GetAvailableWorkflows.SimpleOutline;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/3/12 2:34 PM
 */
public interface GetAvailableWorkflows {

    @Data
    @ToString(callSuper = true)
    class Arg extends com.facishare.bpm.controller.web.model.GetAvailableWorkflows.Arg{

    }

    @Data
    class Result extends com.facishare.bpm.controller.web.model.GetAvailableWorkflows.Result{

        public Result(List<WorkflowOutline> outlines) {
            this.outlines=outlines.stream().map(outline-> BeanUtils.transfer(outline,SimpleOutline.class,(src,rst)->{
                rst.setSingleInstanceFlow(src.getSingleInstanceFlow()==null?0:src.getSingleInstanceFlow());
            })).collect(Collectors.toList());
        }
    }

}

