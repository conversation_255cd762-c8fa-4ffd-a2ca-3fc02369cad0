package com.facishare.bpm.plugins;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.plugins.model.*;
import com.facishare.bpm.service.impl.BPMWorkflowDraftServiceImpl;
import com.facishare.bpm.utils.BeanConvertUtil;
import com.facishare.bpm.utils.WorkflowPluginUtil;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.rest.ext.annotation.RestAPI;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.REPLACE_WHEN_NOT_FOUND;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/9 1:13 PM
 */
@DocDescribe(label = "草稿相关")
@Slf4j
@Service
@Path("plugins/draft")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RestAPI(flowType = RestConstant.FlowType.BPM)
public class DraftResource extends BaseBPMResource {


    @Autowired
    private BPMWorkflowDraftServiceImpl draftService;

    @GET
    @Path("/ping")
    public String ping() {
        return "ok";
    }



    @DocDescribe(label = "保存流程定义草稿",desc = "新增接口,暂无复用")
    @POST
    @Path("create")
    public CreateDraft.Result createDraft(CreateDraft.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowPluginUtil.checkBusinessCode(arg.getSupportFlow());
        log.debug("createDraft start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        String draftId = arg.getDraftId();
        // id不为空,但是draftd为空,表示有定义,保存草稿
        if (Strings.isNullOrEmpty(draftId) && !Strings.isNullOrEmpty(arg.getId())) {
            arg.setOutlineId(arg.getId());
        }
        arg.setId(null);
        return new CreateDraft.Result(draftService.saveWorkflowDraft(serviceManager, arg,arg.getSupportFlow().checkQuota));
    }



    @DocDescribe(label = "更新流程定义草稿",desc = "新增接口,暂无复用")
    @POST
    @Path("update")
    public UpdateDraft.Result updateDraft(UpdateDraft.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowPluginUtil.checkBusinessCode(arg.getSupportFlow());
        log.debug("updateDraft start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        //删除流程定义后要删除草稿,如果用户打开两个tabs,A页签将定义删除,因为定义删除后,草稿也同样删除了
        //B业务提交草稿保存时,需要查一下当前的草稿是否还存在,因为定义的删除导致草稿删除了,故添加此校验
        arg.setId(arg.getDraftId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        draftService.updateWorkflowDraft(serviceManager, arg);
        return new UpdateDraft.Result();
    }


    @DocDescribe(label = "删除流程定义草稿",desc = "新增接口,暂无复用")
    @POST
    @Path("delete")
    public DeleteDefinition.Result deleteDraft(DeleteDefinition.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("deleteDraft start : tenantId={}, appId={}, userId={}, outlineId={}",
                serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId(), arg.getId());
        boolean flag = false;
        if (StringUtils.isNotEmpty(arg.getId())) {
            draftService.delete(serviceManager, arg.getId());
            flag = true;
        } else {
            log.error("流程定义草稿Id为空");
        }
        return new DeleteDefinition.Result(flag);
    }


    @DocDescribe(label = "查询草稿列表",desc = "新增接口,暂无复用")
    @POST
    @Path("getList")
    public GetDraftList.Result getDraftList(GetDraftList.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowPluginUtil.checkBusinessCode(arg.getSupportFlow());
        log.debug("getDraftList start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        Page page = arg.getPage();
        String orderBy = page.getOrderBy();
        if (StringUtils.isEmpty(orderBy)) {
            page.setOrderBy(WorkflowOutlineEntity.Fields.lastModifiedTime);
        }
        WorkflowOutlineQuery workflowOutlineQuery = new WorkflowOutlineQuery();
        workflowOutlineQuery.setEntityId(arg.getEntityId());
        workflowOutlineQuery.setSupportFlowType(arg.getSupportFlow().code);
        String keyword = arg.getKeyWord();

        boolean isValid = !Strings.isNullOrEmpty(keyword) && (keyword.startsWith("?") || keyword.startsWith("*") || keyword.startsWith("+"));
        if (isValid) {
            workflowOutlineQuery.setName("\\" + keyword);
        }

        PageResult<WorkflowOutlineDraft> ret = draftService.getWorkflowDrafts(serviceManager, workflowOutlineQuery, page, arg.getSupportPagingQuery());
        return new GetDraftList.Result(ret.getTotal(), ret.getDataList());
    }




    @DocDescribe(label = "查询流程的草稿",desc = "新增接口,暂无复用")
    @POST
    @Path("getDraftById")
    public GetDraft.Result getDraftById(GetDraft.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowOutlineDraft outlineDraft = draftService.getWorkflowDraftById(serviceManager, arg.getId());
        if (REPLACE_WHEN_NOT_FOUND.equals(outlineDraft.getEntryTypeName()) || StringUtils.isBlank(outlineDraft.getEntryTypeName())) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_INLET_OBJECT_DISABLE_OR_DELETE);
        }
        return new GetDraft.Result(outlineDraft);
    }

}
