package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

/**
 * 创建流程定义 Created by cuiyongxu on 16/12/21.
 */
public interface CreateDefinition {
    @Data
    class Arg extends WorkflowOutline {
        @Override
        public String toString() {
            return super.toString();
        }
    }

    @Data
    class Result {
        @DocDescribe(label = "outlineId",desc = "业务流程自存储Id")
        String id;

        public Result(String id) {
            this.id = id;
        }
    }
}
