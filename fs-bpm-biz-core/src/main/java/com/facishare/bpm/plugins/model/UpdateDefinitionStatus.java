package com.facishare.bpm.plugins.model;


import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

public interface UpdateDefinitionStatus {
    @Data
    class Arg {
        @DocDescribe(label = "outlineId",desc = "起的名字有点恶心,是单值")
        @NotEmpty(message = "ids不能为空")
        String ids;//单个id
        @DocDescribe(label = "启用停用标识",desc = "true启用;false停用")
        boolean enabled; //状态
    }

    @Data
    class Result {
        boolean result = true;

        public Result(boolean result) {
            this.result = result;
        }
    }
}
