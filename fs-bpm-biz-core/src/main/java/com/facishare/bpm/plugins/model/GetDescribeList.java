package com.facishare.bpm.plugins.model;

import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public interface GetDescribeList {
    @Data
    class Arg {
        @Deprecated
        String packageName;
        List<String> apiNames;
        private Boolean useGroupManager;

        public Boolean getUseGroupManager() {
            return Boolean.TRUE.equals(useGroupManager);
        }
    }

    @Data
    class Result {
        List<PluginSimpleMetaDesc> descs;

        public Result(List<SimpleMetadataDesc> objs) {
            if (CollectionUtils.isNotEmpty(objs)) {
                descs = objs.stream().map(item ->
                        BeanUtils.transfer(item, PluginSimpleMetaDesc.class)).collect(Collectors.toList());
                return;
            }
            descs = Lists.newArrayList();
        }
    }

    @Data
    class PluginSimpleMetaDesc {
        private String displayName;
        private String objApiName;
        private String defineType;
        private Long lastModifiedTime;
    }
}