package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/7/30 3:36 PM
 */
public interface GetInstanceList {

    @Data
    class Arg {
        private String outlineId;
        private InstanceState state;
        private int pageNumber;
        private int pageSize;
        /**
         * 默认已开始时间逆序 start
         */
        private String orderBy = "start";
        private String entityId;
        private String objectId;


        public Page getPage() {
            Page page = new Page();
            page.setPageNumber(pageNumber);
            page.setPageSize(pageSize);
            page.setOrderBy(orderBy);
            page.setAsc(false);
            return page;
        }
    }

    @Data
    class Result {
        int total;
        List<WorkflowInstanceVO> workflowInstances;


        public Result(int total, List<WorkflowInstanceVO> workflowInstances) {
            this.total = total;
            this.workflowInstances = workflowInstances;

        }
    }
}
