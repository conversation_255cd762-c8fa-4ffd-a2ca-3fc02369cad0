package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 获取流程定义详情 Created by cuiyongxu on 17/1/3.
 */
public interface GetDefinition {
    @Data
    class Arg {
        @NotEmpty(message = "流程定义id不能为空")
        @DocDescribe(label = "outlineId")
        private String id;
    }

    @Data
    class Result {
        WorkflowOutline outline;


        public Result(WorkflowOutline workflowOutline) {
            this.outline = workflowOutline;
        }
    }
}
