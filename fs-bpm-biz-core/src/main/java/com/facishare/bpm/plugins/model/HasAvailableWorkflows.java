package com.facishare.bpm.plugins.model;

import lombok.Data;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/1 2:13 PM
 */
public interface HasAvailableWorkflows {

    @Data
    class Arg extends GetAvailableWorkflows.Arg {

        @Override
        public String toString() {
            return super.toString();
        }
    }

    @Data
    class Result {

        private boolean hasAvailable;

        public Result(boolean hasAvailable) {
            this.hasAvailable = hasAvailable;
        }
    }
}
