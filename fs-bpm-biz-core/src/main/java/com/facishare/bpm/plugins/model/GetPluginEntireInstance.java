package com.facishare.bpm.plugins.model;

import com.facishare.bpm.controller.mobile.model.MGetEntireWorkflow;
import com.facishare.bpm.model.instance.EntireWorkflow;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 5.7
 */
public interface GetPluginEntireInstance {
    @Data
    class Result{
        private Map<String,Object> workflow;
        private Map<String,Object> workflowInstance;
        private String svg;

        public Result(EntireWorkflow entireWorkflow) {
            MGetEntireWorkflow.Result result = new MGetEntireWorkflow.Result(entireWorkflow);
            this.workflow=result.getWorkflow();
            this.workflowInstance=result.getWorkflowInstance();
            this.svg=result.getSvg();
        }
    }
    @Data
    class Arg{
        @NotEmpty(message = "实例标示不能为空")
        private String instanceId;
    }
}
