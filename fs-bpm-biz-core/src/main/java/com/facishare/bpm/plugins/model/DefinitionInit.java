package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.Map;

/**
 * Created by wangzhx on 2018/11/20.
 */
public interface DefinitionInit {
    @Data
    class Arg {
        @DocDescribe(label = "模板Id", desc =
                "393216117345976320  外勤上门维修服务流程（示例）；\n" +
                "405678154806329344  外勤上门安装服务流程（示例）；\n" +
                "5ea6bcc6e98ea400014b14c7 外勤上门维修服务流程（示例）- 新；\n"+
                "5ea6bc200d0fd00001815bf7 外勤上门安装服务流程（示例）- 新； \n",
                required = true)
        @NotEmpty(message = "sourceWorkflowId 不能为空")
        private String sourceWorkflowId;

        @DocDescribe(desc = "预留", label = "预留")
        private Map<String, Object> variables;
    }

    @Data
    class Result {

        @DocDescribe(label = "定义对象")
        private WorkflowOutline outline;

        public Result(WorkflowOutline outline) {
            this.outline = outline;
        }
    }

}
