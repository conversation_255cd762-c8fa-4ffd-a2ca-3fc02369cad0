package com.facishare.bpm.plugins.handler;

import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.rest.RestResult;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.ext.RestRequest;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.rest.ext.exception.RestParamsSerializationException;
import com.facishare.rest.ext.handler.DefaultExceptionHandler;
import com.facishare.rest.ext.handler.RestExtExceptionMapper;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;

/**
 *
 * <AUTHOR>
 * @date 24/06/2017
 */
@Provider
@Component
public class BPMPluginsRestExceptionHandler extends RestExtExceptionMapper {
    @Override
    public Response handler(RestRequest request, Throwable throwable) {

        if(throwable instanceof BPMBusinessException){
            BPMBusinessException error = (BPMBusinessException) throwable;
            return RestResult.getRestResult(error.getErrorCode(),error.getMessage());
        }else if(throwable instanceof RestProxyBusinessException){
            RestProxyBusinessException error = (RestProxyBusinessException) throwable;
            return RestResult.getRestResult(error.getCode(),error.getMessage());
        }else if(throwable instanceof RestParamsSerializationException){
            RestParamsSerializationException error = (RestParamsSerializationException) throwable;
            return RestResult.getRestResult(error.getCode(),error.getMessage()+" "+error.getCause().getMessage());
        }
        return DefaultExceptionHandler.instance.handler(request,throwable);
    }

    @Override
    public String getFlowType() {
        return RestConstant.FlowType.BPM;
    }

}
