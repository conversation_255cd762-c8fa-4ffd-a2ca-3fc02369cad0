package com.facishare.bpm.plugins;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.manage.DefinitionInitManager;
import com.facishare.bpm.model.GetBPMLicense;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.plugins.model.*;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMTenantService;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.BeanConvertUtil;
import com.facishare.bpm.utils.WorkflowPluginUtil;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.rest.ext.annotation.RestAPI;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.REPLACE_WHEN_NOT_FOUND;

/**
 * <AUTHOR>
 * @since 5.7
 */
@DocDescribe(label = "定义相关")
@Slf4j
@Service
@Path("plugins/definition")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RestAPI(flowType = RestConstant.FlowType.BPM)

public class DefinitionResource extends BaseBPMResource {

    @Autowired
    private BPMDefinitionService definitionService;

    @Autowired
    private BPMTenantService tenantService;

    @Autowired
    private DefinitionInitManager definitionInitManager;


    @DocDescribe(label = "流程的创建", desc = "新增接口,暂无复用")
    @POST
    @Path("create")
    public CreateDefinition.Result createDefinition(CreateDefinition.Arg arg) {
        RefServiceManager serviceManager = getManager();

        if (StringUtils.isNoneBlank(arg.getId())) {
            arg.setDraftId(arg.getId());
        }
        arg.setId(null);
        WorkflowPluginUtil.checkBusinessCode(arg.getSupportFlow());
        log.debug("rest createDefinition start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        return new CreateDefinition.Result(definitionService.deployWorkflow(serviceManager, arg, arg.getSupportFlow().checkQuota,true));
    }

    @DocDescribe(label = "更新流程定义", desc = "新增接口,暂无复用")
    @POST
    @Path("update")
    public UpdateDefinition.Result updateDefinition(UpdateDefinition.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowPluginUtil.checkBusinessCode(arg.getSupportFlow());
        Workflow.isBlank(arg.getId(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        log.debug("rest updateDefinition start : tenantId={}, appId={}, userId={}.", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        BeanConvertUtil.toWorkflowOutline(arg, serviceManager.getContext());
        definitionService.updateWorkflow(serviceManager, arg, true, true);
        return new UpdateDefinition.Result();
    }


    @DocDescribe(label = "删除流程定义", desc = "新增接口,暂无复用")
    @POST
    @Path("delete")
    public DeleteDefinition.Result deleteDefinition(DeleteDefinition.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("deleteDefinition start : tenantId={}, appId={}, userId={}, outlineId={}", serviceManager.getTenantId(),
                serviceManager.getAppId(), serviceManager.getUserId(), arg.getId());
        boolean flag = false;
        if (StringUtils.isNotEmpty(arg.getId())) {
            flag = definitionService.deleteWorkflowById(serviceManager, arg.getId());
        } else {
            log.error("流程定义Id为空");
        }
        return new DeleteDefinition.Result(flag);
    }

    @DocDescribe(label = "根据workflowId查询定义详情,通过workflowId查询", desc = "新增接口,暂无复用")
    @POST
    @Path("getWorkflowOutlineByWorkflowId")
    public GetDefinitionByWorkflowId.Result getDefinitionByWorkflowId(GetDefinitionByWorkflowId.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("getWorkflowOutlineByWorkflowId start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        WorkflowOutline workflowOutline = definitionService.getDefinitionByWorkflowId(serviceManager, arg.getWorkflowId());

        return new GetDefinitionByWorkflowId.Result(workflowOutline);
    }


    @DocDescribe(label = "查询流程定义详情,通过outlineId查询", desc = "新增接口,暂无复用")
    @POST
    @Path("getWorkflowOutlineById")
    public GetDefinition.Result getDefinition(GetDefinition.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("getWorkflowOutlineById start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        String id = arg.getId();
        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(id)) {
            log.info("流程定义id不能为空");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        WorkflowOutline workflowOutline = definitionService.getWorkflowOutlineById(serviceManager, id);
        I18NParser.parse(serviceManager.getTenantId(),workflowOutline);
        if (REPLACE_WHEN_NOT_FOUND.equals(workflowOutline.getEntryTypeName()) || com.alibaba.dubbo.common.utils.StringUtils.isBlank(workflowOutline.getEntryTypeName())) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_INLET_OBJECT_DISABLE_OR_DELETE);
        }
        return new GetDefinition.Result(workflowOutline);
    }


    @DocDescribe(label = "更新流程定义状态", desc = "新增接口,暂无复用")
    @POST
    @Path("updateStatus")
    public UpdateDefinitionStatus.Result updateDefinitionStatus(UpdateDefinitionStatus.Arg arg) {
        RefServiceManager serviceManager = getManager();
        //目前不支持批量修改状态get(0)
        boolean flag = definitionService.enableWorkflow(serviceManager, arg.getIds(), arg.isEnabled());
        return new UpdateDefinitionStatus.Result(flag);
    }


    @DocDescribe(label = "获取流程定义列表")
    @POST
    @Path("getList")
    public GetDefinitionList.Result getDefinitionList(GetDefinitionList.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowPluginUtil.checkBusinessCode(arg.getSupportFlow());
        log.debug("GetDefinitionList start : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        Page page = arg.getPage();
        String orderBy = page.getOrderBy();
        String keyword = arg.getKeyWord();
        if (StringUtils.isEmpty(orderBy)) {
            page.setOrderBy(WorkflowOutlineEntity.Fields.lastModifiedTime);
        }
        if (keyword != null && (keyword.startsWith("?") || keyword.startsWith("*") || keyword.startsWith("+"))) {
            arg.setKeyWord("\\" + keyword);
        }
        WorkflowOutlineQuery workflowOutlineQuery = new WorkflowOutlineQuery();
        workflowOutlineQuery.setEntityId(arg.getEntityId());
        workflowOutlineQuery.setExternalFlow(arg.getExternalFlow());
        workflowOutlineQuery.setEnable(arg.getEnabled());
        workflowOutlineQuery.setSupportFlowType(arg.getSupportFlow().code);
        String keyWord = arg.getKeyWord();
        if (!StringUtils.isEmpty(keyWord)) {
            workflowOutlineQuery.setName(keyWord);
        }
        PageResult<WorkflowOutline> ret = definitionService.getWorkflowOutlines(serviceManager, workflowOutlineQuery, page, null, Boolean.TRUE);
        return new GetDefinitionList.Result(ret.getTotal(), ret.getDataList());
    }

    /**
     * 和master对比了下  发现这个接口会调用getDefinitionList,但是getDefinitionList中会校验supporFlow这个参数,而当前方法
     * 却没有传递 supportFlow这个参数,所以这个接口永远都跑不通,故还原回原来的样子
     * @param arg
     * @return
     */
    @Path("list")
    @POST
    public GetDefinitions.Result getDefinitions(GetDefinitions.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowOutlineQuery query = new WorkflowOutlineQuery();
        query.setEntityId(arg.getEntityId());
        query.setStartQueryTime(arg.getStartQueryTime());
        query.setContainAllFlowType(arg.getContainAllFlowType());
        Page page = new Page(arg.getPageSize(), arg.getPage(), null, true);
        PageResult<WorkflowOutline> outline = definitionService.getWorkflowOutlines(serviceManager, query, page, null, Boolean.TRUE);
        return GetDefinitions.Result.fromOutline(outline).setPage(arg.getPage());
    }

    @Path("available")
    @POST
    public GetAvailableWorkflows.Result getAvailableWorkflows(GetAvailableWorkflows.Arg arg) {
        log.info("rest接口调用 getAvailableWorkflows");
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> rst = definitionService.getAvailableWorkflows(serviceManager, arg.getEntryType(), arg.getObjectId(), false, null, null, Boolean.TRUE);
        return new GetAvailableWorkflows.Result(rst);
    }

    @Path("has/available")
    @POST
    public HasAvailableWorkflows.Result hasAvailable(HasAvailableWorkflows.Arg arg) {
        log.info("rest接口调用 hasAvailableWorkflows");
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> rst = definitionService.getAvailableWorkflows(serviceManager, arg.getEntryType(), arg.getObjectId(), false, null, null, Boolean.TRUE);
        return new HasAvailableWorkflows.Result(rst.size() > 0);
    }

    @Path("lanes")
    @POST
    public GetLanes.Result getLanes(GetLanes.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowOutline workflow = definitionService.getWorkflowOutlineById(serviceManager, arg.getId());
        return new GetLanes.Result(workflow.getWorkflow(), workflow.getExtension());
    }

    @Path("simple/list")
    @POST
    public List<GetDefinitions.SimplestDefinition> getDefinitionsForBI() {
        RefServiceManager serviceManager = getManager();
        List<WorkflowOutline> outlines = definitionService.getAllWorkflowOutlines(serviceManager);

        List<GetDefinitions.SimplestDefinition> rets = outlines
                .stream()
                .map(GetDefinitions.SimplestDefinition::fromOutline)
                .collect(Collectors.toList());

        return rets;
    }

    @Path("quota")
    @POST
    public GetBPMLicense getWorkflowQuota() {
        RefServiceManager serviceManager = getManager();
        return tenantService.getQuotaDetail(serviceManager);
    }


    /**
     * 深研接口初始化
     *
     * @param arg
     * @return
     */
    @Path("init")
    @POST
    public DefinitionInit.Result definitionInit(DefinitionInit.Arg arg) {
        return new DefinitionInit.Result(
                definitionInitManager.definitionInit(getRemoteContext(), arg.getSourceWorkflowId()));
    }

    @DocDescribe(label = "将其他流程（简易流程等）转换为普通业务流定义（新增）")
    @POST
    @Path("convertDefinitionFromOtherType")
    public ConvertDefinitionFormOtherType.Result convertDefinitionFromOtherType(ConvertDefinitionFormOtherType.Arg arg){
        RefServiceManager serviceManager = getManager();
        return new ConvertDefinitionFormOtherType.Result(definitionService.convertDefinitionFromOtherType(serviceManager, arg.getId(), arg.getName()));
    }



}
