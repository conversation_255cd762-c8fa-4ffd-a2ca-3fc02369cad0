package com.facishare.bpm.plugins;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.ext.RestContext;
import com.facishare.rest.ext.RestRequest;

import java.util.Objects;

/**
 * Created by <PERSON> on 07/07/2017.
 */
public class BaseBPMResource extends BPMBaseService {

    public RemoteContext getRemoteContext() {
        RestContext restContext = getContext();
        RemoteContext context = new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setTenantId(restContext.getTenantId() + "");
        context.setUserId(restContext.getUserId() + "");
        context.setOutAppId(restContext.getAppId());
        context.setOuterTenantId(restContext.getOutTenantId()==null?0:restContext.getOutTenantId());
        context.setOuterUserId(restContext.getOutUserId()==null?0:restContext.getOutUserId());
        Integer outIdentityType = restContext.getOutIdentityType();
        if (Objects.nonNull(outIdentityType)) {
            context.setOuterIdentityType(String.valueOf(outIdentityType));
        }
        return context;
    }

    public RestContext getContext() {
        return RestRequest.getLocal().getRestContext();
    }

    protected RefServiceManager getManager(){
        return getServiceManager(getRemoteContext());
    }
    // 老接口  /workflow 没有消息头  RemoteContext 在参数中
    protected RefServiceManager getManager(RemoteContext context){
        return getServiceManager(context);
    }
}
