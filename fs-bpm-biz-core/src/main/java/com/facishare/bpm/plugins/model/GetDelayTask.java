package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

public interface GetDelayTask {

    @Data
    class Arg{
        @DocDescribe(label = "objectId", desc = "数据id")
        private String objectId;
        @DocDescribe(label = "workflowInstanceId", desc = "实例id")
        private String workflowInstanceId;
        @DocDescribe(label = "entityId", desc = "对象ApiName")
        private String entityId;
        @DocDescribe(label = "state", desc = "任务状态")
        private TaskState state;
        @DocDescribe(label = "sourceWorkflowId", desc = "流程定义版本id")
        private String sourceWorkflowId;
        @DocDescribe(label = "startTime", desc = "开始时间")
        private Long startTime;
        @DocDescribe(label = "endTime", desc = "结束时间")
        private Long endTime;
        @DocDescribe(label = "pageNumber", desc = "分页查询 当前页数")
        private Integer pageNumber = 1;
        @DocDescribe(label = "pageSize", desc = "分页查询 当前页最大任务数")
        private Integer pageSize = 20;
    }

}
