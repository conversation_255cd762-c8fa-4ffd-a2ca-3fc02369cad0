package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.Data;

/**
 * 创建流程定义 Created by cuiyongxu on 16/12/21.
 */
public interface UpdateDefinition {
    class Arg extends WorkflowOutline {
        public static Arg getInstance(WorkflowOutline outline) {
            return BeanUtils.transfer(outline, Arg.class);
        }
    }

    @Data
    class Result {
    }
}
