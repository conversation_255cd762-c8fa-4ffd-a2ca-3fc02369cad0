package com.facishare.bpm.plugins;

import com.facishare.bpm.plugins.model.GetDescribeList;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.facishare.rest.ext.annotation.RestAPI;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * Created by <PERSON> on 07/07/2017.
 */
@DocDescribe(label = "元数据相关")
@Slf4j
@Service
@Path("plugins/metadata")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RestAPI(flowType = RestConstant.FlowType.BPM)
public class MetaDataResource extends BaseBPMResource {
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    /**
     * @param arg
     * @return
     */
    @DocDescribe(label = "获取对象描述")
    @Path("getDescribeList")
    @POST
    public GetDescribeList.Result getDescribeList(GetDescribeList.Arg arg) {
        List<SimpleMetadataDesc> objs = metadataService.findDescsByTenantId(getRemoteContext(),
                arg.getApiNames(), false,arg.getUseGroupManager());
        return new GetDescribeList.Result(objs);
    }


}
