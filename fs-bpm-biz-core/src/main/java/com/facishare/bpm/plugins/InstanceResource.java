package com.facishare.bpm.plugins;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.web.model.GetWorkflowInstanceLog;
import com.facishare.bpm.exception.*;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.WorkflowInstanceLog;
import com.facishare.bpm.model.instance.EntireWorkflow;
import com.facishare.bpm.model.instance.TriggerWorkflow;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.plugins.model.*;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.rule.RuleMessage;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.rest.RestResult;
import com.facishare.rest.ext.RestRequest;
import com.facishare.rest.ext.annotation.RestAPI;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.Objects;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY;

/**
 * Created by Aaron on 07/07/2017.
 */
@DocDescribe(label = "实例相关")
@Slf4j
@Service
@Path("plugins/instance")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RestAPI(flowType = RestConstant.FlowType.BPM)
public class InstanceResource extends BaseBPMResource {
    @Autowired
    private BPMInstanceService bpmInstanceService;
    @Autowired
    private BPMTaskService taskService;
    @Autowired
    private BPMDefinitionService definitionService;
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;


    @Path("start")
    @POST
    public Response start(StartBPM.Arg arg) {
        RefServiceManager serviceManager = getManager();
        String instanceId = "";
        try {
            String triggerSourceId = null;
            if(TriggerSource.function.equals(arg.getSource())){
                MultivaluedMap<String, String> headers = RestRequest.getLocal().getRequest().getHeaders();
                if(CollectionUtils.isNotEmpty(headers.get("x-fs-peer-display-name"))){
                    triggerSourceId = headers.get("x-fs-peer-display-name").get(0);
                }
            }else {
                triggerSourceId = arg.getTriggerSourceId();
            }
            TriggerWorkflow triggerWorkflow = bpmInstanceService.triggerWorkflowForRest(
                    serviceManager, arg.getSource(), triggerSourceId, arg.getId(), arg.getEntityId(),arg.getObjectId(),arg.getSourceWorkflowId(), arg.getStartInstanceId()
            );
            String message = triggerWorkflow.getMessage();
            RuleMessage ruleMessage = triggerWorkflow.getRuleMessage();
            if (Objects.nonNull(ruleMessage)
                    && CollectionUtils.isNotEmpty(ruleMessage.getConditions())) {
                if(Strings.isNullOrEmpty(message)){
                    throw new BPMStartException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_START_RULE_NOT_MEET);
                }else{
                    throw new BPMStartException(message);
                }
            } else {
                return RestResult.getRestResult(triggerWorkflow.getResult());
            }
        } catch (BPMInstanceException e) {
            if (arg.isIgnoreMultiInstanceException() && BPMBusinessExceptionCode.PAAS_FLOW_BPM_REPEAT_TRIGGER.getCode() == e.getErrorCode()) {
                log.warn("后动作调用,忽略重复发起异常");
                return RestResult.getRestResult(instanceId);
            }
            throw e;
        } catch (BPMLoopBreakerException loopBreakerException) {
            log.warn("rest接口调用存在死循环,自动终止发起流程:{}",arg.getId());
            return RestResult.getRestResult( BPMI18N.PAAS_FLOW_BPM_START_INSTANCE_REST_LOOP.text()+ ":" + arg.getId());
        } catch (BPMWorkflowNotFoundException bpmWorkflowNotFoundException) {
            log.warn("定义不存在:{}", arg.getId());
            return RestResult.getRestResult( BPMI18N.PAAS_FLOW_BPM_DEFINE_NOT_FOUND.text() + ":" + arg.getId());
        }
    }

    @Path("progress")
    @POST
    public GetInstance.Result getInstance(GetInstance.Arg arg) {
        RefServiceManager serviceManager = getManager();
        WorkflowInstance instance = bpmInstanceService.getWorkflowInstance(serviceManager, arg.getInstanceId());
        if (instance == null || instance.getId() == null) {
            throw new BPMWorkflowInstanceNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NOT_FOUND);
        }
        PageResult<BPMTask> tasks = taskService.getTasksByPage(serviceManager, new Page(arg.getPageSize(), arg.getPageNumber(), null, false), new TaskQuery().setInstanceId(arg.getInstanceId()));
        List<PoolEntity> pools = definitionService.getWorkflowExtensionByWorkflowId(serviceManager, instance.getWorkflowId()).getPools();
        return GetInstance.fromEntireInstance(instance, tasks.getResult(), pools);
    }

    @Path("detail")
    @POST
    public GetPluginEntireInstance.Result getEntireInstance(GetPluginEntireInstance.Arg arg) {
        RefServiceManager serviceManager = getManager();
        EntireWorkflow entireWorkflow = bpmInstanceService.getEntireWorkflowInstance(serviceManager, arg.getInstanceId());
        return new GetPluginEntireInstance.Result(entireWorkflow);
    }

    @Path("cancel")
    @POST
    public boolean cancelInstance(PluginCancelInstance.Arg arg) {
        RefServiceManager serviceManager = getManager();
        bpmInstanceService.cancelWorkflowInstance(serviceManager, arg.getInstanceId(), arg.getReason());
        return true;
    }


    @DocDescribe(label = "查询实例列表")
    @POST
    @Path("list")
    public GetInstanceList.Result getInstanceList(GetInstanceList.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("getInstanceList : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        String sourceWorkflowId = definitionService.getSourceWorkflowIdByOutlineId(serviceManager, arg.getOutlineId());
        if (Strings.isEmpty(sourceWorkflowId)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        //RefServiceManager serviceManager, InstanceState state, String objectId, int pageSize,
        //                                                                           int pageNumber, String orderBy, String sourceWorkflowId
        PageResult<WorkflowInstanceVO> ret = bpmInstanceService.getWorkflowInstancesSkipDataAuth(
                serviceManager,
                arg.getState(),
                null,
                arg.getPageSize(),
                arg.getPageNumber(),
                arg.getOrderBy(),
                sourceWorkflowId
        );
        List<WorkflowInstanceVO> dataList = ret.getDataList();
        I18NParser.parse(serviceManager.getTenantId(),dataList);
        return new GetInstanceList.Result(ret.getTotal(), dataList);
    }


    @DocDescribe(label = "查询实例列表")
    @POST
    @Path("getInstanceList")
    public GetInstanceList.Result getInstanceListByOutlineId(GetInstanceList.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("getInstanceListByOutlineId : tenantId={}, appId={}, userId={}", serviceManager.getTenantId(), serviceManager.getAppId(), serviceManager.getUserId());
        String sourceWorkflowId = definitionService.getSourceWorkflowIdByOutlineId(serviceManager, arg.getOutlineId());
        if (Strings.isEmpty(sourceWorkflowId)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        PageResult<WorkflowInstanceVO> ret = metadataService.findDataByQuery(
                serviceManager.getContext(),
                TransferDataConstants.APINAME_INSTANCE,
                arg.getState(),
                sourceWorkflowId,
                null,
                null,
                arg.getPageSize(),
                arg.getPageNumber());
        return new GetInstanceList.Result(ret.getTotal(), ret.getDataList());
    }


    @DocDescribe(label = "根据entityId和objectId 查询实例列表")
    @POST
    @Path("/list/data")
    public GetInstanceList.Result getInstanceListByEntityIdAndObjectId(GetInstanceList.Arg arg) {
        RefServiceManager serviceManager = getManager();
        log.debug("getInstanceListByEntityIdAndObjectId : tenantId={}, entityId={}, objectId={}", serviceManager.getTenantId(), arg.getEntityId(), arg.getObjectId());

        InstanceState state = arg.getState();
        if (Objects.isNull(state)) {
            state = InstanceState.in_progress_or_error;
        }

        if(Strings.isEmpty(arg.getEntityId())){
            throw new BPMWorkflowInstanceNotFoundException("entityId not empty");
        }
        if(Strings.isEmpty(arg.getObjectId())){
            throw new BPMWorkflowInstanceNotFoundException("objectId not empty");
        }

        PageResult<WorkflowInstanceVO> ret = bpmInstanceService.getWorkflowInstances(
                serviceManager,
                state,
                arg.getEntityId(),
                arg.getObjectId(),
                arg.getPageSize(),
                arg.getPageNumber(),
                arg.getOrderBy(),
                null
        );
        List<WorkflowInstanceVO> dataList = ret.getDataList();
        I18NParser.parse(serviceManager.getTenantId(),dataList);
        return new GetInstanceList.Result(ret.getTotal(), dataList);
    }


    @DocDescribe(label = "获取流程实例日志根据流程实例id")
    @POST
    @Path("getWorkflowInstanceLog")
    public GetWorkflowInstanceLog.Result getWorkflowInstanceLog(GetWorkflowInstanceLog.Arg arg) {
        if(Objects.isNull(arg)){
            throw new ValidationException(BPMI18N.getI18NByName(PAAS_FLOW_BPM_PARAMETER_ANOMALY));
        }
        RefServiceManager serviceManager = getManager();
        WorkflowInstanceLog workflowInstanceLog = bpmInstanceService.getWorkflowInstanceLog(serviceManager, arg.getWorkflowInstanceId());
        I18NParser.parse(serviceManager.getTenantId(),workflowInstanceLog);
        return BeanUtils.transfer(workflowInstanceLog, GetWorkflowInstanceLog.Result.class);
    }

}
