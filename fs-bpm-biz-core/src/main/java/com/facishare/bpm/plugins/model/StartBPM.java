package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON> on 07/07/2017.
 */
public interface StartBPM {
    @Data
    class Arg {
        /**
         * 流程outline id
         */
        @DocDescribe(label = "业务流id",desc = "要触发的业务流outlineId")
        private String id;
        /**
         * 发起对象id
         */
        @DocDescribe(label = "数据Id",desc = "基于哪条数据触发业务流",required = true)
        @NotEmpty(message = "数据id不能为空")
        private String objectId;
        /**
         * 发起对象apiName
         */
        @NotEmpty(message = "对象entityId不能为空")
        @DocDescribe(label = "对象apiname",desc = "触发哪个对象的业务流",required = true)
        private String entityId;
        /**
         * 触发来源
         */
        @NotNull(message = "source不能为空")
        @DocDescribe(label = "触发来源",desc = "person(手动触发), bpm(业务流程), workflow(工作流), approval(审批流), openapi(外部调用), market(营销流程)",required = true)
        private TriggerSource source;

        @DocDescribe(label = "触发来源id",desc = "若是函数触发此字段为函数APIName；若是流程触发此字段为workflowId")
        private String triggerSourceId;

        /**
         * 是否忽略多实例异常,目前只有后动作调用
         */
        private boolean ignoreMultiInstanceException;

        /**
         * 750 添加  如果存在 优先使用sourceWorkflowId 来触发流程
         */
        private String sourceWorkflowId;

        private String startInstanceId;


    }
}
