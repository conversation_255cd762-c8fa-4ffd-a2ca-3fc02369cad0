package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.WorkflowOutlineDraft;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by wangz on 17-1-5.
 */
public interface GetDraft {
    @Data
    class Arg {
        @NotEmpty(message = "草稿Id不能为空")
        private String id;
    }

    @Data
    class Result {
        private WorkflowOutlineDraft workflowOutline;


        public Result(WorkflowOutlineDraft workflowOutline) {
            this.workflowOutline = workflowOutline;
        }
    }

}
