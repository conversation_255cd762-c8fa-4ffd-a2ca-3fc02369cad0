package com.facishare.bpm.plugins.model;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;
import com.facishare.stage.doc.annotations.DocDescribe;
import lombok.Data;

import java.util.List;

/**
 * 获取流程定义,列表或单一对象详情 Created by cuiyongxu on 16/12/22.
 */
public interface GetDefinitionList {
    @Data
    class Arg {
        @DocDescribe(label = "第几页")
        private int pageNumber;
        @DocDescribe(label = "每页多少条")
        private int pageSize;
        @DocDescribe(label = "定义名称标识")
        private String keyWord;
        private String enabled; //1代表启用,2代表未启用,0查全部
        private String orderBy;//排序,lastModifiedTime,createTime
        private String entityId;//过滤对象id
        private Integer externalFlow;//1代表外部流程  0 不是外部流程   -1 所有的
        private SupportFlow supportFlow;

        public Page getPage() {
            Page page = new Page();
            page.setPageNumber(pageNumber);
            page.setPageSize(pageSize);
            page.setOrderBy(orderBy);
            return page;
        }

        public Boolean getEnabled() {
            if ("1".equals(this.enabled)) {
                return true;
            } else if ("2".equals(this.enabled)) {
                return false;
            } else {
                return null;
            }
        }

        public Integer getExternalFlow(){
            if(externalFlow==null||externalFlow==-1){
                return null;
            }
            return externalFlow;
        }

    }


    @Data
    class Result {
        int totalCount;
        List<WorkflowOutline> outlines;


        public Result(int totalCount, List<WorkflowOutline> definitions) {
            this.totalCount = totalCount;
            this.outlines = definitions;

        }

    }
}
