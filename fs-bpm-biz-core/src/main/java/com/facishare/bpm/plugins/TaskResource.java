package com.facishare.bpm.plugins;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.web.model.ChangeTaskHandler;
import com.facishare.bpm.controller.web.model.GetTaskInfo;
import com.facishare.bpm.controller.web.model.UpdateDataAndCompleteTask;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMParamsException;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.AfterRetry;
import com.facishare.bpm.model.paas.engine.bpm.AutoTask;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.CompleteTaskResult;
import com.facishare.bpm.model.task.MTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.plugins.model.*;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.CompleteTaskFormValidateManager;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.ext.annotation.RestAPI;
import com.facishare.rest.ext.common.RestConstant;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_PLEASE_ENTER_APPROVAL_COMMENTS;


/**
 * Created by Aaron on 07/07/2017.
 */
@DocDescribe(label = "任务相关")
@Slf4j
@Service
@Path("plugins/task")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RestAPI(flowType = RestConstant.FlowType.BPM)
public class TaskResource extends BaseBPMResource {
    @Autowired
    private BPMTaskService taskService;

    @Path("detail")
    @POST
    @DocDescribe(label = "获取任务详情")
    public GetPluginTask.Result getTask(GetPluginTask.Arg arg) {
        RefServiceManager serviceManager = getManager();

        String instanceId = arg.getInstanceId();
        String activityInstanceId = arg.getActivityInstanceId();
        log.info("getTask , instanceId:{},activityInstanceId:{}", instanceId, activityInstanceId);
        MTask task = taskService.getMTask(serviceManager, instanceId, activityInstanceId,"",false);
        return new GetPluginTask.Result(task);
    }


    @DocDescribe(label = "获取任务")
    @Path("getTask")
    @POST
    public GetTaskInfo.Result getTask(GetTaskInfo.Arg arg) {
        RefServiceManager serviceManager = getManager();
        TaskDetail task = taskService.getBpmTaskDetail(serviceManager, arg.getId(), null, null, null, TaskParams.create().isTaskDetail(true).isTaskNotGetData(Boolean.TRUE.equals(arg.getNotGetData()) && SwitchConfigManager.getTaskNotGetTask(serviceManager.getTenantId())));
        return new GetTaskInfo.Result(serviceManager, task);
    }

    //TODO 跟函数同步下
    @DocDescribe(label = "更换任务处理人")
    @Path("changeTaskHandler")
    @POST
    public ChangeTaskHandler.Result changeTaskHandlers(ChangeTaskHandler.Arg arg) {
        RefServiceManager serviceManager = getManager();
        Boolean ret = taskService.changeTaskHandlers(serviceManager, arg.getTaskId(), arg.getCandidateIds(), arg.getModifyOpinion());
        return new ChangeTaskHandler.Result(ret);
    }


    @DocDescribe(label = "替换任务处理人")
    @Path("replaceTaskHandler")
    @POST
    public ChangeTaskHandler.Result replaceTaskHandlers(ChangeTaskHandler.Arg arg) {
        RefServiceManager serviceManager = getManager();
        Boolean ret = taskService.replaceTaskHandlers(serviceManager, arg.getTaskId(), arg.getCandidateIds());
        return new ChangeTaskHandler.Result(ret);
    }


    /**
     * 只支持完成任务，和接收审批意见，不支持选择和创建类型的任务处理
     *
     * @param arg
     * @return
     */
    @DocDescribe(label = "只支持完成任务，和接收审批意见，不支持选择和创建类型的任务处理")
    @Path("complete")
    @POST
    public boolean complete(PluginCompleteTask.Arg arg) {
        RefServiceManager serviceManager = getManager();
        Map<String, Object> data = Maps.newHashMap();
        if (!Strings.isNullOrEmpty(arg.getApprovalResult())) {
            data.put(BPMConstants.ApproveResult.RESULT, arg.getApprovalResult());
        }

        Map<String,Object> approveData = arg.getData();
        if(MapUtils.isNotEmpty(approveData)){
            data.putAll(approveData);
        }

        CompleteTaskResult completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(), arg.getOpinion(), data
                , arg.getAddOrReplaceNextTaskAssignee(), arg.getAssignNextTaskProcessors(), true,false, arg.getIgnoreNoBlockValidate());
        if (Objects.nonNull(completeTaskResult) && Objects.nonNull(completeTaskResult.getRuleMessage()) && CollectionUtils.isNotEmpty(completeTaskResult.getRuleMessage().getConditions())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_RULE_NOT_MEET);
        }
        return true;
    }

    @DocDescribe(label = "完成任务，不符合完成条件会返回阻断信息")
    @Path("/v2/complete")
    @POST
    public com.facishare.bpm.controller.web.model.CompleteTask.Result completeV2(PluginCompleteTask.Arg arg) {
        RefServiceManager serviceManager = getManager();
        Map<String, Object> data = Maps.newHashMap();
        if (!Strings.isNullOrEmpty(arg.getApprovalResult())) {
            data.put(BPMConstants.ApproveResult.RESULT, arg.getApprovalResult());
        }

        Map<String,Object> approveData = arg.getData();
        if(MapUtils.isNotEmpty(approveData)){
            data.putAll(approveData);
        }

        CompleteTaskResult completeTaskResult = null;
        try {
            completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(), arg.getOpinion(), data
                    , arg.getAddOrReplaceNextTaskAssignee(), arg.getAssignNextTaskProcessors(), true,false, arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            if(e.getErrorCode() == 301090061) {
                return com.facishare.bpm.controller.web.model.CompleteTask.Result.fail(null, e.getMessage());
            }
            if(e.getErrorCode() == 301090011 || e.getErrorCode() == 301090012) {
                return com.facishare.bpm.controller.web.model.CompleteTask.Result.fail(e.getMessage(), null);
            }
            //将后动作异常直接放在消息体中返回
            if (e.getErrorCode() == 301080003) {
                return new com.facishare.bpm.controller.web.model.CompleteTask.Result(false, e.getMessage(), completeTaskResult);
            }
            throw e;
        }
        return new com.facishare.bpm.controller.web.model.CompleteTask.Result(true, "", completeTaskResult);
    }


    @DocDescribe(label = "完成任务")
    @Path("completeTask")
    @POST
    public boolean completeTask(PluginCompleteTask.CompleteTaskArg arg) {
        RefServiceManager serviceManager = getManager();
        Map<String, Object> data = Maps.newHashMap();
        CompleteTaskResult completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(), "", data
                , arg.getAddOrReplaceNextTaskAssignee(), arg.getAssignNextTaskProcessors(), true, false, arg.getIgnoreNoBlockValidate());
        if (Objects.nonNull(completeTaskResult) && Objects.nonNull(completeTaskResult.getRuleMessage()) && CollectionUtils.isNotEmpty(completeTaskResult.getRuleMessage().getConditions())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_RULE_NOT_MEET);
        }
        return true;
    }

    /**
     * 审批任务  处理审批节点的任务
     *
     * @param arg
     * @return
     */
    @DocDescribe(label = "审批任务  处理审批节点的任务")
    @Path("approvalTask")
    @POST
    public boolean approvalTask(PluginCompleteTask.Arg arg) {
        String approvalResult = arg.getApprovalResult();
        if (Strings.isNullOrEmpty(approvalResult)) {
            throw new BPMParamsException(PAAS_FLOW_BPM_PLEASE_ENTER_APPROVAL_COMMENTS);
        }

        RefServiceManager serviceManager = getManager();
        Map<String, Object> data = Maps.newHashMap();
        data.put(BPMConstants.ApproveResult.RESULT, approvalResult);

        Map<String, Object> approveData = arg.getData();
        if (MapUtils.isNotEmpty(approveData)) {
            data.putAll(approveData);
        }
        CompleteTaskResult completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(), arg.getOpinion(), data
                , arg.getAddOrReplaceNextTaskAssignee(), arg.getAssignNextTaskProcessors(), true, false, arg.getIgnoreNoBlockValidate());
        if (Objects.nonNull(completeTaskResult) && Objects.nonNull(completeTaskResult.getRuleMessage()) && CollectionUtils.isNotEmpty(completeTaskResult.getRuleMessage().getConditions())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_RULE_NOT_MEET);
        }
        return true;
    }


    /**
     * 更新并完成任务,  布局规则 &验证规则不会校验  需要调用方自行校验
     * @param arg
     * @return
     */
    @DocDescribe(label = "更新并完成任务,  布局规则 &验证规则不会校验  需要调用方自行校验")
    @POST
    @Path("updateDataAndCompleteTask")
    public UpdateDataAndCompleteTask.Result updateDataAndCompleteTask(UpdateDataAndCompleteTask.Arg arg) {
        RefServiceManager serviceManager = getManager();
        Map<String, Object> bpmExtension = taskService.getBPMExtensionByTaskId(serviceManager, arg.getTaskId());
        Map<String, Object> purgedData = CompleteTaskFormValidateManager.getLegalUpdateFields(serviceManager
                ,arg.getEntityId()
                ,arg.getData(),
                MapUtil.instance.getList(bpmExtension, BPMConstants.FORM), serviceManager.getFields(arg.getEntityId()), false);
        String dataString = JacksonUtil.toJson(purgedData);
        Task task = taskService.getPaaSTask(serviceManager, arg.getTaskId(), true);
        TraceContext.get().setSourceProcessId(task.getSourceWorkflowId());
        Map metaData = serviceManager.updateData(serviceManager.getContext(), arg.getEntityId(), arg.getObjectId(), dataString, false, true, "workflow_bpm");
        CompleteTaskResult completeTaskResult;
        try {
            completeTaskResult = taskService.completeTask(serviceManager, arg.getTaskId(),
                    null,
                    arg.getData(),
                    arg.getAddOrReplaceNextTaskAssignee(),
                    arg.getNextTaskAssignee(),
                    false,false, arg.getIgnoreNoBlockValidate());
        } catch (BPMBusinessException e) {
            // 捕获非阻断异常
            if(e.getErrorCode() == 301090061) {
                return new UpdateDataAndCompleteTask.Result(e.getMessage());
            }
            throw e;
        }
        return new UpdateDataAndCompleteTask.Result(metaData, completeTaskResult);

    }


    @Path("after/action/retry")
    @POST
    public PluginAfterActionRetry.Result afterActionRetry(PluginAfterActionRetry.Arg arg) {
        RefServiceManager serviceManager = getManager();
        AfterRetry.RetryResult result = taskService.afterActionRetry(serviceManager, arg.getTaskId(), arg.getRowNum(), arg.getExecuteType());
        return new PluginAfterActionRetry.Result(result);
    }

    /**
     * 重新解析任务处理人
     * @param arg
     * @return
     */
    @DocDescribe(label = "重新解析任务处理人")
    @Path("/refreshHandlerByTaskId")
    @POST
    public RefreshHandlerByTaskId.Result refreshHandlerByTaskId(RefreshHandlerByTaskId.Arg arg) {
        taskService.refreshHandlerByTaskId(getManager(), arg.getTaskId());
        return new RefreshHandlerByTaskId.Result();
    }

    @Path("/findDelayTask")
    @DocDescribe(label = "根据objectId获取instanceId查询等待节点")
    @POST
    public List<AutoTask> findDelayTask(GetDelayTask.Arg arg) {
        return taskService.findDelayTask(getManager(), arg.getObjectId(), arg.getWorkflowInstanceId(), arg.getPageNumber(), arg.getPageSize());
    }

    @Path("/findByIds")
    @DocDescribe(label = "批量获取任务")
    @POST
    public List<GetPluginTask.TaskByIdsResult> getByIds(GetPluginTask.GetTaskByIdsArg arg) {
        return GetPluginTask.TaskByIdsResult.of(taskService.findTaskByIds(getManager(),arg.getTaskIds()));
    }

    @Path("/findAutoTasks")
    @DocDescribe(label = "根据instanceId查询自动节点（含等待节点）")
    @POST
    public List<Task> findAutoTasks(GetDelayTask.Arg arg) {
        return taskService.findAutoTasksByInstanceId(getManager(), arg.getWorkflowInstanceId(), arg.getEntityId(), arg.getState(), arg.getSourceWorkflowId(), arg.getStartTime(), arg.getEndTime(), arg.getPageNumber(), arg.getPageSize());
    }


    @DocDescribe(label = "任务催办")
    @Path("/remind")
    @POST
    public boolean refreshHandlerByTaskId(Remind.Arg arg) {
        taskService.remindTask(getManager(), arg.getTaskId(),arg.getContent(),arg.getRemindPersons());
        return true;
    }

}
