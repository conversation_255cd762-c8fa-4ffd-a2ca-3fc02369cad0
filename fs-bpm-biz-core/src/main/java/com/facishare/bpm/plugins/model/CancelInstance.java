package com.facishare.bpm.plugins.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 停止流程实例 Created by cuiyongxu on 16/12/22.
 */
public interface CancelInstance {
    @Data
    class Arg {
        @NotEmpty(message = "流程实例id不能为空")
        String id;//实例Id,取消实例时使用
        //@FcpField(description="流程中止原因")
        String reason;
    }

    @Data
    class Result {
        boolean result = true;


    }
}
