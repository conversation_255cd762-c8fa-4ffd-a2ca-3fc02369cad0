package com.facishare.bpm;

import com.facishare.bpm.model.Pool.Lane;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import com.facishare.bpm.model.paas.engine.bpm.ActivityInstance;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.test.I18nTestHelper;
import org.junit.Before;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * wansong
 */
public class GetInstanceByObjectTest {

    private GetInstanceByObject getInstanceByObject;
    private WorkflowInstance workflowInstance;
    private Map<String, String> activityIdAndTypes;
    private List<PoolEntity> pools;

    @Before
    public void setUp() {
        getInstanceByObject = new GetInstanceByObject();
        workflowInstance = mock(WorkflowInstance.class);
        activityIdAndTypes = new HashMap<>();
        pools = new ArrayList<>();
        I18nTestHelper.setupI18nClientStub();
    }

    /**
     * 准备基础的 Pool 和 Lane 数据
     */
    private void preparePoolAndLane() {
        PoolEntity poolEntity = mock(PoolEntity.class);
        LaneEntity lane = new LaneEntity();
        when(poolEntity.getLanes()).thenReturn(Collections.singletonList(lane));
        pools.add(poolEntity);
    }

    @Test
    public void testSetLanes_EmptyActivityInstances() {
        // 准备测试数据
        when(workflowInstance.getActivityInstances()).thenReturn(Collections.emptyList());
        preparePoolAndLane();
        
        // 执行测试
        getInstanceByObject.setLanes(workflowInstance, activityIdAndTypes, pools);
        
        // 验证结果
        List<Lane> lanes = getInstanceByObject.getLanes();
        assertNotNull("lanes should not be null", lanes);
        assertEquals("should have one lane for empty activities", 1, lanes.size());
    }

    @Test
    public void testSetLanes_FilterInvalidTaskTypes() {
        // 准备测试数据
        List<ActivityInstance> instances = Arrays.asList(
            createActivityInstance("1", 1000L, "invalidType"),
            createActivityInstance("2", 2000L, "userTask"),
            createActivityInstance("3", 3000L, "executionTask"),
            createActivityInstance("4", 4000L, "latencyTask"),
            createActivityInstance("5", 5000L, "unknownType")
        );
        when(workflowInstance.getActivityInstances()).thenReturn(instances);
        
        // 设置类型映射
        activityIdAndTypes.put("1", "invalidType");
        activityIdAndTypes.put("2", "userTask");
        activityIdAndTypes.put("3", "executionTask");
        activityIdAndTypes.put("4", "latencyTask");
        activityIdAndTypes.put("5", "unknownType");

        preparePoolAndLane();

        // 执行测试
        getInstanceByObject.setLanes(workflowInstance, activityIdAndTypes, pools);

        // 验证结果
        List<Lane> lanes = getInstanceByObject.getLanes();
        assertNotNull("lanes should not be null", lanes);
        assertEquals("should have one lane", 1, lanes.size());
        

    }

    @Test
    public void testSetLanes_SortByStartTime() {
        // 准备测试数据 - 故意打乱顺序
        List<ActivityInstance> instances = Arrays.asList(
            createActivityInstance("1", 2000L, "userTask"),
            createActivityInstance("2", 3000L, "userTask"),
            createActivityInstance("3", 1000L, "userTask"),
            createActivityInstance("4", null, "userTask")
        );
        when(workflowInstance.getActivityInstances()).thenReturn(instances);
        
        instances.forEach(instance -> 
            activityIdAndTypes.put(instance.getActivityId(), "userTask"));

        preparePoolAndLane();

        // 执行测试
        getInstanceByObject.setLanes(workflowInstance, activityIdAndTypes, pools);

        // 验证结果
        List<Lane> lanes = getInstanceByObject.getLanes();
        assertNotNull("lanes should not be null", lanes);
        assertEquals("should have one lane", 1, lanes.size());

    }

    @Test
    public void testSetLanes_LatestInstanceOnly() {
        // 准备测试数据 - 包含重复的 activityId
        List<ActivityInstance> instances = Arrays.asList(
            createActivityInstance("1", 1000L, "userTask"),
            createActivityInstance("1", 2000L, "userTask"),
            createActivityInstance("1", 1500L, "userTask"),
            createActivityInstance("2", 3000L, "executionTask"),
            createActivityInstance("2", 2500L, "executionTask")
        );
        when(workflowInstance.getActivityInstances()).thenReturn(instances);
        
        activityIdAndTypes.put("1", "userTask");
        activityIdAndTypes.put("2", "executionTask");

        preparePoolAndLane();

        // 执行测试
        getInstanceByObject.setLanes(workflowInstance, activityIdAndTypes, pools);

        // 验证结果
        List<Lane> lanes = getInstanceByObject.getLanes();
        assertNotNull("lanes should not be null", lanes);
        assertEquals("should have one lane", 1, lanes.size());

    }

    /**
     * 创建测试用的ActivityInstance
     */
    private ActivityInstance createActivityInstance(String activityId, Long startTime, String type) {
        ActivityInstance instance = mock(ActivityInstance.class);
        when(instance.getActivityId()).thenReturn(activityId);
        when(instance.getStart()).thenReturn(startTime != null ? startTime.toString() : null);
        return instance;
    }

    @Test
    public void testSetLanes_AllTaskTypes() {
        // 准备测试数据
        List<ActivityInstance> instances = Arrays.asList(
            createActivityInstance("1", 1000L, "userTask"),
            createActivityInstance("2", 2000L, "executionTask"),
            createActivityInstance("3", 3000L, "latencyTask")
        );
        when(workflowInstance.getActivityInstances()).thenReturn(instances);
        activityIdAndTypes.put("1", "userTask");
        activityIdAndTypes.put("2", "executionTask");
        activityIdAndTypes.put("3", "latencyTask");
        
        // 执行测试
        getInstanceByObject.setLanes(workflowInstance, activityIdAndTypes, pools);
        
        // 验证结果 - 所有有效类型都被保留
        verify(workflowInstance).getActivityInstances();
    }

    @Test
    public void testSetLanes_WithPools() {
        // 准备测试数据
        List<ActivityInstance> instances = Collections.singletonList(
            createActivityInstance("1", 1000L, "userTask")
        );
        when(workflowInstance.getActivityInstances()).thenReturn(instances);
        activityIdAndTypes.put("1", "userTask");

        // 创建测试用的Pool和Lane
        PoolEntity poolEntity = mock(PoolEntity.class);
        when(poolEntity.getLanes()).thenReturn(Collections.singletonList(new LaneEntity()));
        pools.add(poolEntity);

        // 执行测试
        getInstanceByObject.setLanes(workflowInstance, activityIdAndTypes, pools);

        // 验证结果
        List<Lane> lanes= getInstanceByObject.getLanes();
        assertNotNull("lanes should not be null", lanes);
        assertFalse("lanes should not be empty", lanes.isEmpty());
    }
}