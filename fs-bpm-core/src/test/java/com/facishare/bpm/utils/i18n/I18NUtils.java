package com.facishare.bpm.utils.i18n;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.Map;
import java.util.Set;

/**
 * Mock I18NUtils for testing
 * 用于测试的I18NUtils Mock实现，避免NullPointerException
 * 
 * <AUTHOR> Implementation
 * @date 2024-12-26
 */
@Slf4j
public class I18NUtils {
    
    public static final String TRANSLATE_PRE = "flow.";
    
    /**
     * Mock text方法，返回默认的中文文本或格式化后的文本
     * 
     * @param key 国际化key
     * @param defaultText 默认文本（中文）
     * @param params 参数
     * @return 格式化后的文本
     */
    public static String text(String key, String defaultText, Object... params) {
        try {
            // 如果默认文本为空，返回key
            if (StringUtils.isBlank(defaultText)) {
                log.warn("Default text is blank for key: {}, returning key", key);
                return key;
            }
            
            // 如果没有参数，直接返回默认文本
            if (params == null || params.length == 0) {
                return defaultText;
            }
            
            // 使用MessageFormat格式化文本
            return MessageFormat.format(defaultText, params);
            
        } catch (Exception e) {
            log.error("Error in i18n processing for key: {}, defaultText: {}", key, defaultText, e);
            // 发生异常时返回默认文本或key
            return StringUtils.isNotBlank(defaultText) ? defaultText : key;
        }
    }
    
    /**
     * Mock方法：保存定义名称和描述翻译
     */
    public static void saveDefinitionNameAndDescTranslate(String tenantId, String workflowId, 
                                                         Map<String, String> nameTranslateInfo, 
                                                         Map<String, String> descTranslateInfo, 
                                                         String name, String description) {
        log.debug("Mock saveDefinitionNameAndDescTranslate called for tenantId: {}, workflowId: {}", tenantId, workflowId);
        // Mock实现，不做实际操作
    }
    
    /**
     * Mock方法：获取定义名称和描述的国际化
     */
    public static Map<String, org.apache.commons.lang3.tuple.Pair<String, String>> getDefNameAndDescI18nByLangCode(
            String tenantId, Set<String> workflowIds) {
        log.debug("Mock getDefNameAndDescI18nByLangCode called for tenantId: {}, workflowIds: {}", tenantId, workflowIds);
        // Mock实现，返回空Map
        return java.util.Collections.emptyMap();
    }
    
    /**
     * Mock方法：获取国际化代码和值
     */
    public static Map<String, String> obtainI8NCodeAndValueByKeyLevelList(String tenantId, java.util.List<String[]> keyList) {
        log.debug("Mock obtainI8NCodeAndValueByKeyLevelList called for tenantId: {}", tenantId);
        // Mock实现，返回空Map
        return java.util.Collections.emptyMap();
    }
    
    /**
     * Mock方法：获取国际化代码和值（单个key）
     */
    public static Map<String, String> obtainI8NCodeAndValueByKeyLevel(String tenantId, String... keys) {
        log.debug("Mock obtainI8NCodeAndValueByKeyLevel called for tenantId: {}, keys: {}", tenantId, java.util.Arrays.toString(keys));
        // Mock实现，返回空Map
        return java.util.Collections.emptyMap();
    }
    
    /**
     * Mock方法：获取上下文
     */
    public static MockI18NContext getContext() {
        return new MockI18NContext();
    }
    
    /**
     * Mock I18N上下文类
     */
    public static class MockI18NContext {
        public String getLanguage() {
            return "zh-CN";
        }
    }
}
