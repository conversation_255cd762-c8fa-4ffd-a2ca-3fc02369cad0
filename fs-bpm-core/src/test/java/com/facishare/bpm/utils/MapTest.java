package com.facishare.bpm.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/15 8:25 PM
 */
public class MapTest {

    @Test
    public void compute() {
        Map<String, Integer> errorCount = Maps.newHashMap();

        //如果从引擎侧获取的异常不为空,则需要将其添加到异常数据中

        List<String> engineTask = Lists.newArrayList("1","2","1","3","1","1");
        engineTask.forEach(task -> {
            errorCount.compute(task, (activityId1, count) -> {
                if (Objects.isNull(count)) {
                    count = 1;
                } else {
                    count += 1;
                }
                return count;
            });
        });

        System.out.println(1);
    }
}
