package com.facishare.bpm.manager;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.manage.DefinitionInitManager;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.rest.core.model.RemoteContext;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2018/12/10 6:48 PM
 */
public class DefinitionInitManagerTest extends BaseTest{

    @Autowired
    private DefinitionInitManager definitionInitManager;

    @Test
    public void testDefinitionInit(){

        //Uid,SWI,TId
        RemoteContext context = new RemoteContext();
        context.setTenantId("71559");
        context.setUserId("1000");
        context.setAppId("BPM");

        WorkflowOutline workflowOutline = definitionInitManager.definitionInit(context, "405678154806329344");
        // WorkflowOutline id = definitionService.definitionInit(context, "object_OOOO__c", variables);
        System.out.println(workflowOutline);


    }

}
