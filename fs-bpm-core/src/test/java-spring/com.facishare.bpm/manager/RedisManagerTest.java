package com.facishare.bpm.manager;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.manage.RedisManager;
import com.facishare.bpm.utils.ObjectId;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.fcp.util.MD5Util;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2018/12/27 6:11 PM
 */
public class RedisManagerTest extends BaseTest {

    @Autowired
    private RedisManager redisManager;

    @Test
    public void setValueWithExpireTime() {
        Thread[] threads = new Thread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new TestThread(redisManager);
        }

        for (Thread thread : threads) {
            thread.start();
        }
    }

    @SneakyThrows
    @Test
    public void test122() {

        for (int i = 0; i < 10; i++) {
            new Thread(()->{
                String parallelRedisKey = "71557" + UtilConstans.UNDERLINE + "12323232323232" + FlowType.workflow_bpm.name();
                redisManager.setValueWithExpireTime(parallelRedisKey, StringUtils.EMPTY, 10);
                if (!redisManager.setValueWithExpireTime(parallelRedisKey, StringUtils.EMPTY, 10)) {
                    System.out.println(Thread.currentThread().getId() + "-不允许完成任务");
                    try {
                        Thread.sleep(1 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }

        Thread.sleep(5 * 1000);

    }


    @Test
    public void setMap() {
        Map<String, String> map = Maps.newHashMap();
        map.put("dsf", "234234");
        map.put("dsfde", "dsfsdf");
        map.put("dsfd", "3424");
        map.put("dsfdddd", "你哈");
        map.put("dsfdddddd", "你哈)(*UDJFUDSHF(*DS");
        redisManager.setValueOfMap("cuiyongxu", map);
        System.out.println("=============redis save done ==============");

        Map<String, String> rst = redisManager.getValueMapByKey("cuiyongxu");
        System.out.println(JacksonUtil.toJson(rst));
    }


    @Test
    public void getMap() {
        Map<String, String> rst = redisManager.getValueMapByKey("cuiyongxu");
        System.out.println("===========>" + JacksonUtil.toJson(rst));
    }


    @Test
    public void getMd5() {
        System.out.println(MD5Util.toMD5Hex("你好 ".getBytes()));
        System.out.println(MD5Util.toMD5Hex(" 你好 ".getBytes()));
        System.out.println(MD5Util.toMD5Hex(" 你好".getBytes()));
        System.out.println(MD5Util.toMD5Hex("你好".getBytes()));
        System.out.println(MD5Util.toMD5Hex("你好1".getBytes()));
        System.out.println(MD5Util.toMD5Hex("你好".getBytes()));
        System.out.println(MD5Util.toMD5Hex("你好".getBytes()));
    }
}


class TestThread extends Thread {

    private RedisManager redisManager;

    public TestThread(RedisManager redisManager) {
        this.redisManager = redisManager;
    }

    @Override
    public void run() {
        if (!redisManager.setValueWithExpire(MD5Util.toMD5Hex("你好".getBytes()), "71557")) {
            System.out.println("请重新设置流程名称");
        }
    }
}

