package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.rest.core.model.ClientInfo;
import com.facishare.rest.core.model.ClientType;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.CircleType;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.model.task.LaneBriefTaskVO;
import com.facishare.bpm.model.task.TaskLog;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.memory.page.Comparation;
import com.facishare.bpm.util.memory.page.ComparationType;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;


/**
 * Created by wangz on 17-2-27.
 */
@Slf4j
public class BPMTaskServiceImplTest extends BaseTest {


    @Autowired
    private BPMTaskService taskService;
    @Autowired
    private BPMInstanceService instanceService;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;

    @BeforeClass
    public static void setup(){
        System.setProperty("spring.profiles.active","fstest" );
    }


    @Test
    public void getTasksByPage() throws Exception {
        String instanceId = startNewWorkflow();
        TaskQuery query = new TaskQuery();
        query.setIsCompleted(false);
        query.setAssignee(userId);
        query.setInstanceId(instanceId);
        query.setInstanceId("58b58a343db71da1178fb6bc");

        PageResult<BPMTask> result = taskService.getTasksByPage(getServiceManager(context), new Page(), query);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getDataList());
    }

    @Test
    public void testArea() {
        BPMTask bpmTask = new BPMTask();
        List<Object> allForm2 = Lists.newArrayList();
        List<Object> allForm = Lists.newArrayList();
        Map<String, String> form = Maps.newHashMap();
        form.put("type", "city");
        allForm.add(form);
        allForm2.add(allForm);
        Map<String, Object> value = Maps.newHashMap();
        value.put("form", allForm2);
        bpmTask.setExtension(value);
        bpmTask.setAreaOptions(getServiceManager(new RemoteContext("", "71557", "BPM", "1000")));
        System.out.println(1);
    }

    @Test
    public void getTasksByLane() throws Exception {

        PageResult<LaneBriefTaskVO> result = taskService.getTasksByLane(getServiceManager(context), CircleType.ALL, "236901564152446976",
                Lists.newArrayList(new Comparation("laneId", ComparationType.EQUAL, "1490671650732")), null);
    }

    @Test
    public void getTask() {
        ClientInfo clientInfo = new ClientInfo();
        //clientInfo.setClientType(ClientType.android);
        context.setClientInfo(clientInfo);
        context.setTenantId("74203");

        context.setUserId("1000");
        context.setAppId(BPMConstants.APP_ID);
        context.setEa("74203");
        //5cc00f913db71d17299b734f
        //5cc018363db71d17299b7362
        //
        BPMTask ret = taskService.getTask(getServiceManager(context), "5d9daa526886293ff03373c9");
        Assert.assertNotNull(ret);
    }

    @Test
    public void getTaskMap() {
        Map<String, String> data1 = Maps.newHashMap();
        data1.put("k1", null);
        data1.put("k2", "");
        data1.put("k3", "ff");
        data1.put("k4", "");

        System.out.println("=========" + data1.getOrDefault("k1", "3333"));//=========null
        System.out.println("=========" + data1.getOrDefault("k2", "4444"));//=========
        System.out.println("=========" + data1.getOrDefault("k3", "5555"));//=========ff
        System.out.println("=========" + data1.getOrDefault("vv", "k444"));//=========k444
        //key 不存在的时候才会赋值
    }

    @Test
    public void getUncompletedTasksByObject() throws Exception {
        RefServiceManager serviceManager = getServiceManager(context);
//        String outlineId = deploySimpleProcess();
        String outlineId = deployProcess("allPassTest.json");
        String dataId = createNewAccount();
        String instanceId = instanceService.startWorkflow(serviceManager, TriggerSource.approval, outlineId, dataId);
        log.debug("=============================instanceId={}", instanceId);

        List<TaskOutline> ret = taskService.getUncompletedTasksByObject(serviceManager,
                dataId);
//        List<TaskOutline> ret = taskService.getUncompletedTasksByObject(context, entityId,
//                "7a5ef35bb06d4ac7befc75c3c5e990f8");

        Assert.assertNotNull(ret);
        Assert.assertTrue(ret.size() > 0);
    }

    @Test
    public void getTasksByInstanceIds() throws Exception {
//        String instanceId = startNewWorkflow();
        RefServiceManager serviceManager = getServiceManager(context);
        String instanceId = "59957d503db71d7dcc22869c";
        WorkflowInstance instance = instanceService.getWorkflowInstance(serviceManager, instanceId);
//        List<ActivityInstance> activityInstances = instance.getActivityInstances();

//        List<String> activityInstanceIds = Lists.newArrayList();
//        for (ActivityInstance activityInstance : activityInstances) {
//            if (activityInstance.getEnd() == null) {
//                log.debug("========================activityInstace={}", activityInstance);
//                activityInstanceIds.add(String.valueOf(activityInstance.getId()));
//            }
//        }

        List<BPMTask> tasks = taskService.getTasksByInstanceIds(serviceManager, instanceId, Lists.newArrayList("2"));
        Assert.assertNotNull(tasks);
        Assert.assertTrue(tasks.size() > 0);
    }


    @Test
    public void getHandleTaskList() throws Exception {
        String instanceId = startNewWorkflow();

        Page page = new Page();
        page.setPageNumber(1);
        page.setPageSize(20);
        page.setOrderBy("createTime");
        page.setAsc(false);
        PageResult<TaskOutline> dataList = taskService.getHandleTaskList(getServiceManager(context), false, null, page);
        Assert.assertNotNull(dataList.getDataList());

        //200任务中包不包含刚启动的流程实例
        boolean ret = false;
        for (TaskOutline outline : dataList.getDataList()) {
            //判断存不存在非进行中
            if (!outline.getState().equals("in_progress") && !outline.getState().equals("error")) {
                break;
            }
            if (outline.getWorkflowInstanceId().equals(instanceId)) {
                ret = true;
            }
        }
        Assert.assertTrue(ret);

    }

    @Test
    public void getHandleTaskList2() throws Exception {

        Page page = new Page();
        page.setPageNumber(1);
        page.setPageSize(20);
        page.setOrderBy("createTime");
        page.setAsc(false);
        PageResult<TaskOutline> dataList = taskService.getHandleTaskList(getServiceManager(context), false, null, page);


    }

    @Test
    public void tempTest() {
//        1.创建新客户对象
//        String relatedObjectId = "59100dd2bab09cdd77d9cd54";
//        String relatedEntityId ="object_27z__c";
        String objectId = "f7e3d0a9ce8a44b0900c1e78526f2d21";
        String taskId = "599ebc306886292238b76487";
//        String taskId = "58f2d3673db71dc86fdd4611";

//        List<TaskOutline> taskOutlines = taskService.getUncompletedTasksByObject(context, objectId);
//        context.setUserId("2005");
        context.setUserId("1001");
        context.setTenantId("57612");
        BPMTask task = taskService.getTask(getServiceManager(context), taskId);
//        Map<String, Object> data = Maps.newHashMap();
//        data.put(BPMConstants.ExtensionKey.relatedObjectId, relatedObjectId);
//        taskService.completeTask(context, taskId, objectId, null, data);
//        data.put(BPMConstants.ApproveResult.RESULT,"agree");
//        taskService.completeTask(context, taskId, objectId, null, data);

    }

    @Test
    public void tempTest2() {

//
//        context.setTenantId("7");
//        context.setUserId("1001");
        String taskId = "59afb1e6688629f366ac9a9c";
        context.setTenantId("57612");
        context.setUserId("1001");
        BPMTask task = taskService.getTask(getServiceManager(context), taskId);
//        instanceService.startWorkflow(context, "245058783641501696", "58fef51fbab09c21cef400b6");
//        List<TaskOutline> taskOutlines = taskService.getUncompletedTasksByObject(context,
//                "265ff588887a4cfbb2232936ca0c7db1");
//        Map<String,Object> result = Maps.newHashMap();
//        result.put("url","222");
//        result.put("name","");
//        result.put("tel","");
//        taskService.completeTask(context, "58de3d7f3db71d922aa9454a", "c13d9abdabb648e9ae6517e24ef32df1" ,null, null);
//        List<BPMTask> tasks = taskService.getTasksByInstanceIds(context, "58f2e4353db71dc86fdd4614", Lists
//                .newArrayList("4"));
//        instanceService.cancelWorkflowInstance(context, "58c35ed53db71d2d80398057");
//        Page page = new Page();
//        page.setOrderBy("createTime");
//        page.setPageNumber(1);
//        page.setPageSize(20);
//        page.setAsc(false);
//        PageResult<TaskOutline> taskoutlines = taskService.getHandleTaskList(context, false,null, page);
//        instanceService.getWorkflowInstances(context, InstanceState.pass_or_cancel,
//                "1655606a3da8425b8b7f5beda770440e", null, null, 100, 1);
    }

    @Test
    public void completeTask() {
        RefServiceManager serviceManager = getServiceManager(context);
        //1.启动流程
        String instanceId = startNewWorkflow();
//        String instanceId = "58b67d8f3db71da1178fb703";
        //2.查找未完成的activityInstanceId
        WorkflowInstance workflowInstance = instanceService.getWorkflowInstance(serviceManager, instanceId);
        String unHandleTaskInstanceId = null;
        List<ActivityInstance> activityInstances = workflowInstance.getActivityInstances();
        for (ActivityInstance activityInstance : activityInstances) {
            if (activityInstance.getEnd() == null) {
                unHandleTaskInstanceId = String.valueOf(activityInstance.getId());
                log.debug("================activity name={}", activityInstance.getActivityName());
                break;
            }
        }
        Assert.assertNotNull(unHandleTaskInstanceId);

        //3.完成任务
        List<BPMTask> bpmTasks = taskService.getTasksByInstanceIds(serviceManager, instanceId, Lists.newArrayList
                (unHandleTaskInstanceId));
        String taskId = bpmTasks.get(0).getId();
        log.debug("==================taskId={}", taskId);
        Map<String, Object> data = Maps.newHashMap();
        data.put(BPMConstants.ApproveResult.RESULT, true);
        taskService.completeTask(serviceManager, taskId, "同意，很好", data, null, null,false,false, null); //非创建结点
        Assert.assertTrue(true);

//        4.判断任务状态
        BPMTask task = taskService.getTask(serviceManager, taskId);
//        BPMTask task = taskService.getTask(context, "58b7a6f93db71d6d41fc9c06");
        Assert.assertTrue(task.getState().equals(TaskState.pass));
        Assert.assertTrue(task.getCompleted().booleanValue());
    }

    @Test
    public void getTaskLogs() throws Exception {
        List<TaskLog> taskLogs = taskService.getTaskLogs(getServiceManager(context), "59c8be363db71d9d13e2b74b");
//        Assert.assertTrue(taskLogs.size()==40);
    }

    @Test
    public void changeTaskHanlders() throws Exception {
        boolean ret = taskService.changeTaskHandlers(getServiceManager(context), "58fd649e3db71d2804505ef8", Lists.newArrayList("2003",
                "2001"),"" );
        Assert.assertTrue(ret);
    }

    @Test
    public void getWorkflowTasks() throws Exception {
        List<LaneBriefTaskVO> ret = taskService.getWorkflowUncompletedTasks(getServiceManager(context), "381608295523811328", LaneBriefTaskVO
                .QueryType.activity, "1533798396233", null);
    }

    @Test
    public void getMTask() throws Exception {
        taskService.getTask(getServiceManager(context), "59ed5be53db71d122f7565ed");
//        taskService.completeTask(context, "59ed5be53db71d122f7565ed",null,null);
    }

    @Test
    public void getActivityTimeOutTask() {
        RemoteContext context = new RemoteContext("", "71554", BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        String sourceWorkflowId = "481575979694292992";
        String id = "1563591101488";
        LaneBriefTaskVO.QueryType type = LaneBriefTaskVO.QueryType.activity;
        LaneBriefTaskVO.QueryState state = LaneBriefTaskVO.QueryState.timeout;
        Page page = new Page(20, 1, "last_modified_time", true);


        PageResult<LaneBriefTaskVO> pageResult = taskService.getAllWorkflowTasks(getServiceManager(context), sourceWorkflowId, type, id, state, page);


        System.out.println(JacksonUtil.toJson(pageResult));
    }

    @Test
    public void getLaneTimeOutTask() {
        RemoteContext context = new RemoteContext("", "71554", BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        String sourceWorkflowId = "481575979694292992";
        String id = "1563591101489";
        LaneBriefTaskVO.QueryType type = LaneBriefTaskVO.QueryType.lane;
        LaneBriefTaskVO.QueryState state = LaneBriefTaskVO.QueryState.timeout;
        Page page = new Page(20, 1, "last_modified_time", true);


        PageResult<LaneBriefTaskVO> pageResult = taskService.getAllWorkflowTasks(getServiceManager(context), sourceWorkflowId, type, id, null, page);


        System.out.println(JacksonUtil.toJson(pageResult));
    }


    @Test
    public void getAutomaticAndQuartzTaskByInstanceId() {
        RemoteContext context = new RemoteContext("", "54821", BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);

        List<Task> tasks = taskService.getAutomaticAndQuartzTaskByInstanceId(getServiceManager(context), "5c9b5e8b3db71d55de4d8f68");

        System.out.println(tasks.size());

    }


    @Test
    public void afterfActionRetry() {
        RemoteContext context = new RemoteContext("", "54821", BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        //BPMConstants.LATENCY_SUFFIX
        //BPMConstants.EXECUTION_SUFFIX
        AfterRetry.RetryResult retryResult = taskService.afterActionRetry(getServiceManager(context), "5c82bbbf3db71df1b91c8564", 1, 0);
        System.out.println(retryResult.isSuccess() ? "成功" : retryResult.getMessage());
    }


    @Test
    public void getUpdateBpmTaskDetail(){
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setClientType(ClientType.android);
        RemoteContext context = new RemoteContext("", "74203", "BPM", "1000");
        context.setClientInfo(clientInfo);
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5d66851968862930fd7eaa0b",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }


    @Test
    public void getApproveBpmTaskDetail(){
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1026");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5cb972f56886294082bda92c",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }

    @Test
    public void getCustomOperationBpmTaskDetail(){
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1007");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5cb9765c6886294082bda940",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }


    @Test
    public void getLeadsObjOperationBpmTaskDetail(){
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1007");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5cb976f36886294082bda947",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }


    @Test
    public void getREFBpmTaskDetail(){
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1007");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5cb97bee6886294082bda960",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }

    @Test
    public void getMDBpmTaskDetail(){
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1007");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"638899033edb816cde529d5c",null,null,null, TaskParams.create().includeTaskFeedDetailConfig(true));
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }


    //
    //查找关联5cc2b5816886292303356140
    @Test
    public void other(){
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1000");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5cb83f806886294082bd6eb5",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }

    @Test
    public void completeIsTrue(){
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setClientType(ClientType.android);
        RemoteContext context = new RemoteContext("", "54821", "BPM", "1007");
        context.setClientInfo(clientInfo);
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5ccfa1a43db71d04e9fb1148",null,null,null, TaskParams.create());
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }

    @Test
    public void setMoreOperations(){
        TaskDetail taskDetail = new TaskDetail();
        taskDetail.setMoreOperations(Lists.newArrayList(new MoreOperationManager.MoreOperation("stopBPM","停止流程")));
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }


    @Test
    public void getTaskDetail(){
        RemoteContext context = new RemoteContext("", "79299", "BPM", "1000");
        TaskDetail taskDetail = taskService.getBpmTaskDetail(getServiceManager(context),"5faca4d1b36de70001bb53dc",null,null,null,TaskParams.create().isIgnoreSignAndPayGroupLayout(true));
        log.info("Result:{}",JacksonUtil.toJson(taskDetail));
    }
}
