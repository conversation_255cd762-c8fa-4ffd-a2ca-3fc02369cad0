package com.facishare.bpm.service.impl;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.service.BPMTenantService;
import com.facishare.rest.core.model.RemoteContext;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class BPMTenantServiceImplTest extends BaseTest {

    @Autowired
    private BPMTenantService tenantService;

    @Test
    public void checkTenantI18n() {
        RemoteContext remoteContext = new RemoteContext("", "2", "BPM", "1000");
        boolean flag = tenantService.hasQuota(getServiceManager(context));
        System.out.println(flag);
    }
}
