package com.facishare.bpm.service.impl;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.WorkflowInstanceLog;
import com.facishare.bpm.model.instance.EntireWorkflow;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.paas.I18N;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by wangz on 17-2-27.
 */
@Slf4j
public class BPMInstanceServiceImplTest extends BaseTest {


    @Autowired
    private BPMInstanceService instanceService;

    @Test
    public void tempTest() {
        instanceService.getEntireWorkflowInstance(getServiceManager(context), "59c088463db71d74cdbe684f");
    }


    @Test
    public void triggerWorkflowForRest() {
//        instanceService.triggerWorkflowForRest(getServiceManager(context), TriggerSource.workflow, "5e8fd5228653354e24951d3a", "object_UPfNh__c", "5fc466107949e10001eabe8a");

        // 第一个节点是自动节点
//        for (int i = 0; i < 10; i++) {
        context = new RemoteContext("","82034", BPMConstants.APP_ID, userId);
            instanceService.triggerWorkflowForRest(getServiceManager(context), TriggerSource.workflow,null, "603cc3de88e31000018e79b4", "object_UPfNh__c", "603ce02afdd03200012bea2d","603cc3de88e31000018e79b3", null);
//        }
        //第一个节点非自动节点
//        for (int i = 0; i < 10; i++) {
//            instanceService.triggerWorkflowForRest(getServiceManager(context), TriggerSource.workflow, "5fbdc0b5ae494d0001142498", "object_UPfNh__c", "5fc466107949e10001eabe8a");
//        }

    }


    @Test
    public void cancelWorkflowInstance() throws Exception {
//        String instanceId = startNewWorkflow();
        String instanceId = "682fe1684d5898347b55574e";
        context = new RemoteContext("","86163", BPMConstants.APP_ID, "-10000");
        context.setFsPeerDisplayName("func_ABCD__c");
        instanceService.cancelWorkflowInstance(getServiceManager(context), instanceId, "测试11111", "function");

        WorkflowInstance instance = instanceService.getWorkflowInstance(getServiceManager(context), instanceId);
        Assert.assertTrue(instance.getState().equals("cancel"));
    }

    @Test
    public void getWorkflowInstance() throws Exception {
        String instanceId = startNewWorkflow();
        Assert.assertNotNull(instanceService.getWorkflowInstance(getServiceManager(context), instanceId));
    }

    @Test
    public void getWorkflowInstances() throws Exception {
        int pageSize = 10;
        int page = 1;
        context.setTenantId("71698");
        context.setUserId("1001");
        PageResult<WorkflowInstanceVO> pageResult = instanceService.getWorkflowInstances(getServiceManager(context),
                null, "5c1204c8a5083dbb9e60068b",  pageSize, page, null, null);
    }

    @Test
    public void getEntireWorkflowInstance() {
//        String instanceId = startNewWorkflow();
//        EntireWorkflowInstance result = instanceService.getEntireWorkflowInstance(context, instanceId);
        EntireWorkflow result = instanceService.getEntireWorkflowInstance(getServiceManager(context), "597aa4ec3db71da53fb6c8fb");
        Assert.assertTrue(!Strings.isNullOrEmpty(result.getSvg()));
        Assert.assertTrue(result.getWorkflowInstance() != null);
    }

    @Test
    public void getNewEntireWorkflowInstance() {
//        String instanceId = startNewWorkflow();
//        EntireWorkflowInstance result = instanceService.getEntireWorkflowInstance(context, instanceId);
        EntireWorkflow result = instanceService.getEntireWorkflowInstance(getServiceManager(context), "58e778f63db71d2aa4311789");
        Assert.assertTrue(!Strings.isNullOrEmpty(result.getSvg()));
    }


    @Test
    public void getWorkflowStatsDataByPG(){

        I18N.setContext("71557", "en");
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);

        RemoteContext context = new RemoteContext("","71557","BPM","1007");
        Pair ret = instanceService.getWorkflowStatsDataByPG(getServiceManager(context), "61de46755659570001585d92");
        System.out.println(JacksonUtil.toJson(ret));
    }

    @Test
    public void getWorkflowInstanceLog(){
        RemoteContext context = new RemoteContext("","86163","BPM","1011");
        WorkflowInstanceLog workflowInstanceLog = instanceService.getWorkflowInstanceLog(getServiceManager(context),"682f2187419246389edd3d8c");
        System.out.println(JacksonUtil.toJson(workflowInstanceLog));
    }

    @Test
    public void getInstancesByObject(){
        RemoteContext context = new RemoteContext("","54821","BPM","2000");
        List<GetInstanceByObject> instancesByObject = instanceService.getInstancesByObject(getServiceManager(context),"AccountObj","b924c9cddf964d4a9cc8c5616370b0d2");
        System.out.println(JacksonUtil.toJson(instancesByObject));
        BPMInstanceServiceImpl service = new BPMInstanceServiceImpl();
        System.out.println(service.getReason(getServiceManager(context),"Account","3213"));
    }

    @Test
    public void getInstanceListTest() {
        context.setTenantId("74203");
        PageResult<WorkflowInstanceVO> workflowInstances = bpmMetadataServiceImpl.findDataByQuery(context, TransferDataConstants.APINAME_INSTANCE,
                InstanceState.pass, "506018551770513408", "form默认值", "5d380126a5083df61a3adc3a", 66, 1);
        System.out.println("workflowInstances:" + JsonUtil.toJson(workflowInstances));
    }

    /**
     * sourceWorkflowId:5eb36f01865335b518f81d70
     */
    @Test
    public void triggerWorkflow() {
        String outlineId = "5eb36f03865335b518f81d71";
        String objectId = "5eb92db763b8e1000134b612";
        RemoteContext context = new RemoteContext("", "71557", BPMConstants.APP_ID, "-10000");
        instanceService.triggerWorkflow(getServiceManager(context), TriggerSource.person, null, outlineId, objectId);
    }
}
