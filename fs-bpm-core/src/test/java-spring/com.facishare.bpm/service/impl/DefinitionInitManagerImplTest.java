package com.facishare.bpm.service.impl;

import com.facishare.bpm.manage.impl.DefinitionInitManagerImpl;
import com.facishare.bpm.model.WorkflowOutline;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.Map;

/**
 * Created by wangzhx on 2018/11/20.
 */
public class DefinitionInitManagerImplTest{
    private DefinitionInitManagerImpl definitionInitManager = new DefinitionInitManagerImpl();

    @Test
    public void testGetInitDefinition(){
        definitionInitManager.init();
        Map<String,Object> variables = Maps.newHashMap();
//        variables.put("name","ceu111i");
        WorkflowOutline workflowOutline = definitionInitManager.getInitDefinition("object_OOOO__c");
        System.out.println(workflowOutline);
    }
}
