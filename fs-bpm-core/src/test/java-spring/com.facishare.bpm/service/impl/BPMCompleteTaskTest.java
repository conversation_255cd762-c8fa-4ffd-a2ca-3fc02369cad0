package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Maps;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.rest.core.util.JsonUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by cuiyongxu on 17/2/7.
 */
@Slf4j
public class BPMCompleteTaskTest extends BaseTest {

    @Autowired
    private BPMDefinitionService bpmDefinitionService;

    @Autowired
    private BPMTaskService bpmTaskService;

    @Autowired
    private BPMInstanceService bpmInstanceService;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;

    public RemoteContext context;

    private String objectId = "7a5ef35bb06d4ac7befc75c3c5e990f8";

    @Before
    public void setUp() {
        context = new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setEa("2");
        context.setTenantId("2");
        context.setUserId("1000");//1317
    }


    /**
     * 执行流程实例
     */
    @Test
    public void execute() {
        RefServiceManager serviceManager = getServiceManager(context);
        String objectId = "7a5ef35bb06d4ac7befc75c3c5e990f8";
        String outlineId = "229945473405452288";
        String entityId = "AccountObj";
        String instanceId = "";
        instanceId = "58ba4ce63db71d22c9409f70";//bpmDefinitionService.startWorkflow(context, outlineId, entityId, objectId);
        String result = "agree";//agree,reject
        String opinion = "审批意见";
        log.info("实例Id:{}", instanceId);
        WorkflowInstance workflowInstance = bpmInstanceService.getWorkflowInstance(serviceManager, instanceId);
        if (workflowInstance.getEnd() != null) {
            log.info("当前流程实例已结束:{}", instanceId);
            return;
        }
        List<String> activityInstances = workflowInstance.getActivityInstances().parallelStream().map(e -> e.getId() + "").collect(Collectors.toList());
        PageResult<Task> tasksResult = paasWorkflow.getTasksByInstanceIds(context, instanceId,
                activityInstances);
        List<Task> bpmTasks = tasksResult.getDataList();
        log.info("任务个数:{}", bpmTasks.size());
        bpmTasks.forEach(e -> {
            String taskId = e.getId();
            log.info("taskId:{}", taskId);
            if ("pass".equals(e.getState().name())) {
                log.info("当前任务已完成:{}", taskId);
                return;
            }

            String id = "";
            List<String> candidateIds = e.getCandidateIds();
            Map map = Maps.newHashMap();
            //会签如何如何
            if ("all".equals(e.getTaskType())) {
                log.info("当前节点为会签节点:{}", e.getTaskType());
                map.put(BPMConstants.ApproveResult.RESULT, result);//设置全部同意
                //所有人执行
                candidateIds.forEach(c -> {
                    log.info("当前审批用户:{}", c);
                    context.setUserId(c);
                    bpmTaskService.completeTask(serviceManager, taskId, opinion, map, null, null, false,false, null);
                    if (true) {
                        log.info("会签审批成功:{}", taskId);
                    } else {
                        log.info("会签审批失败:{}", taskId);
                    }
                });
            } else {
                log.info("当前节点为普通任务节点:{}", e.getTaskType());
                //非会签随便一个人审批
                id = candidateIds.get(0);
                log.info("当前审批用户:{}", id);
                context.setUserId(id);
                map.put(BPMConstants.ApproveResult.RESULT, result);//设置全部同意
                boolean flag = true;
                bpmTaskService.completeTask(serviceManager, taskId, opinion, map, null, null, true,false, null);
                if (flag) {
                    log.info("普通任务审批成功:{}", taskId);
                } else {
                    log.info("普通任务审批失败:{}", taskId);
                }
            }
        });
    }


    /**
     * 执行单个任务
     */
    @Test
    public void allTypeExecute() {
        String taskId = "58ba4e0b3db71d22c9409f74";
        String opinion = "审批意见" + new Random().nextInt();
        Map map = Maps.newHashMap();
        map.put(BPMConstants.ApproveResult.RESULT, "agree");
        context = new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setEa("2");
        context.setTenantId("2");
        context.setUserId("1000");
        boolean flag = true;
        bpmTaskService.completeTask(getServiceManager(context), taskId, opinion, map, null, null, true,false, null);
        if (flag) {
            log.info("普通任务审批成功:{}", taskId);
        } else {
            log.info("普通任务审批失败:{}", taskId);
        }
    }

    //1.创建对象
    //2.执行节点
    //229720315113668608
    @Test
    public void deployWorkflow() {
        String json = getOutlineJsonFromFile("process2.json");
        WorkflowOutline workflowOutline = JsonUtil.fromJson(json, WorkflowOutline.class);
        String id = bpmDefinitionService.deployWorkflow(getServiceManager(context), workflowOutline, true, true);
        log.debug("_id ============ {}", id);
        Assert.assertNotNull(id);
    }


    /**
     * 启动流程实例 58b944323db71db0dfeb6e92
     */
    @Test
    public void startWorkflow() {
        Assert.assertNotNull(bpmInstanceService.startWorkflow(getServiceManager(context), TriggerSource.approval, "229711423658950656", objectId));
    }


    /**
     * 更新客户对象详细地址
     */
    @Deprecated
    @Test
    public void completeTaskCreateObject() {
        Map<String, Object> conditionMap = new HashMap<>();

        conditionMap.put("tel", "13681467423");
        bpmTaskService.completeTask(getServiceManager(context), "58b9505e3db71db0dfeb6e97", "审批意见", conditionMap, null, null, true,false, null);
    }

    /**
     * 审批任务
     */
    @Test
    public void completeTaskApproveObject() {
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put(BPMConstants.ApproveResult.RESULT, "agree");
        bpmTaskService.completeTask(getServiceManager(context), "58b950fa3db71db0dfeb6e98", "审批意见", conditionMap, null, null, true,false, null);
    }


    /**
     * 更新对象
     */
    @Test
    public void completeTaskUpdateObject() {
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("tel", "13681467423");
        bpmTaskService.completeTask(getServiceManager(context), "58b9514a3db71db0dfeb6e99", "审批意见", conditionMap, null, null, true,false, null);
    }

    /**
     * 报价
     */
    @Test
    public void completeTaskAmountsObject() {
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("total_order_amount", 80000);
        bpmTaskService.completeTask(getServiceManager(context), "589d8fce0959b67356aa4173", "审批意见", conditionMap, null, null, true,false, null);
    }

    /**
     * 创建联系人
     */
    @Test
    public void completeTaskContactObject() {
        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("name", "测试22eee42336");
        conditionMap.put("department", "BPeM34ee3组");
        conditionMap.put("account_id", "7a5ef35bb06d4ac7befc75c3c5e990f8");
        conditionMap.put("email", "<EMAIL>");
        bpmTaskService.completeTask(getServiceManager(context), "58a57ac60959b6c68b8970fe", "审批意见", conditionMap, null, null, true,false, null);
    }

    /**
     * 执行任务
     */
    @Test
    public void completeTaskExecuteObject() {
        Map<String, Object> conditionMap = new HashMap<>();
        bpmTaskService.completeTask(getServiceManager(context), "589d8b2b0959b67356aa4169", "审批意见", conditionMap, null, null, true,false, null);
    }


    @Test
    public void getTask() {
        context.setTenantId("54821");
        context.setUserId("1000");
        Assert.assertNotNull(bpmTaskService.getTask(getServiceManager(context), "5bdc13bf3db71d5434162dac"));
    }


    @Test
    public void completeLatencyTaskObject() {
        context = new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setEa("2");
        context.setTenantId("2");
        context.setUserId("1000");
        bpmTaskService.completeTask(getServiceManager(context), "latency_5ca3190d3db71db5c8f3eb6c", "", Maps.newHashMap(), 1, Maps.newHashMap(), true,false, null);
    }


    @Test
    public void completeTaskByUpdate() {
        context = new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setTenantId("54821");
        context.setUserId("1000");
    }

}
