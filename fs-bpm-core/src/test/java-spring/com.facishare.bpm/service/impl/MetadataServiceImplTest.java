package com.facishare.bpm.service.impl;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.remote.metadata.impl.MetadataServiceImpl;
import com.facishare.bpm.remote.model.matedata.SimpleMetadataDesc;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.elasticsearch.common.Strings;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;
import java.util.Map;

/**
 * Created by wansong on 2019/8/15.
 */
@Slf4j
public class MetadataServiceImplTest extends BaseTest {
  @Autowired
  @Qualifier("bpmMetadataServiceImpl")
  private MetadataServiceImpl metadataService;

  @Test
  public void findDescsByApiNames() {
//    for(int i=71554;i<100000;i++){
      String apiName="71554";
    context.setTenantId(apiName);
    List<SimpleMetadataDesc> rst = metadataService.findDescsByApiNames(context,
      Lists.newArrayList(),
      true);
    rst.stream().forEach(item -> {
      item.getFields().values().stream().map(field -> (Map) field).forEach(field -> {
        boolean isActive = Boolean.valueOf(((Map) field).get("is_active") == null ? false : (boolean) ((Map) field).get("is_active"));
        String type = (String) ((Map) field).get("type");
        if (isActive && type.equals("count") && !Strings.isNullOrEmpty((String) ((Map) field).get("field_api_name"))) {
          try{
          Asserts.notBlank((CharSequence) ((Map) field).get("lookup_field_type"),
            ((Map) field).get("name") + " lookup_field_type key 补充失败 " + ((Map) field).get("describe_api_name") + "." + ((Map) field).get("field_api_name"));
          }catch (Exception e){
            log.error("",e);
          }
        }
      });
    });
//  }
  }
}
