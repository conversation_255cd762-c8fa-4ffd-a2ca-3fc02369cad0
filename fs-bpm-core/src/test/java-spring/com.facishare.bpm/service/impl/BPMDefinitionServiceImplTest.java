package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;


/**
 * Created by wangz on 17-2-27.
 */
@Slf4j
public class BPMDefinitionServiceImplTest extends BaseTest {


    @Test
    public void updateDefine() {
        RemoteContext context = new RemoteContext("", "71630", BPMConstants.APP_ID, userId);
        String processJson = getOutlineJsonFromFile("deploy2.json");
        WorkflowOutline outline = JsonUtil.fromJson(processJson, WorkflowOutline.class);
//        WorkflowOutlineEntity entity = definitionService.updateWorkflow(context, outline, true, true);
        WorkflowOutlineEntity entity = definitionService.updateWorkflow(getServiceManager(context), outline, true, true);
        if (!Strings.isNullOrEmpty(entity.getId())) {
            log.info("流程更新成功");
        }
    }


    @Test
    public void tempTest() throws Exception {
        String outlineId = "237758348991823872";
        context.setTenantId("57612");
        context.setUserId("1001");

        List ret = definitionService.getAvailableWorkflows(getServiceManager(context), "AccountObj", null, false, 0,null, Boolean.TRUE);
//        Assert.assertTrue(ret);
    }

    @Test
    public void getAvailableWorkflows() throws Exception {
//        List<WorkflowOutline> outlines = definitionService.getAvailableWorkflows(context, "AccountObj", true, null);
        List<WorkflowOutline> outlines = definitionService.getAvailableWorkflows(getServiceManager(context), "AccountObj", null, false, null,null, Boolean.TRUE);
        Assert.assertNotNull(outlines);
        SimpleDateFormat sdf = new SimpleDateFormat("YYYY-MM-dd");
        outlines.forEach(outline -> {
            System.out.println(outline.getName() + "---" + sdf.format(outline.getLastModifiedTime()));
        });
    }

    @Test
    public void getWorkflowOutlines() throws Exception {
//        String outlineId = deploySimpleProcess();
        Page page = new Page();
        page.setPageNumber(1);
        page.setPageSize(10);
        PageResult<WorkflowOutline> pageResult = definitionService.getWorkflowOutlines(getServiceManager(context), null, page, null, false);
        Assert.assertNotNull(pageResult.getDataList());
        Assert.assertTrue(pageResult.getTotal() > 0);
    }

    @Test
    public void deployWorkflow() throws Exception {
        String outlineId = deploySimpleProcess();
        Assert.assertNotNull(outlineId);

    }

    @Test
    public void updateDefinition() throws Exception {
        String workflowOutlineJson = "{\"id\":\"268268519681589248\",\"tenantId\":\"54821\",\"userId\":\"1000\"," +
                "\"sourceWorkflowId\":\"268268519446708224\",\"workflowId\":\"59687fa93db71dcf66388ff1\",\"name\":\"测试rule\",\"count\":1,\"enabled\":true,\"description\":\"\",\"entryType\":\"AccountObj\",\"entryTypeName\":\"客户\",\"rangeEmployeeIds\":[2003,2005,1000],\"rangeCircleIds\":[],\"rangeGroupIds\":[],\"rangeRoleIds\":[],\"createdBy\":\"1000\",\"createTime\":*************,\"lastModifiedBy\":\"1000\",\"lastModifiedTime\":*************,\"workflow\":{\"type\":\"workflow_bpm\",\"name\":\"测试rule\",\"description\":\"\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"*************\"},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":1,\"remindContent\":\"${workflowName}的任务 ${taskName} 剩余处理时间还有 ${remindTime} 小时，请尽快处理。\",\"remindTime\":-2,\"remindTitle\":\"业务流程超时提醒\",\"remindTargets\":{\"ext_bpm\":[\"activity_*************##assigneeId##leader$$节点处理人上级\"]}}],\"bpmExtension\":{\"actionCode\":\"\",\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"AccountObj\",\"entityName\":\"客户\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"remindLatency\":20,\"canSkip\":false,\"remind\":true,\"name\":\"业务活动\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"activity_*************##AccountObj##owner$$数据相关人员-数据负责人\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[{\"taskType\":\"updates\"},{\"taskType\":\"updates\"}]},\"rule\":{\"conditionPattern\":\"(0)\",\"conditions\":[{\"leftSide\":{\"fieldName\":\"account_source\",\"fieldSrc\":\"activity_*************##AccountObj\",\"type\":\"string\"},\"operator\":\"equals\",\"rightSide\":{\"value\":\"1\"},\"rowNo\":0}]}},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"*************\"}],\"transitions\":[{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"serialNumber\":0},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"serialNumber\":0}],\"variables\":[{\"id\":\"activity_0##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}}],\"id\":\"59687fa93db71dcf66388ff1\",\"sourceWorkflowId\":\"268268519446708224\"},\"rule\":{\"id\":\"59687fa93db71dcf66388ff2\",\"deleted\":false,\"tenantId\":\"54821\",\"appId\":\"BPM\",\"entityId\":\"null\",\"conditionPattern\":\"(0)\",\"conditions\":[{\"rowNo\":0,\"leftSide\":{\"fieldName\":\"account_no\",\"type\":\"string\",\"fieldSrc\":\"AccountObj\"},\"operator\":\"contains\",\"rightSide\":{\"value\":\"123\"}}],\"workflowSrcId\":\"268268519446708224\"},\"extension\":{\"id\":\"268268519580925952\",\"pools\":[{\"lanes\":[{\"id\":\"*************\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"*************\",\"*************\",\"*************\"]}]}],\"diagram\":[{\"id\":\"*************\",\"attr\":{\"width\":220,\"height\":540,\"x\":40,\"y\":60}},{\"id\":\"*************\",\"attr\":{\"width\":60,\"height\":60,\"x\":120,\"y\":125}},{\"id\":\"*************\",\"attr\":{\"width\":160,\"height\":50,\"x\":70,\"y\":260}},{\"id\":\"*************\",\"attr\":{\"width\":60,\"height\":60,\"x\":103,\"y\":392}},{\"id\":\"*************\",\"attr\":{\"d\":\"M150,310 v33 a8.5,8.5 0 0 1 -8.5,8.5 h0 a8.5,8.5 0 0 0 -8.5,8.5 v33\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}},{\"id\":\"*************\",\"attr\":{\"d\":\"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}}],\"svg\":\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" class=\\\"paas-bpm-canvas\\\" height=650 width=310 tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><defs><marker id=\\\"end-arrow\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#666666\\\"></path></marker><marker id=\\\"end-arrow-colored\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#49bffc\\\"></path></marker><marker id=\\\"approval-yes\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#7FC25D\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><marker id=\\\"approval-no\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#DC9688\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><filter id=\\\"box-shadow\\\"><feGaussianBlur in=\\\"SourceAlpha\\\" stdDeviation=\\\"2\\\"></feGaussianBlur><feOffset dx=\\\"0\\\" dy=\\\"1\\\" result=\\\"offsetblur\\\"></feOffset><feFlood flood-color=\\\"black\\\" flood-opacity=\\\"0.06\\\"></feFlood><feComposite in2=\\\"offsetblur\\\" operator=\\\"in\\\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\\\"SourceGraphic\\\"></feMergeNode></feMerge></filter><style type=\\\"text/css\\\">svg {\\n        background-color: #f3f3f5;\\n      }\\n\\n      g[type=pool] {\\n        font-size: 13px;\\n      }</style></defs><g class=\\\"paas-bpm-canvas-wrapper\\\" height=\\\"100%\\\" width=\\\"100%\\\" transform=\\\"translate(0,0) scale(1)\\\" k=\\\"100\\\"><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"pool\\\" x=\\\"40\\\" y=\\\"60\\\" width=\\\"220\\\" height=\\\"540\\\" transform=\\\"translate(40,60)\\\" tabindex=\\\"0\\\" class=\\\"paas-bpm-resizable\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"540\\\" namePos=\\\"top\\\" highlight=\\\"false\\\" stroke=\\\"#cccccc\\\" rx=\\\"0\\\" ry=\\\"0\\\" resizable=\\\"true\\\" placeAt=\\\"first\\\" external=\\\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"220\\\" height=\\\"540\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"-4.5\\\" y=\\\"265.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"105.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"215.5\\\" y=\\\"265.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"105.5\\\" y=\\\"535.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><g name=\\\"external\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"60\\\" stroke=\\\"transparent\\\" rx=\\\"0\\\" ry=\\\"0\\\" y=\\\"0\\\" class=\\\"paas-bpm-pool-drag-area\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"30\\\" x=\\\"110\\\">阶段</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 545)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g><rect width=\\\"18\\\" height=\\\"18\\\" class=\\\"paas-bpm-badge\\\" x=\\\"0.5\\\" y=\\\"0.5\\\" fill=\\\"#ccc\\\"></rect><text class=\\\"paas-bpm-badge-text\\\" text-anchor=\\\"middle\\\" fill=\\\"#70757f\\\" x=\\\"9\\\" y=\\\"13\\\">1</text></g><g name=\\\"line-wrapper\\\"><g tabindex=\\\"0\\\" data-id=\\\"*************\\\"><path d=\\\"M150,310 v33 a8.5,8.5 0 0 1 -8.5,8.5 h0 a8.5,8.5 0 0 0 -8.5,8.5 v33\\\" start-id=\\\"*************\\\" end-id=\\\"*************\\\" fill=\\\"transparent\\\" stroke-width=\\\"1\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow)\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"*************\\\"><path d=\\\"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\\\" start-id=\\\"*************\\\" end-id=\\\"*************\\\" fill=\\\"transparent\\\" stroke-width=\\\"1\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow)\\\"></path></g></g><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"startEvent\\\" x=\\\"120\\\" y=\\\"125\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(120,125)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><rect fill=\\\"#f3f3f5\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"3\\\" color=\\\"#70757f\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"52.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"52.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"35\\\" x=\\\"30\\\" fill=\\\"#70757f\\\">开始</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 65)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"70\\\" y=\\\"260\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(70,260)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"\\\"><rect fill=\\\"white\\\" width=\\\"160\\\" height=\\\"50\\\" rx=\\\"3\\\" ry=\\\"3\\\" filter=\\\"url(#box-shadow)\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"17.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"72.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"152.5\\\" y=\\\"17.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"72.5\\\" y=\\\"42.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"30\\\" x=\\\"80\\\">业务活动</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 55)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"endEvent\\\" x=\\\"103\\\" y=\\\"392\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(103,392)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><rect fill=\\\"#f3f3f5\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"3\\\" color=\\\"#70757f\\\" external=\\\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"52.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"52.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><g name=\\\"external\\\"><rect x=\\\"4\\\" y=\\\"4\\\" fill=\\\"transparent\\\" rx=\\\"52\\\" ry=\\\"52\\\" width=\\\"52\\\" height=\\\"52\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"1\\\" color=\\\"#e67373\\\" type=\\\"rect\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"35\\\" x=\\\"30\\\" fill=\\\"#70757f\\\">结束</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 65)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g></g></svg>\"},\"isDeleted\":false,\"hasInstance\":true}";
        WorkflowOutline outline = JsonUtil.fromJson(workflowOutlineJson, WorkflowOutline.class);
        definitionService.updateWorkflow(getServiceManager(context), outline, true, false);

    }

    @Test
    public void startWorkflow() throws IOException {
//        String outlineId = deployProcess("allPassTest.json");
//        String outlineId = deploySimpleProcess();
//        String objectId = createNewAccount();
        RefServiceManager serviceManager = getServiceManager(context);
        context.setUserId("1000");
        context.setTenantId("2");
        String outlineId = "241056090681933824";
        String objectId = "9738484cb95d4d999dadb4367523cc06";
        String instanceId = instanceService.startWorkflow(serviceManager, TriggerSource.approval, outlineId, objectId);
//        String instanceId = definitionService.startWorkflow(context, "228864715987255296", entityId, "3d0f6fd6aff6472d986aefb7c497e4b1");
        log.debug("=================INSTANCE_ID={}", instanceId);
        Assert.assertNotNull(instanceId);
        instanceService.startWorkflow(serviceManager, TriggerSource.approval, outlineId, objectId);
    }


    @Test(expected = BPMBusinessException.class)
    public void deleteWorkflowById() throws Exception {
        RefServiceManager serviceManager = getServiceManager(context);
        String outlineId = deploySimpleProcess();
        Assert.assertTrue(definitionService.deleteWorkflowById(serviceManager, outlineId));

        outlineId = deploySimpleProcess();
        String objectId = createNewAccount();
        instanceService.startWorkflow(serviceManager, TriggerSource.approval, outlineId, objectId);
        definitionService.deleteWorkflowById(serviceManager, outlineId);
    }

    @Test
    public void getWorkflowOutlineById() throws Exception {
        String outlineId = deploySimpleProcess();
        WorkflowOutline outline = definitionService.getWorkflowOutlineById(getServiceManager(context), outlineId);
        Assert.assertNotNull(outline);
    }

    @Test
    public void enableWorkflow() throws Exception {
        RefServiceManager serviceManager = getServiceManager(context);
        String outlineId = deploySimpleProcess();
        definitionService.enableWorkflow(serviceManager, outlineId, false);

        List<WorkflowOutline> list = definitionService.getAvailableWorkflows(serviceManager, entityId, null, false, 1,null, Boolean.TRUE);
        boolean result = true;
        for (WorkflowOutline outline : list) {
            if (outline.getId().equals(outlineId)) {
                result = false;
                break;
            }
        }
        Assert.assertTrue(result);
    }

    @Test
    public void getWorkflowExtensionByWorkflowId() throws Exception {

        String outlineId = deploySimpleProcess();
        WorkflowOutline outline = definitionService.getWorkflowOutlineById(getServiceManager(context), outlineId);

        WorkflowExtension extension = definitionService.getWorkflowExtensionByWorkflowId(context, outline.getWorkflowId());
        log.debug("========================extesnion_id={}", extension.getId());
        Assert.assertNotNull(extension.getId());
    }


    @Test
    public void getWorkflowOutlineBySourceId() throws Exception {
        RefServiceManager serviceManager = getServiceManager(context);
        String outlineId = deploySimpleProcess();
        WorkflowOutline outline = definitionService.getWorkflowOutlineById(serviceManager, outlineId);


        outline = definitionService.getWorkflowOutlineBySourceId(serviceManager, outline.getSourceWorkflowId());
        Assert.assertNotNull(outline);
    }


    @Test
    public void getWorkflowEntryTypeNameMap() throws Exception {
        RefServiceManager serviceManager = getServiceManager(context);
        String outlineId = deploySimpleProcess();
        WorkflowOutline outline = definitionService.getWorkflowOutlineById(serviceManager, outlineId);

        Map<String, String> entryTypeMap = definitionService.getWorkflowEntryTypeNameMap(serviceManager, Lists.newArrayList
                (outline.getSourceWorkflowId()));
        Assert.assertNotNull(entryTypeMap);

    }

    @Test
    public void getWorkflowById() throws Exception {
        RefServiceManager serviceManager = getServiceManager(context);
        String outlineId = deploySimpleProcess();
        WorkflowOutline outline = definitionService.getWorkflowOutlineById(serviceManager, outlineId);

        ExecutableWorkflowExt workflow = definitionService.getWorkflowById(serviceManager, outline.getWorkflowId());
        Assert.assertNotNull(workflow);
    }

    @Test
    public void testDeleteWorkflow() {
        RefServiceManager serviceManager = getServiceManager(context);
        RemoteContext context = new RemoteContext();
        context.setTenantId("58335");
        definitionService.destroyTenant(serviceManager, true);
        List<WorkflowOutline> allWorkflowOutlines = definitionService.getAllWorkflowOutlines(serviceManager);
        boolean result = allWorkflowOutlines.stream().
                allMatch(workflowOutline -> workflowOutline.isDeleted() == true
                        && workflowOutline.isEnabled() == false
                        && workflowOutline.getCount() == 0);
        Assert.assertTrue(result);
    }


    @Test
    public void getWorkflowOutlineBySourceId2() throws Exception {

        RemoteContext context = new RemoteContext();
        context.setTenantId("71557");
        context.setUserId("1000");
        context.setAppId("BPM");

        definitionService.getWorkflowOutlineBySourceId(getServiceManager(context), "405678154806329344");

    }
}
