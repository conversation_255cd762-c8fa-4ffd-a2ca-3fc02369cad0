package com.facishare.bpm.service.impl;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.RefServiceManager;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.spockframework.util.Assert;

import java.util.Map;
import java.util.Set;

/**
 * Created by wangzhx on 2018/12/13.
 */
public class BPMOrganizationServiceTest extends BaseTest {

    @Test
    public void testGetPersons() {
        Map<String, Object> nextTaskAssigneeScope = Maps.newConcurrentMap();
        nextTaskAssigneeScope.put("group", Lists.newArrayList("5c120efe319d19c178379524"));
        nextTaskAssigneeScope.put("person", Lists.newArrayList("1002", "2566", "9856", "1002"));
        nextTaskAssigneeScope.put("dept", Lists.newArrayList("1000"));
        Set<String> bpm = getServiceManager(new RemoteContext("71557", "71557", "BPM", "1000")).getPersons(nextTaskAssigneeScope);
        System.out.println("bpm:------" + bpm);

    }

    @Test
    public void testCheckIsStop() {
//        bpmOrganizationService.validateAssignee(context,Lists.newArrayList("1012"),Lists.newArrayList("5b4d8eba319d1928b963ba9b"),Lists.newArrayList("1002"),Lists.newArrayList("00000000000000000000000000000002","5c19c120e4b039a555116f98"));
    }

    @Test
    public void testGetFieldPermissionTest() {
        RefServiceManager bpm = getServiceManager(new RemoteContext("71557", "71557", "BPM", "1000"));
        Assert.notNull(bpm.getFieldPermissions("AccountObj"));
    }
}
