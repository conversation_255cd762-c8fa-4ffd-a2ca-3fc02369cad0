package com.facishare.bpm.service.impl;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.OrgOutline;
import com.facishare.bpm.model.org.BPMOrg;
import com.facishare.bpm.service.BPMOrganizationService;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.ws.rs.OPTIONS;
import java.util.Map;

/**
 * Created by cuiyongxu on 17/5/4.
 */
@Slf4j
public class BPMOrganizationServiceImplTest extends BaseTest {

    @Autowired
    private BPMOrganizationService bpmOrganizationService;

    @Test
    public void getOrganization() {
        RemoteContext context = new RemoteContext("74164", "74164", "CRM", "1000");
        OrgOutline orgOutline = new OrgOutline();

//        orgOutline.setCRMGroup(Lists.newArrayList("58cfbc563db71d6164dee6c0", "58ec3edb3db71d6164dee6c3","58ae6f7d3db71dfa38fe5c7d","58b516793db71da77b6748b4"));//停用组
//        orgOutline.setDept(Lists.newArrayList(1000, 1020, 1062, 1009, 1057));//存在待开发
//        orgOutline.setPerson(Lists.newArrayList(1000, 1002, 1876, 1795));//停用用户
//        orgOutline.setRole(Lists.newArrayList("00000000000000000000000000000009", "00000000000000000000000000000020", "00000000000000000000000000000014"));//不存在启用停用

        orgOutline.setLinkAppId("FSAID_11490d9e");
        orgOutline.setLinkAppType(1);
        orgOutline.setExternalRole(Lists.newArrayList("outtenant_manager_role","outtenant_manager_leader_role","5fb5e76f4cd8b800018fe3fe","er_visitor","5dbfc89fe4b0311c1da8d8d3","5d8b34eee4b062c52b70cfac","5d8ad29ce4b062c52b70b1bf","5d88b80fe4b0ded922ee5d6e","5d84a08ce4b0a93f5a11c2a3","5d849ebbe4b0a93f5a11bfa3","5d8497d1e4b0a93f5a11bb12","5d8497b8e4b0a93f5a11b829","5d849004e4b06e1c8204cf2a","5d848f9ce4b06e1c8204cc04","5d834ab9e4b024bcb83295ef","5d82e9d0e4b00a5609f0dd7b","5d81df4be4b0b27ed244d420","5d81d90ee4b0ed759cc9dcf4","5d81989ee4b0a7d277fb2ffb","5d80848fe4b026017de793d3","5b74f2a7e4b05348496c9192","5b74f295e4b05348496c9087","5b6817bbe4b066655a6397e4"));
        BPMOrg org = bpmOrganizationService.getOrganization(getServiceManager(context), orgOutline);


        log.info("====={}", org.getPerson());
        log.info("====={}", org.getRole());
        log.info("====={}", org.getDept());
        log.info("====={}", org.getCRMGroup());
        log.info("====={}", org.getExternalPerson());
    }


    @Test
    public void externalRole() {
        RemoteContext context = new RemoteContext("2", "2", "CRM", "100");
        OrgOutline orgOutline = new OrgOutline();

        orgOutline.setExternalPerson(Lists.newArrayList("300000215"));//不存在启用停用

        BPMOrg org = bpmOrganizationService.getOrganization(getServiceManager(context), orgOutline);


        log.info("====={}", org.getExternalPerson());
    }



}
