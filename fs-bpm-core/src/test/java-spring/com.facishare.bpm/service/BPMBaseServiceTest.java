package com.facishare.bpm.service;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.RefServiceManager;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/4/8 7:42 PM
 */

public class BPMBaseServiceTest extends BaseTest {

    @Autowired
    private BPMBaseService bpmBaseService;

    @Test
    public void test() {
        RemoteContext context = new RemoteContext("", "71557", "BPM", "1000");
        RefServiceManager serviceManager = bpmBaseService.getServiceManager(context);
        Map<String, Object> fieldDesc = serviceManager.getFieldDesc("object_UPfNh__c", "field_q9v1T__c", false);
        System.out.println(JsonUtil.toJson(fieldDesc));
    }
}
