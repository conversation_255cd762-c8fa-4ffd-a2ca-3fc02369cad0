package com.facishare.bpm.service.remote;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;

import com.facishare.rest.core.util.JsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class PaasWorkflowServiceProxyTest extends BaseTest {

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;
    @Test
    public void cancel() {
        paasWorkflowServiceProxy.cancel(context, "5b5fcf843db71d7bce51541d", "测试退回原因");
    }

    @Test
    public void getInstance() {
        WorkflowInstance instance = paasWorkflowServiceProxy.getWorkflowInstance(context, "5b692d253db71d2b451e5cd9");
        System.out.println(JacksonUtil.toJson(instance));
    }
    @Test
    public void testBatchDeleteDefinition(){
        RemoteContext context=new RemoteContext();
        context.setEa("58335");
        context.setAppId(BPMConstants.APP_ID);
        context.setTenantId("58335");
        context.setUserId(BPMConstants.CRM_SYSTEM_USER);
        paasWorkflowServiceProxy.batchDeleteDefinition(context);
    }
    @Test
    public void testBatchDeleteInstance(){
        RemoteContext context=new RemoteContext();
        context.setEa("58335");
        context.setAppId(BPMConstants.APP_ID);
        context.setTenantId("58335");
        context.setUserId(BPMConstants.CRM_SYSTEM_USER);
        paasWorkflowServiceProxy.batchDeleteInstance(context);

    }


    @Test
    public void getCurrentUserInProgressTasks(){
        RemoteContext context=new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setTenantId("71557");
        context.setUserId("1007");
        List<Task> data = paasWorkflowServiceProxy.getCurrentUserInProgressTasks(context, "1007", "5f338ea70cd1df0001d2c535",
                "1007", "object_UPfNh__c", "5f338e67dfd4590001e85c1e", "5f337cd5f70b3300013cb0d0");
        System.out.println(JsonUtil.toJson(data));

    }
}
