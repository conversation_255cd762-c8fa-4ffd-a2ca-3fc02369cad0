package com.facishare.bpm.service.remote;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.model.resource.metadata.GetUserFieldPermiss;
import com.facishare.bpm.proxy.MetaDataAuthProxy;
import com.facishare.rest.core.model.RemoteContext;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON> on 08/03/2017.
 */

public class MetaDataRoleAuthServiceTest extends BaseTest {
    @Autowired
    private MetaDataAuthProxy metaDataAuthProxy;
    @Test
    public void getUserPrivilegeOfFields(){
        RemoteContext context=new RemoteContext("2","2","2","2");
        GetUserFieldPermiss.Result result=metaDataAuthProxy.getFieldsPrivilegeOfUser(context,"AccountObj");
        Assert.assertNotNull(result);
    }
}
