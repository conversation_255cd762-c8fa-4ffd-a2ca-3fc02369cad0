package com.facishare.bpm.helper;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.rest.core.util.JacksonUtil;
import org.junit.BeforeClass;
import org.junit.Test;

import java.util.List;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/8/22 2:14 PM
 */
public class TaskButtonConfigHelperTest extends BaseTest {


    @BeforeClass
    public static void setup(){

    }


    @Test
    public void getTaskFormButtons() {
        List<ActionButton> taskFormButtons = TaskButtonConfigHelper.getTaskFormButtons(ExecutionTypeEnum.approve, false);
        System.out.println(JacksonUtil.toJson(taskFormButtons));
    }
}

