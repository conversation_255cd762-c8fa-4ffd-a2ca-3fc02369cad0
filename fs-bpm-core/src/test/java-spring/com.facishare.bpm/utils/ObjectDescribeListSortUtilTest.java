package com.facishare.bpm.utils;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.remote.model.config.BPMObjectSupportConfig;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2019/1/21 4:35 PM
 */
public class ObjectDescribeListSortUtilTest {


    @Test
    public void testSort() {
        String field = getJsonFile("field_list.json");
        List<Map<String, Object>> field_list = JacksonUtil.fromJson(field, new TypeReference<List<Map<String, Object>>>() {
        });

        String fs_bpm_object_support_config_i18n = getJsonFile("fs-bpm-object-support-config");

        BPMObjectSupportConfig objectSupports = JsonUtil.fromJson(fs_bpm_object_support_config_i18n, BPMObjectSupportConfig.class);

        List<Map<String, Object>> rsts = ObjectDescribeListSortUtil.sort("71557",objectSupports, field_list);

        rsts.forEach(rst -> {
            System.out.println(rst.get("api_name"));
        });
    }


    protected String getJsonFile(String processFile) {
        String path = BaseTest.class.getResource("/").getPath() + "/data/" + processFile;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    isr.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }
}
