package com.facishare.bpm.utils;

import com.facishare.bpm.handler.task.button.model.DefaultActionLabel;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.Data;
import org.junit.Test;

import java.util.Map;

@Data

public class TaskStat {
    private String sourceWorkflowId;
    private String stageId;
    private String activityId;
    private String state;
    private int count;

    public static TaskStat create(
            String sourceWorkflowId, String stageId, String activityId, String state, int count
    ) {
        TaskStat stat = new TaskStat();
        stat.setSourceWorkflowId(sourceWorkflowId);
        stat.setStageId(stageId);
        stat.setActivityId(activityId);
        stat.setState(state);
        stat.setCount(count);

        return stat;
    }


    @Test
    public void _Test() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("update", "更新");

        Map<String, Object> child = Maps.newHashMap();
        child.put("lable", "1111");
        map.put("UpdateAndComplete", child);
        Map<String, DefaultActionLabel> defaultActionLabelMap = JacksonUtil.fromJson(
                JacksonUtil.toJson(map),
                new TypeReference<Map<String, DefaultActionLabel>>() {
                });
        System.out.println(1);
    }


}
