package com.facishare.bpm.utils;

import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.metadata.GetUserFieldPermiss;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.proxy.MetaDataAuthProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.helper.StopWatch;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTaskDataEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;


/**
 * Created by wangz on 17-3-8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
@Slf4j
public class TaskOutLineUtilsTest {

    @Autowired
    private MetaDataAuthProxy metaDataAuthProxys;
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;
    private Map<String, WorkflowInstance> workflowInstanceMap;
    private List<Task> tasks = Lists.newArrayList();
    private Map<String, FlowExtensionEntity> extensions;
    private RemoteContext context;
    private String objectId = "cfcab67d9d3140e1ab4b5468241801c8";
    private Map<String, Map<String, Object>> descMap;

    String workflowInstanceId = "instance_1";
    String taskIdPrefix = "task_";
    String activityIdPrefix = "activity_";
    String sourceWorkflowId = "source_1";
    String executionType = "update";
    String workflowId = "workflow_1";
    String extensionId = "extension_1";


    public void initUncompletedTasks() {
        context = initContext();


        //一个进行中任务，处理人1000
        int i = 1;
        Task task = generateTask(workflowInstanceId, taskIdPrefix + i, activityIdPrefix + i,
                sourceWorkflowId, workflowId, objectId, executionType, false, false, false, Lists.newArrayList("1000"));
        tasks.add(task);
        i++;

        //一个进行中任务，处理人2000
        task = generateTask(workflowInstanceId, taskIdPrefix + i, activityIdPrefix + i,
                sourceWorkflowId, workflowId, objectId, executionType, false, false, false, Lists.newArrayList("2000"));
        tasks.add(task);
        i++;

        //一个异常任务,处理人1000
        task = generateTask(workflowInstanceId, taskIdPrefix + i, activityIdPrefix + i,
                sourceWorkflowId, workflowId, objectId, executionType, false, false, true, Lists.newArrayList("1000"));
        tasks.add(task);
        i++;


        //流程扩展
        extensions = generateExtension(extensionId, workflowId, Lists.newArrayList
                (activityIdPrefix + 1, activityIdPrefix + 2, activityIdPrefix + 3));
        //流程实例
        workflowInstanceMap = geenrateInstance(sourceWorkflowId, workflowInstanceId, workflowId,
                objectId, false, false, false, Lists.newArrayList(activityIdPrefix + 1, activityIdPrefix + 2,
                        activityIdPrefix + 3));

    }


    public void initCompletedTasks() {
        context = initContext();
//        一个已完成任务，处理人1000
        int i = 4;
        Task task = generateTask(workflowInstanceId, taskIdPrefix + i, activityIdPrefix + i,
                sourceWorkflowId, workflowId, objectId, executionType, true, false, false, Lists.newArrayList("1000"));
        tasks.add(task);
        i++;

//        一个已取消任务，处理人1000
        task = generateTask(workflowInstanceId, taskIdPrefix + i, activityIdPrefix + i,
                sourceWorkflowId, workflowId, objectId, executionType, false, true, false, Lists.newArrayList("1000"));
        tasks.add(task);
        i++;

        //流程扩展
        extensions = generateExtension(extensionId, workflowId, Lists.newArrayList(activityIdPrefix + 4,
                activityIdPrefix + 5));
        //流程实例
        workflowInstanceMap = geenrateInstance(sourceWorkflowId, workflowInstanceId, workflowId,
                objectId, false, false, false, Lists.newArrayList(activityIdPrefix + 4, activityIdPrefix + 5));
        List apiNames = Lists.newArrayList("AccountObj");

    }

    @Test
    public void getTaskOutlineFromTasks() throws Exception {

        initUncompletedTasks();
        StopWatch stopWatch = StopWatch.create("Test");
        List<TaskOutline> outlines = TaskUtils.getTaskOutlineFromTasks(tasks, workflowInstanceMap, extensions,
                getTaskOtherDetail(), getTaskEntryObjectNames(context), stopWatch);
    }

    @Test
    public void getCompletedTaskForm() throws Exception {
        initCompletedTasks();
        BPMTask bpmTask = BeanUtils.transfer(tasks.get(0), BPMTask.class);
//        TaskHelper.setCompletedTaskForm(context, bpmTask, descMap.get
//                        ("AccountObj"), generateFieldAuth(), generateTaskData());

    }

    @Test
    public void getUncompletedTaskFrom() throws Exception {
        initUncompletedTasks();
        BPMTask bpmTask = BeanUtils.transfer(tasks.get(0), BPMTask.class);
//        TaskHelper.setUncompletedTaskFrom(context, bpmTask,
//                workflowInstanceMap.get(workflowInstanceId), descMap.get("AccountObj"), generateFieldAuth(),
//                getExpressionData());
    }

    @Test
    public void setFormFieldPermission() throws Exception {

    }

    private TaskUtils.TaskOtherDetail<Task, TaskOutline, WorkflowInstance, FlowExtensionEntity>
    getTaskOtherDetail() {
        return (task, taskOutline, instance, extension) -> {
            List<String> candidateIds = task.getCandidateIds();

            List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) task.getBpmExtension().get
                    (WorkflowKey.ActivityKey.ExtensionKey.form);
            if (!CollectionUtils.isEmpty(forms)) {
                GetUserFieldPermiss.Result fieldsAuth = metaDataAuthProxys.getFieldsPrivilegeOfUser(context, (String)
                        task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.entityId));
            } else {
            }

        };
    }
//
//    private TaskHelper.DataCacheHandler<RemoteContext, Map<String, Map<String, Object>>, WorkflowInstance,
//                BPMExtensionUtils.Expression> getExpressionData() {
//        return (RemoteContext context, Map<String, Map<String, Object>> dataMap,
//                WorkflowInstance instance, BPMExtensionUtils.Expression expression) -> {
//            String instanceVariableKey = expression.getInstanceVariableKey();
//            Map<String, Object> formData = dataMap.get(instanceVariableKey); //先从缓存中查，如果不存在从数据库查，更新缓存
//            Map<String, Object> variables = instance.getVariables();
//            if (formData == null) {
//                String dataId = (String) variables.get(expression.getInstanceVariableKey());
//                if (dataId == null) {
//                    throw new BPMTaskExecuteException("no matched data id for form expression : " + expression
//                            .getInstanceVariableKey() + ".");
//                }
//                try {
//                    formData = metadataService.findDataById(context, expression.getDescApiName(), dataId);
//                } catch (BPMMetadataServiceException e) {
//                    formData = Maps.newHashMap();
//                }
//                dataMap.put(instanceVariableKey, formData);
//
//            }
//            return formData;
//        };
//    }

    private Consumer<Map<Pair<String, String>, List<TaskOutline>>> getTaskEntryObjectNames(RemoteContext context) {
        return entityTaskMaps -> entityTaskMaps.forEach((k, v) -> {
            String name = metadataService.getPaaSObjectName(context, k.getKey(), k.getValue());
            v.forEach(item -> item.setObjectName(name));
        });
    }

    private Task generateTask(String workflowInstanceId, String taskId, String activityId,
                              String sourceWorkflowId, String workflowId, String objectId,
                              String executionType, boolean isCompleted, boolean isCanceled,
                              boolean isError, List<String> candidateIds) {
        Task task = new Task();
        task.setWorkflowInstanceId(workflowInstanceId);
//        task.setActionType(); 完成任务才有taskType
        task.setActivityId(activityId);
        task.setAppId(BPMConstants.APP_ID);
        task.setApplicantId("1000");
        Map<String, Map<String, List<String>>> assignee = Maps.newHashMap();
        Map<String, List<String>> personAssignee = Maps.newHashMap();
        personAssignee.put("person", Lists.newArrayList(candidateIds));
        task.setAssignee(personAssignee);
        task.setCandidateIds(Lists.newArrayList(candidateIds));
        task.setCreateTime(System.currentTimeMillis());
        task.setName("未完成任务");
        task.setDescription("未完成任务描述");
        task.setEntityId("AccountObj");
        task.setId(taskId);

//        task.setObjectId("cfcab67d9d3140e1ab4b5468241801c8");
        task.setObjectId(objectId);
        task.setSourceWorkflowId(sourceWorkflowId);
        task.setState(TaskState.in_progress);
        task.setTenantId("2");
        task.setWorkflowDescription("流程描述");
        task.setWorkflowId(workflowId);
        task.setWorkflowName("流程名");
        task.setTaskType("anyone");
        String executionName = null;
        switch (executionType) {
            case "update":
                executionName = "更新";
                break;
            case "create":
                executionName = "创建";
                break;
            case "operation":
                executionName = "操作对象";
                break;
            case "approve":
                executionName = "审批";
                break;
            default:
                executionName = "其他";
        }

        if (isCompleted) {
            task.setName("已完成任务");
            task.setModifyTime(System.currentTimeMillis());
            task.setCompleted(true);
            task.setAssigneeIds(Lists.newArrayList("1000"));
            task.setActionType("agree");
            Opinion opinion = new Opinion();
            opinion.setTenantId("2");
            opinion.setActionType("auto_agree");
            opinion.setOpinion("非常同意");
            opinion.setReplyTime(System.currentTimeMillis());
            opinion.setUserId("1000");
            task.setOpinions(Lists.newArrayList(opinion));
        }

        if (isCanceled) {
            task.setName("取消任务");
            task.setCanceled(true);
        }

        if (isError) {
            task.setName("异常任务");
            task.setState(TaskState.error);
        }
        task.setBpmExtension(generateBpmFormExtension(objectId, executionType, executionName, activityId,
                generateForms(objectId, activityId)));

        return task;
    }

    Map<String, FlowExtensionEntity> generateExtension(String id, String workflowId, List<String> activityIds) {
        FlowExtensionEntity extensionEntity = new FlowExtensionEntity();
        extensionEntity.setTenantId("2");
        extensionEntity.setId(id);
        extensionEntity.setWorkflowId(workflowId);
        LaneEntity lane1 = new LaneEntity();
        lane1.setId("1");
        lane1.setName("阶段1");
        lane1.setActivities(activityIds);
        List<LaneEntity> lanes = Lists.newArrayList(lane1);
        PoolEntity pool = new PoolEntity();
        pool.setName("");
        pool.setId("1");
        pool.setLanes(lanes);
        List<PoolEntity> pools = Lists.newArrayList(pool);
        extensionEntity.setPools(pools);
        Map extensionMap = Maps.newHashMap();
        extensionMap.put(workflowId, extensionEntity);
        return extensionMap;

    }

    Map<String, WorkflowInstance> geenrateInstance(String sourceWorkflowId, String workflowIntanceId,
                                                   String workflowId, String objectId, boolean isCompleted,
                                                   boolean isCanceled, boolean isError, List<String> activityIds) {
        WorkflowInstance instance = new WorkflowInstance();
        instance.setId(workflowIntanceId);
        instance.setWorkflowId(workflowId);
        instance.setSourceWorkflowId(sourceWorkflowId);
        instance.setApplicantId("1000");
        instance.setEntityId("AccountObj");
        instance.setStart(System.currentTimeMillis());
        instance.setState(InstanceState.in_progress);
        instance.setTenantId("2");
        instance.setStart(System.currentTimeMillis());
        instance.setWorkflowDescription("流程描述");

        instance.setWorkflowName("流程名");
        instance.setObjectId(objectId);
        if (isCanceled) {
            instance.setEnd(System.currentTimeMillis());
        }
        if (isCanceled) {
            instance.setState(InstanceState.cancel);
        }
        if (isError) {
            instance.setState(InstanceState.error);
        }

        Map<String, Object> varible = Maps.newHashMap();
        varible.put("activity_0##AccountObj", objectId);
        instance.setVariables(varible);

        List<ActivityInstance> activityInstances = Lists.newArrayList();
        for (String id : activityIds) {
            ActivityInstance activityInstance = new ActivityInstance();
            activityInstance.setActivityId(id);
            activityInstance.setActivityName("任务1");
            activityInstances.add(activityInstance);
        }
        instance.setActivityInstances(activityInstances);

        Map<String, WorkflowInstance> maps = Maps.newHashMap();
        maps.put(workflowIntanceId, instance);
        return maps;
    }

    Map<String, Object> generateBpmFormExtension(String objectId, String executionType, String executionName, String
            activityId, List<List<Map<String, Object>>> forms) {
        Map<String, Object> bpmExtension = Maps.newHashMap();
        bpmExtension.put(WorkflowKey.ActivityKey.ExtensionKey.entityId, "AccountObj");
        Map<String, String> objectExp = Maps.newHashMap();
        objectExp.put("expression", "activity_0##AccountObj");
        bpmExtension.put(WorkflowKey.ActivityKey.ExtensionKey.objectId, objectExp);
        bpmExtension.put("entityName", "客户");
        bpmExtension.put(WorkflowKey.ActivityKey.ExtensionKey.executionType, executionType);
        bpmExtension.put("executionName", executionName);
        bpmExtension.put("actionCode", "changeowner");
        bpmExtension.put("actionName", "更换负责人");
        bpmExtension.put("form", forms);

        return bpmExtension;
    }

    List<List<Map<String, Object>>> generateForms(String objectId, String activityId) {
        Map<String, Object> form = Maps.newHashMap();
        form.put("name", "account_level");
        Map<String, String> valueExp = Maps.newHashMap();

        valueExp.put("expression", "activity_0##AccountObj##account_level");
        form.put("value", valueExp);
        form.put("default", "1");
        form.put("label", "级别");
        form.put("type", "select_one");
        form.put("required", "false");

        Map<String, Object> form2 = Maps.newHashMap();
        form2.put("name", "tel");
        Map<String, String> valueExp2 = Maps.newHashMap();
        valueExp2.put("expression", "activity_0##AccountObj##tel");
        form2.put("value", "*****************");
        form2.put("label", "电话");
        form2.put("type", "text");
        form2.put("required", "false");
        List<Map<String, Object>> forms = Lists.newArrayList(form, form2);
        List<List<Map<String, Object>>> ret = Lists.newArrayList();
        ret.add(forms);
        return ret;
    }

    private RemoteContext initContext() {
        RemoteContext context = new RemoteContext();
        context.setUserId("1000");
        context.setTenantId("2");
        context.setAppId(BPMConstants.APP_ID);
        return context;
    }

    public GetUserFieldPermiss.Result generateFieldAuth() {
        GetUserFieldPermiss.Result result = new GetUserFieldPermiss.Result();
        Map<String, Integer> fieldAuth = Maps.newHashMap();
        fieldAuth.put("name", 2);
        fieldAuth.put("tel", 2);
        result.setResult(fieldAuth);
        return result;
    }

    private WorkflowTaskDataEntity generateTaskData() {
        WorkflowTaskDataEntity taskDataEntity = new WorkflowTaskDataEntity();
        Map<String, Object> data = Maps.newHashMap();
        data.put("_id", objectId);
        data.put("account_level", "2");
        data.put("tel", "***********");

        taskDataEntity.setData(data);
        taskDataEntity.setId("activity_4");
        taskDataEntity.setActivityId("activity_4");
        taskDataEntity.setTenantId("2");
        return taskDataEntity;

    }
}
