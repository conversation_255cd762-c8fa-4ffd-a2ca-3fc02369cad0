package com.facishare.bpm.utils;

import com.facishare.bpm.BaseTest;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

//https://www.ceshi112.com/XV/Home/index#bi/list
@Slf4j
public class ReadBIUtilTest {


    //1. 阶段顶部显示数据:一个定义,一个阶段,一个节点,进行中的总数
    //2. 节点上显示数据:一个节点,进行中的总数
    //3. 右下方显示正常的任务:状态为进行中的任务的总数
    //4. 右下角超时的任务:bpmtask_istimeout为true

    private String cookie = "JSESSIONID=C7C1A3C04FBB152490EA26FF730B68EC; session=.eJydjl1qwzAQhK8i9By3Wmn1l1P0vQSzXq1iUzcxlgOFkLtX0Bv0aRhmPmaeeqwrtVmaPn8-tTq66G9pja6iT_pjFWqi1vtVLTd13BUx91Ad89LU1jtv-vI6_ZO7nPr4Lm3W52N_SHdL0WcdnLPRmYRspPgaU2GpDgIiFQiGEHIRYM-IqdcqVAtccg7W2GTcJB4iyGSDD5E8umwEoysIUhlyjlOkwDVgMTj5GKFm59kbiyg5Tcn2--Ojyf73ppJLYImGyJAGLJiHbKoMNuRACYwI-k7Qto18_HTgvW3L7UZfsuvXLzghaCQ.Xr19Tw.MeMy1wc05ufpSuN2rbbIuh7No9M";

    @Test
    public void getInstnace() {
        String json = getFile("bbb");
        List<String> lines = Splitter.on(";").splitToList(json);
        Map<String, Set<String>> result = Maps.newHashMap();
        for (String line : lines) {
            if (Strings.isNullOrEmpty(line)) {
                continue;
            }
            List<String> tenantId_instanceId = Splitter.on(",").splitToList(line);
            String tenantId = tenantId_instanceId.get(0);
            String instanceId = tenantId_instanceId.get(1);

            Set<String> instanceIds = result.get(tenantId);
            if (CollectionUtils.isEmpty(instanceIds)) {
                instanceIds = Sets.newHashSet();
            }
            instanceIds.add(instanceId);
            result.put(tenantId, instanceIds);
        }
        Map<String, Set<String>> result2 = Maps.newHashMap();

        List<String> keys = result.keySet().stream().collect(Collectors.toList());
        for (int i = 0; i < keys.size(); i++) {
            String tenantId = keys.get(i);
            Set<String> instanceIds = result.get(tenantId);
            if ( instanceIds.size() >= 1) {
                try {

                    send(tenantId, instanceIds.stream().collect(Collectors.toList()));
                    Thread.sleep(1000);
                    instanceIds.forEach(instanceId -> {
                        String metadata;
                        try {
                            metadata = getMetadata(tenantId, instanceId);
                        } catch (IOException e) {
                            log.info("{},{}", tenantId, JsonUtil.toJson(instanceIds));
                            return;
                        }
                        if (Strings.isNullOrEmpty(metadata)) {
                            log.info("{},{}", tenantId, JsonUtil.toJson(instanceIds));
                            return;
                        }
                        Map<String, Object> data = JacksonUtil.fromJson(metadata, new TypeReference<Map>() {
                        });
                        try {
                            Map<String, Object> resultObj = (Map<String, Object>) data.get("result");
                            if (Objects.isNull(resultObj.get("objectIds"))) {
                                log.info("{},{}", tenantId, JsonUtil.toJson(instanceIds));
                            }
                        }catch (Exception e){
                            log.error("error",e);
                        }
                    });

                    System.out.println(tenantId + ":" + instanceIds.size() + " 条");
                    System.out.print("");

                } catch (IOException | InterruptedException e) {
                    log.error("tenantId:{},e:", tenantId, e);
                }
            }else{
                result2.put(tenantId, instanceIds);
            }
        }

        result2.forEach((tenantId, instanceIds) -> {
            //System.out.println(tenantId + "," + JsonUtil.toJson(instanceIds));
            System.out.println();
        });
    }

    @Test
    public void sendTest() throws IOException {
        send("666719", Lists.newArrayList("5ebab1f650bd2f0001155fac"));
    }

    @Test
    public void getData() throws IOException {
        String metadata = getMetadata("666719", "5ebab1f650bd2f0001155fac");
        if (Strings.isNullOrEmpty(metadata)) {
            return;
        }
        Map<String, Object> data = JacksonUtil.fromJson(metadata, new TypeReference<Map>() {
        });
        Map<String, Object> result = (Map<String, Object>) data.get("result");
        System.out.println(result.get("objectIds"));
    }

    private HttpClient httpClient = new HttpClient();

    public void send(String tenantId, List<String> instanceIds) throws IOException {

        PostMethod postMethod = new PostMethod("http://oss.foneshare.cn/javaconsole/proxy.do?targetURL=http://************:37790/bpm/metadata/" + tenantId + "/transfer/instance/");
        postMethod.addRequestHeader(new Header("Cookie", cookie));
        postMethod.addRequestHeader("Host", "oss.foneshare.cn");
        postMethod.addRequestHeader("Origin", "http://oss.foneshare.cn");
        postMethod.addRequestHeader("Pragma", "no-cache");
        postMethod.addRequestHeader("Referer", "http://oss.foneshare.cn/javaconsole/");
        postMethod.addRequestHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36");
        postMethod.addRequestHeader("Content-Type", "application/json;charset=UTF-8");

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < instanceIds.size(); i++) {
            String k = instanceIds.get(i);
            if (i == instanceIds.size() - 1) {
                sb.append(k);
            } else {
                sb.append(k + ",");
            }
        }

        RequestEntity entity = new StringRequestEntity(sb.toString(), "text/html", "utf-8");
        postMethod.setRequestEntity(entity);

        int i = httpClient.executeMethod(postMethod);
        if (i == 200) {
            String ret = postMethod.getResponseBodyAsString();
        } else {
            System.out.println(i);
        }
    }

    public String getMetadata(String tenantId, String id) throws IOException {
        GetMethod getMethod = new GetMethod("http://oss.foneshare.cn/javaconsole/proxy.do?targetURL=http://************:37790/bpm/thirdPart/paas/data/" + tenantId + "/BpmInstance/" + id);
        getMethod.addRequestHeader(new Header("Cookie", cookie));
        getMethod.addRequestHeader("Host", "oss.foneshare.cn");
        getMethod.addRequestHeader("Origin", "http://oss.foneshare.cn");
        getMethod.addRequestHeader("Pragma", "no-cache");
        getMethod.addRequestHeader("Referer", "http://oss.foneshare.cn/javaconsole/");
        getMethod.addRequestHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36");
        getMethod.addRequestHeader("Content-Type", "application/json;charset=UTF-8");
        int i = httpClient.executeMethod(getMethod);
        if (i == 200) {
            return getMethod.getResponseBodyAsString();
        } else {
            System.out.println(i);
            return null;
        }
    }


    protected String getFile(String processFile) {
        String path = BaseTest.class.getResource("/").getPath() + "/doc/" + processFile;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }
}
