package com.facishare.bpm.utils;

import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created by wangzhx on 2019/8/5.
 */
public class ListSubUtilTest {

    @Test
    public void test() {
        Set<String> strings = ListUtil.combineSubList(Lists.newArrayList(Lists.newArrayList("1", "2"), Lists.newArrayList("1", "6")));
        System.out.println(strings);

    }

    @Test
    public void testGetListGroup() {
        List<String> collect = IntStream.range(1, 100).boxed().map(String::valueOf).collect(Collectors.toList());
        List<List<String>> listGroup1 = ListUtil.getAverageList(collect, 5);
        List<List<String>> listGroup2 = ListUtil.getSubList(collect, 5);
        System.out.println(listGroup1);
        System.out.println(listGroup2);
    }
}
