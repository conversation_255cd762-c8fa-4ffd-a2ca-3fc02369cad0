package com.facishare.bpm.utils;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.google.common.collect.Lists;

import org.junit.Test;

import java.util.List;

public class CancelInstanceUtil {

    private static String tenantId = "573985";
    private static List<String> instanceIds = Lists.newArrayList(
            "5af925f24fa42469f4cbc345",
            "5af9255d4fa42469f4cbc1a5",
            "5af3a195cc9da36368842705",
            "5af0fc9c4fa4248578ef0553",
            "5aefc3794fa4248578ed8fe1",
            "5ae9ffd9cc9da3605493aeaf",
            "5add95444fa42420c263371c",
            "5acb2639cc9da333db31f04c",
            "5acad705cc9da333db3158ab",
            "5aba0b7f4fa424b8d483d4fd",
            "5a52e80acc9da30ca755748c",
            "5a3cb17f4fa4242558dd7f20",
            "5a3a970f4fa4242558dbd288");


    @Test
    public void generateCurl() {
        instanceIds.forEach(instanceId -> {
            System.out.println(getTemplete(instanceId));
        });
    }


    public String getTemplete(String instanceId) {
        return "curl -X POST \\\n" +
                "  http://paas.nsvc.foneshare.cn/fs-paas-workflow/paas/bpm/cancel/type \\\n" +
                "  -H 'cache-control: no-cache' \\\n" +
                "  -H 'content-type: application/json' \\\n" +
                "  -H 'postman-token: 8e7601b3-8226-d0fe-bd0f-02841818459d' \\\n" +
                "  -d '{\"workflowInstanceId\":\"" + instanceId + "\",\"context\":{\"tenantId\":\"" + tenantId + "\",\"appId\":\"BPM\",\"userId\":\""+ BPMConstants.CRM_SYSTEM_USER+"\"}}'";
    }

}
