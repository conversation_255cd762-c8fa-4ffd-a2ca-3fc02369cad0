package com.facishare.bpm.utils;

import com.facishare.bpm.model.resource.app.GetAppActions;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.gson.reflect.TypeToken;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * Created by wangzhx on 2020/2/11.
 */
public class AppUtilTest {

    String json = "[{\"actionCode\":\"dispatch\",\"actionName\":\"外部企业处理\"},{\"actionCode\":\"assignSecond\",\"actionName\":\"指派工单\",\"childMenuDesc\":\"请选择指派工单类型\",\"children\":[{\"actionCode\":\"assign\",\"actionName\":\"可指派任意对象\"},{\"actionCode\":\"assignInner\",\"actionName\":\"仅能指派给内部员工\"},{\"actionCode\":\"assignOuter\",\"actionName\":\"仅能指派给外部企业\"},{\"actionCode\":\"assignGroup\",\"actionName\":\"仅能指派给工单服务组\"}]},{\"actionCode\":\"workRate\",\"actionName\":\"服务评价\"},{\"actionCode\":\"newCheckIn\",\"actionName\":\"新建并执行外勤计划\",\"childMenuDesc\":\"选择外勤类型\",\"children\":[{\"actionCode\":\"5de60cfb30336b1da13d8b44\",\"actionName\":\"退换换单\"},{\"actionCode\":\"5dbf955030336b8ab8eeb5e6\",\"actionName\":\"易用性测试外勤\"},{\"actionCode\":\"5d6fb89330336bb2ad70407c\",\"actionName\":\"沃得外勤-任意\"},{\"actionCode\":\"5cf89b9030336baa3de08f2e\",\"actionName\":\"自定义字段\"},{\"actionCode\":\"5cef847730336bd1c4961b05\",\"actionName\":\"出发签到\"},{\"actionCode\":\"5ced31b430336b177aaf8f42\",\"actionName\":\"沃得外勤维修\"},{\"actionCode\":\"5c7e3b3d8d8fe67b07df7ec8\",\"actionName\":\"231\"},{\"actionCode\":\"5b8f4f0b8d8fe6d793cdf1a1\",\"actionName\":\"外勤1下游专用\"},{\"actionCode\":\"5b8e21fb8d8fe63c6393421f\",\"actionName\":\"访销\"}]}]";
    List<GetAppActions.AppAction> appActionList = JsonUtil.fromJson(json,
            new TypeToken<List<GetAppActions.AppAction>>() {
            }.getType());


    @Test
    public void test() {
//        Assert.assertEquals("assignSecond", AppUtil.getSecondActionCode("assignSecond", null, appActionList, 1));
//        Assert.assertEquals("assignSecond", AppUtil.getSecondActionCode("assign", null, appActionList, 1));
//        Assert.assertEquals("assignSecond", AppUtil.getSecondActionCode("assign", null, appActionList, 1));
//        Assert.assertEquals("newCheckIn", AppUtil.getSecondActionCode("5c7e3b3d8d8fe67b07df7ec8", null, appActionList, 1));

    }

    @Test
    public void copyBean() {
        RemoteContext a = new RemoteContext("fktest088", "590065", "BPM", "1000");
        RemoteContext b = new RemoteContext();
        BeanUtils.copy(a, b);

        b.setUserId("-10000");
        System.out.println(JacksonUtil.toJson("a:" + a));
        System.out.println(JacksonUtil.toJson("b:" + b));
    }
    @Test
    public void copyBean2(){
        RemoteContext a = new RemoteContext("fktest088", "590065", "BPM", "1000");

        RemoteContext cnt = BeanUtils.transfer(a, RemoteContext.class,(src,rst)->{
            rst.setUserId(BPMConstants.CRM_SYSTEM_USER);
        });
        System.out.println(JacksonUtil.toJson("cnt:" + cnt));

    }
}
