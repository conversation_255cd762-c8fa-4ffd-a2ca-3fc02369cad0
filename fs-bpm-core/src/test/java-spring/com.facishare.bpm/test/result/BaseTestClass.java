package com.facishare.bpm.test.result;

/**
 * <AUTHOR>
 * @creat_date: 2020/6/23
 * @creat_time: 19:31
 * @since 7.2.0
 */
public abstract class BaseTestClass<P,R> {
  abstract R apply(P a);

  public static void main(String[] args) {
    BaseTestClass a=null;
    if("1".equals("1")){
      a=new ATestClass();
    }
    ATestClass.AR rst = (ATestClass.AR) a.apply(new ATestClass.A());
    System.out.println(rst.getClass());
  }
}
