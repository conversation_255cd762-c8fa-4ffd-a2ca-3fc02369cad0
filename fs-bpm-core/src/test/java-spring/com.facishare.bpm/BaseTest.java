package com.facishare.bpm;

import com.facishare.bpm.service.BPMBaseService;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Maps;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;


import org.apache.commons.lang.StringUtils;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by Aaron on 28/12/2016.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
@Slf4j
public class BaseTest extends BPMBaseService{
    @BeforeClass
    public static void setEnv() {
        System.setProperty("spring.profiles.active","fstest");
    }


    public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";



    @Test
    public void isNumeric(){
        System.out.println(StringUtils.isNumeric("111"));
        System.out.println(StringUtils.isNumeric("-1"));
        System.out.println(StringUtils.isNumeric("3.33"));
        System.out.println(StringUtils.isNumeric("a"));
        System.out.println(StringUtils.isNumeric("32423sdf"));
    }

    @Autowired
    protected BPMDefinitionService definitionService;
    @Autowired
    protected BPMInstanceService instanceService;
    @Autowired
    protected MetadataService metadataService;
    protected String tenantId = "71557";
    protected String ea = "71557";
    protected String userId = "1011";
    protected String entityId = "object_ogYw2__c";
    protected RemoteContext context = new RemoteContext(ea, tenantId, BPMConstants.APP_ID, userId);

    protected String startNewWorkflow() {
        String outlineId = deploySimpleProcess();
        String dataId = createNewAccount();

        String instanceId = instanceService.startWorkflow(getServiceManager(context), TriggerSource.approval, outlineId, dataId);
        log.debug("=============================instanceId={}", instanceId);
        return instanceId;
    }

    protected String createNewAccount(){
        Map<String, String> account = Maps.newHashMap();
        String name = String.valueOf(System.currentTimeMillis());
        account.put("remark","bpm简单测试数据");
        account.put("tel", name);
        account.put("email","bpm_"+name+"@bpm.cn");
        account.put("tenant_id", tenantId);
        account.put("url","www.bpm.cn");
        account.put("name","bpm_"+name);
        account.put("detail_address","bpm701");
        String id = (String)metadataService.createData(context, entityId, JsonUtil.toJson(account)).get("_id");
        log.debug("================================data id={}", id);
        return id;
    }

    protected String deploySimpleProcess(){
        String outlineId = deployProcess("SimpleTestProcess.json");
        log.debug("==========================outlineId={}", outlineId);
        return outlineId;
    }

    protected String deployProcess(String processFile){
        String processJson = getOutlineJsonFromFile(processFile);
        WorkflowOutline outline = JsonUtil.fromJson(processJson, WorkflowOutline.class);
        return definitionService.deployWorkflow(getServiceManager(context), outline,true,true);
    }


    protected String getOutlineJsonFromFile(String processFile) {
        String path = BaseTest.class.getResource("/").getPath() + "/doc/" + processFile;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }

    void deleteWorkflowOutline(String outlineId){
        definitionService.deleteWorkflowById(getServiceManager(context), outlineId);
    }
}
