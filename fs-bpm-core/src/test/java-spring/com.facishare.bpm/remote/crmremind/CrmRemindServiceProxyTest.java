package com.facishare.bpm.remote.crmremind;

import com.facishare.bpm.BaseTest;
import com.facishare.rest.core.model.RemoteContext;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * Created by wangz on 17-11-27.
 */

/**
 * Created by wangz on 17-1-5.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class CrmRemindServiceProxyTest extends BaseTest{

    @Autowired
    private CrmRemindServiceProxy crmRemindServiceProxy;

    @Test
    public void clearTaskRemind() throws Exception {
        RemoteContext context = new RemoteContext();
        context.setTenantId("54821");
        context.setUserId("2003");
        boolean ret = crmRemindServiceProxy.clearTaskRemind(getServiceManager(context), 100);
        Assert.assertTrue(ret);
    }

}