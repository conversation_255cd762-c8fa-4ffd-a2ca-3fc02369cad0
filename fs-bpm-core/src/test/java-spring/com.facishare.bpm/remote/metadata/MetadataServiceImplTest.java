package com.facishare.bpm.remote.metadata;

import com.facishare.bpm.remote.model.SimpleMetadataAction;
import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.model.Pair;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wangz on 17-1-5.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:applicationContext.xml")
public class MetadataServiceImplTest {


    @Autowired
    MetadataService metadataService;
    RemoteContext context;
    String descApiName = "AccountObj";
    String id = "caed0ffa4e4841fbb366970d843ae371";

    @Before
    public void setup() {
        context = new RemoteContext();
        context.setUserId(BPMConstants.CRM_SYSTEM_USER);
        context.setTenantId("57612");
        context.setEa("2");
    }

    @Test
    public void findCustomObjs() {
        metadataService.findDescsByTenantId(context, null, false,false);
    }

    @Test
    public void findDescribe() throws Exception {
        Map desc = metadataService.findDescribe(context, descApiName, false,false);
        Assert.assertNotNull(desc);
    }

    @Test
    public void createData() throws Exception {
        Map data = new HashMap();
        data.put("name", "instance1");
        data.put("state__c", "in_progress");
        data.put("sourceWorkflowId__c", "1");
        data.put("objectIds__c",Lists.newArrayList("1","2"));
        data.put("entityId__c","AccountObj");
        data.put("objectId__c","1");
        data.put("participantIds__c",Lists.newArrayList("1001","1002"));
        data.put("applicantId__c",Lists.newArrayList("1001"));
        data.put("stageNames__c",Lists.newArrayList("阶段1","阶段2"));
        data.put("workflowId__c","1");
        data.put("workflowId__c","1");
        data.put("taskNames__c",Lists.newArrayList("任务1","任务2"));
        data.put("startTime__c",System.currentTimeMillis());
        data.put("endTime__c",System.currentTimeMillis());
        data.put("_id","1");
        Map result_1 = metadataService.createData(context, "bpmInstanceTest1__c", JsonUtil.toJson(data));

        Assert.assertNotNull(result_1);
//        try{
//            metadataService.createData(context, descApiName, JsonUtil.toJson(data));
//        }catch (Exception e){
//            Assert.assertTrue(e instanceof BPMWorkflowException);
//        }

    }




    @Test
    public void findDescsByTenantId() throws Exception {
        RemoteContext remoteContext = new RemoteContext("","71557","CRM","1000");
        // List<SimpleMetadataDesc> descs = metadataService.findDescsByTenantId(context, "CRM",Lists.newArrayList("AccountObj"), false);
        List<SimpleMetadataDesc> descs = metadataService.findDescsByTenantId(remoteContext, null,false,false);
        for (SimpleMetadataDesc desc : descs) {
            System.out.println(JsonUtil.toPrettyJson(desc));
        }
        Assert.assertTrue(descs.size() > 0);

    }


    @Test
    public void findDescWithReference(){
        Map<String,Object> result = metadataService.findDescWithReference(context, "ContractObj");
    }
    @Test
    public void updateData() throws Exception {
//        TN_0a473cc6cc3c430e97a38bd7c0603445  json
        Map<String,Object> data = Maps.newHashMap();
        data.put("UDInt1__c", 1.2);
//        data.put("field_21262__c", true);
//        data.put("tel",456.0);
//        data.put("account_source",1);
        String id = "06f12e14fad54b508caa6238313c9f9e";
//        descApiName = "object_237__c";
//        Map result_1 = metadataService.updateData(context, descApiName, id, JsonUtil.toJson(data), BPMSwitchConfigHelper.isIncrementalUpdate(), Boolean.FALSE);

//        Assert.assertNotNull(result_1);
    }

    @Test
    public void findDataById() throws Exception {
//        descApiName = "object_237__c";
//        id = "58feb614bab09caaa2376efd";
        Map result_1 = metadataService.findDataById(context,descApiName,id,false,false,false,true,true,false,null).getObject_data();
        Assert.assertNotNull(result_1);
    }




    @Test
    public void getAction() throws IOException {
        HttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost("http://10.113.32.46:8802/crmrest/v1/7/customAction/AccountObj/changeowner");
        Map<String, String> data = Maps.newHashMap();
        data.put("ObjectID", "6e6c7ae19d9f4fc48bc09adbd004fa06");
        data.put("OwnerID", "1016");
        post.setEntity(new StringEntity(JsonUtil.toJson(data)));

        HttpResponse response = client.execute(post);
        String ret = EntityUtils.toString(response.getEntity());
        System.out.println(ret);

    }

    @Test
    public void findDescActions() throws Exception {
        descApiName = "AccountObj";
        List<SimpleMetadataAction> actions = metadataService.findActionsByDesc(context, descApiName, null);
        Assert.assertNotNull(actions);

        descApiName = "LeadsObj";
        actions = metadataService.findActionsByDesc(context, descApiName, null);
        Assert.assertNotNull(actions);


        descApiName = "SalesOrderObj";
        actions = metadataService.findActionsByDesc(context, descApiName, null);
        Assert.assertNotNull(actions);


        descApiName = "CaseObj";
        actions = metadataService.findActionsByDesc(context, descApiName, null);
        Assert.assertNotNull(actions);

    }

    @Test
    public void findReferences() throws Exception {
        context.setTenantId("74164");
        context.setUserId("1000");
        List<SimpleMetadataDesc> ret = metadataService.findReferences(context, "CasesObj");

        Assert.assertNotNull(ret);
        Assert.assertTrue(ret.size() > 0);
    }

    @Test
    public void getPaaSObjectNames() throws Exception {
        List<Pair<String, String>> entityIdObjectId = Lists.newArrayList();
        entityIdObjectId.add(new Pair<>("AccountObj", "8252aedb87de4507956f032488c940d1"));
        entityIdObjectId.add(new Pair<>("AccountObj", "e99f1a2ae51f49dc8315f46e9da063c4"));
        entityIdObjectId.add(new Pair<>("object_gd__c", "58fde342eb8caaba31040db0"));
        Map<String, String> names = metadataService.getPaaSObjectNames(context, entityIdObjectId);
        Assert.assertTrue(names.size() == 3);
    }



    @Test
    public void getDataOwner() {
        List<String> owner = metadataService.getDataOwner(context, "object_237__c", "58fef51fbab09c21cef400b6");

        List<String> owner2 = metadataService.getDataOwner(context, "AccountObj", "eadfbe2fdc1f47efa97a5c5d040e77e9" );
    }


//    @Test
//    public void validateUpgradeByVersion(){
//        BPMSwitchConfigHelper.validateUpgradeByVersion(100653003L,"");
//    }



}
