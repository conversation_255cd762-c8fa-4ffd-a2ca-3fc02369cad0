package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/8/22 2:17 PM
 */
public class ApproveTaskButtonHandlerTest extends BaseTest{

    @Autowired
    private ApproveTaskButtonHandler approveTaskButtonHandler;

    @Test
    public void setButtons(){
        StandardData standardData = new StandardData();
        FormButtonResult formButtonResult = approveTaskButtonHandler.setButtons(getServiceManager(context), standardData, false, TaskParams.create());
        System.out.println(JacksonUtil.toJson(formButtonResult));
    }

    @Test
    public void test(){
        List<ActionButton> buttons = Lists.newArrayList(new ActionButton("agree","同意"),new ActionButton("reject","拒绝"));
        //parallelStream用到第三个参数
        LinkedHashMap<Object, Object> collect = buttons.parallelStream().collect(Maps::newLinkedHashMap, (m, button) -> m.put(button.getAction(), button), (k1, k2) -> {
            System.out.println("k1"+k1);
            System.out.println("k2"+k2);
        });
        System.out.println("collect"+collect);

        Map<String, ActionButton> collect1 = buttons.stream().collect(Collectors.toMap(ActionButton::getAction, k -> k));
        System.out.println("collect1"+collect1);

        //stream()不会用第三个参数
        LinkedHashMap<Object, Object> collect2 = buttons.stream().collect(Maps::newLinkedHashMap, (m, button) -> m.put(button.getAction(), button), (k1, k2) -> {
            System.out.println("collect2-k1"+k1);
            System.out.println("collect2-k2"+k2);
        });
        System.out.println("collect"+collect2);
    }
}
