package com.facishare.bpm.producer.impl;

import com.facishare.bpm.producer.model.BPMEventBean;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/2/10 4:10 PM
 */
@Slf4j
public class OpenApiProducerManagerImplTest {

    static ExecutorService executor = Executors.newFixedThreadPool(500);


    @Test
    public void testOpenApi() {

//
//        Map<String, Object> iworkflowInstanceData = Maps.newHashMap();
//        iworkflowInstanceData.put("ns", "workflowInstances");
//        iworkflowInstanceData.put("op", "i");
//
//        Map<String, Object> uworkflowInstanceData = Maps.newHashMap();
//        uworkflowInstanceData.put("ns", "workflowInstances");
//        uworkflowInstanceData.put("op", "u");

        Map<String, Object> itasks = Maps.newHashMap();
        itasks.put("ns", "tasks");
        itasks.put("op", "i");

        Map<String, Object> utasks = Maps.newHashMap();
        utasks.put("ns", "tasks");
        utasks.put("op", "u");


        List<Map<String, Object>> datas = Lists.newArrayList();

        for (int i = 0; i < 10; i++) {
//            datas.add(iworkflowInstanceData);
//            datas.add(uworkflowInstanceData);
            datas.add(itasks);
            datas.add(utasks);
        }


        for (Map<String, Object> d : datas) {
            executor.submit(() -> {
                sendInstanceOrTaskChange(d);
            });
        }

    }


    public void sendInstanceOrTaskChange(Map<String, Object> data) {
        String ns = (String) data.get("ns");
        String op = (String) data.get("op");
        try {
            BPMEventBean eventData = TaskOrInstanceChangeDataBuilder.valueOf(ns)
                    .setIsInstert(op.equals("i"))
                    .getEventData(data);
        } catch (Throwable e) {
            log.error("sendInstanceOrTaskChange:data:{}", JsonUtil.toJson(data), e);
        }
    }


    public enum TaskOrInstanceChangeDataBuilder implements EventDataInterface {
        workflowInstances {
            @Override
            public BPMEventBean getEventData(Map<String, Object> data) {
                log.info("workflowInstances this.isInsert {} ", this.isInsert);
                return null;
            }
        }, tasks {
            @Override
            public BPMEventBean getEventData(Map<String, Object> data) {
                log.info("task this.isInsert {} ", this.isInsert);
                return null;
            }
        };


        protected boolean isInsert;

        public TaskOrInstanceChangeDataBuilder setIsInstert(boolean insert) {
            this.isInsert = insert;
            return this;
        }

    }

    interface EventDataInterface {
        BPMEventBean getEventData(Map<String, Object> data);
    }


}