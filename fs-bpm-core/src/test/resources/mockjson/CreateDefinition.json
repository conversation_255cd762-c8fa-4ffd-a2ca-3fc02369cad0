{"appId": "BPM", "enabled": true, "draftId": true, "workflow": {"activities": [{"id": "1731047930837", "type": "startEvent", "name": "开始", "description": ""}, {"id": "17310479308371", "type": "userTask", "name": "业务活动", "canSkip": false, "taskType": "anyone", "assignee": {"person": ["1054"]}, "bpmExtension": {"executionType": "update", "executionName": "编辑对象", "entityId": "object_7prLH__c", "entityName": "油焖大虾", "objectId": {"expression": "activity_0##object_7prLH__c"}, "objectName": "流程发起数据/油焖大虾", "layoutType": "none", "defaultButtons": {}, "outerTenantType": "outerTenantField"}, "linkAppEnable": false, "assigneeType": "assignee", "assignNextTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "1731047970976"}], "variables": [{"id": "activity_0##object_7prLH__c", "type": {"name": "text"}}, {"id": "activity_17310479308371##object_7prLH__c", "type": {"name": "text"}}], "transitions": [{"id": "1731047930837111", "fromId": "1731047930837", "toId": "17310479308371"}, {"fromId": "17310479308371", "toId": "1731047970976", "id": "1731047972564"}]}, "extension": {"diagram": [{"id": "173104793083711", "attr": {"x": 80, "y": 60, "width": 220, "height": 540}}, {"id": "1731047930837111", "attr": {"d": "M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5", "fromPosition": "bottom", "toPosition": "top"}}, {"id": "1731047930837", "attr": {"x": 160, "y": 125}}, {"id": "1731047972564", "attr": {"d": "M 180 316 L 180 382 C 180 387.33 181.67 390 185 390 L 185 390 C 188.33 390 190 392.67 190 398 L 190 408.5", "fromPosition": "bottom", "toPosition": "top"}}, {"id": "1731047970976", "attr": {"x": 160, "y": 410}}, {"id": "17310479308371", "attr": {"x": 110, "y": 260}}], "pools": [{"lanes": [{"activities": ["1731047930837", "17310479308371", "1731047970976"], "id": "173104793083711", "name": "阶段", "description": "", "order": 1}]}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100%\" height=\"100%\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" class=\"x6-graph-svg\" viewBox=\"80.00000762939453 60 219.99999237060547 540\"><defs style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><marker refX=\"-1\" id=\"marker-v0-2980955686\" overflow=\"visible\" orient=\"auto\" markerUnits=\"userSpaceOnUse\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><path stroke=\"#545861\" fill=\"#545861\" transform=\"rotate(180)\" d=\"M 0 0 L 12 -4 L 12 4 Z\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/></marker></defs><g class=\"x6-graph-svg-viewport\" transform=\"matrix(0.7,0,0,0.7,290.3117647058824,119)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><g class=\"x6-graph-svg-primer\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><g class=\"x6-graph-svg-stage\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><!--z-index:1--><g data-cell-id=\"173104793083711\" data-shape=\"lane\" class=\"x6-cell x6-node\" transform=\"translate(80,60)\" id=\"v1\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><rect fill=\"#E6F4FF\" stroke=\"#333333\" stroke-width=\"0\" height=\"16\" width=\"220\" transform=\"matrix(1,0,0,1,0,0)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><rect fill=\"#dee1e8\" stroke=\"#333333\" stroke-width=\"0\" x=\"1\" y=\"1\" stroke-opacity=\"0\" width=\"32\" height=\"16\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><text font-size=\"14\" xml:space=\"preserve\" fill=\"#000000\" text-anchor=\"start\" font-family=\"Arial, helvetica, sans-serif\" width=\"270\" transform=\"matrix(1,0,0,1,35,10)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\"><title style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">阶段</title><tspan dy=\"0.3em\" class=\"v-line\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">阶段</tspan></text><text font-size=\"14\" xml:space=\"preserve\" fill=\"#181c25\" text-anchor=\"middle\" font-family=\"Arial, helvetica, sans-serif\" transform=\"matrix(1,0,0,1,15,10)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\"><title style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">阶段</title><tspan dy=\"0.3em\" class=\"v-line\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">1</tspan></text><rect fill=\"none\" stroke=\"#368DFF\" stroke-width=\"1\" fill-opacity=\"0\" width=\"220\" height=\"540\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><image style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/></g><g data-cell-id=\"1731047930837111\" data-shape=\"edge\" class=\"x6-cell x6-edge\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"10\" d=\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><path fill=\"none\" pointer-events=\"none\" stroke-linejoin=\"round\" stroke=\"#545861\" stroke-width=\"1.5\" d=\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\" marker-end=\"url(#marker-v0-2980955686)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><g class=\"x6-edge-labels\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><g class=\"x6-edge-label\" data-index=\"0\" cursor=\"default\" transform=\"matrix(1,0,0,1,189.78128051757812,234.9930419921875)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><rect fill=\"#fff\" rx=\"3\" ry=\"3\" width=\"0\" height=\"0\" transform=\"matrix(1,0,0,1,0,0)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><text fill=\"#000\" font-size=\"14\" text-anchor=\"middle\" text-vertical-anchor=\"middle\" pointer-events=\"none\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/></g></g></g><!--z-index:2--><!--z-index:3--><g data-cell-id=\"1731047930837\" data-shape=\"startEvent\" class=\"x6-cell x6-node\" transform=\"translate(160,125)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle fill=\"#16B4AB\" stroke=\"#368DFF\" stroke-width=\"1\" cx=\"30\" cy=\"30\" r=\"30\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><text font-size=\"14\" xml:space=\"preserve\" fill=\"#ffffff\" text-anchor=\"middle\" font-family=\"Arial, helvetica, sans-serif\" transform=\"matrix(1,0,0,1,30,30)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\"><title style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">开始</title><tspan dy=\"0.3em\" class=\"v-line\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">开始</tspan></text><g class=\"x6-port x6-port-top\" transform=\"matrix(1,0,0,1,30,0)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"top\" port-group=\"top\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-right\" transform=\"matrix(1,0,0,1,60,30)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"right\" port-group=\"right\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-bottom\" transform=\"matrix(1,0,0,1,30,60)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"bottom\" port-group=\"bottom\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-left\" transform=\"matrix(1,0,0,1,0,30)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"left\" port-group=\"left\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g></g><!--z-index:4--><!--z-index:5--><!--z-index:6--><g data-cell-id=\"1731047972564\" data-shape=\"edge\" class=\"x6-cell x6-edge\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><path fill=\"none\" cursor=\"pointer\" stroke=\"transparent\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"10\" d=\"M 180 316 L 180 382 C 180 387.33 181.67 390 185 390 L 185 390 C 188.33 390 190 392.67 190 398 L 190 408.5\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><path fill=\"none\" pointer-events=\"none\" stroke-linejoin=\"round\" stroke=\"#545861\" stroke-width=\"1.5\" d=\"M 180 316 L 180 382 C 180 387.33 181.67 390 185 390 L 185 390 C 188.33 390 190 392.67 190 398 L 190 408.5\" marker-end=\"url(#marker-v0-2980955686)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/></g><!--z-index:7--><g data-cell-id=\"1731047970976\" data-shape=\"endEvent\" class=\"x6-cell x6-node\" transform=\"translate(160,410)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle fill=\"#ffffff\" stroke=\"#737C8C\" stroke-width=\"4\" org-stroke=\"#737C8C\" fill-opacity=\"0\" cx=\"30\" cy=\"30\" r=\"30\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><circle fill=\"#737C8C\" stroke=\"#333333\" stroke-width=\"0\" cx=\"30\" cy=\"30\" r=\"25\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><text font-size=\"14\" xml:space=\"preserve\" fill=\"#ffffff\" text-anchor=\"middle\" font-family=\"Arial, helvetica, sans-serif\" transform=\"matrix(1,0,0,1,30,30)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\"><title style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">结束</title><tspan dy=\"0.3em\" class=\"v-line\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">结束</tspan></text><g class=\"x6-port x6-port-top\" transform=\"matrix(1,0,0,1,30,0)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"top\" port-group=\"top\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\" id=\"v2\"/></g><g class=\"x6-port x6-port-right\" transform=\"matrix(1,0,0,1,60,30)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"right\" port-group=\"right\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-bottom\" transform=\"matrix(1,0,0,1,30,60)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"bottom\" port-group=\"bottom\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-left\" transform=\"matrix(1,0,0,1,0,30)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"left\" port-group=\"left\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g></g><!--z-index:8--><g data-cell-id=\"17310479308371\" data-shape=\"business\" class=\"x6-cell x6-node\" transform=\"translate(110,260)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><rect fill=\"#E6F4FF\" stroke=\"#368DFF\" stroke-width=\"1\" rx=\"2\" width=\"140\" height=\"56\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><rect fill=\"#E6F4FF\" stroke=\"#5498ff\" stroke-width=\"1\" rx=\"2\" class=\"x6-hightlight-body\" width=\"140\" height=\"56\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><text font-size=\"14\" xml:space=\"preserve\" fill=\"#000000\" text-anchor=\"left\" font-family=\"Arial, helvetica, sans-serif\" text=\"业务活动\" transform=\"matrix(1,0,0,1,37,28)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\"><title style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">业务活动</title><tspan dy=\"0.3em\" class=\"v-line\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\">业务活动</tspan></text><image x=\"10\" y=\"15\" width=\"24\" height=\"24\" xlink:href=\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzI2ODI0NzAyNzQwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ3MjA3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik04OTYgMTI4YzIzLjU1MiAwIDQyLjY4OCAxOS4wNzIgNDIuNjg4IDQyLjY4OHY2ODIuNjI0QTQyLjY4OCA0Mi42ODggMCAwIDEgODk2IDg5NkgxMjhhNDIuNjg4IDQyLjY4OCAwIDAgMS00Mi42ODgtNDIuNjg4VjE3MC42ODhDODUuMzEyIDE0Ny4wNzIgMTA0LjQ0OCAxMjggMTI4IDEyOGg3Njh6IG0tNDIuNjg4IDg1LjMxMkgxNzAuNjg4djU5Ny4zNzZoNjgyLjYyNFYyMTMuMzEyek00MzIgMzIwYzYwLjk5MiAwIDkwLjg4IDQxLjY2NCA5MC44OCAxMDcuMjY0IDAgMzAuMDgtMTYuODk2IDU4LjI0LTM1LjEzNiA3OC4zMzYtNi4yMDggMTEuMi05LjkyIDIyLjQtOS45MiAzMi41MTIgMCA4LjcwNCA5Ljg1NiAxOC42ODggMjMuOTM2IDIxLjQ0bDEzLjE4NCAyLjQ5NiAyLjYyNCAwLjY0YzM4LjQgOS42IDgwLjQ0OCAyOC42MDggODguODk2IDU5LjUybDEuMzQ0IDcuMzYgMC4xOTIgNC4wOTZ2MTkuNTg0YTI4LjAzMiAyOC4wMzIgMCAwIDEtMjAuMjg4IDI3LjI2NGwtOC40NDggMi4zMDQtMjAuMjI0IDUuMDU2QzUyNS40NCA2OTUuNjggNDc4LjcyIDcwNCA0MzIgNzA0Yy00MS42IDAtODMuMDcyLTYuNTkyLTExNS4zOTItMTMuNTA0bC0yMi4zMzYtNS4xODRjLTYuNzItMS43MjgtMTIuOC0zLjMyOC0xNy45Mi00LjhhMjcuODQgMjcuODQgMCAwIDEtMTkuODQtMjEuOTUyTDI1NiA2NTMuMjQ4VjYzMy42YzAtMzQuODE2IDQxLjQwOC01Ny4wODggODEuNzkyLTY4LjczNmw4LjY0LTIuMzA0IDE1LjgwOC0zLjA3MmMxMy45NTItMi43NTIgMjMuOTM2LTExLjk2OCAyMy45MzYtMjEuNDRhNjkuMTIgNjkuMTIgMCAwIDAtOS45Mi0zMi40NDhjLTE4LjI0LTIwLjIyNC0zNS4yLTQ4LjM4NC0zNS4yLTc4LjRDMzQxLjEyIDM2MS42NjQgMzcxLjA3MiAzMjAgNDMyIDMyMHoiIGZpbGw9IiM3MzdDOEMiIHAtaWQ9IjQ3MjA4Ij48L3BhdGg+PC9zdmc+\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><g class=\"x6-port x6-port-top\" transform=\"matrix(1,0,0,1,70,0)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"top\" port-group=\"top\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-right\" transform=\"matrix(1,0,0,1,140,28)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"right\" port-group=\"right\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-bottom\" transform=\"matrix(1,0,0,1,70,56)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"bottom\" port-group=\"bottom\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g><g class=\"x6-port x6-port-left\" transform=\"matrix(1,0,0,1,0,28)\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"><circle r=\"6\" fill=\"#fff\" stroke=\"#5498ff\" port=\"left\" port-group=\"left\" class=\"x6-port-body\" magnet=\"true\" stroke-width=\"1\" style=\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\"/></g></g><!--z-index:9--></g><g class=\"x6-graph-svg-decorator\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/><g class=\"x6-graph-svg-overlay\" style=\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\"/></g></svg>"}, "name": "测试一下12356", "entryType": "object_7prLH__c", "entryTypeName": "油焖大虾", "externalFlow": 0, "rangeEmployeeIds": ["1054"], "linkAppEnable": false, "singleInstanceFlow": 0}