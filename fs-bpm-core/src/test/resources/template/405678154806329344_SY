{"name": "外勤上门安装服务流程（示例）", "enabled": true, "description": "", "entryType": "CasesObj", "entryTypeName": "工单", "rangeEmployeeIds": [], "rangeCircleIds": [], "rangeGroupIds": [], "rule": {}, "rangeRoleIds": ["00000000000000000000000000000010"], "workflow": {"activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1537251786119"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_0##CasesObj"}, "entityId": "CasesObj", "entityName": "工单"}, "name": "客服受理", "description": "", "id": "1537251786129", "assignee": {"role": ["00000000000000000000000000000010"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0.0}, {"type": "endEvent", "name": "结束", "description": "", "id": "1537251786137"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "externalApplyTask", "executionName": "", "entityName": "工单", "entityId": "CasesObj", "objectId": {"expression": "activity_0##CasesObj"}, "externalApply": {"appCode": "0", "appName": "服务通", "actionCode": "assign", "actionName": "工单指派"}}, "name": "工单指派", "description": "", "id": "1539596209579", "assignee": {}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 1.0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "工单", "entityId": "CasesObj", "executionType": "update", "objectId": {"expression": "activity_0##CasesObj"}, "form": [[{"name": "field_gcjl__c", "label": "服务过程记录", "type": "long_text", "required": false}, {"name": "field_xcpz__c", "label": "服务现场拍照", "type": "image", "required": false}]]}, "name": "工单处理", "description": "", "id": "1543816894383", "assignee": {"role": ["00000000000000000000000000000010"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0.0}], "transitions": [{"id": "1537251786130", "fromId": "1537251786119", "toId": "1537251786129", "serialNumber": 0.0}, {"id": "1539596209580", "fromId": "1537251786129", "toId": "1539596209579", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_1537251786129##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}, "serialNumber": 1.0}, {"id": "1539596209589", "fromId": "1537251786129", "toId": "1537251786137", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_1537251786129##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}, "serialNumber": 2.0}, {"id": "1543816894386", "fromId": "1539596209579", "toId": "1543816894383", "serialNumber": 3.0}, {"id": "1543816894387", "fromId": "1543816894383", "toId": "1537251786137", "serialNumber": 4.0}], "variables": [{"id": "activity_0##CasesObj", "type": {"name": "text"}}, {"id": "activity_1537251786129##result", "type": {"name": "text"}}, {"id": "activity_1537251786129##CasesObj", "type": {"name": "text"}}, {"id": "activity_1539596209579##CasesObj", "type": {"name": "text"}}, {"id": "activity_1543816894383##CasesObj", "type": {"name": "text"}}], "id": "5c04c834cc9da3ea8994e3cd", "sourceWorkflowId": "405678154806329344", "createTime": 1540971911605.0}, "extension": {"pools": [{"lanes": [{"id": "1537251786121", "name": "客服受理", "description": "", "activities": ["1537251786119", "1537251786129"]}]}, {"lanes": [{"id": "1537251786125", "name": "工单指派", "description": "", "activities": ["1539596209579"]}]}, {"lanes": [{"id": "1537251786127", "name": "上门服务", "description": "", "activities": ["1543816894383"]}]}, {"lanes": [{"id": "1539596209586", "name": "工单结束", "description": "", "activities": ["1537251786137"]}]}], "diagram": [{"id": "1539596209586", "attr": {"width": 220.0, "height": 540.0, "x": 760.0, "y": 60.0}}, {"id": "1537251786127", "attr": {"width": 220.0, "height": 540.0, "x": 520.0, "y": 60.0}}, {"id": "1537251786125", "attr": {"width": 220.0, "height": 540.0, "x": 280.0, "y": 60.0}}, {"id": "1537251786121", "attr": {"width": 220.0, "height": 540.0, "x": 40.0, "y": 60.0}}, {"id": "1537251786119", "attr": {"width": 60.0, "height": 60.0, "x": 120.0, "y": 125.0}}, {"id": "1537251786129", "attr": {"width": 90.0, "height": 90.0, "x": 150.0, "y": 240.0}}, {"id": "1537251786137", "attr": {"width": 60.0, "height": 60.0, "x": 850.0, "y": 500.0}}, {"id": "1539596209579", "attr": {"width": 160.0, "height": 50.0, "x": 310.0, "y": 260.0}}, {"id": "1543816894383", "attr": {"width": 160.0, "height": 50.0, "x": 550.0, "y": 270.0}}, {"id": "1543816894387", "attr": {"d": "M710,295 h65 a20,20 0 0 1 20,20 v166 a20,20 0 0 0 20,20 h65", "toPosition": "top", "fromPosition": "right", "type": "polyline"}}, {"id": "1543816894386", "attr": {"d": "M470,285 h36 a5,5 0 0 1 5,5 v0 a5,5 0 0 0 5,5 h36", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1539596209589", "attr": {"d": "M150,330 v180 a20,20 0 0 0 20,20 h681", "toPosition": "left", "fromPosition": "bottom", "type": "polyline", "markerStart": "url(#approval-no_1543817200686)"}}, {"id": "1539596209580", "attr": {"d": "M195,285 h58 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h58", "toPosition": "left", "fromPosition": "right", "type": "polyline", "markerStart": "url(#approval-yes_1543817200686)"}}, {"id": "1537251786130", "attr": {"d": "M150,185 v28 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v28", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=650 width=1030 tabindex=\"0\"><defs><marker id=\"end-arrow_1543817200686\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1543817200686\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1543817200686\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1543817200686\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n        background-color: #f3f3f5;\n      }\n\n      g[type=pool] {\n        font-size: 13px;\n      }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g data-id=\"1539596209586\" shape=\"rectangle\" type=\"pool\" x=\"760\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(760,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"220\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">工单结束</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">4</text></g><g data-id=\"1537251786127\" shape=\"rectangle\" type=\"pool\" x=\"520\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(520,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"220\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">上门服务</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">3</text></g><g data-id=\"1537251786125\" shape=\"rectangle\" type=\"pool\" x=\"280\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(280,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"220\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">工单指派</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">2</text></g><g data-id=\"1537251786121\" shape=\"rectangle\" type=\"pool\" x=\"40\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(40,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"220\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">客服受理</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">1</text></g><g name=\"line-wrapper\"><g tabindex=\"0\" data-id=\"1543816894387\"><path d=\"M710,295 h65 a20,20 0 0 1 20,20 v166 a20,20 0 0 0 20,20 h65\" start-id=\"1543816894383\" end-id=\"1537251786137\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1543817200686)\"></path></g><g tabindex=\"0\" data-id=\"1543816894386\"><path d=\"M470,285 h36 a5,5 0 0 1 5,5 v0 a5,5 0 0 0 5,5 h36\" start-id=\"1539596209579\" end-id=\"1543816894383\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1543817200686)\"></path></g><g tabindex=\"0\" data-id=\"1539596209589\"><path d=\"M150,330 v180 a20,20 0 0 0 20,20 h681\" start-id=\"1537251786129\" end-id=\"1537251786137\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-start=\"url(#approval-no_1543817200686)\" marker-end=\"url(#end-arrow_1543817200686)\"></path></g><g tabindex=\"0\" data-id=\"1539596209580\"><path d=\"M195,285 h58 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h58\" start-id=\"1537251786129\" end-id=\"1539596209579\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-start=\"url(#approval-yes_1543817200686)\" marker-end=\"url(#end-arrow_1543817200686)\"></path></g><g tabindex=\"0\" data-id=\"1537251786130\"><path d=\"M150,185 v28 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v28\" start-id=\"1537251786119\" end-id=\"1537251786129\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1543817200686)\"></path></g></g><g data-id=\"1537251786119\" shape=\"rectangle\" type=\"startEvent\" x=\"120\" y=\"125\" width=\"60\" height=\"60\" transform=\"translate(120,125)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"60\" width=\"60\" fill=\"#70757f\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">开始</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1537251786129\" type=\"userTask\" shape=\"diamond\" transform=\"translate(150,240)\" x=\"150\" y=\"240\" width=\"90\" height=\"90\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"63.63961030678927\" height=\"63.63961030678927\" rx=\"3\" ry=\"3\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" transform=\"translate(-45,0)\"><rect width=\"90\" height=\"90\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"40.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"36.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"40.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"36.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"85.5\" y=\"40.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"81.5\" y=\"36.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"40.5\" y=\"85.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"36.5\" y=\"81.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"90\" width=\"90\" x=\"-45\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">客服受理</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(-45, 100)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1537251786137\" shape=\"rectangle\" type=\"endEvent\" x=\"850\" y=\"500\" width=\"60\" height=\"60\" transform=\"translate(850,500)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"60\" width=\"60\" fill=\"#70757f\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">结束</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1539596209579\" shape=\"rectangle\" type=\"userTask\" x=\"310\" y=\"260\" width=\"160\" height=\"50\" transform=\"translate(310,260)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"160\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">工单指派</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1543816894383\" shape=\"rectangle\" type=\"userTask\" x=\"550\" y=\"270\" width=\"160\" height=\"50\" transform=\"translate(550,270)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" class=\"bpm-draw-polyline-able bpm-shape-focus-node node-focused\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"160\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">工单处理</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "externalFlow": 0, "singleInstanceFlow": 0, "isDeleted": false}