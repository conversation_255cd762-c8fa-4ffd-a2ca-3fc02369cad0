<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{12} %msg%n</pattern>
        </encoder>
    </appender>


    <logger name="com.facishare" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
    </logger>
</configuration>
