{"id": "BI_5be5065c688629c8cd6a7b92", "isPreview": 0, "pageNumber": 1, "showMode": 0, "isView": 1, "globalFilter": null, "pageSize": 2000, "displayFields": [{"fieldID": "Bi_pre_352fa95391992844345d6f6c01ab2240", "dbObjName": "BpmInstance", "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "fieldLocation": 7, "fieldType": "String", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objName": "业务流程实例", "isKey": 0, "isShow": 1, "isIndex": 1, "isCalc": 0, "objectName": "BpmInstance", "aggrType": "0", "columnName": "bpminstance_sourceworkflowid", "fixed": 0, "groupSeq": 1, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 0, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "text", "operateMenu": ["order", "filter", "group"]}, {"fieldID": "Bi_pre_0f3150f7ce4573d55d69289413d4cdd6", "dbObjName": "BpmInstance", "fieldName": "流程状态", "dbFieldName": "state", "fieldLocation": 13, "fieldType": "String", "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objName": "业务流程实例", "isKey": 0, "isShow": 1, "isIndex": 1, "enumName": "Bi_pre_0f3150f7ce4573d55d69289413d4cdd6", "isCalc": 0, "objectName": "BpmInstance", "aggrType": "0", "columnName": "bpminstance_state", "fixed": 0, "groupSeq": 2, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 1, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_MultiSelect", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "select_one", "operateMenu": ["filter", "group"]}, {"fieldID": "Bi_pre_5414ef2374d774b4b6432ba0cefd4270", "dbObjName": "BpmInstance", "fieldName": "流程发起时间", "dbFieldName": "startTime", "fieldLocation": 11, "fieldType": "Date", "subFieldType": "DateTime", "objId": "Bi_pre_2b77a95c3fb2d2a70f151b4030dde7a8", "objName": "业务流程实例", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "yyyy-MM-dd HH:mm", "isCalc": 0, "objectName": "BpmInstance", "aggrType": "1", "columnName": "bpminstance_starttime", "fixed": 0, "groupSeq": 0, "isGroup": 0, "isVisible": 1, "orderType": 0, "seq": 2, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_Time", "group": "DateTime", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "datetime", "operateMenu": ["order", "filter", "group", "aggr"]}], "refresh": 1}