{"ruleId": "241033141530820609", "tenantId": "2", "userId": "1000", "sourceWorkflowId": "241033141463711744", "workflowId": "58eca5813db71d05aca0d2dc", "name": "5.6 联系人及关联对象", "count": 0, "enabled": true, "description": "5.6 联系人及关联对象", "entryType": "ContactObj", "entryTypeName": "联系人", "rangeEmployeeIds": [1416], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": [], "createdBy": "1000", "createTime": 1491903873864, "lastModifiedBy": "1000", "lastModifiedTime": 1491903873864, "workflow": {"type": "workflow_bpm", "name": "5.6 联系人及关联对象", "description": "5.6 联系人及关联对象", "activities": [{"type": "startEvent", "name": "开始", "description": "", "ruleId": "1491903743374"}, {"type": "userTask", "bpmExtension": {"actionCode": "0", "executionType": "update", "executionName": "编辑对象", "entityId": "ContactObj", "entityName": "联系人", "objectId": {"expression": "activity_0##ContactObj"}}, "remindLatency": 2, "canSkip": false, "remind": true, "name": "编辑联系人", "description": "编辑联系人", "ruleId": "1491903743375", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "bpmExtension": {"actionCode": "0", "executionType": "addRelatedObject", "executionName": "新建关联对象", "objectId": {"expression": "activity_0##ContactObj"}, "entityName": "联系人", "entityId": "ContactObj", "relatedEntityName": "销售订单", "relatedObjectId": {"expression": "activity_1491903743379##SalesOrderObj"}, "relatedEntityId": "SalesOrderObj"}, "remindLatency": 3, "remind": true, "name": "新建关联对象", "description": "新建关联对象", "ruleId": "1491903743379", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "endEvent", "name": "结束", "description": "", "ruleId": "1491903743381"}], "transitions": [{"ruleId": "1491903743377", "fromId": "1491903743374", "toId": "1491903743375"}, {"ruleId": "1491903743380", "fromId": "1491903743375", "toId": "1491903743379"}, {"ruleId": "1491903743382", "fromId": "1491903743379", "toId": "1491903743381"}], "variables": [{"ruleId": "activity_0##ContactObj", "type": {"name": "text"}}, {"ruleId": "activity_1491903743375##ContactObj", "type": {"name": "text"}}, {"ruleId": "activity_1491903743379##ContactObj", "type": {"name": "text"}}, {"ruleId": "activity_1491903743379##SalesOrderObj", "type": {"name": "text"}}], "ruleId": "58eca5813db71d05aca0d2dc", "sourceWorkflowId": "241033141463711744"}, "extension": {"ruleId": "241033141530820608", "pools": [{"lanes": [{"ruleId": "1491903743376", "name": "阶段", "description": "", "activities": ["1491903743374", "1491903743375", "1491903743379", "1491903743381"]}]}], "diagram": [{"ruleId": "1491903743376", "attr": {"width": 220, "height": 540, "x": 40, "y": 40}}, {"ruleId": "1491903743374", "attr": {"width": 60, "height": 60, "x": 120, "y": 105}}, {"ruleId": "1491903743375", "attr": {"width": 160, "height": 50, "x": 70, "y": 240}}, {"ruleId": "1491903743379", "attr": {"width": 160, "height": 50, "x": 70, "y": 360}}, {"ruleId": "1491903743381", "attr": {"width": 60, "height": 60, "x": 120, "y": 480}}, {"ruleId": "1491903743382", "attr": {"d": "M150 410 L151 481", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}, {"ruleId": "1491903743380", "attr": {"d": "M150 290 L151 361", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}, {"ruleId": "1491903743377", "attr": {"d": "M151 165 L151 241", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=630 width=310 tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><defs><marker ruleId=\"end-arrow\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker ruleId=\"end-arrow-colored\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker ruleId=\"approval-yes\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker ruleId=\"approval-no\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter ruleId=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n        background-color: #f3f3f5;\n      }\n\n      g[type=pool] {\n        font-size: 13px;\n      }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0)\"><g ruleId=\"1491903743376\" shape=\"rectangle\" type=\"pool\" x=\"40\" y=\"40\" width=\"220\" height=\"540\" transform=\"translate(40,40)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"215.5\" y=\"265.5\" fill=\"white\" rx=\"0\" ry=\"0\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"105.5\" y=\"535.5\" fill=\"white\" rx=\"0\" ry=\"0\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text text-anchor=\"middle\" name=\"name\" y=\"30\" x=\"110\">阶段</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 545)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">1</text></g><g name=\"line-wrapper\"><g fill=\"transparent\" tabindex=\"0\" ruleId=\"1491903743382\"><path type=\"line\" d=\"M150 410 L151 481\" from-position=\"bottom\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\" to-position=\"top\" start-ruleId=\"1491903743379\" end-ruleId=\"1491903743381\"></path></g><g fill=\"transparent\" tabindex=\"0\" ruleId=\"1491903743380\"><path type=\"line\" d=\"M150 290 L151 361\" from-position=\"bottom\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\" to-position=\"top\" start-ruleId=\"1491903743375\" end-ruleId=\"1491903743379\"></path></g><g tabindex=\"0\" ruleId=\"1491903743377\"><path d=\"M151 165 L151 241\" start-ruleId=\"1491903743374\" end-ruleId=\"1491903743375\" fill=\"transparent\" stroke-width=\"1\" type=\"line\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g></g><g ruleId=\"1491903743374\" shape=\"rectangle\" type=\"startEvent\" x=\"120\" y=\"105\" width=\"60\" height=\"60\" transform=\"translate(120,105)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" class=\"\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"55.5\" y=\"25.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"25.5\" y=\"55.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" name=\"name\" y=\"35\" x=\"30\" fill=\"#70757f\">开始</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 65)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g ruleId=\"1491903743375\" shape=\"rectangle\" type=\"userTask\" x=\"70\" y=\"240\" width=\"160\" height=\"50\" transform=\"translate(70,240)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" status=\"normal\" class=\"\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"155.5\" y=\"20.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"75.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" name=\"name\" y=\"30\" x=\"80\">编辑联系人</text><g name=\"error-node\" class=\"hide\" transform=\"translate(80, 55)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点有且只能有一根连出的线</tspan></text></g></g><g ruleId=\"1491903743379\" shape=\"rectangle\" type=\"userTask\" x=\"70\" y=\"360\" width=\"160\" height=\"50\" transform=\"translate(70,360)\" tabindex=\"0\" status=\"normal\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" class=\"\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"155.5\" y=\"20.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"75.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" name=\"name\" y=\"30\" x=\"80\">新建关联对象</text><g name=\"error-node\" class=\"hide\" transform=\"translate(80, 55)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点配置错误</tspan></text></g></g><g ruleId=\"1491903743381\" shape=\"rectangle\" type=\"endEvent\" x=\"120\" y=\"480\" width=\"60\" height=\"60\" transform=\"translate(120,480)\" tabindex=\"0\" status=\"normal\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" class=\"bpm-shape-focus-node\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"55.5\" y=\"25.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"25.5\" y=\"55.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text text-anchor=\"middle\" name=\"name\" y=\"35\" x=\"30\" fill=\"#70757f\">结束</text><g name=\"error-node\" class=\"hide\" transform=\"translate(30, 65)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点至少有一根连入的线</tspan></text></g></g></g></svg><div class=\"paas-bpm-designer-overlay hide\"></div>"}, "isDeleted": false, "hasInstance": false}