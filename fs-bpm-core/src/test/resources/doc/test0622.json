{"activities": [{"id": "*************", "type": "startEvent", "name": "开始", "description": ""}, {"id": "*************", "type": "userTask", "name": "业务活动1", "description": "", "canSkip": false, "taskType": "anyone", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "bpmExtension": {"actionCode": "", "executionType": "update", "executionName": "编辑对象", "entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}}}, {"id": "*************", "name": "＋", "description": "", "type": "parallelGateway"}, {"id": "*************", "name": "业务活动2", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}}, "type": "userTask"}, {"id": "*************", "name": "业务活动3", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}}, "type": "userTask"}, {"id": "***********00", "name": "＋", "description": "", "type": "parallelGateway"}, {"id": "***********01", "name": "业务活动4", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "type": "userTask"}, {"id": "***********02", "name": "业务活动5", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}}, "type": "userTask"}, {"id": "***********10", "name": "＋", "description": "", "type": "parallelGateway"}, {"id": "***********13", "name": "结束", "description": "", "type": "endEvent"}, {"id": "***********14", "name": "＋", "description": "", "type": "parallelGateway"}, {"id": "***********17", "name": "业务活动7", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "type": "userTask"}, {"id": "***********27", "name": "业务活动6", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "type": "userTask"}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_***********01##AccountObj", "type": {"name": "text"}}, {"id": "activity_***********02##AccountObj", "type": {"name": "text"}}, {"id": "activity_***********17##AccountObj", "type": {"name": "text"}}, {"id": "activity_***********27##AccountObj", "type": {"name": "text"}}], "transitions": [{"id": "*************", "fromId": "*************", "toId": "*************"}, {"id": "***********03", "fromId": "*************", "toId": "*************"}, {"id": "***********04", "fromId": "*************", "toId": "*************"}, {"id": "***********05", "fromId": "*************", "toId": "*************"}, {"id": "***********07", "fromId": "*************", "toId": "***********00"}, {"id": "***********09", "fromId": "***********00", "toId": "***********02"}, {"id": "***********12", "fromId": "***********02", "toId": "***********10"}, {"id": "***********15", "fromId": "***********10", "toId": "***********14"}, {"id": "***********18", "fromId": "***********14", "toId": "***********17"}, {"id": "***********19", "fromId": "***********17", "toId": "***********13"}, {"id": "***********25", "fromId": "*************", "toId": "***********01"}, {"id": "***********26", "fromId": "***********01", "toId": "***********14"}, {"id": "***********28", "fromId": "***********00", "toId": "***********27"}, {"id": "***********29", "fromId": "***********27", "toId": "***********10"}]}