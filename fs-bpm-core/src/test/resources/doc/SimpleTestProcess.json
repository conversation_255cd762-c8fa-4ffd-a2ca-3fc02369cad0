{"name": "wangz0304流程1", "description": "简单测试流程，包含3个结点，不包含svg，diagram等前端信息", "appId": "BPM", "tenantId": "2", "enabled": true, "entryType": "AccountObj", "entryTypeName": "客户", "createTime": "*************", "id": "233706504594292736", "sourceWorkflowId": "233706504493629440", "rangeCircleIds": [1, 2], "rangeEmployeeIds": [1000, 2000], "workflow": {"name": "wangz0304流程1", "description": "简单测试流程，包含3个结点，不包含svg，diagram等前端信息", "sourceWorkflowId": "233706504493629440", "activities": [{"id": "1", "name": "开始", "type": "startEvent", "description": "开始描述"}, {"id": "2", "name": "第一个结点", "description": "第一个结点描述", "type": "userTask", "canSkip": true, "assignee": {"person": ["1000", "1204"]}, "remind": true, "taskType": "anyone", "remindLatency": 72000, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "executionType": "update", "executionName": "审批客户对象", "form": [[{"default": "默认值", "name": "name", "value": {"expression": "activity_0##AccountObj##name"}, "label": "客户详细地址", "type": "text", "readonly": false, "required": true}, {"default": "默认值", "name": "email", "value": {"expression": "activity_0##AccountObj##email"}, "label": "邮件", "type": "text", "readonly": false, "required": true}]]}}, {"id": "3", "name": "结束", "type": "endEvent"}], "transitions": [{"id": "t1", "fromId": "1", "toId": "2"}, {"id": "t2", "fromId": "2", "toId": "3"}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}]}, "extension": {"pools": [{"id": "p1", "name": "", "lanes": [{"id": "l_1", "name": "第一阶段", "activities": ["1", "2", "3"]}]}]}}