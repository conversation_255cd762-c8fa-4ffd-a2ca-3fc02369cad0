{"name": "会签测试流程", "description": "会签测试流程，包含更新、审批、会签3个流程", "appId": "BPM", "tenantId": "2", "enabled": true, "entryType": "AccountObj", "entryTypeName": "客户", "rangeCircleIds": [1, 2], "rangeEmployeeIds": [1000, 1204, 1597], "workflow": {"name": "会签试流程", "description": "会签流程，包含一个结点会签", "activities": [{"id": "1", "name": "开始", "type": "startEvent", "description": "开始描述"}, {"id": "2", "name": "更新结点", "description": "更新结点描述", "type": "userTask", "canSkip": false, "taskType": "anyone", "assignee": {"person": ["1000", "1204", "1597"]}, "remind": true, "remindLatency": 72000, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "executionType": "update", "executionName": "更新客户详细地址", "form": [[{"default": "默认值", "name": "detail_address", "value": {"expression": "activity_0##AccountObj##detail_address"}, "label": "客户详细地址", "type": "text", "readonly": false, "required": true}, {"default": "默认值", "name": "remark", "value": {"expression": "activity_0##AccountObj##remark"}, "label": "备注", "type": "long_text", "readonly": false, "required": true}, {"default": "默认值", "name": "url", "value": {"expression": "activity_0##AccountObj##url"}, "label": "网址", "type": "text", "readonly": false, "required": true}, {"default": "默认值", "name": "account_level", "value": {"expression": "activity_0##AccountObj##account_level"}, "label": "客户级别", "type": "select_one", "options": [{"value": "1", "label": "重要客户"}, {"value": "2", "label": "普通客户"}, {"value": "3", "label": "一般客户"}], "readonly": false, "required": true}, {"default": "默认值", "name": "account_level", "value": {"expression": "activity_0##AccountObj##account_level"}, "label": "客户级别22", "type": "select_many", "options": [{"value": "6", "label": "一级代理"}, {"value": "7", "label": "二级代理"}, {"value": "8", "label": "三级代理"}], "readonly": false, "required": true}]]}}, {"id": "3", "name": "审批结点", "description": "审批结点描述", "type": "userTask", "canSkip": true, "assignee": {"person": ["1000", "1204", "1597"]}, "remind": true, "taskType": "anyone", "remindLatency": 72000, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_2##AccountObj"}, "executionType": "approve", "executionName": "审批客户对象", "form": [[{"default": "默认值", "name": "result", "value": "", "label": "审批结果", "type": "text", "readonly": false, "required": true}]]}}, {"id": "4", "name": "审核是否通过", "type": "exclusiveGateway"}, {"id": "5", "name": "会签结点", "description": "会签结点描述", "type": "userTask", "canSkip": true, "taskType": "all", "assignee": {"person": ["1000", "1204", "1597"]}, "remind": true, "remindLatency": 72000, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_2##AccountObj"}, "executionType": "approve", "executionName": "会签审批", "form": [[{"default": "默认值", "name": "result", "value": "", "label": "审批结果", "type": "text", "readonly": false, "required": true}]]}}, {"id": "6", "name": "结束", "type": "endEvent"}], "transitions": [{"id": "t1", "fromId": "1", "toId": "2"}, {"id": "t2", "fromId": "2", "toId": "3"}, {"id": "t3", "fromId": "3", "toId": "4"}, {"id": "t4", "fromId": "4", "toId": "5", "name": "通过", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_3##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}}, {"id": "t5", "fromId": "4", "toId": "2", "name": "不同意", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_3##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}}, {"id": "t6", "fromId": "5", "toId": "6"}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_2##AccountObj", "type": {"name": "text"}}, {"id": "activity_3##result", "type": {"name": "text"}}, {"id": "activity_5##result", "type": {"name": "text"}}]}, "extension": {"pools": [{"id": "p1", "name": "", "lanes": [{"id": "l_1", "name": "第一阶段", "activities": ["1", "2", "3", "4"]}, {"id": "l_2", "name": "第二阶段", "activities": ["5", "6"]}]}]}}