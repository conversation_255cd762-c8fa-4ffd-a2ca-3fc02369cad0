[{"type": "startEvent", "name": "业务发起", "description": null, "id": 1624257918049}, {"type": "parallelGateway", "name": "并行任务", "description": null, "id": 1624257918094}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_1624434298675##object_0O1JF__c"}, "form": [[{"name": "field_184rv__c", "type": "select_many", "required": false, "readonly": true, "label": "贷款机构"}, {"name": "field_itqc1__c", "type": "text", "required": false, "readonly": true, "label": "抵押城市"}, {"name": "field_3eZfY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人姓名"}, {"name": "field_X2K8n__c", "type": "select_one", "required": false, "readonly": true, "label": "主贷人性别"}, {"name": "field_lcT0Z__c", "type": "number", "required": false, "readonly": true, "label": "主贷人电话"}, {"name": "field_DKVkx__c", "type": "text", "required": false, "readonly": true, "label": "主贷人身份证号"}, {"name": "field_08q23__c", "type": "text", "required": false, "readonly": true, "label": "主贷人银行卡号"}, {"name": "field_u5311__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人婚姻状况"}, {"name": "field_4RQ8G__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人学历"}, {"name": "field_i2D2w__c", "type": "text", "required": false, "readonly": true, "label": "联系人1姓名"}, {"name": "field_fWAHb__c", "type": "select_many", "required": false, "readonly": true, "label": "联系人1与主贷人关系"}, {"name": "field_oAbu3__c", "type": "number", "required": false, "readonly": true, "label": "联系人1电话"}, {"name": "field_i7ErW__c", "type": "text", "required": false, "readonly": true, "label": "联系人2姓名"}, {"name": "field_4512m__c", "type": "select_many", "required": false, "readonly": true, "label": "联系人2与主贷人关系"}, {"name": "field_14o60__c", "type": "number", "required": false, "readonly": true, "label": "联系人2电话"}, {"name": "field_yW44K__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶姓名"}, {"name": "field_qpNBY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶身份证号"}, {"name": "field_2oQiL__c", "type": "number", "required": false, "readonly": true, "label": "主贷人配偶电话"}, {"name": "field_dk230__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶学历"}, {"name": "field_FlJhz__c", "type": "text", "required": false, "readonly": true, "label": "担保人姓名"}, {"name": "field_xnu6K__c", "type": "text", "required": false, "readonly": true, "label": "担保人身份证号"}, {"name": "field_6IChh__c", "type": "number", "required": false, "readonly": true, "label": "担保人电话"}, {"name": "field_w2UMV__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人婚姻状况"}, {"name": "field_515lq__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人学历"}, {"name": "field_Y8a71__c", "type": "image", "required": false, "readonly": true, "label": "大数据授权书"}, {"name": "field_Spp2N__c", "type": "select_one", "required": true, "label": "主贷人征信结果"}, {"name": "field_4j584__c", "type": "select_one", "required": false, "label": "配偶征信结果"}, {"name": "field_cG5az__c", "type": "select_one", "required": false, "label": "担保人征信结果"}, {"name": "field_OZUYF__c", "type": "image", "required": true, "label": "征信明细"}, {"name": "field_N3X15__c", "type": "select_one", "required": true, "label": "贷款机构大数据结果"}, {"name": "field_YlU8E__c", "type": "image", "required": false, "label": "贷款机构大数据明细"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "征信查询", "description": null, "id": 1624257918096, "assignee": {"extUserType": ["${activity_1624257918096##object_0O1JF__c##field_kSkv7__c}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "reminders": [{"remindStrategy": 2.0, "remindContent": "${workflowName} 的任务 ${taskName} 已完成，完成时间 ${taskCompletedTime}。", "remindTitle": "任务完成通知", "remindTime": 0.0, "remindTargets": {"ext_process": ["${activity_1624257918097##object_0O1JF__c##owner}"]}, "channel": null}], "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_184rv__c", "type": "select_many", "required": false, "readonly": true, "label": "贷款机构"}, {"name": "field_itqc1__c", "type": "text", "required": false, "readonly": true, "label": "抵押城市"}, {"name": "field_52J9g__c", "type": "text", "required": false, "readonly": true, "label": "车商"}, {"name": "field_3eZfY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人姓名"}, {"name": "field_X2K8n__c", "type": "select_one", "required": false, "readonly": true, "label": "主贷人性别"}, {"name": "field_DKVkx__c", "type": "text", "required": false, "readonly": true, "label": "主贷人身份证号"}, {"name": "field_lcT0Z__c", "type": "number", "required": false, "readonly": true, "label": "主贷人电话"}, {"name": "field_08q23__c", "type": "text", "required": false, "readonly": true, "label": "主贷人银行卡号"}, {"name": "field_u5311__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人婚姻状况"}, {"name": "field_4RQ8G__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人学历"}, {"name": "field_10j0o__c", "type": "number", "required": false, "readonly": true, "label": "主贷人驾驶证档案编号"}, {"name": "field_i2D2w__c", "type": "text", "required": false, "readonly": true, "label": "联系人1姓名"}, {"name": "field_fWAHb__c", "type": "select_many", "required": false, "readonly": true, "label": "联系人1与主贷人关系"}, {"name": "field_oAbu3__c", "type": "number", "required": false, "readonly": true, "label": "联系人1电话"}, {"name": "field_i7ErW__c", "type": "text", "required": false, "readonly": true, "label": "联系人2姓名"}, {"name": "field_4512m__c", "type": "select_many", "required": false, "readonly": true, "label": "联系人2与主贷人关系"}, {"name": "field_14o60__c", "type": "number", "required": false, "readonly": true, "label": "联系人2电话"}, {"name": "field_yW44K__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶姓名"}, {"name": "field_qpNBY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶身份证号"}, {"name": "field_2oQiL__c", "type": "number", "required": false, "readonly": true, "label": "主贷人配偶电话"}, {"name": "field_dk230__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶学历"}, {"name": "field_FlJhz__c", "type": "text", "required": false, "readonly": true, "label": "担保人姓名"}, {"name": "field_xnu6K__c", "type": "text", "required": false, "readonly": true, "label": "担保人身份证号"}, {"name": "field_6IChh__c", "type": "number", "required": false, "readonly": true, "label": "担保人电话"}, {"name": "field_w2UMV__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人婚姻状况"}, {"name": "field_515lq__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人学历"}, {"name": "field_Y8a71__c", "type": "image", "required": false, "readonly": true, "label": "大数据授权书"}, {"name": "field_jyy0m__c", "type": "select_one", "required": true, "label": "主贷人大数据结果"}, {"name": "field_1R682__c", "type": "select_one", "required": false, "label": "配偶大数据结果"}, {"name": "field_eQJsj__c", "type": "select_one", "required": false, "label": "担保人大数据结果"}, {"name": "field_gGfaE__c", "type": "text", "required": true, "label": "大数据异常说明"}, {"name": "field_5YoiS__c", "type": "image", "required": false, "label": "大数据明细"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "大数据查询", "description": null, "id": 1624257918097, "assignee": {"person": [1004, 1134]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "parallelGateway", "name": "并行任务", "description": null, "id": 1624329210984}, {"type": "userTask", "reminders": [], "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_Kc37F__c", "type": "select_one", "required": true, "label": "车辆类别"}, {"name": "field_Xa29s__c", "type": "text", "required": true, "label": "车辆品牌"}, {"name": "field_4ik7G__c", "type": "text", "required": true, "label": "车辆型号"}, {"name": "field_Zpcil__c", "type": "text", "required": true, "label": "车架号"}, {"name": "field_V1q81__c", "type": "text", "required": true, "label": "车辆颜色"}, {"name": "field_fjwCs__c", "type": "date", "required": true, "label": "首次登记日期"}, {"name": "field_aDZEi__c", "type": "object_reference", "required": true, "label": "车龄"}, {"name": "field_sA948__c", "type": "object_reference", "required": true, "label": "车辆过户次数"}, {"name": "field_lypb4__c", "type": "number", "required": true, "label": "里程数"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "车辆基础信息", "description": null, "id": 1624329210988, "assignee": {"ext_process": ["${activity_1624329210988##object_0O1JF__c##owner}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "reminders": [{"remindStrategy": 2.0, "remindContent": "${workflowName} 的任务 ${taskName} 已完成，完成时间 ${taskCompletedTime}。", "remindTitle": "任务完成通知", "remindTime": 0.0, "remindTargets": {"ext_bpm": ["${instance##owner}"]}, "channel": "send_qixin"}], "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_Y9392__c", "type": "select_one", "required": true, "label": "考核形式"}, {"name": "field_z8153__c", "type": "employee", "required": true, "label": "考核人员"}, {"name": "field_w5x17__c", "type": "date", "required": true, "label": "考核日期"}, {"name": "field_3vxkA__c", "type": "text", "required": false, "label": "备注"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "考核形式", "description": null, "id": 1624329210992, "assignee": {"person": [1004, 1134]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "reminders": [{"remindStrategy": 2.0, "remindContent": "${workflowName} 的任务 ${taskName} 已完成，完成时间 ${taskCompletedTime}。", "remindTitle": "任务完成通知", "remindTime": 0.0, "remindTargets": {"ext_process": ["${activity_1624329210998##object_0O1JF__c##owner}"]}, "channel": "send_qixin"}], "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_1624329210992##object_0O1JF__c"}, "form": [[{"name": "field_184rv__c", "type": "select_many", "required": false, "readonly": true, "label": "贷款机构"}, {"name": "field_itqc1__c", "type": "text", "required": false, "readonly": true, "label": "抵押城市"}, {"name": "field_52J9g__c", "type": "text", "required": false, "readonly": true, "label": "车商"}, {"name": "field_3eZfY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人姓名"}, {"name": "field_X2K8n__c", "type": "select_one", "required": false, "readonly": true, "label": "主贷人性别"}, {"name": "field_DKVkx__c", "type": "text", "required": false, "readonly": true, "label": "主贷人身份证号"}, {"name": "field_lcT0Z__c", "type": "number", "required": false, "readonly": true, "label": "主贷人电话"}, {"name": "field_08q23__c", "type": "text", "required": false, "readonly": true, "label": "主贷人银行卡号"}, {"name": "field_u5311__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人婚姻状况"}, {"name": "field_4RQ8G__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人学历"}, {"name": "field_10j0o__c", "type": "number", "required": false, "readonly": true, "label": "主贷人驾驶证档案编号"}, {"name": "field_i2D2w__c", "type": "text", "required": false, "readonly": true, "label": "联系人1姓名"}, {"name": "field_fWAHb__c", "type": "select_many", "required": false, "readonly": true, "label": "联系人1与主贷人关系"}, {"name": "field_oAbu3__c", "type": "number", "required": false, "readonly": true, "label": "联系人1电话"}, {"name": "field_i7ErW__c", "type": "text", "required": false, "readonly": true, "label": "联系人2姓名"}, {"name": "field_4512m__c", "type": "select_many", "required": false, "readonly": true, "label": "联系人2与主贷人关系"}, {"name": "field_14o60__c", "type": "number", "required": false, "readonly": true, "label": "联系人2电话"}, {"name": "field_yW44K__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶姓名"}, {"name": "field_qpNBY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶身份证号"}, {"name": "field_2oQiL__c", "type": "number", "required": false, "readonly": true, "label": "主贷人配偶电话"}, {"name": "field_dk230__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶学历"}, {"name": "field_FlJhz__c", "type": "text", "required": false, "readonly": true, "label": "担保人姓名"}, {"name": "field_xnu6K__c", "type": "text", "required": false, "readonly": true, "label": "担保人身份证号"}, {"name": "field_6IChh__c", "type": "number", "required": false, "readonly": true, "label": "担保人电话"}, {"name": "field_w2UMV__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人婚姻状况"}, {"name": "field_515lq__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人学历"}, {"name": "field_Y8a71__c", "type": "image", "required": false, "readonly": true, "label": "大数据授权书"}, {"name": "field_Spp2N__c", "type": "select_one", "required": false, "readonly": true, "label": "主贷人征信结果"}, {"name": "field_4j584__c", "type": "select_one", "required": false, "readonly": true, "label": "配偶征信结果"}, {"name": "field_cG5az__c", "type": "select_one", "required": false, "readonly": true, "label": "担保人征信结果"}, {"name": "field_OZUYF__c", "type": "image", "required": false, "readonly": true, "label": "征信明细"}, {"name": "field_N3X15__c", "type": "select_one", "required": false, "readonly": true, "label": "贷款机构大数据结果"}, {"name": "field_YlU8E__c", "type": "image", "required": false, "readonly": true, "label": "贷款机构大数据明细"}, {"name": "field_jyy0m__c", "type": "select_one", "required": false, "readonly": true, "label": "主贷人大数据结果"}, {"name": "field_1R682__c", "type": "select_one", "required": false, "readonly": true, "label": "配偶大数据结果"}, {"name": "field_eQJsj__c", "type": "select_one", "required": false, "readonly": true, "label": "担保人大数据结果"}, {"name": "field_gGfaE__c", "type": "text", "required": false, "readonly": true, "label": "大数据异常说明"}, {"name": "field_5YoiS__c", "type": "image", "required": false, "readonly": true, "label": "大数据明细"}, {"name": "field_Kc37F__c", "type": "select_one", "required": false, "readonly": true, "label": "车辆类别"}, {"name": "field_Xa29s__c", "type": "text", "required": false, "readonly": true, "label": "车辆品牌"}, {"name": "field_4ik7G__c", "type": "text", "required": false, "readonly": true, "label": "车辆型号"}, {"name": "field_Zpcil__c", "type": "text", "required": false, "readonly": true, "label": "车架号"}, {"name": "field_V1q81__c", "type": "text", "required": false, "readonly": true, "label": "车辆颜色"}, {"name": "field_fjwCs__c", "type": "date", "required": false, "readonly": true, "label": "首次登记日期"}, {"name": "field_lypb4__c", "type": "number", "required": false, "readonly": true, "label": "里程数"}, {"name": "field_Y9392__c", "type": "select_one", "required": false, "readonly": true, "label": "考核形式"}, {"name": "field_z8153__c", "type": "employee", "required": false, "readonly": true, "label": "考核人员"}, {"name": "field_w5x17__c", "type": "date", "required": false, "readonly": true, "label": "考核日期"}, {"name": "field_3vxkA__c", "type": "text", "required": false, "readonly": true, "label": "备注"}, {"name": "field_mZjl4__c", "type": "select_one", "required": true, "label": "考核评估"}, {"name": "field_b19wW__c", "type": "select_one", "required": true, "readonly": false, "label": "婚姻状况"}, {"name": "field_c2HXa__c", "type": "text", "required": true, "label": "家庭结构"}, {"name": "field_wc925__c", "type": "text", "required": true, "readonly": false, "label": "现住址"}, {"name": "field_eP00e__c", "type": "select_one", "required": true, "label": "住房权属"}, {"name": "field_2c2Q8__c", "type": "select_one", "required": true, "label": "购车性质"}, {"name": "field_Zd1iz__c", "type": "currency", "required": true, "label": "实际成交价"}, {"name": "field_JnIpt__c", "type": "currency", "required": true, "label": "贷款额度"}, {"name": "field_S2K1r__c", "type": "text", "required": true, "label": "首付情况及构成"}, {"name": "field_J1Vv6__c", "type": "select_one", "required": true, "label": "驾照核验"}, {"name": "field_D1p41__c", "type": "text", "required": false, "label": "其它房产信息"}, {"name": "field_08C5l__c", "type": "text", "required": false, "label": "其它车辆信息"}, {"name": "field_oqVwo__c", "type": "text", "required": false, "label": "其他资产"}, {"name": "field_S0l9e__c", "type": "text", "required": true, "label": "主贷人单位名称"}, {"name": "field_f9zA3__c", "type": "text", "required": true, "label": "主贷人单位地址"}, {"name": "field_RJ3tl__c", "type": "text", "required": true, "label": "主贷人工作年限"}, {"name": "field_GaR2S__c", "type": "text", "required": true, "label": "主贷人职务及工作内容"}, {"name": "field_1leee__c", "type": "text", "required": true, "label": "主贷人负债情况"}, {"name": "field_372Fo__c", "type": "text", "required": true, "label": "主贷人收入情况及构成"}, {"name": "field_511oy__c", "type": "text", "required": false, "label": "配偶资产信息"}, {"name": "field_4vRgp__c", "type": "text", "required": false, "label": "配偶工作信息"}, {"name": "field_6gc1w__c", "type": "text", "required": false, "label": "配偶收入及负债情况"}, {"name": "field_f3kWG__c", "type": "select_one", "required": false, "label": "担保人驾照核验"}, {"name": "field_Opwc8__c", "type": "text", "required": false, "label": "担保人家庭情况"}, {"name": "field_C103c__c", "type": "text", "required": false, "label": "担保人现住址"}, {"name": "field_qhBC2__c", "type": "select_one", "required": false, "label": "担保人住房权属"}, {"name": "field_6idrr__c", "type": "text", "required": false, "label": "担保人资产信息"}, {"name": "field_s09l0__c", "type": "text", "required": false, "label": "担保人工作信息"}, {"name": "field_fW4Q5__c", "type": "text", "required": false, "label": "担保人收入及负债情况"}, {"name": "field_wT8a9__c", "type": "long_text", "required": false, "label": "实际用车/钱人与主贷人关系"}, {"name": "field_pgsTu__c", "type": "long_text", "required": false, "label": "其他情况说明"}, {"name": "field_J1rjL__c", "type": "image", "required": true, "label": "证明材料"}, {"name": "field_B24hF__c", "type": "image", "required": false, "label": "家访照片"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "考核结果", "description": null, "id": 1624329210998, "assignee": {"extUserType": ["${activity_1624329210998##object_0O1JF__c##field_z8153__c}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "reminders": [{"remindStrategy": 2.0, "remindContent": "${workflowName} 的任务 ${taskName} 已完成，完成时间 ${taskCompletedTime}。", "remindTitle": "任务完成通知", "remindTime": 0.0, "remindTargets": {"ext_bpm": ["${instance##owner}"]}, "channel": "send_qixin"}], "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_1624329210998##object_0O1JF__c}", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "form": [[{"name": "field_7y7tr__c", "type": "select_one", "required": false, "label": "终审结果"}, {"name": "field_YL0Sj__c", "type": "long_text", "required": false, "label": "拒绝原因"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "审批", "description": null, "id": 1624329211003, "assignee": {"person": [1004, 1134]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_WX41L__c", "type": "select_one", "required": true, "label": "材料是否齐全"}, {"name": "field_vwukd__c", "type": "select_one", "required": true, "label": "是否过户"}, {"name": "field_njS7d__c", "type": "select_one", "required": false, "label": "未过户说明"}, {"name": "field_N6b5a__c", "type": "image", "required": false, "label": "过户手续照片"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "垫资确认", "description": null, "id": 1624329211008, "assignee": {"ext_bpm": ["${instance##owner}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_1624329210998##object_0O1JF__c}", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "form": [[{"name": "field_7y7tr__c", "type": "select_one", "required": false, "label": "终审结果"}, {"name": "field_YL0Sj__c", "type": "long_text", "required": false, "label": "拒绝原因"}, {"name": "field_68DzN__c", "type": "select_one", "required": false, "label": "总监审批"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "总监审批", "description": null, "id": 1624329211010, "assignee": {"person": [1001]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}}, {"type": "endEvent", "name": "结束", "description": null, "id": 1624329211012}, {"type": "userTask", "reminders": [{"remindStrategy": 2.0, "remindContent": "${workflowName} 的任务 ${taskName} 已完成，完成时间 ${taskCompletedTime}。", "remindTitle": "任务完成通知", "remindTime": 0.0, "remindTargets": {"ext_bpm": ["${instance##owner}"]}, "channel": "send_qixin"}], "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_184rv__c", "type": "select_many", "required": false, "readonly": true, "label": "贷款机构"}, {"name": "field_itqc1__c", "type": "text", "required": false, "readonly": true, "label": "抵押城市"}, {"name": "field_52J9g__c", "type": "text", "required": false, "readonly": true, "label": "车商"}, {"name": "field_3eZfY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人姓名"}, {"name": "field_X2K8n__c", "type": "select_one", "required": false, "readonly": true, "label": "主贷人性别"}, {"name": "field_DKVkx__c", "type": "text", "required": false, "readonly": true, "label": "主贷人身份证号"}, {"name": "field_u5311__c", "type": "select_many", "required": false, "readonly": true, "label": "主贷人婚姻状况"}, {"name": "field_yW44K__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶姓名"}, {"name": "field_qpNBY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人配偶身份证号"}, {"name": "field_FlJhz__c", "type": "text", "required": false, "readonly": true, "label": "担保人姓名"}, {"name": "field_xnu6K__c", "type": "text", "required": false, "readonly": true, "label": "担保人身份证号"}, {"name": "field_w2UMV__c", "type": "select_many", "required": false, "readonly": true, "label": "担保人婚姻状况"}, {"name": "field_kSkv7__c", "type": "employee", "required": true, "label": "面签人员"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "选择运营人员", "description": null, "id": 1624434298675, "assignee": {"person": [1045]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "parallelGateway", "name": "并行任务", "description": null, "id": 1624434298685}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_1624434298675##object_0O1JF__c"}, "form": [[{"name": "field_kSkv7__c", "type": "employee", "required": true, "readonly": false, "label": "面签人员"}, {"name": "field_0cu0D__c", "type": "date", "required": true, "readonly": false, "label": "面签日期"}, {"name": "field_pfL22__c", "type": "currency", "required": true, "label": "客贷金额"}, {"name": "field_2Z0nP__c", "type": "currency", "required": false, "label": "合同金额"}, {"name": "field_oG029__c", "type": "percentile", "required": false, "label": "总手续费率"}, {"name": "field_d629A__c", "type": "currency", "required": false, "label": "总手续费金额"}, {"name": "field_e1gYL__c", "type": "percentile", "required": false, "label": "店内手续费率"}, {"name": "field_L7rz7__c", "type": "currency", "required": false, "label": "店内手续费金额"}, {"name": "field_4BggA__c", "type": "percentile", "required": false, "label": "车商手续费率"}, {"name": "field_gSy42__c", "type": "currency", "required": false, "label": "车商手续费金额"}, {"name": "field_58B8B__c", "type": "percentile", "required": false, "label": "介绍人手续费率"}, {"name": "field_syPp6__c", "type": "currency", "required": false, "label": "介绍人手续费金额"}, {"name": "field_1kTE5__c", "type": "currency", "required": false, "label": "杂费金额"}, {"name": "field_3v09G__c", "type": "currency", "required": false, "label": "末期月还金额"}, {"name": "field_hwd8D__c", "type": "currency", "required": false, "label": "权益包金额"}, {"name": "field_668l5__c", "type": "currency", "required": false, "label": "服务费金额"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "面签及贷款信息确认", "description": null, "id": 1624434298688, "assignee": {"extUserType": ["${activity_1624434298688##object_0O1JF__c##field_kSkv7__c}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_1624329211008##object_0O1JF__c}", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "form": [[{"name": "field_WX41L__c", "type": "select_one", "required": false, "label": "材料是否齐全"}, {"name": "field_vwukd__c", "type": "select_one", "required": false, "label": "是否过户"}, {"name": "field_njS7d__c", "type": "select_one", "required": false, "label": "未过户说明"}, {"name": "field_N6b5a__c", "type": "image", "required": false, "label": "过户手续照片"}, {"name": "field_pfL22__c", "type": "currency", "required": false, "label": "客贷金额"}, {"name": "field_va5v1__c", "type": "object_reference", "required": false, "label": "合同金额"}, {"name": "field_oG029__c", "type": "percentile", "required": false, "label": "总手续费率"}, {"name": "field_d629A__c", "type": "currency", "required": false, "label": "总手续费金额"}, {"name": "field_e1gYL__c", "type": "percentile", "required": false, "label": "店内手续费率"}, {"name": "field_L7rz7__c", "type": "currency", "required": false, "label": "店内手续费金额"}, {"name": "field_4BggA__c", "type": "percentile", "required": false, "label": "车商手续费率"}, {"name": "field_gSy42__c", "type": "currency", "required": false, "label": "车商手续费金额"}, {"name": "field_58B8B__c", "type": "percentile", "required": false, "label": "介绍人手续费率"}, {"name": "field_syPp6__c", "type": "currency", "required": false, "label": "介绍人手续费金额"}, {"name": "field_1kTE5__c", "type": "currency", "required": false, "label": "杂费金额"}, {"name": "field_3v09G__c", "type": "currency", "required": false, "label": "末期月还金额"}, {"name": "field_hwd8D__c", "type": "currency", "required": false, "label": "权益包金额"}, {"name": "field_668l5__c", "type": "currency", "required": false, "label": "服务费金额"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "垫资审批", "description": null, "id": 1624434298701, "assignee": {"ext_bpm": ["${activity_1624329210988##assigneeId##leader}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_1624329211008##object_0O1JF__c"}, "form": [[{"name": "field_89kJp__c", "type": "date", "required": true, "label": "件全日期"}, {"name": "field_vpj3n__c", "type": "date", "required": true, "label": "上报银行日期"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "组卷信息", "description": null, "id": 1624434298704, "assignee": {"extUserType": ["${activity_1624434298704##object_0O1JF__c##field_kSkv7__c}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_TYY42__c", "type": "date", "required": true, "label": "抵押手续件全日期"}, {"name": "field_ef5OW__c", "type": "date", "required": true, "label": "完成抵押日期"}, {"name": "field_4ftIw__c", "type": "text", "required": true, "label": "车牌号（新）"}, {"name": "field_92Rb3__c", "type": "date", "required": true, "label": "保单生效日期"}, {"name": "field_1lJfb__c", "type": "text", "required": true, "label": "最终抵押地"}, {"name": "field_a02m4__c", "type": "text", "required": true, "label": "抵押人员"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "抵押", "description": null, "id": 1624434298711, "assignee": {"person": [1011]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": null, "id": 1624434298718}, {"type": "parallelGateway", "name": "并行任务", "description": null, "id": 1624585104071}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_18415__c", "type": "image", "required": false, "readonly": true, "label": "转账截图"}, {"name": "field_s5OHb__c", "type": "employee", "required": true, "label": "安装人员"}, {"name": "field_Red1k__c", "type": "select_one", "required": true, "label": "GPS退回原因"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "指定GPS安装人员", "description": null, "id": 1624585104073, "assignee": {"person": [1004, 1134]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_tQ51g__c", "type": "long_text", "required": true, "label": "GPS安装联系人"}, {"name": "field_RPNmt__c", "type": "phone_number", "required": true, "label": "gps安装联系人电话"}, {"name": "field_18415__c", "type": "image", "required": false, "label": "转账截图"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "申请安装GPS", "description": null, "id": 1624585104074, "assignee": {"ext_bpm": ["${instance##owner}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "exclusiveGateway", "name": "分支节点", "description": null, "id": 1624585104078, "defaultTransitionId": 1624585104088, "gatewayType": 0.0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_184rv__c", "type": "select_many", "required": false, "readonly": true, "label": "贷款机构"}, {"name": "field_52J9g__c", "type": "text", "required": false, "readonly": true, "label": "车商"}, {"name": "field_3eZfY__c", "type": "text", "required": false, "readonly": true, "label": "主贷人姓名"}, {"name": "field_Xa29s__c", "type": "text", "required": false, "readonly": true, "label": "车辆品牌"}, {"name": "field_4ik7G__c", "type": "text", "required": false, "readonly": true, "label": "车辆型号"}, {"name": "field_Zpcil__c", "type": "text", "required": false, "readonly": true, "label": "车架号"}, {"name": "field_tQ51g__c", "type": "long_text", "required": false, "readonly": true, "label": "GPS安装联系人"}, {"name": "field_RPNmt__c", "type": "phone_number", "required": false, "readonly": true, "label": "gps安装联系人电话"}, {"name": "field_i9a2S__c", "type": "number", "required": true, "label": "乐道GPS安装数"}, {"name": "field_TdIXU__c", "type": "long_text", "required": true, "label": "乐道GPS设备编号及安装位置"}, {"name": "field_DGjgb__c", "type": "number", "required": true, "label": "渠道GPS安装数"}, {"name": "field_Z9tl1__c", "type": "long_text", "required": true, "label": "渠道GPS设备编号及安装位置"}, {"name": "field_5wnh9__c", "type": "select_one", "required": true, "label": "GPS是否安装"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "填写GPS安装信息", "description": null, "id": 1624585104086, "assignee": {"extUserType": ["${activity_1624585104086##object_0O1JF__c##field_s5OHb__c}"]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "乐道车贷业务", "entityId": "object_0O1JF__c", "executionType": "update", "objectId": {"expression": "activity_0##object_0O1JF__c"}, "form": [[{"name": "field_4ik7G__c", "type": "text", "required": false, "readonly": true, "label": "车辆型号"}, {"name": "field_Zpcil__c", "type": "text", "required": false, "readonly": true, "label": "车架号"}, {"name": "field_5wnh9__c", "type": "select_one", "required": false, "readonly": true, "label": "GPS是否安装"}, {"name": "field_i9a2S__c", "type": "number", "required": false, "readonly": true, "label": "乐道GPS安装数"}, {"name": "field_TdIXU__c", "type": "long_text", "required": false, "readonly": true, "label": "乐道GPS设备编号及安装位置"}, {"name": "field_987aB__c", "type": "select_one", "required": true, "label": "GPS信息是否通过"}, {"name": "field_1EXp1__c", "type": "select_one", "required": true, "label": "GPS是否绑定"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": false, "name": "GPS绑定", "description": null, "id": 1624585104089, "assignee": {"person": [1033]}, "taskType": "anyone", "assignNextTask": 0.0, "externalApplyTask": 0}, {"id": 1624859845464, "name": "并行任务", "description": null, "type": "parallelGateway"}]