{"name": "2月23晚taskname测试", "description": "包含审批，更新，changeowner操作结点", "appId": "BPM", "tenantId": "2", "enabled": true, "entryType": "AccountObj", "entryTypeName": "客户", "rangeCircleIds": [1, 2], "rangeEmployeeIds": [1000, 2000], "workflow": {"activities": [{"id": "1", "taskName": "开始", "type": "startEvent", "description": "开始描述"}, {"id": "2", "taskName": "客户报备update", "description": "报备描述", "type": "userTask", "canSkip": false, "assignee": {"person": ["1000"]}, "remind": true, "remindLatency": 7200, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "executionType": "update", "executionName": "更新客户详细地址", "form": [[{"default": "默认值", "name": "detail_address", "value": {"expression": "activity_0##AccountObj##detail_address"}, "label": "客户详细地址", "type": "text", "readonly": false, "required": true}, {"default": "默认值", "name": "remark", "value": {"expression": "activity_0##AccountObj##remark"}, "label": "备注", "type": "long_text", "readonly": false, "required": true}, {"default": "默认值", "name": "url", "value": {"expression": "activity_0##AccountObj##url"}, "label": "网址", "type": "text", "readonly": false, "required": true}, {"default": "默认值", "name": "account_level", "value": {"expression": "activity_0##AccountObj##account_level"}, "label": "客户级别", "type": "select_one", "options": [{"value": "1", "label": "重要客户"}, {"value": "2", "label": "普通客户"}, {"value": "3", "label": "一般客户"}], "readonly": false, "required": true}, {"default": "默认值", "name": "account_level", "value": {"expression": "activity_0##AccountObj##account_level"}, "label": "客户级别22", "type": "select_many", "options": [{"value": "6", "label": "一级代理"}, {"value": "7", "label": "二级代理"}, {"value": "8", "label": "三级代理"}], "readonly": false, "required": true}]]}}, {"id": "3", "taskName": "客户审核approve", "description": "审核描述", "type": "userTask", "canSkip": true, "assignee": {"person": ["1000", "1000", "1317"]}, "taskType": "one_pass", "remind": true, "remindLatency": 7200, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "executionType": "approve", "executionName": "审批客户对象", "form": [[{"default": "默认值", "name": "result", "value": true, "label": "审批结果", "type": "boolean", "readonly": false, "required": true}]]}}, {"id": "4", "taskName": "审核是否通过", "type": "exclusiveGateway"}, {"id": "5", "taskName": "更换负责人action", "description": "action描述", "type": "userTask", "canSkip": true, "assignee": {"person": ["1000"]}, "remind": true, "remindLatency": 7200, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "executionType": "operation", "executionName": "更新客户对象", "actionCode": "changeowner", "actionName": "变更负责人", "form": [[]]}}, {"id": "6", "taskName": "过了这个就结束了", "description": "当前结点描述", "type": "userTask", "canSkip": true, "assignee": {"person": ["1000"]}, "remind": true, "remindLatency": 7200, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "executionType": "update", "executionName": "更新", "form": [[]]}}, {"id": "7", "taskName": "结束", "type": "endEvent"}], "transitions": [{"id": "t1", "fromId": "1", "toId": "2"}, {"id": "t2", "fromId": "2", "toId": "3"}, {"id": "t3", "fromId": "3", "toId": "4"}, {"id": "t4", "fromId": "4", "toId": "5", "name": "通过", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_3##result"}, "right": {"value": true, "type": {"name": "boolean"}}}]}}, {"id": "t5", "fromId": "4", "toId": "2", "name": "驳回", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_3##result"}, "right": {"value": false, "type": {"name": "boolean"}}}]}}, {"id": "t6", "fromId": "5", "toId": "6"}, {"id": "t7", "fromId": "6", "toId": "7"}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_3##result", "type": {"name": "boolean"}}, {"id": "activity_2##AccountObj", "type": {"name": "text"}}, {"id": "activity_3##AccountObj", "type": {"name": "text"}}, {"id": "activity_5##AccountObj", "type": {"name": "text"}}, {"id": "activity_6##AccountObj", "type": {"name": "text"}}]}, "extension": {"pools": [{"id": "p1", "name": "", "lanes": [{"id": "l_1", "name": "第一阶段", "activities": ["1", "2"]}, {"id": "l_2", "name": "第二阶段", "activities": ["3", "4"]}, {"id": "l_3", "name": "第三阶段", "activities": ["5", "6", "7"]}]}]}}