{"tenantId": "2", "userId": "1000", "workflowId": "", "name": "典型流程-大客户备案会签", "count": 0, "enabled": true, "description": "大客户级别确认会签", "entryType": "AccountObj", "entryTypeName": "客户", "rangeEmployeeIds": [], "rangeCircleIds": [1001], "rangeGroupIds": [], "rangeRoleIds": [], "createdBy": "1000", "createTime": *************, "lastModifiedBy": "1000", "lastModifiedTime": *************, "workflow": {"type": "workflow_bpm", "status": "open", "name": "典型流程-大客户备案会签", "description": "大客户级别确认会签", "activities": [{"type": "startEvent", "icon": "start-icon", "element": "start", "name": "开始", "description": "", "id": "*************"}, {"type": "userTask", "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "actionCode": "", "executionType": "update", "executionName": "编辑对象", "form": [[{"name": "_id", "value": "", "type": "text", "label": "客户ID", "hidden": true}, {"name": "account_level", "label": "客户级别", "value": "", "type": "select_one", "required": false, "readonly": false, "options": [{"label": "重要客户", "value": "1", "resource_bundle_key": ""}, {"label": "普通客户", "value": "2", "resource_bundle_key": ""}, {"label": "一般客户", "value": "3", "resource_bundle_key": ""}, {"label": "123453", "value": "4", "resource_bundle_key": ""}, {"label": "abcbvcb", "value": "5", "resource_bundle_key": ""}, {"label": "一级代理", "value": "6", "resource_bundle_key": ""}, {"label": "二级代理", "value": "7", "resource_bundle_key": ""}, {"label": "三级代理", "value": "8", "resource_bundle_key": ""}]}]]}, "icon": "approval-icon", "className": "", "canSkip": false, "layout": {"layout": {"api_name": "undefined_detail_layout", "components": [{"api_name": "default_component", "field_section": [{"api_name": "default", "form_fields": [{"field_name": "account_level", "render_type": "select_one"}]}], "object_describe": {"api_name": "bpmtest__c", "fields": {}, "validate_rules": {}}, "type": "form"}], "is_default": true, "layout_type": "detail"}, "objectDescribe": {"default_detail_layout": "bpmtest__c_detail_layout", "fields": {"account_level": {"type": "select_one", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "options": [{"label": "重要客户", "value": "1", "resource_bundle_key": ""}, {"label": "普通客户", "value": "2", "resource_bundle_key": ""}, {"label": "一般客户", "value": "3", "resource_bundle_key": ""}, {"label": "123453", "value": "4", "resource_bundle_key": ""}, {"label": "abcbvcb", "value": "5", "resource_bundle_key": ""}, {"label": "一级代理", "value": "6", "resource_bundle_key": ""}, {"label": "二级代理", "value": "7", "resource_bundle_key": ""}, {"label": "三级代理", "value": "8", "resource_bundle_key": ""}], "api_name": "account_level", "status": "released", "label": "客户级别", "description": "客户级别"}}}}, "form": [[{"name": "_id", "value": "", "type": "text", "label": "客户ID", "hidden": true}, {"name": "account_level", "label": "客户级别", "value": "", "type": "select_one", "required": false, "readonly": false, "options": [{"label": "重要客户", "value": "1", "resource_bundle_key": ""}, {"label": "普通客户", "value": "2", "resource_bundle_key": ""}, {"label": "一般客户", "value": "3", "resource_bundle_key": ""}, {"label": "123453", "value": "4", "resource_bundle_key": ""}, {"label": "abcbvcb", "value": "5", "resource_bundle_key": ""}, {"label": "一级代理", "value": "6", "resource_bundle_key": ""}, {"label": "二级代理", "value": "7", "resource_bundle_key": ""}, {"label": "三级代理", "value": "8", "resource_bundle_key": ""}]}]], "value": "activity_*************##AccountObj", "element": "business", "name": "客户分级", "description": "", "id": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "element": "exclusive-gateway", "name": "分支节点", "description": "重要客户需审核\n非重要客户直接通过", "id": "*************"}, {"type": "userTask", "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "actionCode": "", "executionType": "approve", "executionName": "", "form": [[{"name": "result", "value": true, "label": "会签结果", "type": "text", "readonly": false, "required": true}]], "objectId": {"expression": "activity_*************##AccountObj"}}, "element": "multi-approval", "name": "会签", "description": "重要客户升级会签", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##AccountObj##owner", "activity_*************##AccountObj##leader"]}, "taskType": "all"}, {"type": "endEvent", "element": "end", "name": "结束", "description": "", "id": "*************"}, {"type": "userTask", "layout": {"layout": {"api_name": "undefined_detail_layout", "components": [{"api_name": "default_component", "field_section": [{"api_name": "default", "form_fields": [{"field_name": "account_level", "render_type": "select_one"}]}], "object_describe": {"api_name": "bpmtest__c", "fields": {}, "validate_rules": {}}, "type": "form"}], "is_default": true, "layout_type": "detail"}, "objectDescribe": {"default_detail_layout": "bpmtest__c_detail_layout", "fields": {"account_level": {"type": "select_one", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "options": [{"label": "重要客户", "value": "1", "resource_bundle_key": ""}, {"label": "普通客户", "value": "2", "resource_bundle_key": ""}, {"label": "一般客户", "value": "3", "resource_bundle_key": ""}, {"label": "123453", "value": "4", "resource_bundle_key": ""}, {"label": "abcbvcb", "value": "5", "resource_bundle_key": ""}, {"label": "一级代理", "value": "6", "resource_bundle_key": ""}, {"label": "二级代理", "value": "7", "resource_bundle_key": ""}, {"label": "三级代理", "value": "8", "resource_bundle_key": ""}], "api_name": "account_level", "status": "released", "label": "客户级别", "description": "客户级别"}}}}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "actionCode": "", "executionType": "update", "executionName": "", "form": [[{"name": "_id", "value": "", "type": "text", "label": "客户ID", "hidden": true}, {"name": "account_level", "label": "客户级别", "value": "", "type": "select_one", "required": false, "readonly": false, "options": [{"label": "重要客户", "value": "1", "resource_bundle_key": ""}, {"label": "普通客户", "value": "2", "resource_bundle_key": ""}, {"label": "一般客户", "value": "3", "resource_bundle_key": ""}, {"label": "123453", "value": "4", "resource_bundle_key": ""}, {"label": "abcbvcb", "value": "5", "resource_bundle_key": ""}, {"label": "一级代理", "value": "6", "resource_bundle_key": ""}, {"label": "二级代理", "value": "7", "resource_bundle_key": ""}, {"label": "三级代理", "value": "8", "resource_bundle_key": ""}]}]], "objectId": {"expression": "activity_0##AccountObj"}}, "form": [[{"name": "_id", "value": "", "type": "text", "label": "客户ID", "hidden": true}, {"name": "account_level", "label": "客户级别", "value": "", "type": "select_one", "required": false, "readonly": false, "options": [{"label": "重要客户", "value": "1", "resource_bundle_key": ""}, {"label": "普通客户", "value": "2", "resource_bundle_key": ""}, {"label": "一般客户", "value": "3", "resource_bundle_key": ""}, {"label": "123453", "value": "4", "resource_bundle_key": ""}, {"label": "abcbvcb", "value": "5", "resource_bundle_key": ""}, {"label": "一级代理", "value": "6", "resource_bundle_key": ""}, {"label": "二级代理", "value": "7", "resource_bundle_key": ""}, {"label": "三级代理", "value": "8", "resource_bundle_key": ""}]}]], "element": "business", "name": "重新定义客户级别", "description": "重新定义客户级别", "id": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "endEvent", "element": "end", "name": "结束", "description": "", "id": "*************"}, {"type": "endEvent", "element": "end", "name": "结束", "description": "", "id": "*************"}], "transitions": [{"id": "*************", "fromId": "*************", "toId": "*************"}, {"id": "*************", "fromId": "*************", "toId": "*************"}, {"description": "客户分级 / 客户级别 等于 重要客户", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##AccountObj##account_level"}, "right": {"value": "1", "type": {"name": "text"}}}]}]}}, {"id": "*************", "fromId": "*************", "toId": "*************"}, {"id": "*************", "fromId": "*************", "toId": "*************"}, {"id": "*************", "fromId": "*************", "toId": "*************"}, {"description": "客户分级 / 客户级别 不等于 重要客户", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "notEquals", "left": {"expression": "activity_*************##AccountObj##account_level"}, "right": {"value": "1", "type": {"name": "text"}}}]}]}}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##result", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj##account_level", "type": {"name": "text"}}]}, "extension": {"id": "231685770195927040", "pools": [{"lanes": [{"id": "*************", "name": "客户分级", "activities": ["*************", "*************"]}]}, {"lanes": [{"id": "*************", "name": "重要客户升级会签", "activities": ["*************", "*************"]}]}], "diagram": [{"id": "*************", "attr": {"width": "583", "height": "600", "x": "299", "y": "51"}}, {"id": "*************", "attr": {"width": "216", "height": "600", "x": "50", "y": "50"}}, {"id": "*************", "attr": {"d": "M385 279 L385 454", "connectPosition": "top"}}, {"id": "*************", "attr": {"d": "M757 281                      L758 447", "connectPosition": "top"}}, {"id": "*************", "attr": {"d": "M567 282                          L568 451", "connectPosition": "top"}}, {"id": "*************", "attr": {"d": "M567 282                                  L673 280", "connectPosition": "left"}}, {"id": "*************", "attr": {"d": "M385 279               L523 279", "connectPosition": "left"}}, {"id": "*************", "attr": {"d": "M160 278                L341 276", "connectPosition": "left"}}, {"id": "*************", "attr": {"d": "M160 124 L160 250", "connectPosition": "top"}}, {"id": "*************", "attr": {"width": "60", "height": "60", "x": "130", "y": "100"}}, {"id": "*************", "attr": {"width": "160", "height": "48", "x": "80", "y": "254"}}, {"id": "*************", "attr": {"width": "160", "height": "48", "x": "677", "y": "257"}}, {"id": "*************", "attr": {"width": "60", "height": "60", "x": "539", "y": "455"}}, {"id": "*************", "attr": {"width": "60", "height": "60", "x": "729", "y": "451"}}, {"id": "*************", "attr": {"width": "60", "height": "60", "x": "355", "y": "458"}}, {"id": "*************", "attr": {"width": "80", "height": "80", "x": "527", "y": "238"}}, {"attr": {"d": "M0,0 L20,20 L19.9418747,2.99420208 C19.9362226,1.34054993 18.5929201,0 16.9260921,0 L0,0 Z"}}, {"id": "*************", "attr": {"width": "80", "height": "80", "x": "345", "y": "235"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"100%\" width=\"100%\" viewBox=\"0 0 932 701\"><g id=\"*************\" shape=\"rectangle\" type=\"pool\" x=\"299\" y=\"51\" width=\"583\" height=\"600\" transform=\"translate(299,51)\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"583\" height=\"600\" namePos=\"top\" highlight=\"false\" stroke=\"#000000\" stroke-opacity=\"0.16\" rx=\"1\" ry=\"1\" resizable=\"true\" placeAt=\"first\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"583\" height=\"600\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"300\"></circle><circle r=\"5\" name=\"top\" cx=\"291.5\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"583\" cy=\"300\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"291.5\" cy=\"600\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></circle></g><text name=\"name\" y=\"30\" x=\"239.5\">重要客户升级会签</text></g><g id=\"*************\" shape=\"rectangle\" type=\"pool\" x=\"50\" y=\"50\" width=\"216\" height=\"600\" transform=\"translate(50,50)\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"216\" height=\"600\" namePos=\"top\" highlight=\"false\" stroke=\"#000000\" stroke-opacity=\"0.16\" rx=\"1\" ry=\"1\" resizable=\"true\" placeAt=\"first\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"216\" height=\"600\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"300\"></circle><circle r=\"5\" name=\"top\" cx=\"108\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"216\" cy=\"300\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"108\" cy=\"600\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></circle></g><text name=\"name\" y=\"30\" x=\"82\">客户分级</text></g><defs><marker id=\"end-arrow\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"0\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"0\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><style type=\"text/css\">svg{background-color:#f3f3f5}g[type=pool]{font-size:13px}</style></defs><g name=\"line-wrapper\"><g id=\"*************\"><path d=\"M385 279 L385 454\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"top\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g><g id=\"*************\"><path d=\"M757 281                      L758 447\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"top\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g><g id=\"*************\"><path d=\"M567 282                          L568 451\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"top\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g><g id=\"*************\"><path d=\"M567 282                                  L673 280\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"left\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g><g id=\"*************\"><path d=\"M385 279               L523 279\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"left\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g><g id=\"*************\"><path d=\"M160 278                L341 276\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"left\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g><g id=\"*************\"><path d=\"M160 124 L160 250\" start-id=\"*************\" end-id=\"*************\" stroke-width=\"1\" connect-position=\"top\" stroke=\"#999999\" marker-end=\"url(#end-arrow)\"></path></g></g><g id=\"*************\" shape=\"rectangle\" type=\"startEvent\" x=\"130\" y=\"100\" width=\"60\" height=\"60\" transform=\"translate(130,100)\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#708fcc\" stroke-width=\"3\" color=\"#708fcc\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"30\"></circle><circle r=\"5\" name=\"top\" cx=\"30\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"60\" cy=\"30\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"30\" cy=\"60\"></circle></g><text name=\"name\" y=\"35\" fill=\"#708fcc\" x=\"18\">开始</text></g><g id=\"*************\" shape=\"rectangle\" type=\"userTask\" x=\"80\" y=\"254\" width=\"160\" height=\"48\" transform=\"translate(80,254)\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"48\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"48\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"24\"></circle><circle r=\"5\" name=\"top\" cx=\"80\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"160\" cy=\"24\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"80\" cy=\"48\"></circle></g><text name=\"name\" y=\"29\" x=\"56\">客户分级</text></g><g id=\"*************\" shape=\"rectangle\" type=\"userTask\" x=\"677\" y=\"257\" width=\"160\" height=\"48\" transform=\"translate(677,257)\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"48\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"48\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"24\"></circle><circle r=\"5\" name=\"top\" cx=\"80\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"160\" cy=\"24\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"80\" cy=\"48\"></circle></g><text name=\"name\" y=\"29\" x=\"32\">重新定义客户级别</text></g><g id=\"*************\" shape=\"rectangle\" type=\"endEvent\" x=\"539\" y=\"455\" width=\"60\" height=\"60\" transform=\"translate(539,455)\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#e67373\" stroke-width=\"3\" color=\"#e67373\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#e67373&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"30\"></circle><circle r=\"5\" name=\"top\" cx=\"30\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"60\" cy=\"30\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"30\" cy=\"60\"></circle></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#e67373\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text name=\"name\" y=\"35\" fill=\"#e67373\" x=\"18\">结束</text></g><g id=\"*************\" shape=\"rectangle\" type=\"endEvent\" x=\"729\" y=\"451\" width=\"60\" height=\"60\" transform=\"translate(729,451)\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#e67373\" stroke-width=\"3\" color=\"#e67373\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#e67373&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"30\"></circle><circle r=\"5\" name=\"top\" cx=\"30\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"60\" cy=\"30\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"30\" cy=\"60\"></circle></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#e67373\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text name=\"name\" y=\"35\" fill=\"#e67373\" x=\"18\">结束</text></g><g id=\"*************\" shape=\"rectangle\" type=\"endEvent\" x=\"355\" y=\"458\" width=\"60\" height=\"60\" transform=\"translate(355,458)\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#e67373\" stroke-width=\"3\" color=\"#e67373\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#e67373&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"30\"></circle><circle r=\"5\" name=\"top\" cx=\"30\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"60\" cy=\"30\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"30\" cy=\"60\"></circle></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#e67373\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text name=\"name\" y=\"35\" fill=\"#e67373\" x=\"18\">结束</text></g><g id=\"*************\" type=\"userTask\" shape=\"diamond\" x=\"527\" transform=\"translate(567,238)\" y=\"238\" width=\"80\" height=\"80\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"80\" height=\"80\" rx=\"5\" ry=\"5\" status=\"<g transform=&quot;translate(20, 20)&quot;><path d=&quot;M0,0 L20,20 L19.9418747,2.99420208 C19.9362226,1.34054993 18.5929201,0 16.9260921,0 L0,0 Z&quot; id=&quot;Path-5&quot; fill=&quot;#BFD1F5&quot;></path> <path d=&quot;M12.2426407,2 L13.6568542,3.41421356 L9.41421356,7.65685425 L8,6.24264069 L12.2426407,2 Z M14.363961,4.12132034 L15.7781746,5.53553391 L11.5355339,9.77817459 L10.1213203,8.36396103 L14.363961,4.12132034 Z M16.4852814,6.24264069 L17.8994949,7.65685425 L13.6568542,11.8994949 L12.2426407,10.4852814 L16.4852814,6.24264069 Z&quot; id=&quot;Combined-Shape&quot; fill=&quot;#708FCC&quot;></path></g>\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" transform=\"translate(-50,7)\" class=\"hide\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"50\"></circle><circle r=\"5\" name=\"top\" cx=\"50\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"100\" cy=\"50\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"50\" cy=\"100\"></circle></g><text name=\"name\" y=\"60\" x=\"-12\">会签</text><g name=\"status\"><g transform=\"translate(20, 20)\"><path d=\"M0,0 L20,20 L19.9418747,2.99420208 C19.9362226,1.34054993 18.5929201,0 16.9260921,0 L0,0 Z\" id=\"Path-5\" fill=\"#BFD1F5\"></path> <path d=\"M12.2426407,2 L13.6568542,3.41421356 L9.41421356,7.65685425 L8,6.24264069 L12.2426407,2 Z M14.363961,4.12132034 L15.7781746,5.53553391 L11.5355339,9.77817459 L10.1213203,8.36396103 L14.363961,4.12132034 Z M16.4852814,6.24264069 L17.8994949,7.65685425 L13.6568542,11.8994949 L12.2426407,10.4852814 L16.4852814,6.24264069 Z\" id=\"Combined-Shape\" fill=\"#708FCC\"></path></g></g></g><g id=\"*************\" type=\"exclusiveGateway\" shape=\"diamond\" x=\"345\" transform=\"translate(385,235)\" y=\"235\" width=\"80\" height=\"80\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"80\" height=\"80\" rx=\"5\" ry=\"5\" icon=\"[object Object]\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" transform=\"translate(-50,7)\" class=\"hide\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><circle r=\"5\" name=\"left\" cx=\"0\" cy=\"50\"></circle><circle r=\"5\" name=\"top\" cx=\"50\" cy=\"0\"></circle><circle r=\"5\" name=\"right\" class=\"ew-resize\" cx=\"100\" cy=\"50\"></circle><circle r=\"5\" name=\"bottom\" class=\"ns-resize\" cx=\"50\" cy=\"100\"></circle></g><text name=\"name\" y=\"60\" x=\"-24\">分支节点</text></g></svg>"}}