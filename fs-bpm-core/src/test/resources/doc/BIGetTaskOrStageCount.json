{"id": "BI_5be52ae1688629c238818390", "isPreview": 0, "pageNumber": 1, "showMode": 0, "isView": 1, "globalFilter": null, "pageSize": 2000, "displayFields": [{"fieldID": "Bi_pre_bd7e29962a892b56231a2912b6763cf9", "dbObjName": "BpmTask", "fieldName": "流程apiName", "dbFieldName": "sourceWorkflowId", "fieldLocation": 5, "fieldType": "String", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "isCalc": 0, "aggrType": "0", "columnName": "bpmtask_sourceworkflowid", "fixed": 0, "groupSeq": 1, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 0, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "text", "operateMenu": ["order", "filter", "group"]}, {"fieldID": "Bi_pre_dad0c90249015120b944eae2fb394313", "dbObjName": "BpmTask", "fieldName": "当期阶段Id", "dbFieldName": "stageId", "fieldLocation": 11, "fieldType": "String", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "isCalc": 0, "aggrType": "0", "columnName": "bpmtask_stageid", "fixed": 0, "groupSeq": 2, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 1, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "text", "operateMenu": ["order", "filter", "group"]}, {"fieldID": "Bi_pre_9723b0ba4af75cefbb31c99919d8a420", "dbObjName": "BpmTask", "fieldName": "是否超时", "dbFieldName": "isTimeout", "fieldLocation": 15, "fieldType": "Boolean", "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "enumName": "Bi_pre_9723b0ba4af75cefbb31c99919d8a420", "isCalc": 0, "aggrType": "0", "columnName": "bpmtask_istimeout", "fixed": 0, "groupSeq": 0, "isGroup": 0, "isVisible": 1, "orderType": 0, "seq": 3, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_MultiSelect", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "true_or_false", "operateMenu": ["order", "filter", "group", "aggr"]}, {"fieldID": "Bi_pre_409d6d8cb7f4498da04fd223a444dd31", "dbObjName": "BpmTask", "fieldName": "任务状态", "dbFieldName": "state", "fieldLocation": 9, "fieldType": "String", "subFieldType": "SingleSelectEnum", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "enumName": "Bi_pre_409d6d8cb7f4498da04fd223a444dd31", "isCalc": 0, "aggrType": "0", "columnName": "bpmtask_state", "fixed": 0, "groupSeq": 3, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 2, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_MultiSelect", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "select_one", "operateMenu": ["filter", "group"]}, {"fieldID": "Bi_pre_b1672080db3f0dc01a760160ef84975a", "dbObjName": "BpmTask", "fieldName": "流程节点定义Id", "dbFieldName": "activityId", "fieldLocation": 6, "fieldType": "String", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "isCalc": 0, "aggrType": "0", "columnName": "bpmtask_activityid", "fixed": 0, "groupSeq": 4, "isGroup": 1, "isVisible": 1, "orderType": 0, "seq": 3, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_Input", "format": "String", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "text", "operateMenu": ["order", "filter", "group"]}, {"fieldID": "Bi_pre_2c5bcf7511fff1c660833df3f80cfd07", "dbObjName": "BpmTask", "fieldName": "创建时间", "dbFieldName": "create_time", "fieldLocation": -1, "fieldType": "Date", "subFieldType": "DateTime", "objId": "Bi_pre_6cce3ac0d82e0a98a17b5ade0b5c1cd5", "objName": "业务流程任务", "isKey": 0, "isShow": 1, "isIndex": 1, "formatStr": "yyyy-MM-dd HH:mm", "isCalc": 0, "aggrType": "1", "columnName": "bpmtask_create_time", "fixed": 0, "groupSeq": 0, "isGroup": 0, "isVisible": 1, "orderType": 0, "seq": 4, "isPerm": 1, "isPre": 0, "ui": {"type": "UI_Time", "group": "DateTime", "justLeafNodeSelect": 0}, "ei": 71567, "isSingle": 1, "type": "datetime", "operateMenu": ["order", "filter", "group", "aggr"]}], "refresh": 1}