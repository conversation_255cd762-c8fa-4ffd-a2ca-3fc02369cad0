{"id": "231691237454413824", "name": "客户成单流程cuiyongxu", "description": "包含客户报备,审核,成单,汇款等结点", "userId": "1317", "tenantId": "2", "appId": "BPM", "enabled": true, "entryType": "AccountObj", "entryTypeName": "客户", "rangeCircleIds": [1, 2], "rangeEmployeeIds": [1317, 2000, 1016], "workflow": {"activities": [{"id": "1", "name": "开始", "type": "startEvent"}, {"id": "2", "name": "客户报备", "type": "userTask", "canSkip": false, "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "update", "executionName": "更新客户详细地址", "actionCode": "", "actionName": "", "form": [[{"default": "默认值", "name": "detail_address", "value": {"expression": "activity_0##AccountObj##detail_address"}, "label": "客户详细地址", "type": "text", "readonly": false, "required": true}, {"default": "默认值", "name": "remark", "value": {"expression": "activity_0##AccountObj##remark"}, "label": "备注", "type": "long_text", "readonly": false, "required": true}, {"default": "默认值", "name": "account_level", "value": {"expression": "activity_0##AccountObj##account_level"}, "label": "客户级别", "type": "select_one", "options": [{"value": "1", "label": "重要客户"}, {"value": "2", "label": "普通客户"}, {"value": "3", "label": "一般客户"}], "readonly": false, "required": true}, {"default": "默认值", "name": "account_level", "value": {"expression": "activity_0##AccountObj##account_level"}, "label": "客户级别22", "type": "select_many", "options": [{"value": "6", "label": "一级代理"}, {"value": "7", "label": "二级代理"}, {"value": "8", "label": "三级代理"}], "readonly": false, "required": true}]]}}, {"id": "3", "name": "客户审核", "type": "userTask", "taskType": "one_pass", "canSkip": true, "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "approve", "executionName": "审批客户对象", "form": [[{"default": "默认值", "name": "result", "value": true, "label": "审批结果", "type": "boolean", "readonly": false, "required": true}]]}}, {"id": "4", "name": "审核是否通过", "type": "exclusiveGateway"}, {"id": "5", "name": "修改客户电话", "type": "userTask", "canSkip": true, "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "update", "executionName": "更新客户对象", "form": [[{"default": "默认值", "name": "tel", "value": {"expression": "activity_0##AccountObj##tel"}, "label": "电话", "type": "text", "readonly": false, "required": true}]]}}, {"id": "6", "name": "创建联系人", "type": "userTask", "canSkip": true, "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "ContactObj", "objectId": {"expression": "activity_0##AccountObj##_id"}, "entityName": "联系人", "executionType": "create", "executionName": "创建联系人对象", "form": [[{"default": "默认值", "name": "account_id", "value": {"expression": "activity_0##AccountObj##_id"}, "label": "客户", "type": "text", "hidden": true}, {"default": "默认值", "name": "department", "value": "", "label": "部门", "type": "text", "hidden": false}, {"default": "默认值", "name": "name", "value": {"expression": "activity_0##AccountObj##name"}, "label": "姓名", "type": "text", "hidden": false}, {"default": "默认值", "name": "email", "value": {"expression": "activity_0##AccountObj##email"}, "label": "邮箱", "type": "text", "hidden": false}]]}}, {"id": "7", "name": "拜访客户", "type": "userTask", "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionType": "approve", "executionName": "拜访客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "form": []}}, {"id": "8", "name": "洽谈报价", "type": "userTask", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionType": "update", "executionName": "洽谈报价", "objectId": {"expression": "activity_0##AccountObj##_id"}, "form": [[{"default": "默认值", "name": "total_refund_amount", "value": {"expression": "activity_0##AccountObj##total_refund_amount"}, "label": "报价金额", "type": "number", "hidden": true}]]}}, {"id": "9", "name": "报价审核", "type": "exclusiveGateway"}, {"id": "10", "name": "成单", "type": "userTask", "canSkip": true, "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "approve", "executionName": "成单审批", "form": [[{"default": "默认值", "name": "result", "value": true, "label": "审批结果", "type": "boolean", "readonly": false, "required": true}]]}}, {"id": "11", "name": "报价复审", "type": "userTask", "canSkip": true, "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "approve", "executionName": "成单审批", "form": [[{"default": "默认值", "name": "result", "value": true, "label": "审批结果", "type": "boolean", "readonly": false, "required": true}]]}}, {"id": "12", "name": "消息通知", "type": "userTask", "canSkip": true, "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "approve", "executionName": "成单审批", "form": []}}, {"id": "13", "name": "报价复审是否通过", "type": "exclusiveGateway"}, {"id": "14", "name": "是否成单", "type": "exclusiveGateway"}, {"id": "15", "name": "更换负责人", "type": "userTask", "canSkip": true, "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "changeowner", "executionName": "更换负责人", "form": []}}, {"id": "16", "name": "签单", "type": "userTask", "canSkip": true, "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionType": "approve", "executionName": "签单", "objectId": {"expression": "activity_0##AccountObj##_id"}, "form": []}}, {"id": "17", "name": "订单审核", "type": "userTask", "canSkip": true, "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj##_id"}, "executionType": "approve", "executionName": "订单审核", "form": [[{"default": "默认值", "name": "result", "value": true, "label": "审批结果", "type": "boolean", "readonly": false, "required": true}]]}}, {"id": "18", "name": "审核是否通过", "type": "exclusiveGateway"}, {"id": "19", "name": "订单交付,回款", "type": "userTask", "canSkip": true, "taskType": "one_pass", "assignee": {"person": ["1317", "1016"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionType": "approve", "executionName": "汇款", "objectId": {"expression": "activity_0##AccountObj##_id"}, "form": []}}, {"id": "20", "name": "结束", "type": "endEvent"}], "transitions": [{"id": "t1", "fromId": "1", "toId": "2"}, {"id": "t2", "fromId": "2", "toId": "3"}, {"id": "t3", "fromId": "3", "toId": "4"}, {"id": "t4", "fromId": "4", "toId": "5", "name": "通过", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_3##result"}, "right": {"value": true, "type": {"name": "boolean"}}}]}}, {"id": "t5", "fromId": "4", "toId": "2", "name": "驳回", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_3##result"}, "right": {"value": false, "type": {"name": "boolean"}}}]}}, {"id": "t6", "fromId": "5", "toId": "6"}, {"id": "t7", "fromId": "6", "toId": "7"}, {"id": "t8", "fromId": "7", "toId": "8"}, {"id": "t9", "fromId": "8", "toId": "9"}, {"id": "t10", "fromId": "9", "toId": "10", "condition": {"type": "and", "conditions": [{"type": "greaterThan", "left": {"expression": "activity_8##AccountObj##total_refund_amount"}, "right": {"value": 10000, "type": {"name": "number"}}}, {"type": "lessThan", "left": {"expression": "activity_8##AccountObj##total_refund_amount"}, "right": {"value": 100000, "type": {"name": "number"}}}]}}, {"id": "t11", "fromId": "9", "toId": "11", "condition": {"type": "and", "conditions": [{"type": "lessThan", "left": {"expression": "activity_8##AccountObj##total_refund_amount"}, "right": {"value": 10000, "type": {"name": "number"}}}]}}, {"id": "t12", "fromId": "11", "toId": "13"}, {"id": "t13", "fromId": "13", "toId": "10", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_11##result"}, "right": {"value": true, "type": {"name": "boolean"}}}]}}, {"id": "t14", "fromId": "13", "toId": "8", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_11##result"}, "right": {"value": false, "type": {"name": "boolean"}}}]}}, {"id": "t15", "fromId": "9", "toId": "12", "condition": {"type": "and", "conditions": [{"type": "greaterThan", "left": {"expression": "activity_8##AccountObj##total_refund_amount"}, "right": {"value": 100000, "type": {"name": "number"}}}]}}, {"id": "t16", "fromId": "12", "toId": "10"}, {"id": "t17", "fromId": "10", "toId": "14"}, {"id": "t18", "fromId": "14", "toId": "16", "name": "成单成功", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_10##result"}, "right": {"value": true, "type": {"name": "boolean"}}}]}}, {"id": "t19", "fromId": "14", "toId": "15", "name": "成单失败", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_10##result"}, "right": {"value": false, "type": {"name": "boolean"}}}]}}, {"id": "t20", "fromId": "15", "toId": "7"}, {"id": "t21", "fromId": "16", "toId": "17"}, {"id": "t22", "fromId": "17", "toId": "18"}, {"id": "t23", "fromId": "18", "toId": "19", "name": "通过", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_17##result"}, "right": {"value": true, "type": {"name": "boolean"}}}]}}, {"id": "t24", "fromId": "18", "toId": "16", "name": "驳回", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_17##result"}, "right": {"value": false, "type": {"name": "boolean"}}}]}}, {"id": "t25", "fromId": "19", "toId": "20"}], "variables": [{"id": "activity_0##AccountObj##_id", "type": {"name": "text"}}, {"id": "activity_8##AccountObj##total_refund_amount", "type": {"name": "number"}}, {"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_2##AccountObj", "type": {"name": "text"}}, {"id": "activity_3##result", "type": {"name": "boolean"}}, {"id": "activity_5##AccountObj", "type": {"name": "text"}}, {"id": "activity_6##ContactObj", "type": {"name": "text"}}, {"id": "activity_8##AccountObj", "type": {"name": "text"}}, {"id": "activity_10##result", "type": {"name": "boolean"}}, {"id": "activity_11##result", "type": {"name": "boolean"}}, {"id": "activity_17##result", "type": {"name": "boolean"}}]}, "extension": {"svg": "svg_context", "pools": [{"id": "pool_1", "name": "", "lanes": [{"id": "34", "name": "客户备案和审核阶段", "activities": ["1", "2", "3", "4"]}, {"id": "pool_2", "name": "销售阶段", "activities": ["5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15"]}, {"id": "pool_3", "name": "成单阶段", "activities": ["16", "17", "18", "19", "20"]}]}]}}