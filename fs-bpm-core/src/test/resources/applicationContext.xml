<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.facishare.bpm"/>

    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"/>

    <import resource="classpath:spring/fs-bpm-data.xml"/>
    <import resource="classpath:spring/fs-bpm-rest-proxy.xml"/>
    <import resource="classpath:spring/fs-bpm-fsi-proxy.xml"/>
    <import resource="classpath:spring/fs-bpm-aop.xml"/>
    <import resource="classpath:spring/fs-bpm-mq.xml"/>
    <import resource="classpath:spring/flow-public-db-config.xml"/>
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>
    <import resource="classpath*:spring/spring-pg.xml"/>
    <import resource="classpath:spring/license-client.xml"/>


</beans>
