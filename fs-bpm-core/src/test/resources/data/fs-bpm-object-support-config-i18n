{"objectBaseConfig": {"AccountObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "return": "paas.flow.bpm.config.action.return", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": ["OpportunityObj", "SalesOrderObj", "ContactObj", "ContractObj"]}, "LeadsObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember", "close": "paas.flow.bpm.config.action.close", "followup": "paas.flow.bpm.config.action.followup", "transform": "paas.flow.bpm.config.action.transform"}, "references": []}, "OpportunityObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": ["SalesOrderObj"]}, "SalesOrderObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember", "confirmreceive": "paas.flow.bpm.config.action.confirmreceive", "confirmdelivery": "paas.flow.bpm.config.action.confirmdelivery"}, "references": ["ReturnedGoodsInvoiceObj"]}, "ContractObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": []}, "ContactObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": []}, "ReturnedGoodsInvoiceObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": []}, "MarketingEventObj": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": ["LeadsObj"]}, "custom": {"actions": {"changeowner": "paas.flow.bpm.config.action.changeowner", "addteammember": "paas.flow.bpm.config.action.add.teammember"}, "references": [], "notSupportOldReferences": ["VisitingObj", "SalesOrderProductObj", "ReturnedGoodsInvoiceProductObj"]}}, "whiteList": ["WechatFanObj", "PaymentObj", "PaymentPlanObj", "GoodsReceivedNoteObj", "DeliveryNoteObj", "PromotionObj", "CasesObj", "OutboundDeliveryNoteObj", "PartnerObj", "RequisitionNoteObj", "DeviceObj", "CreditFileObj", "RebateUseRuleObj", "AdvertisementObj", "CheckRecordObj", "StatementObj", "NewOpportunityObj", "NewOpportunityLinesObj", "NewOpportunityContactsObj", "StockCheckNoteObj", "Quote<PERSON><PERSON><PERSON>"]}