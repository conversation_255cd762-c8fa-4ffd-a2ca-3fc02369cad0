[{"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689c99e787b86896a1a11", "tenant_id": "74186", "is_udef": true, "api_name": "MenuWorkbenchObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "CRM菜单项", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053788608, "create_time": 1527155145467, "store_table_name": "menu_workbench", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689ce9e787b86896a1a5a", "tenant_id": "74186", "is_udef": true, "api_name": "ReturnedGoodsInvoiceProductObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "退货单关联的产品", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791529, "create_time": 1527155150085, "store_table_name": "biz_returned_goods_invoice_product", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c07bc67e0f66e1a38e4a16e", "tenant_id": "74186", "is_udef": true, "api_name": "NewOpportunityObj", "created_by": "-1000", "last_modified_by": "-10000", "display_name": "商机2.0", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 2, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544011218641, "create_time": 1544010855901, "store_table_name": "new_opportunity", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5c187215319d19017e9b8f55", "describe_id": "5bffb059319d1987a7198f4e", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5c187215319d19017e9b8f54", "describe_id": "5bffb059319d1987a7198f4e", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5c187215319d19017e9b8f53", "describe_id": "5bffb059319d1987a7198f4e", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5c187215319d19017e9b8f52", "describe_id": "5bffb059319d1987a7198f4e", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5c187215319d19017e9b8f51", "describe_id": "5bffb059319d1987a7198f4e", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5c187215319d19017e9b8f50", "describe_id": "5bffb059319d1987a7198f4e", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5bffb059319d1987a7198f4e", "tenant_id": "74186", "is_udef": true, "api_name": "TravelReimbursedApproveObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "差旅报销", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 6, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1545105941067, "create_time": 1543483481691, "store_table_name": "travel_reimbursed_approve", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c07bc6ae0f66e1a38e4a1af", "tenant_id": "74186", "is_udef": true, "api_name": "Quote<PERSON><PERSON><PERSON>", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "报价单", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544010858292, "create_time": 1544010858292, "store_table_name": "quote", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c064b807cfed92bbfb78fe6", "tenant_id": "74186", "is_udef": true, "api_name": "object_O3cbA__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "杨倩从对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 2, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": 1543916448188, "create_time": 1543916416497, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689c99e787b86896a1a05", "tenant_id": "74186", "is_udef": null, "api_name": "MenuItemObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "CRM菜单项", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 5, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536570981332, "create_time": 1527155145305, "store_table_name": "menu_item", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c0618ad7cfed9602799fae2", "tenant_id": "74186", "is_udef": true, "api_name": "object_Eic04__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "主属性自增编号", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 3, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": 1543903643866, "create_time": 1543903405070, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c08abdc9e9c295bef525684", "tenant_id": "74186", "is_udef": true, "api_name": "SPUObj", "created_by": null, "last_modified_by": "-1000", "display_name": "商品", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544072156672, "create_time": 1544072156672, "store_table_name": "stand_prod_unit", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689d09e787b86896a1b20", "tenant_id": "74186", "is_udef": true, "api_name": "GoalRuleObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "目标规则配置", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053793005, "create_time": 1527155152633, "store_table_name": "goal_rule", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c079f0e7cfed92138527c05", "tenant_id": "74186", "is_udef": true, "api_name": "object_Mce8v__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "杨倩关联上对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 9, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": 1544428940113, "create_time": 1544003342466, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689d19e787b86896a1b52", "tenant_id": "74186", "is_udef": true, "api_name": "GoalRuleApplyCircleObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "目标规则适用部门", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053793255, "create_time": 1527155153228, "store_table_name": "goal_rule_apply_circle", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b7a8ab57cfed9492b5f7848", "tenant_id": "74186", "is_udef": true, "api_name": "object_X0819__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "自定义对象全字段", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 2, "release_version": "6.4", "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": *************, "create_time": *************, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689cc9e787b86896a1a41", "tenant_id": "74186", "is_udef": true, "api_name": "AccountAttObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "客户-附件", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": null, "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b8e521f319d19cd1ff34c21", "tenant_id": "74186", "is_udef": null, "api_name": "LeadsPoolObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "线索池", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": "biz_leads_pool", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5c26057e319d199e2b2764d7", "describe_id": "5b0689cf9e787b86896a1a8c", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5c26057e319d199e2b2764d6", "describe_id": "5b0689cf9e787b86896a1a8c", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5c26057e319d199e2b2764d5", "describe_id": "5b0689cf9e787b86896a1a8c", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5c26057e319d199e2b2764d4", "describe_id": "5b0689cf9e787b86896a1a8c", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5c26057e319d199e2b2764d3", "describe_id": "5b0689cf9e787b86896a1a8c", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5c26057e319d199e2b2764d2", "describe_id": "5b0689cf9e787b86896a1a8c", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689cf9e787b86896a1a8c", "tenant_id": "74186", "is_udef": true, "api_name": "PriceBookProductObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "价目表明细", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 4, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1545995646793, "create_time": 1527155151291, "store_table_name": "price_book_product", "module": null, "icon_index": 16, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c079f407cfed92138529180", "tenant_id": "74186", "is_udef": true, "api_name": "object_inHny__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "杨倩关联下对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 5, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": 1544003448382, "create_time": 1544003392954, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b8caf58319d19cd1ff32152", "tenant_id": "74186", "is_udef": null, "api_name": "CommonlyUsedMenuItem", "created_by": null, "last_modified_by": null, "display_name": "常用的对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1535946584998, "create_time": 1535946584998, "store_table_name": "common_used_menu_item", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5b8e5220319d19cd1ff34cb0", "describe_id": "5b0689cf9e787b86896a1ac4", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5b8e5220319d19cd1ff34caf", "describe_id": "5b0689cf9e787b86896a1ac4", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5b8e5220319d19cd1ff34cae", "describe_id": "5b0689cf9e787b86896a1ac4", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5b8e5220319d19cd1ff34cad", "describe_id": "5b0689cf9e787b86896a1ac4", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5b8e5220319d19cd1ff34cac", "describe_id": "5b0689cf9e787b86896a1ac4", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5b8e5220319d19cd1ff34cab", "describe_id": "5b0689cf9e787b86896a1ac4", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689cf9e787b86896a1ac4", "tenant_id": "74186", "is_udef": true, "api_name": "QuoteLinesObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "报价单明细", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053792502, "create_time": 1527155151798, "store_table_name": "quote_lines", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b697e1a7cfed9108f98d266", "tenant_id": "74186", "is_udef": true, "api_name": "ReturnedGoodsInvoiceObj", "created_by": null, "last_modified_by": null, "display_name": "退货单", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 5, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1543925427163, "create_time": 1533640218781, "store_table_name": "biz_returned_goods_invoice", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689c99e787b86896a19fb", "tenant_id": "74186", "is_udef": true, "api_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "CRM菜单", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053788356, "create_time": 1527155145045, "store_table_name": "menu", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c07bc6be0f66e1a38e4a1fb", "tenant_id": "74186", "is_udef": true, "api_name": "NewOpportunityContactsObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "商机联系人", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544010859526, "create_time": 1544010859526, "store_table_name": "new_opportunity_contacts", "module": null, "icon_index": null, "description": "商机联系人", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b95d2ab319d19cd1ff60bb3", "tenant_id": "74186", "is_udef": null, "api_name": "ScheduleObj", "created_by": null, "last_modified_by": null, "display_name": "日程", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "config": {"layout": {"add": 1, "assign": 1}, "cascade": {"add": 1}, "fields": {"add": 1}, "recorde_type": {"add": 1, "assign": 1}}, "last_modified_time": 1536545451658, "create_time": 1536545451658, "store_table_name": "schedule_data", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c07bc6ae0f66e1a38e4a1df", "tenant_id": "74186", "is_udef": true, "api_name": "NewOpportunityLinesObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "商机2.0明细", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544010858859, "create_time": 1544010858859, "store_table_name": "new_opportunity_Lines", "module": null, "icon_index": null, "description": "商机2.0明细对象", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c1b3665319d19a849edaa81", "tenant_id": "74186", "is_udef": true, "api_name": "SaleEventObj", "created_by": null, "last_modified_by": "-1000", "display_name": "销售记录", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 2, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": null, "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689cc9e787b86896a1a29", "tenant_id": "74186", "is_udef": true, "api_name": "AccountFinInfoObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "客户-财务信息", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": null, "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b7669c3319d19f01b6e999a", "tenant_id": "74186", "is_udef": null, "api_name": "stageRuntime", "created_by": null, "last_modified_by": null, "display_name": "阶段运行时", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "config": {"layout": {"add": 0}, "record_type": {"add": 0, "assign": 0}}, "last_modified_time": *************, "create_time": *************, "store_table_name": "stage_runtime", "module": null, "icon_index": null, "description": "阶段的运行时信息", "visible_scope": "bi"}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c0f34f77cfed99897d395d3", "tenant_id": "74186", "is_udef": true, "api_name": "object_tC6M2__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "自行车(INAG)", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 16, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": *************, "create_time": *************, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689cc9e787b86896a1a36", "tenant_id": "74186", "is_udef": true, "api_name": "AccountAddObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "客户地址", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": null, "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": null, "_id": "5bc488da319d190c29eeeb4a", "tenant_id": "74186", "is_udef": true, "api_name": "PaymentPlanObj", "created_by": null, "last_modified_by": "-1000", "display_name": "回款计划", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 2, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": "payment_plan", "module": null, "icon_index": null, "description": "回款计划", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689ce9e787b86896a1a5f", "tenant_id": "74186", "is_udef": true, "api_name": "SaleActionStageObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "销售阶段", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791602, "create_time": 1527155150175, "store_table_name": "sale_action_stage", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5bfd1f89319d195da0d9e5c7", "describe_id": "5c20479b319d19e720ba12d5", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5bfd1f89319d195da0d9e5c8", "describe_id": "5c20479b319d19e720ba12d5", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5bfd1f89319d195da0d9e5c6", "describe_id": "5c20479b319d19e720ba12d5", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5bfd1f89319d195da0d9e5c5", "describe_id": "5c20479b319d19e720ba12d5", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5bfd1f89319d195da0d9e5c4", "describe_id": "5c20479b319d19e720ba12d5", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5bfd1f89319d195da0d9e5c3", "describe_id": "5c20479b319d19e720ba12d5", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5c20479b319d19e720ba12d5", "tenant_id": "74186", "is_udef": null, "api_name": "AttachObj", "created_by": null, "last_modified_by": null, "display_name": "附件", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1545619355619, "create_time": 1545619355619, "store_table_name": "biz_obj_attach", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"NextStage": {"tenant_id": "-100", "action_class": "NextStageAction", "action_id": "5b8e521f319d19cd1ff34c34", "describe_id": "5b0689ce9e787b86896a1a5b", "action_code": "NextStage", "source_type": "java_spring", "label": "更换销售阶段"}, "ChangeSale": {"tenant_id": "-100", "action_class": "ChangeSaleAction", "action_id": "5b8e521f319d19cd1ff34c33", "describe_id": "5b0689ce9e787b86896a1a5b", "action_code": "ChangeSale", "source_type": "java_spring", "label": "更换销售流程"}}, "index_version": 1, "_id": "5b0689ce9e787b86896a1a5b", "tenant_id": "74186", "is_udef": true, "api_name": "SaleActionObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "销售流程", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791575, "create_time": 1527155150137, "store_table_name": "sale_action", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b697e1a7cfed9108f98d265", "tenant_id": "74186", "is_udef": true, "api_name": "SalesOrderObj", "created_by": null, "last_modified_by": null, "display_name": "销售订单", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 6, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544010863309, "create_time": 1533640218780, "store_table_name": "biz_sales_order", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5bd2c012319d19ffefa807ba", "tenant_id": "74186", "is_udef": true, "api_name": "SpecificationObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "规格", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 5, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "config": {"record_type": {"add": 0, "assign": 0}}, "last_modified_time": 1544855997996, "create_time": 1540538386812, "store_table_name": "specification", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5b8e521e319d19cd1ff34c07", "describe_id": "5b0689cc9e787b86896a1a35", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerAction", "action_id": "5b8e521e319d19cd1ff34c06", "describe_id": "5b0689cc9e787b86896a1a35", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5b8e521e319d19cd1ff34c05", "describe_id": "5b0689cc9e787b86896a1a35", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberAction", "action_id": "5b8e521e319d19cd1ff34c04", "describe_id": "5b0689cc9e787b86896a1a35", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689cc9e787b86896a1a35", "tenant_id": "74186", "is_udef": true, "api_name": "ContractObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "合同", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053790684, "create_time": 1527155148751, "store_table_name": "biz_contract", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689ce9e787b86896a1a79", "tenant_id": "74186", "is_udef": true, "api_name": "PriceBookObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "价目表", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053792156, "create_time": 1527155150933, "store_table_name": "price_book", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5b8e521f319d19cd1ff34c1b", "describe_id": "5b0689cd9e787b86896a1a49", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerAction", "action_id": "5b8e521f319d19cd1ff34c1a", "describe_id": "5b0689cd9e787b86896a1a49", "action_code": "ChangeOwner", "source_type": "java_spring"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5b8e521f319d19cd1ff34c19", "describe_id": "5b0689cd9e787b86896a1a49", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberAction", "action_id": "5b8e521f319d19cd1ff34c18", "describe_id": "5b0689cd9e787b86896a1a49", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689cd9e787b86896a1a49", "tenant_id": "74186", "is_udef": true, "api_name": "OpportunityObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "商机", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 4, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053790947, "create_time": 1527155149003, "store_table_name": "biz_opportunity", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5c3d7a06319d19f813a14924", "describe_id": "5c331fdb319d19272c8974a2", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5c3d7a06319d19f813a14923", "describe_id": "5c331fdb319d19272c8974a2", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5c3d7a06319d19f813a14922", "describe_id": "5c331fdb319d19272c8974a2", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5c3d7a06319d19f813a14921", "describe_id": "5c331fdb319d19272c8974a2", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5c3d7a06319d19f813a14920", "describe_id": "5c331fdb319d19272c8974a2", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5c3d7a06319d19f813a1491f", "describe_id": "5c331fdb319d19272c8974a2", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5c331fdb319d19272c8974a2", "tenant_id": "74186", "is_udef": true, "api_name": "ChargeApproveObj", "created_by": null, "last_modified_by": "-1000", "display_name": "报销费用", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1547532806460, "create_time": 1546854363158, "store_table_name": "charge_approve", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c0616b57cfed96027990f8a", "tenant_id": "74186", "is_udef": true, "api_name": "PaymentObj", "created_by": "-1000", "last_modified_by": "1000", "display_name": "回款", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": null, "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1543902901779, "create_time": 1543902901779, "store_table_name": "payment_customer", "module": null, "icon_index": null, "description": "回款", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689d19e787b86896a1b6b", "tenant_id": "74186", "is_udef": true, "api_name": "BpmTask", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "业务流程任务", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 5, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": "bpm_task", "module": null, "icon_index": null, "description": "业务流程任务", "visible_scope": "bi"}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c445200319d191f7e11dbbc", "tenant_id": "74186", "is_udef": true, "api_name": "AccountObj", "created_by": null, "last_modified_by": null, "display_name": "客户", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": "biz_account", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": null, "_id": "5c08bcf8319d19ab6cd33912", "tenant_id": "74186", "is_udef": true, "api_name": "SpuSkuSpecValueRelateObj", "created_by": null, "last_modified_by": null, "display_name": "商品产品规格值关联表", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": *************, "create_time": *************, "store_table_name": "spu_sku_spec_value_relate", "module": null, "icon_index": null, "description": "商品产品规格值关联表", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689c99e787b86896a1a1d", "tenant_id": "74186", "is_udef": true, "api_name": "RoleSourceObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "权限和资源的关联表", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053788785, "create_time": 1527155145631, "store_table_name": "role_source", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689cd9e787b86896a1a57", "tenant_id": "74186", "is_udef": true, "api_name": "HighSeasObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "公海", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 4, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791485, "create_time": 1527155149898, "store_table_name": "biz_highseas", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b9b2b6b319d1906c4095fe6", "tenant_id": "74186", "is_udef": null, "api_name": "ApprovalInstanceObj", "created_by": null, "last_modified_by": null, "display_name": "审批流实例", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536895851358, "create_time": 1536895851358, "store_table_name": "approval_instance", "module": null, "icon_index": null, "description": "审批流实例", "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5b8e521e319d19cd1ff34c03", "describe_id": "5b0689cc9e787b86896a1a34", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerAction", "action_id": "5b8e521e319d19cd1ff34c02", "describe_id": "5b0689cc9e787b86896a1a34", "action_code": "ChangeOwner", "source_type": "java_spring"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5b8e521e319d19cd1ff34c01", "describe_id": "5b0689cc9e787b86896a1a34", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}}, "index_version": 1, "_id": "5b0689cc9e787b86896a1a34", "tenant_id": "74186", "is_udef": true, "api_name": "InvoiceApplicationObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "开票申请", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053790527, "create_time": 1527155148586, "store_table_name": "biz_invoice_application", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5c18b40f319d19c02843b630", "describe_id": "5c0a1f26319d19a6e2164624", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5c18b40f319d19c02843b62f", "describe_id": "5c0a1f26319d19a6e2164624", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5c18b40f319d19c02843b62e", "describe_id": "5c0a1f26319d19a6e2164624", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5c18b40f319d19c02843b62d", "describe_id": "5c0a1f26319d19a6e2164624", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5c18b40f319d19c02843b62c", "describe_id": "5c0a1f26319d19a6e2164624", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5c18b40f319d19c02843b62b", "describe_id": "5c0a1f26319d19a6e2164624", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5c0a1f26319d19a6e2164624", "tenant_id": "74186", "is_udef": true, "api_name": "AIRefObj", "created_by": null, "last_modified_by": "-1000", "display_name": "AI从对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 2, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1545122830743, "create_time": 1544167206155, "store_table_name": "ai_ref_data", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689d19e787b86896a1b4d", "tenant_id": "74186", "is_udef": true, "api_name": "GoalRuleDetailObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "子目标规则配置", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053793229, "create_time": 1527155153192, "store_table_name": "goal_rule_detail", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c05ead07cfed96027985959", "tenant_id": "74186", "is_udef": true, "api_name": "object_g6tue__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "测试字段", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 1, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": 1543891664451, "create_time": 1543891664451, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5b8e5220319d19cd1ff34c4c", "describe_id": "5b0689ce9e787b86896a1a72", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5b8e5220319d19cd1ff34c4b", "describe_id": "5b0689ce9e787b86896a1a72", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5b8e5220319d19cd1ff34c4a", "describe_id": "5b0689ce9e787b86896a1a72", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5b8e5220319d19cd1ff34c49", "describe_id": "5b0689ce9e787b86896a1a72", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5b8e5220319d19cd1ff34c48", "describe_id": "5b0689ce9e787b86896a1a72", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5b8e5220319d19cd1ff34c47", "describe_id": "5b0689ce9e787b86896a1a72", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": null, "_id": "5b0689ce9e787b86896a1a72", "tenant_id": "74186", "is_udef": true, "api_name": "OrderPaymentObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "回款明细", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 4, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053792032, "create_time": 1527155150836, "store_table_name": "payment_order", "module": null, "icon_index": null, "description": "回款明细", "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5b8f4d3e319d19cd1ff352cf", "describe_id": "5b0689d19e787b86896a1bbb", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5b8f4d3e319d19cd1ff352ce", "describe_id": "5b0689d19e787b86896a1bbb", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5b8f4d3e319d19cd1ff352cd", "describe_id": "5b0689d19e787b86896a1bbb", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5b8f4d3e319d19cd1ff352cc", "describe_id": "5b0689d19e787b86896a1bbb", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5b8f4d3e319d19cd1ff352cb", "describe_id": "5b0689d19e787b86896a1bbb", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5b8f4d3e319d19cd1ff352ca", "describe_id": "5b0689d19e787b86896a1bbb", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689d19e787b86896a1bbb", "tenant_id": "74186", "is_udef": true, "api_name": "CheckinsImgObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "外勤图片", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 5, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536118078584, "create_time": 1527155153643, "store_table_name": "checkins_img_obj", "module": null, "icon_index": 0, "description": "外勤图片", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5bebbf3efd992c7551c20fa7", "tenant_id": "74186", "is_udef": true, "api_name": "SpecificationValueObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "规格值", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 2, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "config": {"record_type": {"add": 0, "assign": 0}}, "last_modified_time": 1544855955419, "create_time": 1542176574986, "store_table_name": "specification_value", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5b8e521f319d19cd1ff34c1e", "describe_id": "5b0689cd9e787b86896a1a4a", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerAction", "action_id": "5b8e521f319d19cd1ff34c1d", "describe_id": "5b0689cd9e787b86896a1a4a", "action_code": "ChangeOwner", "source_type": "java_spring"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5b8e521f319d19cd1ff34c1c", "describe_id": "5b0689cd9e787b86896a1a4a", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}}, "index_version": 1, "_id": "5b0689cd9e787b86896a1a4a", "tenant_id": "74186", "is_udef": true, "api_name": "RefundObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "退款", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791108, "create_time": 1527155149252, "store_table_name": "biz_refund", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689d19e787b86896a1b56", "tenant_id": "74186", "is_udef": true, "api_name": "BpmInstance", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "业务流程", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 4, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1541406696459, "create_time": 1527155153257, "store_table_name": "bpm_instance", "module": null, "icon_index": null, "description": "业务流程", "visible_scope": "bi"}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689c89e787b86896a19d9", "tenant_id": "74186", "is_udef": true, "api_name": "PersonnelObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "人员", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 4, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "config": {"layout": {"add": 0}, "record_type": {"add": 0, "assign": 0}}, "last_modified_time": 1542613251668, "create_time": 1527155144118, "store_table_name": "org_employee_user", "module": null, "icon_index": null, "description": "描述企业的员工基本信息", "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5c0a1e59319d19a6e2164623", "describe_id": "5c0a1e59319d19a6e216460f", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5c0a1e59319d19a6e2164622", "describe_id": "5c0a1e59319d19a6e216460f", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5c0a1e59319d19a6e2164621", "describe_id": "5c0a1e59319d19a6e216460f", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5c0a1e59319d19a6e2164620", "describe_id": "5c0a1e59319d19a6e216460f", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5c0a1e59319d19a6e216461f", "describe_id": "5c0a1e59319d19a6e216460f", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5c0a1e59319d19a6e216461e", "describe_id": "5c0a1e59319d19a6e216460f", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5c0a1e59319d19a6e216460f", "tenant_id": "74186", "is_udef": null, "api_name": "AIMain<PERSON>bj", "created_by": null, "last_modified_by": null, "display_name": "AI主对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544167001086, "create_time": 1544167001086, "store_table_name": "ai_main_data", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5b8f4d0a319d19cd1ff35211", "describe_id": "5b0689d19e787b86896a1b85", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5b8f4d0a319d19cd1ff35210", "describe_id": "5b0689d19e787b86896a1b85", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5b8f4d0a319d19cd1ff3520f", "describe_id": "5b0689d19e787b86896a1b85", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5b8f4d0a319d19cd1ff3520e", "describe_id": "5b0689d19e787b86896a1b85", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5b8f4d0a319d19cd1ff3520d", "describe_id": "5b0689d19e787b86896a1b85", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5b8f4d0a319d19cd1ff3520c", "describe_id": "5b0689d19e787b86896a1b85", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689d19e787b86896a1b85", "tenant_id": "74186", "is_udef": true, "api_name": "CheckinsObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "高级外勤对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 11, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536118025926, "create_time": 1527155153466, "store_table_name": "checkins_data", "module": null, "icon_index": 0, "description": "高级考勤", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5bff8d6f7cfed943d429b0b3", "tenant_id": "74186", "is_udef": true, "api_name": "ProductObj", "created_by": null, "last_modified_by": "-1000", "display_name": "产品", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1543474543245, "create_time": 1543474543245, "store_table_name": "biz_product", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689cb9e787b86896a1a25", "tenant_id": "74186", "is_udef": true, "api_name": "SalesOrderProductObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "订单产品", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 9, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053789430, "create_time": 1527155147062, "store_table_name": "biz_sales_order_product", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5b8e521f319d19cd1ff34c20", "describe_id": "5b0689cd9e787b86896a1a4b", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5b8e521f319d19cd1ff34c1f", "describe_id": "5b0689cd9e787b86896a1a4b", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}}, "index_version": 1, "_id": "5b0689cd9e787b86896a1a4b", "tenant_id": "74186", "is_udef": true, "api_name": "VisitingObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "拜访", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791196, "create_time": 1527155149401, "store_table_name": "visit", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c07bc68e0f66e1a38e4a18a", "tenant_id": "74186", "is_udef": true, "api_name": "LeadsObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "销售线索", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544010856864, "create_time": 1544010856864, "store_table_name": "sales_clue", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerAction", "action_id": "5b8e521f319d19cd1ff34c30", "describe_id": "5b0689cd9e787b86896a1a56", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5b8e521f319d19cd1ff34c2f", "describe_id": "5b0689cd9e787b86896a1a56", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5b8e521f319d19cd1ff34c2e", "describe_id": "5b0689cd9e787b86896a1a56", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}}, "index_version": 1, "_id": "5b0689cd9e787b86896a1a56", "tenant_id": "74186", "is_udef": true, "api_name": "MarketingEventObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "市场活动", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053791428, "create_time": 1527155149771, "store_table_name": "biz_marketing_event", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockCustomAction", "action_id": "5c19b157319d19087702b49f", "describe_id": "5c19b144319d19087702b457", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerCustomAction", "action_id": "5c19b157319d19087702b49e", "describe_id": "5c19b144319d19087702b457", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "EditTeamMember": {"tenant_id": "-100", "action_class": "EditTeamMemberCustomAction", "action_id": "5c19b157319d19087702b49d", "describe_id": "5c19b144319d19087702b457", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockCustomAction", "action_id": "5c19b157319d19087702b49c", "describe_id": "5c19b144319d19087702b457", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "DeleteTeamMember": {"tenant_id": "-100", "action_class": "DeleteTeamMemberCustomAction", "action_id": "5c19b157319d19087702b49b", "describe_id": "5c19b144319d19087702b457", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberCustomAction", "action_id": "5c19b157319d19087702b49a", "describe_id": "5c19b144319d19087702b457", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5c19b144319d19087702b457", "tenant_id": "74186", "is_udef": true, "api_name": "ReimbursedApproveObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "普通报销", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1545187670936, "create_time": 1545187652815, "store_table_name": "reimbursed_approve", "module": null, "icon_index": 0, "description": null, "visible_scope": null}, {"fields": {}, "actions": {"Lock": {"tenant_id": "-100", "action_class": "LockAction", "action_id": "5c0a44ad319d19a6e2164d2e", "describe_id": "5b0689cb9e787b86896a1a27", "action_code": "Lock", "source_type": "java_spring", "label": "加锁"}, "ChangeOwner": {"tenant_id": "-100", "action_class": "ChangeOwnerAction", "action_id": "5c0a44ad319d19a6e2164d2d", "describe_id": "5b0689cb9e787b86896a1a27", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "Unlock": {"tenant_id": "-100", "action_class": "UnlockAction", "action_id": "5c0a44ad319d19a6e2164d2c", "describe_id": "5b0689cb9e787b86896a1a27", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "AddTeamMember": {"tenant_id": "-100", "action_class": "AddTeamMemberAction", "action_id": "5c0a44ad319d19a6e2164d2b", "describe_id": "5b0689cb9e787b86896a1a27", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}}, "index_version": 1, "_id": "5b0689cb9e787b86896a1a27", "tenant_id": "74186", "is_udef": true, "api_name": "ContactObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "联系人", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 6, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1544176813284, "create_time": 1527155147851, "store_table_name": "biz_contact", "module": null, "icon_index": null, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b0689d09e787b86896a1b2f", "tenant_id": "74186", "is_udef": true, "api_name": "GoalValueObj", "created_by": "-1000", "last_modified_by": "-1000", "display_name": "目标", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 8, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536053793149, "create_time": 1527155152796, "store_table_name": "goal_value", "module": null, "icon_index": 12, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b8fa9b9319d19cd1ff4c311", "tenant_id": "74186", "is_udef": true, "api_name": "CasesObj", "created_by": null, "last_modified_by": "-1000", "display_name": "工单", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 3, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536141861642, "create_time": 1536141753226, "store_table_name": "cases", "module": null, "icon_index": 13, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c3f1d47319d19f3fb498caa", "tenant_id": "74186", "is_udef": null, "api_name": "ObjectFollowDealSettingObj", "created_by": null, "last_modified_by": null, "display_name": "对象跟进成交动作设置", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1547640135443, "create_time": 1547640135443, "store_table_name": "object_follow_deal_setting", "module": null, "icon_index": 14, "description": null, "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5b9b2b79319d1906c4095ffc", "tenant_id": "74186", "is_udef": null, "api_name": "ApprovalTaskObj", "created_by": null, "last_modified_by": null, "display_name": "审批流程任务", "package": "CRM", "record_type": null, "is_active": true, "icon_path": null, "version": 1, "release_version": "6.4", "plural_name": null, "define_type": "package", "is_deleted": false, "last_modified_time": 1536895865612, "create_time": 1536895865612, "store_table_name": "approval_task", "module": null, "icon_index": null, "description": "审批流程任务", "visible_scope": null}, {"fields": {}, "actions": {}, "index_version": 1, "_id": "5c064b6c7cfed92bbfb781c4", "tenant_id": "74186", "is_udef": true, "api_name": "object_6409c__c", "created_by": "1000", "last_modified_by": "1000", "display_name": "杨倩主对象", "package": "CRM", "record_type": null, "is_active": true, "icon_path": "A_201705_11_e4e168dc5ce84c2c84abe7767827bbf0.png", "version": 6, "release_version": null, "plural_name": null, "define_type": "custom", "is_deleted": false, "config": {"button": {"add": 1}, "layout": {"add": 1, "assign": 1}, "layout_rule": {"add": 1}, "edit": 1, "cascade": {"add": 1}, "rule": {"add": 1}, "fields": {"add": 1}, "record_type": {"add": 1, "assign": 1}}, "last_modified_time": 1544788073393, "create_time": 1543916396458, "store_table_name": null, "module": null, "icon_index": 0, "description": null, "visible_scope": null}]