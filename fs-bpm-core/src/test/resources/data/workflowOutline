{"tenantId": "1", "userId": "4778", "sourceWorkflowId": "237351484530720768", "workflowId": "58dbea7bcc9da36e3ebf0db2", "name": "【模板】大客户备案会签", "count": 20, "enabled": true, "description": "【模板】大客户备案会签", "entryType": "AccountObj", "entryTypeName": "客户", "rangeEmployeeIds": [], "rangeCircleIds": [1381], "rangeGroupIds": [], "rangeRoleIds": [], "createdBy": "4778", "createTime": *************, "lastModifiedBy": "4778", "lastModifiedTime": *************, "workflow": {"type": "workflow_bpm", "name": "【模板】大客户备案会签", "description": "【模板】大客户备案会签", "activities": [{"type": "startEvent", "icon": "start-icon", "name": "开始", "description": "", "ruleId": "*************"}, {"type": "userTask", "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "actionCode": "0", "executionType": "update", "executionName": "编辑对象", "form": [[{"name": "account_level", "label": "客户级别", "type": "select_one", "required": false}, {"name": "account_type", "label": "客户类型", "type": "select_one", "required": false}, {"name": "name", "label": "客户名称", "type": "text", "required": true, "readonly": true}, {"name": "UDDate1__c", "label": "日期测试", "type": "date", "required": false}]]}, "remindLatency": 1, "icon": "approval-icon", "canSkip": false, "remind": true, "name": "客户分级", "description": "客户分级", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "客户级别判断", "description": "客户级别判断", "ruleId": "*************", "defaultTransitionId": "*************"}, {"type": "userTask", "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "actionCode": "0", "executionType": "approve", "executionName": "", "form": [[{"name": "result", "value": true, "label": "会签结果", "type": "text", "readonly": false, "required": true}]], "objectId": {"expression": "activity_0##AccountObj"}}, "name": "重要客户会签", "description": "重要客户会签", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "all"}, {"type": "userTask", "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "actionCode": "0", "executionType": "update", "executionName": "", "form": [[{"name": "account_level", "label": "客户级别", "type": "select_one", "required": true}]], "objectId": {"expression": "activity_0##AccountObj"}}, "name": "重新分级", "description": "重新分级", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "endEvent", "name": "结束", "description": "", "ruleId": "*************"}, {"type": "userTask", "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "actionCode": "changeowner", "executionType": "operation", "executionName": "", "form": [], "objectId": {"expression": "activity_0##AccountObj"}}, "remindLatency": 1, "remind": true, "name": "变更负责人", "description": "变更负责人", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "endEvent", "name": "结束", "description": "", "ruleId": "*************"}], "transitions": [{"ruleId": "*************", "fromId": "*************", "toId": "*************"}, {"ruleId": "*************", "fromId": "*************", "toId": "*************"}, {"description": "流程发起记录 / 客户级别 等于 1星  资料，无KP电话", "ruleId": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##AccountObj##account_level"}, "right": {"value": "7", "type": {"name": "text"}}}]}]}}, {"ruleId": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}}, {"ruleId": "*************", "fromId": "*************", "toId": "*************"}, {"ruleId": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}}, {"ruleId": "*************", "fromId": "*************", "toId": "*************"}, {"description": "流程发起记录 / 客户级别 等于 1星  资料，无KP电话", "ruleId": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##AccountObj##account_level"}, "right": {"value": "7", "type": {"name": "text"}}}]}]}}], "variables": [{"ruleId": "activity_0##AccountObj", "type": {"name": "text"}}, {"ruleId": "activity_*************##AccountObj", "type": {"name": "text"}}, {"ruleId": "activity_*************##result", "type": {"name": "text"}}, {"ruleId": "activity_*************##AccountObj", "type": {"name": "text"}}, {"ruleId": "activity_*************##AccountObj", "type": {"name": "text"}}, {"ruleId": "activity_0##AccountObj##account_level", "type": {"name": "text"}}], "ruleId": "58dbea7bcc9da36e3ebf0db2", "sourceWorkflowId": "237351484530720768"}, "svg": null, "extension": {"ruleId": "237354049632829440", "pools": [{"ruleId": null, "name": null, "lanes": [{"ruleId": "*************", "name": "客户分级", "description": "客户分级", "activities": ["*************", "*************", "*************"]}]}, {"ruleId": null, "name": null, "lanes": [{"ruleId": "*************", "name": "重要客户会签", "description": "重要客户会签", "activities": ["*************", "*************", "*************"]}]}, {"ruleId": null, "name": null, "lanes": [{"ruleId": "*************", "name": "变更负责人", "description": "变更负责人", "activities": ["*************", "*************"]}]}], "diagram": [{"ruleId": "*************", "attr": {"width": 220, "height": 540, "x": 646, "y": 67}}, {"ruleId": "*************", "attr": {"width": 220, "height": 540, "x": 309, "y": 66}}, {"ruleId": "*************", "attr": {"width": 220, "height": 540, "x": 40, "y": 60}}, {"ruleId": "*************", "attr": {"width": 60, "height": 60, "x": 120, "y": 130}}, {"ruleId": "*************", "attr": {"width": 160, "height": 50, "x": 70, "y": 260}}, {"ruleId": "*************", "attr": {"width": 100, "height": 100, "x": 150, "y": 365}}, {"ruleId": "*************", "attr": {"width": 100, "height": 100, "x": 445, "y": 129}}, {"ruleId": "*************", "attr": {"width": 160, "height": 50, "x": 365, "y": 318}}, {"ruleId": "*************", "attr": {"width": 60, "height": 60, "x": 422, "y": 442}}, {"ruleId": "*************", "attr": {"width": 160, "height": 50, "x": 682, "y": 310}}, {"ruleId": "*************", "attr": {"width": 60, "height": 60, "x": 732, "y": 441}}, {"ruleId": "*************", "attr": {"d": "M199.5,414.5 h91.5 a20,20 0 0 1 20,20 v17 a20,20 0 0 0 20,20 h91.5", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"ruleId": "*************", "attr": {"d": "M761.5 359.5 L762 441", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}, {"ruleId": "*************", "attr": {"d": "M494.5,178.5 h74 a20,20 0 0 1 20,20 v116 a20,20 0 0 0 20,20 h74", "toPosition": "left", "fromPosition": "right", "type": "polyline", "markerStart": "url(#approval-yes)"}}, {"ruleId": "*************", "attr": {"d": "M444.5 367.5 L452 442", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}, {"ruleId": "*************", "attr": {"d": "M444.5 228.5 L445 318", "toPosition": "top", "fromPosition": "bottom", "type": "line", "markerStart": "url(#approval-no)"}}, {"ruleId": "*************", "attr": {"d": "M199.5,414.5 h78 a20,20 0 0 0 20,-20 v-196 a20,20 0 0 1 20,-20 h78", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"ruleId": "*************", "attr": {"d": "M149.5 309.5 L150 365", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}, {"ruleId": "*************", "attr": {"d": "M149 185.5 L149 260", "toPosition": "top", "fromPosition": "bottom", "type": "line"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"100%\" width=\"100%\" class=\"paas-bpm-canvas\" viewBox=\"0 0 916 657\" font-size-adjust=\"none\" font-style=\"normal\" font-variant=\"normal\" font-weight=\"normal\" line-heigth=\"normal\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><defs><marker ruleId=\"end-arrow\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker ruleId=\"end-arrow-colored\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker ruleId=\"approval-yes\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker ruleId=\"approval-no\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter ruleId=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"5\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"rgba(0,0,0,0.06)\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n        background-color: #f3f3f5;\n      }\n\n      g[type=pool] {\n        font-size: 13px;\n      }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0)\"><g ruleId=\"*************\" shape=\"rectangle\" type=\"pool\" x=\"646\" y=\"67\" width=\"220\" height=\"540\" transform=\"translate(646,67)\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"&lt;rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;&gt;&lt;/rect&gt;\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"215.5\" y=\"265.5\" fill=\"white\" rx=\"0\" ry=\"0\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"105.5\" y=\"535.5\" fill=\"white\" rx=\"0\" ry=\"0\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" alignment-baseline=\"baseline\" fill=\"#70757f\" x=\"9\" y=\"13\">3</text><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"30\" x=\"110\">变更负责人</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 545)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"pool\" x=\"309\" y=\"66\" width=\"220\" height=\"540\" transform=\"translate(309,66)\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"&lt;rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;&gt;&lt;/rect&gt;\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"215.5\" y=\"265.5\" fill=\"white\" rx=\"0\" ry=\"0\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"105.5\" y=\"535.5\" fill=\"white\" rx=\"0\" ry=\"0\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" alignment-baseline=\"baseline\" fill=\"#70757f\" x=\"9\" y=\"13\">2</text><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"30\" x=\"110\">重要客户会签</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 545)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"pool\" x=\"40\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(40,60)\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"&lt;rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;&gt;&lt;/rect&gt;\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"215.5\" y=\"265.5\" fill=\"white\" rx=\"0\" ry=\"0\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"105.5\" y=\"535.5\" fill=\"white\" rx=\"0\" ry=\"0\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" alignment-baseline=\"baseline\" fill=\"#70757f\" x=\"9\" y=\"13\">1</text><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"30\" x=\"110\">客户分级</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 545)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g name=\"line-wrapper\"><g ruleId=\"*************\"><path d=\"M199.5,414.5 h91.5 a20,20 0 0 1 20,20 v17 a20,20 0 0 0 20,20 h91.5\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M761.5 359.5 L762 441\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"line\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M494.5,178.5 h74 a20,20 0 0 1 20,20 v116 a20,20 0 0 0 20,20 h74\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-start=\"url(#approval-yes)\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M444.5 367.5 L452 442\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"line\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M444.5 228.5 L445 318\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"line\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-start=\"url(#approval-no)\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M199.5,414.5 h78 a20,20 0 0 0 20,-20 v-196 a20,20 0 0 1 20,-20 h78\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M149.5 309.5 L150 365\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"line\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g><g ruleId=\"*************\"><path d=\"M149 185.5 L149 260\" start-ruleId=\"*************\" end-ruleId=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"line\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow)\"></path></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"startEvent\" x=\"120\" y=\"130\" width=\"60\" height=\"60\" transform=\"translate(120,130)\" class=\"\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"55.5\" y=\"25.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"25.5\" y=\"55.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"35\" x=\"30\" fill=\"#70757f\">开始</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 65)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"userTask\" x=\"70\" y=\"260\" width=\"160\" height=\"50\" transform=\"translate(70,260)\" class=\"\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"155.5\" y=\"20.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"75.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"30\" x=\"80\">客户分级</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 55)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g ruleId=\"*************\" type=\"exclusiveGateway\" shape=\"diamond\" transform=\"translate(150,365)\" x=\"150\" y=\"365\" width=\"100\" height=\"100\" status=\"normal\" class=\"\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"70.71067811865474\" height=\"70.71067811865474\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" transform=\"translate(-50,0)\" stroke=\"#49bffc\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"45.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"95.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"45.5\" y=\"95.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text name=\"name\" text-anchor=\"middle\" alignment-baseline=\"baseline\" x=\"0\" y=\"55\">客户级别判断</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 105)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点配置错误</tspan></text></g></g><g ruleId=\"*************\" type=\"userTask\" shape=\"diamond\" transform=\"translate(445,129)\" x=\"445\" y=\"129\" width=\"100\" height=\"100\" class=\"\" status=\"normal\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"70.71067811865474\" height=\"70.71067811865474\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" transform=\"translate(-50,0)\" stroke=\"\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"45.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"95.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"45.5\" y=\"95.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text name=\"name\" text-anchor=\"middle\" alignment-baseline=\"baseline\" x=\"0\" y=\"55\">重要客户会签</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 105)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点必须在阶段内</tspan></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"userTask\" x=\"365\" y=\"318\" width=\"160\" height=\"50\" transform=\"translate(365,318)\" class=\"\" status=\"normal\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"155.5\" y=\"20.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"75.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"30\" x=\"80\">重新分级</text><g name=\"error-node\" class=\"hide\" transform=\"translate(80, 55)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点必须在阶段内</tspan></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"endEvent\" x=\"422\" y=\"442\" width=\"60\" height=\"60\" transform=\"translate(422,442)\" class=\"\" status=\"normal\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"&lt;rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;&gt;&lt;/rect&gt;\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"55.5\" y=\"25.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"25.5\" y=\"55.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"35\" x=\"30\" fill=\"#70757f\">结束</text><g name=\"error-node\" class=\"hide\" transform=\"translate(30, 65)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\">该节点必须在阶段内</tspan></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"userTask\" x=\"682\" y=\"310\" width=\"160\" height=\"50\" transform=\"translate(682,310)\" class=\"\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" filter=\"url(#box-shadow)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"155.5\" y=\"20.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"75.5\" y=\"45.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"30\" x=\"80\">变更负责人</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 55)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g ruleId=\"*************\" shape=\"rectangle\" type=\"endEvent\" x=\"732\" y=\"441\" width=\"60\" height=\"60\" transform=\"translate(732,441)\" class=\"\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"&lt;rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;&gt;&lt;/rect&gt;\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" class=\"paas-bpm-connect-point resize\" fill=\"white\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" x=\"55.5\" y=\"25.5\" fill=\"white\" rx=\"9\" ry=\"9\" class=\"ew-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" x=\"25.5\" y=\"55.5\" fill=\"white\" rx=\"9\" ry=\"9\" name=\"bottom\" class=\"ns-resize paas-bpm-connect-point\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text text-anchor=\"middle\" alignment-baseline=\"baseline\" name=\"name\" y=\"35\" x=\"30\" fill=\"#70757f\">结束</text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 65)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g></svg><div class=\"paas-bpm-designer-overlay hide\"></div>"}, "templateId": "237344896352845824", "hasInstance": true, "deleted": false, "new": false}