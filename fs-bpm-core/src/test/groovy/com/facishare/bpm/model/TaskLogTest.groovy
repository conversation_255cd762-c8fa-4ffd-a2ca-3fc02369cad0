package com.facishare.bpm.model

import com.facishare.bpm.model.paas.engine.approvalflow.CRMConstants
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.google.common.collect.Lists
import spock.lang.Specification

class TaskLogTest extends Specification {
    
    def "校验是否为有效的修改日志"() {
        given: "准备TaskLog和ApproverModifyLog数据"
        def taskLog = new TaskLog()
        def modifyLog = new Task.ApproverModifyLog()
        modifyLog.setUserId(userId)
        modifyLog.setBeforeModifyPersons(beforeModifyPersons)

        when: "调用isValidModifyLog方法"
        def result = taskLog.isValidModifyLog(modifyLog)

        then: "验证结果"
        result == expected

        where: "不同测试场景"
        userId              | beforeModifyPersons                     || expected
        "1002"              | Lists.newArrayList("1003")              || true
        CRMConstants.SYSTEM | Lists.newArrayList("1002")              || true
        CRMConstants.SYSTEM | Lists.newArrayList()                    || false
        CRMConstants.SYSTEM | null                                    || false
        CRMConstants.SYSTEM | Lists.newArrayList(CRMConstants.SYSTEM) || false
        "1002"              | Lists.newArrayList(CRMConstants.SYSTEM) || true   // 理论上不存在这种场景
    }
}
