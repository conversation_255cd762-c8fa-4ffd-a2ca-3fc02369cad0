package com.facishare.bpm.handler.task.button.model

import com.facishare.bpm.model.ActionButton
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper
import spock.lang.Specification

/**
 * Test for FormButtonResult
 */
class FormButtonResultTest extends Specification {

    def "test default constructor"() {
        when:
        def result = new FormButtonResult()

        then:
        result.getButtons() != null
        result.getButtons().isEmpty()
        result.getErrorMsg() == null
        result.getCustom() == false
        result.getCustomExtension() == null
        result.getPlugin() == null
    }

    def "test constructor with buttons"() {
        given: "list of action buttons"
        def buttons = [
            new ActionButton("save", "Save"),
            new ActionButton("cancel", "Cancel")
        ]

        when:
        def result = new FormButtonResult(buttons)

        then:
        result.getButtons().size() == 2
        result.getButtons()[0].getAction() == "save"
        result.getButtons()[0].getOrder() == 0
        result.getButtons()[1].getAction() == "cancel"
        result.getButtons()[1].getOrder() == 1
        result.getErrorMsg() == null
    }

    def "test constructor with buttons and error message"() {
        given: "list of action buttons and error message"
        def buttons = [
            new ActionButton("save", "Save")
        ]
        def errorMsg = "Test error message"

        when:
        def result = new FormButtonResult(buttons, errorMsg)

        then:
        result.getButtons().size() == 1
        result.getButtons()[0].getAction() == "save"
        result.getButtons()[0].getOrder() == 0
        result.getErrorMsg() == errorMsg
    }

    def "test constructor with error message only"() {
        given: "error message"
        def errorMsg = "Error occurred"

        when:
        def result = new FormButtonResult(errorMsg)

        then:
        result.getButtons().isEmpty()
        result.getErrorMsg() == errorMsg
    }

    def "test getButtons with null buttons"() {
        given: "form button result with null buttons"
        def result = new FormButtonResult()
        result.buttons = null

        when:
        def buttons = result.getButtons()

        then:
        buttons != null
        buttons.isEmpty()
    }

    def "test addButton"() {
        given: "form button result"
        def result = new FormButtonResult()

        when:
        result.addButton("approve", "Approve")
        result.addButton("reject", "Reject")

        then:
        result.getButtons().size() == 2
        result.getButtons()[0].getAction() == "approve"
        result.getButtons()[0].getLabel() == "Approve"
        result.getButtons()[0].getOrder() == 0
        result.getButtons()[1].getAction() == "reject"
        result.getButtons()[1].getLabel() == "Reject"
        result.getButtons()[1].getOrder() == 1
    }

    def "test setButtons with empty list"() {
        given: "form button result with existing buttons"
        def result = new FormButtonResult()
        result.addButton("existing", "Existing")
        def emptyButtons = []

        when:
        result.setButtons(emptyButtons)

        then:
        // setButtons with empty list doesn't clear existing buttons, just returns early
        result.getButtons().size() == 1
        result.getButtons()[0].getAction() == "existing"
    }

    def "test setButtons with null list"() {
        given: "form button result with existing buttons"
        def result = new FormButtonResult()
        result.addButton("existing", "Existing")

        when:
        result.setButtons(null)

        then:
        // setButtons with null list doesn't clear existing buttons, just returns early
        result.getButtons().size() == 1
        result.getButtons()[0].getAction() == "existing"
    }

    def "test setButtons with null buttons field"() {
        given: "form button result with null buttons field"
        def result = new FormButtonResult()
        result.buttons = null

        when:
        result.setButtons(null)

        then:
        result.getButtons().isEmpty()
    }

    def "test setButtons with valid buttons"() {
        given: "form button result and new buttons"
        def result = new FormButtonResult()
        def newButtons = [
            new ActionButton("update", "Update"),
            new ActionButton("delete", "Delete"),
            new ActionButton("view", "View")
        ]

        when:
        result.setButtons(newButtons)

        then:
        result.getButtons().size() == 3
        result.getButtons()[0].getOrder() == 0
        result.getButtons()[1].getOrder() == 1
        result.getButtons()[2].getOrder() == 2
    }

    def "test setButtons order assignment"() {
        given: "buttons with existing orders"
        def result = new FormButtonResult()
        def buttons = [
            new ActionButton("first", "First"),
            new ActionButton("second", "Second")
        ]
        // Set some initial orders
        buttons[0].setOrder(99)
        buttons[1].setOrder(88)

        when:
        result.setButtons(buttons)

        then:
        result.getButtons()[0].getOrder() == 0  // Should be reset to 0
        result.getButtons()[1].getOrder() == 1  // Should be reset to 1
    }

    def "test custom properties"() {
        given: "form button result"
        def result = new FormButtonResult()
        def customExtension = ["key": "value"]
        def plugin = Mock(FlowElementPluginWrapper)

        when:
        result.setCustom(true)
        result.setCustomExtension(customExtension)
        result.setPlugin(plugin)

        then:
        result.getCustom() == true
        result.getCustomExtension() == customExtension
        result.getPlugin() == plugin
    }

    def "test error message property"() {
        given: "form button result"
        def result = new FormButtonResult()
        def errorMsg = "Custom error message"

        when:
        result.setErrorMsg(errorMsg)

        then:
        result.getErrorMsg() == errorMsg
    }

    def "test buttons property modification"() {
        given: "form button result with buttons"
        def result = new FormButtonResult()
        def originalButtons = [
            new ActionButton("original", "Original")
        ]
        result.setButtons(originalButtons)

        when: "modify the buttons list directly"
        def buttons = result.getButtons()
        buttons.add(new ActionButton("added", "Added"))

        then: "the modification should be reflected"
        result.getButtons().size() == 2
        result.getButtons()[1].getAction() == "added"
    }
}
