package com.facishare.bpm.handler.task.detail.model

import com.facishare.bpm.handler.task.form.model.CustomerData
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.task.BPMTask
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Test for StandardData
 */
class StandardDataTest extends Specification {

    def "test initCustomerData"() {
        given: "customer data with various properties"
        def standardData = new StandardData()
        def customerData = new CustomerData()
        customerData.setData(["field1": "value1"])
        customerData.setDescribe(["describe1": "desc1"])
        customerData.setLayout(["layout1": "layout1"])
        customerData.setDescribeExt(["ext1": "ext1"])

        when:
        standardData.initCustomerData(customerData)

        then:
        standardData.getData() == ["field1": "value1"]
        standardData.getDescribe() == ["describe1": "desc1"]
        standardData.getLayout() == ["layout1": "layout1"]
        standardData.getDescribeExt() == ["ext1": "ext1"]
    }

    def "test initFlowLayoutData"() {
        given: "task with flow layout data"
        def standardData = new StandardData()
        def task = Mock(Task)
        task.getObjectFlowLayoutExists() >> true
        task.getLayoutType() >> "standard"
        task.getLayoutApiName() >> "StandardLayout"

        when:
        standardData.initFlowLayoutData(task)

        then:
        standardData.getObjectFlowLayoutExists() == true
        standardData.getLayoutType() == "standard"
        standardData.getLayoutApiName() == "StandardLayout"
    }

    def "test setObjectPermissions with valid permissions"() {
        given: "standard data and permissions map"
        def standardData = new StandardData()
        def permissions = [
            "edit": true,
            "view": true,
            "delete": false
        ]

        when:
        standardData.setObjectPermissions(permissions)

        then:
        standardData.getObjectPermissions() == permissions
        standardData.getObjectPermissions()["edit"] == true
        standardData.getObjectPermissions()["view"] == true
        standardData.getObjectPermissions()["delete"] == false
    }

    def "test setObjectPermissions with empty permissions"() {
        given: "standard data and empty permissions map"
        def standardData = new StandardData()
        def permissions = [:]

        when:
        standardData.setObjectPermissions(permissions)

        then:
        standardData.getObjectPermissions() == null
    }

    def "test setObjectPermissions with null permissions"() {
        given: "standard data and null permissions"
        def standardData = new StandardData()
        def permissions = null

        when:
        standardData.setObjectPermissions(permissions)

        then:
        standardData.getObjectPermissions() == null
    }

    def "test createStandardDataByExternalApplyTask"() {
        given: "BPM task with external apply data"
        def bpmTask = Mock(BPMTask)
        bpmTask.getAppCode() >> "TestApp"
        bpmTask.getActionCode() >> "TestAction"
        bpmTask.getEntityId() >> "TestEntity"
        bpmTask.getObjectId() >> "TestObject"
        bpmTask.getId() >> "TestTaskId"

        when:
        def result = StandardData.createStandardDataByExternalApplyTask(bpmTask)

        then:
        result.getAppCode() == "TestApp"
        result.getActionCode() == "TestAction"
        result.getEntityId() == "TestEntity"
        result.getObjectId() == "TestObject"
        result.getTaskId() == "TestTaskId"
    }

    def "test getOnlyRelatedObject with true value"() {
        given: "standard data with onlyRelatedObject set to true"
        def standardData = new StandardData()
        standardData.setOnlyRelatedObject(true)

        when:
        def result = standardData.getOnlyRelatedObject()

        then:
        result == true
    }

    def "test getOnlyRelatedObject with false value"() {
        given: "standard data with onlyRelatedObject set to false"
        def standardData = new StandardData()
        standardData.setOnlyRelatedObject(false)

        when:
        def result = standardData.getOnlyRelatedObject()

        then:
        result == false
    }

    def "test getOnlyRelatedObject with null value"() {
        given: "standard data with onlyRelatedObject set to null"
        def standardData = new StandardData()
        standardData.setOnlyRelatedObject(null)

        when:
        def result = standardData.getOnlyRelatedObject()

        then:
        result == false
    }

    def "test getHasPermissions with true value"() {
        given: "standard data with hasPermissions set to true"
        def standardData = new StandardData()
        standardData.setHasPermissions(true)

        when:
        def result = standardData.getHasPermissions()

        then:
        result == true
    }

    def "test getHasPermissions with false value"() {
        given: "standard data with hasPermissions set to false"
        def standardData = new StandardData()
        standardData.setHasPermissions(false)

        when:
        def result = standardData.getHasPermissions()

        then:
        result == false
    }

    def "test getHasPermissions with null value"() {
        given: "standard data with hasPermissions set to null"
        def standardData = new StandardData()
        standardData.setHasPermissions(null)

        when:
        def result = standardData.getHasPermissions()

        then:
        result == false
    }

    def "test getEnableLayoutRules with true value"() {
        given: "standard data with enableLayoutRules set to true"
        def standardData = new StandardData()
        standardData.setEnableLayoutRules(true)

        when:
        def result = standardData.getEnableLayoutRules()

        then:
        result == true
    }

    def "test getEnableLayoutRules with false value"() {
        given: "standard data with enableLayoutRules set to false"
        def standardData = new StandardData()
        standardData.setEnableLayoutRules(false)

        when:
        def result = standardData.getEnableLayoutRules()

        then:
        result == false
    }

    def "test getEnableLayoutRules with null value"() {
        given: "standard data with enableLayoutRules set to null"
        def standardData = new StandardData()
        standardData.setEnableLayoutRules(null)

        when:
        def result = standardData.getEnableLayoutRules()

        then:
        result == false
    }

    def "test createActionCodeAndLabels with normal action"() {
        given: "standard data with normal action code and label"
        def standardData = new StandardData()
        standardData.setActionCode("approve")
        standardData.setActionLabel("Approve")

        when:
        def result = standardData.createActionCodeAndLabels()

        then:
        result.size() == 1
        result["approve"] == "Approve"
    }

    def "test createActionCodeAndLabels with HandleThree action"() {
        given: "standard data with HandleThree action code"
        def standardData = new StandardData()
        standardData.setActionCode("HandleThree")
        standardData.setActionLabel("无效/进行中/转换")

        when:
        def result = standardData.createActionCodeAndLabels()

        then:
        result.size() == 3
        result[BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes[0]] == "无效"
        result[BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes[1]] == "进行中"
        result[BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes[2]] == "转换"
    }

    def "test getSequenceTaskCandidateIds with null list"() {
        given: "standard data with null sequence task candidate ids"
        def standardData = new StandardData()
        standardData.setSequenceTaskCandidateIds(null)

        when:
        def result = standardData.getSequenceTaskCandidateIds()

        then:
        result != null
        result.isEmpty()
    }

    def "test getSequenceTaskCandidateIds with existing list"() {
        given: "standard data with existing sequence task candidate ids"
        def standardData = new StandardData()
        def candidateIds = ["user1", "user2", "user3"]
        standardData.setSequenceTaskCandidateIds(candidateIds)

        when:
        def result = standardData.getSequenceTaskCandidateIds()

        then:
        result == candidateIds
        result.size() == 3
    }

    def "test createSignInInfoApiNameBySignInDescribe"() {
        given: "sign in assembly describe"
        def standardData = new StandardData()
        def signInDescribe = [
            (BPMConstants.MetadataKey.fields): [
                "sign_in_location_field": "signInLocation",
                "sign_in_time_field": "signInTime",
                "sign_out_location_field": "signOutLocation",
                "sign_out_time_field": "signOutTime"
            ]
        ]

        when:
        def result = standardData.createSignInInfoApiNameBySignInDescribe(signInDescribe)

        then:
        result.getSignInPlace() == "signInLocation"
        result.getSignInTime() == "signInTime"
        result.getSignOutPlace() == "signOutLocation"
        result.getSignOutTime() == "signOutTime"
    }

    def "test SignInInfoApiName getApiNameListByActionType for signin"() {
        given: "sign in info with api names"
        def standardData = new StandardData()
        def signInInfo = new StandardData.SignInInfoApiName(standardData)
        signInInfo.setSignInPlace("signInLocation")
        signInInfo.setSignInTime("signInTime")
        signInInfo.setSignOutPlace("signOutLocation")
        signInInfo.setSignOutTime("signOutTime")

        when:
        def result = signInInfo.getApiNameListByActionType(BPMConstants.OperationCodeType.signin)

        then:
        result.size() == 2
        result.contains("signInLocation")
        result.contains("signInTime")
    }

    def "test SignInInfoApiName getApiNameListByActionType for signout"() {
        given: "sign in info with api names"
        def standardData = new StandardData()
        def signInInfo = new StandardData.SignInInfoApiName(standardData)
        signInInfo.setSignInPlace("signInLocation")
        signInInfo.setSignInTime("signInTime")
        signInInfo.setSignOutPlace("signOutLocation")
        signInInfo.setSignOutTime("signOutTime")

        when:
        def result = signInInfo.getApiNameListByActionType(BPMConstants.OperationCodeType.signout)

        then:
        result.size() == 2
        result.contains("signOutLocation")
        result.contains("signOutTime")
    }

    def "test SignInInfoApiName getters and setters"() {
        given: "sign in info instance"
        def standardData = new StandardData()
        def signInInfo = new StandardData.SignInInfoApiName(standardData)

        when:
        signInInfo.setSignInPlace("testSignInPlace")
        signInInfo.setSignInTime("testSignInTime")
        signInInfo.setSignOutPlace("testSignOutPlace")
        signInInfo.setSignOutTime("testSignOutTime")

        then:
        signInInfo.getSignInPlace() == "testSignInPlace"
        signInInfo.getSignInTime() == "testSignInTime"
        signInInfo.getSignOutPlace() == "testSignOutPlace"
        signInInfo.getSignOutTime() == "testSignOutTime"
    }

    def "test SignInInfoApiName with null values"() {
        given: "sign in info with null values"
        def standardData = new StandardData()
        def signInInfo = new StandardData.SignInInfoApiName(standardData)

        when:
        def signinResult = signInInfo.getApiNameListByActionType(BPMConstants.OperationCodeType.signin)
        def signoutResult = signInInfo.getApiNameListByActionType(BPMConstants.OperationCodeType.signout)

        then:
        signinResult.size() == 2
        signinResult.contains(null)
        signoutResult.size() == 2
        signoutResult.contains(null)
    }


}
