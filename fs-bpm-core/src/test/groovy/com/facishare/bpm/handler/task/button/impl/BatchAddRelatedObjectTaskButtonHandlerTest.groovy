package com.facishare.bpm.handler.task.button.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import spock.lang.Specification

/**
 * Test for BatchAddRelatedObjectTaskButtonHandler
 */
class BatchAddRelatedObjectTaskButtonHandlerTest extends Specification {

    def batchAddRelatedObjectTaskButtonHandler = new BatchAddRelatedObjectTaskButtonHandler()
    def serviceManager = Mock(RefServiceManager)
    def standardData = Mock(StandardData)
    def taskParams = Mock(TaskParams)

    def "test getTaskType"() {
        when:
        def result = batchAddRelatedObjectTaskButtonHandler.getTaskType()

        then:
        result == ExecutionTypeEnum.batchAddRelatedObject
    }

    def "test setButtons basic functionality"() {
        given: "basic setup"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getButtons() != null
    }

    def "test setButtons with only related object"() {
        given: "only related object scenario"
        standardData.getOnlyRelatedObject() >> true
        standardData.getDefaultButtons() >> [:]

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getButtons() != null
    }

    def "test setButtons with assignNextTask true"() {
        given: "assignNextTask true"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, true, taskParams)

        then:
        result != null
        result.getButtons() != null
    }

    def "test setButtons with null service manager"() {
        given: "null service manager"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]
        standardData.getRelatedEntityId() >> "TestEntity"

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(null, standardData, false, taskParams)

        then:
        // Should throw NullPointerException for null service manager
        thrown(NullPointerException)
    }

    def "test setButtons with null standard data"() {
        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, null, false, taskParams)

        then:
        // Should throw NullPointerException for null standard data
        thrown(NullPointerException)
    }

    def "test setButtons with null task params"() {
        given: "null task params"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, null)

        then:
        result != null
        result.getButtons() != null
    }

    def "test setButtons with different default buttons"() {
        given: "different default buttons configurations"
        def standardData1 = Mock(StandardData)
        def standardData2 = Mock(StandardData)
        
        standardData1.getOnlyRelatedObject() >> false
        standardData1.getDefaultButtons() >> [:]
        
        standardData2.getOnlyRelatedObject() >> false
        standardData2.getDefaultButtons() >> [
            "save": "Save Button",
            "cancel": "Cancel Button"
        ]

        when:
        def result1 = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData1, false, taskParams)
        def result2 = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData2, false, taskParams)

        then:
        result1 != null
        result2 != null
        result1.getButtons() != null
        result2.getButtons() != null
    }

    def "test multiple calls consistency"() {
        given: "consistent setup"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result1 = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)
        def result2 = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result1 != null
        result2 != null
        result1.getButtons() != null
        result2.getButtons() != null
        // Results should be consistent in structure
    }

    def "test handler instance properties"() {
        expect:
        batchAddRelatedObjectTaskButtonHandler != null
        batchAddRelatedObjectTaskButtonHandler instanceof BatchAddRelatedObjectTaskButtonHandler
        batchAddRelatedObjectTaskButtonHandler.getTaskType() == ExecutionTypeEnum.batchAddRelatedObject
    }

    def "test setButtons with various boolean combinations"() {
        given: "various boolean combinations"
        standardData.getOnlyRelatedObject() >> onlyRelated
        standardData.getDefaultButtons() >> [:]

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, assignNext, taskParams)

        then:
        result != null
        result.getButtons() != null

        where:
        onlyRelated | assignNext
        true        | true
        true        | false
        false       | true
        false       | false
    }

    def "test setButtons error handling"() {
        given: "setup that might cause errors"
        standardData.getRelatedEntityId() >> { throw new RuntimeException("Test exception") }

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        thrown(RuntimeException)
    }

    def "test setButtons with complex default buttons"() {
        given: "complex default buttons"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [
            "button1": "Label 1",
            "button2": "Label 2",
            "button3": "Label 3",
            "button4": "Label 4"
        ]

        when:
        def result = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getButtons() != null
    }
}
