package com.facishare.bpm.handler.task.complete.impl

import com.facishare.bpm.manage.InstanceVariableManager
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.flow.mongo.bizdb.BizTaskDataDao
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification

/**
 * Test for BaseTaskCompleteHandler
 */
class BaseTaskCompleteHandlerTest extends Specification {

    def baseTaskCompleteHandler = new BaseTaskCompleteHandler()
    def instanceVariableManager = Mock(InstanceVariableManager)
    def paasWorkflowServiceProxy = Mock(PaasWorkflowServiceProxy)
    def bizTaskDataDao = Mock(BizTaskDataDao)

    def setup() {
        ReflectionTestUtils.setField(baseTaskCompleteHandler, "instanceVariableManager", instanceVariableManager)
        ReflectionTestUtils.setField(baseTaskCompleteHandler, "paasWorkflowServiceProxy", paasWorkflowServiceProxy)
        ReflectionTestUtils.setField(baseTaskCompleteHandler, "bizTaskDataDao", bizTaskDataDao)
    }

    def "test dependency injection"() {
        when:
        def injectedInstanceVariableManager = ReflectionTestUtils.getField(baseTaskCompleteHandler, "instanceVariableManager")
        def injectedPaasWorkflowServiceProxy = ReflectionTestUtils.getField(baseTaskCompleteHandler, "paasWorkflowServiceProxy")
        def injectedBizTaskDataDao = ReflectionTestUtils.getField(baseTaskCompleteHandler, "bizTaskDataDao")

        then:
        injectedInstanceVariableManager == instanceVariableManager
        injectedPaasWorkflowServiceProxy == paasWorkflowServiceProxy
        injectedBizTaskDataDao == bizTaskDataDao
    }

    def "test instanceVariableManager is accessible"() {
        when:
        def manager = ReflectionTestUtils.getField(baseTaskCompleteHandler, "instanceVariableManager")

        then:
        manager != null
        manager instanceof InstanceVariableManager
    }

    def "test paasWorkflowServiceProxy is accessible"() {
        when:
        def proxy = ReflectionTestUtils.getField(baseTaskCompleteHandler, "paasWorkflowServiceProxy")

        then:
        proxy != null
        proxy instanceof PaasWorkflowServiceProxy
    }

    def "test bizTaskDataDao is accessible"() {
        when:
        def dao = ReflectionTestUtils.getField(baseTaskCompleteHandler, "bizTaskDataDao")

        then:
        dao != null
        dao instanceof BizTaskDataDao
    }

    def "test handler instantiation"() {
        when:
        def handler = new BaseTaskCompleteHandler()

        then:
        handler != null
        handler instanceof BaseTaskCompleteHandler
    }

    def "test multiple instances are independent"() {
        given:
        def handler1 = new BaseTaskCompleteHandler()
        def handler2 = new BaseTaskCompleteHandler()
        def manager1 = Mock(InstanceVariableManager)
        def manager2 = Mock(InstanceVariableManager)

        when:
        ReflectionTestUtils.setField(handler1, "instanceVariableManager", manager1)
        ReflectionTestUtils.setField(handler2, "instanceVariableManager", manager2)

        then:
        ReflectionTestUtils.getField(handler1, "instanceVariableManager") == manager1
        ReflectionTestUtils.getField(handler2, "instanceVariableManager") == manager2
        ReflectionTestUtils.getField(handler1, "instanceVariableManager") != ReflectionTestUtils.getField(handler2, "instanceVariableManager")
    }

    def "test null dependency injection"() {
        given:
        def handler = new BaseTaskCompleteHandler()

        when:
        ReflectionTestUtils.setField(handler, "instanceVariableManager", null)
        ReflectionTestUtils.setField(handler, "paasWorkflowServiceProxy", null)
        ReflectionTestUtils.setField(handler, "bizTaskDataDao", null)

        then:
        ReflectionTestUtils.getField(handler, "instanceVariableManager") == null
        ReflectionTestUtils.getField(handler, "paasWorkflowServiceProxy") == null
        ReflectionTestUtils.getField(handler, "bizTaskDataDao") == null
    }
}
