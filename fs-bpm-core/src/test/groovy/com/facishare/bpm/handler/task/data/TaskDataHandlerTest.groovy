package com.facishare.bpm.handler.task.data

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.flow.mongo.bizdb.BizTaskDataDao
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification

/**
 * Test for TaskDataHandler abstract class
 */
class TaskDataHandlerTest extends Specification {

    def taskDataDao = Mock(BizTaskDataDao)
    def serviceManager = Mock(RefServiceManager)

    def "test abstract class instantiation through concrete implementation"() {
        given: "concrete implementation of TaskDataHandler"
        def handler = new TestTaskDataHandler()
        ReflectionTestUtils.setField(handler, "taskDataDao", taskDataDao)

        when:
        def injectedDao = ReflectionTestUtils.getField(handler, "taskDataDao")

        then:
        injectedDao == taskDataDao
        handler instanceof TaskDataHandler
    }

    def "test saveData method call"() {
        given: "concrete implementation"
        def handler = new TestTaskDataHandler()
        def taskId = "task123"
        def activityId = "activity456"
        def activityInstanceId = 789
        def data = "test data"

        when:
        handler.saveData(serviceManager, taskId, activityId, activityInstanceId, data)

        then:
        handler.saveDataCalled == true
        handler.lastSavedData == data
        handler.lastTaskId == taskId
        handler.lastActivityId == activityId
        handler.lastActivityInstanceId == activityInstanceId
    }

    def "test getData method call"() {
        given: "concrete implementation"
        def handler = new TestTaskDataHandler()
        def taskId = "task123"

        when:
        def result = handler.getData(serviceManager, taskId)

        then:
        handler.getDataCalled == true
        handler.lastGetTaskId == taskId
        result == "test result"
    }

    def "test getTaskType method call"() {
        given: "concrete implementation"
        def handler = new TestTaskDataHandler()

        when:
        def result = handler.getTaskType()

        then:
        result == ExecutionTypeEnum.update
    }

    def "test taskDataDao dependency injection"() {
        given: "handler with injected dao"
        def handler = new TestTaskDataHandler()
        def mockDao = Mock(BizTaskDataDao)

        when:
        ReflectionTestUtils.setField(handler, "taskDataDao", mockDao)
        def injectedDao = ReflectionTestUtils.getField(handler, "taskDataDao")

        then:
        injectedDao == mockDao
        injectedDao instanceof BizTaskDataDao
    }

    def "test multiple handlers independence"() {
        given: "multiple handler instances"
        def handler1 = new TestTaskDataHandler()
        def handler2 = new TestTaskDataHandler()
        def dao1 = Mock(BizTaskDataDao)
        def dao2 = Mock(BizTaskDataDao)

        when:
        ReflectionTestUtils.setField(handler1, "taskDataDao", dao1)
        ReflectionTestUtils.setField(handler2, "taskDataDao", dao2)

        then:
        ReflectionTestUtils.getField(handler1, "taskDataDao") == dao1
        ReflectionTestUtils.getField(handler2, "taskDataDao") == dao2
        ReflectionTestUtils.getField(handler1, "taskDataDao") != ReflectionTestUtils.getField(handler2, "taskDataDao")
    }

    def "test saveData with null parameters"() {
        given: "concrete implementation"
        def handler = new TestTaskDataHandler()

        when:
        handler.saveData(null, null, null, null, null)

        then:
        handler.saveDataCalled == true
        handler.lastSavedData == null
        handler.lastTaskId == null
        handler.lastActivityId == null
        handler.lastActivityInstanceId == null
    }

    def "test getData with null taskId"() {
        given: "concrete implementation"
        def handler = new TestTaskDataHandler()

        when:
        def result = handler.getData(serviceManager, null)

        then:
        handler.getDataCalled == true
        handler.lastGetTaskId == null
        result == "test result"
    }

    def "test saveData with different data types"() {
        given: "concrete implementation"
        def handler = new TestTaskDataHandler()

        when: "save different types of data"
        handler.saveData(serviceManager, "task1", "activity1", 1, "string data")
        handler.saveData(serviceManager, "task2", "activity2", 2, 12345)
        handler.saveData(serviceManager, "task3", "activity3", 3, ["key": "value"])

        then:
        handler.saveDataCalled == true
        // Last call should have map data
        handler.lastSavedData == ["key": "value"]
        handler.lastTaskId == "task3"
    }

    // Concrete test implementation of the abstract class
    static class TestTaskDataHandler extends TaskDataHandler<Object, String> {
        boolean saveDataCalled = false
        boolean getDataCalled = false
        Object lastSavedData
        String lastTaskId
        String lastActivityId
        Integer lastActivityInstanceId
        String lastGetTaskId

        @Override
        void saveData(RefServiceManager serviceManager, String taskId, String activityId, Integer activityInstanceId, Object data) {
            saveDataCalled = true
            lastSavedData = data
            lastTaskId = taskId
            lastActivityId = activityId
            lastActivityInstanceId = activityInstanceId
        }

        @Override
        String getData(RefServiceManager serviceManager, String taskId) {
            getDataCalled = true
            lastGetTaskId = taskId
            return "test result"
        }

        @Override
        ExecutionTypeEnum getTaskType() {
            return ExecutionTypeEnum.update
        }
    }
}
