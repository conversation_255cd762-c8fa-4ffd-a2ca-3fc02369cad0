package com.facishare.bpm.handler.task.form.model

import spock.lang.Specification

/**
 * Test for FormAssembleType enum
 */
class FormAssembleTypeTest extends Specification {

    def "test enum values"() {
        when:
        def values = FormAssembleType.values()

        then:
        values.length == 4
        values.contains(FormAssembleType.data)
        values.contains(FormAssembleType.describe)
        values.contains(FormAssembleType.layout)
        values.contains(FormAssembleType.describeExt)
    }

    def "test enum valueOf"() {
        when:
        def dataType = FormAssembleType.valueOf("data")
        def describeType = FormAssembleType.valueOf("describe")
        def layoutType = FormAssembleType.valueOf("layout")
        def describeExtType = FormAssembleType.valueOf("describeExt")

        then:
        dataType == FormAssembleType.data
        describeType == FormAssembleType.describe
        layoutType == FormAssembleType.layout
        describeExtType == FormAssembleType.describeExt
    }

    def "test enum name"() {
        expect:
        FormAssembleType.data.name() == "data"
        FormAssembleType.describe.name() == "describe"
        FormAssembleType.layout.name() == "layout"
        FormAssembleType.describeExt.name() == "describeExt"
    }

    def "test enum ordinal"() {
        expect:
        FormAssembleType.data.ordinal() == 0
        FormAssembleType.describe.ordinal() == 1
        FormAssembleType.layout.ordinal() == 2
        FormAssembleType.describeExt.ordinal() == 3
    }

    def "test enum toString"() {
        expect:
        FormAssembleType.data.toString() == "data"
        FormAssembleType.describe.toString() == "describe"
        FormAssembleType.layout.toString() == "layout"
        FormAssembleType.describeExt.toString() == "describeExt"
    }

    def "test enum equality"() {
        expect:
        FormAssembleType.data == FormAssembleType.data
        FormAssembleType.describe == FormAssembleType.describe
        FormAssembleType.layout == FormAssembleType.layout
        FormAssembleType.describeExt == FormAssembleType.describeExt
        
        FormAssembleType.data != FormAssembleType.describe
        FormAssembleType.layout != FormAssembleType.describeExt
    }

    def "test enum in switch statement"() {
        when:
        def result = processFormAssembleType(FormAssembleType.data)

        then:
        result == "Processing data"

        when:
        result = processFormAssembleType(FormAssembleType.describe)

        then:
        result == "Processing describe"

        when:
        result = processFormAssembleType(FormAssembleType.layout)

        then:
        result == "Processing layout"

        when:
        result = processFormAssembleType(FormAssembleType.describeExt)

        then:
        result == "Processing describeExt"
    }

    def "test enum in collections"() {
        given:
        def enumSet = EnumSet.allOf(FormAssembleType.class)
        def enumList = Arrays.asList(FormAssembleType.values())

        expect:
        enumSet.size() == 4
        enumSet.contains(FormAssembleType.data)
        enumSet.contains(FormAssembleType.describe)
        enumSet.contains(FormAssembleType.layout)
        enumSet.contains(FormAssembleType.describeExt)

        enumList.size() == 4
        enumList.indexOf(FormAssembleType.data) == 0
        enumList.indexOf(FormAssembleType.describe) == 1
        enumList.indexOf(FormAssembleType.layout) == 2
        enumList.indexOf(FormAssembleType.describeExt) == 3
    }

    def "test enum compareTo"() {
        expect:
        FormAssembleType.data.compareTo(FormAssembleType.describe) < 0
        FormAssembleType.describe.compareTo(FormAssembleType.layout) < 0
        FormAssembleType.layout.compareTo(FormAssembleType.describeExt) < 0
        FormAssembleType.describeExt.compareTo(FormAssembleType.data) > 0
        FormAssembleType.data.compareTo(FormAssembleType.data) == 0
    }

    def "test invalid valueOf"() {
        when:
        FormAssembleType.valueOf("invalid")

        then:
        thrown(IllegalArgumentException)
    }

    // Helper method for testing enum in switch
    private String processFormAssembleType(FormAssembleType type) {
        switch (type) {
            case FormAssembleType.data:
                return "Processing data"
            case FormAssembleType.describe:
                return "Processing describe"
            case FormAssembleType.layout:
                return "Processing layout"
            case FormAssembleType.describeExt:
                return "Processing describeExt"
            default:
                return "Unknown type"
        }
    }
}
