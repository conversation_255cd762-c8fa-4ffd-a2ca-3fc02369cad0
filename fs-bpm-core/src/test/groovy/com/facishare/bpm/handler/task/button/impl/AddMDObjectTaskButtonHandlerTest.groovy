package com.facishare.bpm.handler.task.button.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import spock.lang.Specification

/**
 * Test for AddMDObjectTaskButtonHandler
 */
class AddMDObjectTaskButtonHandlerTest extends Specification {

    def addMDObjectTaskButtonHandler = new AddMDObjectTaskButtonHandler()
    def serviceManager = Mock(RefServiceManager)
    def standardData = Mock(StandardData)
    def taskParams = Mock(TaskParams)

    def "test getTaskType"() {
        when:
        def result = addMDObjectTaskButtonHandler.getTaskType()

        then:
        result == ExecutionTypeEnum.addMDObject
    }

    def "test setButtons with inactive related entity"() {
        given: "standard data with inactive related entity"
        standardData.getRelatedEntityId() >> "TestEntity"
        serviceManager.findDescribe("TestEntity", false, false) >> [
            (BPMConstants.MetadataKey.isActive): false
        ]

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getErrorMsg() != null
        result.getButtons().isEmpty()
    }

    def "test setButtons with empty related field api name"() {
        given: "standard data with empty related field api name"
        standardData.getRelatedEntityId() >> "TestEntity"
        standardData.getRelatedFieldApiName() >> ""
        serviceManager.findDescribe("TestEntity", false, false) >> [
            (BPMConstants.MetadataKey.isActive): true
        ]

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getErrorMsg() != null
        result.getButtons().isEmpty()
    }

    def "test setButtons with null related field api name"() {
        given: "standard data with null related field api name"
        standardData.getRelatedEntityId() >> "TestEntity"
        standardData.getRelatedFieldApiName() >> null
        serviceManager.findDescribe("TestEntity", false, false) >> [
            (BPMConstants.MetadataKey.isActive): true
        ]

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getErrorMsg() != null
        result.getButtons().isEmpty()
    }

    def "test setButtons with null related entity describe"() {
        given: "standard data with null related entity describe"
        standardData.getRelatedEntityId() >> "TestEntity"
        serviceManager.findDescribe("TestEntity", false, false) >> null

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        // Should handle null describe gracefully
        result != null
    }

    def "test setButtons with missing isActive field"() {
        given: "standard data with describe missing isActive field"
        standardData.getRelatedEntityId() >> "TestEntity"
        serviceManager.findDescribe("TestEntity", false, false) >> [:]

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        // Should handle missing isActive field (defaults to false)
        result != null
        result.getErrorMsg() != null
        result.getButtons().isEmpty()
    }

    def "test setButtons with null service manager"() {
        given: "null service manager"
        standardData.getRelatedEntityId() >> "TestEntity"

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(null, standardData, false, taskParams)

        then:
        thrown(NullPointerException)
    }

    def "test setButtons with null standard data"() {
        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, null, false, taskParams)

        then:
        thrown(NullPointerException)
    }

    def "test setButtons with assignNextTask true"() {
        given: "standard data with inactive related entity and assignNextTask true"
        standardData.getRelatedEntityId() >> "TestEntity"
        serviceManager.findDescribe("TestEntity", false, false) >> [
            (BPMConstants.MetadataKey.isActive): false
        ]

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, true, taskParams)

        then:
        result != null
        result.getErrorMsg() != null
        result.getButtons().isEmpty()
    }

    def "test setButtons with null task params"() {
        given: "standard data with inactive related entity and null task params"
        standardData.getRelatedEntityId() >> "TestEntity"
        serviceManager.findDescribe("TestEntity", false, false) >> [
            (BPMConstants.MetadataKey.isActive): false
        ]

        when:
        def result = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, null)

        then:
        result != null
        result.getErrorMsg() != null
        result.getButtons().isEmpty()
    }

    def "test multiple calls consistency"() {
        given: "standard data with inactive related entity"
        standardData.getRelatedEntityId() >> "TestEntity"
        serviceManager.findDescribe("TestEntity", false, false) >> [
            (BPMConstants.MetadataKey.isActive): false
        ]

        when:
        def result1 = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)
        def result2 = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result1 != null
        result2 != null
        result1.getErrorMsg() == result2.getErrorMsg()
        result1.getButtons().size() == result2.getButtons().size()
    }
}
