package com.facishare.bpm.handler.task.button.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper
import com.facishare.bpm.handler.task.button.model.FormButtonResult
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.model.ActionButton
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.model.paas.engine.bpm.Task
import spock.lang.Specification

import java.lang.reflect.Field

/**
 * BpmUpdateTaskButtonHandler 单元测试
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
class BpmUpdateTaskButtonHandlerTest extends Specification {

    BpmUpdateTaskButtonHandler handler
    RefServiceManager serviceManager
    StandardData standardData
    TaskParams taskParams
    Task.UpdateFromProperty updateFromProperty

    def setup() {
        handler = new BpmUpdateTaskButtonHandler()
        serviceManager = Mock(RefServiceManager)
        standardData = Mock(StandardData)
        taskParams = Mock(TaskParams)
        updateFromProperty = Mock(Task.UpdateFromProperty)

        // 初始化TaskButtonConfigHelper的buttons字段
        initializeTaskButtonConfigHelper()
    }

    def initializeTaskButtonConfigHelper() {
        try {
            // 使用反射设置buttons字段
            Field buttonsField = TaskButtonConfigHelper.class.getDeclaredField("buttons")
            buttonsField.setAccessible(true)

            // 创建测试用的按钮配置
            Map<String, List<ActionButton>> testButtons = [:]
            testButtons.put(ExecutionTypeEnum.update.name(), [
                new ActionButton("save", "保存"),
                new ActionButton("complete", "完成")
            ])

            buttonsField.set(null, testButtons)
        } catch (Exception e) {
            // 如果反射失败，忽略错误，测试会处理异常情况
        }
    }

    def "test setButtons - 成功执行测试"() {
        given: "准备测试数据"
        standardData.getOnlyRelatedObject() >> false
        standardData.getObjectPermissions() >> ["edit": true]
        standardData.getDefaultButtons() >> [:]  // 应该是Map而不是List
        standardData.getHasPermissions() >> true
        standardData.isAfterActionWaiting() >> false
        standardData.getUpdateFromProperty() >> updateFromProperty
        updateFromProperty.isOnlyExistsSigninGroup() >> false

        when: "调用setButtons方法"
        def result = handler.setButtons(serviceManager, standardData, false, taskParams)

        then: "验证方法成功执行"
        // I18N资源已正确加载，方法应该成功执行
        result != null
        result.getButtons() != null
        noExceptionThrown()
    }

    def "test setButtons - 空参数处理"() {
        when: "传入null参数"
        def result = handler.setButtons(null, null, false, null)

        then: "应该抛出异常"
        thrown(NullPointerException)
    }

    def "test getTaskType - 获取任务类型"() {
        when: "调用getTaskType方法"
        def taskType = handler.getTaskType()

        then: "返回正确的任务类型"
        taskType == ExecutionTypeEnum.update
    }

    def "test 基本实例化"() {
        when: "创建BpmUpdateTaskButtonHandler实例"
        def newHandler = new BpmUpdateTaskButtonHandler()

        then: "实例创建成功"
        newHandler != null
        newHandler.getClass().getSimpleName() == "BpmUpdateTaskButtonHandler"
    }
}
