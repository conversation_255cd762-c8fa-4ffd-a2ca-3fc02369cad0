package com.facishare.bpm.handler.task.button.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import spock.lang.Specification

/**
 * Test for CustomTaskButtonHandler
 */
class CustomTaskButtonHandlerTest extends Specification {

    def customTaskButtonHandler = new CustomTaskButtonHandler()
    def serviceManager = Mock(RefServiceManager)
    def standardData = Mock(StandardData)
    def taskParams = Mock(TaskParams)

    def "test getTaskType"() {
        when:
        def result = customTaskButtonHandler.getTaskType()

        then:
        result == ExecutionTypeEnum.custom
    }

    def "test setButtons returns empty FormButtonResult"() {
        when:
        def result = customTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        result.getButtons().isEmpty()
        result.getErrorMsg() == null
        result.getCustom() == false
    }

    def "test setButtons with assignNextTask true"() {
        when:
        def result = customTaskButtonHandler.setButtons(serviceManager, standardData, true, taskParams)

        then:
        result != null
        result.getButtons().isEmpty()
    }

    def "test setButtons with null parameters"() {
        when:
        def result = customTaskButtonHandler.setButtons(null, null, false, null)

        then:
        result != null
        result.getButtons().isEmpty()
    }

    def "test setButtons multiple calls"() {
        when:
        def result1 = customTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)
        def result2 = customTaskButtonHandler.setButtons(serviceManager, standardData, true, taskParams)

        then:
        result1 != null
        result2 != null
        result1.getButtons().isEmpty()
        result2.getButtons().isEmpty()
        // Each call should return a new instance (they are different objects)
        !result1.is(result2)
    }

    def "test setButtons with different StandardData"() {
        given: "different standard data instances"
        def standardData1 = Mock(StandardData)
        def standardData2 = Mock(StandardData)

        when:
        def result1 = customTaskButtonHandler.setButtons(serviceManager, standardData1, false, taskParams)
        def result2 = customTaskButtonHandler.setButtons(serviceManager, standardData2, false, taskParams)

        then:
        result1 != null
        result2 != null
        result1.getButtons().isEmpty()
        result2.getButtons().isEmpty()
    }

    def "test setButtons consistency"() {
        when: "call setButtons multiple times with same parameters"
        def results = []
        for (int i = 0; i < 5; i++) {
            results << customTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)
        }

        then: "all results should be consistent"
        results.every { it != null }
        results.every { it.getButtons().isEmpty() }
        results.every { it.getErrorMsg() == null }
        results.every { it.getCustom() == false }
    }
}
