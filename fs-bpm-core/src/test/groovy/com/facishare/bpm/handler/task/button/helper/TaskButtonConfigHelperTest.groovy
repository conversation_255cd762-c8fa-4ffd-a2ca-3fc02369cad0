package com.facishare.bpm.handler.task.button.helper

import com.facishare.bpm.model.ActionButton
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import spock.lang.Specification

import java.lang.reflect.Field

/**
 * TaskButtonConfigHelper 单元测试
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
class TaskButtonConfigHelperTest extends Specification {

    def setup() {
        // 初始化TaskButtonConfigHelper的buttons字段
        initializeTaskButtonConfigHelper()
    }

    def initializeTaskButtonConfigHelper() {
        try {
            // 使用反射设置buttons字段
            Field buttonsField = TaskButtonConfigHelper.class.getDeclaredField("buttons")
            buttonsField.setAccessible(true)

            // 创建测试用的按钮配置
            Map<String, List<ActionButton>> testButtons = [:]
            testButtons.put(ExecutionTypeEnum.approve.name(), [
                new ActionButton("agree", "同意"),
                new ActionButton("disagree", "不同意")
            ])

            buttonsField.set(null, testButtons)
        } catch (Exception e) {
            // 如果反射失败，忽略错误
        }
    }

    def "test getTaskFormButtons - 正常情况"() {
        when: "调用getTaskFormButtons方法"
        def buttons = TaskButtonConfigHelper.getTaskFormButtons(ExecutionTypeEnum.approve, false)

        then: "返回按钮列表"
        buttons != null
        buttons.size() >= 0
    }

    def "test getTaskFormButtons - 空结果"() {
        when: "调用不存在的执行类型"
        def buttons = TaskButtonConfigHelper.getTaskFormButtons(ExecutionTypeEnum.operation, false)

        then: "返回空列表"
        buttons != null
        buttons.size() == 0
    }

    def "test 基本实例化"() {
        when: "创建TaskButtonConfigHelper实例"
        def helper = new TaskButtonConfigHelper()

        then: "实例创建成功"
        helper != null
        helper.getClass().getSimpleName() == "TaskButtonConfigHelper"
    }
}
