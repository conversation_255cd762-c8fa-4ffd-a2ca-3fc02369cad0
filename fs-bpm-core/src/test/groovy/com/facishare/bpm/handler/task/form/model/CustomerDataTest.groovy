package com.facishare.bpm.handler.task.form.model

import spock.lang.Specification

/**
 * Test for CustomerData
 */
class CustomerDataTest extends Specification {

    def "test default constructor"() {
        when:
        def customerData = new CustomerData()

        then:
        customerData.getData() == null
        customerData.getLayout() == null
        customerData.getDescribe() == null
        customerData.getDescribeExt() == null
    }

    def "test constructor with parameters"() {
        given: "maps for customer data"
        def data = ["field1": "value1", "field2": "value2"]
        def layout = ["layout1": "config1"]
        def describe = ["describe1": "desc1"]
        def describeExt = ["ext1": "extension1"]

        when:
        def customerData = new CustomerData(data, layout, describe, describeExt)

        then:
        customerData.getData() == data
        customerData.getLayout() == layout
        customerData.getDescribe() == describe
        customerData.getDescribeExt() == describeExt
    }

    def "test constructor with null parameters"() {
        when:
        def customerData = new CustomerData(null, null, null, null)

        then:
        customerData.getData() == null
        customerData.getLayout() == null
        customerData.getDescribe() == null
        customerData.getDescribeExt() == null
    }

    def "test setters and getters"() {
        given: "customer data instance"
        def customerData = new CustomerData()
        def data = ["testField": "testValue"]
        def layout = ["layoutField": "layoutValue"]
        def describe = ["describeField": "describeValue"]
        def describeExt = ["extField": "extValue"]

        when:
        customerData.setData(data)
        customerData.setLayout(layout)
        customerData.setDescribe(describe)
        customerData.setDescribeExt(describeExt)

        then:
        customerData.getData() == data
        customerData.getLayout() == layout
        customerData.getDescribe() == describe
        customerData.getDescribeExt() == describeExt
    }

    def "test data modification"() {
        given: "customer data with initial data"
        def initialData = ["initial": "value"]
        def customerData = new CustomerData(initialData, null, null, null)

        when: "modify the data map"
        customerData.getData().put("new", "newValue")

        then: "the modification should be reflected"
        customerData.getData().size() == 2
        customerData.getData()["initial"] == "value"
        customerData.getData()["new"] == "newValue"
    }

    def "test layout modification"() {
        given: "customer data with initial layout"
        def initialLayout = ["section1": "config1"]
        def customerData = new CustomerData(null, initialLayout, null, null)

        when: "modify the layout map"
        customerData.getLayout().put("section2", "config2")

        then: "the modification should be reflected"
        customerData.getLayout().size() == 2
        customerData.getLayout()["section1"] == "config1"
        customerData.getLayout()["section2"] == "config2"
    }

    def "test describe modification"() {
        given: "customer data with initial describe"
        def initialDescribe = ["field1": "description1"]
        def customerData = new CustomerData(null, null, initialDescribe, null)

        when: "modify the describe map"
        customerData.getDescribe().put("field2", "description2")

        then: "the modification should be reflected"
        customerData.getDescribe().size() == 2
        customerData.getDescribe()["field1"] == "description1"
        customerData.getDescribe()["field2"] == "description2"
    }

    def "test describeExt modification"() {
        given: "customer data with initial describeExt"
        def initialDescribeExt = ["ext1": "value1"]
        def customerData = new CustomerData(null, null, null, initialDescribeExt)

        when: "modify the describeExt map"
        customerData.getDescribeExt().put("ext2", "value2")

        then: "the modification should be reflected"
        customerData.getDescribeExt().size() == 2
        customerData.getDescribeExt()["ext1"] == "value1"
        customerData.getDescribeExt()["ext2"] == "value2"
    }

    def "test empty maps"() {
        given: "empty maps"
        def emptyData = [:]
        def emptyLayout = [:]
        def emptyDescribe = [:]
        def emptyDescribeExt = [:]

        when:
        def customerData = new CustomerData(emptyData, emptyLayout, emptyDescribe, emptyDescribeExt)

        then:
        customerData.getData().isEmpty()
        customerData.getLayout().isEmpty()
        customerData.getDescribe().isEmpty()
        customerData.getDescribeExt().isEmpty()
    }

    def "test complex data structures"() {
        given: "complex nested data structures"
        def complexData = [
            "user": [
                "name": "John Doe",
                "age": 30,
                "addresses": [
                    ["type": "home", "city": "New York"],
                    ["type": "work", "city": "Boston"]
                ]
            ],
            "preferences": ["theme": "dark", "language": "en"]
        ]
        def complexLayout = [
            "sections": [
                ["name": "personal", "fields": ["name", "age"]],
                ["name": "contact", "fields": ["addresses"]]
            ]
        ]

        when:
        def customerData = new CustomerData(complexData, complexLayout, null, null)

        then:
        customerData.getData()["user"]["name"] == "John Doe"
        customerData.getData()["user"]["age"] == 30
        customerData.getData()["preferences"]["theme"] == "dark"
        customerData.getLayout()["sections"].size() == 2
    }
}
