package com.facishare.bpm.handler.task.form.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.form.model.FormAssembleType
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey
import spock.lang.Specification

/**
 * Test for GenerateDataManagerImpl
 */
class GenerateDataManagerImplTest extends Specification {

    def generateDataManagerImpl = new GenerateDataManagerImpl()
    def serviceManager = Mock(RefServiceManager)
    def taskParams = Mock(TaskParams)

    def "test getTaskType"() {
        when:
        def result = generateDataManagerImpl.getTaskType()

        then:
        result == FormAssembleType.data
    }

    def "test execute with successful data retrieval"() {
        given: "successful data retrieval"
        def entityId = "TestEntity"
        def objectId = "TestObject"
        def forms = []
        def expectedData = [
            "id": objectId,
            "name": "Test Name",
            "value": "Test Value"
        ]
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> expectedData

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, taskParams)

        then:
        result == expectedData
        result["id"] == objectId
        result["name"] == "Test Name"
        result["value"] == "Test Value"
    }

    def "test execute with exception during data retrieval"() {
        given: "exception during data retrieval"
        def entityId = "TestEntity"
        def objectId = "TestObject"
        def forms = []
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> { throw new RuntimeException("Data not found") }

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, taskParams)

        then:
        result != null
        result.isEmpty()
    }

    def "test execute with null service manager"() {
        given: "null service manager"
        def entityId = "TestEntity"
        def objectId = "TestObject"
        def forms = []

        when:
        def result = generateDataManagerImpl.execute(null, entityId, objectId, forms, taskParams)

        then:
        result != null
        result.isEmpty()
    }

    def "test execute with null entity ID"() {
        given: "null entity ID"
        def entityId = null
        def objectId = "TestObject"
        def forms = []
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> [:]

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, taskParams)

        then:
        result != null
    }

    def "test execute with null object ID"() {
        given: "null object ID"
        def entityId = "TestEntity"
        def objectId = null
        def forms = []
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> [:]

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, taskParams)

        then:
        result != null
    }

    def "test setObjectDataByFieldDescribe with regular field"() {
        given: "regular field setup"
        def fields = [
            "testField": [
                "type": "text",
                "label": "Test Field"
            ]
        ]
        def formField = [
            (WorkflowKey.ActivityKey.ExtensionKey.name): "testField",
            (WorkflowKey.ActivityKey.ExtensionKey.value): "test value",
            (WorkflowKey.ActivityKey.ExtensionKey.type): "text"
        ]
        def objectData = [:]

        when:
        GenerateDataManagerImpl.setObjectDataByFieldDescribe(fields, formField, objectData)

        then:
        objectData["testField"] == "test value"
    }

    def "test setObjectDataByFieldDescribe with percentile field"() {
        given: "percentile field setup"
        def fields = [
            "percentField": [
                "type": BPMConstants.MetadataKey.PERCENTILE,
                "label": "Percent Field"
            ]
        ]
        def formField = [
            (WorkflowKey.ActivityKey.ExtensionKey.name): "percentField",
            (WorkflowKey.ActivityKey.ExtensionKey.value): 0.85,
            (WorkflowKey.ActivityKey.ExtensionKey.type): BPMConstants.MetadataKey.PERCENTILE
        ]
        def objectData = [:]

        when:
        GenerateDataManagerImpl.setObjectDataByFieldDescribe(fields, formField, objectData)

        then:
        objectData["percentField"] == "0.85"
    }

    def "test setObjectDataByFieldDescribe with object reference field"() {
        given: "object reference field setup"
        def fields = [
            "refField": [
                "type": BPMConstants.MetadataKey.objectReference,
                "label": "Reference Field"
            ]
        ]
        def formField = [
            (WorkflowKey.ActivityKey.ExtensionKey.name): "refField",
            (WorkflowKey.ActivityKey.ExtensionKey.value): "ref123",
            (WorkflowKey.ActivityKey.ExtensionKey.type): BPMConstants.MetadataKey.objectReference,
            (WorkflowKey.ActivityKey.ExtensionKey.relatedObjectName): "Related Object Name"
        ]
        def objectData = [:]

        when:
        GenerateDataManagerImpl.setObjectDataByFieldDescribe(fields, formField, objectData)

        then:
        objectData["refField"] == "ref123"
        objectData["refField" + BPMConstants.MetadataKey.lookupFieldNameValuePostfix] == "Related Object Name"
    }

    def "test setObjectDataByFieldDescribe with null value"() {
        given: "field with null value"
        def fields = [
            "nullField": [
                "type": "text",
                "label": "Null Field"
            ]
        ]
        def formField = [
            (WorkflowKey.ActivityKey.ExtensionKey.name): "nullField",
            (WorkflowKey.ActivityKey.ExtensionKey.value): null,
            (WorkflowKey.ActivityKey.ExtensionKey.type): "text"
        ]
        def objectData = [:]

        when:
        GenerateDataManagerImpl.setObjectDataByFieldDescribe(fields, formField, objectData)

        then:
        objectData["nullField"] == null
    }

    def "test setObjectDataByFieldDescribe with empty field describe"() {
        given: "empty field describe"
        def fields = [:]
        def formField = [
            (WorkflowKey.ActivityKey.ExtensionKey.name): "unknownField",
            (WorkflowKey.ActivityKey.ExtensionKey.value): "some value",
            (WorkflowKey.ActivityKey.ExtensionKey.type): "text"
        ]
        def objectData = [:]

        when:
        GenerateDataManagerImpl.setObjectDataByFieldDescribe(fields, formField, objectData)

        then:
        objectData["unknownField"] == "some value"
    }

    def "test setObjectDataByFieldDescribe with percentile field and null value"() {
        given: "percentile field with null value"
        def fields = [
            "percentField": [
                "type": BPMConstants.MetadataKey.PERCENTILE,
                "label": "Percent Field"
            ]
        ]
        def formField = [
            (WorkflowKey.ActivityKey.ExtensionKey.name): "percentField",
            (WorkflowKey.ActivityKey.ExtensionKey.value): null,
            (WorkflowKey.ActivityKey.ExtensionKey.type): BPMConstants.MetadataKey.PERCENTILE
        ]
        def objectData = [:]

        when:
        GenerateDataManagerImpl.setObjectDataByFieldDescribe(fields, formField, objectData)

        then:
        objectData["percentField"] == null
    }

    def "test execute with complex forms parameter"() {
        given: "complex forms parameter"
        def entityId = "TestEntity"
        def objectId = "TestObject"
        def forms = [
            [
                ["name": "field1", "value": "value1"],
                ["name": "field2", "value": "value2"]
            ],
            [
                ["name": "field3", "value": "value3"]
            ]
        ]
        def expectedData = ["field1": "value1", "field2": "value2"]
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> expectedData

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, taskParams)

        then:
        result == expectedData
    }

    def "test execute with null forms parameter"() {
        given: "null forms parameter"
        def entityId = "TestEntity"
        def objectId = "TestObject"
        def forms = null
        def expectedData = ["data": "test"]
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> expectedData

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, taskParams)

        then:
        result == expectedData
    }

    def "test execute with null task params"() {
        given: "null task params"
        def entityId = "TestEntity"
        def objectId = "TestObject"
        def forms = []
        def expectedData = ["data": "test"]
        serviceManager.findDataById(entityId, objectId, true, true, false, true) >> expectedData

        when:
        def result = generateDataManagerImpl.execute(serviceManager, entityId, objectId, forms, null)

        then:
        result == expectedData
    }
}
