package com.facishare.bpm.handler.task.button.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import spock.lang.Specification

/**
 * Test for AddRelatedObjectTaskButtonHandler
 */
class AddRelatedObjectTaskButtonHandlerTest extends Specification {

    def addRelatedObjectTaskButtonHandler = new AddRelatedObjectTaskButtonHandler()
    def serviceManager = Mock(RefServiceManager)
    def standardData = Mock(StandardData)
    def taskParams = Mock(TaskParams)

    def "test getTaskType"() {
        when:
        def result = addRelatedObjectTaskButtonHandler.getTaskType()

        then:
        result == ExecutionTypeEnum.addRelatedObject
    }

    def "test setButtons basic functionality"() {
        given: "basic setup"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
        // The method should return a FormButtonResult
    }

    def "test setButtons with only related object"() {
        given: "only related object scenario"
        standardData.getOnlyRelatedObject() >> true
        standardData.getDefaultButtons() >> [:]

        when:
        def result = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
    }

    def "test setButtons with assignNextTask true"() {
        given: "assignNextTask true"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, true, taskParams)

        then:
        result != null
    }

    def "test setButtons with null parameters"() {
        when:
        def result = addRelatedObjectTaskButtonHandler.setButtons(null, null, false, null)

        then:
        // Should throw NullPointerException for null parameters
        thrown(NullPointerException)
    }

    def "test setButtons with different standard data configurations"() {
        given: "different configurations"
        def standardData1 = Mock(StandardData)
        def standardData2 = Mock(StandardData)
        
        standardData1.getOnlyRelatedObject() >> true
        standardData1.getDefaultButtons() >> [:]
        
        standardData2.getOnlyRelatedObject() >> false
        standardData2.getDefaultButtons() >> ["button1": "label1"]

        when:
        def result1 = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData1, false, taskParams)
        def result2 = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData2, false, taskParams)

        then:
        result1 != null
        result2 != null
    }

    def "test multiple calls consistency"() {
        given: "consistent setup"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result1 = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)
        def result2 = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result1 != null
        result2 != null
        // Results should be consistent
    }

    def "test setButtons with various task params"() {
        given: "various task params"
        def taskParams1 = Mock(TaskParams)
        def taskParams2 = Mock(TaskParams)
        
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result1 = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams1)
        def result2 = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams2)

        then:
        result1 != null
        result2 != null
    }

    def "test handler instance properties"() {
        expect:
        addRelatedObjectTaskButtonHandler != null
        addRelatedObjectTaskButtonHandler instanceof AddRelatedObjectTaskButtonHandler
        addRelatedObjectTaskButtonHandler.getTaskType() == ExecutionTypeEnum.addRelatedObject
    }

    def "test setButtons with empty default buttons"() {
        given: "empty default buttons"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [:]

        when:
        def result = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
    }

    def "test setButtons with non-empty default buttons"() {
        given: "non-empty default buttons"
        standardData.getOnlyRelatedObject() >> false
        standardData.getDefaultButtons() >> [
            "save": "Save",
            "cancel": "Cancel"
        ]

        when:
        def result = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)

        then:
        result != null
    }
}
