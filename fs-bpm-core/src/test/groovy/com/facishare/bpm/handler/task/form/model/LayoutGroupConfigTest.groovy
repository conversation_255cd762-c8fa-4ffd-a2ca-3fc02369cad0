package com.facishare.bpm.handler.task.form.model

import spock.lang.Specification

/**
 * Test for LayoutGroupConfig
 */
class LayoutGroupConfigTest extends Specification {

    def "test default constructor"() {
        when:
        def config = new LayoutGroupConfig()

        then:
        config.getChildSort() == null
        config.getCantDisplayChild() == null
    }

    def "test cantDisplay with null cantDisplayChild"() {
        given: "config with null cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(null)

        when:
        def result = config.cantDisplay("text")

        then:
        result == false
    }

    def "test cantDisplay with empty cantDisplayChild"() {
        given: "config with empty cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild([])

        when:
        def result = config.cantDisplay("text")

        then:
        result == false
    }

    def "test cantDisplay with matching render type"() {
        given: "config with cantDisplayChild containing the render type"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["text", "number", "date"])

        when:
        def result = config.cantDisplay("text")

        then:
        result == true
    }

    def "test cantDisplay with non-matching render type"() {
        given: "config with cantDisplayChild not containing the render type"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["text", "number", "date"])

        when:
        def result = config.cantDisplay("email")

        then:
        result == false
    }

    def "test cantDisplay with null render type"() {
        given: "config with cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["text", "number"])

        when:
        def result = config.cantDisplay(null)

        then:
        result == false
    }

    def "test cantDisplay with empty render type"() {
        given: "config with cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["text", "number"])

        when:
        def result = config.cantDisplay("")

        then:
        result == false
    }

    def "test cantDisplay case sensitivity"() {
        given: "config with cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["text", "NUMBER"])

        when:
        def result1 = config.cantDisplay("TEXT")
        def result2 = config.cantDisplay("number")
        def result3 = config.cantDisplay("NUMBER")

        then:
        result1 == false  // Case sensitive, "TEXT" != "text"
        result2 == false  // Case sensitive, "number" != "NUMBER"
        result3 == true   // Exact match
    }

    def "test setters and getters for childSort"() {
        given: "layout group config"
        def config = new LayoutGroupConfig()
        def childSort = ["field1", "field2", "field3"]

        when:
        config.setChildSort(childSort)

        then:
        config.getChildSort() == childSort
        config.getChildSort().size() == 3
        config.getChildSort().contains("field1")
        config.getChildSort().contains("field2")
        config.getChildSort().contains("field3")
    }

    def "test setters and getters for cantDisplayChild"() {
        given: "layout group config"
        def config = new LayoutGroupConfig()
        def cantDisplayChild = ["hidden", "disabled", "readonly"]

        when:
        config.setCantDisplayChild(cantDisplayChild)

        then:
        config.getCantDisplayChild() == cantDisplayChild
        config.getCantDisplayChild().size() == 3
        config.getCantDisplayChild().contains("hidden")
        config.getCantDisplayChild().contains("disabled")
        config.getCantDisplayChild().contains("readonly")
    }

    def "test childSort modification"() {
        given: "config with initial childSort"
        def config = new LayoutGroupConfig()
        def initialSort = ["field1", "field2"]
        config.setChildSort(initialSort)

        when: "modify the childSort list"
        config.getChildSort().add("field3")

        then: "the modification should be reflected"
        config.getChildSort().size() == 3
        config.getChildSort().contains("field3")
    }

    def "test cantDisplayChild modification"() {
        given: "config with initial cantDisplayChild"
        def config = new LayoutGroupConfig()
        def initialCantDisplay = ["text"]
        config.setCantDisplayChild(initialCantDisplay)

        when: "modify the cantDisplayChild list"
        config.getCantDisplayChild().add("number")

        then: "the modification should be reflected"
        config.getCantDisplayChild().size() == 2
        config.cantDisplay("text") == true
        config.cantDisplay("number") == true
    }

    def "test cantDisplay with single element list"() {
        given: "config with single element cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["onlyOne"])

        when:
        def result1 = config.cantDisplay("onlyOne")
        def result2 = config.cantDisplay("other")

        then:
        result1 == true
        result2 == false
    }

    def "test cantDisplay with duplicate elements"() {
        given: "config with duplicate elements in cantDisplayChild"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild(["text", "text", "number"])

        when:
        def result = config.cantDisplay("text")

        then:
        result == true
    }

    def "test empty childSort"() {
        given: "config with empty childSort"
        def config = new LayoutGroupConfig()
        config.setChildSort([])

        when:
        def childSort = config.getChildSort()

        then:
        childSort != null
        childSort.isEmpty()
    }

    def "test null assignments"() {
        given: "config"
        def config = new LayoutGroupConfig()

        when:
        config.setChildSort(null)
        config.setCantDisplayChild(null)

        then:
        config.getChildSort() == null
        config.getCantDisplayChild() == null
        config.cantDisplay("anything") == false
    }

    def "test complex render types"() {
        given: "config with complex render types"
        def config = new LayoutGroupConfig()
        config.setCantDisplayChild([
            "text_field",
            "number_input",
            "date_picker",
            "file_upload",
            "rich_text_editor"
        ])

        when:
        def results = [
            config.cantDisplay("text_field"),
            config.cantDisplay("number_input"),
            config.cantDisplay("date_picker"),
            config.cantDisplay("file_upload"),
            config.cantDisplay("rich_text_editor"),
            config.cantDisplay("unknown_type")
        ]

        then:
        results[0] == true
        results[1] == true
        results[2] == true
        results[3] == true
        results[4] == true
        results[5] == false
    }
}
