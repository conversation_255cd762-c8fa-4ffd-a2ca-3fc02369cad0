package com.facishare.bpm.handler.task.form.model

import spock.lang.Specification

/**
 * Test for FormField
 */
class FormFieldTest extends Specification {

    def "test default constructor"() {
        when:
        def formField = new FormField("api", "header", [], "title")

        then:
        formField.getApi_name() == "api"
        formField.getHeader() == "header"
        formField.is_show == true
        formField.getTab_index() == "ltr"
        formField.getTitle() == "title"
        formField.getForm_fields() != null
        formField.getSignin() == null
        formField.getSignout() == null
    }

    def "test constructor with signin and signout"() {
        given: "form field parameters"
        def apiName = "test_api"
        def header = "Test Header"
        def formFields = [
            ["name": "field1", "type": "text"],
            ["name": "field2", "type": "number"]
        ]
        def title = "Test Title"
        def signin = true
        def signout = false

        when:
        def formField = new FormField(apiName, header, formFields, title, signin, signout)

        then:
        formField.getApi_name() == apiName
        formField.getHeader() == header
        formField.getForm_fields() == formFields
        formField.getTitle() == title
        formField.getSignin() == true
        formField.getSignout() == false
        formField.is_show == true
        formField.getTab_index() == "ltr"
    }

    def "test constructor with null signin and signout"() {
        given: "form field parameters with null signin/signout"
        def apiName = "test_api"
        def header = "Test Header"
        def formFields = [["name": "field1"]]
        def title = "Test Title"

        when:
        def formField = new FormField(apiName, header, formFields, title, null, null)

        then:
        formField.getApi_name() == apiName
        formField.getHeader() == header
        formField.getForm_fields() == formFields
        formField.getTitle() == title
        formField.getSignin() == false  // null should be converted to false
        formField.getSignout() == false  // null should be converted to false
    }

    def "test constructor without signin and signout"() {
        given: "form field parameters without signin/signout"
        def apiName = "simple_api"
        def header = "Simple Header"
        def formFields = [["name": "simple_field"]]
        def title = "Simple Title"

        when:
        def formField = new FormField(apiName, header, formFields, title)

        then:
        formField.getApi_name() == apiName
        formField.getHeader() == header
        formField.getForm_fields() == formFields
        formField.getTitle() == title
        formField.getSignin() == null
        formField.getSignout() == null
        formField.is_show == true
        formField.getTab_index() == "ltr"
    }

    def "test setters and getters"() {
        given: "form field instance"
        def formField = new FormField("api", "header", [], "title")

        when:
        formField.setApi_name("new_api")
        formField.setHeader("New Header")
        formField.set_show(false)
        formField.setTab_index("rtl")
        formField.setTitle("New Title")
        formField.setForm_fields([["name": "new_field"]])
        formField.setSignin(true)
        formField.setSignout(true)

        then:
        formField.getApi_name() == "new_api"
        formField.getHeader() == "New Header"
        formField.is_show == false
        formField.getTab_index() == "rtl"
        formField.getTitle() == "New Title"
        formField.getForm_fields() == [["name": "new_field"]]
        formField.getSignin() == true
        formField.getSignout() == true
    }

    def "test empty form fields"() {
        given: "empty form fields"
        def emptyFormFields = []

        when:
        def formField = new FormField("api", "header", emptyFormFields, "title")

        then:
        formField.getForm_fields().isEmpty()
        formField.getApi_name() == "api"
        formField.getHeader() == "header"
        formField.getTitle() == "title"
    }

    def "test null form fields"() {
        when:
        def formField = new FormField("api", "header", null, "title")

        then:
        formField.getForm_fields() == null
        formField.getApi_name() == "api"
        formField.getHeader() == "header"
        formField.getTitle() == "title"
    }

    def "test complex form fields"() {
        given: "complex form fields structure"
        def complexFormFields = [
            [
                "name": "user_info",
                "type": "object",
                "fields": [
                    ["name": "first_name", "type": "text", "required": true],
                    ["name": "last_name", "type": "text", "required": true],
                    ["name": "age", "type": "number", "min": 0, "max": 150]
                ]
            ],
            [
                "name": "preferences",
                "type": "array",
                "items": [
                    ["name": "theme", "type": "select", "options": ["light", "dark"]],
                    ["name": "language", "type": "select", "options": ["en", "zh", "ja"]]
                ]
            ]
        ]

        when:
        def formField = new FormField("complex_api", "Complex Form", complexFormFields, "Complex Title", true, false)

        then:
        formField.getForm_fields().size() == 2
        formField.getForm_fields()[0]["name"] == "user_info"
        formField.getForm_fields()[0]["type"] == "object"
        formField.getForm_fields()[1]["name"] == "preferences"
        formField.getForm_fields()[1]["type"] == "array"
        formField.getSignin() == true
        formField.getSignout() == false
    }

    def "test form field modification"() {
        given: "form field with initial data"
        def initialFormFields = [["name": "initial_field"]]
        def formField = new FormField("api", "header", initialFormFields, "title")

        when: "modify the form fields list"
        formField.getForm_fields().add(["name": "added_field"])

        then: "the modification should be reflected"
        formField.getForm_fields().size() == 2
        formField.getForm_fields()[0]["name"] == "initial_field"
        formField.getForm_fields()[1]["name"] == "added_field"
    }

    def "test boolean signin/signout values"() {
        when: "test various boolean combinations"
        def formField1 = new FormField("api1", "header1", [], "title1", true, true)
        def formField2 = new FormField("api2", "header2", [], "title2", false, false)
        def formField3 = new FormField("api3", "header3", [], "title3", true, false)
        def formField4 = new FormField("api4", "header4", [], "title4", false, true)

        then:
        formField1.getSignin() == true && formField1.getSignout() == true
        formField2.getSignin() == false && formField2.getSignout() == false
        formField3.getSignin() == true && formField3.getSignout() == false
        formField4.getSignin() == false && formField4.getSignout() == true
    }

    def "test default values"() {
        when:
        def formField = new FormField("api", "header", [], "title")

        then:
        formField.is_show == true  // default value
        formField.getTab_index() == "ltr"  // default value
    }

    def "test string properties with special characters"() {
        given: "strings with special characters"
        def apiName = "api_with_特殊字符"
        def header = "Header with émojis 🚀"
        def title = "Title with <HTML> & symbols"

        when:
        def formField = new FormField(apiName, header, [], title)

        then:
        formField.getApi_name() == apiName
        formField.getHeader() == header
        formField.getTitle() == title
    }
}
