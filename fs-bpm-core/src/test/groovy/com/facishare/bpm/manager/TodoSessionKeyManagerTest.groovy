package com.facishare.bpm.manager

import com.facishare.bpm.manage.TodoSessionKeyManager
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.proxy.EServiceResourceProxy
import com.facishare.bpm.util.SwitchConfigManager
import com.facishare.flow.postgre.BPMTaskDao
import com.facishare.rest.core.model.RemoteContext
import com.github.autoconf.helper.ConfigHelper
import com.github.jedis.support.MergeJedisCmd
import spock.lang.Specification

/**
 * Test for TodoSessionKeyManager
 */
class TodoSessionKeyManagerTest extends Specification {

    def todoSessionKeyManager = new TodoSessionKeyManager()
    def eServiceResourceProxy = Mock(EServiceResourceProxy)
    def bpmTaskDao = Mock(BPMTaskDao)
    def redisClient = Mock(MergeJedisCmd)
    def context = Mock(RemoteContext)

    def setup() {
        todoSessionKeyManager.eServiceResourceProxy = eServiceResourceProxy
        todoSessionKeyManager.bpmTaskDao = bpmTaskDao
        todoSessionKeyManager.redisClient = redisClient
        context.getTenantId() >> "12345"
        context.getUserId() >> "user123"
    }

    def "test getSessionKey for non-cases object"() {
        given: "non-cases entity"
        def entityId = "AccountObj"
        def executionType = ExecutionTypeEnum.approve
        def actionCode = "approve"
        def externalFlow = 0
        def taskId = "task123"
        def elementApiName = "element1"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == ""
    }

    def "test getSessionKey for cases object with session key combine enabled"() {
        given: "cases object with session key combine enabled"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.approve
        def actionCode = "approve"
        def externalFlow = 0
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> true

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == ""
    }

    def "test getSessionKey for custom execution type"() {
        given: "custom execution type"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.custom
        def actionCode = "customAction"
        def externalFlow = 0
        def taskId = "task123"
        def elementApiName = "customElement"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false
        SwitchConfigManager.getCustomElementSessionKey("customElement") >> "customSessionKey"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "customSessionKey"
    }

    def "test getSessionKey for external apply task with waitAssign"() {
        given: "external apply task with waitAssign action"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.externalApplyTask
        def actionCode = "assignAction"
        def externalFlow = 1
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        eServiceResourceProxy.getAppActionTodoType(context, entityId, actionCode, externalFlow) >> "waitAssign"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "457WaitAssign"
    }

    def "test getSessionKey for external apply task with waitCheckins"() {
        given: "external apply task with waitCheckins action"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.externalApplyTask
        def actionCode = "checkinAction"
        def externalFlow = 1
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        eServiceResourceProxy.getAppActionTodoType(context, entityId, actionCode, externalFlow) >> "waitCheckins"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "457WaitCheckins"
    }

    def "test getSessionKey for external apply task with waitDeal"() {
        given: "external apply task with waitDeal action"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.externalApplyTask
        def actionCode = "dealAction"
        def externalFlow = 1
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        eServiceResourceProxy.getAppActionTodoType(context, entityId, actionCode, externalFlow) >> "waitDeal"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "457WaitDeal"
    }

    def "test getSessionKey for external apply task with waitToOrder"() {
        given: "external apply task with waitToOrder action"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.externalApplyTask
        def actionCode = "orderAction"
        def externalFlow = 1
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        eServiceResourceProxy.getAppActionTodoType(context, entityId, actionCode, externalFlow) >> "waitToOrder"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "457WaitToOrder"
    }

    def "test getSessionKey for external apply task with unknown action"() {
        given: "external apply task with unknown action"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.externalApplyTask
        def actionCode = "unknownAction"
        def externalFlow = 1
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        eServiceResourceProxy.getAppActionTodoType(context, entityId, actionCode, externalFlow) >> "unknownType"

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == ""
    }

    def "test getSessionKey for external apply task with empty actionCode"() {
        given: "external apply task with empty actionCode"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.externalApplyTask
        def actionCode = ""
        def externalFlow = 1
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == ""
    }

    def "test getSessionKey for approve execution type"() {
        given: "approve execution type"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.approve
        def actionCode = "approve"
        def externalFlow = 0
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "457WaitApproval"
    }

    def "test getSessionKey for other execution types"() {
        given: "other execution type"
        def entityId = BPMConstants.MetadataKey.CASESOBJ_API_NAME
        def executionType = ExecutionTypeEnum.businessTask
        def actionCode = "business"
        def externalFlow = 0
        def taskId = "task123"
        def elementApiName = "element1"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSessionKeyCombine("12345") >> false

        when:
        def result = todoSessionKeyManager.getSessionKey(context, entityId, executionType, actionCode, externalFlow, taskId, elementApiName)

        then:
        result == "457WaitDeal"
    }

    def "test updateSessionKey with empty tenantId"() {
        given: "empty tenantId"
        def tenantId = ""

        when:
        todoSessionKeyManager.updateSessionKey(tenantId)

        then:
        0 * redisClient.set(_, _, _)
        0 * bpmTaskDao.updateSessionKeyTo457(_)
    }

    def "test updateSessionKey with non-processor app"() {
        given: "non-processor app"
        def tenantId = "12345"

        // Mock static method
        GroovyMock(ConfigHelper, global: true)
        ConfigHelper.getProcessInfo() >> [getAppName: { "other-app" }]

        when:
        todoSessionKeyManager.updateSessionKey(tenantId)

        then:
        0 * redisClient.set(_, _, _)
        0 * bpmTaskDao.updateSessionKeyTo457(_)
    }

    def "test updateSessionKey with processor app and lock acquired"() {
        given: "processor app and lock acquired"
        def tenantId = "12345"

        // Mock static method
        GroovyMock(ConfigHelper, global: true)
        ConfigHelper.getProcessInfo() >> [getAppName: { "processor-app" }]

        redisClient.set(_, _, _) >> "OK"
        bpmTaskDao.updateSessionKeyTo457(_) >> {}

        when:
        todoSessionKeyManager.updateSessionKey(tenantId)

        then:
        1 * redisClient.set(_, _, _)
        1 * bpmTaskDao.updateSessionKeyTo457(_)
    }

    def "test updateSessionKey with processor app and lock not acquired"() {
        given: "processor app and lock not acquired"
        def tenantId = "12345"

        // Mock static method
        GroovyMock(ConfigHelper, global: true)
        ConfigHelper.getProcessInfo() >> [getAppName: { "processor-app" }]

        redisClient.set(_, _, _) >> "FAIL"

        when:
        todoSessionKeyManager.updateSessionKey(tenantId)

        then:
        1 * redisClient.set(_, _, _)
        0 * bpmTaskDao.updateSessionKeyTo457(_)
    }

    def "test updateSessionKey with cloud app and exception"() {
        given: "cloud app with exception during update"
        def tenantId = "12345"

        // Mock static method
        GroovyMock(ConfigHelper, global: true)
        ConfigHelper.getProcessInfo() >> [getAppName: { "cloud-app" }]

        redisClient.set(_, _, _) >> "OK"
        bpmTaskDao.updateSessionKeyTo457(_) >> { throw new RuntimeException("Update failed") }

        when:
        todoSessionKeyManager.updateSessionKey(tenantId)

        then:
        1 * redisClient.set(_, _, _)
        1 * bpmTaskDao.updateSessionKeyTo457(_)
        noExceptionThrown()
    }
}
