package com.facishare.bpm.manager


import com.facishare.bpm.manage.impl.InstanceVariableManagerImpl
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Created by wangz on 17-7-16.
 */
class InstanceVariableManagerTest extends Specification {

    def "SetInstanceObjectVariable"() {
        given : "variable & metadata"
        def variable = getVariables()
        def variableKeyAndData = getVariableKeyAndData()
        def instanceVariableManager=new InstanceVariableManagerImpl()
        when:
        instanceVariableManager.setInstanceObjectVariable(variable,variableKeyAndData)

        then:
        variable.get("activity_B##AccountObj").equals("2")
        variable.get("activity_B##SelfRef##AccountObj").equals("3")

    }

//    def "SetFieldsValue"() {
//        given : "variable & metadata"  //A -> SelfRef_A == B -> SelfRef_B
//        def variable = getVariables()
//        def variableKeyAndData = getVariableKeyAndData()
//        def instanceVariableManager=new InstanceVariableManagerImpl()
//        def serviceManager = Mock(RefServiceManager.class)
//
//
//        when:
//        instanceVariableManager.setInstanceObjectVariable(variable,variableKeyAndData)
//        instanceVariableManager.setFieldsValue(serviceManager,variable,variableKeyAndData)
//
//        then:
//        variable.get("activity_B##AccountObj##field_3").equals("value_3")
//        variable.get("activity_B##SelfRef##AccountObj##field_1").equals("value_1")
//        variable.get("activity_A##SelfRef##AccountObj##field_2").equals("value_2")
//        !variable.get("activity_A##SelfRef##AccountObj##field_2").equals("field_2")
//    }

    def getVariables() {
        Map<String, String> variables = Maps.newHashMap()
        variables.put("activity_A##AccountObj", "1")
        variables.put("activity_A##SelfRef##AccountObj", "2")  //activity_A##SelfRef##AccountObj = activity_B##Account
        variables.put("activity_B##AccountObj", null)
        variables.put("activity_B##SelfRef##AccountObj", null)
        variables.put("activity_B##SelfRef##AccountObj##field_1", null)
        variables.put("activity_A##SelfRef##AccountObj##field_2", "field_2")
        variables.put("activity_B##AccountObj##field_3", null)

        return variables
    }

    def getVariableKeyAndData(){
        Map<String,Map<String,Object>> variableKeyAndData = Maps.newHashMap()
        String variableKey_1 = "activity_B##AccountObj"
        Map<String,Object> data_1 = Maps.newHashMap()
        data_1.put("_id","2")
        data_1.put("field_3","value_3")
        data_1.put("field_2","value_2")
        variableKeyAndData.put(variableKey_1,data_1)

        String variableKey_2 = "activity_B##SelfRef##AccountObj"
        Map<String,Object> data_2 = Maps.newHashMap()
        data_2.put("_id","3")
        data_2.put("field_1","value_1")
        variableKeyAndData.put(variableKey_2,data_2)

        return variableKeyAndData
    }
}
