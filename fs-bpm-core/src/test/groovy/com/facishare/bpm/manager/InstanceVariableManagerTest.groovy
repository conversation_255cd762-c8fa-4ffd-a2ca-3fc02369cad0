package com.facishare.bpm.manager

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.VariableExt
import com.facishare.bpm.exception.BPMTaskExecuteException
import com.facishare.bpm.manage.impl.InstanceVariableManagerImpl
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.util.SwitchConfigManager
import com.facishare.bpm.utils.DataCacheHandler
import com.facishare.bpm.utils.bean.BeanUtils
import com.facishare.bpm.utils.model.Pair
import com.facishare.rest.core.model.RemoteContext
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Simplified test for InstanceVariableManagerImpl
 */
class InstanceVariableManagerTest extends Specification {

    def instanceVariableManager = new InstanceVariableManagerImpl()
    def serviceManager = Mock(RefServiceManager)
    def dataCacheHandler = Mock(DataCacheHandler)
    def context = Mock(RemoteContext)

    def setup() {
        serviceManager.getTenantId() >> "12345"
        serviceManager.getContext() >> context
        context.getTenantId() >> "12345"
        context.getUserId() >> "user123"

        // Initialize SwitchConfigManager to avoid null pointer
        def mockSwitchConfig = Mock(SwitchConfigManager.SwitchConfig)
        mockSwitchConfig.getUpgradeTaskInfo() >> [:]
        SwitchConfigManager.switchConfig = mockSwitchConfig

        // Mock SwitchConfigManager static methods
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.isUpgradeTaskInfo(_) >> false
    }

    def "test getWorkflowVariableInstances with basic scenario"() {
        given: "basic test data"
        def workflowInstanceId = "instance123"
        def workflowId = "workflow123"
        def variableKeys = [
            "activity_0##AccountObj": new Pair("AccountObj", "obj123")
        ]
        def variableMap = [
            "activity_0##AccountObj": new VariableExt("activity_0##AccountObj", "oldValue")
        ]

        dataCacheHandler.getVariableMap(workflowInstanceId, workflowId) >> variableMap

        when:
        def result = instanceVariableManager.getWorkflowVariableInstances(
            serviceManager, workflowInstanceId, workflowId, variableKeys, dataCacheHandler)

        then:
        result != null
        result.size() >= 0
    }

    def "test setInstanceObjectVariable with simple data"() {
        given: "simple variable data"
        def variable = [:]
        def variableKeyAndData = [
            "key1": ["_id": "id1"]
        ]

        when:
        instanceVariableManager.setInstanceObjectVariable(variable, variableKeyAndData)

        then:
        variable.get("key1") == "id1"
    }

}
