package com.facishare.bpm.manager

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.manage.MoreOperationManager
import com.facishare.bpm.manage.impl.MoreOperationManagerImpl
import com.facishare.bpm.model.task.BPMTask
import com.facishare.bpm.model.task.TaskDetail
import com.facishare.bpm.model.task.LaneTask
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance
import com.facishare.bpm.model.paas.engine.bpm.TaskState
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution
import spock.lang.Specification

/**
 * Test for MoreOperationManager interface
 */
class MoreOperationManagerTest extends Specification {

    def moreOperationManager = new MoreOperationManagerImpl()
    def serviceManager = Mock(RefServiceManager)

    def setup() {
        serviceManager.getTenantId() >> "12345"
        serviceManager.isOuterUserId() >> false
        serviceManager.isNeedDiscussButton() >> true
        serviceManager.hasObjectFunctionPrivilege(_) >> [
            "ChangeBPMApprover": true,
            "StopBPM": true,
            "ViewEntireBPM": true,
            "ViewBPMInstanceLog": true
        ]
    }

    def "test getTaskMoreOperations with BPMTask"() {
        given: "valid BPMTask"
        def task = new BPMTask()
        task.state = TaskState.in_progress
        task.extension = ['entityId': 'AccountObj']

        when:
        def result = moreOperationManager.getTaskMoreOperations(serviceManager, task)

        then:
        result != null
        result.size() >= 0
    }

    def "test getTaskMoreOperations with TaskDetail"() {
        given: "valid TaskDetail"
        def taskDetail = Mock(TaskDetail)
        taskDetail.getEntityId() >> "AccountObj"
        taskDetail.getExecutionType() >> ExecutionTypeEnum.approve
        taskDetail.isInProgress() >> true
        taskDetail.isError() >> false

        // Create a mock execution object
        def execution = Mock(AfterActionExecution.SimpleAfterActionExecution)
        execution.isError() >> false
        taskDetail.getExecution() >> execution

        def task = Mock(Task)
        task.isSequence() >> false

        when:
        def result = moreOperationManager.getTaskMoreOperations(serviceManager, taskDetail, task)

        then:
        result != null
        result.size() >= 0
    }

    def "test getTaskMoreOperations with LaneTask"() {
        given: "valid LaneTask"
        def laneTask = Mock(LaneTask)
        laneTask.getEntityId() >> "AccountObj"
        laneTask.getExecutionType() >> ExecutionTypeEnum.approve
        laneTask.isInProgress() >> true
        laneTask.isError() >> false

        // Create a mock execution object
        def execution = Mock(AfterActionExecution.SimpleAfterActionExecution)
        execution.isError() >> false
        laneTask.getExecution() >> execution

        def task = Mock(Task)
        task.isSequence() >> false

        when:
        def result = moreOperationManager.getTaskMoreOperations(serviceManager, laneTask, task)

        then:
        result != null
        result.size() >= 0
    }

    def "test getTaskMoreOperations with Task"() {
        given: "valid Task"
        def task = Mock(Task)
        task.getEntityId() >> "AccountObj"
        task.getState() >> TaskState.in_progress
        task.getExecutionType() >> ExecutionTypeEnum.approve
        task.isSequence() >> false

        when:
        def result = moreOperationManager.getTaskMoreOperations(serviceManager, task)

        then:
        result != null
        result.size() >= 0
    }

    def "test getInstanceMoreOperations"() {
        given: "valid WorkflowInstance"
        def workflowInstance = Mock(WorkflowInstance)
        workflowInstance.getEntityId() >> "AccountObj"
        workflowInstance.getCompleted() >> false
        workflowInstance.getInstanceAfterError() >> false

        def functionPrivilegeRst = [
            "ViewBPMInstanceLog": true,
            "StopBPM": true,
            "ViewEntireBPM": true
        ]

        when:
        def result = moreOperationManager.getInstanceMoreOperations(serviceManager, functionPrivilegeRst, workflowInstance)

        then:
        result != null
        result.size() >= 0
    }

    def "test getTaskMoreOperations with execution type"() {
        given: "TaskDetail with execution type"
        def taskDetail = Mock(TaskDetail)
        taskDetail.getEntityId() >> "AccountObj"
        taskDetail.getExecutionType() >> ExecutionTypeEnum.execution

        def task = Mock(Task)

        when:
        def result = moreOperationManager.getTaskMoreOperations(serviceManager, taskDetail, task)

        then:
        result != null
        result.size() == 0  // execution type should return empty list
    }

}
