package com.facishare.bpm.manager

import com.facishare.bpm.exception.BPMLoopBreakerException
import com.facishare.bpm.manage.BPMLoopBreakerManager
import com.facishare.bpm.manage.RedisManager
import com.facishare.bpm.util.SwitchConfigManager
import com.facishare.rest.core.model.RemoteContext
import spock.lang.Specification

/**
 * Test for BPMLoopBreakerManager
 */
class BPMLoopBreakerManagerTest extends Specification {

    def bpmLoopBreakerManager = new BPMLoopBreakerManager()
    def redisManager = Mock(RedisManager)
    def context = Mock(RemoteContext)

    def setup() {
        bpmLoopBreakerManager.redisManager = redisManager
        context.getTenantId() >> "12345"
    }

    def "test incAndCheck with successful first set"() {
        given: "first time setting the key"
        def outlineId = "outline123"
        def entityId = "AccountObj"
        def objectId = "obj123"
        def expectedKey = "12345_AccountObj_obj123_outline123"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 5 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> true

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        0 * redisManager.incrBy(_, _)
        noExceptionThrown()
    }

    def "test incAndCheck with key already exists and count below limit"() {
        given: "key already exists with count below limit"
        def outlineId = "outline123"
        def entityId = "AccountObj"
        def objectId = "obj123"
        def expectedKey = "12345_AccountObj_obj123_outline123"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 5 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> false
        redisManager.incrBy(expectedKey, 1L) >> 3L

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        1 * redisManager.incrBy(expectedKey, 1L)
        noExceptionThrown()
    }

    def "test incAndCheck with count at limit"() {
        given: "key already exists with count at limit"
        def outlineId = "outline123"
        def entityId = "AccountObj"
        def objectId = "obj123"
        def expectedKey = "12345_AccountObj_obj123_outline123"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 5 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> false
        redisManager.incrBy(expectedKey, 1L) >> 5L

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        1 * redisManager.incrBy(expectedKey, 1L)
        noExceptionThrown()
    }

    def "test incAndCheck with count exceeding limit"() {
        given: "key already exists with count exceeding limit"
        def outlineId = "outline123"
        def entityId = "AccountObj"
        def objectId = "obj123"
        def expectedKey = "12345_AccountObj_obj123_outline123"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 5 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> false
        redisManager.incrBy(expectedKey, 1L) >> 6L

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        1 * redisManager.incrBy(expectedKey, 1L)
        thrown(BPMLoopBreakerException)
    }

    def "test incAndCheck with different parameters"() {
        given: "different parameters for key generation"
        def outlineId = "outline456"
        def entityId = "ContactObj"
        def objectId = "obj456"
        def expectedKey = "12345_ContactObj_obj456_outline456"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 600 }, getLimitCount: { 10 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 600) >> true

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 600)
        0 * redisManager.incrBy(_, _)
        noExceptionThrown()
    }

    def "test incAndCheck with high limit count"() {
        given: "high limit count scenario"
        def outlineId = "outline789"
        def entityId = "LeadObj"
        def objectId = "obj789"
        def expectedKey = "12345_LeadObj_obj789_outline789"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 100 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> false
        redisManager.incrBy(expectedKey, 1L) >> 50L

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        1 * redisManager.incrBy(expectedKey, 1L)
        noExceptionThrown()
    }

    def "test incAndCheck with edge case count exactly exceeding limit"() {
        given: "count exactly one more than limit"
        def outlineId = "outline999"
        def entityId = "OpportunityObj"
        def objectId = "obj999"
        def expectedKey = "12345_OpportunityObj_obj999_outline999"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 3 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> false
        redisManager.incrBy(expectedKey, 1L) >> 4L

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        1 * redisManager.incrBy(expectedKey, 1L)
        thrown(BPMLoopBreakerException)
    }

    def "test incAndCheck with zero count returned"() {
        given: "zero count returned from increment"
        def outlineId = "outline000"
        def entityId = "CaseObj"
        def objectId = "obj000"
        def expectedKey = "12345_CaseObj_obj000_outline000"

        // Mock static method
        GroovyMock(SwitchConfigManager, global: true)
        SwitchConfigManager.getSwitchConfig() >> [getDurationSeconds: { 300 }, getLimitCount: { 5 }]

        redisManager.setValueWithExpireTime(expectedKey, "1", 300) >> false
        redisManager.incrBy(expectedKey, 1L) >> 0L

        when:
        bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)

        then:
        1 * redisManager.setValueWithExpireTime(expectedKey, "1", 300)
        1 * redisManager.incrBy(expectedKey, 1L)
        noExceptionThrown()
    }
}
