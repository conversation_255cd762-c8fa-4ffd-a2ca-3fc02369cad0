package com.facishare.bpm.manager

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import com.facishare.bpm.bpmn.VariableExt
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.manage.impl.DefineGenerateManagerImpl
import com.facishare.bpm.model.WorkflowOutline
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification

class DefineGenerateManagerTest  extends Specification{
    def "generateActivities"() {
        given : ""
        DefineGenerateManagerImpl test = new DefineGenerateManagerImpl()
        def t1 = ['type' : 'userTask1'] as ActivityExt
        def t2 = ['type' : 'userTask'] as ActivityExt
        def t2extension = ['executionType':'addMDObject','relatedEntityId':'123','entityId':'123']
        t2.put('bpmExtension', t2extension)
        def t3 = ['type' : 'userTask'] as ActivityExt
        def t3extension = ['executionType':'addMDObject','entityId':'1235']
        t3.put('bpmExtension', t3extension)
        def t4 = ['type' : 'userTask'] as ActivityExt
        def t4extension = ['executionType':'approve']
        t4.put('bpmExtension', t4extension)
        List<ActivityExt> tasks = Lists.newArrayList(t1,t2,t3,t4)
        Set<VariableExt> variableExts = Sets.newHashSet()
        when:
        test.generateActivities(tasks, variableExts)

        then:
        variableExts.size()==5
    }

    def "generateVariables"() {
        given : ""
        def refServiceManager = Mock(RefServiceManager.class)
        DefineGenerateManagerImpl test = new DefineGenerateManagerImpl()
        ExecutableWorkflowExt ext = new ExecutableWorkflowExt()
        ext.setActivities(Lists.newArrayList())
        ext.setTransitions(Lists.newArrayList())
        ext.setVariables(Lists.newArrayList())
        WorkflowOutline outline = new WorkflowOutline()
        outline.setWorkflow(ext)
        refServiceManager.getTenantId() >> "71557"

        when:
        test.generateVariables(outline, refServiceManager)

        then:
        1==1
    }

    def "generateVariablesType 1"() {
        given: ""
        def variables = [
                new VariableExt(id: "field##entity1##field1", type: ['name':'111']),
                new VariableExt(id: "field##entity2##field2", typeName: "wrongType")
        ]
        def serviceManager = Mock(RefServiceManager.class)
        def fieldDesc = [type: "actualType", "field_label": "Field Label"]
        serviceManager.getFieldDesc("entity1", "field1") >> fieldDesc

        when: "Generating variables type"
        new DefineGenerateManagerImpl().generateVariablesType(variables, serviceManager)

        then: "The service manager is called correctly and errors are handled"
        thrown(BPMWorkflowDefVerifyException)
    }

    def "generateVariablesType 2"() {
        given: ""
        def variables = [
                new VariableExt(id: "field##entity1##field1", type: ['name':'111'])
        ]
        def serviceManager = Mock(RefServiceManager.class)
        serviceManager.getFieldDesc("entity1", "field1") >> null

        when: "Generating variables type"
        new DefineGenerateManagerImpl().generateVariablesType(variables, serviceManager)

        then: "The service manager is called correctly and errors are handled"
        1==1
    }

    def "setVarByDiff"() {
        given: ""
        def variables1 = [
                new VariableExt(id: "field##entity1##field1", type: ['name':'111']),
                new VariableExt(id: "field##entity1##field2", type: ['name':'111'])
        ] as Set
        def variables2 = [
                new VariableExt(id: "field##entity1##field1", type: ['name':'222']),
                new VariableExt(id: "field##entity1##field3", type: ['name':'111'])
        ]

        when: ""
        new DefineGenerateManagerImpl().setVarByDiff(null,null, null,variables1, variables2)

        then: "The service manager is called correctly and errors are handled"
        1==1
    }

    def "getEngineType"() {
        given:

        when:
        def res  = new DefineGenerateManagerImpl().getEngineType(fieldType)

        then: "The service manager is called correctly and errors are handled"
        res == result
        where:
        fieldType || result
        "number"||"number"
        "currency"||"number"
        "date"||"number"
        "date_time"||"number"
        "time"||"number"
        "percentile"||"number"
        "auto_number"||"text"
        "true_or_false" || "boolean"
        "select_many"||"list"
        "employee"||"list"
        "employee_many"||"list"
        "department"||"list"
        "department_many"||"list"
        "image"||"list"
        "file_attachment"||"list"
        "dimension"||"list"
        "dimension_d1"||"list"
        "dimension_d2"||"list"
        "dimension_d3"||"list"
        "dsfsf" ||"text"
    }


}
