package com.facishare.bpm.utils;

import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.util.MetaDataToWorkflowVariableUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date on 2018/6/22
 * @since 6.3
 */
public class VariableConvertUtils {
    @Test
    public void variableTest(){
        String apiName="AccountObj";
        Map<String,Object> data= Maps.newHashMap();
        data.put("Number__c1","10");
        List<VariableExt> variableList= Lists.newArrayList();
        VariableExt temp = new VariableExt("activity_0##AccountObj##Number__c1","number");
        variableList.add(temp);
        Map<String, Object> rst = MetaDataToWorkflowVariableUtil.convertValueType(apiName, data, variableList);
        System.out.println(JsonUtil.toJson(rst));
    }
}
