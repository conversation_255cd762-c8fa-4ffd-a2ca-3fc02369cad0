package com.facishare.bpm.utils

import com.fxiaoke.common.PasswordUtil
import spock.lang.Specification

/**
 * Created by <PERSON> on 13/06/2017.
 */
class MongoPassUtil extends Specification {
    def "测试"() {
        given: ""
        def rst=PasswordUtil.decode("550193428ACA9193E0113F604F7EE377ECEEED96A4FD9AC0837B9641E0F46BFC")
        when: ""
        println rst
        then: ""
        rst != null

    }

    def "获取密码"(){
        given:
        def datas=["fsdevops":"C2D9D3544279FBA585566BDE056F2E37746D0896E6265F1B",
                   "jenkins":"C2D9D3544279FBA585566BDE056F2E37746D0896E6265F1B",
                   "fssvc0035":"553017088A0A3B261444ED8FE3CDAF587765A2819F4F2BF05FCD44B1538C85AE",
                   fssvcb001:"5B83DA4A70D8F8828C49641D14A87513EFABC8B679F940718C1C53E78759A2D7",
                   "<EMAIL>":"9CB8BFE91D2ACDF474B2A2AF120BA90CA57369A379171A6D",
                   "fssvca037":"9CB8BFE91D2ACDF474B2A2AF120BA90CA57369A379171A6D"]
        when:


        datas.each{
            def db = PasswordUtil.decode(it.value)
            println it.key
            println db
        }

        then:
        println PasswordUtil.decode("")

    }
}