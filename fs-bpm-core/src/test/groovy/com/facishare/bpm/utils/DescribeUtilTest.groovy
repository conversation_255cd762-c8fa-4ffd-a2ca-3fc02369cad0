package com.facishare.bpm.utils

import com.facishare.bpm.RefServiceManager
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2/18/25
 * @apiNote
 *  */
class DescribeUtilTest extends Specification{

    def "test replaceDescFieldLabelByFieldMapping"() {
        given: "a RefServiceManager and a describe map"
        RefServiceManager serviceManager = Mock(RefServiceManager)
        Map<String, Object> describe = new HashMap<>()
        String recordType = "someRecordType"
        String apiName = "someApiName"
        Map<String, String> fieldMapping = new HashMap<>()
        fieldMapping.put("label", "newLabel")
        Map<String, Object> fieldMappingMap = new HashMap<>()
        fieldMappingMap.put("field1", fieldMapping)

        and: "setup the describe structure"
        Map<String, Map<String, Object>> fields = new HashMap<>()
        Map<String, Object> field1Desc = new HashMap<>()
        field1Desc.put("label", "oldLabel")
        fields.put("field1", field1Desc)
        describe.put("fields", fields)
        describe.put("api_name", apiName)

        and: "setup the mock to return fieldMapping"
        serviceManager.findRecordFieldMapping(apiName, recordType) >> fieldMappingMap

        when: "calling the method"
        DescribeUtil.replaceDescFieldLabelByFieldMapping(serviceManager, describe, recordType)

        then: "the label should be updated"
        fields.get("field1").get("label") == "newLabel"
    }

    def "test replaceDescFieldLabelByFieldMapping with empty describe"() {
        given: "a RefServiceManager and an empty describe map"
        RefServiceManager serviceManager = Mock(RefServiceManager)
        Map<String, Object> describe = new HashMap<>()
        String recordType = "someRecordType"

        when: "calling the method"
        DescribeUtil.replaceDescFieldLabelByFieldMapping(serviceManager, describe, recordType)

        then: "no exception should be thrown"
        noExceptionThrown()
    }

    def "test replaceDescFieldLabelByFieldMapping with null fieldMapping"() {
        given: "a RefServiceManager and a describe map with null fieldMapping"
        RefServiceManager serviceManager = Mock(RefServiceManager)
        Map<String, Object> describe = new HashMap<>()
        String recordType = "someRecordType"
        String apiName = "someApiName"
        Map<String, Object> fieldMappingMap = new HashMap<>()

        and: "setup the describe structure"
        Map<String, Map<String, Object>> fields = new HashMap<>()
        Map<String, Object> field1Desc = new HashMap<>()
        field1Desc.put("label", "oldLabel")
        fields.put("field1", field1Desc)
        describe.put("fields", fields)
        describe.put("api_name", apiName)

        and: "setup the mock to return null fieldMapping"
        serviceManager.findRecordFieldMapping(apiName, recordType) >> fieldMappingMap

        when: "calling the method"
        DescribeUtil.replaceDescFieldLabelByFieldMapping(serviceManager, describe, recordType)

        then: "the label should not be updated"
        fields.get("field1").get("label") == "oldLabel"
    }

}
