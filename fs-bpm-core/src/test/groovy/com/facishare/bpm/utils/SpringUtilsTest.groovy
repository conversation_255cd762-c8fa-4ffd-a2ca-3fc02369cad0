package com.facishare.bpm.utils

import org.springframework.context.ApplicationContext
import spock.lang.Specification

/**
 * Test for SpringUtils
 */
class SpringUtilsTest extends Specification {

    def springUtils = new SpringUtils()
    def mockApplicationContext = Mock(ApplicationContext)

    def setup() {
        // Reset the static application context before each test
        springUtils.destroy()
    }

    def cleanup() {
        // Clean up after each test
        try {
            springUtils.destroy()
        } catch (Exception e) {
            // Ignore cleanup exceptions
        }
    }

    def "test setApplicationContext"() {
        when:
        springUtils.setApplicationContext(mockApplicationContext)

        then:
        noExceptionThrown()
    }

    def "test getBean by name with injected context"() {
        given: "application context is set"
        def beanName = "testBean"
        def expectedBean = "testBeanInstance"
        mockApplicationContext.getBean(beanName) >> expectedBean
        springUtils.setApplicationContext(mockApplicationContext)

        when:
        def result = SpringUtils.getBean(beanName)

        then:
        result == expectedBean
    }

    def "test getBean by class with injected context"() {
        given: "application context is set"
        def beanClass = String.class
        def expectedBean = "testBeanInstance"
        mockApplicationContext.getBean(beanClass) >> expectedBean
        springUtils.setApplicationContext(mockApplicationContext)

        when:
        def result = SpringUtils.getBean(beanClass)

        then:
        result == expectedBean
    }

    def "test getBean by name without injected context throws exception"() {
        given: "no application context is set"
        def beanName = "testBean"

        when:
        SpringUtils.getBean(beanName)

        then:
        def exception = thrown(RuntimeException)
        exception.message == "springUtils applicationContext is not injected!"
    }

    def "test getBean by class without injected context throws exception"() {
        given: "no application context is set"
        def beanClass = String.class

        when:
        SpringUtils.getBean(beanClass)

        then:
        def exception = thrown(RuntimeException)
        exception.message == "springUtils applicationContext is not injected!"
    }

    def "test destroy method"() {
        given: "application context is set"
        springUtils.setApplicationContext(mockApplicationContext)

        when:
        springUtils.destroy()

        then:
        noExceptionThrown()

        when: "try to get bean after destroy"
        SpringUtils.getBean("testBean")

        then:
        def exception = thrown(RuntimeException)
        exception.message == "springUtils applicationContext is not injected!"
    }

    def "test multiple setApplicationContext calls"() {
        given: "multiple application contexts"
        def mockContext1 = Mock(ApplicationContext)
        def mockContext2 = Mock(ApplicationContext)
        def beanName = "testBean"
        def bean1 = "bean1"
        def bean2 = "bean2"

        mockContext1.getBean(beanName) >> bean1
        mockContext2.getBean(beanName) >> bean2

        when: "set first context"
        springUtils.setApplicationContext(mockContext1)
        def result1 = SpringUtils.getBean(beanName)

        then:
        result1 == bean1

        when: "set second context"
        springUtils.setApplicationContext(mockContext2)
        def result2 = SpringUtils.getBean(beanName)

        then:
        result2 == bean2
    }

    def "test getBean with empty bean name"() {
        given: "application context is set"
        def beanName = ""
        springUtils.setApplicationContext(mockApplicationContext)
        mockApplicationContext.getBean(beanName) >> { throw new IllegalArgumentException("Bean name cannot be empty") }

        when:
        SpringUtils.getBean(beanName)

        then:
        thrown(IllegalArgumentException)
    }

    def "test getBean with non-existent bean name"() {
        given: "application context is set"
        def beanName = "nonExistentBean"
        springUtils.setApplicationContext(mockApplicationContext)
        mockApplicationContext.getBean(beanName) >> { throw new org.springframework.beans.factory.NoSuchBeanDefinitionException(beanName) }

        when:
        SpringUtils.getBean(beanName)

        then:
        thrown(org.springframework.beans.factory.NoSuchBeanDefinitionException)
    }

    def "test getBean with non-existent bean class"() {
        given: "application context is set"
        def beanClass = List.class
        springUtils.setApplicationContext(mockApplicationContext)
        mockApplicationContext.getBean(beanClass) >> { throw new org.springframework.beans.factory.NoSuchBeanDefinitionException(beanClass) }

        when:
        SpringUtils.getBean(beanClass)

        then:
        thrown(org.springframework.beans.factory.NoSuchBeanDefinitionException)
    }

    def "test concurrent access to SpringUtils"() {
        given: "application context is set"
        def beanName = "testBean"
        def expectedBean = "testBeanInstance"
        mockApplicationContext.getBean(beanName) >> expectedBean
        springUtils.setApplicationContext(mockApplicationContext)

        when: "multiple threads access the bean"
        def results = []
        def threads = []
        
        for (int i = 0; i < 5; i++) {
            threads << Thread.start {
                results << SpringUtils.getBean(beanName)
            }
        }
        
        threads.each { it.join() }

        then:
        results.size() == 5
        results.every { it == expectedBean }
    }

    def "test type casting with getBean by name"() {
        given: "application context is set with a specific bean type"
        def beanName = "stringBean"
        def expectedBean = "testString"
        mockApplicationContext.getBean(beanName) >> expectedBean
        springUtils.setApplicationContext(mockApplicationContext)

        when:
        String result = SpringUtils.getBean(beanName)

        then:
        result == expectedBean
        result instanceof String
    }
}
