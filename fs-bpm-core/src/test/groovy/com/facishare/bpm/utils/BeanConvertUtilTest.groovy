package com.facishare.bpm.utils

import com.facishare.bpm.model.WorkflowOutline
import com.facishare.rest.core.model.RemoteContext
import spock.lang.Specification

/**
 * Test for BeanConvertUtil
 */
class BeanConvertUtilTest extends Specification {

    def "test toWorkflowOutline with null outline"() {
        given: "null outline"
        def outline = null
        def context = new RemoteContext()
        context.setUserId("user123")
        context.setTenantId("tenant123")

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result == null
    }

    def "test toWorkflowOutline with new outline (empty ID)"() {
        given: "new outline with empty ID"
        def outline = new WorkflowOutline()
        outline.setId("")
        def context = new RemoteContext()
        context.setUserId("user123")
        context.setTenantId("tenant123")
        def currentTime = System.currentTimeMillis()

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        result.getCreatedBy() == "user123"
        result.getCreateTime() >= currentTime
        result.getLastModifiedBy() == "user123"
        result.getLastModifiedTime() >= currentTime
        result.getTenantId() == "tenant123"
        result.getUserId() == "user123"
    }

    def "test toWorkflowOutline with new outline (null ID)"() {
        given: "new outline with null ID"
        def outline = new WorkflowOutline()
        outline.setId(null)
        def context = new RemoteContext()
        context.setUserId("user456")
        context.setTenantId("tenant456")
        def currentTime = System.currentTimeMillis()

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        result.getCreatedBy() == "user456"
        result.getCreateTime() >= currentTime
        result.getLastModifiedBy() == "user456"
        result.getLastModifiedTime() >= currentTime
        result.getTenantId() == "tenant456"
        result.getUserId() == "user456"
    }

    def "test toWorkflowOutline with existing outline (has ID)"() {
        given: "existing outline with ID"
        def outline = new WorkflowOutline()
        outline.setId("existing123")
        outline.setCreatedBy("originalUser")
        outline.setCreateTime(1000L)
        def context = new RemoteContext()
        context.setUserId("user789")
        context.setTenantId("tenant789")
        def currentTime = System.currentTimeMillis()

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        result.getId() == "existing123"
        result.getCreatedBy() == "originalUser" // Should not change
        result.getCreateTime() == 1000L // Should not change
        result.getLastModifiedBy() == "user789" // Should update
        result.getLastModifiedTime() >= currentTime // Should update
        result.getTenantId() == "tenant789"
        result.getUserId() == "user789"
    }

    def "test toWorkflowOutline with whitespace ID"() {
        given: "outline with whitespace ID"
        def outline = new WorkflowOutline()
        outline.setId("   ")
        def context = new RemoteContext()
        context.setUserId("userWhitespace")
        context.setTenantId("tenantWhitespace")
        def currentTime = System.currentTimeMillis()

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        // StringUtils.isEmpty("   ") returns false, so this is treated as existing outline
        result.getCreatedBy() == null // Should not be set for existing outline
        result.getCreateTime() == 0L // Should not be set for existing outline
        result.getLastModifiedBy() == "userWhitespace"
        result.getLastModifiedTime() >= currentTime
        result.getTenantId() == "tenantWhitespace"
        result.getUserId() == "userWhitespace"
    }

    def "test toWorkflowOutline preserves other properties"() {
        given: "outline with existing properties"
        def outline = new WorkflowOutline()
        outline.setId("test123")
        outline.setName("Test Workflow")
        outline.setDescription("Test Description")
        outline.setEnabled(true)
        def context = new RemoteContext()
        context.setUserId("user999")
        context.setTenantId("tenant999")

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        result.getId() == "test123"
        result.getName() == "Test Workflow"
        result.getDescription() == "Test Description"
        result.isEnabled() == true
        result.getLastModifiedBy() == "user999"
        result.getTenantId() == "tenant999"
        result.getUserId() == "user999"
    }

    def "test toWorkflowOutline with null context values"() {
        given: "context with null values"
        def outline = new WorkflowOutline()
        outline.setId("")
        def context = new RemoteContext()
        context.setUserId(null)
        context.setTenantId(null)

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        result.getCreatedBy() == null
        result.getLastModifiedBy() == null
        result.getTenantId() == null
        result.getUserId() == null
    }

    def "test toWorkflowOutline time consistency"() {
        given: "new outline"
        def outline = new WorkflowOutline()
        outline.setId("")
        def context = new RemoteContext()
        context.setUserId("timeUser")
        context.setTenantId("timeTenant")

        when:
        def result = BeanConvertUtil.toWorkflowOutline(outline, context)

        then:
        result != null
        result.getCreateTime() != null
        result.getLastModifiedTime() != null
        // Create time and last modified time should be very close (within 1 second)
        Math.abs(result.getLastModifiedTime() - result.getCreateTime()) < 1000
    }
}
