package com.facishare.bpm.utils

import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * Test for ListUtil
 */
class ListUtilTest extends Specification {

    def "test getSubList with normal case"() {
        given: "a list with 4 elements and chunk size 2"
        def source = [1, 2, 3, 4]
        def n = 2

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result != null
        result.size() == 3  // Implementation adds an extra empty list
        result[0] == [1, 2]
        result[1] == [3, 4]
        result[2] == []  // Empty list at the end
    }

    def "test getSubList with uneven division"() {
        given: "a list with 5 elements and chunk size 2"
        def source = [1, 2, 3, 4, 5]
        def n = 2

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result != null
        result.size() == 3
        result[0] == [1, 2]
        result[1] == [3, 4]
        result[2] == [5]
    }

    def "test getSubList with chunk size larger than list"() {
        given: "a list with 3 elements and chunk size 5"
        def source = [1, 2, 3]
        def n = 5

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result != null
        result.size() == 1
        result[0] == [1, 2, 3]
    }

    def "test getSubList with chunk size 1"() {
        given: "a list with 3 elements and chunk size 1"
        def source = [1, 2, 3]
        def n = 1

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result != null
        result.size() == 4  // Implementation adds an extra empty list
        result[0] == [1]
        result[1] == [2]
        result[2] == [3]
        result[3] == []  // Empty list at the end
    }

    def "test getSubList with null source"() {
        given: "null source"
        def source = null
        def n = 2

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result == null
    }

    def "test getSubList with empty source"() {
        given: "empty source"
        def source = []
        def n = 2

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result == null
    }

    def "test getSubList with zero chunk size"() {
        given: "valid source and zero chunk size"
        def source = [1, 2, 3]
        def n = 0

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result == null
    }

    def "test getSubList with negative chunk size"() {
        given: "valid source and negative chunk size"
        def source = [1, 2, 3]
        def n = -1

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result == null
    }

    def "test getAverageList with 6 elements into 3 groups"() {
        given: "a list with 6 elements divided into 3 groups"
        def source = [1, 2, 3, 4, 5, 6]
        def n = 3

        when:
        def result = ListUtil.getAverageList(source, n)

        then:
        result != null
        result.size() == 3
        result[0] == [1, 2]
        result[1] == [3, 4]
        result[2] == [5, 6]
    }

    def "test getAverageList with uneven distribution"() {
        given: "a list with 7 elements divided into 3 groups"
        def source = [1, 2, 3, 4, 5, 6, 7]
        def n = 3

        when:
        def result = ListUtil.getAverageList(source, n)

        then:
        result != null
        result.size() == 3
        result[0] == [1, 2, 3]  // Gets extra element
        result[1] == [4, 5]
        result[2] == [6, 7]
    }

    def "test getAverageList with 8 elements into 3 groups"() {
        given: "a list with 8 elements divided into 3 groups"
        def source = [1, 2, 3, 4, 5, 6, 7, 8]
        def n = 3

        when:
        def result = ListUtil.getAverageList(source, n)

        then:
        result != null
        result.size() == 3
        result[0] == [1, 2, 3]  // Gets extra element
        result[1] == [4, 5, 6]  // Gets extra element
        result[2] == [7, 8]
    }

    def "test getAverageList with single group"() {
        given: "a list with 5 elements divided into 1 group"
        def source = [1, 2, 3, 4, 5]
        def n = 1

        when:
        def result = ListUtil.getAverageList(source, n)

        then:
        result != null
        result.size() == 1
        result[0] == [1, 2, 3, 4, 5]
    }

    def "test getAverageList with more groups than elements"() {
        given: "a list with 2 elements divided into 5 groups"
        def source = [1, 2]
        def n = 5

        when:
        def result = ListUtil.getAverageList(source, n)

        then:
        result != null
        result.size() == 5
        result[0] == [1]
        result[1] == [2]
        result[2] == []
        result[3] == []
        result[4] == []
    }

    def "test combineSubList with normal case"() {
        given: "a list of sublists"
        def source = [
            [1, 2],
            [3, 4],
            [5, 6]
        ]

        when:
        def result = ListUtil.combineSubList(source)

        then:
        result != null
        result.size() == 6
        result.containsAll([1, 2, 3, 4, 5, 6])
    }

    def "test combineSubList with duplicates"() {
        given: "a list of sublists with duplicates"
        def source = [
            [1, 2],
            [2, 3],
            [3, 4]
        ]

        when:
        def result = ListUtil.combineSubList(source)

        then:
        result != null
        result.size() == 4  // Set removes duplicates
        result.containsAll([1, 2, 3, 4])
    }

    def "test combineSubList with empty sublists"() {
        given: "a list containing empty sublists"
        def source = [
            [1, 2],
            [],
            [3, 4]
        ]

        when:
        def result = ListUtil.combineSubList(source)

        then:
        result != null
        result.size() == 4
        result.containsAll([1, 2, 3, 4])
    }

    def "test combineSubList with null source"() {
        given: "null source"
        def source = null

        when:
        def result = ListUtil.combineSubList(source)

        then:
        result != null
        result.isEmpty()
    }

    def "test combineSubList with empty source"() {
        given: "empty source"
        def source = []

        when:
        def result = ListUtil.combineSubList(source)

        then:
        result != null
        result.isEmpty()
    }

    def "test combineSubList with single sublist"() {
        given: "a list with single sublist"
        def source = [
            [1, 2, 3, 4, 5]
        ]

        when:
        def result = ListUtil.combineSubList(source)

        then:
        result != null
        result.size() == 5
        result.containsAll([1, 2, 3, 4, 5])
    }

    def "test with string elements"() {
        given: "string elements"
        def source = ["a", "b", "c", "d", "e"]
        def n = 2

        when:
        def result = ListUtil.getSubList(source, n)

        then:
        result != null
        result.size() == 3
        result[0] == ["a", "b"]
        result[1] == ["c", "d"]
        result[2] == ["e"]
    }
}
