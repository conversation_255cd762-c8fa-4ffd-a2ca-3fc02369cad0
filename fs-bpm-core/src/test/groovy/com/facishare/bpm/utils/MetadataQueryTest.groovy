package com.facishare.bpm.utils

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.model.paas.engine.bpm.InstanceState
import com.facishare.bpm.model.paas.engine.bpm.Page
import com.google.common.collect.Lists
import spock.lang.Specification

class MetadataQueryTest extends Specification{
    def "canRemindTask null"() {
        when:
        RefServiceManager serviceManager = Mock(RefServiceManager)
        serviceManager.isOuterUserId() >> true
        Page page = new Page()
        page.pageSize = 20
        page.pageNumber = 1
        MetadataQuery.getInProgressOrActivityIdTasks("123", Lists.newArrayList("1","2"), page)
        MetadataQuery.getNormalTasks("123", Lists.newArrayList("1","2"), page)
        MetadataQuery.getInstanceByObjectId(serviceManager, "123", "123456", InstanceState.error, 1, 20)
        then:
        1==1
    }

}
