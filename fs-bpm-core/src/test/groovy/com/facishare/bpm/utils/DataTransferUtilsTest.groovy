package com.facishare.bpm.utils

import com.facishare.bpm.model.paas.engine.bpm.InstanceState
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance
import com.facishare.bpm.utils.model.Pair
import com.facishare.flow.mongo.bizdb.entity.LaneEntity
import com.facishare.flow.mongo.bizdb.entity.PoolEntity
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Test for DataTransferUtils
 */
class DataTransferUtilsTest extends Specification {

    def "test getStage with matching activity"() {
        given: "pools with lanes containing activities"
        def lane1 = new LaneEntity()
        lane1.setId("lane1")
        lane1.setName("Stage 1")
        lane1.setActivities(["activity1", "activity2"])

        def lane2 = new LaneEntity()
        lane2.setId("lane2")
        lane2.setName("Stage 2")
        lane2.setActivities(["activity3", "activity4"])

        def pool = new PoolEntity()
        pool.setLanes([lane1, lane2])
        def pools = [pool]

        when:
        def result = DataTransferUtils.getStage("activity3", pools)

        then:
        result != null
        result.getKey() == "lane2"
        result.getValue() == "Stage 2"
    }

    def "test getStage with non-matching activity"() {
        given: "pools with lanes not containing the activity"
        def lane1 = new LaneEntity()
        lane1.setId("lane1")
        lane1.setName("Stage 1")
        lane1.setActivities(["activity1", "activity2"])

        def pool = new PoolEntity()
        pool.setLanes([lane1])
        def pools = [pool]

        when:
        def result = DataTransferUtils.getStage("activity999", pools)

        then:
        result != null
        result.getKey() == ""
        result.getValue() == ""
    }

    def "test getStage with empty pools"() {
        given: "empty pools list"
        def pools = []

        when:
        def result = DataTransferUtils.getStage("activity1", pools)

        then:
        result != null
        result.getKey() == ""
        result.getValue() == ""
    }

    def "test getStage with null lanes"() {
        given: "pool with null lanes"
        def pool = new PoolEntity()
        pool.setLanes(null)
        def pools = [pool]

        when:
        def result = DataTransferUtils.getStage("activity1", pools)

        then:
        result != null
        result.getKey() == ""
        result.getValue() == ""
    }

    def "test getStage with null activities in lane"() {
        given: "lane with null activities"
        def lane = new LaneEntity()
        lane.setId("lane1")
        lane.setName("Stage 1")
        lane.setActivities(null)

        def pool = new PoolEntity()
        pool.setLanes([lane])
        def pools = [pool]

        when:
        def result = DataTransferUtils.getStage("activity1", pools)

        then:
        result != null
        result.getKey() == ""
        result.getValue() == ""
    }

    def "test getStage with multiple pools"() {
        given: "multiple pools with different lanes"
        def lane1 = new LaneEntity()
        lane1.setId("lane1")
        lane1.setName("Stage 1")
        lane1.setActivities(["activity1"])

        def lane2 = new LaneEntity()
        lane2.setId("lane2")
        lane2.setName("Stage 2")
        lane2.setActivities(["activity2"])

        def pool1 = new PoolEntity()
        pool1.setLanes([lane1])

        def pool2 = new PoolEntity()
        pool2.setLanes([lane2])

        def pools = [pool1, pool2]

        when:
        def result = DataTransferUtils.getStage("activity2", pools)

        then:
        result != null
        result.getKey() == "lane2"
        result.getValue() == "Stage 2"
    }

    def "test getThemeFromNameI18NMap with valid data"() {
        given: "i18n data map and time"
        def i18nData = [
            "en": "English Name",
            "zh": "中文名称",
            "ja": "日本語名"
        ]
        def time = 1640995200000L // 2022-01-01 00:00:00

        when:
        def result = DataTransferUtils.getThemeFromNameI18NMap(i18nData, time)

        then:
        result != null
        result.size() == 3
        result["en"] == "English Name(2022-01-01 08:00)"
        result["zh"] == "中文名称(2022-01-01 08:00)"
        result["ja"] == "日本語名(2022-01-01 08:00)"
    }

    def "test getThemeFromNameI18NMap with empty data"() {
        given: "empty i18n data map"
        def i18nData = [:]
        def time = 1640995200000L

        when:
        def result = DataTransferUtils.getThemeFromNameI18NMap(i18nData, time)

        then:
        result == null
    }

    def "test getThemeFromNameI18NMap with null data"() {
        given: "null i18n data map"
        def i18nData = null
        def time = 1640995200000L

        when:
        def result = DataTransferUtils.getThemeFromNameI18NMap(i18nData, time)

        then:
        result == null
    }

    def "test transferInstance with new instance"() {
        given: "new workflow instance"
        def workflowInstance = new WorkflowInstance()
        workflowInstance.setId("instance123")
        workflowInstance.setWorkflowName("Test Workflow")
        workflowInstance.setSourceWorkflowId("source123")
        workflowInstance.setEntityId("AccountObj")
        workflowInstance.setObjectId("obj123")
        workflowInstance.setWorkflowId("workflow123")
        workflowInstance.setExternalFlow(0)
        workflowInstance.setStart(1640995200000L)
        workflowInstance.setApplicantId("user123")
        workflowInstance.setState(InstanceState.in_progress)
        workflowInstance.setVariables(Maps.newHashMap())
        workflowInstance.setActivityInstances(Lists.newArrayList())

        def pools = Lists.newArrayList()
        def isNew = true
        def referenceObjectIds = [] as Set
        def actualDuration = 3600000L

        when:
        def result = DataTransferUtils.transferInstance(workflowInstance, pools, isNew, referenceObjectIds, actualDuration)

        then:
        result != null
        result.get_id() == "instance123"
        result.getSourceWorkflowId() == "source123"
        result.getObjectApiName() == "AccountObj"
        result.getObjectDataId() == "obj123"
        result.getWorkflowId() == "workflow123"
        result.getExternalFlow() == 0
        result.getWorkflowName() == "Test Workflow"
        result.getStartTime() == 1640995200000L
        result.getApplicantId().contains("user123")
        result.getState() == InstanceState.in_progress
    }

    def "test transferInstance with completed instance"() {
        given: "completed workflow instance"
        def workflowInstance = new WorkflowInstance()
        workflowInstance.setId("instance456")
        workflowInstance.setState(InstanceState.pass)
        workflowInstance.setStart(1640995200000L)
        workflowInstance.setEnd(1640998800000L) // 1 hour later
        workflowInstance.setDuration("3600000")
        workflowInstance.setVariables(Maps.newHashMap())
        workflowInstance.setActivityInstances(Lists.newArrayList())

        def pools = Lists.newArrayList()
        def isNew = false
        def referenceObjectIds = [] as Set
        def actualDuration = 3600000L

        when:
        def result = DataTransferUtils.transferInstance(workflowInstance, pools, isNew, referenceObjectIds, actualDuration)

        then:
        result != null
        result.get_id() == "instance456"
        result.getState() == InstanceState.pass
        result.getEndTime() == 1640998800000L
        result.getDuration() == 3600000L
        result.getActual_duration() == 3600000L
    }

    def "test transferInstance with cancelled instance"() {
        given: "cancelled workflow instance"
        def workflowInstance = new WorkflowInstance()
        workflowInstance.setId("instance789")
        workflowInstance.setState(InstanceState.cancel)
        workflowInstance.setEnd(1640998800000L)
        workflowInstance.setReason("User cancelled")
        workflowInstance.setCancelPerson("user456")
        workflowInstance.setVariables(Maps.newHashMap())
        workflowInstance.setActivityInstances(Lists.newArrayList())

        def pools = Lists.newArrayList()
        def isNew = false
        def referenceObjectIds = [] as Set
        def actualDuration = 1800000L

        when:
        def result = DataTransferUtils.transferInstance(workflowInstance, pools, isNew, referenceObjectIds, actualDuration)

        then:
        result != null
        result.get_id() == "instance789"
        result.getState() == InstanceState.cancel
        result.getEndTime() == 1640998800000L
        result.getCancel_reason() == "User cancelled"
        result.getCancel_from_person().contains("user456")
        result.getActual_duration() == 1800000L
    }

    def "test transferInstance with error instance"() {
        given: "error workflow instance"
        def workflowInstance = new WorkflowInstance()
        workflowInstance.setId("instanceError")
        workflowInstance.setState(InstanceState.error)
        workflowInstance.setEnd(1640998800000L)
        workflowInstance.setErrMsg("System error occurred")
        workflowInstance.setVariables(Maps.newHashMap())
        workflowInstance.setActivityInstances(Lists.newArrayList())

        def pools = Lists.newArrayList()
        def isNew = false
        def referenceObjectIds = [] as Set
        def actualDuration = 900000L

        when:
        def result = DataTransferUtils.transferInstance(workflowInstance, pools, isNew, referenceObjectIds, actualDuration)

        then:
        result != null
        result.get_id() == "instanceError"
        result.getState() == InstanceState.error
        result.getEndTime() == 1640998800000L
        result.getError_reason() == "System error occurred"
        result.getActual_duration() == 900000L
    }
}
