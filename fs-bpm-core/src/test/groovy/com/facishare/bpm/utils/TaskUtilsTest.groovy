package com.facishare.bpm.utils

import com.facishare.bpm.define.conf.DefineConfigHelper
import com.facishare.bpm.exception.BPMChangeApproverException
import com.facishare.bpm.exception.BPMDeployException
import com.facishare.bpm.exception.BPMParamsException
import com.facishare.bpm.exception.BPMTaskExecuteException
import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.paas.engine.bpm.TaskState
import com.facishare.bpm.remote.model.config.BPMObjectSupportConfig
import com.facishare.bpm.utils.helper.StopWatch
import com.facishare.bpm.utils.model.FlowType
import com.facishare.rest.core.model.RemoteContext
import com.google.common.collect.Lists
import spock.lang.Specification


/**
 * Created by <PERSON> on 23/05/2017.
 */
class TaskUtilsTest extends Specification {

    def "测试上下游排序"() {
        when:
        TaskUtils.sortPersons(isUpStreamTenant, persons)
        then:
        rst == persons
        where:
        desc            | isUpStreamTenant | persons                                                         || rst
        "上下游都存在,且当前为上游" | true             | ["100000010", "1003", "100000088", "1009", "100000009", "1022"] || ["1003", "1009", "1022", "100000010", "100000088", "100000009"]
        "上下游都存在,且当前为下游" | false            | ["100000010", "1003", "100000088", "1009", "100000009", "1022"] || ["100000009", "100000088", "100000010", "1022", "1009", "1003"]


    }

    def "test isHidden with field access #access"() {
        given: "a fieldsAuth map and an item map"
        Map<String, Integer> fieldsAuth = ["fieldName": access]
        Map<String, Object> item = ["name": "fieldName"]

        when: "calling the isHidden method"
        boolean hidden = TaskUtils.isHidden(fieldsAuth, item)

        then: "the result should be as expected"
        hidden == expectedHidden

        where:
        access | expectedHidden
        0      | true
        1      | false
        2      | false
        -1     | false
    }

    def "test isHidden with field name not in fieldsAuth"() {
        given: "a fieldsAuth map without the item's field name"
        Map<String, Integer> fieldsAuth = [:]
        Map<String, Object> item = ["name": "fieldName"]

        when: "calling the isHidden method"
        boolean hidden = TaskUtils.isHidden(fieldsAuth, item)

        then: "the result should be false"
        !hidden
    }

    def "test validateAndDistinctCandidateIds"() {
        given: "a fieldsAuth map without the item's field name"

        when: "calling the isHidden method"
        Task task = new Task()
        task.appId = appId
        try {
            TaskUtils.validateAndDistinctCandidateIds(task, candidateIds)
        }catch (Exception e){
        }

        then: "the result should be false"
        1==1

        where:
        candidateIds | appId || result
        ['1-1'] | null  || BPMParamsException
        ['1'] | null || BPMChangeApproverException
        ['1'] | 'BPM' || null
    }

    def "test validateCompleteTask"() {
        when: "calling the isHidden method"
        Task task = new Task()
        task.state = TaskState.in_progress
        task.assigneeIds = assigneeIds
        task.candidateIds = candidateIds
        def context = Mock(RemoteContext.class)
        context.getOuterUserId() >> 123456789L
        try {
            TaskUtils.validateCompleteTask(context, task)
        }catch (Exception e){
        }
        then: "the result should be false"
        1==1
        where:
        assigneeIds  | candidateIds
        ['123456789']   | null
        ['1']  | null
        ['1']  | ['1']
    }

    def "test assertTaskCompleted"() {
        when:
        Task task = new Task()
        task.completed = completed
        task.state = state
        TaskUtils.assertTaskCompleted(task)
        then: "the result should be false"
        thrown(result)

        where:
        completed  | state || result
        false  | TaskState.pass  || BPMTaskExecuteException
        false  | TaskState.cancel ||BPMTaskExecuteException
    }

    def "should return empty list when data or desc is null"() {
        when:
        def result = TaskUtils.verifyRequiredFields(null, null)

        then:
        result.isEmpty()
    }

    def "should return empty list when no required fields are missing"() {
        setup:
        def data = ['field1': 'value1', 'field2': 'value2']
        def desc = [
                (BPMConstants.MetadataKey.fields): [
                        field1: [isRequired: true, isActive: true, label: 'Field 1'],
                        field2: [isRequired: true, isActive: true, label: 'Field 2']
                ]
        ]

        when:
        def result = TaskUtils.verifyRequiredFields(data, desc)

        then:
        result.isEmpty()
    }

    def "should handle country cascade fields correctly"() {
        setup:
        def data = ['country': '']
        def desc = [
                (BPMConstants.MetadataKey.fields): [
                        country: [is_required: true, is_active: true, label: 'country'],
                        field1: [is_required: true, is_active: true, label: 'Field 1']
                ]
        ]

        when:
        def result = TaskUtils.verifyRequiredFields(data, desc)

        then:
        result == ['国家、省、市、区','Field 1']
    }

    def "getTaskOutlineFromTasks"() {
        given:
        Task task = new Task()
        def tasks = Lists.newArrayList(task)
        def func = { entityTaskMaps -> println 1}
        StopWatch stopWatch = StopWatch.create("getHandleTaskList");
        def func2 = { { task2, taskOutline, instance, extension -> println 1 } }

        when:
        try {
            TaskUtils.getTaskOutlineFromTasks(tasks, null, null, func2, func, stopWatch)
        }catch(Exception e){

        }
        then:
        1==1
    }

    def "checkBusinessCode"() {
        given:
        when:
        WorkflowPluginUtil.checkBusinessCode(null)
        then:
        thrown(BPMDeployException)
    }




    def "toWorkflowOutline"() {
        when:
        def context = Mock(RemoteContext.class)
        WorkflowOutline line = new WorkflowOutline()

        BeanConvertUtil.toWorkflowOutline(line, context)
        then:
        1==1
    }

    def "test sort with empty list"() {
        given: "an empty list and objectSupports"
        BPMObjectSupportConfig objectSupports = Mock(BPMObjectSupportConfig.class)
        List<Map<String, Object>> lists = Collections.emptyList()

        when: "calling the method"
        List<Map<String, Object>> result = ObjectDescribeListSortUtil.sort("tenantId", objectSupports, lists)

        then: "the result should be the same as the input"
        result == lists
    }

    def "test sort with non-empty list"() {
        given: "a non-empty list and objectSupports"
        BPMObjectSupportConfig objectSupports = Mock(BPMObjectSupportConfig)
        objectSupports.getObjectBaseConfig() >> ["sfa1": "", "sfa2": ""]
        objectSupports.getWhiteList() >> ["preset1", "preset2"]
        DefineConfigHelper.getObjectSupportByType("tenantId", FlowType.workflow_bpm) >> ["preset3"]

        List<Map<String, Object>> lists = [
                [(BPMConstants.MetadataKey.apiName): "sfa1"],
                [(BPMConstants.MetadataKey.apiName): "preset1"],
                [(BPMConstants.MetadataKey.apiName): "preset2"],
                [(BPMConstants.MetadataKey.apiName): "preset3"],
                [(BPMConstants.MetadataKey.apiName): "sfa2"],
                [(BPMConstants.MetadataKey.apiName): "custom1"],
                [(BPMConstants.MetadataKey.apiName): "custom2"]
        ]

        when: "calling the method"
        List<Map<String, Object>> result = ObjectDescribeListSortUtil.sort("tenantId", objectSupports, lists)

        then: "the result should be sorted and categorized correctly"
        result.size() == 7
        result[0][(BPMConstants.MetadataKey.apiName)] == "sfa1"
        result[1][(BPMConstants.MetadataKey.apiName)] == "sfa2"
        result[2][(BPMConstants.MetadataKey.apiName)] == "preset1"
        result[3][(BPMConstants.MetadataKey.apiName)] == "preset2"
        result[4][(BPMConstants.MetadataKey.apiName)] == "preset3"
        result[5][(BPMConstants.MetadataKey.apiName)] == "custom1"
        result[6][(BPMConstants.MetadataKey.apiName)] == "custom2"
    }

}
