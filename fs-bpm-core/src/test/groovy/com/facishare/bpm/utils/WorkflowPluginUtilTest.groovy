package com.facishare.bpm.utils

import com.facishare.bpm.exception.BPMBusinessExceptionCode
import com.facishare.bpm.exception.BPMDeployException
import com.facishare.flow.mongo.bizdb.entity.SupportFlow
import spock.lang.Specification

/**
 * Test for WorkflowPluginUtil
 */
class WorkflowPluginUtilTest extends Specification {

    def "test checkBusinessCode with valid SupportFlow"() {
        given: "a valid SupportFlow enum"
        def supportFlow = SupportFlow.market

        when:
        WorkflowPluginUtil.checkBusinessCode(supportFlow)

        then:
        noExceptionThrown()
    }

    def "test checkBusinessCode with null SupportFlow"() {
        given: "null SupportFlow"
        def supportFlow = null

        when:
        WorkflowPluginUtil.checkBusinessCode(supportFlow)

        then:
        def exception = thrown(BPMDeployException)
        exception.getErrorCode() == 310011006
    }

    def "test checkBusinessCode with different SupportFlow enum values"() {
        given: "different SupportFlow enum values"
        def supportFlow = SupportFlow.market

        when:
        WorkflowPluginUtil.checkBusinessCode(supportFlow)

        then:
        noExceptionThrown()
    }

    def "test checkBusinessCode multiple times with same enum"() {
        given: "a valid SupportFlow enum"
        def supportFlow = SupportFlow.market

        when: "check multiple times"
        WorkflowPluginUtil.checkBusinessCode(supportFlow)
        WorkflowPluginUtil.checkBusinessCode(supportFlow)
        WorkflowPluginUtil.checkBusinessCode(supportFlow)

        then:
        noExceptionThrown()
    }

    def "test checkBusinessCode exception details"() {
        given: "null SupportFlow"
        def supportFlow = null

        when:
        WorkflowPluginUtil.checkBusinessCode(supportFlow)

        then:
        def exception = thrown(BPMDeployException)
        exception.getErrorCode() == 310011006
        exception.getMessage() != null
        exception.getMessage().length() > 0
    }


}
