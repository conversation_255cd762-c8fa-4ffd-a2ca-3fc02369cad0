package com.facishare.bpm.utils

import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification
import spock.lang.Unroll


class CalculateTaskHandleTimeDetailUtilTest extends Specification {


    @Unroll
    def "test"() {
        when:
        def modifyInfo = CalculateTaskHandleTimeDetailUtil.getTaskHandleTimeDetailModifyInfo(taskInfo, operateId)
        then:
        def modifyInfoRes = rst
        Objects.nonNull(modifyInfoRes)
        def size = CollectionUtils.isEmpty(modifyInfo.getUpdate()) ? 0 : modifyInfo.getUpdate().size()
        def sizeRes = CollectionUtils.isEmpty(modifyInfoRes.getUpdate()) ? 0 : modifyInfoRes.getUpdate().size()
        size == sizeRes
        def flag = true
        for (int i = 0 ; i < size ; i++){
            flag = flag && modifyInfoRes.getUpdate().get(i).get_id() == modifyInfo.getUpdate().get(i).get_id()
            flag = flag && modifyInfoRes.getUpdate().get(i).getState() == modifyInfo.getUpdate().get(i).getState()
            flag = flag && modifyInfoRes.getUpdate().get(i).getStart_time() == modifyInfo.getUpdate().get(i).getStart_time()
            flag = flag && modifyInfoRes.getUpdate().get(i).getEnd_time() == modifyInfo.getUpdate().get(i).getEnd_time()
        }
        flag
        def size1 = CollectionUtils.isEmpty(modifyInfo.getAdd()) ? 0 : modifyInfo.getAdd().size()
        def sizeRes1 = CollectionUtils.isEmpty(modifyInfoRes.getAdd()) ? 0 : modifyInfoRes.getAdd().size()
        size1 == sizeRes1
        for (int i = 0 ; i < size1 ; i++){
            flag = flag && modifyInfoRes.getAdd().get(i).get_id() == modifyInfo.getAdd().get(i).get_id()
            flag = flag && modifyInfoRes.getAdd().get(i).getState() == modifyInfo.getAdd().get(i).getState()
            flag = flag && modifyInfoRes.getAdd().get(i).getStart_time() == modifyInfo.getAdd().get(i).getStart_time()
            flag = flag && modifyInfoRes.getAdd().get(i).getEnd_time() == modifyInfo.getAdd().get(i).getEnd_time()
        }
        flag

        where:
        desc            || taskInfo || operateId                                                         || rst
        "单人未处理终止" || JsonUtil.fromJson("{\"id\":\"65421cc94011a96380dd65d0\",\"state\":\"cancel\",\"instanceId\":\"65421cc94011a96380dd65cf\",\"type\":\"anyone\",\"completed\":false,\"createTime\":1698831561012,\"modifyTime\":1698831587992,\"candidateIds\":[\"1002\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "cancel" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65421cc94011a96380dd65d0_1002\",\"name\":\"65421cc94011a96380dd65d0_1002\",\"task_id\":\"65421cc94011a96380dd65d0\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65421cc94011a96380dd65cf\",\"state\":\"normal\",\"start_time\":1698831561012,\"end_time\":1698831587992,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签未处理终止" || JsonUtil.fromJson("{\"id\":\"65421ef54011a96380dd65fb\",\"state\":\"cancel\",\"instanceId\":\"65421ef14011a96380dd65f5\",\"type\":\"all\",\"completed\":false,\"createTime\":1698832117709,\"modifyTime\":1698832210520,\"allPassType\":0,\"candidateIds\":[\"1000\",\"1002\",\"1114\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "cancel" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65421ef54011a96380dd65fb_1114\",\"name\":\"65421ef54011a96380dd65fb_1114\",\"task_id\":\"65421ef54011a96380dd65fb\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65421ef14011a96380dd65f5\",\"state\":\"normal\",\"start_time\":1698832117709,\"end_time\":1698832210520,\"user_id\":[\"1114\"]},{\"_id\":\"65421ef54011a96380dd65fb_1002\",\"name\":\"65421ef54011a96380dd65fb_1002\",\"task_id\":\"65421ef54011a96380dd65fb\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65421ef14011a96380dd65f5\",\"state\":\"normal\",\"start_time\":1698832117709,\"end_time\":1698832210520,\"user_id\":[\"1002\"]},{\"_id\":\"65421ef54011a96380dd65fb_1000\",\"name\":\"65421ef54011a96380dd65fb_1000\",\"task_id\":\"65421ef54011a96380dd65fb\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65421ef14011a96380dd65f5\",\"state\":\"normal\",\"start_time\":1698832117709,\"end_time\":1698832210520,\"user_id\":[\"1000\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签已有人处理终止" || JsonUtil.fromJson("{\"id\":\"654220344011a96380dd6611\",\"state\":\"cancel\",\"instanceId\":\"6542202d4011a96380dd660b\",\"type\":\"all\",\"completed\":false,\"createTime\":1698832436021,\"modifyTime\":1698832499311,\"allPassType\":0,\"candidateIds\":[\"1000\",\"1002\",\"1114\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1002\",\"id\":\"654220664011a96380dd661b\",\"time\":1698832486822}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "cancel" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654220344011a96380dd6611_1114\",\"name\":\"654220344011a96380dd6611_1114\",\"task_id\":\"654220344011a96380dd6611\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"6542202d4011a96380dd660b\",\"state\":\"normal\",\"start_time\":1698832436021,\"end_time\":1698832499311,\"user_id\":[\"1114\"]},{\"_id\":\"654220344011a96380dd6611_1000\",\"name\":\"654220344011a96380dd6611_1000\",\"task_id\":\"654220344011a96380dd6611\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"6542202d4011a96380dd660b\",\"state\":\"normal\",\"start_time\":1698832436021,\"end_time\":1698832499311,\"user_id\":[\"1000\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人只添加新处理人" || JsonUtil.fromJson("{\"id\":\"654224d64011a96380dd6651\",\"state\":\"in_progress\",\"instanceId\":\"654224d64011a96380dd6650\",\"type\":\"anyone\",\"completed\":false,\"createTime\":1698833622527,\"modifyTime\":1698833639552,\"candidateIds\":[\"1002\",\"1067\",\"1017\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1002\"],\"afterModifyPersons\":[\"1002\",\"1067\",\"1017\"],\"id\":\"654224e74011a96380dd6652\",\"time\":1698833639517}],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654224e74011a96380dd6652" || JsonUtil.fromJson("{\"update\":[],\"add\":[{\"_id\":\"654224e74011a96380dd6652_1017\",\"name\":\"654224e74011a96380dd6652_1017\",\"task_id\":\"654224d64011a96380dd6651\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654224d64011a96380dd6650\",\"state\":\"normal\",\"start_time\":1698833639517,\"user_id\":[\"1017\"]},{\"_id\":\"654224e74011a96380dd6652_1067\",\"name\":\"654224e74011a96380dd6652_1067\",\"task_id\":\"654224d64011a96380dd6651\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654224d64011a96380dd6650\",\"state\":\"normal\",\"start_time\":1698833639517,\"user_id\":[\"1067\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人只删除处理人" || JsonUtil.fromJson("{\"id\":\"654226104011a96380dd665e\",\"state\":\"in_progress\",\"instanceId\":\"654226104011a96380dd665d\",\"type\":\"anyone\",\"completed\":false,\"createTime\":1698833936061,\"modifyTime\":1698833954606,\"candidateIds\":[\"1002\",\"1067\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1002\"],\"afterModifyPersons\":[\"1002\",\"1054\",\"1067\",\"1017\"],\"id\":\"654226174011a96380dd665f\",\"time\":1698833943880},{\"beforeModifyPersons\":[\"1002\",\"1054\",\"1067\",\"1017\"],\"afterModifyPersons\":[\"1002\",\"1067\"],\"id\":\"654226224011a96380dd6660\",\"time\":1698833954598}],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654226224011a96380dd6660" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654226174011a96380dd665f_1017\",\"name\":\"654226174011a96380dd665f_1017\",\"task_id\":\"654226104011a96380dd665e\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654226104011a96380dd665d\",\"state\":\"normal\",\"start_time\":1698833943880,\"end_time\":1698833954598,\"user_id\":[\"1017\"]},{\"_id\":\"654226174011a96380dd665f_1054\",\"name\":\"654226174011a96380dd665f_1054\",\"task_id\":\"654226104011a96380dd665e\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654226104011a96380dd665d\",\"state\":\"normal\",\"start_time\":1698833943880,\"end_time\":1698833954598,\"user_id\":[\"1054\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人添加和删除的处理人都有" || JsonUtil.fromJson("{\"id\":\"654228744011a96380dd666b\",\"state\":\"in_progress\",\"instanceId\":\"654228744011a96380dd666a\",\"type\":\"anyone\",\"completed\":false,\"createTime\":1698834548832,\"modifyTime\":1698834582902,\"candidateIds\":[\"1054\",\"1104\",\"1139\",\"1140\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1002\"],\"afterModifyPersons\":[\"1002\",\"1067\",\"1054\"],\"id\":\"6542287e4011a96380dd666c\",\"time\":1698834558071},{\"beforeModifyPersons\":[\"1002\",\"1067\",\"1054\"],\"afterModifyPersons\":[\"1054\",\"1104\",\"1139\",\"1140\"],\"id\":\"654228964011a96380dd666d\",\"time\":1698834582889}],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654228964011a96380dd666d" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654228744011a96380dd666b_1002\",\"name\":\"654228744011a96380dd666b_1002\",\"task_id\":\"654228744011a96380dd666b\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654228744011a96380dd666a\",\"state\":\"normal\",\"start_time\":1698834548832,\"end_time\":1698834582889,\"user_id\":[\"1002\"]},{\"_id\":\"6542287e4011a96380dd666c_1067\",\"name\":\"6542287e4011a96380dd666c_1067\",\"task_id\":\"654228744011a96380dd666b\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654228744011a96380dd666a\",\"state\":\"normal\",\"start_time\":1698834558071,\"end_time\":1698834582889,\"user_id\":[\"1067\"]}],\"add\":[{\"_id\":\"654228964011a96380dd666d_1139\",\"name\":\"654228964011a96380dd666d_1139\",\"task_id\":\"654228744011a96380dd666b\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654228744011a96380dd666a\",\"state\":\"normal\",\"start_time\":1698834582889,\"user_id\":[\"1139\"]},{\"_id\":\"654228964011a96380dd666d_1104\",\"name\":\"654228964011a96380dd666d_1104\",\"task_id\":\"654228744011a96380dd666b\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654228744011a96380dd666a\",\"state\":\"normal\",\"start_time\":1698834582889,\"user_id\":[\"1104\"]},{\"_id\":\"654228964011a96380dd666d_1140\",\"name\":\"654228964011a96380dd666d_1140\",\"task_id\":\"654228744011a96380dd666b\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654228744011a96380dd666a\",\"state\":\"normal\",\"start_time\":1698834582889,\"user_id\":[\"1140\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签只添加处理人" || JsonUtil.fromJson("{\"id\":\"65422a1d4011a96380dd669f\",\"state\":\"in_progress\",\"instanceId\":\"65422a124011a96380dd6697\",\"type\":\"all\",\"completed\":false,\"createTime\":1698834973488,\"modifyTime\":1698835020513,\"allPassType\":0,\"candidateIds\":[\"1000\",\"1002\",\"1114\",\"1054\",\"1104\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1000\",\"1002\",\"1114\"],\"afterModifyPersons\":[\"1000\",\"1002\",\"1114\",\"1054\",\"1104\"],\"id\":\"65422a4c4011a96380dd66a4\",\"time\":1698835020501}],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65422a4c4011a96380dd66a4" || JsonUtil.fromJson("{\"update\":[],\"add\":[{\"_id\":\"65422a4c4011a96380dd66a4_1104\",\"name\":\"65422a4c4011a96380dd66a4_1104\",\"task_id\":\"65422a1d4011a96380dd669f\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65422a124011a96380dd6697\",\"state\":\"normal\",\"start_time\":1698835020501,\"user_id\":[\"1104\"]},{\"_id\":\"65422a4c4011a96380dd66a4_1054\",\"name\":\"65422a4c4011a96380dd66a4_1054\",\"task_id\":\"65422a1d4011a96380dd669f\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65422a124011a96380dd6697\",\"state\":\"normal\",\"start_time\":1698835020501,\"user_id\":[\"1054\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签添加和删除的处理人都有" || JsonUtil.fromJson("{\"id\":\"65424b0e4011a96380dd6779\",\"state\":\"in_progress\",\"instanceId\":\"65424b0a4011a96380dd6773\",\"type\":\"all\",\"completed\":false,\"createTime\":1698843406800,\"modifyTime\":1698843430060,\"allPassType\":0,\"candidateIds\":[\"1114\",\"1054\",\"1104\",\"1139\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1000\",\"1002\",\"1114\"],\"afterModifyPersons\":[\"1114\",\"1054\",\"1104\",\"1139\"],\"id\":\"65424b264011a96380dd677d\",\"time\":1698843430046}],\"operateLog\":[],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65424b264011a96380dd677d" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65424b0e4011a96380dd6779_1002\",\"name\":\"65424b0e4011a96380dd6779_1002\",\"task_id\":\"65424b0e4011a96380dd6779\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65424b0a4011a96380dd6773\",\"state\":\"normal\",\"start_time\":1698843406800,\"end_time\":1698843430046,\"user_id\":[\"1002\"]},{\"_id\":\"65424b0e4011a96380dd6779_1000\",\"name\":\"65424b0e4011a96380dd6779_1000\",\"task_id\":\"65424b0e4011a96380dd6779\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65424b0a4011a96380dd6773\",\"state\":\"normal\",\"start_time\":1698843406800,\"end_time\":1698843430046,\"user_id\":[\"1000\"]}],\"add\":[{\"_id\":\"65424b264011a96380dd677d_1139\",\"name\":\"65424b264011a96380dd677d_1139\",\"task_id\":\"65424b0e4011a96380dd6779\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65424b0a4011a96380dd6773\",\"state\":\"normal\",\"start_time\":1698843430046,\"user_id\":[\"1139\"]},{\"_id\":\"65424b264011a96380dd677d_1104\",\"name\":\"65424b264011a96380dd677d_1104\",\"task_id\":\"65424b0e4011a96380dd6779\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65424b0a4011a96380dd6773\",\"state\":\"normal\",\"start_time\":1698843430046,\"user_id\":[\"1104\"]},{\"_id\":\"65424b264011a96380dd677d_1054\",\"name\":\"65424b264011a96380dd677d_1054\",\"task_id\":\"65424b0e4011a96380dd6779\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65424b0a4011a96380dd6773\",\"state\":\"normal\",\"start_time\":1698843430046,\"user_id\":[\"1054\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人审批-多个处理，一人同意" || JsonUtil.fromJson("{\"id\":\"65433f694011a96380dd6ff1\",\"state\":\"pass\",\"instanceId\":\"65433f634011a96380dd6fed\",\"type\":\"anyone\",\"completed\":true,\"createTime\":1698905961471,\"modifyTime\":1698905994028,\"candidateIds\":[\"1000\",\"1002\",\"1114\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1002\",\"id\":\"65433f8a4011a96380dd6ff5\",\"time\":1698905994025}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65433f8a4011a96380dd6ff5" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65433f694011a96380dd6ff1_1114\",\"name\":\"65433f694011a96380dd6ff1_1114\",\"task_id\":\"65433f694011a96380dd6ff1\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65433f634011a96380dd6fed\",\"state\":\"normal\",\"start_time\":1698905961471,\"end_time\":1698905994025,\"user_id\":[\"1114\"]},{\"_id\":\"65433f694011a96380dd6ff1_1002\",\"name\":\"65433f694011a96380dd6ff1_1002\",\"task_id\":\"65433f694011a96380dd6ff1\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65433f634011a96380dd6fed\",\"state\":\"normal\",\"start_time\":1698905961471,\"end_time\":1698905994025,\"user_id\":[\"1002\"]},{\"_id\":\"65433f694011a96380dd6ff1_1000\",\"name\":\"65433f694011a96380dd6ff1_1000\",\"task_id\":\"65433f694011a96380dd6ff1\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65433f634011a96380dd6fed\",\"state\":\"normal\",\"start_time\":1698905961471,\"end_time\":1698905994025,\"user_id\":[\"1000\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人审批-多个处理，一人驳回" || JsonUtil.fromJson("{\"id\":\"654340504011a96380dd700a\",\"state\":\"pass\",\"instanceId\":\"6543404c4011a96380dd7005\",\"type\":\"anyone\",\"completed\":true,\"createTime\":1698906192722,\"modifyTime\":1698906210243,\"candidateIds\":[\"1000\",\"1002\",\"1114\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"reject\",\"userId\":\"1002\",\"id\":\"654340624011a96380dd7010\",\"time\":1698906210232}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654340624011a96380dd7010" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654340504011a96380dd700a_1114\",\"name\":\"654340504011a96380dd700a_1114\",\"task_id\":\"654340504011a96380dd700a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"6543404c4011a96380dd7005\",\"state\":\"normal\",\"start_time\":1698906192722,\"end_time\":1698906210232,\"user_id\":[\"1114\"]},{\"_id\":\"654340504011a96380dd700a_1002\",\"name\":\"654340504011a96380dd700a_1002\",\"task_id\":\"654340504011a96380dd700a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"6543404c4011a96380dd7005\",\"state\":\"normal\",\"start_time\":1698906192722,\"end_time\":1698906210232,\"user_id\":[\"1002\"]},{\"_id\":\"654340504011a96380dd700a_1000\",\"name\":\"654340504011a96380dd700a_1000\",\"task_id\":\"654340504011a96380dd700a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"6543404c4011a96380dd7005\",\"state\":\"normal\",\"start_time\":1698906192722,\"end_time\":1698906210232,\"user_id\":[\"1000\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-无需全部人处理-全部同意" || JsonUtil.fromJson("{\"id\":\"654341364011a96380dd7022\",\"state\":\"pass\",\"instanceId\":\"654341314011a96380dd701c\",\"type\":\"all\",\"completed\":true,\"createTime\":1698906422540,\"modifyTime\":1698906638183,\"allPassType\":0,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1000\",\"1002\",\"1114\"],\"afterModifyPersons\":[\"1000\",\"1002\",\"1117\"],\"id\":\"654341eb4011a96380dd7034\",\"time\":1698906603241}],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"654341a44011a96380dd7031\",\"time\":1698906532241},{\"actionType\":\"agree\",\"userId\":\"1117\",\"id\":\"654342024011a96380dd7039\",\"time\":1698906626889},{\"actionType\":\"agree\",\"userId\":\"1002\",\"id\":\"6543420e4011a96380dd703d\",\"time\":1698906638179}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "6543420e4011a96380dd703d" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654341364011a96380dd7022_1002\",\"name\":\"654341364011a96380dd7022_1002\",\"task_id\":\"654341364011a96380dd7022\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654341314011a96380dd701c\",\"state\":\"normal\",\"start_time\":1698906422540,\"end_time\":1698906638179,\"user_id\":[\"1002\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-无需全部人处理-部分人同意" || JsonUtil.fromJson("{\"id\":\"654342a84011a96380dd705b\",\"state\":\"in_progress\",\"instanceId\":\"654342a24011a96380dd7053\",\"type\":\"all\",\"completed\":false,\"createTime\":1698906792022,\"modifyTime\":1698906856178,\"allPassType\":0,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1000\",\"1002\",\"1114\"],\"afterModifyPersons\":[\"1000\",\"1002\",\"1117\"],\"id\":\"654342cc4011a96380dd705d\",\"time\":1698906828788}],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1117\",\"id\":\"654342dd4011a96380dd7062\",\"time\":1698906845719},{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"654342e84011a96380dd7069\",\"time\":1698906856173}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654342e84011a96380dd7069" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654342a84011a96380dd705b_1000\",\"name\":\"654342a84011a96380dd705b_1000\",\"task_id\":\"654342a84011a96380dd705b\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654342a24011a96380dd7053\",\"state\":\"normal\",\"start_time\":1698906792022,\"end_time\":1698906856173,\"user_id\":[\"1000\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-无需全部人处理-有人不同意" || JsonUtil.fromJson("{\"id\":\"654347f94011a96380dd70ff\",\"state\":\"pass\",\"instanceId\":\"654347ee4011a96380dd70f9\",\"type\":\"all\",\"completed\":true,\"createTime\":1698908153364,\"modifyTime\":1698908192083,\"allPassType\":0,\"candidateIds\":[\"1000\",\"1002\",\"1114\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"654348114011a96380dd7101\",\"time\":1698908177794},{\"actionType\":\"reject\",\"userId\":\"1002\",\"id\":\"654348204011a96380dd7105\",\"time\":1698908192080}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654348204011a96380dd7105" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654347f94011a96380dd70ff_1114\",\"name\":\"654347f94011a96380dd70ff_1114\",\"task_id\":\"654347f94011a96380dd70ff\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654347ee4011a96380dd70f9\",\"state\":\"normal\",\"start_time\":1698908153364,\"end_time\":1698908192080,\"user_id\":[\"1114\"]},{\"_id\":\"654347f94011a96380dd70ff_1002\",\"name\":\"654347f94011a96380dd70ff_1002\",\"task_id\":\"654347f94011a96380dd70ff\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654347ee4011a96380dd70f9\",\"state\":\"normal\",\"start_time\":1698908153364,\"end_time\":1698908192080,\"user_id\":[\"1002\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-需要全部人处理-有人同意" || JsonUtil.fromJson("{\"id\":\"65434a134011a96380dd7156\",\"state\":\"in_progress\",\"instanceId\":\"65434a0f4011a96380dd7150\",\"type\":\"all\",\"completed\":false,\"createTime\":1698908691969,\"modifyTime\":1698908724814,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"65434a254011a96380dd7158\",\"time\":1698908709789},{\"actionType\":\"agree\",\"userId\":\"1002\",\"id\":\"65434a344011a96380dd715a\",\"time\":1698908724812}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434a344011a96380dd715a" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434a134011a96380dd7156_1002\",\"name\":\"65434a134011a96380dd7156_1002\",\"task_id\":\"65434a134011a96380dd7156\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434a0f4011a96380dd7150\",\"state\":\"normal\",\"start_time\":1698908691969,\"end_time\":1698908724812,\"user_id\":[\"1002\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-需要全部人处理-有人不同意" || JsonUtil.fromJson("{\"id\":\"6543490c4011a96380dd713c\",\"state\":\"in_progress\",\"instanceId\":\"654349084011a96380dd7136\",\"type\":\"all\",\"completed\":false,\"createTime\":1698908428105,\"modifyTime\":1698908467228,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1117\",\"id\":\"654349214011a96380dd7142\",\"time\":1698908449929},{\"actionType\":\"reject\",\"userId\":\"1002\",\"id\":\"654349334011a96380dd7146\",\"time\":1698908467225}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654349334011a96380dd7146" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"6543490c4011a96380dd713c_1002\",\"name\":\"6543490c4011a96380dd713c_1002\",\"task_id\":\"6543490c4011a96380dd713c\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654349084011a96380dd7136\",\"state\":\"normal\",\"start_time\":1698908428105,\"end_time\":1698908467225,\"user_id\":[\"1002\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-需要全部人处理-全部同意" || JsonUtil.fromJson("{\"id\":\"65434b231472b01064ed1f37\",\"state\":\"pass\",\"instanceId\":\"65434b1e1472b01064ed1f31\",\"type\":\"all\",\"completed\":true,\"createTime\":1698908963628,\"modifyTime\":1698909007899,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1117\",\"id\":\"65434b311472b01064ed1f3e\",\"time\":1698908977762},{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"65434b3d1472b01064ed1f41\",\"time\":1698908989724},{\"actionType\":\"agree\",\"userId\":\"1002\",\"id\":\"65434b4f1472b01064ed1f43\",\"time\":1698909007895}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434b4f1472b01064ed1f43" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434b231472b01064ed1f37_1002\",\"name\":\"65434b231472b01064ed1f37_1002\",\"task_id\":\"65434b231472b01064ed1f37\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434b1e1472b01064ed1f31\",\"state\":\"normal\",\"start_time\":1698908963628,\"end_time\":1698909007895,\"user_id\":[\"1002\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-需要全部人处理-全部不同意" || JsonUtil.fromJson("{\"id\":\"65434c0e1472b01064ed1f63\",\"state\":\"pass\",\"instanceId\":\"65434c081472b01064ed1f5d\",\"type\":\"all\",\"completed\":true,\"createTime\":1698909198026,\"modifyTime\":1698909233333,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[],\"opinionLog\":[{\"actionType\":\"reject\",\"userId\":\"1000\",\"id\":\"65434c161472b01064ed1f65\",\"time\":1698909206396},{\"actionType\":\"reject\",\"userId\":\"1117\",\"id\":\"65434c211472b01064ed1f67\",\"time\":1698909217101},{\"actionType\":\"reject\",\"userId\":\"1002\",\"id\":\"65434c311472b01064ed1f69\",\"time\":1698909233317}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434c311472b01064ed1f69" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434c0e1472b01064ed1f63_1002\",\"name\":\"65434c0e1472b01064ed1f63_1002\",\"task_id\":\"65434c0e1472b01064ed1f63\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434c081472b01064ed1f5d\",\"state\":\"normal\",\"start_time\":1698909198026,\"end_time\":1698909233317,\"user_id\":[\"1002\"]}],\"add\":[]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人审批-暂停" || JsonUtil.fromJson("{\"id\":\"65434d691472b01064ed1f79\",\"state\":\"suspend\",\"instanceId\":\"65434d691472b01064ed1f78\",\"type\":\"anyone\",\"completed\":false,\"createTime\":1698909545414,\"modifyTime\":1698909558368,\"candidateIds\":[\"1002\"],\"approverModifyLog\":[],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"65434d761472b01064ed1f7a\",\"time\":1698909558368}],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434d761472b01064ed1f7a" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434d691472b01064ed1f79_1002\",\"name\":\"65434d691472b01064ed1f79_1002\",\"task_id\":\"65434d691472b01064ed1f79\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"normal\",\"start_time\":1698909545414,\"end_time\":1698909558368,\"user_id\":[\"1002\"]}],\"add\":[{\"_id\":\"65434d761472b01064ed1f7a_1002\",\"name\":\"65434d761472b01064ed1f7a_1002\",\"task_id\":\"65434d691472b01064ed1f79\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"suspend\",\"start_time\":1698909558368,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "单人审批-继续" || JsonUtil.fromJson("{\"id\":\"65434d691472b01064ed1f79\",\"state\":\"in_progress\",\"instanceId\":\"65434d691472b01064ed1f78\",\"type\":\"anyone\",\"completed\":false,\"createTime\":1698909545414,\"modifyTime\":1698909721428,\"candidateIds\":[\"1002\"],\"approverModifyLog\":[],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"65434d761472b01064ed1f7a\",\"time\":1698909558368},{\"type\":\"resume\",\"id\":\"65434e191472b01064ed1f8c\",\"time\":1698909721428}],\"opinionLog\":[]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434e191472b01064ed1f8c" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434d761472b01064ed1f7a_1002\",\"name\":\"65434d761472b01064ed1f7a_1002\",\"task_id\":\"65434d691472b01064ed1f79\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"suspend\",\"start_time\":1698909558368,\"end_time\":1698909721428,\"user_id\":[\"1002\"]}],\"add\":[{\"_id\":\"65434e191472b01064ed1f8c_1002\",\"name\":\"65434e191472b01064ed1f8c_1002\",\"task_id\":\"65434d691472b01064ed1f79\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"normal\",\"start_time\":1698909721428,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-有人同意后暂停" || JsonUtil.fromJson("{\"id\":\"65434ecc1472b01064ed1f9a\",\"state\":\"suspend\",\"instanceId\":\"65434d691472b01064ed1f78\",\"type\":\"all\",\"completed\":false,\"createTime\":1698909900836,\"modifyTime\":1698909947066,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"65434efb1472b01064ed1f9e\",\"time\":1698909947066}],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"65434ee31472b01064ed1f9c\",\"time\":1698909923887}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434efb1472b01064ed1f9e" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434ecc1472b01064ed1f9a_1117\",\"name\":\"65434ecc1472b01064ed1f9a_1117\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"normal\",\"start_time\":1698909900836,\"end_time\":1698909947066,\"user_id\":[\"1117\"]},{\"_id\":\"65434ecc1472b01064ed1f9a_1002\",\"name\":\"65434ecc1472b01064ed1f9a_1002\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"normal\",\"start_time\":1698909900836,\"end_time\":1698909947066,\"user_id\":[\"1002\"]}],\"add\":[{\"_id\":\"65434efb1472b01064ed1f9e_1117\",\"name\":\"65434efb1472b01064ed1f9e_1117\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"suspend\",\"start_time\":1698909947066,\"user_id\":[\"1117\"]},{\"_id\":\"65434efb1472b01064ed1f9e_1002\",\"name\":\"65434efb1472b01064ed1f9e_1002\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"suspend\",\"start_time\":1698909947066,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-有人不同意后暂停" || JsonUtil.fromJson("{\"id\":\"6543505b1472b01064ed1fb9\",\"state\":\"suspend\",\"instanceId\":\"654350581472b01064ed1fb3\",\"type\":\"all\",\"completed\":false,\"createTime\":1698910299920,\"modifyTime\":1698910390164,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"654350b61472b01064ed1fc5\",\"time\":1698910390163}],\"opinionLog\":[{\"actionType\":\"reject\",\"userId\":\"1117\",\"id\":\"654350761472b01064ed1fbb\",\"time\":1698910326094},{\"actionType\":\"reject\",\"userId\":\"1000\",\"id\":\"6543507f1472b01064ed1fbd\",\"time\":1698910335686}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654350b61472b01064ed1fc5" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"6543505b1472b01064ed1fb9_1002\",\"name\":\"6543505b1472b01064ed1fb9_1002\",\"task_id\":\"6543505b1472b01064ed1fb9\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654350581472b01064ed1fb3\",\"state\":\"normal\",\"start_time\":1698910299920,\"end_time\":1698910390163,\"user_id\":[\"1002\"]}],\"add\":[{\"_id\":\"654350b61472b01064ed1fc5_1002\",\"name\":\"654350b61472b01064ed1fc5_1002\",\"task_id\":\"6543505b1472b01064ed1fb9\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654350581472b01064ed1fb3\",\"state\":\"suspend\",\"start_time\":1698910390163,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-有人同意后继续" || JsonUtil.fromJson("{\"id\":\"65434ecc1472b01064ed1f9a\",\"state\":\"in_progress\",\"instanceId\":\"65434d691472b01064ed1f78\",\"type\":\"all\",\"completed\":false,\"createTime\":1698909900836,\"modifyTime\":1698910135898,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"65434efb1472b01064ed1f9e\",\"time\":1698909947066},{\"type\":\"resume\",\"id\":\"65434fb71472b01064ed1fad\",\"time\":1698910135897}],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"65434ee31472b01064ed1f9c\",\"time\":1698909923887}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "65434fb71472b01064ed1fad" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"65434efb1472b01064ed1f9e_1117\",\"name\":\"65434efb1472b01064ed1f9e_1117\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"suspend\",\"start_time\":1698909947066,\"end_time\":1698910135897,\"user_id\":[\"1117\"]},{\"_id\":\"65434efb1472b01064ed1f9e_1002\",\"name\":\"65434efb1472b01064ed1f9e_1002\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"suspend\",\"start_time\":1698909947066,\"end_time\":1698910135897,\"user_id\":[\"1002\"]}],\"add\":[{\"_id\":\"65434fb71472b01064ed1fad_1117\",\"name\":\"65434fb71472b01064ed1fad_1117\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"normal\",\"start_time\":1698910135897,\"user_id\":[\"1117\"]},{\"_id\":\"65434fb71472b01064ed1fad_1002\",\"name\":\"65434fb71472b01064ed1fad_1002\",\"task_id\":\"65434ecc1472b01064ed1f9a\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"65434d691472b01064ed1f78\",\"state\":\"normal\",\"start_time\":1698910135897,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "会签-有人不同意后继续" || JsonUtil.fromJson("{\"id\":\"6543505b1472b01064ed1fb9\",\"state\":\"in_progress\",\"instanceId\":\"654350581472b01064ed1fb3\",\"type\":\"all\",\"completed\":false,\"createTime\":1698910299920,\"modifyTime\":1698910512110,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1117\"],\"approverModifyLog\":[],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"654350b61472b01064ed1fc5\",\"time\":1698910390163},{\"type\":\"resume\",\"id\":\"654351301472b01064ed1fd1\",\"time\":1698910512109}],\"opinionLog\":[{\"actionType\":\"reject\",\"userId\":\"1117\",\"id\":\"654350761472b01064ed1fbb\",\"time\":1698910326094},{\"actionType\":\"reject\",\"userId\":\"1000\",\"id\":\"6543507f1472b01064ed1fbd\",\"time\":1698910335686}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "654351301472b01064ed1fd1" || JsonUtil.fromJson("{\"update\":[{\"_id\":\"654350b61472b01064ed1fc5_1002\",\"name\":\"654350b61472b01064ed1fc5_1002\",\"task_id\":\"6543505b1472b01064ed1fb9\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654350581472b01064ed1fb3\",\"state\":\"suspend\",\"start_time\":1698910390163,\"end_time\":1698910512109,\"user_id\":[\"1002\"]}],\"add\":[{\"_id\":\"654351301472b01064ed1fd1_1002\",\"name\":\"654351301472b01064ed1fd1_1002\",\"task_id\":\"6543505b1472b01064ed1fb9\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654350581472b01064ed1fb3\",\"state\":\"normal\",\"start_time\":1698910512109,\"user_id\":[\"1002\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)
        "校验全部" || JsonUtil.fromJson("{\"id\":\"654372e2c31ab34aa5ffd130\",\"state\":\"cancel\",\"instanceId\":\"654372d4c31ab34aa5ffd123\",\"type\":\"all\",\"completed\":false,\"createTime\":1698919138644,\"modifyTime\":1698919475234,\"allPassType\":1,\"candidateIds\":[\"1000\",\"1002\",\"1099\",\"1101\"],\"approverModifyLog\":[{\"beforeModifyPersons\":[\"1000\",\"1002\",\"1117\"],\"afterModifyPersons\":[\"1000\",\"1002\",\"1099\",\"1101\"],\"id\":\"65437383c31ab34aa5ffd134\",\"time\":1698919299315}],\"operateLog\":[{\"type\":\"suspend\",\"id\":\"654373e0c31ab34aa5ffd139\",\"time\":1698919392318},{\"type\":\"resume\",\"id\":\"654373f3c31ab34aa5ffd13a\",\"time\":1698919411330}],\"opinionLog\":[{\"actionType\":\"agree\",\"userId\":\"1000\",\"id\":\"65437314c31ab34aa5ffd132\",\"time\":1698919188788},{\"actionType\":\"agree\",\"userId\":\"1002\",\"id\":\"65437398c31ab34aa5ffd135\",\"time\":1698919320348},{\"actionType\":\"reject\",\"userId\":\"1099\",\"id\":\"65437415c31ab34aa5ffd13b\",\"time\":1698919445127}]}", CalculateTaskHandleTimeDetailUtil.TaskInfo) || "" || JsonUtil.fromJson("{\"add\":[{\"_id\":\"654372e2c31ab34aa5ffd130_1117\",\"name\":\"654372e2c31ab34aa5ffd130_1117\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919138644,\"end_time\":1698919299315,\"user_id\":[\"1117\"]},{\"_id\":\"654372e2c31ab34aa5ffd130_1002\",\"name\":\"654372e2c31ab34aa5ffd130_1002\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919138644,\"end_time\":1698919320348,\"user_id\":[\"1002\"]},{\"_id\":\"654372e2c31ab34aa5ffd130_1000\",\"name\":\"654372e2c31ab34aa5ffd130_1000\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919138644,\"end_time\":1698919188788,\"user_id\":[\"1000\"]},{\"_id\":\"65437383c31ab34aa5ffd134_1101\",\"name\":\"65437383c31ab34aa5ffd134_1101\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919299315,\"end_time\":1698919392318,\"user_id\":[\"1101\"]},{\"_id\":\"65437383c31ab34aa5ffd134_1099\",\"name\":\"65437383c31ab34aa5ffd134_1099\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919299315,\"end_time\":1698919392318,\"user_id\":[\"1099\"]},{\"_id\":\"654373e0c31ab34aa5ffd139_1101\",\"name\":\"654373e0c31ab34aa5ffd139_1101\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"suspend\",\"start_time\":1698919392318,\"end_time\":1698919411330,\"user_id\":[\"1101\"]},{\"_id\":\"654373e0c31ab34aa5ffd139_1099\",\"name\":\"654373e0c31ab34aa5ffd139_1099\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"suspend\",\"start_time\":1698919392318,\"end_time\":1698919411330,\"user_id\":[\"1099\"]},{\"_id\":\"654373f3c31ab34aa5ffd13a_1101\",\"name\":\"654373f3c31ab34aa5ffd13a_1101\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919411330,\"end_time\":1698919475234,\"user_id\":[\"1101\"]},{\"_id\":\"654373f3c31ab34aa5ffd13a_1099\",\"name\":\"654373f3c31ab34aa5ffd13a_1099\",\"task_id\":\"654372e2c31ab34aa5ffd130\",\"task_api_name\":\"BpmTask\",\"instance_id\":\"654372d4c31ab34aa5ffd123\",\"state\":\"normal\",\"start_time\":1698919411330,\"end_time\":1698919445127,\"user_id\":[\"1099\"]}]}", CalculateTaskHandleTimeDetailUtil.ModifyInfo)

    }


}