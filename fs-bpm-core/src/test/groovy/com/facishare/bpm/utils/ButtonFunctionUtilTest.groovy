package com.facishare.bpm.utils

import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.paas.engine.bpm.TaskState
import spock.lang.Specification

class ButtonFunctionUtilTest extends Specification {

    def "toWorkflowOutline"() {
        when:
        def res = ButtonFunctionUtil.isNeedChangeBPMApprover(hasChangeBPMApproverPrivilege
                , isInProgressTaskState
                , isErrorTaskState
                , isExecutionState
                , isLinkAppNode
                , isUpstreamTenant
                , isSupportTaskType)

        then:
        res==result
        where:
        hasChangeBPMApproverPrivilege | isInProgressTaskState | isErrorTaskState | isExecutionState  | isLinkAppNode  |isUpstreamTenant |isSupportTaskType   || result
        true | true  | true  | true  | true  | true | false || false
        true | true  | true  | false  | true  | true | true || true
        true | true  | true  | false  | true  | false | true || true
    }

    def "isNeedStopBPM"() {
        when:
        def res = ButtonFunctionUtil.isNeedStopBPM(true, true)

        then:
        res==true
    }

    def "isNeedAddTag"() {
        when:
        Task task = new Task()
        task.completed = completed
        task.candidateIds = candidateIds
        task.bpmExtension = ['executionType' : executionType]
        task.taskType = taskType
        task.state = state
        task.nodeType = nodeType
        def res = ButtonFunctionUtil.isNeedAddTag(addTagUser, task)

        then:
        res==result

        where:
        addTagUser | completed | candidateIds| executionType | taskType | state | nodeType || result
        null | true  | null| null| null| null| null || false
        '1003' | false | null | 'approve' | 'anyone' | TaskState.in_progress | 'tag' || false
        '1003' | false | ['1001'] | 'approve' | 'anyone' | TaskState.in_progress | 'tag1' || false
        '1001' | false | ['1001'] | 'approve' | 'anyone' | TaskState.in_progress | 'tag1' || true
    }

    def "canRemindTask null"() {
        when:

        def res = ButtonFunctionUtil.canRemindTask(null, null)

        then:
        res==false
    }

    def "canRemindTask"() {
        when:
        Task task = new Task()
        task.state = state
        task.candidateIds = candidateIds
        def res = ButtonFunctionUtil.canRemindTask(null, task)

        then:
        res==false

        where:
         state | candidateIds || result
        TaskState.error | ['1002'] || true
        TaskState.error | null || false

    }
}