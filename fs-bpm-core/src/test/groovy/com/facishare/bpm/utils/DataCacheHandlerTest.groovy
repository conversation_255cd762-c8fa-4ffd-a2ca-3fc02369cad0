package com.facishare.bpm.utils

import com.facishare.bpm.bpmn.VariableExt
import com.facishare.bpm.utils.model.Pair
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Test for DataCacheHandler interface
 */
class DataCacheHandlerTest extends Specification {

    def dataCacheHandler = Mock(DataCacheHandler)

    def "test getData with Pair parameter"() {
        given: "entity and object ID pair"
        def entityIdObjectId = new Pair<String, String>("AccountObj", "obj123")
        def expectedData = [
            "id": "obj123",
            "name": "Test Account",
            "owner": "user123"
        ]

        dataCacheHandler.getData(entityIdObjectId) >> expectedData

        when:
        def result = dataCacheHandler.getData(entityIdObjectId)

        then:
        result == expectedData
        result["id"] == "obj123"
        result["name"] == "Test Account"
        result["owner"] == "user123"
    }

    def "test getData with separate parameters"() {
        given: "separate entity ID and object ID"
        def entityId = "ContactObj"
        def objectId = "contact456"
        def expectedData = [
            "id": "contact456",
            "name": "Test Contact",
            "email": "<EMAIL>"
        ]

        dataCacheHandler.getData(entityId, objectId) >> expectedData

        when:
        def result = dataCacheHandler.getData(entityId, objectId)

        then:
        result == expectedData
        result["id"] == "contact456"
        result["name"] == "Test Contact"
        result["email"] == "<EMAIL>"
    }

    def "test getDataCache"() {
        given: "cached data"
        def expectedCache = [
            "AccountObj_obj123": [
                "id": "obj123",
                "name": "Account 1"
            ],
            "ContactObj_contact456": [
                "id": "contact456",
                "name": "Contact 1"
            ]
        ]

        dataCacheHandler.getDataCache() >> expectedCache

        when:
        def result = dataCacheHandler.getDataCache()

        then:
        result == expectedCache
        result.size() == 2
        result["AccountObj_obj123"]["name"] == "Account 1"
        result["ContactObj_contact456"]["name"] == "Contact 1"
    }

    def "test getDescribeCache"() {
        given: "cached describe data"
        def expectedDescribeCache = [
            "AccountObj": [
                "apiName": "AccountObj",
                "label": "Account",
                "fields": []
            ],
            "ContactObj": [
                "apiName": "ContactObj",
                "label": "Contact",
                "fields": []
            ]
        ]

        dataCacheHandler.getDescribeCache() >> expectedDescribeCache

        when:
        def result = dataCacheHandler.getDescribeCache()

        then:
        result == expectedDescribeCache
        result.size() == 2
        result["AccountObj"]["label"] == "Account"
        result["ContactObj"]["label"] == "Contact"
    }

    def "test getDescribe with statistics"() {
        given: "API name and include statistics flag"
        def apiName = "AccountObj"
        def includeStatistics = true
        def expectedDescribe = [
            "apiName": "AccountObj",
            "label": "Account",
            "fields": [],
            "recordCount": 100,
            "lastModified": System.currentTimeMillis()
        ]

        dataCacheHandler.getDescribe(apiName, includeStatistics) >> expectedDescribe

        when:
        def result = dataCacheHandler.getDescribe(apiName, includeStatistics)

        then:
        result == expectedDescribe
        result["apiName"] == "AccountObj"
        result["recordCount"] == 100
    }

    def "test getDescribe without statistics"() {
        given: "API name without statistics"
        def apiName = "ContactObj"
        def includeStatistics = false
        def expectedDescribe = [
            "apiName": "ContactObj",
            "label": "Contact",
            "fields": []
        ]

        dataCacheHandler.getDescribe(apiName, includeStatistics) >> expectedDescribe

        when:
        def result = dataCacheHandler.getDescribe(apiName, includeStatistics)

        then:
        result == expectedDescribe
        result["apiName"] == "ContactObj"
        !result.containsKey("recordCount")
    }

    def "test getVariableMap"() {
        given: "workflow instance and workflow IDs"
        def workflowInstanceId = "instance123"
        def workflowId = "workflow456"
        def variable1 = new VariableExt("activity_0##AccountObj", "obj123")
        def variable2 = new VariableExt("activity_1##ContactObj", "contact456")
        def expectedVariableMap = [
            "activity_0##AccountObj": variable1,
            "activity_1##ContactObj": variable2
        ]

        dataCacheHandler.getVariableMap(workflowInstanceId, workflowId) >> expectedVariableMap

        when:
        def result = dataCacheHandler.getVariableMap(workflowInstanceId, workflowId)

        then:
        result == expectedVariableMap
        result.size() == 2
        result["activity_0##AccountObj"].getId() == "activity_0##AccountObj"
        result["activity_1##ContactObj"].getId() == "activity_1##ContactObj"
    }

    def "test getVariableInstances"() {
        given: "workflow instance ID"
        def workflowInstanceId = "instance789"
        def expectedVariableInstances = [
            "activity_0##AccountObj": "obj123",
            "activity_1##ContactObj": "contact456",
            "activity_2##LeadObj": "lead789"
        ]

        dataCacheHandler.getVariableInstances(workflowInstanceId) >> expectedVariableInstances

        when:
        def result = dataCacheHandler.getVariableInstances(workflowInstanceId)

        then:
        result == expectedVariableInstances
        result.size() == 3
        result["activity_0##AccountObj"] == "obj123"
        result["activity_1##ContactObj"] == "contact456"
        result["activity_2##LeadObj"] == "lead789"
    }

    def "test getData with null parameters"() {
        given: "null parameters"
        def entityId = null
        def objectId = null

        dataCacheHandler.getData(entityId, objectId) >> [:]

        when:
        def result = dataCacheHandler.getData(entityId, objectId)

        then:
        result == [:]
    }

    def "test getDescribe with null API name"() {
        given: "null API name"
        def apiName = null
        def includeStatistics = false

        dataCacheHandler.getDescribe(apiName, includeStatistics) >> null

        when:
        def result = dataCacheHandler.getDescribe(apiName, includeStatistics)

        then:
        result == null
    }

    def "test getVariableMap with empty results"() {
        given: "workflow IDs that return empty results"
        def workflowInstanceId = "emptyInstance"
        def workflowId = "emptyWorkflow"

        dataCacheHandler.getVariableMap(workflowInstanceId, workflowId) >> [:]

        when:
        def result = dataCacheHandler.getVariableMap(workflowInstanceId, workflowId)

        then:
        result == [:]
        result.isEmpty()
    }

    def "test getVariableInstances with empty results"() {
        given: "workflow instance ID that returns empty results"
        def workflowInstanceId = "emptyInstance"

        dataCacheHandler.getVariableInstances(workflowInstanceId) >> [:]

        when:
        def result = dataCacheHandler.getVariableInstances(workflowInstanceId)

        then:
        result == [:]
        result.isEmpty()
    }
}
