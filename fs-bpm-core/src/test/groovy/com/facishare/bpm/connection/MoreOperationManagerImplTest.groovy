package com.facishare.bpm.connection

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.manage.MoreOperationManager
import com.facishare.bpm.manage.impl.MoreOperationManagerImpl
import com.facishare.bpm.model.TaskOutline
import com.facishare.bpm.model.paas.engine.bpm.*
import com.facishare.bpm.model.resource.metadata.GetObjectsFunctionPrivilege
import com.facishare.bpm.model.task.BPMTask
import com.facishare.bpm.model.task.LaneTask
import com.facishare.bpm.model.task.MTask
import com.facishare.bpm.model.task.TaskDetail
import com.facishare.bpm.proxy.AuthServiceProxy
import com.facishare.bpm.proxy.MetaDataAuthProxy
import com.facishare.rest.core.model.RemoteContext
import com.google.common.collect.Lists
import spock.lang.Specification

import java.util.stream.Collectors

/**
 * Created by <PERSON> on 12/05/2017.
 */
class MoreOperationManagerImplTest extends Specification {

    def "任务更多操作测试"() {
        given: "设置基本mork信息"
        def moreOperationManager = new MoreOperationManagerImpl()
        def serviceManager = Mock(RefServiceManager.class)
        serviceManager.hasObjectFunctionPrivilege(_)>> FunctionPrivilege
        serviceManager.isOuterUserId()>> isOuterUserId

        when: "调用方法"
        def task = new MTask()
        task.state = state
        task.taskExtension = taskExtension
        task.execution = execution
        task.linkAppName = linkAppName
        def taskMore = moreOperationManager.getTaskMoreOperations(serviceManager, task)

        then: "校验结果"
        taskMore.containsAll(taskMoreResult)
        where: "设置参数"
        desc               | contextUserId | isAdmin | isOuterUserId | FunctionPrivilege          | state |     taskExtension |              execution                      |         linkAppName                                                                                                                                            || taskMoreResult

        "上游:有权限+非互联+节点进行中" | "a"           | true    | false         | ["ChangeBPMApprover": true] | TaskState.in_progress | ["entityId": "AccountObj"] | ["actions": [["executionState": "success"] as AfterAction]]  | null || [new MoreOperationManager.MoreOperation("更换处理人", "ChangeBPMApprover")]
        "下游:权限+非互联+节点异常"   | "a"           | true    | true          | ["ChangeBPMApprover": true] |   TaskState.error | ["entityId": "AccountObj"] | ["actions": [["executionState": "success"] as AfterAction]]      | null    || []
        "下游:权限+互联+节点进行中"   | "a"           | true    | true          | ["ChangeBPMApprover": true] |   TaskState.in_progress | ["entityId": "AccountObj"] | ["actions": [["executionState": "success"] as AfterAction]]          |          "prm"                       || [new MoreOperationManager.MoreOperation("更换处理人", "ChangeBPMApprover")]
        "下游:权限+互联+节点进行中+应用节点"   | "a"           | true    | true          | ["ChangeBPMApprover": true] | TaskState.in_progress | ["entityId": "AccountObj", "executionType": "externalApplyTask"] | ["actions": [["executionState": "success"] as AfterAction]]          |          "prm"   || []
        "下游:权限+互联+节点异常" | "a" | true | true | ["ChangeBPMApprover": true] |   TaskState.error | ["entityId": "AccountObj", "executionType": "externalApplyTask"] | ["actions": [["executionState": "success"] as AfterAction]]          |          "prm"                                   || []
    }

    def "实例更多操作测试"() {
        def moreOperationManager = new MoreOperationManagerImpl()
        given: "设置基本mork信息"
        def authServiceProxy = Mock(AuthServiceProxy)
        def metaDataAuthService = Mock(MetaDataAuthProxy)
        moreOperationManager.authServiceProxy = authServiceProxy
        moreOperationManager.metaDataAuthProxy = metaDataAuthService
        and: "设置是否是管理员"
        def context = new RemoteContext(userId: contextUserId)
        authServiceProxy.isAdmin(_) >> isAdmin
        and: "设置是否有编辑权限"
        metaDataAuthService.getObjectsFunctionPrivilege(_, _) >> new GetObjectsFunctionPrivilege.Result(result: ["AccountObj": func])
        def serviceManager = Mock(RefServiceManager.class)
        serviceManager.getContext()>> context

        when: "调用方法"
        println(desc)
        def instance = new WorkflowInstance()
        instance.state = state
        instance.applicantId = applicantId
        instance.entityId = entityId
        def instanceMore = moreOperationManager.getInstanceMoreOperations(serviceManager, func, instance)

        then: "校验结果"
        instanceMore.stream().map({ i -> i.getCode() }).collect(Collectors.toList()).containsAll(instanceMoreResult.stream().map {m -> m.getCode()}.collect(Collectors.toList()))
        where: "设置参数"
        desc               | contextUserId | isAdmin | func                                       | state     | applicantId    | entityId                                                                                    || instanceMoreResult
        "是管理员且有权限"         | "ab"          | true    | ["StopBPM": true, "ViewEntireBPM": true]   | InstanceState.in_progress | "a" | "AccountObj" || [MoreOperationManagerImpl.viewEntireBPM, MoreOperationManagerImpl.stopBPM]
        "是管理员且有权限但流程已结束"   | "ab"          | true    | ["StopBPM": true, "ViewEntireBPM": true]   |    InstanceState.pass | "a" | "AccountObj"     || [MoreOperationManagerImpl.viewEntireBPM, MoreOperationManagerImpl.stopBPM]
        "是管理员且只有stopBPM权限" | "ab"          | true    | ["StopBPM": true, "ViewEntireBPM": false]  | InstanceState.in_progress | "a" | "AccountObj" || [ MoreOperationManagerImpl.stopBPM]
        "是管理员且没有权限"        | "ab"          | true    | ["StopBPM": false, "ViewEntireBPM": false] | InstanceState.in_progress | "a" | "AccountObj" || []
        "不是管理员且不是发起者,有权限时" | "ab"          | false   | ["StopBPM": true, "ViewEntireBPM": true]   | InstanceState.in_progress | "a" | "AccountObj" || [MoreOperationManagerImpl.viewEntireBPM, MoreOperationManagerImpl.stopBPM]
        "不是管理员且不是发起者，无权限时" | "ab"          | false   | ["StopBPM": false, "ViewEntireBPM": false] | InstanceState.in_progress | "a" | "AccountObj" || []
        "是发起者且有权限"         | "a"           | true    | ["StopBPM": true, "ViewEntireBPM": true]   | InstanceState.in_progress | "a" | "AccountObj" || [MoreOperationManagerImpl.viewEntireBPM, MoreOperationManagerImpl.stopBPM]
        "是发起者且只有stopBPM权限" | "a"           | true    | ["StopBPM": true, "ViewEntireBPM": false]  | InstanceState.in_progress | "a" | "AccountObj" || [MoreOperationManagerImpl.stopBPM]
        "是发起者且没有权限"        | "a"           | true    | ["StopBPM": false, "ViewEntireBPM": false] | InstanceState.in_progress | "a" | "AccountObj" || []

    }

    def "任务列表更多操作测试"() {
        given: "设置基本mork信息"
        def moreOperationManager = new MoreOperationManagerImpl()
        def authServiceProxy = Mock(AuthServiceProxy)
        def metaDataAuthService = Mock(MetaDataAuthProxy)
        moreOperationManager.authServiceProxy = authServiceProxy
        moreOperationManager.metaDataAuthProxy = metaDataAuthService
        def context = new RemoteContext(userId: contextUserId)
        and: "设置是否是管理员"
        authServiceProxy.isAdmin(_) >> isAdmin
        and: "设置是否有编辑权限"
        metaDataAuthService.getObjectsFunctionPrivilege(_, _) >> new GetObjectsFunctionPrivilege.Result(result: ["AccountObj": func])
        def serviceManager = Mock(RefServiceManager.class)
        serviceManager.getContext()>> context
        serviceManager.hasObjectFunctionPrivilege("AccountObj") >> func
        when: "调用方法"
        def taskOutline = new TaskOutline()
        taskOutline.entityId = entityId
        taskOutline.entryType = entryType
        taskOutline.executionTypeEnum = executionTypeEnum
        taskOutline.state = state
        taskOutline.applicantId = applicantId
        taskOutline.completed = completed
        moreOperationManager.setTaskMoreOperationsWithInstanceOperation(serviceManager, Lists.newArrayList(taskOutline), true)
        then: "校验结果"
        println(desc)
        print(taskOutline.getMoreOperations().values())
        print(taskInstanceMoreResult)
        where: "设置参数"
        desc      | contextUserId | isAdmin | func                             | entityId   | entryType | executionTypeEnum | state | completed | applicantId                                                                                                                                                                                                        || taskInstanceMoreResult
        "任务没有结束，当前人是管理员，但不是发起人" | "a"     | true    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": true] |  "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                         || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover, MoreOperationManagerImpl.stopBPM, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，当前人是管理员，但不是发起人,没有中止流程的权限" | "a"     | true    | ["StopBPM": false, "ViewEntireBPM": true,"ChangeBPMApprover": true] |     "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                       || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover, MoreOperationManagerImpl.viewEntireBPM]
        "任务结束，当前人是管理员，但不是发起人" | "a"     | true    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": true] |  "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.cancel |false | "ab"                                                              || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，发起人" | "ab"     | false    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": true] |   "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                                             || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover, MoreOperationManagerImpl.stopBPM, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，是发起人,没有中止流程的权限" | "ab"     | false    | ["StopBPM": false, "ViewEntireBPM": true,"ChangeBPMApprover": true] |  "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                                  || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover, MoreOperationManagerImpl.viewEntireBPM]
        "任务结束，是发起人" | "ab"     | false    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": true] | "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.cancel |false | "ab"                                                                        || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，不是管理员也不是发起人" | "a"     | false    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": true] | "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                                         || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，不是管理员也不是发起人,没有中止流程的权限" | "a"     | false    | ["StopBPM": false, "ViewEntireBPM": true,"ChangeBPMApprover": true] |  "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                            || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，不是管理员也不是发起人,没有修改处理人的权限" | "a"     | false    | ["StopBPM": false, "ViewEntireBPM": true,"ChangeBPMApprover": true] |   "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                           || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "任务没有结束，不是管理员也不是发起人,没有查看完整流程图的权限" | "a"     | false    | ["StopBPM": false, "ViewEntireBPM": false,"ChangeBPMApprover": true] |  "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"                                           || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.changeBPMApprover]
        "任务结束，不是管理员也不是发起人" | "a"     | false    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": true] |    "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.cancel |false | "ab"                                                             || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "任务结束，流程异常，是管理员，可以终止流程，可以查看完整视图，不可以更换处理人" | "a"     | true    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": false] | "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.error |true | "ab"                         || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "任务结束，流程异常，不是管理员，可以终止流程，可以查看完整视图，不可以更换处理人" | "a"     | true    | ["StopBPM": false, "ViewEntireBPM": true,"ChangeBPMApprover": false] |  "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.error |true | "ab"                       || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "任务结束，流程异常，是发起人，可以终止流程，可以查看完整视图，不可以更换处理人" | "ab"     | true    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": false] |    "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.error |true | "ab"                        || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "应用节点，任务没有结束，流程正常，是管理员，可以终止流程，可以查看完整视图，不可以更换处理人" | "a"     | true    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": false] |   "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.error |false | "ab"                || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "应用节点，任务没有结束，流程正常，不是管理员,不是流程发起人，可以终止流程，可以查看完整视图，不可以更换处理人" | "a"     | false    | ["StopBPM": false, "ViewEntireBPM": true,"ChangeBPMApprover": false] |"AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab" || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM]
        "应用节点，任务没有结束，流程正常，不是管理员,是发起人，可以终止流程，可以查看完整视图，不可以更换处理人" | "ab"     | false    | ["StopBPM": true, "ViewEntireBPM": true,"ChangeBPMApprover": false] | "AccountObj" |  "AccountObj" |    ExecutionTypeEnum.update     |   TaskState.in_progress |false | "ab"   || [MoreOperationManagerImpl.viewBPMInstanceLog, MoreOperationManagerImpl.viewEntireBPM, MoreOperationManagerImpl.stopBPM]

    }

    def "getTaskMoreOperations"() {
        given: "设置基本mork信息"
        def ref = Mock(RefServiceManager.class)
        BPMTask task = new BPMTask()
        task.state = TaskState.in_progress
        task.extension = ['entityId':'A']
        ref.hasObjectFunctionPrivilege("A") >> ['ChangeBPMApprover': true]
        ref.isOuterUserId() >> false
        ref.isNeedDiscussButton() >> true
        TaskDetail taskDetail = new TaskDetail()
        taskDetail.executionType = ExecutionTypeEnum.execution
        LaneTask laneTask = new LaneTask()
        laneTask.executionType = ExecutionTypeEnum.execution

        when: "调用方法"
        new MoreOperationManagerImpl().getTaskMoreOperations(ref, task)
        new MoreOperationManagerImpl().getTaskMoreOperations(ref, taskDetail, null)
        new MoreOperationManagerImpl().getTaskMoreOperations(ref, laneTask, null)

        then: "校验结果"
        1==1
    }

    def "getTaskMoreOperations2"() {
        given: "设置基本mork信息"
        def ref = Mock(RefServiceManager.class)
        Task task = new Task()
        task.state = TaskState.in_progress
        task.bpmExtension = ['executionType' : 'approve']
        task.taskType = "anyone"
        task.completed = false
        task.candidateIds = ['1001']
        //task.extension = ['entityId':'A']
        ref.hasObjectFunctionPrivilege("A") >> ['ChangeBPMApprover': true]
        ref.isOuterUserId() >> false
        ref.isNeedDiscussButton() >> true
        ref.getUserId() >> '1001'
        ref.isAdmin() >> true
        ref.isNeedDiscussButton() >> true

        when: "调用方法"
        new MoreOperationManagerImpl().getTaskMoreOperations(ref,'A', true,false, false, false, true,candidateIds, isExternalApplyTask, isAfterActionWaiting, task)

        then: "校验结果"
        1==1
        where:
        isAfterActionWaiting | isExternalApplyTask | candidateIds
        true | true | []
        false | true | ['1001']
        false | false | []
        false | false | ['1002']

    }

    def "getInstanceMoreOperations"() {
        given: "设置基本mork信息"
        def ref = Mock(RefServiceManager.class)
        ref.isOuterUserId() >> false
        ref.isNeedDiscussButton() >> true
        ref.getUserId() >> '1001'
        ref.isAdmin() >> true
        ref.isNeedDiscussButton() >> true
        ref.hasObjectFunctionPrivilege("A") >> ['ViewBPMInstanceLog': true]
        WorkflowInstance instance = new WorkflowInstance()
        Map functionPrivilegeRst = ['ViewBPMInstanceLog': true]
        List<TaskOutline> tasksList = tasks
        Task task = new Task()
        task.bpmExtension = ['executionType':'execution']


        when: "调用方法"
        new MoreOperationManagerImpl().getInstanceMoreOperations(ref,functionPrivilegeRst, instance)
        new MoreOperationManagerImpl().setInstanceMoreOperations(ref,Lists.newArrayList(), null, instance)
        new MoreOperationManagerImpl().setTaskMoreOperationsWithInstanceOperation(ref, tasksList, true)
        new MoreOperationManagerImpl().getTaskMoreOperations(ref, task)

        then: "校验结果"
        1==1
        where:
        tasks | result
        []|null
        [['entryType' : 'A','state' : TaskState.cancel] as TaskOutline] | null


    }

}