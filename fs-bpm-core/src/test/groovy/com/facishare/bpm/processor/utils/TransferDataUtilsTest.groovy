package com.facishare.bpm.processor.utils

import com.facishare.bpm.model.RelevantTeam
import com.facishare.bpm.model.meta.BPMInstanceObj
import com.facishare.bpm.model.paas.engine.bpm.*
import com.facishare.bpm.proxy.OrganizationServiceProxy
import com.facishare.bpm.remote.model.org.Employee
import com.facishare.bpm.utils.DataTransferUtils
import com.facishare.bpm.utils.i18n.I18NUtils
import com.facishare.flow.mongo.bizdb.entity.LaneEntity
import com.facishare.flow.mongo.bizdb.entity.PoolEntity
import com.facishare.rest.core.model.RemoteContext
import com.facishare.rest.core.util.JsonUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification

import java.util.function.Function

/**
 * Created by wangz on 17-7-10.
 */
class TransferDataUtilsTest extends Specification {

    def "transferToWorkflowInstance"() {
        given: "workflowInstance 源数据"
        def workflowInstance = getWorkflowInstanceInfo()
        workflowInstance.state = InstanceState.cancel
        I18NUtils.obtainI8NCodeAndValueByKeyLevelList("", Lists.newArrayList()) >> null

        when: "transfer workflowInstance to metadata"
        def laneEntity = ['id':'256', 'name':'addf'] as LaneEntity
        laneEntity.activities = ['45']
        PoolEntity pool = new PoolEntity();
        pool.lanes = Lists.newArrayList(laneEntity)
        def workflowInstanceMD = DataTransferUtils.transferInstance(workflowInstance, Lists.newArrayList(pool), true, Sets.newHashSet(), 0)

        then:
        print JsonUtil.toPrettyJson(workflowInstanceMD)

    }

    def "transferToWorkflowInstance end"() {
        given: "workflowInstance 源数据"
        def workflowInstance = getWorkflowInstanceInfo()
        workflowInstance.setEnd(1730459688885L)
        workflowInstance.state = state

        when: "transfer workflowInstance to metadata"
        def laneEntity = ['id':'256', 'name':'addf'] as LaneEntity
        laneEntity.activities = ['45']
        PoolEntity pool = new PoolEntity();
        pool.lanes = Lists.newArrayList(laneEntity)
        def workflowInstanceMD = DataTransferUtils.transferInstance(workflowInstance, Lists.newArrayList(), true, Sets.newHashSet(), 0)

        then:
        print JsonUtil.toPrettyJson(workflowInstanceMD)

        where:
            state | aa
        InstanceState.cancel | "qqq"
        InstanceState.error | "qqq"

    }


    def "transferToTask"() {
        given: "task 源数据"
        def workflowInstance = getWorkflowInstanceInfo()
        workflowInstance.state = InstanceState.in_progress
        I18NUtils.obtainI8NCodeAndValueByKeyLevelList("", Lists.newArrayList()) >> null
        def laneEntity = ['id':'256', 'name':'addf'] as LaneEntity
        laneEntity.activities = ['45']
        PoolEntity pool = new PoolEntity();
        pool.lanes = Lists.newArrayList(laneEntity)
        def workflowInstanceMD = DataTransferUtils.transferInstance(workflowInstance, Lists.newArrayList(pool), true, Sets.newHashSet(), 0)

        def task = getTaskInfo()
        def organizationServiceProxy = Mock(OrganizationServiceProxy.class)
        def context = Mock(RemoteContext.class)
        Function<List<String>, Map<Integer, Employee>> getEmployeeFunction = { userIds -> organizationServiceProxy.getOutMembersByIds(context, Lists.newArrayList(userIds)) }

        when: "transfer task to metadata"
        task.completed = completed
        task.state = state
        def taskMD = DataTransferUtils.transferTask(task, workflowInstanceMD, Lists.newArrayList(), true, getEmployeeFunction, "111", 0L)

        then:
        print JsonUtil.toPrettyJson(taskMD)
        where:
        completed | state
        false | TaskState.in_progress
        true | TaskState.cancel
    }

    def "transferTaskOpinion"() {
        given: ""

        def task = getTaskInfo()
        def opinion = new Opinion()
        opinion.replyTime = 1494057904857L

        when: "transfer opinion to metadata"
        def opinionMD = DataTransferUtils.transferTaskOpinion(task,opinion)

        then:
        print JsonUtil.toPrettyJson(opinionMD)
    }

    def "transferSuspendOpinion"() {
        given: ""

        def task = getTaskInfo()
        def operateLog = new OperateLog()
        operateLog.createTime = 1494057904857L

        when: "transfer suspendOpinion to metadata"
        def opinionMD = DataTransferUtils.transferTaskSuspendOpinion(task,operateLog)

        then:
        print JsonUtil.toPrettyJson(opinionMD)
    }

    def "setParticipantsAndObjectIds"() {
        given: ""

        def instance = new BPMInstanceObj()
        def relevant_team = [
                type: "embedded_object_list",
                define_type: "package",
                is_index: true,
                is_need_convert: false,
                is_active: true,
                is_required: false,
                is_unique: false,
                status: "released",
                embedded_fields: [
                        teamMemberEmployee: [
                                type: "employee",
                                define_type: "package",
                                is_index: true,
                                is_need_convert: true,
                                is_required: false,
                                is_unique: false,
                                is_single: true,
                                api_name: "teamMemberEmployee",
                                description: "成员员工",
                                help_text: "成员员工",
                                label: "成员员工"
                        ],
                        teamMemberRole: [
                                type: "select_one",
                                define_type: "package",
                                is_index: true,
                                is_need_convert: false,
                                is_required: false,
                                is_unique: false,
                                options: [
                                        [label: "负责人", value: "1", resource_bundle_key: null],
                                        [label: "普通成员", value: "4", resource_bundle_key: null]
                                ],
                                api_name: "teamMemberRole",
                                description: "成员角色",
                                help_text: "成员角色",
                                label: "成员角色"
                        ],
                        teamMemberPermissionType: [
                                type: "select_one",
                                define_type: "package",
                                is_index: true,
                                is_need_convert: false,
                                is_required: false,
                                is_unique: false,
                                options: [
                                        [label: "只读", value: "1", resource_bundle_key: null],
                                        [label: "读写", value: "2", resource_bundle_key: null]
                                ],
                                api_name: "teamMemberPermissionType",
                                description: "成员权限类型",
                                help_text: "成员权限类型",
                                label: "成员权限类型"
                        ]
                ],
                label: "相关团队",
                help_text: "相关团队",
                api_name: "relevant_team"
        ] as RelevantTeam
        instance.relevant_team = Sets.newHashSet(relevant_team)
        instance.objectIds = ['11','22']
        def task1 = ['candidateIds':['1001','1003'], 'objectId':'1215'] as Task
        def task2 = ['candidateIds':['1004','1005'], 'objectId':'1216'] as Task
        def tasks = Lists.newArrayList(task1, task2)


        when: "transfer suspendOpinion to metadata"
        def opinionMD = DataTransferUtils.setParticipantsAndObjectIds(instance,tasks, Maps.newHashMap(),Sets.newHashSet("2635"))

        then:
        print JsonUtil.toPrettyJson(opinionMD)
    }


    def getWorkflowInstanceInfo(){
        WorkflowInstance res = new WorkflowInstance();
        res.setId("1")
        res.setSourceWorkflowId("2")
        res.setEntityId("A")
        res.setObjectId("a")
        res.setWorkflowId("3")
        res.setWorkflowName("ceshi")
        res.setStart(1730459688885L)
        res.variables = ['SelfRef':'123']
        res.activityInstances = [['activityId':'45', 'activityName':'haha'] as ActivityInstance]
        res.getWorkflowNameDataI18N() >> ['key1': 'value1', 'key2': 'value2']
        return res
    }
    def getTaskInfo(){
        Task task = new Task()
        task.setId("112154")
        task.setTaskType("anyone")
        task.setName("task1")
        task.setCreateTime(1494057904857L)
        Map<String, Object> bpmExtension = Maps.newHashMap()
        bpmExtension.put("actionCode", "0")
        bpmExtension.put("executionType", "addRelatedObject")
        bpmExtension.put("relatedEntityId", "AccountObj")
        bpmExtension.put("executionName", "编辑对象")
        bpmExtension.put("entityId", "object_237__c")
        bpmExtension.put("entityName", "全类型对象")
        task.setBpmExtension(bpmExtension)
        task.assignee = ['person':['1','2'],'dept':['1','2'],'dept_leader':['1','2'],'role':['1','2'],'group':['1','2'],'ext_bpm':['1','2']]
        task.candidateIds = ['1002']
        return task
    }
}
