//package com.facishare.bpm.service.impl
//
//import com.effektif.workflow.api.workflowinstance.ActivityInstance
//import com.facishare.bpm.model.WorkflowOutline
//import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance
//import com.facishare.bpm.model.resource.paas.PageResult
//import com.facishare.bpm.proxy.AuthServiceProxy
//import com.facishare.bpm.proxy.OrganizationServiceProxy
//import com.facishare.bpm.remote.metadata.MetadataService
//import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
//import com.facishare.bpm.utils.IDCreatorUtil
//import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
//import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
//import com.facishare.flow.mongo.bizdb.entity.LaneEntity
//import com.facishare.flow.mongo.bizdb.entity.PoolEntity
//import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity
//import com.facishare.rest.core.model.RemoteContext
//import com.facishare.rest.core.util.JsonUtil
//import lombok.extern.slf4j.Slf4j
//import org.apache.commons.io.IOUtils
//import spock.lang.Specification
//
///**
// * Created by cuiyongxu on 17/5/16.
// */
//@Slf4j
//class GBPMInstanceServiceTest extends Specification {
//
//
//    def bpmInstanceService
//    def bpmDefinitionService
//    def metadataService
//    def outlineEntity
//    def outlineDao
//    def workflowExtensionDao
//    def paasWorkflow
//    def authServiceProxy
//    def organizationService
//    def id
//    def context
//
//
//    def setup() {
//        context = new RemoteContext(tenantId: "bpm_test_workflowoutline", userId: "userId")
//        id = IDCreatorUtil.createId()
//        bpmDefinitionService = Mock(BPMDefinitionServiceImpl.class)
//        metadataService = Mock(MetadataService.class)
//        outlineEntity = WorkflowOutlineEntity.create(workflowOutline)
//        outlineDao = Mock(BpmSimpleDefinitionDao.class)
//        workflowExtensionDao = Mock(DefinitionExtensionDao.class)
//        paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
//        authServiceProxy = Mock(AuthServiceProxy.class)
//        organizationService = Mock(OrganizationServiceProxy.class)
//
//        bpmInstanceService = new BPMInstanceServiceImpl(
//                bpmDefinitionService: bpmDefinitionService,
//                metadataService: metadataService,
//                outlineDao: outlineDao,
//                workflowExtensionDao: workflowExtensionDao,
//                paasWorkflow: paasWorkflow,
//                authServiceProxy: authServiceProxy,
//                organizationService: organizationService)
//    }
//
//    ////////////////////////////////////////===   服务初始化完毕   ===////////////////////////////////////////
//
//    def "启动流程实例"() {
//        given: "init"
//
//        when: "执行用"
//
//        outlineEntity.id = id
//        outlineDao.findOneFlowExtension(_, _) >> outlineEntity
//        paasWorkflow.getWorkflowInstances(_, _, _, _, _, _, _, _, _) >> getPageResultInstances(0)
//        metadataService.findDataById(context, _, _, _, _, _, _, _, _) >> findDataById()
//        paasWorkflow.start(_, _, _, _, _) >> id
//        outlineDao.incCount(_, _) >> Mock(WorkflowOutlineEntity.class)
//        def instanceId = bpmInstanceService.startWorkflow(context, "outlineId", "objectId")
//
//        then: "条件执行"
//
//        instanceId != null
//        println " 实例Id:" + instanceId
//
//    }
//
//    def "取消流程实例"() {
//        when: "执行用"
//        paasWorkflow.getWorkflowInstance(_, _) >> Mock(WorkflowInstance.class)
//        authServiceProxy.isCRMAdmin(_) >> true
//        bpmInstanceService.cancelWorkflowInstance(context, id)
//        then: "条件执行"
//        println("取消流程实例成功")
//
//    }
//
//    def "获取流程实例详情"() {
//
//        when: "执行用"
//        paasWorkflow.getWorkflowInstance(_, _) >> Mock(WorkflowInstance.class)
//        def instance = bpmInstanceService.getWorkflowInstance(context, id)
//        then: "条件执行"
//        instance != null
//        println instance
//
//    }
//
//    def "获取全部流程实例"() {
//        when: "执行用"
//
//        paasWorkflow.getAllWorkflowInstances(_, _, _, _, _) >> getPageResultInstances(20)
//        workflowExtensionDao.findOneFlowExtension(_, _) >> getExtension(10)
//        authServiceProxy.isCRMAdmin(_) >> true
//        metadataService.getPaaSObjectNames(_, _) >> getPaaSObjectNames(10)
//        bpmDefinitionService.getWorkflowEntryTypeNameMap(_, _) >> getWorkflowEntryTypeNameMap()
//        def pageInstance = bpmInstanceService.getWorkflowInstances(context, InstanceState.in_progress, id,
//                "workflowName", com.facishare.bpm.model.CircleType.ALL, 1, 20, id, false)
//        then: "条件执行"
//        pageInstance != null
//        println pageInstance.dataList
//    }
//
//
//    def "获取完整流程"() {
//        when: "执行用"
//        WorkflowInstance workflowInstance = new WorkflowInstance()
//        workflowInstance.setId(id)
//        ActivityInstance activityInstance = new ActivityInstance()
//        activityInstance.setStart(new Date().getTime() + "")
//        workflowInstance.setActivityInstances(getActivityInstance(10))
//        paasWorkflow.getWorkflowInstance(_, _) >> workflowInstance
//        outlineDao.findBySourceWorkflowId(_, _) >> Mock(WorkflowOutlineEntity)
//        workflowExtensionDao.findOneFlowExtension(_, _) >> getWorkflowExtensionEntity()
//        paasWorkflow.getWorkflowMap(_, _) >> Mock(Map)
//        metadataService.getPaaSObjectNames(_, _) >> getPaaSObjectNames(10)
//        def entireWorkflow = bpmInstanceService.getEntireWorkflowInstance(context, id)
//        then: "条件执行"
//        entireWorkflow != null
//        println entireWorkflow
//    }
//
//    def "阶段视图显示任务详情"() {
//        when: "执行用"
//        bpmDefinitionService.getWorkflowOutlineBySourceId(_, _) >> getWorkflowOutline()
//        paasWorkflow.getAllInprogressTasksBySourceWorkflowId(_, _) >> []
//        paasWorkflow.getWorkflowInstancesBySourceWorkflowId(_, _, _) >> getPageResultInstances(10)
//        def value = bpmInstanceService.getWorkflowStatsData(context, id)
//
//        then: "条件执行"
//        value != null
//        println value
//
//    }
//    ////////////////////////////////////////===   service方法测试   ===////////////////////////////////////////
//
//
//    def getInstanceList(count) {
//        def list = []
//        count.times {
//            list << new WorkflowInstance(activityInstances: getActivityInstance(count),
//                    sourceWorkflowId: "FS-" + (it + 7) * 65,
//                    workflowId: IDCreatorUtil.createId(),
//                    applicantId: "aplicationID" + (it * 65),
//                    start: new Random(13).nextLong(),
//                    objectId: "FS-" + ((it / 3) / 12 * it + 99) * 7 + (it * 6))
//        }
//        list
//    }
//
//
//    def getWorkflowExtensionEntity() {
//        new FlowExtensionEntity(pools: poolEntities(10))
//    }
//
//    def poolEntities(count) {
//        def poolEntities = []
//        count.times {
//            poolEntities << new PoolEntity(id: it, name: new Random(count).nextLong(), lanes: laneEntity(count))
//        }
//        poolEntities
//
//    }
//
//
//    def laneEntity(count) {
//        def lanes = []
//        count.times {
//            lanes << new LaneEntity(id: it, activities: activities(count))
//        }
//        lanes
//    }
//
//
//    def activities(count) {
//        def list = []
//        count.times {
//            list << it + ""
//        }
//        list
//    }
//
//
//    def getActivityInstance(count) {
//        def list = []
//        count.times {
//            list << new ActivityInstance(activityId: it, start: new Date().getTime())
//        }
//        list
//    }
//
//
//    def getPaaSObjectNames(count) {
//        def objectNames = [:]
//        count.times {
//            objectNames["FS-" + ((it / 3) / 12 * it + 99) * 7 + (it * 6)] = "EntityName" + (it + 8) * 26 * it
//        }
//        objectNames
//    }
//
//    def getExtension(count) {
//        def list = []
//        count.times {
//            list << new FlowExtensionEntity(workflowId: it)
//        }
//        list
//    }
//
//
//    def getPageResultInstances(count) {
//        new PageResult(dataList: getInstanceList(10), total: count)
//    }
//
//
//    def getWorkflowEntryTypeNameMap() {
//        def map = [:]
//        10.times {
//            map["FS-" + (it + 7) * 65] = "FS-ENTITY-" + it
//        }
//        map
//    }
//
//    def getWorkflowOutline() {
//        JsonUtil.fromJson(
//                IOUtils.toString(getClass().getClassLoader().getResourceAsStream("data/workflowOutline")).replaceAll(" ", "")
//                , WorkflowOutline.class)
//    }
//
//    def findDataById() {
//        ["_id": IDCreatorUtil.createId()]
//    }
//
//}
