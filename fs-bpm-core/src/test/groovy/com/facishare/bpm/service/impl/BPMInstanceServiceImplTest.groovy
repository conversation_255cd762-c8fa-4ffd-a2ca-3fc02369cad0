package com.facishare.bpm.service.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.VariableExt
import com.facishare.bpm.manage.InstanceVariableManager
import com.facishare.bpm.manage.MoreOperationManager
import com.facishare.bpm.manage.RedisManager
import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.model.paas.engine.bpm.*
import com.facishare.bpm.model.resource.newmetadata.FindInternalDataById
import com.facishare.bpm.model.resource.paas.PageResult
import com.facishare.bpm.remote.metadata.MetadataService
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.bpm.service.BPMDefinitionService
import com.facishare.bpm.service.BPMTaskService
import com.facishare.bpm.utils.DataCacheHandler
import com.facishare.bpm.utils.model.Pair
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity
import com.facishare.rest.core.model.RemoteContext
import com.google.common.collect.Maps
import spock.lang.Specification

class BPMInstanceServiceImplTest  extends Specification {

    def startWorkflow() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def bpmTaskService = Mock(BPMTaskService.class)
        def instanceVariableManager = Mock(InstanceVariableManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)


        WorkflowOutlineEntity workflowOutlineEntity = ["enabled": enabled,"workflowJson":"{\"variables\":[{\"id\":\"1\"},{\"id\":\"2\"}]}"] as WorkflowOutlineEntity
        outlineDao.find(_,_) >> workflowOutlineEntity
        def dataCacheHandler = new DataCacheHandler(){
            @Override
            Map<String, Object> getData(Pair<String, String> entityIdObjectId) {
                return null
            }

            @Override
            Map<String, Object> getData(String entityId, String objectId) {
                return ["id":"123"]
            }

            @Override
            Map<String, Map<String, Object>> getDataCache() {
                return null
            }

            @Override
            Map<String, Map<String, Object>> getDescribeCache() {
                return null
            }

            @Override
            Map<String, Object> getDescribe(String apiName, boolean includeStatistics) {
                return null
            }

            @Override
            Map<String, VariableExt> getVariableMap(String workflowInstanceId, String workflowId) {
                return null
            }

            @Override
            Map<String, Object> getVariableInstances(String workflowInstanceId) {
                return null
            }
        }
        bpmTaskService.getDataHandler(_) >> dataCacheHandler
        outlineDao.incCount(_,_) >> workflowOutlineEntity

        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.outlineDao = outlineDao
        test.bpmTaskService = bpmTaskService
        test.instanceVariableManager = instanceVariableManager
        test.paasWorkflow = paasWorkflow

        when:
        try{
            test.startWorkflow(serviceManager, null , null, null)
        }catch(Exception e){}

        then:
        1==1
        where:
        enabled | data
        false | null
        true | null
    }

    def cancelWorkflowInstance() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def redisManager = Mock(RedisManager.class)

        WorkflowInstance instance  = ["id":"1111","state":state, "type" : type] as WorkflowInstance
        paasWorkflow.getWorkflowInstance(_,_) >> instance
        serviceManager.hasObjectFunctionPrivilege(_) >> ["sda":true, "sdafeaf":false]
        serviceManager.isAdmin() >> isAdmin
        redisManager.setValueWithExpireTime(_,_,_) >> redisFlag


        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.redisManager = redisManager
        when:
        try{
            test.cancelWorkflowInstance(serviceManager,"1111","1111")
            test.getWorkflowInstance(serviceManager, "1212")
        }catch(Exception e){}
        then:
        1==1
        where:
        state | type | isAdmin | redisFlag
        InstanceState.cancel | null | null | null
        InstanceState.in_progress | "CRM" | null | null
        InstanceState.in_progress | "workflow_bpm" | true | false
        InstanceState.in_progress | "workflow_bpm" | true | true
        InstanceState.in_progress | "workflow_bpm" | false | true
    }

    def getWorkflowInstanceLog() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def bpmTaskService = Mock(BPMTaskService.class)

        WorkflowInstance instance  = ["id":"1111","state":InstanceState.cancel] as WorkflowInstance
        paasWorkflow.getWorkflowInstance(_,_) >> instance
        bpmTaskService.getTasksByInstanceId(_,_) >> []
        bpmTaskService.getAutomaticAndQuartzTaskByInstanceId(_,_) >> []

        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.bpmTaskService = bpmTaskService
        when:
        test.getWorkflowInstanceLog(serviceManager,"1111")
        then:
        1==1

    }

    def getWorkflowInstances() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def bpmDefinitionService = Mock(BPMDefinitionService.class)

        PageResult pageResult = ["total": 1, "dataList" : ins] as PageResult
        paasWorkflow.getWorkflowInstances(_,_,_,_,_,_,_,_,_) >> pageResult
        bpmDefinitionService.getWorkflowOutlineBySourceId(_,_)>>[["id":"111","sourceWorkflowId" :"222"] as WorkflowOutline]

        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.bpmDefinitionService = bpmDefinitionService
        when:
        test.getWorkflowInstances(serviceManager, null, null, null, 1, 1, null, null)
        test.getWorkflowInstancesSkipDataAuth(serviceManager, null, null,1,1,null, null)
        then:
        1==1
        where:
        ins |  res
        [] | null
        [["id":"111","sourceWorkflowId" :"222","start" :1L] as WorkflowInstance, ["id":"111","sourceWorkflowId" :"222","start" :2L] as WorkflowInstance] | null
        null | null

    }

    def getEntireWorkflowInstance() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def workflowExtensionDao = Mock(DefinitionExtensionDao.class)

        paasWorkflow.getWorkflowInstance(_,_) >> ins
        PageResult pageResult = ["total": 1, "dataList" : null] as PageResult
        paasWorkflow.getTasksByInstanceIdsWithPage(_,_,_,_,_) >> pageResult
        outlineDao.findBySourceWorkflowId(_,_) >> instance
        Map m = Maps.newHashMap();
        paasWorkflow.getWorkflowMap(_,_)>> m
        workflowExtensionDao.findOneFlowExtension(_,_) >> ext
        serviceManager.getObjectNameAndDisplayName(_,_) >> m

        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.outlineDao = outlineDao
        test.workflowExtensionDao = workflowExtensionDao

        when:
        try {
            test.getEntireWorkflowInstance(serviceManager, null)
        }catch(Exception e){}

        then:
        1==1
        where:
        ins |  instance | ext
        ["sourceWorkflowId" :"222","start" :2L] as WorkflowInstance | null | null
        ["id" :"222","start" :2L,"activityInstances" :[]] as WorkflowInstance | null | null
        ["id" :"222","start" :2L,"activityInstances" :[]] as WorkflowInstance | ["id":"1112"] as WorkflowOutlineEntity | null
        ["id":"222", "start":2L, "activityInstances":[["activityId":"121"] as ActivityInstance], "objectId":"111"] as WorkflowInstance | ["id":"1112"] as WorkflowOutlineEntity | ["id":"1112"] as FlowExtensionEntity
    }

    def fillTriggerSourceDesc() {
        given:
        def context = Mock(RemoteContext.class)
        WorkflowInstance instance = ["triggerSource" : TriggerSource.function, "triggerSourceId":"111"] as WorkflowInstance
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()

        when:
        try {
            test.fillTriggerSourceDesc(context, instance)
        }catch(Exception e){}
        then:
        1==1
    }

    def isSingleInstance() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        WorkflowOutlineEntity instance = ["singleInstanceFlow" : 1,"sourceWorkflowId":"112", "entryType":"111"] as WorkflowOutlineEntity
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        PageResult pageResult = ["total": total, "dataList" : []] as PageResult
        paasWorkflow.getWorkflowInstances(_,_,_,_,_,_,_,_,_) >> pageResult
        def redisManager = Mock(RedisManager.class)
        redisManager.setValueWithExpire(_,_) >> redisFlag
        test.paasWorkflow = paasWorkflow
        test.redisManager = redisManager
        serviceManager.getTenantId() >> "111"
        when:
        try {
            test.isSingleInstance(instance, serviceManager, "1212")
        }catch(Exception e){}
        then:
        1==1
        where:
        total |  redisFlag
        1  | false
        -1   | true
        -1   |   false
    }

    def triggerWorkflow() {
        given:
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def serviceManager = Mock(RefServiceManager.class)
        def bpmTaskService = Mock(BPMTaskService.class)
        def instanceVariableManager = Mock(InstanceVariableManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def redisManager = Mock(RedisManager.class)
        WorkflowOutlineEntity instance = ["sourceWorkflowId":"112", "entryType":entryType, "enabled": enabled,"workflowJson":"{\"variables\":[{\"id\":\"1\"},{\"id\":\"2\"}]}"] as WorkflowOutlineEntity
        outlineDao.find(_,_) >> instance
        def dataCacheHandler = new DataCacheHandler(){
            @Override
            Map<String, Object> getData(Pair<String, String> entityIdObjectId) {
                return null
            }

            @Override
            Map<String, Object> getData(String entityId, String objectId) {
                if ("111".equals(entityId)) return null
                return ["id":"123"]
            }

            @Override
            Map<String, Map<String, Object>> getDataCache() {
                return null
            }

            @Override
            Map<String, Map<String, Object>> getDescribeCache() {
                return null
            }

            @Override
            Map<String, Object> getDescribe(String apiName, boolean includeStatistics) {
                return null
            }

            @Override
            Map<String, VariableExt> getVariableMap(String workflowInstanceId, String workflowId) {
                return null
            }

            @Override
            Map<String, Object> getVariableInstances(String workflowInstanceId) {
                return null
            }
        }
        bpmTaskService.getDataHandler(_) >> dataCacheHandler
        TriggerWorkflowByRule.TriggerResult triggerResult = ["instanceId":"11","rule": null] as TriggerWorkflowByRule.TriggerResult
        TriggerWorkflowByRule.Result result  = ["result" : triggerResult] as TriggerWorkflowByRule.Result
        paasWorkflow.trigger(_,_,_,_,_,_,_,_) >> result
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        outlineDao.incCount(_,_) >> instance
        test.outlineDao = outlineDao
        test.bpmTaskService = bpmTaskService
        test.instanceVariableManager = instanceVariableManager
        test.redisManager = redisManager
        test.paasWorkflow = paasWorkflow

        when:
        try {
            test.triggerWorkflow(serviceManager, null, null, null, "111")
        }catch(Exception e){}
        then:
        1==1
        where:
        enabled | entryType
        false | null
        true | "111"
        true | "222"
    }

    def triggerWorkflowForRest() {
        given:
        def bpmDefinitionService = Mock(BPMDefinitionService.class)
        WorkflowOutline outline = ["id":"111"] as WorkflowOutline
        bpmDefinitionService.getWorkflowOutlineBySourceWorkflowId(_,_) >> outline
        bpmDefinitionService.getWorkflowOutlineBySourceIdAndOutlineId(_,_,_) >> outline2
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.bpmDefinitionService = bpmDefinitionService

        when:
        try {
            test.triggerWorkflowForRest(null,null,null,null,null,null,null,null)
        }catch(Exception e){}
        then:
        1==1
        where:
        outline2 | aaa
        null | null
        ["isDeleted" : true] as WorkflowOutline | null
        ["isDeleted" : false, "enabled": false] as WorkflowOutline | null
    }

    def getInstancesByObject() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def workflowExtensionDao = Mock(DefinitionExtensionDao.class)
        def moreOperationManager = Mock(MoreOperationManager.class)
        Pair<List, List> pair = new Pair<>(key, value);
        paasWorkflow.getUncompletedTasks(_,_) >> pair
        workflowExtensionDao.findPoolsByWorkflowIds(_,_) >> Maps.newHashMap()
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.workflowExtensionDao = workflowExtensionDao
        test.moreOperationManager = moreOperationManager

        when:
        try {
            test.getInstancesByObject(serviceManager, null, null)
        }catch(Exception e){}
        then:
        1==1
        where:
        key | value
        []| []
        [["workflowInstanceId":"111"]as Task] | []
        [["workflowInstanceId":"111"]as Task] | [["id":"111"]as WorkflowInstance]


    }

    def assertInstanceNotNull() {
        given:
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()

        when:
        try {
            test.assertInstanceNotNull(null, instance, null)
        }catch(Exception e){}
        then:
        1==1
        where:
        instance | value
        null | null
        ["workflowId" : "111"]as WorkflowInstance | null

    }

    def hasProcessingSourceWorkflow() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()

        when:
        try {
            test.hasProcessingSourceWorkflow(serviceManager, null, null, null)
        }catch(Exception e){}
        then:
        1==1

    }

    def recoveryCancelInstance() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def metadataService = Mock(MetadataService.class)
        RecoveryCancelInstance.Result res = ["errCode" : 200] as RecoveryCancelInstance.Result
        paasWorkflow.recoveryCancelInstance(_,_) >> res
        Map map = ["name" : "11"] as Map
        FindInternalDataById.FindInternalDataByIdResultDetail detail = ["object_data" : map] as FindInternalDataById.FindInternalDataByIdResultDetail
        metadataService.findDataById(_,_,_,_,_,_,_,_,_,_) >> detail
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.metadataService = metadataService

        when:
            test.recoveryCancelInstance(serviceManager, null)
            test.cancelWorkflowInstances(serviceManager, null ,null)
            test.afterActionRetry(serviceManager, null, 1, 1)
            test.getReason(serviceManager, null, null)
            test.getInstanceLockKey("111","222")
        then:
        1==1

    }

    def getTaskOrInstanceRelatedEntityId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def bpmTaskService = Mock(BPMTaskService.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        Task task = ["id" : "111"] as Task
        bpmTaskService.getPaaSTask(_,_) >> task
        WorkflowInstance instance = ["id" : "111"] as WorkflowInstance
        paasWorkflow.getWorkflowInstance(_,_) >> instance
        BPMInstanceServiceImpl test  = new BPMInstanceServiceImpl()
        test.bpmTaskService = bpmTaskService
        test.paasWorkflow = paasWorkflow


        when:
        test.getTaskOrInstanceRelatedEntityId(serviceManager, taskId, instanceId)
        then:
        1==1
        where:
        taskId | instanceId
        "111" | null
        null | "111"
        null | null
    }

}
