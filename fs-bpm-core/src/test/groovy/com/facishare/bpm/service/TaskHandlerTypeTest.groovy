//package com.facishare.bpm.service
//
//import com.fxiaoke.common.Pair
//import spock.lang.Specification
//
///**
// * Created by wangz on 17-7-16.
// */
//class TaskHandlerTypeTest extends Specification {
//    def taskHandler = TaskHandlerManager.TaskHandlerType.approve
//    def "SetWorkflowVariable"() {
//    }
//
//    def "GenerateVariableKey "() {
//
//        when:
//        Map<String,Pair<String,String>> variableKey = taskHandler.generateVariableKey("0",new Pair<>("AccountObj",
//                "123"))
//
//        then:
//        variableKey.size() == 1
//        variableKey.get("activity_0##AccountObj") != null
//    }
//
//    def "GenerateVariableKey selfRef"() {
//
//        when:
//        Map<String,Pair<String,String>> variableKey = taskHandler.generateVariableKey("0",new Pair<>("AccountObj",
//                "123"), new Pair<>("AccountObj","234"))
//
//
//        then:
//        variableKey.size() == 2
//        variableKey.get("activity_0##SelfRef##AccountObj") != null
//    }
//}
