//package com.facishare.bpm.service
//
//import com.facishare.bpm.exception.BPMTaskExecuteException
//import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
//import com.google.common.collect.Lists
//import com.google.common.collect.Maps
//import spock.lang.Specification
//
///**
// * Created by wangz on 17-7-20.
// */
//class GTaskHandlerTypeTest extends Specification {
//    def taskHandlerManage = new TaskHandlerManager()
//
//    def "verifyRequiredField test"() {
//        given:
//        def desc = Maps.newHashMap()
//        def fieldsMap = Maps.newHashMap()
//        def fieldAMap = Maps.newHashMap()
//        fieldAMap.put(BPMConstants.MetadataKey.isRequired, true)
//        fieldAMap.put(BPMConstants.MetadataKey.label, "A字段")
//        fieldsMap.put("fieldA",fieldAMap)
//        desc.put("fields", fieldsMap)
//
//        def data = Maps.newHashMap()
//
//        List<Map<String, Object>> form = Lists.newArrayList()
//        Map<String, Object> fieldFrom = Maps.newHashMap()
//        fieldFrom.put("name", "fieldA")
//        fieldFrom.put("label", "field字段A")
//        fieldFrom.put("label", "field字段A")
//        fieldFrom.put(BPMConstants.ExtensionKey.required, false)
//        form.add(fieldFrom)
//        def forms = Lists.newArrayList()
//        forms.add(form)
//
//        when:
//        TaskHandlerManager.TaskHandlerType.update.verifyRequiredField(desc, data, forms)
//
//        then:
//        BPMTaskExecuteException e = thrown()
//        e.printStackTrace()
//    }
//
//    def "oneTrue test"() {
//        when:
//        boolean ret = TaskHandlerManager.TaskHandlerType.update.oneTrue(condition, condtion2, condition3)
//
//        then:
//        ret == expect
//
//        where:
//        condition | condtion2 | condition3 || expect
//        null      | false     | true       || true
//        false     | false     | false      || false
//        true      | false     | false      || true
//    }
//}
