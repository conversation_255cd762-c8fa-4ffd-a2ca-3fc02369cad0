package com.facishare.bpm.service.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.model.OrgOutline
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId
import spock.lang.Specification

class BPMOrganizationServiceImplTest extends Specification{

    def getOrganization() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        serviceManager.getMembersByIds(_) >> null
        serviceManager.getGroupByIds(_) >> null
        serviceManager.getRoleByCode(_) >> null
        serviceManager.getDeptByIDs(_) >> null
        serviceManager.getExternalUserIdInfo(_) >> null
        List<GetOutRolesByTenantId.SimpleRoleResult> re = [["roleId":"1","roleName":"1"] as GetOutRolesByTenantId.SimpleRoleResult] as List
        serviceManager.getRolesByAppId(_,_) >> re
        BPMOrganizationServiceImpl test  = new BPMOrganizationServiceImpl()
        OrgOutline line = ["person":[1,2], "CRMGroup":["1","2"],"role":["1","2"],"dept":[1,2],"externalPerson":["1","2"],"externalRole":["1","2"],"linkAppId":"1","linkAppType" : 1] as OrgOutline

        when:
        test.getOrganization(serviceManager, line)
        then:
        1==1
    }
}
