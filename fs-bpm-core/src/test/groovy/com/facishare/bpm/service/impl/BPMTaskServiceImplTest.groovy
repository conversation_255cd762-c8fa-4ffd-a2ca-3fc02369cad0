package com.facishare.bpm.service.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.manage.RedisManager
import com.facishare.bpm.model.WorkflowExtension
import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.model.paas.engine.bpm.*
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate
import com.facishare.bpm.model.resource.paas.PageResult
import com.facishare.bpm.model.task.LaneBriefTaskVO
import com.facishare.bpm.proxy.NewPaasMetadataProxy
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.bpm.service.BPMDefinitionService
import com.facishare.bpm.util.SwitchConfigManager
import com.facishare.flow.postgre.entity.BpmTaskCountEntity
import com.facishare.flow.repository.BPMTaskRepository
import spock.lang.Specification

class BPMTaskServiceImplTest extends Specification{

    def getTasksByPage() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def page = Mock(Page.class)
        def query = Mock(TaskQuery.class)
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow

        when:
            test.getTasksByPage(serviceManager, page, query)
        then:
        1==1
    }

    def getTaskLogs() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        WorkflowInstance ins = ["state" : InstanceState.in_progress, "start" : 1002L] as WorkflowInstance
        paasWorkflow.getWorkflowInstance(_,_) >> ins
        PageResult pageResult = ["total": 1] as PageResult
        paasWorkflow.getTasksByCondition(_,_,_,_,_,_,_,_,_) >> pageResult
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        when:
        test.getTaskLogs(serviceManager, instanceId)
        then:
        1==1
        where:
        instanceId | res
        null | null
        "1" | null
    }

    def changeTaskHandlers() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        Task task = ["state" : state,"appId" : "BPM", "linkApp" : linkApp, "candidateIds" : candidateIdsTask] as Task
        paasWorkflow.getTask(_,_) >> task

        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow

        when:
        try {
            test.changeTaskHandlers(serviceManager, "111", candidateIds, null)
        }catch(Exception e){}

        then:
        1==1
        where:
        state | candidateIds | linkApp | candidateIdsTask
        TaskState.tag_waiting | null | null | null
        TaskState.in_progress | ["154574547"] | null | null
        TaskState.in_progress | ["1002"] | "111" | ["1002"]
        TaskState.in_progress | ["1002"] | "111" | ["1003"]
    }

    def replaceTaskHandlers() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        serviceManager.getUserId() >> userId
        Task task = ["state" : TaskState.in_progress,"appId" : "BPM", "linkApp" : linkApp, "candidateIds" : candidateIdsTask, "linkAppEnable" : true] as Task
        paasWorkflow.getTask(_,_) >> task
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow

        when:
        try {
            test.replaceTaskHandlers(serviceManager, "111",["1002"] )
        }catch(Exception e){}

        then:
        1==1
        where:
        userId | linkApp | candidateIdsTask
        "1001" | null | null
        "-10000" | null | null
        "-10000" | "111" | ["1002"]
        "-10000" | "111" | ["1003"]
    }

    def getAllWorkflowTasks() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def bpmDefinitionService = Mock(BPMDefinitionService.class)
        def bpmTaskRepository = Mock(BPMTaskRepository.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        WorkflowOutline line = ["extension":["pools" : null] as WorkflowExtension] as WorkflowOutline
        bpmDefinitionService.getWorkflowOutlineBySourceId(_,_) >> line
        List<BpmTaskCountEntity> list1 = [["sourceWorkflowId" : "111", "startTime": 1L, "createTime" : 2L, "objectDataId": "111", "workflowInstanceId": "111"] as BpmTaskCountEntity]
        bpmTaskRepository.findTimeoutTask(_,_,_,_,_) >> list1
        FindDataBySearchTemplate.Result res = ["data":["queryResult":["data": [["id":"11"]as Map], "totalNumber" : 2]as FindDataBySearchTemplate.QueryResultInfo] as FindDataBySearchTemplate.InnerResult] as FindDataBySearchTemplate.Result
        serviceManager.findDataBySearchTemplate(_,_) >> res
        PageResult pageResult = ["total": 1, "dataList" :[]] as PageResult
        paasWorkflow.getTasksByCondition(_,_,_,_,_,_,_,_,_) >> pageResult
        Map map = ["1","2"] as  Map
        serviceManager.getPaaSObjectNames(_) >> map
        paasWorkflow.getWorkflowInstanceMap(_,_)>> map
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.bpmDefinitionService = bpmDefinitionService
        test.bpmTaskRepository = bpmTaskRepository
        test.paasWorkflow = paasWorkflow

        when:
        try {
            test.getAllWorkflowTasks(serviceManager, "111",type, "111", state, new Page())
        }catch(Exception e){}

        then:
        1==1
        where:
        type | state
        LaneBriefTaskVO.QueryType.activity | null
        LaneBriefTaskVO.QueryType.lane | LaneBriefTaskVO.QueryState.timeout
        LaneBriefTaskVO.QueryType.lane | LaneBriefTaskVO.QueryState.error
        LaneBriefTaskVO.QueryType.lane | LaneBriefTaskVO.QueryState.normal
    }

    def remindTask() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        Task task = ["state" : state,"appId" : appId] as Task
        paasWorkflow.getTask(_,_) >> task
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        when:
        try {
            test.remindTask(serviceManager, null, null, remindPersons)
        }catch(Exception e){}
        then:
        1==1
        where:
        state | appId | remindPersons
        null | "111" | null
        TaskState.error | "BPM" | null
        TaskState.in_progress | "BPM" | null
        TaskState.in_progress | "BPM" | ["111"]
    }
    def findAutoTasksByInstanceId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        List<Task> task = taskDemo
        PageResult pageResult = ["total": 1,"dataList" : task] as PageResult
        paasWorkflow.getAutoTasksByInstanceId(_,_,_,_,_,_,_,_,_) >> pageResult
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        when:
        try {
            test.findAutoTasksByInstanceId(serviceManager, null, null, null, null, null, null, 1, 2)
        }catch(Exception e){}
        then:
        1==1
        where:
        taskDemo | res
        [] | null
        [["id" : "253"] as Task] | null
    }

    def operateTask() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def redisManager = Mock(RedisManager.class)
        Task task = ["state" : state,"appId" : appId] as Task
        paasWorkflow.getTask(_,_) >> task
        redisManager.setValueWithExpireTime(_,_,_) >> redisFlag
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.redisManager = redisManager
        when:
        try {
            test.operateTask(serviceManager, null, type, null, null)
        }catch(Exception e){}
        then:
        1==1
        where:
        appId | redisFlag | type | state
        "111" | null | null | null
        "BPM" | false | null | null
        "BPM" | true | "suspend" | TaskState.error
        "BPM" | true | "resume" | TaskState.error
        "BPM" | true | "addTag" | TaskState.error
        "BPM" | true | "12121" | TaskState.error
    }

    def findTaskByIds() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        List<Task> taskList = [["bpmExtension":["layoutType":"objectFlowLayout", "layoutApiName": "111"] as Map] as Task]
        List<Task> taskList1 = [["activityInstanceId": 1] as Task]
        List<Task> taskList2 = [["entityId": "11","objectId":"1"] as Task,["entityId": "11","objectId":"2"] as Task]
        when:
        try {
            test.filterTaskListByActivityInstanceIds([],[])
            test.filterTaskListByActivityInstanceIds(taskList1, ["1"])
            test.findTaskByIds(serviceManager, null)
            test.findDelayTask(serviceManager, null,null,null,null)
            test.getObjectIdsByTasks(taskList2)
            test.fillFlowLayoutExists(serviceManager, taskList)
        }catch(Exception e){}
        then:
        1==1
    }

    def edit() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def newPaasMetadataProxy = Mock(NewPaasMetadataProxy.class)
        def switchConfig = Mock(SwitchConfigManager.SwitchConfig.class)
        Task task = ["completed" : completed, "externalApplyTask" :1] as Task
        paasWorkflow.getTask(_,_) >> task
        SwitchConfigManager.switchConfig = switchConfig
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.newPaasMetadataProxy = newPaasMetadataProxy
        when:
        try {
            test.edit(serviceManager, taskId, null,null,null,null,null,null)
        }catch(Exception e){}
        then:
        1==1
        where:
        taskId | completed
        "111" | null
        "677f7e4759bcb00af7989390" | true
        "677f7e4759bcb00af7989390" | false
    }

    def refreshHandlerByTaskId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        BPMTaskServiceImpl test  = new BPMTaskServiceImpl()
        test.paasWorkflow = paasWorkflow
        when:
        try {
            test.refreshHandlerByTaskId(serviceManager, taskId)
        }catch(Exception e){}
        then:
        1==1
        where:
        taskId | completed
        "111" | null
        "677f7e4759bcb00af7989390" | true
    }
}
