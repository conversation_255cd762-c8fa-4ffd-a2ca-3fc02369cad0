//package com.facishare.bpm.service.impl
//
//import com.facishare.bpm.RefServiceManager
//import com.facishare.bpm.bpmn.ExecutableWorkflowExt
//import com.facishare.bpm.model.TaskParams
//import com.facishare.bpm.model.WorkflowOutline
//import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList
//import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId
//import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo
//import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate
//import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra
//import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions
//import com.facishare.bpm.model.resource.newmetadata.UpdateData
//import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch
//import com.facishare.bpm.remote.model.org.*
//import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
//import com.facishare.bpm.util.verifiy.VerifyManager
//import com.facishare.bpm.util.verifiy.Workflow
//import com.facishare.bpm.utils.IDCreatorUtil
//import com.facishare.bpm.utils.model.Pair
//import com.facishare.flow.element.plugin.api.FlowElement
//import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper
//import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
//import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
//import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity
//import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
//import com.facishare.rest.core.model.ClientInfo
//import com.facishare.rest.core.model.RemoteContext
//import com.facishare.rest.core.util.JacksonUtil
//import com.facishare.rest.core.util.JsonUtil
//import org.apache.commons.io.IOUtils
//import spock.lang.Specification
//
//import java.util.function.Function
//
///**
// * Created by Aaron on 09/05/2017.
// */
//class GBPMDefinitionServiceTest extends Specification {
//    {
//        System.setProperty("-Dspring.profiles.active", "ceshi113")
//    }
//
//    def getWorkflow() {
//        def workflowOutline = JsonUtil.fromJson(
//                IOUtils.toString(getClass().getClassLoader().getResourceAsStream("data/workflowOutline")).replaceAll(" ", "")
//                , WorkflowOutline.class)
//        return workflowOutline
//    }
//
//    def "创建流程定义"() {
//        given: "设置创建流程参数"
//        def context = new RemoteContext(tenantId: "bpm_test_workflowoutline", userId: "userId")
//        def definitionService = new BPMDefinitionServiceImpl()
//        def workflowOutlineDao = Mock(BpmSimpleDefinitionDao.class)
//        def tenantService = Mock(BPMTenantServiceImpl)
//        def extensionDao = Mock(DefinitionExtensionDao)
//        def extension = Mock(FlowExtensionEntity)
//        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
//        def workflow = getWorkflow()
//        def createWorkflow = WorkflowOutlineEntity.create(workflow)
//        definitionService.setBpmTenantService(tenantService)
//        definitionService.setOutlineDao(workflowOutlineDao)
//        definitionService.setPaasWorkflow(paasWorkflow)
//        definitionService.setWorkflowExtensionDao(extensionDao)
//
//        when: "创建流程"
//        paasWorkflow.deploy(_) >> "12"
//        createWorkflow.setId(IDCreatorUtil.createId())
//        workflowOutlineDao.createOrUpdate(_) >> createWorkflow
//        tenantService.hasQuota(context) >> true
//        extensionDao.save(_) >> extension
//        extension.getId() >> 1
//        def outlineId = definitionService.deployWorkflow(context, workflow, true)
//
//        then: "调用流程配额信息"
//        outlineId != null
//        1 * tenantService.hasQuota(_)
//        and: "企业流程数目是否增加"
//        1 * tenantService.incSourceWorkflowCount(_)
//        1 * tenantService.incWorkflowCount(_)
//        and: "当流程名字相等时"
//        1 * workflowOutlineDao.findByWorkflowName(context.getTenantId(), workflow.getName())
//    }
//
//    def "更新流程定义"() {
//        given: "设置更新流程参数"
//        def context = new RemoteContext(tenantId: "bpm_test_workflowoutline", userId: "userId")
//        def definitionService = new BPMDefinitionServiceImpl()
//        def workflowOutlineDao = Mock(BpmSimpleDefinitionDao.class)
//        def tenantService = Mock(BPMTenantServiceImpl)
//        def extensionDao = Mock(DefinitionExtensionDao)
//        def extension = Mock(FlowExtensionEntity)
//        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
//        def workflow = getWorkflow()
//        def createWorkflow = WorkflowOutlineEntity.create(workflow)
//        createWorkflow.setId(workflow.getId())
//        definitionService.setBpmTenantService(tenantService)
//        definitionService.setOutlineDao(workflowOutlineDao)
//        definitionService.setPaasWorkflow(paasWorkflow)
//        definitionService.setWorkflowExtensionDao(extensionDao)
//
//        when: "创建流程"
//        paasWorkflow.deploy(_) >> "12"
//        workflowOutlineDao.createOrUpdate(_) >> createWorkflow
//        tenantService.hasQuota(context) >> true
//        extensionDao.save(_) >> extension
//        extension.getId() >> 1
//        definitionService.updateWorkflow(context, workflow)
//
//        then: "调用流程配额信息"
//        0 * tenantService.hasQuota(_)
//        and: "企业流程数目是否增加"
//        0 * tenantService.incSourceWorkflowCount(_)
//        1 * tenantService.incWorkflowCount(_)
//        and: "当流程名字相等时"
//        1 * workflowOutlineDao.findByWorkflowName(context.getTenantId(), workflow.getName())
//    }
//
//
//    def "bug"() {
//        given:
//        def workflowOutline = JacksonUtil.fromJson(
//                IOUtils.toString(getClass().getClassLoader().getResourceAsStream("doc/deploy2.json")).replaceAll(" ", "")
//                , WorkflowOutline.class)
//        print(JsonUtil.toJson(workflowOutline))
//        ExecutableWorkflowExt executableWorkflow = WorkflowOutline.fillExecutableWorkflowDetail(workflowOutline, true);
//        Workflow workflow = new Workflow(
//                serviceManager(),
//                workflowOutline.getName(),
//                workflowOutline.getEntryType(),
//                workflowOutline.getEntryTypeName(),
//                executableWorkflow,
//                workflowOutline.getRule(),
//                JacksonUtil.toJson(workflowOutline.getExtension()),
//                workflowOutline.getExternalFlow());
//
//        workflow.setRangeAssignee(workflowOutline.getRangeAssignee());
//        workflow.setServiceManager(serviceManager);
//        VerifyManager.instance.execute(workflow);
//        println 1
//    }
//
//    def serviceManager(){
//        new RefServiceManager() {
//            @Override
//            Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
//                return null
//            }
//
//            @Override
//            String getI18nLinkAppName(String linkApp, String linkAppName) {
//                return null;
//            }
//
//            @Override
//            boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
//                return false
//            }
//
//            @Override
//            Map<String, Object> getFlowConfig(String terminal, List<String> types) {
//                return null
//            }
//
//            @Override
//            boolean isNeedDiscussButton() {
//                return false
//            }
//
//            @Override
//            UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
//                return null
//            }
//
//            @Override
//            boolean checkEmailEnable(String sender) {
//                return false
//            }
//
//            @Override
//            Map findDataById(String entityId, String objectId) {
//                return null
//            }
//
//            @Override
//            Object findDataById(String entityId, String objectId, String field, boolean includeDescribe) {
//                return null
//            }
//
//            @Override
//            Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe) {
//                return null
//            }
//
//            @Override
//            Map findDataById(RemoteContext cnt, String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
//                return null
//            }
//
//            @Override
//            Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
//                return null
//            }
//
//            @Override
//            Map findDescribe(String entityId, boolean containAllFields, boolean includeStatistics) {
//                return null
//            }
//
//            @Override
//            Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
//                return null
//            }
//
//            @Override
//            Map<String, Object> getFields(String entityId) {
//                return null
//            }
//
//            @Override
//            FindDataBySearchTemplate.Result findDataBySearchTemplate(String apiName, SearchTemplateQuery query) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getSimpleEntityNames() {
//                return null
//            }
//
//            @Override
//            Map<String, String> getSimpleEntityNamesBySocketConfig() {
//                return null
//            }
//
//            @Override
//            Map<String, Object> getFieldDesc(String entityId, String field) {
//                return null
//            }
//
//            @Override
//            String getFieldType(String entityId, String field) {
//                return null
//            }
//
//            @Override
//            String getKey(String entityId, String objectId) {
//                return null
//            }
//
//            @Override
//            String getDescDisplayName(String entityId) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getPaaSObjectNames(Collection<Pair<String, String>> entityIdAndObjectIdList) {
//                return null
//            }
//
//            @Override
//            String getPaaSObjectName(String entityId, String objectId) {
//                return null
//            }
//
//            @Override
//            String getActionNameByActionCode(String entityId, String actionCode) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getRefEntityIdByEntityId(String entityId) {
//                return null
//            }
//
//            @Override
//            Map<String, Object> findDataByIdWithEmptyMap(String entityId, String dataId) {
//                return null
//            }
//
//            @Override
//            List<String> getDataOwner(String entityId, String id) {
//                return null
//            }
//
//            @Override
//            boolean isDataOwnerByQueryMetadata(String apiName, String objectId) {
//                return false
//            }
//
//            @Override
//            Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
//                return null
//            }
//
//
//
//            @Override
//            RemoteContext getContext() {
//                return null
//            }
//
//            @Override
//            String getTenantId() {
//                return null
//            }
//
//            @Override
//            String getUserId() {
//                return null
//            }
//
//            @Override
//            RemoteContext getNotExistEAContext() {
//                return null
//            }
//
//
//            @Override
//            List<String> getValue(boolean convert, String entityId, String objectId, List<String> extUserType, String instanceId, Map<String, Object> variables) {
//                return null
//            }
//
//            @Override
//            List<Integer> getDeptIdsByUserId() {
//                return null
//            }
//
//            @Override
//            List<String> getGroupByUserId() {
//                return null
//            }
//
//            @Override
//            List getMembersByGroupIds(List groups) {
//                return null
//            }
//
//            @Override
//            List<String> getMembersByDeptIds(List<Object> deptIds) {
//                return null
//            }
//
//            @Override
//            List<String> getDeptLeaders(List<Object> deptIds) {
//                return null
//            }
//
//            @Override
//            List<String> getDeptLeadersByUserIds(List<String> userIds) {
//                return null
//            }
//
//            @Override
//            List<String> getLeaders(List<String> userIds) {
//                return null
//            }
//
//            @Override
//            List<String> getCRMUserOfRoles(List<Object> roles) {
//                return null
//            }
//
//            @Override
//            RemoteContext getAdminContext() {
//                return null
//            }
//
//            @Override
//            boolean isAdmin() {
//                return false
//            }
//
//
//            @Override
//            Map<String, Object> getDescribe(String entityId) {
//                return null
//            }
//
//            @Override
//            Map<String, Map<String, Object>> getDescribes(Collection<String> entityIds) {
//                return null
//            }
//
//            @Override
//            boolean workflowIsExists(String tenantId, String outLineId, String sourceWorkflowId) {
//                return false
//            }
//
//            @Override
//            boolean isFieldInactive(String entityId, String field) {
//                return false
//            }
//
//            @Override
//            List<String> getRoleByUserId() {
//                return null
//            }
//
//            @Override
//            Map<String, Object> getAreaOption(String fieldType) {
//                return null
//            }
//
//            @Override
//            void validateAssignee(Set<Object> deptIds, Set<Object> groupIdList, Set<Object> userIds, Set<Object> roleCodes, Set<Object> deptLeader, String tipName) {
//
//            }
//
//            @Override
//            List<String> getEmployeesByReportingObjectId(String userId) {
//                return null
//            }
//
//            @Override
//            Map<String, Role> getRoleByCode(List<String> roleList) {
//                return null
//            }
//
//            @Override
//            Map<Integer, Employee> getMembersByIds(List personList) {
//                return null
//            }
//
//            @Override
//            Map<String, CRMGroup> getGroupByIds(List crmGroup) {
//                return null
//            }
//
//            @Override
//            Map<Integer, Department> getDeptByIDs(List deptIds) {
//                return null
//            }
//
//            @Override
//            Set<String> getPersons(Map<String, Object> nextTaskAssigneeScope) {
//                return null
//            }
//
//            @Override
//            Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
//                return null
//            }
//
//            @Override
//            boolean getDeliveryNoteEnable() {
//                return false
//            }
//
//            @Override
//            GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
//                return null
//            }
//
//            @Override
//            Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
//                return null
//            }
//
//            @Override
//            GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
//                return null
//            }
//
//            @Override
//            Map<String, Boolean> hasObjectFunctionPrivilege(String entityId) {
//                return null
//            }
//
//            @Override
//            boolean dataPrivilege(String entityId, String objectId) {
//                return false
//            }
//
//            @Override
//            String getAppId() {
//                return null
//            }
//
//            @Override
//            ClientInfo getClientInfo() {
//                return null
//            }
//
//            @Override
//            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
//                return null
//            }
//
//            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck) {
//                return null
//            }
//
//            @Override
//            Map<String, Object> findDataHandlePermissionsById(String entityId, String objectId) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getAppActions(String entityId, Boolean isExternalFlow, String appId, Integer appType) {
//                return null
//            }
//
//            @Override
//            Map<String, Object> getFieldDesc(String entityId, String field, boolean includeCountFieldLookupType) {
//                return null
//            }
//
//            @Override
//            Map<Integer, Employee> getExternalUserIdInfo(List externalRole) {
//                return null
//            }
//
//            @Override
//            Map<String, Integer> getFieldPermissions(String entityId) {
//                return null
//            }
//
//            @Override
//            boolean isOuterUserId() {
//                return false
//            }
//
//            @Override
//            List<Long> getOuterMainOwner() {
//                return null
//            }
//
//            @Override
//            Map<String, Employee> getEmployeeInfo(List<Object> userIds) {
//                return null
//            }
//
//            @Override
//            String getUserIdWithOuterUserId() {
//                return null
//            }
//
//            @Override
//            boolean isOuterMainOwner() {
//                return false
//            }
//
//            @Override
//            List<String> getExternalRole(List externalRole, String lowerTenantId, String linkAppId, Integer linkAppType) {
//                return null
//            }
//
//            @Override
//            List<GetOutRolesByTenantId.SimpleRoleResult> getRolesByAppId(String appId, int appType) {
//                return null
//            }
//
//            @Override
//            GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getPaaSObjectNames(String entityId, List<String> ids) {
//                return null
//            }
//
//            @Override
//            Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
//                return null
//            }
//
//            @Override
//            Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData, boolean skipRelevantTeam) {
//                return null
//            }
//
//            @Override
//            def <E> E getObjectFromCache(String key, Function<String, E> function) {
//                return null
//            }
//
//            @Override
//            Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet) {
//                return null
//            }
//
//            @Override
//            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction) {
//                return null
//            }
//
//            @Override
//            Map<String, String> findDataExhibitButton(String apiName, String objectId, String usePageType) {
//                return null
//            }
//
//            @Override
//            FlowElementWrapper getFlowElementWrapper(String elementApiName) {
//                return null
//            }
//
//            @Override
//            FlowElement getFlowElement(String elementApiName) {
//                return null
//            }
//        }
//    }
//}
