package com.facishare.bpm.common

import com.facishare.bpm.remote.model.config.BPMObjectSupportConfig
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 5.7
 */
class BPMObjectSupportConfigTest extends Specification {
    def '测试'(){
        given:"参数设置"
        def config=new BPMObjectSupportConfig()
        config.setWhiteList(whiteList)
        config.setObjectBaseConfig(Maps.newHashMap())
        when:"调用"
        def rst=config.isCustomObj("71557", apiName)
        then:"结果"
        rst==result
        where:
        whiteList|apiName||result
        ["PromotionObj"]|"PromotionObj"||true
        ["PromotionObj"]|"Promotion__c"||true
        ["PromotionObj"]|"Promotion"||false
    }
}
