package com.facishare.bpm

import com.facishare.bpm.model.ActionButton
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/9/20 6:31 PM
 */
@ContextConfiguration(locations = "classpath:applicationContext.xml")
class GBaseTest extends Specification {

    void setup() {
        System.setProperty("spring.profiles.active", "fstest")
    }

    def listToString(Collection<ActionButton> actionButtonCollection) {
        StringBuffer stringBuffer = new StringBuffer()
        for (int i = 0; i < actionButtonCollection.size(); i++) {
            ActionButton actionButton = actionButtonCollection[i]
            if (i == actionButtonCollection.size() - 1) {
                stringBuffer.append(actionButton.getAction() + "_" + actionButton.getLabel())
            } else {
                stringBuffer.append(actionButton.getAction() + "_" + actionButton.getLabel()).append(",")
            }
        }
        stringBuffer.toString()
    }


    def listToStringNew(List<ActionButton> buttons) {
        buttons.sort({ o1, o2 ->
           if( o1.getLabel().hashCode()>o2.getLabel().hashCode()){
               return -1
           }else{
               return 1;
           }
        })
        StringBuffer stringBuffer = new StringBuffer()
        for (int i = 0; i < buttons.size(); i++) {
            ActionButton actionButton = buttons[i]
            if (i == buttons.size() - 1) {
                stringBuffer.append(actionButton.getAction() + "_" + actionButton.getLabel())
            } else {
                stringBuffer.append(actionButton.getAction() + "_" + actionButton.getLabel()).append(",")
            }
        }
        stringBuffer.toString()
    }
}
