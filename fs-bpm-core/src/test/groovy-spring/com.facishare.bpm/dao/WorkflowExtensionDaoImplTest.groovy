package com.facishare.bpm.dao

import com.facishare.bpm.utils.IDCreatorUtil
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity
import lombok.extern.slf4j.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.concurrent.TimeUnit

/**
 * Created by cuiyongxu on 17/6/12.
 */
@Slf4j
@ContextConfiguration(locations = "classpath:applicationContext.xml")
class WorkflowExtensionDaoImplTest extends Specification {

    def tenantId, workflowId
    def ids = []

    def setup() {
        tenantId = "54821"
        workflowId = "5953201c3db71d8252e8a858"
        ids << workflowId << "59531beb3db71d8252e8a82d" << "595204c63db71d8252e8a53f"
    }

    @Autowired
    DefinitionExtensionDao workflowExtensionDao

    def "查询扩展详情"() {
        given:
        assert workflowExtensionDao.findOneFlowExtension(tenantId, workflowId) != null
    }

    def "查询扩展详情列表"() {
        given:
        println workflowExtensionDao.findOneFlowExtension(tenantId, ids);
    }

    def "查询扩展详情Map,workflowId,Entity"() {
        given:
        println workflowExtensionDao.findToMap(tenantId, ids);
    }

    def "保存扩展"() {
        given:
        def entity = workflowExtensionDao.save(tenantId, new FlowExtensionEntity(tenantId: "54821", workflowId: "123456"))
        println entity.id
    }

    def "更新扩展"() {
        given:
        def entity = workflowExtensionDao.save(tenantId, new FlowExtensionEntity(tenantId: "54821", workflowId: IDCreatorUtil.createId(), svg: "123456789"))
        println entity.id
        TimeUnit.SECONDS.sleep(10);
        entity.svg = "999666"
        workflowExtensionDao.update(tenantId, entity)

    }
}
