package com.facishare.bpm.dao

import com.facishare.bpm.utils.IDCreatorUtil
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
import com.facishare.rest.core.util.JsonUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Profile
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification


/**
 * Created by <PERSON> on 07/05/2017.
 */
@ContextConfiguration(locations = "classpath:applicationContext.xml")
@Profile(value = "ceshi113")
class WorkflowOutlineDaoImplTest extends Specification {
    @Autowired
    BpmSimpleDefinitionDao workflowOutlineDao

    def "createOrUpdate"() {
        given:"初始化"
        def tenantId = "12"
        def workflow = JsonUtil.fromFile("data/workflowOutline", WorkflowOutlineEntity.class)
        workflow.setTenantId(tenantId)
        workflow.setSourceWorkflowId(IDCreatorUtil.createId())
        def count = workflowOutlineDao.findAll(tenantId).size()
        when: "当流程概要有id时，保存后数据库条数不增加"
        def workflowEntity = workflowOutlineDao.createOrUpdate(workflow)
        then: "应该存在id"
        workflowEntity.id != null
        workflowOutlineDao.findAll(tenantId).size() == count + 1

        when: "当流程概要有id时，保存后数据库条数不增加"
        workflowEntity = workflowOutlineDao.createOrUpdate(workflow)


        then: "条数应该没有变化"
        workflowOutlineDao.findAll(tenantId).size() == count+1
        cleanup: "清理信息"
        workflowOutlineDao.delete(tenantId, workflowEntity.id)
    }
}