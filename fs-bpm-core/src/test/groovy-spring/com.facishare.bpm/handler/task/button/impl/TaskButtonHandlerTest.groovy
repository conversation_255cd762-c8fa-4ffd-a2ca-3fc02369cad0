package com.facishare.bpm.handler.task.button.impl

import com.facishare.bpm.GBaseTest
import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.button.model.DefaultActionLabel
import com.facishare.bpm.handler.task.button.model.FormButtonResult
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.model.ActionButton
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.service.BPMBaseService
import com.facishare.rest.core.model.RemoteContext
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Unroll

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/9/24 10:41 AM
 */
class TaskButtonHandlerTest extends GBaseTest {

    @Autowired
    private BPMBaseService baseService;

    @Autowired
    BpmUpdateTaskButtonHandler updateTaskButtonHandler;

    @Autowired
    OperationTaskButtonHandler operationTaskButtonHandler;

    @Autowired
    AddRelatedObjectTaskButtonHandler addRelatedObjectTaskButtonHandler;

    @Autowired
    BatchAddRelatedObjectTaskButtonHandler batchAddRelatedObjectTaskButtonHandler;

    @Autowired
    AddMDObjectTaskButtonHandler addMDObjectTaskButtonHandler;

    @Autowired
    BatchEditMasterDetailButtonHandler batchEditMasterDetailButtonHandler;

    @Autowired
    ApproveTaskButtonHandler approveTaskButtonHandler;

    @Unroll
    def "同意不同意按钮自定义-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        when:
        FormButtonResult formButtonResult = approveTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc                  || standardData         || tenantId || rst
        "审批节点无自定义按钮"          || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "agree", "label": "同意"),
                new ActionButton(action: "reject", "label": "不同意")
        )
        "审批节点有自定义按钮"          || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                defaultButtons: ["agree": new DefaultActionLabel(label: "同意了"), "reject": new DefaultActionLabel(label: "不同意了")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "agree", "label": "同意了"),
                new ActionButton(action: "reject", "label": "不同意了")
        )
        "审批节点有自定义按钮-只有同意自定义"  || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                defaultButtons: ["agree": new DefaultActionLabel(label: "同意了")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "agree", "label": "同意了"),
                new ActionButton(action: "reject", "label": "不同意")
        )
        "审批节点有自定义按钮-只有不同意自定义" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                defaultButtons: ["reject": new DefaultActionLabel(label: "不不不同意了")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "agree", "label": "同意"),
                new ActionButton(action: "reject", "label": "不不不同意了")
        )


    }

    @Unroll
    def "任务落地页-更新按钮下发 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = updateTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams as TaskParams)
        then:

        def a = listToStringNew(formButtonResult.getButtons());
        def b = listToStringNew(rst);

        if (a != b) {
            print(">>>>>>>>>>>>>>>" + a + " != " + b + "====> " + desc)
        }
        a == b
        where:
        desc                          || standardData || tenantId || taskParams                                                || rst
        "填写无自定义按钮"                    || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存")
        )
        "填写无自定义按钮-无字段"                || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )
        "填写无自定义按钮"                    || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存")
        )

        "填写无自定义按钮-不需要填写按钮"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存")
        )

        "填写有自定义按钮"                    || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["update": new DefaultActionLabel(label: "填写66666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存")
        )

        "保存有自定义按钮"                    || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save": new DefaultActionLabel(label: "保存6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存6666")
        )
        "更新并完成任务有自定义按钮"               || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["UpdateAndComplete": new DefaultActionLabel(label: "保存并完成任务6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存")
        )

        "填写及更新并完成任务有自定义按钮"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["UpdateAndComplete": new DefaultActionLabel(label: "保存并完成任务6666"),
                                 "update"           : new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存")
        )

        "填写及保存有自定义按钮"                 || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存6666")
        )

        "没有编辑的功能权限,有字段,存在自定义按钮"       || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : false,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成", "code": "update")
        )


        "有编辑的功能权限,但是form没有字段"         || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )
//
        "有编辑的功能权限,form有字段"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(false).isTaskDetail(true) || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存6666")
        )
        "有权限,只有签到组件 任务落地页"                 || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(true,false),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isTaskDetail(true)                 || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )


        //*********** 终端请求测试
        "有字段,有权限,填写无自定义按钮"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "无字段,有权限,填写无自定义按钮-无字段"        || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )
        "有权限,有字段,填写无自定义按钮"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有字段,有权限,填写无自定义按钮-不需要填写按钮"    || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有字段,有权限,填写有自定义按钮"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["update": new DefaultActionLabel(label: "填写66666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有字段,有权限,保存有自定义按钮"            || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save": new DefaultActionLabel(label: "保存6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "有字段,有权限,更新并完成任务有自定义按钮"       || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["UpdateAndComplete": new DefaultActionLabel(label: "保存并完成任务6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有字段,有权限,填写及更新并完成任务有自定义按钮"    || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["UpdateAndComplete": new DefaultActionLabel(label: "保存并完成任务6666"),
                                 "update"           : new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有字段,有权限,填写及保存有自定义按钮"         || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "无权限,有字段,编辑的功能权限,有字段,存在自定义按钮" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : false,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成", "code": "update")
        )


        "有权限,无字段"                     || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )
//
        "有权限,form有字段"                 || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "有权限,只有签到组件"                 || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(true,false),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create().isMobile(true)                        || Lists.newArrayList(
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )
    }
    @Unroll
    def "更新-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = updateTaskButtonHandler.setButtons(serviceManager, standardData, false, taskParams)
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc                         || standardData  || tenantId || taskParams                         || rst
        "填写无自定义按钮"                   || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "填写无自定义按钮-不需要填写按钮"           || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "填写有自定义按钮"                   || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["update": new DefaultActionLabel(label: "填写66666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "保存有自定义按钮"                   || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save": new DefaultActionLabel(label: "保存6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "更新并完成任务有自定义按钮"              || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["UpdateAndComplete": new DefaultActionLabel(label: "保存并完成任务6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "填写及更新并完成任务有自定义按钮"           || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["UpdateAndComplete": new DefaultActionLabel(label: "保存并完成任务6666"),
                                 "update"           : new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "填写及保存有自定义按钮"                || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "没有编辑的功能权限,有字段,存在自定义按钮"      || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : false,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有编辑的功能权限,但是form没有字段"        || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || TaskParams.create()                || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )

        "有编辑的功能权限,但是form没有字段,只有签到组件" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(true, false),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["Save"  : new DefaultActionLabel(label: "保存6666"),
                                 "update": new DefaultActionLabel(label: "填写6666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71557"  || TaskParams.create().isMobile(true) || Lists.newArrayList(
                new ActionButton(action: "Update", "label": "填写6666", code: "update", i18nConvert: false),
                new ActionButton(action: "Complete", "label": "完成", i18nConvert: true, code: "update")
        )
    }

    @Unroll
    def "更换负责人-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = operationTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc                   || standardData        || tenantId || rst
        "更换负责人无自定义按钮"          || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                actionCode: "changeowner",
                actionLabel: "变更负责人",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "changeowner", "label": "变更负责人")
        )
//
        "更换负责人有自定义按钮"          || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                actionCode: "changeowner",
                actionLabel: "变更负责人",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["changeowner": new DefaultActionLabel(label: "变变变变变变变1")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "changeowner", "label": "变变变变变变变1")
        )

        "更换负责人有自定义按钮,但是没有功能权限" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                actionCode: "changeowner",
                actionLabel: "变更负责人",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["changeowner": new DefaultActionLabel(label: "变变变变变变变2")],
                objectPermissions: [
                        "changeowner"       : false,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList()
    }
    @Unroll
    def "一转三 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = operationTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc            || standardData               || tenantId || rst
//
        "无自定义按钮"        || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                actionCode: "HandleThree",
                actionLabel: "无效/跟进/转换",
                combinedActionCode: true,
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : true,
                        "followup"          : true,
                        "transform"         : true,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "close", "label": "无效"),
                new ActionButton(action: "followup", "label": "跟进"),
                new ActionButton(action: "transform", "label": "转换")

        )
        "有自定义按钮"        || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),
                actionCode: "HandleThree",
                actionLabel: "无效/跟进/转换",
                combinedActionCode: true,// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: [
                        "close"    : new DefaultActionLabel(label: "无效1"),
                        "followup" : new DefaultActionLabel(label: "跟进1"),
                        "transform": new DefaultActionLabel(label: "转换1")
                ],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : true,
                        "followup"          : true,
                        "transform"         : true,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "close", "label": "无效1"),
                new ActionButton(action: "followup", "label": "跟进1"),
                new ActionButton(action: "transform", "label": "转换1")
        )
        "有自定义按钮,无效 无权限" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),
                actionCode: "HandleThree",
                actionLabel: "无效/跟进/转换",
                combinedActionCode: true,// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: [
                        "close"    : new DefaultActionLabel(label: "无效1"),
                        "followup" : new DefaultActionLabel(label: "跟进1"),
                        "transform": new DefaultActionLabel(label: "转换1")
                ],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "followup"          : true,
                        "transform"         : true,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "followup", "label": "跟进1"),
                new ActionButton(action: "transform", "label": "转换1")
        )
    }

    @Unroll
    def "添加团队成员-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = operationTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc           || standardData                || tenantId || rst
        "添加团队成员无自定义按钮" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                actionCode: "addteammember",
                actionLabel: "添加团队成员",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "addteammember", "label": "添加团队成员")
        )

        "添加团队成员有自定义按钮" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                actionCode: "addteammember",
                actionLabel: "添加团队成员",
                updateFromProperty: Task.UpdateFromProperty.create(false, true),// getDetail中会设置true/false 没有字段的时候会为false
                defaultButtons: ["addteammember": new DefaultActionLabel(label: "添加团队成员666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "addteammember", "label": "添加团队成员666")
        )
    }

    @Unroll
    def "选择或新建关联对象-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = addRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc                  || standardData         || tenantId || rst
        "选择或新建关联对象无自定义按钮"     || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                relatedEntityId: "object_pFsNR__c",
                relatedEntityName: "自行车_dddd",
                target_related_list_name: "target_related_list_0tooR__c",
                relatedFieldApiName: "field_n0odH__c",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "选择或新建 自行车_dddd")
        )

        "选择或新建关联对象无自定义按钮-只新建" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                relatedEntityId: "object_pFsNR__c",
                onlyRelatedObject: true,
                relatedEntityName: "自行车_dddd",
                target_related_list_name: "target_related_list_0tooR__c",
                relatedFieldApiName: "field_n0odH__c",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "新建 自行车_dddd")
        )

        "选择或新建关联对象有自定义按钮"     || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                relatedEntityId: "object_pFsNR__c",
                relatedEntityName: "自行车_dddd",
                target_related_list_name: "target_related_list_0tooR__c",
                relatedFieldApiName: "field_n0odH__c",
                defaultButtons: ["addRelatedObject": new DefaultActionLabel(label: "选择_新建自行车999999")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "选择_新建自行车999999")
        )


        "选择或新建关联对象有自定义按钮-只新建" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                relatedEntityId: "object_pFsNR__c",
                relatedEntityName: "自行车_dddd",
                onlyRelatedObject: true,
                target_related_list_name: "target_related_list_0tooR__c",
                relatedFieldApiName: "field_n0odH__c",
                defaultButtons: ["addRelatedObject": new DefaultActionLabel(label: "新建自行车99999曾多次的9")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "新建自行车99999曾多次的9")
        )

    }

    @Unroll
    def "批量新建关联对象-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = batchAddRelatedObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc             || standardData              || tenantId || rst
        "批量新建关联对象无自定义按钮" || new StandardData(
                entityId: "object_UPfNh__c",
                objectId: "5eb8b5f9e863eb000199ef83",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                relatedEntityId: "object_5EgJR__c",
                relatedEntityName: "自行车_dddd",
                relatedFieldApiName: "field_nXN69__c",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71557"  || Lists.newArrayList(
                new ActionButton(action: "BatchAddRelatedObject", "label": "批量新建 自行车_dddd"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "批量新建关联对象有自定义按钮" || new StandardData(
                entityId: "object_UPfNh__c",
                objectId: "5eb8b5f9e863eb000199ef83",
                updateFromProperty: Task.UpdateFromProperty.create(false, false),// getDetail中会设置true/false 没有字段的时候会为false
                relatedEntityId: "object_5EgJR__c",
                relatedEntityName: "自行车_dddd",
                relatedFieldApiName: "field_nXN69__c",
                defaultButtons: ["batchAddRelatedObject": new DefaultActionLabel(label: "批量新建666666666")],
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71557"  || Lists.newArrayList(
                new ActionButton(action: "BatchAddRelatedObject", "label": "批量新建666666666"),
                new ActionButton(action: "Complete", "label": "完成")
        )
    }

    @Unroll
    def "选择或新建从对象-测试新接口返回自定义按钮 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        //def privilege = serviceManager.hasObjectFunctionPrivilege("object_pFsNR__c");
        //print(JacksonUtil.toJson(privilege))

        when:
        FormButtonResult formButtonResult = addMDObjectTaskButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc             || standardData              || tenantId || rst
        "选择或新建从对象无自定义按钮" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                relatedEntityId: "object_o578d__c",
                relatedEntityName: "从对象4",
                relatedObjectId: [expression: "activity_1569291437570##object_o578d__c"],
                target_related_list_name: "target_related_list_zQC49__c",
                relatedFieldApiName: "field_7uhdk__c",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddMDObject", "label": "选择或新建 从对象4")
        )

        "选择或新建从对象有自定义按钮" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                relatedEntityId: "object_o578d__c",
                relatedEntityName: "从对象4",
                relatedObjectId: [expression: "activity_1569291437570##object_o578d__c"],
                defaultButtons: ["addMDObject": new DefaultActionLabel(label: "新建从对象0000000")],
                target_related_list_name: "target_related_list_zQC49__c",
                relatedFieldApiName: "field_7uhdk__c",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddMDObject", "label": "新建从对象0000000")
        )


    }


    @Unroll
    def "编辑从对象 #desc"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)

        when:
        FormButtonResult formButtonResult = batchEditMasterDetailButtonHandler.setButtons(serviceManager, standardData, false, TaskParams.create())
        then:
        listToStringNew(formButtonResult.getButtons()) == listToStringNew(rst)
        where:
        desc     || standardData                      || tenantId || rst
        "无自定义按钮" || new StandardData(
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "BatchEditMasterDetailObject", "label": "编辑从对象"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "有自定义按钮" || new StandardData(
                defaultButtons: ["batchEditMasterDetailObject": new DefaultActionLabel(label: "批量编辑从对象aaa")],
                entityId: "object_pFsNR__c",
                objectId: "5d8328037cfed989fcf9ba89",
                objectPermissions: [
                        "changeowner"       : true,
                        "ChangeBPMApprover" : true,
                        "confirmreceive"    : false,
                        "confirmdelivery"   : false,
                        "Edit"              : true,
                        "View"              : true,
                        "ViewEntireBPM"     : true,
                        "followup"          : false,
                        "ViewBPMInstanceLog": false,
                        "transform"         : false,
                        "Discuss"           : false,
                        "addteammember"     : true,
                        "StopBPM"           : true,
                        "close"             : false,
                        "return"            : false]) || "71554"  || Lists.newArrayList(
                new ActionButton(action: "BatchEditMasterDetailObject", "label": "批量编辑从对象aaa"),
                new ActionButton(action: "Complete", "label": "完成")
        )
    }
}
