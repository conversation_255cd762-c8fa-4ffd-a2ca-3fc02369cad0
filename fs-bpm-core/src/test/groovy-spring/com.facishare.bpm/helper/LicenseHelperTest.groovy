package com.facishare.bpm.helper

import com.facishare.bpm.GBaseTest
import com.facishare.bpm.model.resource.paas.license.GetProductVersion
import com.facishare.bpm.proxy.PaasLicenseProxy
import com.facishare.paas.license.pojo.ProductVersionPojo
import com.facishare.rest.core.model.RemoteContext
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Shared

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/11/26 11:33 AM
 */
class LicenseHelperTest extends GBaseTest {

    @Autowired
    LicenseHelper licenseHelper;

    @Autowired
    PaasLicenseProxy paasLicenseProxy;

    @Shared
    def oLicenseValue

    @Shared
    GetProductVersion.Product oCRMVersionValue

    def "获取业务流license"() {
        given:
        when:
        def newValue = licenseHelper.getBPMQuota(context)
        oLicenseValue = paasLicenseProxy.getBPMQuota(context)
        then:
        newValue == oLicenseValue
        where:
        context                                       || rst
        new RemoteContext("", "71557", "CRM", "1000") || null
        new RemoteContext("", "71557", "BPM", "1000") || null
    }

    def "获取CRMVersion"() {
        given:
        when:
        ProductVersionPojo newValue = licenseHelper.getCRMVersion(context)
        oCRMVersionValue = paasLicenseProxy.getCRMVersion(context)
        then:
        newValue.getCurrentVersion() == oCRMVersionValue.getCurrentVersion()
        where:
        context                                       || rst
        new RemoteContext("", "71557", "CRM", "1000") || null
        new RemoteContext("", "71557", "BPM", "1000") || null
    }
}
