package com.facishare.bpm.service.impl

import com.facishare.bpm.model.resource.metadata.GetCountryAreaOptions
import com.facishare.bpm.proxy.MetadataOptionsProxy
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Profile
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by <PERSON> on 10/05/2017.
 */
@ContextConfiguration(locations = "classpath:applicationContext.xml")
@Profile(value = "ceshi113")
class MetaDataOptionsServiceTest extends Specification {
    @Autowired
    private MetadataOptionsProxy metadataOptionsProxy;


    def "获取国家省市区"() {
        when: "获取一次"
        metadataOptionsProxy.getArea(GetCountryAreaOptions.CountryAreaOption.city).options.size() > 0
        then: "结果"
        metadataOptionsProxy.getArea(GetCountryAreaOptions.CountryAreaOption.city).options.size() > 0
        metadataOptionsProxy.getArea(GetCountryAreaOptions.CountryAreaOption.country).options.size() > 0
        metadataOptionsProxy.getArea(GetCountryAreaOptions.CountryAreaOption.district).options.size() > 0
    }
}
