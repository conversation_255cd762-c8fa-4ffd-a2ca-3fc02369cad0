package com.facishare.bpm.service.impl

import com.facishare.bpm.exception.BPMNoQuotaException
import com.facishare.bpm.model.resource.paas.license.GetProductVersion
import com.facishare.bpm.proxy.PaasLicenseProxy
import com.facishare.flow.mongo.bizdb.TenantDao
import com.facishare.flow.mongo.bizdb.entity.TenantEntity
import com.facishare.rest.core.model.RemoteContext
import spock.lang.Specification

/**
 * Created by <PERSON> on 30/04/2017.
 */
class GBPMTenantServiceImplTest extends Specification {

    def "检测一个企业是否有配额"() {
        def context = Mock(RemoteContext)
        def tenantService = new BPMTenantServiceImpl()

        given: "初始需要用到的信息"
        def licenseService = Mock(PaasLicenseProxy)
        def tenantDao = Mock(TenantDao)
        tenantService.setTenantDao tenantDao
        tenantService.setLicenseServiceProxy licenseService
        licenseService.getCRMVersion(context) >> new GetProductVersion.Product(currentVersion: crmversion, versionName: versionName)
        licenseService.getBPMQuota(context) >> quota
        tenantDao.findAndModify(context) >> tenantEntity

        when: "验证是否有配额"
        tenantService.hasQuota(context)

        then: "验证不同场景下的结果是否正确"
        BPMNoQuotaException e = thrown()
        e.message == errorMessage
        println("$crmversion $something")


        where: "设置场景"
        error | something | crmversion                                             | versionName | quota | tenantEntity                                              || errorMessage
        true  | "测试通过"    | GetProductVersion.CRMVersion.basic_edition.name()      | ""          | 0     | new TenantEntity(sourceWorkflowCount: 1, validate: true)  || "您的企业没有配额，请联系客服人员"
        true  | "测试通过"    | GetProductVersion.CRMVersion.standard_edition.name()   | "标准版"       | 1     | new TenantEntity(sourceWorkflowCount: 1, validate: true)  || "您所在企业购买的标准版，“业务流程管理”为赠送使用，如果您想新建更多业务流程，请购买企业版。"
        true  | "测试通过"    | GetProductVersion.CRMVersion.standard_edition.name()   | "标准版"       | 1     | new TenantEntity(sourceWorkflowCount: 1, validate: true)  || "您所在企业购买的标准版，“业务流程管理”为赠送使用，如果您想新建更多业务流程，请购买企业版。"
        true  | "测试通过"    | GetProductVersion.CRMVersion.enterprise_edition.name() | "标准版"       | 10    | new TenantEntity(sourceWorkflowCount: 10, validate: true) || "您所在企业目前支持10个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。"
        //todo 正常情况下如何 测试

    }


}
