package com.facishare.bpm.service.impl

import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.model.resource.paas.PageResult
import com.facishare.bpm.proxy.AuthServiceProxy
import com.facishare.bpm.proxy.MDSProxy
import com.facishare.bpm.proxy.OrganizationServiceProxy
import com.facishare.bpm.remote.metadata.MetadataService
import com.facishare.bpm.remote.model.Page
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.bpm.util.WorkflowJsonUtil
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery
import com.facishare.rest.core.model.RemoteContext
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * Created by wangz on 17-6-13.
 */
class GBPMDefinitionServiceImplTest extends Specification {
    RemoteContext context
    MDSProxy mdsProxy = Mock()
    BpmSimpleDefinitionDao outlineDao = Mock()
    PaasWorkflowServiceProxy paasWorkflow = Mock()
    DefinitionExtensionDao workflowExtensionDao = Mock()
    BPMTenantServiceImpl bpmTenantService = Mock()
    OrganizationServiceProxy organizationServiceProxy = Mock()
    AuthServiceProxy authServiceProxy = Mock()
    MetadataService metadataService = Mock()
    def bpmDefinitionService = new BPMDefinitionServiceImpl()

    def setup() {
        context = new RemoteContext("ea", "tenantId", "appId", "userId")
        bpmDefinitionService.setMetadataService(metadataService)
        bpmDefinitionService.setOrganizationServiceProxy(organizationServiceProxy)
        bpmDefinitionService.setAuthServiceProxy(authServiceProxy)
        bpmDefinitionService.setBpmTenantService(bpmTenantService)
        bpmDefinitionService.setWorkflowExtensionDao(workflowExtensionDao)
        bpmDefinitionService.setPaasWorkflow(paasWorkflow)
        bpmDefinitionService.setOutlineDao(outlineDao)
    }

    def "DeployWorkflow"() {

    }

    def "UpdateWorkflow"() {
    }

    def "GetAvailableWorkflows 管理员身份查询可用列表"() {
        given: "mock 管理员身份"
        def entryType = "AccountObj"
        def objectId = "objectId"
        boolean isFilterObjectInstance = Mock()
        authServiceProxy.isCRMAdmin(context) >> isCRMAdmin
        outlineDao.findByEntryType(context.getTenantId(), ) >> outlineEntities


    }

    def "GetWorkflowOutlines : 更新entryTypeName"() {
        given:
        List<WorkflowOutlineEntity> entities = Lists.newArrayList()
        WorkflowOutlineEntity entity1 = new WorkflowOutlineEntity()
        entity1.setEntryType("apiname_1")
        entity1.setEntryTypeName("对象1")

        WorkflowOutlineEntity entity2 = new WorkflowOutlineEntity()
        entity2.setEntryType("apiname_2")
        entity2.setEntryTypeName("对象2")

        entities.add(entity1)
        entities.add(entity2)

        PageResult outlineEntities = new PageResult()
        outlineEntities.setTotal(2)
        outlineEntities.setDataList(entities)

        WorkflowOutlineQuery query = new WorkflowOutlineQuery()
        Page page = new Page()

        outlineDao.find(context.getTenantId(), query, page,true, null) >> outlineEntities
//        def entryTypeNames = ["apiname_2":"对象2的新名字","apiname_1":"对象1"]
//        print entryTypeNames

        when:
        PageResult<WorkflowOutline>  outlines = bpmDefinitionService.getWorkflowOutlines(context, query, new Page())
        def entryType1 = outlines.getDataList().get(0).getEntryType()
        def entryType2 = outlines.getDataList().get(1).getEntryType()
        def entryTypeName1 = outlines.getDataList().get(0).getEntryTypeName()
        def entryTypeName2 = outlines.getDataList().get(1).getEntryTypeName()
        def entryTypeNames =[:]
        entryTypeNames."$entryType1" =entryTypeName1
        entryTypeNames."$entryType2" =entryTypeName2

        then:
        outlines.getTotal() == 2
        entryTypeNames == result
        entryTypeNames.get("apiname_1") == "对象1"
        entryTypeNames.get("apiname_2") == "对象2的新名字"

        where:
        displayNames || result
        ["apiname_1":"对象1","apiname_2":"对象2的新名字"] ||["apiname_2":"对象2的新名字","apiname_1":"对象1"]
    }

    def "DeleteWorkflowById"() {
    }

    def "GetWorkflowOutlineById - 更新entryTypeName"() {
        given:
        def entity = new WorkflowOutlineEntity()
        entity.setEntryType("apiName")
        entity.setEntryTypeName("对象")
        def id = "id"

        outlineDao.find(context.getTenantId(), id) >> entity

        when:
        def outline = bpmDefinitionService.getWorkflowOutlineById(context,id)
        outline.getEntryTypeName() >> result

        then:
        displayNames.get("apiName") == result

        where:
        displayNames         || result
        ["apiName":"新名字1"] || "新名字1"
        ["apiName":"新名字2"] || "新名字2"

    }

    def "EnableWorkflow"() {
    }

    def "GetWorkflowExtensionByWorkflowId"() {
    }

    def "GetWorkflowOutlineBySourceId"() {
    }

    def "GetWorkflowEntryTypeNameMap"() {
    }

    def "GetWorkflowById"() {
    }

    def "GetActivityDefByActivityId"() {
        given:
        def workflowJson = "{\"type\":\"workflow_bpm\",\"name\":\"客户流程测试1\",\"description\":\"客户流程测试\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"actionCode\":\"0\",\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"AccountObj\",\"entityName\":\"客户\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"},\"form\":[[{\"name\":\"name\",\"label\":\"客户名称\",\"type\":\"text\",\"required\":true},{\"name\":\"account_source\",\"label\":\"来源\",\"type\":\"select_one\",\"required\":false,\"readonly\":false},{\"name\":\"tel\",\"label\":\"电话\",\"type\":\"text\",\"required\":true},{\"name\":\"url\",\"label\":\"网址\",\"type\":\"text\",\"required\":false}]]},\"canSkip\":false,\"name\":\"客户信息补全1\",\"description\":\"客户信息补全\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"parallelGateway\",\"name\":\"＋\",\"description\":\"\",\"id\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"},\"form\":[[{\"name\":\"remark\",\"label\":\"备注\",\"type\":\"long_text\",\"required\":false}]]},\"name\":\"财务信息补全\",\"description\":\"财务信息补全\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"},\"form\":[[{\"name\":\"remark\",\"label\":\"备注\",\"type\":\"long_text\",\"required\":false}]]},\"remindLatency\":1,\"remind\":true,\"name\":\"法务信息补全\",\"description\":\"法务信息补全\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"actionCode\":\"0\",\"executionType\":\"approve\",\"executionName\":\"\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"},\"form\":[[{\"name\":\"result\",\"value\":true,\"label\":\"审批结果\",\"type\":\"text\",\"readonly\":false,\"required\":true}]],\"entityId\":\"AccountObj\"},\"remindLatency\":1,\"remind\":true,\"name\":\"财务审批\",\"description\":\"财务审批\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"actionCode\":\"0\",\"executionType\":\"approve\",\"executionName\":\"\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"},\"form\":[[{\"name\":\"result\",\"value\":true,\"label\":\"会签结果\",\"type\":\"text\",\"readonly\":false,\"required\":true}]],\"entityId\":\"AccountObj\"},\"remindLatency\":1,\"remind\":true,\"name\":\"法务会签\",\"description\":\"法务会签\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"all\"},{\"type\":\"parallelGateway\",\"name\":\"＋\",\"description\":\"\",\"id\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"actionCode\":\"AddTeamMember\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"name\":\"添加团队成员\",\"description\":\"添加团队成员\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"actionCode\":\"ChangeOwner\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"remindLatency\":1,\"remind\":true,\"name\":\"变更负责人\",\"description\":\"变更负责人\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"parallelGateway\",\"name\":\"＋\",\"description\":\"\",\"id\":\"*************\"},{\"type\":\"exclusiveGateway\",\"name\":\"智能匹配客服\",\"description\":\"智能匹配客服\",\"id\":\"*************\",\"defaultTransitionId\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"name\":\"天津客服\",\"description\":\"天津客服\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"remindLatency\":1,\"remind\":true,\"name\":\"北京客服\",\"description\":\"北京客服\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"remindLatency\":1,\"remind\":true,\"name\":\"复核验收\",\"description\":\"复核验收\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程发起人\"]},\"taskType\":\"anyone\"},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"*************\"}],\"transitions\":[{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##result\"},\"right\":{\"value\":\"reject\",\"type\":{\"name\":\"text\"}}}]}},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##result\"},\"right\":{\"value\":\"reject\",\"type\":{\"name\":\"text\"}}}]}},{\"id\":\"1494059106421\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##result\"},\"right\":{\"value\":\"agree\",\"type\":{\"name\":\"text\"}}}]}},{\"id\":\"1494059106422\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##result\"},\"right\":{\"value\":\"agree\",\"type\":{\"name\":\"text\"}}}]}},{\"id\":\"1494059106427\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"1494059106428\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"description\":\"流程发起数据 / 客户 / 详细地址 包含 天津\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"contains\",\"left\":{\"expression\":\"activity_0##AccountObj##address\"},\"right\":{\"value\":\"天津\",\"type\":{\"name\":\"text\"}}}]}]}},{\"description\":\"流程发起数据 / 客户 / 地址 包含 北京\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"contains\",\"left\":{\"expression\":\"activity_0##AccountObj##location\"},\"right\":{\"value\":\"北京\",\"type\":{\"name\":\"text\"}}}]}]}},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"}],\"variables\":[{\"id\":\"activity_0##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##result\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##result\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_0##AccountObj##address\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_0##AccountObj##location\",\"type\":{\"name\":\"text\"}}],\"id\":\"5925eb953db71dd5a48e3dad\",\"sourceWorkflowId\":\"252016043760451584\"}"
        def workflow = WorkflowJsonUtil.convertExecutableWorkflowFromJson(workflowJson)
        def defineService = new BPMDefinitionServiceImpl()
        def paasWorkflow = Mock(PaasWorkflowServiceProxy)
        paasWorkflow.getWorkflow(_, _) >> workflow
        defineService.setPaasWorkflow(paasWorkflow)
        def context = new RemoteContext()
        def activityId = "*************"
        def exclusiveGatewayActivityId = "*************"
        def approveId = "*************"
        when:
        def activity = defineService.getActivityDefByActivityId(context, "", activityId)
        def exclusiveGatewayActivity = defineService.getActivityDefByActivityId(context, "", exclusiveGatewayActivityId)
        def approveActivity = defineService.getActivityDefByActivityId(context, "", approveId)
        then:
        activity != null
        exclusiveGatewayActivity.get("transitions") != null
        approveActivity.get("transitions") != null
    }



}
