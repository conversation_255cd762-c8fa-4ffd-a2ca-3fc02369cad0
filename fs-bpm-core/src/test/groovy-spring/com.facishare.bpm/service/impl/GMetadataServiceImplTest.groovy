package com.facishare.bpm.service.impl

import com.facishare.bpm.proxy.CRMProxy
import com.facishare.bpm.proxy.GDSProxy
import com.facishare.bpm.proxy.PaasMetadataProxy
import com.facishare.bpm.remote.metadata.impl.MetadataServiceImpl
import com.facishare.bpm.utils.IDCreatorUtil
import com.facishare.rest.core.model.RemoteContext
import lombok.extern.slf4j.Slf4j
import spock.lang.Specification

/**
 * Created by cuiyongxu on 17/6/28.
 */
@Slf4j
class GMetadataServiceImplTest extends Specification {

    def metadataService;

    def metadataResource;
    def crmService;
    def gdsManager;

    def setup() {
        gdsManager = Mock(GDSProxy.class)
        crmService = Mock(CRMProxy.class)
        metadataResource = Mock(PaasMetadataProxy.class)

        metadataService = new MetadataServiceImpl(
                metadataResource: metadataResource,crmService:crmService,gdsManager:gdsManager);
    }


    def remotContext = new RemoteContext(ea: "54821", tenantId: "54821", userId: "1000", appId: "BPM")
    def apiName = "AccountObj"


    def "查询对象描述"() {
        given:
        //getPathParams(_, _, _, _) >> getPathParams();
        def describe = metadataService.findDescribe(remotContext, apiName, false)
        println describe
    }

    def "根据actionCode查询actionName"() {
        given:
        def actionName = metadataService.getActionNameByActionCode(remotContext, apiName, "ChangeOwner")
        println actionName
    }


    def getPathParams() {
        [describeApiName: apiName, tenantId: remotContext.tenantId, dataId: IDCreatorUtil.createId(), actionCode: "ChangeOwner"]
    }

}
