package com.facishare.bpm.service.impl

import com.facishare.bpm.manage.FormButtonManager
import com.facishare.bpm.model.paas.engine.approvalflow.CRMConstants
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey
import com.facishare.bpm.model.task.BPMTask
import com.facishare.bpm.remote.metadata.MetadataService
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.flow.mongo.bizdb.BizTaskDataDao
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
import com.facishare.rest.core.model.RemoteContext
import com.fxiaoke.common.Pair
import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * Created by wangz on 17-6-19.
 */
class GBPMTaskServiceImplTest extends Specification {
    RemoteContext context

    def paasWorkflow = Mock(PaasWorkflowServiceProxy)
    def workflowExtensionDao = Mock(DefinitionExtensionDao)
    def metadataService = Mock(MetadataService)
    def formButtonManager = Mock(FormButtonManager)
    def taskDataDao = Mock(BizTaskDataDao)

    def taskService = new BPMTaskServiceImpl()

    def setup() {
        context = new RemoteContext("ea", "tenantId", "appId", "userId")
        taskService.setPaasWorkflow(paasWorkflow)
        taskService.setWorkflowExtensionDao(workflowExtensionDao)
        taskService.setFormButtonManager(formButtonManager)
        taskService.setBizTaskDataDao(taskDataDao)
    }

    def "GetBPMTask - 动态更新entityName/relatedEntityName"() {
        given: "mock 数据"
        def objectId = "objectId"
        def bpmExtension = Maps.newHashMap()
        bpmExtension.put("entityId", "apiName1")
        bpmExtension.put("entityName", "对象1")
        bpmExtension.put("relatedEntityId", "apiName2")
        bpmExtension.put("relatedEntityName", "关联对象")
        def objectIdExp = Maps.newHashMap()
        objectIdExp.put("expression", "activity_0##apiName1")
        bpmExtension.put("objectId", objectIdExp)
        bpmExtension.put(WorkflowKey.ActivityKey.ExtensionKey.executionType, ExecutionTypeEnum.addRelatedObject.name())

        def taskId = "id"
        def instanceId = "iid"
        def task = new Task()
        task.setId(taskId)
        task.setWorkflowInstanceId(instanceId)
        task.setBpmExtension(bpmExtension)

        def workflowInstance = new WorkflowInstance()
        def variable = Maps.newHashMap()
        variable.put("activity_0##apiName1", objectId)
        workflowInstance.setId(instanceId)
        workflowInstance.setVariables(variable)

        def data = Maps.newHashMap()
        data.put("id", objectId)
        data.put("name", "name")
        def entityIdObjectId = new Pair<String,String>("apiName1",objectId)
        def taskDatas = Maps.newHashMap()
        taskDatas.put(entityIdObjectId,data)
        paasWorkflow.getTask(context, taskId) >> task
        paasWorkflow.getWorkflowInstance(context, instanceId) >> workflowInstance

        when:
        BPMTask bpmTask = taskService.getTask(context, taskId)
        def entityName = bpmTask.getExtension().get(CRMConstants.ExtensionKey.entityName)
        def relatedEntityName = bpmTask.getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityName)

        then:
        entityName == entityName_expect
        relatedEntityName == relatedEntityName_expect

        where:
        displayNames                                                || entityName_expect     || relatedEntityName_expect
        ["apiName1": "对象1的新name", "apiName2": "关联对象的新name"]  || "对象1的新name"         || "关联对象的新name"

    }
}
