package com.facishare.bpm.service

import com.facishare.bpm.proxy.ApiBusProxy
import com.facishare.bpm.proxy.NewPaasMetadataProxy
import com.facishare.rest.core.model.RemoteContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/4/27 7:30 下午
 */
@ContextConfiguration(locations = "classpath:applicationContext.xml")
class MetadataGetValue extends Specification {

    @Autowired
    private NewPaasMetadataProxy newPaasMetadataProxy;

    @Autowired
    private ApiBusProxy apiBusProxy;

    def "getvalue"() {
        when:
        RemoteContext context = new RemoteContext("", tenantId, "CRM", userId);

        println descApiName
        Map<String, Object> oldValue = newPaasMetadataProxy.findDataByIdWithWhitDistribution(context,
                descApiName,
                dataId,
                includeInvalid,
                includeDeleted,
                includeDescribe,
                checkPrivilege);

        Map<String, Object> newValue = apiBusProxy.findDataById(context,
                descApiName,
                dataId,
                includeInvalid,
                includeDeleted,
                includeDescribe,
                checkPrivilege,
                doCalculate,
                includeQuoteValue,
                includeLookupName,
                includeStatistics
        );


        then:
        boolean flag = true;
        oldValue.keySet().forEach({ key ->
            key
            flag = flag && oldValue.get(key) == newValue.get(key)
        })

        flag == Boolean.TRUE

        where:
        desc | tenantId | userId | descApiName       | dataId                     | includeInvalid | includeDeleted | includeDescribe | checkPrivilege | doCalculate | includeQuoteValue | includeLookupName | includeStatistics || rst
        ""   | "71557"  | "1017" | "object_5EgJR__c" | "5cb8221e7cfed9b176b681fb" | true           | true           | true            | true           | true        | true              | true              | true              || null
    }

}
