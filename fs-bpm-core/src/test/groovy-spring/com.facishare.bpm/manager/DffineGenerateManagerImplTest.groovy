package com.facishare.bpm.manager

import com.facishare.bpm.GBaseTest
import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.manage.impl.DefineGenerateManagerImpl
import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.service.BPMBaseService
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils
import com.facishare.rest.core.model.RemoteContext
import com.facishare.rest.core.util.JacksonUtil
import org.springframework.beans.factory.annotation.Autowired

class DffineGenerateManagerImplTest extends GBaseTest {

    @Autowired
    DefineGenerateManagerImpl defineGenerateManager
    @Autowired
    private BPMBaseService baseService;


    def "hello"() {
        given:
        print(1)
    }

    def "testGenerate"() {
        given:
        String variables = "[{\"id\":\"activity_1691304283397##object_77d7H__c##field_Hf3Ys__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_0##object_cDq6i__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283385##object_cDq6i__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283385##object_7wq09__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283395##object_7wq09__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283395##object_jxm4e__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283396##object_jxm4e__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283396##object_77d7H__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283397##object_VFi2e__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1691304283405##object_VFi2e__c\",\"type\":{\"name\":\"text\"}}],\"id\":\"64cf67f17d059953f875ef69\",\"sourceWorkflowId\":\"64cf5a8b5707e7000104857a\",\"createTime\":1691310731352,\"tenantId\":\"71557\"},\"rule\":{\"ruleId\":\"64cf67f17d059953f875ef6a\",\"deleted\":false,\"entityId\":\"object_cDq6i__c\",\"conditionPattern\":\"(0)\",\"conditions\":[{\"leftSide\":{\"fieldName\":\"activity_0##object_cDq6i__c##field_eeq1k__c\",\"fieldSrc\":\"activity_0##object_cDq6i__c\",\"fieldType\":\"string\"},\"operator\":\"notEmpty\",\"rightSide\":{\"value\":\"\",\"metadata\":{\"containSubDept\":false}},\"rowNo\":0}]";
        String outlineJson = "{\"id\":\"64cf5a8b5707e7000104857b\",\"tenantId\":\"71557\",\"userId\":\"1002\",\"sourceWorkflowId\":\"64cf5a8b5707e7000104857a\",\"workflowId\":\"64cf67f17d059953f875ef69\",\"name\":\"variables测试111-1\",\"count\":0,\"enabled\":true,\"description\":\"\",\"entryType\":\"object_cDq6i__c\",\"entryTypeName\":\"lc测试111-关联C\",\"rangeEmployeeIds\":[1132],\"rangeCircleIds\":[],\"rangeGroupIds\":[],\"rangeRoleIds\":[],\"createdBy\":\"1002\",\"createTime\":1691310731131,\"lastModifiedBy\":\"1002\",\"lastModifiedTime\":1691314161322,\"workflow\":{\"creator\":\"1002\",\"externalFlow\":0,\"modifier\":\"1002\",\"entityId\":\"object_cDq6i__c\",\"errorNotifyRecipients\":{},\"history\":false,\"type\":\"workflow_bpm\",\"singleInstanceFlow\":0,\"modifyTime\":1691314161532,\"enable\":true,\"appId\":\"BPM\",\"linkAppEnable\":false,\"name\":\"variables测试111-1\",\"description\":\"\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"1691304283384\"},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":3,\"remindContent\":\"\${activity_1691304283385 # # object_cDq6i__c # # field_eeq1k__c},\${activity_1691304283385 # # object_cDq6i__c # # owner}\",\"remindTime\":0,\"channel\":\"send_sms\"}],\"bpmExtension\":{\"executionType\":\"addRelatedObject\",\"executionName\":\"选择或新建关联对象\",\"entityId\":\"object_cDq6i__c\",\"entityName\":\"lc测试111-关联C\",\"objectId\":{\"expression\":\"activity_0##object_cDq6i__c\"},\"commonButtonApiNames\":\"\",\"relatedEntityName\":\"lc测试111-关联B(查找关联-关联c)\",\"relatedObjectId\":{\"expression\":\"activity_1691304283385##object_7wq09__c\"},\"target_related_list_name\":\"target_related_list_RhvsF__c\",\"relatedEntityId\":\"object_7wq09__c\"},\"canSkip\":false,\"remind\":false,\"linkAppEnable\":false,\"name\":\"业务活动-c\",\"description\":\"\",\"id\":\"1691304283385\",\"assignee\":{\"ext_bpm\":[\"\${instance # # owner}\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":3,\"remindContent\":\"\${activity_1691304283395 # # object_7wq09__c # # field_QQ87i__c},\${activity_1691304283395 # # object_7wq09__c # # field_mmXc9__c}\",\"remindTime\":0,\"channel\":\"send_sms\"}],\"bpmExtension\":{\"executionName\":\"选择或新建关联对象\",\"entityName\":\"lc测试111-关联B\",\"entityId\":\"object_7wq09__c\",\"executionType\":\"addRelatedObject\",\"objectId\":{\"expression\":\"activity_1691304283385##object_7wq09__c\"},\"relatedEntityName\":\"lc测试111-关联A(查找关联-关联b)\",\"relatedObjectId\":{\"expression\":\"activity_1691304283395##object_jxm4e__c\"},\"target_related_list_name\":\"target_related_list_m6p5b__c\",\"relatedEntityId\":\"object_jxm4e__c\"},\"remind\":false,\"linkAppEnable\":false,\"itemList\":[],\"name\":\"业务活动-b\",\"description\":\"\",\"id\":\"1691304283395\",\"assignee\":{\"ext_bpm\":[\"\${instance # # owner}\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":3,\"remindContent\":\"\${activity_1691304283396 # # object_jxm4e__c # # field_dz021__c},\${activity_1691304283396 # # object_jxm4e__c # # field_aaGe5__c},\${activity_1691304283396 # # object_jxm4e__c # # field_c1pFe__c},\${activity_1691304283396 # # object_jxm4e__c # # field_Da82q__c},\${activity_1691304283396 # # object_jxm4e__c # # field_aaGe5__c}\",\"remindTime\":0,\"channel\":\"send_sms\"}],\"bpmExtension\":{\"executionName\":\"选择或新建关联对象\",\"entityName\":\"lc测试111-关联A\",\"entityId\":\"object_jxm4e__c\",\"executionType\":\"addRelatedObject\",\"objectId\":{\"expression\":\"activity_1691304283395##object_jxm4e__c\"},\"relatedEntityName\":\"lc测试111(查找关联-关联a)\",\"relatedObjectId\":{\"expression\":\"activity_1691304283396##object_77d7H__c\"},\"target_related_list_name\":\"target_related_list_Ogz4a__c\",\"relatedEntityId\":\"object_77d7H__c\"},\"remind\":false,\"linkAppEnable\":false,\"itemList\":[],\"name\":\"业务活动-a\",\"description\":\"\",\"id\":\"1691304283396\",\"assignee\":{\"ext_bpm\":[\"\${instance # # owner}\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":3,\"remindContent\":\"\${activity_1691304283397 # # object_77d7H__c # # field_qqqqqq__c},\${activity_1691304283397 # # object_77d7H__c # # field_Hf3Ys__c},\${activity_1691304283397 # # object_77d7H__c # # field_8ap3u__c},\${activity_1691304283397 # # object_77d7H__c # # field_eo791__c},\${activity_1691304283397 # # object_77d7H__c # # field_ax68v__c # # field_mmBM8__c}\",\"remindTime\":0,\"channel\":\"send_sms\"}],\"bpmExtension\":{\"executionName\":\"选择或新建关联对象\",\"entityName\":\"lc测试111\",\"entityId\":\"object_77d7H__c\",\"executionType\":\"addRelatedObject\",\"objectId\":{\"expression\":\"activity_1691304283396##object_77d7H__c\"},\"relatedEntityName\":\"111关联对象(查找关联)\",\"relatedObjectId\":{\"expression\":\"activity_1691304283397##object_VFi2e__c\"},\"target_related_list_name\":\"target_related_list_xVqY2__c\",\"relatedEntityId\":\"object_VFi2e__c\"},\"remind\":false,\"linkAppEnable\":false,\"itemList\":[],\"name\":\"业务活动-111\",\"description\":\"\",\"id\":\"1691304283397\",\"assignee\":{\"ext_bpm\":[\"\${instance # # owner}\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"1691304283398\"},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":3,\"remindContent\":\"\${activity_1691304283405 # # object_VFi2e__c # # field_2m834__c}\",\"remindTime\":0,\"channel\":\"send_sms\"}],\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"111关联对象\",\"entityId\":\"object_VFi2e__c\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_1691304283397##object_VFi2e__c\"},\"fieldApiName\":\"\"},\"remind\":false,\"linkAppEnable\":false,\"itemList\":[],\"name\":\"业务活动-111关联对象\",\"description\":\"\",\"id\":\"1691304283405\",\"assignee\":{\"ext_bpm\":[\"\${instance # # owner}\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0}],\"transitions\":[{\"id\":\"1691304283387\",\"fromId\":\"1691304283384\",\"toId\":\"1691304283385\",\"serialNumber\":0},{\"id\":\"1691304283399\",\"fromId\":\"1691304283385\",\"toId\":\"1691304283395\",\"serialNumber\":1},{\"id\":\"1691304283401\",\"fromId\":\"1691304283395\",\"toId\":\"1691304283396\",\"serialNumber\":2},{\"id\":\"1691304283403\",\"fromId\":\"1691304283396\",\"toId\":\"1691304283397\",\"serialNumber\":3},{\"id\":\"1691304283406\",\"fromId\":\"1691304283397\",\"toId\":\"1691304283405\",\"serialNumber\":4},{\"id\":\"1691304283408\",\"fromId\":\"1691304283405\",\"toId\":\"1691304283398\",\"serialNumber\":5}],\"variables\":"+ variables+",\"workflowSrcId\":\"64cf5a8b5707e7000104857a\"},\"extension\":{\"pools\":[{\"lanes\":[{\"id\":\"1691304283386\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"1691304283384\",\"1691304283385\",\"1691304283395\",\"1691304283396\",\"1691304283397\",\"1691304283398\",\"1691304283405\"]}]}],\"diagram\":[{\"id\":\"1691304283386\",\"attr\":{\"width\":920,\"height\":740,\"x\":80,\"y\":60}},{\"id\":\"1691304283384\",\"attr\":{\"width\":60,\"height\":60,\"x\":160,\"y\":125}},{\"id\":\"1691304283385\",\"attr\":{\"width\":160,\"height\":50,\"x\":110,\"y\":260}},{\"id\":\"1691304283395\",\"attr\":{\"width\":160,\"height\":50,\"x\":110,\"y\":370}},{\"id\":\"1691304283396\",\"attr\":{\"width\":160,\"height\":50,\"x\":110,\"y\":470}},{\"id\":\"1691304283397\",\"attr\":{\"width\":160,\"height\":50,\"x\":110,\"y\":570}},{\"id\":\"1691304283398\",\"attr\":{\"width\":60,\"height\":60,\"x\":470,\"y\":390}},{\"id\":\"1691304283405\",\"attr\":{\"width\":160,\"height\":50,\"x\":423,\"y\":142}},{\"id\":\"1691304283408\",\"attr\":{\"d\":\"M503,192 v98 a1.5,1.5 0 0 1 -1.5,1.5 h0 a1.5,1.5 0 0 0 -1.5,1.5 v98\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}},{\"id\":\"1691304283406\",\"attr\":{\"d\":\"M270,595 h57 a20,20 0 0 0 20,-20 v-388 a20,20 0 0 1 20,-20 h57\",\"toPosition\":\"left\",\"fromPosition\":\"right\",\"type\":\"polyline\"}},{\"id\":\"1691304283403\",\"attr\":{\"d\":\"M190,520 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}},{\"id\":\"1691304283401\",\"attr\":{\"d\":\"M190,420 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}},{\"id\":\"1691304283399\",\"attr\":{\"d\":\"M190,310 v31 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v31\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}},{\"id\":\"1691304283387\",\"attr\":{\"d\":\"M190,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\",\"toPosition\":\"top\",\"fromPosition\":\"bottom\",\"type\":\"polyline\"}}],\"svg\":\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" class=\\\"paas-bpm-canvas\\\" height=850 width=1050 tabindex=\\\"0\\\"><defs><marker id=\\\"end-arrow_1691304283410\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#666666\\\"></path></marker><marker id=\\\"end-arrow-colored_1691304283410\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#49bffc\\\"></path></marker><marker id=\\\"approval-yes_1691304283410\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#7FC25D\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><marker id=\\\"approval-no_1691304283410\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#DC9688\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><filter id=\\\"box-shadow\\\"><feGaussianBlur in=\\\"SourceAlpha\\\" stdDeviation=\\\"2\\\"></feGaussianBlur><feOffset dx=\\\"0\\\" dy=\\\"1\\\" result=\\\"offsetblur\\\"></feOffset><feFlood flood-color=\\\"black\\\" flood-opacity=\\\"0.06\\\"></feFlood><feComposite in2=\\\"offsetblur\\\" operator=\\\"in\\\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\\\"SourceGraphic\\\"></feMergeNode></feMerge></filter><style type=\\\"text/css\\\">svg {\\n          background-color: #f3f3f5;\\n        }\\n\\n        g[type=pool] {\\n          font-size: 13px;\\n        }</style></defs><g class=\\\"paas-bpm-canvas-wrapper\\\" height=\\\"100%\\\" width=\\\"100%\\\" transform=\\\"translate(0,0) scale(1)\\\"><g name=\\\"pool-wrapper\\\"><g data-id=\\\"1691304283386\\\" shape=\\\"rectangle\\\" type=\\\"pool\\\" x=\\\"80\\\" y=\\\"60\\\" width=\\\"920\\\" height=\\\"740\\\" transform=\\\"translate(80,60)\\\" tabindex=\\\"0\\\" class=\\\"paas-bpm-resizable\\\"><rect fill=\\\"rgba(255,255,255,0.4)\\\" width=\\\"920\\\" height=\\\"740\\\" namePos=\\\"top\\\" highlight=\\\"false\\\" stroke=\\\"#5498FF\\\" rx=\\\"0\\\" ry=\\\"0\\\" resizable=\\\"true\\\" external=\\\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"920\\\" height=\\\"740\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"-4.5\\\" y=\\\"365.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"455.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"915.5\\\" y=\\\"365.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"455.5\\\" y=\\\"735.5\\\"></rect></g><g name=\\\"external\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"32\\\" stroke=\\\"transparent\\\" rx=\\\"0\\\" ry=\\\"0\\\" y=\\\"0\\\" class=\\\"paas-bpm-pool-drag-area\\\"></rect></g><text node-name=\\\"node-name\\\" transform=\\\"translate(10,15)\\\"><tspan>阶段</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 750)\\\" trans-y=\\\"750\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g></g><g name=\\\"line-wrapper\\\"><g tabindex=\\\"0\\\" data-id=\\\"1691304283408\\\"><path d=\\\"M503,192 v98 a1.5,1.5 0 0 1 -1.5,1.5 h0 a1.5,1.5 0 0 0 -1.5,1.5 v98\\\" start-id=\\\"1691304283405\\\" end-id=\\\"1691304283398\\\" fill=\\\"transparent\\\" stroke-width=\\\"2\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1691304283410)\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"1691304283406\\\"><path d=\\\"M270,595 h57 a20,20 0 0 0 20,-20 v-388 a20,20 0 0 1 20,-20 h57\\\" start-id=\\\"1691304283397\\\" end-id=\\\"1691304283405\\\" fill=\\\"transparent\\\" stroke-width=\\\"2\\\" type=\\\"polyline\\\" to-position=\\\"left\\\" from-position=\\\"right\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1691304283410)\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"1691304283403\\\"><path d=\\\"M190,520 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\\\" start-id=\\\"1691304283396\\\" end-id=\\\"1691304283397\\\" fill=\\\"transparent\\\" stroke-width=\\\"2\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1691304283410)\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"1691304283401\\\"><path d=\\\"M190,420 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\\\" start-id=\\\"1691304283395\\\" end-id=\\\"1691304283396\\\" fill=\\\"transparent\\\" stroke-width=\\\"2\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1691304283410)\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"1691304283399\\\"><path d=\\\"M190,310 v31 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v31\\\" start-id=\\\"1691304283385\\\" end-id=\\\"1691304283395\\\" fill=\\\"transparent\\\" stroke-width=\\\"2\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1691304283410)\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"1691304283387\\\"><path d=\\\"M190,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\\\" start-id=\\\"1691304283384\\\" end-id=\\\"1691304283385\\\" fill=\\\"transparent\\\" stroke-width=\\\"2\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1691304283410)\\\"></path></g></g><g data-id=\\\"1691304283384\\\" shape=\\\"rectangle\\\" type=\\\"startEvent\\\" x=\\\"160\\\" y=\\\"125\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(160,125)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#16B4AB\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#16B4AB\\\" stroke-width=\\\"3\\\" color=\\\"#FFFFFF\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"21.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"21.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"51.5\\\" y=\\\"21.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"21.5\\\" y=\\\"51.5\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#FFFFFF\\\" transform=\\\"translate(30,35)\\\"><tspan>开始</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 70)\\\" trans-y=\\\"70\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1691304283385\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"110\\\" y=\\\"260\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(110,260)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#E6F4FF\\\" width=\\\"160\\\" height=\\\"50\\\" stroke=\\\"#368DFF\\\" rx=\\\"3\\\" ry=\\\"3\\\" textTransformLineOne=\\\"translate(80,30)\\\" textTransformLineTwo=\\\"translate(80,20)\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"151.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"41.5\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" transform=\\\"translate(80,30)\\\"><tspan>业务活动-c</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 60)\\\" trans-y=\\\"60\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1691304283395\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"110\\\" y=\\\"370\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(110,370)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#E6F4FF\\\" width=\\\"160\\\" height=\\\"50\\\" stroke=\\\"#368DFF\\\" rx=\\\"3\\\" ry=\\\"3\\\" textTransformLineOne=\\\"translate(80,30)\\\" textTransformLineTwo=\\\"translate(80,20)\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"151.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"41.5\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" transform=\\\"translate(80,30)\\\"><tspan>业务活动-b</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 60)\\\" trans-y=\\\"60\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1691304283396\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"110\\\" y=\\\"470\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(110,470)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#E6F4FF\\\" width=\\\"160\\\" height=\\\"50\\\" stroke=\\\"#368DFF\\\" rx=\\\"3\\\" ry=\\\"3\\\" textTransformLineOne=\\\"translate(80,30)\\\" textTransformLineTwo=\\\"translate(80,20)\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"151.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"41.5\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" transform=\\\"translate(80,30)\\\"><tspan>业务活动-a</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 60)\\\" trans-y=\\\"60\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1691304283397\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"110\\\" y=\\\"570\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(110,570)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#E6F4FF\\\" width=\\\"160\\\" height=\\\"50\\\" stroke=\\\"#368DFF\\\" rx=\\\"3\\\" ry=\\\"3\\\" textTransformLineOne=\\\"translate(80,30)\\\" textTransformLineTwo=\\\"translate(80,20)\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"151.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"41.5\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" transform=\\\"translate(80,30)\\\"><tspan>业务活动-111</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 60)\\\" trans-y=\\\"60\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1691304283398\\\" shape=\\\"rectangle\\\" type=\\\"endEvent\\\" x=\\\"470\\\" y=\\\"390\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(470,390)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#737C8C\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#737C8C\\\" stroke-width=\\\"3\\\" color=\\\"#FFFFFF\\\" external=\\\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#FFFFFF&quot; stroke-width=&quot;2&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"21.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"21.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"51.5\\\" y=\\\"21.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"21.5\\\" y=\\\"51.5\\\"></rect></g><g name=\\\"external\\\"><rect x=\\\"4\\\" y=\\\"4\\\" fill=\\\"transparent\\\" rx=\\\"52\\\" ry=\\\"52\\\" width=\\\"52\\\" height=\\\"52\\\" stroke=\\\"#FFFFFF\\\" stroke-width=\\\"2\\\" color=\\\"#e67373\\\" type=\\\"rect\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#FFFFFF\\\" transform=\\\"translate(30,35)\\\"><tspan>结束</tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 70)\\\" trans-y=\\\"70\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1691304283405\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"423\\\" y=\\\"142\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(423,142)\\\" tabindex=\\\"0\\\"><rect fill=\\\"#E6F4FF\\\" width=\\\"160\\\" height=\\\"50\\\" stroke=\\\"#368DFF\\\" rx=\\\"3\\\" ry=\\\"3\\\" textTransformLineOne=\\\"translate(80,30)\\\" textTransformLineTwo=\\\"translate(80,20)\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"-8.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"-8.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"151.5\\\" y=\\\"16.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"17\\\" height=\\\"17\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"17\\\" ry=\\\"17\\\" x=\\\"71.5\\\" y=\\\"41.5\\\"></rect></g><text node-name=\\\"node-name\\\" text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" transform=\\\"translate(80,20)\\\"><tspan x=\\\"0\\\" y=\\\"0\\\">业务活动-111关联<title>业务活动-111关联对象</title></tspan><tspan x=\\\"0\\\" y=\\\"20\\\">对象<title>业务活动-111关联对象</title></tspan></text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 60)\\\" trans-y=\\\"60\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g></g><rect class=\\\"paas-bpm-canvas-drag-area hide\\\" height=\\\"100%\\\" width=\\\"100%\\\" fill=\\\"transparent\\\"></rect></svg>\"},\"externalFlow\":0,\"singleInstanceFlow\":0,\"isDeleted\":false,\"hasInstance\":false,\"linkAppEnable\":false}";
        RemoteContext context = new RemoteContext();
        String tenantId = "71557";
        context.setAppId("BPM")
        context.setUserId("1002")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)

        defineGenerateManager.generateVariables(JacksonUtil.fromJson(outlineJson, WorkflowOutline.class), serviceManager);
    }
    def "testCommon"(){
        given:
        String expression = "activity_1691304283405##object_VFi2e__c"
        println(ValidateVariableAndContentUtils.getInnerKeyByFirst(expression))
        RemoteContext context = new RemoteContext();
        String tenantId = "71557";
        context.setAppId("BPM")
        context.setUserId("1002")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)

        println(defineGenerateManager.getType(expression, serviceManager))

    }
}
