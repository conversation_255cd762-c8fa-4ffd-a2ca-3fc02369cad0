package com.facishare.bpm.manager

import com.facishare.bpm.GBaseTest
import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.manage.FormButtonManager
import com.facishare.bpm.model.ActionButton
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.TaskState
import com.facishare.bpm.model.task.BPMTask
import com.facishare.bpm.service.BPMBaseService
import com.facishare.rest.core.model.RemoteContext
import com.facishare.rest.core.util.JacksonUtil
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/9/20 6:32 PM
 */
class NewTaskButtonManagerImplTest extends GBaseTest {


    @Autowired
    FormButtonManager formButtonManager;

    @Autowired
    private BPMBaseService baseService;

    def "老接口获取自定义按钮"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId(tenantId)
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        println JacksonUtil.toJson(bpmTask.buttons)

        when:
        formButtonManager.setButtons(serviceManager, bpmTask,taskParams)
        def buttons = bpmTask.getButtons()
        then:
        listToString(rst) == listToString(buttons)
        where:
        label                          || taskParams ||bpmTask || tenantId || rst
        "更新,保存,保存并完成任务,完成任务 全部都有自定义按钮" || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: [
                                "update"           : ["label": "填写66666"],//填写的按钮
                                "Save"             : ["label": "保存66666"],// 保存的按钮
                                "UpdateAndComplete": ["label": "更新并保存666"],//保存并完成任务按钮
                                "Complete"         : ["label": "完成任务666"] //完成任务
                        ]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "更新并保存666"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存66666"),
                new ActionButton(action: "Complete", "label": "完成任务666")
        )

        "填写有自定义按钮"                     || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["update": ["label": "填写66666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "保存有自定义按钮"                     || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["Save": ["label": "保存6666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "保存并完成任务有自定义按钮"                ||TaskParams.create()|| new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["UpdateAndComplete": ["label": "保存并完成任务6666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "完成任务有自定义按钮"                   || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["Complete": ["label": "完成任务6666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成任务6666")
        )


        "填写和保存有自定义按钮"                  || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["update": ["label": "填写6666"],
                                         "Save"  : ["label": "保存6666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存6666"),
                new ActionButton(action: "Complete", "label": "完成")
        )


        "填写和保存并完成任务有自定义按钮"             || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["update"           : ["label": "填写6666"],
                                         "UpdateAndComplete": ["label": "保存并完成任务6666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成任务6666"),
                new ActionButton(action: "Update", "label": "填写6666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "更新有自定义按钮,添加保存"                || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["update": ["label": "填写66666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "更新有自定义按钮"                     || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["update": ["label": "填写66666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "未解析到处理人"                     || TaskParams.create()||new BPMTask(state: TaskState.error, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "update",
                        form          : [[[api_name : "field_W0cmE__c",
                                           name     : "field_W0cmE__c",
                                           label    : "日期时间",
                                           status   : "new",
                                           time_zone: "GMT+8",
                                           type     : "date_time",
                                           readonly : false,
                                           required : false]], [

                                         ]]
                        ,
                        defaultButtons: ["update": ["label": "填写66666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )
        "更新无自定义按钮"                     ||TaskParams.create()|| new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType: "update",
                        form         : [[[api_name : "field_W0cmE__c",
                                          name     : "field_W0cmE__c",
                                          label    : "日期时间",
                                          status   : "new",
                                          time_zone: "GMT+8",
                                          type     : "date_time",
                                          readonly : false,
                                          required : false]], [

                                        ]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写"),
                new ActionButton(action: "Save", "label": "保存"),
                new ActionButton(action: "Complete", "label": "完成")
        )

        "form无字段,之下发完成任务按钮"            || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType: "update",
                        form         : [[], [

                        ]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "Complete", "label": "完成")
        )

        "更换负责人无自定义按钮"                  || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType: "operation",
                        actionCode   : "changeowner",
                        actionLabel  : "变更负责人"
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "changeowner", "label": "变更负责人")
        )

        "更换负责人有自定义按钮"                  || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "operation",
                        actionCode    : "changeowner",
                        actionLabel   : "变更负责人",
                        defaultButtons: ["changeowner": ["label": "变更负责人666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "changeowner", "label": "变更负责人666")
        )

        "添加团队成员无自定义按钮"                 || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType: "operation",
                        actionCode   : "addteammember",
                        actionLabel  : "添加团队成员"
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "addteammember", "label": "添加团队成员")
        )

        "添加团队成员有自定义按钮"                 || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType : "operation",
                        actionCode    : "addteammember",
                        actionLabel   : "添加团队成员",
                        defaultButtons: ["addteammember": ["label": "添加团队成员666"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "addteammember", "label": "添加团队成员666")
        )

        "选择或新建关联对象无自定义按钮"              || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType           : "addRelatedObject",
                        relatedEntityId         : "object_pFsNR__c",
                        relatedEntityName       : "自行车_dddd",
                        relatedObjectId         : [expression: "activity_1569238279516##SelfRef##object_pFsNR__c"],
                        expression              : "activity_1569238279516##SelfRef##object_pFsNR__c",
                        target_related_list_name: "target_related_list_0tooR__c",
                        relatedFieldApiName     : "field_n0odH__c"
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "选择或新建 自行车_dddd")
        )
        "选择或新建关联对象无自定义按钮,只新建"          ||TaskParams.create()|| new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType           : "addRelatedObject",
                        relatedEntityId         : "object_pFsNR__c",
                        relatedEntityName       : "自行车_dddd",
                        relatedObjectId         : [expression: "activity_1569238279516##SelfRef##object_pFsNR__c"],
                        expression              : "activity_1569238279516##SelfRef##object_pFsNR__c",
                        target_related_list_name: "target_related_list_0tooR__c",
                        relatedFieldApiName     : "field_n0odH__c",
                        onlyRelatedObject       : true
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "新建 自行车_dddd")
        )
        "选择或新建关联对象有自定义按钮"              || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType           : "addRelatedObject",
                        relatedEntityId         : "object_pFsNR__c",
                        relatedEntityName       : "自行车_dddd",
                        relatedObjectId         : [expression: "activity_1569238279516##SelfRef##object_pFsNR__c"],
                        expression              : "activity_1569238279516##SelfRef##object_pFsNR__c",
                        target_related_list_name: "target_related_list_0tooR__c",
                        relatedFieldApiName     : "field_n0odH__c",
                        defaultButtons          : ["addRelatedObject": ["label": "选择新建我我我"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "选择新建我我我")
        )
        "选择或新建关联对象有自定义按钮,只新建"          ||TaskParams.create()|| new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "object_pFsNR__c", objectId: "5d8328037cfed989fcf9ba89",
                extension: [
                        executionType           : "addRelatedObject",
                        relatedEntityId         : "object_pFsNR__c",
                        relatedEntityName       : "自行车_dddd",
                        relatedObjectId         : [expression: "activity_1569238279516##SelfRef##object_pFsNR__c"],
                        expression              : "activity_1569238279516##SelfRef##object_pFsNR__c",
                        target_related_list_name: "target_related_list_0tooR__c",
                        relatedFieldApiName     : "field_n0odH__c",
                        onlyRelatedObject       : true,
                        defaultButtons          : ["addRelatedObject": ["label": "选择新建我我我"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddRelatedObject", "label": "选择新建我我我")
        )
        "选择或新建从对象无自定义按钮"               || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "PaymentObj", objectId: "5d4be8627cfed9ddcb30f2c7",
                extension: [
                        executionType           : "addMDObject",
                        relatedEntityId         : "object_o578d__c",
                        relatedEntityName       : "从对象4",
                        relatedObjectId         : [expression: "activity_1569291437570##object_o578d__c"],
                        target_related_list_name: "target_related_list_zQC49__c",
                        relatedFieldApiName     : "field_7uhdk__c"
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddMDObject", "label": "选择或新建 从对象4")
        )

        "选择或新建从对象有自定义按钮"               || TaskParams.create()||new BPMTask(state: TaskState.in_progress, taskOwner: Boolean.TRUE, entityId: "PaymentObj", objectId: "5d4be8627cfed9ddcb30f2c7",
                extension: [
                        executionType           : "addMDObject",
                        relatedEntityId         : "object_o578d__c",
                        relatedEntityName       : "从对象4",
                        relatedObjectId         : [expression: "activity_1569291437570##object_o578d__c"],
                        target_related_list_name: "target_related_list_zQC49__c",
                        relatedFieldApiName     : "field_7uhdk__c",
                        defaultButtons          : ["addMDObject": ["label": "选择从对象5555555555555"]]
                ])                                || "71554"  || Lists.newArrayList(
                new ActionButton(action: "AddMDObject", "label": "选择从对象5555555555555")
        )

    }


    def "测试list"() {

        given:
        def list = Lists.newArrayList(
                new ActionButton(action: "UpdateAndComplete", "label": "保存并完成"),
                new ActionButton(action: "Update", "label": "填写66666"),
                new ActionButton(action: "Complete", "label": "完成"))
        print(listToString(list))
    }
}


