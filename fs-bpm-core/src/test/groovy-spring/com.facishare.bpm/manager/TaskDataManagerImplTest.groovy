package com.facishare.bpm.manager

import com.facishare.bpm.GBaseTest
import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.data.TaskDataOfBatchAddRelatedObjectHandler
import com.facishare.bpm.manage.TaskDataManager
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.service.BPMBaseService
import com.facishare.rest.core.model.RemoteContext
import org.springframework.beans.factory.annotation.Autowired

/**
 * desc:
 * version: 7.2.0
 * Created by wansong on 2020/6/21 15:59 PM
 */
class TaskDataManagerImplTest extends GBaseTest {


    @Autowired
    TaskDataManager taskDataManager;

    @Autowired
    private BPMBaseService baseService;

    def "任务数据测试"() {
        given:
        RemoteContext context = new RemoteContext();
        context.setAppId("BPM")
        context.setUserId("1000")
        context.setTenantId("71554")
        RefServiceManager serviceManager = baseService.getServiceManager(context)
        Map<String,String> data=["entityId":"aaaa","objectId":"b"] as Map;
        when:
        taskDataManager.getHandler(ExecutionTypeEnum.batchAddRelatedObject).saveData(serviceManager, "test12","a",1,data)
        def rst=(Map<String,TaskDataOfBatchAddRelatedObjectHandler.TaskDataOfBatchAddRelatedObjectVO>)taskDataManager.getHandler(ExecutionTypeEnum.batchAddRelatedObject).getData(serviceManager,"test12")
        then:
        rst.size()>0
    }
}


