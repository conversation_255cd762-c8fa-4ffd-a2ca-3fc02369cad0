package com.facishare.bpm.manager

import com.facishare.bpm.GBaseTest
import com.facishare.bpm.manage.BPMLoopBreakerManager
import com.facishare.rest.core.model.RemoteContext
import org.springframework.beans.factory.annotation.Autowired

class BPMLoopBreakerManagerTest extends GBaseTest {


    @Autowired
    BPMLoopBreakerManager bpmLoopBreakerManager;


    def "hello"() {
        given:
        print(1)
    }


    def "死循环测试"() {
        given:

        RemoteContext context = new RemoteContext()
        context.setAppId("BPM")
        context.setTenantId("71557")
        context.setUserId("1007")

        String entityId = "AccountObj";
        String objectId = "5f9b7809c7613c000123f1ab";
        String outlineId = "5f9adca5e87b690001805949";
        for (int i = 0; i < 10; i++) {
            println ">>>>>>> "+i
            bpmLoopBreakerManager.incAndCheck(context, outlineId, entityId, objectId)
        }
    }

}
