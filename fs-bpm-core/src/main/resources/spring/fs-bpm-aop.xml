<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!-- fs-bpm-console and fs-bpm-processor used -->

    <bean id="serviceProfiler" class="com.facishare.bpm.aop.ServiceTraceProfiler"/>

    <aop:config proxy-target-class="true">
        <aop:aspect ref="serviceProfiler">
            <aop:around method="profile" pointcut="execution(* com.facishare.bpm.*.*(..))"/>
        </aop:aspect>
    </aop:config>

</beans>
