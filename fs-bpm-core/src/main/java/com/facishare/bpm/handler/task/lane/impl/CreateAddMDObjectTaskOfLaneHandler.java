package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.AddMDObjectTaskButtonHandler;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * Created by wangzhx on 2019/4/29.
 */
@Service
public class CreateAddMDObjectTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private AddMDObjectTaskButtonHandler addRelatedObjectTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        return execute(serviceManager, task, addRelatedObjectTaskButtonHandler,taskParams);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.addMDObject;
    }
}
