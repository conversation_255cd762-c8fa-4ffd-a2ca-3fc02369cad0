package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.BatchEditMasterDetailButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.detail.model.StandardDataOfBatchEditMasterDetailObject;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.proxy.PaasLogProxy;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 7.3.0
 * Created by cuiyongxu on 2020/8/19 7:22 下午
 */
@Slf4j
@Service
public class BatchEditMasterDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private BatchEditMasterDetailButtonHandler batchEditMasterDetailButtonHandler;

    @Autowired
    private PaasLogProxy paasLogProxy;


    @Override
    public StandardDataOfBatchEditMasterDetailObject getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardDataOfBatchEditMasterDetailObject standardData =
                BeanUtils.transfer(StandardData.getStandardData(task), StandardDataOfBatchEditMasterDetailObject.class);
        Map<String, Object> bpmExtension = task.getBpmExtension();
        standardData.setRelatedEntityId((String) bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId));
        standardData.setTarget_related_list_name(bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.relatedListName));
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        return standardData;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);


        StandardDataOfBatchEditMasterDetailObject standardData = getStandardData(serviceManager, task, taskParams);
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);

        standardData.setShowDataChangeLog(paasLogProxy.hasEditLog(serviceManager.getContext(),task.getId(),task.getEntityId(),task.getObjectId(), task.getCreateTime()));
        //H5需要下发data
        if(Boolean.TRUE.equals(taskParams.getIsH5())){
            standardData.setData(serviceManager.findDataById(task.getEntityId(), task.getObjectId()));
        }
        taskDetail.setData(standardData);
        FormButtonResult formButtonResult = batchEditMasterDetailButtonHandler.setButtons(serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));

        taskDetail.setData(standardData);
        return taskDetail;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.batchEditMasterDetailObject;
    }
}
