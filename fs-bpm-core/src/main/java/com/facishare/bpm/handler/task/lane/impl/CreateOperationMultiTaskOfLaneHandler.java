package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.OperationMultiTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey;

@Service
public class CreateOperationMultiTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private OperationMultiTaskButtonHandler operationMultiTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)){
            return laneTask;
        }
        //将自定义按钮的ApiNames放到standardData上
        standardData.setCommonButtonApiNames((List<String>) task.getBpmExtension().get(ActivityKey.ExtensionKey.COMMON_BUTTON_API_NAMES));
        setButtonsAndErrorMsg(serviceManager, operationMultiTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        laneTask.setData(standardData);
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operationMulti;
    }
}
