package com.facishare.bpm.handler.task.complete;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:23 PM
 */
public interface TaskCompleteHandler {


    CompleteTask.Result execute(
            RefServiceManager serviceManager,
            Task task,
            String opinion,
            Map<String, Object> completedData,
            Integer addOrReplaceNextTaskAssignee,
            Map<String, Object> nextTaskAssignee,
            DataCacheHandler dataCacheHandler,
            boolean needValidateNextTaskAssignee, Boolean ignoreNoBlockValidate) throws Exception;

    ExecutionTypeEnum getTaskType();

}
