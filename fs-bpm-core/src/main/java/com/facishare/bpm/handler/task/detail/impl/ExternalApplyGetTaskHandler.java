package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ExternalApplyTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.TaskDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by wangzhx on 2019/8/21.
 */
@Service
public class ExternalApplyGetTaskHandler implements UserGetTaskDetailHandler {
    @Autowired
    private ExternalApplyTaskButtonHandler externalApplyTaskButtonHandler;

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        standardData.setAppCode(task.getExternalApplyAppCode());
        standardData.setActionCode(task.getExternalApplyActionCode());
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        return standardData;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);

        StandardData standardData = getStandardData(serviceManager, task, taskParams);

        //  web端需要查询数据详情,点击应用按钮的时候需要,如果没有权限  也需要跳转过去, 历史接口 GetTask 不需要
        standardData.setData(serviceManager.findDataById(task.getEntityId(), task.getObjectId()));
        taskDetail.setData(standardData);
        //770 待办详情页优化 需要下发数据权限,目前web端暂不下发
        if (taskParams.isMobile() || taskParams.isH5()) {
            taskDetail.setHasDataPrivilege(serviceManager.dataPrivilege(task.getEntityId(), task.getObjectId()));
        }
        FormButtonResult formButtonResult = externalApplyTaskButtonHandler.setButtons(serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));

        return taskDetail;
    }
    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.externalApplyTask;
    }
}
