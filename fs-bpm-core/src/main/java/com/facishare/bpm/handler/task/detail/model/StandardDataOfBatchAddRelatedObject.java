package com.facishare.bpm.handler.task.detail.model;

import com.facishare.bpm.handler.task.data.TaskDataOfBatchAddRelatedObjectHandler;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @creat_date: 2020/6/21
 * @creat_time: 15:37
 * @since 7.2.0
 */
@Data
public class StandardDataOfBatchAddRelatedObject extends StandardData {
  private Map<String, List<TaskDataOfBatchAddRelatedObjectHandler.TaskDataOfBatchAddRelatedObjectVO>> relatedObjectList;
}
