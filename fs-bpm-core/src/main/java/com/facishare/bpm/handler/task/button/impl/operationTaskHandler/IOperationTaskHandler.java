package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.google.common.collect.Maps;

import java.util.Map;

public interface IOperationTaskHandler {

    String defaultHandlerType = "default";


    /**
     * 获取任务按钮及异常信息
     *
     * @param serviceManager
     * @param standardData
     * @param taskParams
     * @return
     */
    FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams);

    /**
     * 任务详情页  获取操作任务详情
     *
     * @param serviceManager
     * @param task
     * @param taskParams
     * @return
     */
    TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams);

    /**
     * 数据详情页  获取部分任务信息 当处理任务时  前端会重新拉取 getTaskInfo接口(getTaskDetail)
     *
     * @param serviceManager
     * @param task
     * @param taskParams
     * @return
     */
    LaneTask getLaneTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams);

    /**
     * 操作类型
     *
     * @return
     */
    String getType();

    /**
     * 校验功能权限
     *
     * @param actions
     * @param func
     * @return
     */
    default Map<String, String> filterActionByFunction(Map<String, String> actions,
                                                       Map<String, Boolean> func) {
        Map<String, String> ret = Maps.newLinkedHashMap();
        actions.keySet().forEach(actionCode -> {
            //跳过签到/签退的功能权限
            if (func.get(actionCode)) {
                ret.put(actionCode, actions.get(actionCode));
            }
        });
        return ret;
    }

}
