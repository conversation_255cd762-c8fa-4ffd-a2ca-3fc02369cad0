package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.i18n.I18NUtils;
import com.google.common.base.Strings;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/26 2:15 PM
 */
@Service
public class AddRelatedObjectTaskButtonHandler implements TaskButtonHandler {


    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {

        String relatedEntityId = standardData.getRelatedEntityId();

        Map<String, Object> relatedDescribe = serviceManager.findDescribe(relatedEntityId, false, false);

        boolean isActive = MapUtil.instance.getBool(relatedDescribe, BPMConstants.MetadataKey.isActive);
        if (!isActive) {
            return new FormButtonResult(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST.text());
        }
        //如果被关联的字段删除了或者禁用了,则不下发选择或新建按钮

        String refApiName = standardData.getRelatedFieldApiName();
        if (Strings.isNullOrEmpty(refApiName)) {
            return new FormButtonResult(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST.text());
        }
        List<ActionButton> buttons = TaskButtonConfigHelper.getTaskFormButtons(getTaskType(), standardData.getOnlyRelatedObject());
        buttons.forEach(item -> {
            if (item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.ADDRELATEDOBJECT_CODE)
                    || item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.ADDMDOBJECT)) {
                item.setLabel(
                        // TODO 选择新建国际化验证下 选择并新建{0}
                        I18NUtils.text(item.getLabel(),"选择并新建{0}", standardData.getRelatedEntityName())//ignoreI18n
                );
            }
        });
        return new FormButtonResult(TaskButtonHandler.getCustomButtons(getTaskType(), standardData.getDefaultButtons(), buttons));
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.addRelatedObject;
    }

}
