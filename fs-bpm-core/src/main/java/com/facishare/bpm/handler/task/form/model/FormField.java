package com.facishare.bpm.handler.task.form.model;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class FormField {
    String api_name;
    String header;
    boolean is_show = true;
    String tab_index = "ltr";
    String title;
    List<Map<String, Object>> form_fields;
    Boolean signin;
    Boolean signout;


    public FormField(String api_name, String header, List<Map<String, Object>> form_fields, String title, Boolean signin, Boolean signout) {
        this.api_name = api_name;
        this.form_fields = form_fields;
        this.header = header;
        this.title = title;
        this.signin = Objects.isNull(signin) ? false : signin;
        this.signout = Objects.isNull(signout) ? false : signout;
    }

    public FormField(String api_name, String header, List<Map<String, Object>> form_fields, String title) {
        this.api_name = api_name;
        this.form_fields = form_fields;
        this.header = header;
        this.title = title;
    }
}