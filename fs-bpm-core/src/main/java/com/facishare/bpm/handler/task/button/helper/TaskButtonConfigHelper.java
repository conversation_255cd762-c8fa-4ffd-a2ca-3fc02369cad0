package com.facishare.bpm.handler.task.button.helper;

import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/26 11:56 AM
 */
@Slf4j
public class TaskButtonConfigHelper {

    private final static String configName = "fs-bpm-task-form-button-config";
    private static volatile Map<String, List<ActionButton>> buttons;


    static {
        ConfigFactory.getConfig(configName, (config) -> {
            try {
                String content = new String(config.getContent());
                buttons = JsonUtil.fromJson(content, new TypeToken<Map<String, List<ActionButton>>>() {
                }.getType());
                log.info(JsonUtil.toPrettyJson(buttons));
            } catch (Exception e) {
                log.error("load form buttons config error!", e);
                throw new RuntimeException(e);
            }
        });
    }

    public static List<ActionButton> getTaskFormButtons(ExecutionTypeEnum executionType, Boolean onlyRelatedObject) {
        List<ActionButton> rst = buttons.get(executionType.name());
        if (CollectionUtils.isNotEmpty(rst)) {
            return rst.stream().map(item -> {
                        if (executionType.isMDObjectOrRelated() && Boolean.TRUE.equals(onlyRelatedObject)) {
                            return new ActionButton(item.getAction(), BPMI18N.PAAS_FLOW_BPM_CONFIG_BUTTON_ONLY_CREATE.key);
                        }
                        return new ActionButton(item.getAction(), executionType.name(), item.getLabel());
                    }
            ).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

}
