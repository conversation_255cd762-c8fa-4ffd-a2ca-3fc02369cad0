package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ApproveTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.TaskDetail;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/19 2:38 PM
 */
@Slf4j
@Service
public class ApproveGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private ApproveTaskButtonHandler approveTaskButtonHandler;

    @Autowired
    private GetTaskFormManager getTaskFormManager;

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        standardData.setFormEditable(task.approveFormSupportEdit());
        standardData.setUpdateFromProperty(task.existForm());
        //数据权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        standardData.setEnableLayoutRules(MapUtils.getBooleanValue(task.getBpmExtension(), BPMConstants.ENABLELAYOUTRULES));
        return standardData;
    }

    @SneakyThrows
    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task,TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        UpdateGetTaskDetailHandler.buildTaskUpdateFormWithoutBtn(serviceManager, getTaskFormManager, task, taskParams, standardData);
        taskDetail.setData(standardData);
        FormButtonResult formButtonResult = approveTaskButtonHandler.setButtons(serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams);

        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        return taskDetail;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.approve;
    }
}
