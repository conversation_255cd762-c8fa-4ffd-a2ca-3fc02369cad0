package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.UpdateLookupTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


@Slf4j
@Service
public class UpdateLookupGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private UpdateLookupTaskButtonHandler updateLookupTaskButtonHandler;

    @Autowired
    private UpdateGetTaskDetailHandler updateGetTaskDetailHandler;

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        //获取关联对象的信息
        String entityId = "";
        String objectId = "";
        Boolean relatedFieldObjectHasValue;
        String fieldApiName  = (String)task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.fieldApiName);
        if (StringUtils.isBlank(fieldApiName)) {
            relatedFieldObjectHasValue = Boolean.FALSE;
        }else {
            objectId = (String)serviceManager.findDataById(task.getEntityId(), task.getObjectId(), fieldApiName, true);
            Map<String, Object> desc = (Map<String, Object>)serviceManager.getFields(task.getEntityId()).get(fieldApiName);
            if (StringUtils.isBlank(objectId) || MapUtils.isEmpty(desc) || !BPMConstants.MetadataKey.objectReference.equals(desc.get(BPMConstants.MetadataKey.type)) || Boolean.FALSE.equals(desc.get(BPMConstants.MetadataKey.isActive))) {
                relatedFieldObjectHasValue = Boolean.FALSE;
            }else {
                relatedFieldObjectHasValue = Boolean.TRUE;
            }
            if (MapUtils.isNotEmpty(desc) && Objects.nonNull(desc.get(BPMConstants.MetadataKey.targetApiName))){
                entityId = (String) desc.get(BPMConstants.MetadataKey.targetApiName);
            }
        }
        standardData.setRelatedFieldObjectIsIdentical(entityId.equals(task.getLookupFieldEntityId()));
        standardData.setRelatedFieldObjectHasValue(relatedFieldObjectHasValue);
        if(StringUtils.isNotBlank(entityId) && StringUtils.isNotBlank(objectId)){
            standardData.setEntityId(entityId);
            standardData.setObjectId(objectId);
            UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        }
        //功能权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        standardData.setUpdateFromProperty(task.existForm());
        /**
         * 兼容enableLayoutRules 为null
         */
        standardData.setEnableLayoutRules(MapUtils.getBooleanValue(task.getBpmExtension(), BPMConstants.ENABLELAYOUTRULES));
        return standardData;
    }

    @SneakyThrows
    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task,TaskParams taskParams) {
        StandardData standardData = getStandardData(serviceManager,task,taskParams);
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        taskDetail.setData(standardData);
        if(standardData.getRelatedFieldObjectIsIdentical() && standardData.getRelatedFieldObjectHasValue()){
            updateGetTaskDetailHandler.buildTaskUpdateForm(serviceManager, standardData, task, taskParams);
        }
        FormButtonResult formButtonResult = updateLookupTaskButtonHandler.setButtons(
                serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        return taskDetail;
    }


    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.updateLookup;
    }
}
