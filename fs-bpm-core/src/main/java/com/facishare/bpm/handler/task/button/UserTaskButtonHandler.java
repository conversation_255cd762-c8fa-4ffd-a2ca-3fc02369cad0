package com.facishare.bpm.handler.task.button;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;

import java.util.List;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 9:54 AM
 */
public interface UserTaskButtonHandler {


    FormButtonResult setButtons(RefServiceManager serviceManager,
                                StandardData standardData,
                                boolean assignNextTask,
                                TaskState state,
                                List<String> candidateIds,
                                List<String> assigneeIds,
                                AfterActionExecution.SimpleAfterActionExecution execution,
                                TaskParams taskParams);

    FormButtonResult setButtons(RefServiceManager serviceManager,
                                StandardData standardData,
                                boolean assignNextTask,
                                TaskParams taskParams);

}
