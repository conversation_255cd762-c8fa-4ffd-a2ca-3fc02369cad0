package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.operationTaskHandler.OperationTaskBuildManager;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * Created by wangzhx on 2019/4/29.
 */
@Slf4j
@Service
public class CreateOperationTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private OperationTaskBuildManager operationTaskManager;


    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        return operationTaskManager.getLaneTaskDetail(serviceManager, task, taskParams);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operation;
    }
}
