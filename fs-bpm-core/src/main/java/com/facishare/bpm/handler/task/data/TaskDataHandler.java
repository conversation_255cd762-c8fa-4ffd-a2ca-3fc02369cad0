package com.facishare.bpm.handler.task.data;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @creat_date: 2020/6/23
 * @creat_time: 17:32
 * @since 7.2.0
 */
public abstract class TaskDataHandler<E,R> {
  @Autowired
  protected BizTaskDataDao taskDataDao;

  public abstract void saveData(RefServiceManager serviceManager,String taskId, String activityId, Integer activityInstanceId, E data);

  public abstract R getData(RefServiceManager serviceManager,String taskId);

  public abstract ExecutionTypeEnum getTaskType();
}
