package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.CustomTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.TaskDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * desc: 业务元素任务详情handler
 * Created by wansong on 2023/10/11
 */
@Slf4j
@Service
public class CustomTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private CustomTaskButtonHandler customTaskButtonHandler;

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        return standardData;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task,TaskParams taskParams) {

        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        taskDetail.setData(getStandardData(serviceManager,task,taskParams));
        //如果是业务元素节点
        taskDetail.elementConfigData(task.getCustomExtension(),serviceManager.getFlowElementWrapper(task.getElementApiName()).getPlugin());
        StandardData standardData = getStandardData(serviceManager,task,taskParams);
        FormButtonResult formButtonResult = customTaskButtonHandler.setButtons(
                serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        return taskDetail;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.custom;
    }
}
