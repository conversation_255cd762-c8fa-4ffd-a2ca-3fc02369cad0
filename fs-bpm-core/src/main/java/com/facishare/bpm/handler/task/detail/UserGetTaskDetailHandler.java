package com.facishare.bpm.handler.task.detail;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMGetTaskExecutionException;
import com.facishare.bpm.exception.BPMTaskReferObjectNotFoundException;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.util.BPMExtensionUtils;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.Pair;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/19 11:07 AM
 */
public interface UserGetTaskDetailHandler extends GetTaskDetailHandler {

    Logger log = LoggerFactory.getLogger(UserGetTaskDetailHandler.class);

    @Override
    default TaskDetail execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        if(StringUtils.isBlank(task.getObjectId())){
            throw new BPMGetTaskExecutionException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_RELATED_DATA_NOT_FOUND, BPMI18N.PAAS_FLOW_BPM_TASK_RELATED_DATA_NOT_FOUND.text());
        }
        TaskDetail taskDetail = getTaskDetail(serviceManager, task, taskParams);
        taskDetail.setOpinions(task.getOpinions());
        taskDetail.setCandidateModifyLog(task.getApproverModifyLog());
        //设置当前人是否为节点的处理人
        taskDetail.setIsOwner(TaskButtonHandler.isTaskOwner(serviceManager, taskDetail.getCandidateIds()));
        return taskDetail;
    }


    static String getObjectIdFromVariable(Map<String, Object> variables, Object objectExpression) {
        if (objectExpression == null) {
            return null;
        }

        String objectId = null;
        BPMExtensionUtils.Expression expression = BPMExtensionUtils.transferExpression(objectExpression);
        if (expression != null) {
            objectId = (String) variables.get(expression.getInstanceVariableKey());
        }
        return objectId;
    }

    default Map<String, ActionButton> transButtons(List<ActionButton> buttons) {
        return buttons.stream().collect(Maps::newLinkedHashMap, (m, button) -> m.put(button.getAction(), button), Map::putAll);
    }

    static void relatedOrMDObjectSetExt(RefServiceManager serviceManager,
                                        PaasWorkflowServiceProxy paasWorkflowServiceProxy,
                                        StandardData standardData,
                                        boolean completed,
                                        Map<String, Object> bpmExtension, TaskParams taskParams, String entityId, String objectId, ExecutionTypeEnum executionType, String workflowInstanceId) {
        String taskId = standardData.getTaskId();
        String relatedEntityId = standardData.getRelatedEntityId();

        Map<Pair<String, String>, Map<String, Object>> taskDatas = Maps.newHashMap();

        log.info("getTaskData:taskId:{},entityId:{},objectId:{}", taskId, entityId, objectId);
        if (!Strings.isNullOrEmpty(objectId)) {
            taskDatas.put(new Pair<>(entityId, objectId), serviceManager.findDataHandlePermissionsById(entityId, objectId));
        } else {
            log.error("getTaskData error : objectId null, wrong definition. CONTEXT={}" + "OBJECT_ID={},TASK_ID={}", serviceManager.getContext(), objectId, taskId);
            throw new BPMTaskReferObjectNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_CHOICE_OR_CREATE_OBJECT_NOT_FOUND);
        }

        Pair<String, String> relatedDetail = new Pair<>((String) bpmExtension.get
                (WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId), (String) bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.relatedListName));

        Map<String, Object> relatedDesc = serviceManager.findDescribe(relatedDetail.getKey(), true, false);
        standardData.setRelatedFieldApiName(getReferenceAndMasterDetailField(relatedDesc, relatedDetail.getValue()));
        standardData.setObject_describe_api_name(entityId);
        standardData.setTarget_related_list_name(bpmExtension.get(BPMConstants.MetadataKey.relatedListName));
        standardData.setRelatedObjectId(bpmExtension.get(BPMConstants.MetadataKey.relatedObjectId));

        if (Boolean.TRUE.equals(completed) && ExecutionTypeEnum.batchAddRelatedObject != executionType) {
            Map<String, Object> variables = paasWorkflowServiceProxy.getWorkflowInstance(serviceManager.getContext(),workflowInstanceId).getVariables();
            String relatedObjectId = getObjectIdFromVariable(variables, standardData.getRelatedObjectId());
            if (!Strings.isNullOrEmpty(relatedObjectId)) {
                taskDatas.put(new Pair<>(relatedEntityId, relatedObjectId), serviceManager.findDataByIdWithEmptyMap(relatedEntityId, relatedObjectId));
            } else {
                log.error("getTaskData:data not found:tenantId:{},taskId:{},relatedEntityId:{},relatedObjectId:{}", serviceManager.getTenantId(), taskId, relatedEntityId, relatedObjectId);
            }
        }

        if (standardData.getOnlyRelatedObject()) {
            standardData.setRelatedDescribe(relatedDesc);
        }
        setTaskObjectProperty(serviceManager, standardData, taskDatas, taskParams);
    }

    static String getReferenceAndMasterDetailField(Map<String, Object> desc, String targetRelatedListName) {
        if (Strings.isNullOrEmpty(targetRelatedListName) || Objects.isNull(desc)) {
            return null;
        }
        Map<String, Map<String, Object>> fields = MapUtil.instance.getMapOfGeneric(desc, BPMConstants.MetadataKey.fields);
        for (Map.Entry<String, Map<String, Object>> entry : fields.entrySet()) {
            String fieldApiName = entry.getKey();
            Map<String, Object> fieldDesc = entry.getValue();
            if (targetRelatedListName.equals(fieldDesc.get(WorkflowKey.ActivityKey.ExtensionKey.relatedListName))
                    && MapUtil.instance.getBool(fieldDesc, BPMConstants.MetadataKey.isActive)) {
                return fieldApiName;
            }
        }

        return null;
    }


    static void setTaskObjectProperty(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        String entityId = standardData.getEntityId();
        String objectId = standardData.getObjectId();

        String relatedEntityId = standardData.getRelatedEntityId();
        if (!Strings.isNullOrEmpty(relatedEntityId)) {
            Map<String, String> simpleEntityNames = serviceManager.getSimpleEntityNames();
            if (MapUtils.isNotEmpty(simpleEntityNames)) {
                standardData.setEntityName(simpleEntityNames.getOrDefault(entityId, BPMConstants.REPLACE_WHEN_NOT_FOUND));
                standardData.setRelatedEntityId(relatedEntityId);
                standardData.setRelatedEntityName(simpleEntityNames.getOrDefault(relatedEntityId, BPMConstants.REPLACE_WHEN_NOT_FOUND));
            }
        }

        if (Objects.nonNull(objectId) && taskParams.isFromTaskDetail() && !Boolean.TRUE.equals(taskParams.getIsTaskNotGetData())) {
            Map dataName = serviceManager.getPaaSObjectNames(entityId, Lists.newArrayList(objectId));
            if (MapUtils.isNotEmpty(dataName) && dataName.containsKey(objectId)) {
                standardData.setObjectName(dataName.getOrDefault(objectId, BPMConstants.REPLACE_WHEN_NO_PERMISSION));
            }
        }
    }

    static void setTaskObjectProperty(RefServiceManager serviceManager, StandardData standardData, Map<Pair<String, String>, Map<String, Object>> taskDatas, TaskParams taskParams) {

        setTaskObjectProperty(serviceManager, standardData, taskParams);

        //只有新建关联对象  批量新建关联对象   新建从对象 taskDatas才会有值
        taskDatas.forEach((entityIdObjectId, data) -> {
            if (entityIdObjectId.getValue().equals(standardData.getObjectId())) {
                //id在前面已经填充，不再填充。当没有数据权限时，从data中拿不到id
                standardData.setObjectName(data.getOrDefault(WorkflowKey.ActivityKey.ExtensionKey.name, BPMConstants.REPLACE_WHEN_NO_PERMISSION));
            } else if (entityIdObjectId.getKey().equals(standardData.getRelatedEntityId())) {
                standardData.setRelatedObjectId(data.get(BPMConstants.MetadataKey.id));
                standardData.setRelatedObjectName(data.getOrDefault(WorkflowKey.ActivityKey.ExtensionKey.name, BPMConstants.REPLACE_WHEN_NO_PERMISSION));
            }
        });
    }

    TaskDetail getTaskDetail(RefServiceManager serviceManager,
                             Task task,
                             TaskParams taskParams);
}
