package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.operationTaskHandler.OperationTaskBuildManager;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import com.facishare.paas.I18N;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_IN;
import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_OUT;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/19 3:08 PM
 */
@Slf4j
@Service
public class OperationGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private BizTaskDataDao bizTaskDataDao;
    @Autowired
    private OperationTaskBuildManager operationTaskManager;


    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        Map<String, Object> bpmExtension = task.getBpmExtension();
        boolean isCombinedActionCode = BPMConstants.MetadataKey.APINAME_LEADSOBJ.equals(bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.entityId))
                && BPMConstants.MetadataKey.leadsObjHandleActionCode.equals(bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode));
        standardData.setCombinedActionCode(isCombinedActionCode);
        standardData.setDescribe(serviceManager.getDescribe(task.getEntityId()));
        ActionButton operationActionButton = getOperationActionButton(serviceManager, bizTaskDataDao,standardData.isCombinedActionCode(), task);
        standardData.setActionCode(operationActionButton.getAction());
        standardData.setActionLabel(operationActionButton.getLabel());
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);

        //功能权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));

        return standardData;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        return operationTaskManager.getTaskDetail(serviceManager, task, taskParams);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operation;
    }

    public static ActionButton getOperationActionButton(RefServiceManager serviceManager, BizTaskDataDao bizTaskDataDao, boolean combinedActionCode, Task task) {
        String actionCode = "";
        String actionLabel = "";
        if (combinedActionCode && Boolean.TRUE.equals(task.getCompleted())) {
            Map<String, Object> snapshot = (Map) bizTaskDataDao.find(serviceManager.getTenantId(), task.getId()).getData();
            if (snapshot != null) {
                actionCode = (String) snapshot.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode);
                actionLabel = (String) snapshot.get(WorkflowKey.ActivityKey.ExtensionKey.actionLabel);

            } else {
                log.error("taskId:{}", task.getId() + " isCombinedActionCode,但是完成任务时由于h5问题" + "，遗失了actionCode和actionLabel,以此作为记录");
            }
        } else {
            actionCode = task.getActionCode();
            actionLabel = serviceManager.getActionNameByActionCode(task.getEntityId(), actionCode);
            if (StringUtils.isBlank(actionLabel)) {
                actionLabel = getSpecialActionLabelByActionCode(actionCode);
            }
        }
        return new ActionButton(actionCode, actionLabel);
    }

    public static Map<String, Object> getData(Map<String, Object> allData, String actionCode) {
        switch (actionCode) {
            case "return":
                return allData.keySet().stream().filter(k -> k.equals(BPMConstants.HIGH_SEAS_ID) && Objects.nonNull(allData.get(k))).collect(Collectors.toMap((key -> key), (allData::get)));
            case "HandleThree":
                return allData;
            default:
                return Maps.newHashMap();
        }

    }

    private static String getSpecialActionLabelByActionCode(String actionCode) {
        switch (actionCode) {
            case "signin":
                return PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_IN.text();
            case "signout":
                return PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_OUT.text();
        }
        return null;
    }

}
