package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.util.MetadataUtils;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:26 PM
 */
@Slf4j
@Service
public class UpdateCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {


    private static List areaType = Lists.newArrayList("province", "city", "district");

    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        RemoteContext context = serviceManager.getContext();

        String taskId = task.getId();
        String objectId = task.getObjectId();
        String activityId = task.getActivityId();
        String entityId = task.getEntityId();
        if (!task.isDefaultLayoutAndFormNotEmpty()) {
            Map<String, Object> metaData = dataCacheHandler.getData(entityId, objectId);

            verifyRequiredField(serviceManager.findDescribe(entityId, false, false),
                    metaData, task.getForm());

        }
        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId));

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKey,
                dataCacheHandler);

        CompleteTask.Result completeTaskResult = paasWorkflowServiceProxy.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);
        reportFlowBizLog(context, task ,null);
        //TODO update 看是否可以删除
        //bizTaskDataDao.snapshotTaskData(taskId, context.getTenantId(), activityId, metaData);
        log.info("update:setFieldsValue:{}", JacksonUtil.toJson(variables));
        return completeTaskResult;
    }


    public static void verifyRequiredField(Map<String, Object> desc, Map<String, Object> data, List<List<Map<String,
            Object>>> forms) {
        if (data == null) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND);
        }

        Map<String, Object> fieldDescs = MapUtil.instance.getMap(desc, BPMConstants.MetadataKey.fields);
        List<String> noValueFieldLabels = Lists.newArrayList();
        if (forms != null) {
            for (List<Map<String, Object>> fields : forms) {
                for (Map<String, Object> field : fields) {
                    String fieldName = (String) field.get(WorkflowKey.ActivityKey.ExtensionKey.name);
                    Map<String, Object> fieldDesc = MapUtil.instance.getMap(fieldDescs, fieldName);

                    if (areaType.contains(field.get(WorkflowKey.ActivityKey.ExtensionKey.type)) || fieldDesc == null) {
                        continue;
                    }

                    // 字段描述为空|| 国家省市区||字段描述中的isActive=false,获取不到默认为true,即  字段被删除或者字段被禁用了,则不去校验这类字段,让其可以保存入库
                    if (MapUtils.isEmpty(fieldDesc) || areaType.contains(field.get(WorkflowKey.ActivityKey.ExtensionKey.type))||!(boolean) fieldDesc.getOrDefault(BPMConstants.MetadataKey.isActive, true)) {
                        continue;
                    }

                    boolean required = oneTrue(field.getOrDefault(WorkflowKey.ActivityKey.ExtensionKey.required, false), fieldDesc.get
                            (BPMConstants.MetadataKey.isRequired));
                    //必填 & 字段描述存在 & 数据不存在，则验证不通过
                    if (required && MetadataUtils.isNullOrEmptyObject(data.get(fieldName)) && !TaskHelper.hasInheritType(fieldDesc)) {
                        noValueFieldLabels.add((String) field.get(BPMConstants.MetadataKey.label));
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(noValueFieldLabels)) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION,
                    BPMI18N.PAAS_FLOW_BPM_REQUIRED_FILED_ERROR_MSG2.text().replaceFirst("-", String.join(",", noValueFieldLabels)));
        }
    }


    private static boolean oneTrue(Object condition, Object... conditions) {
        Boolean ret = Boolean.class.isInstance(condition) ? (Boolean) condition : Boolean.FALSE;
        if (Boolean.TRUE.equals(ret)) {
            return true;
        }

        for (Object c : conditions) {
            ret = Boolean.class.isInstance(c) ? (Boolean) c : Boolean.FALSE;
            if (Boolean.TRUE.equals(ret)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.update;
    }

}
