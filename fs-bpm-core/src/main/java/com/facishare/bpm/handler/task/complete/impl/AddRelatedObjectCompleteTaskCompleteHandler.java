package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:39 PM
 */
@Slf4j
@Service
public class AddRelatedObjectCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {

    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        RemoteContext context = serviceManager.getContext();

        if(completedData == null) {
            log.info("completedData is null! :  CONTEXT={}, TASK_ID={}", context, task.getId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA);
        }

        String relatedObjectId = (String) completedData.get(WorkflowKey.ActivityKey.ExtensionKey.relatedObjectId);
        if (Strings.isNullOrEmpty(relatedObjectId)) {
            log.info("completeTask failed : no relatedObjectId! CONTEXT={}, TASK_ID={}", context, task.getId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA);
        }

        String taskId = task.getId();
        String objectId = task.getObjectId();
        String activityId = task.getActivityId();
        String entityId = task.getEntityId();
        String relatedEntityId = task.getRelatedEntityId();

        Map<String, Pair<String, String>> variableKeys = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId), new Pair<>(relatedEntityId, relatedObjectId));

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKeys,
                dataCacheHandler);
        reportFlowBizLog(context, task ,null);
        return paasWorkflowServiceProxy.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);

    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.addRelatedObject;
    }
}
