package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import org.springframework.stereotype.Component;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.Button.confirmdelivery;

/**
 *  确认发货
 */
@Component
public class OperationTaskConfirmDeliveryHandler extends OperationTaskConfirmReceiveHandler {

    @Override
    public FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        return super.getBtnAndErrorMsg(serviceManager, standardData, taskParams);
    }

    @Override
    public String getType() {
        return BPMConstants.OperationCodeType.confirmdelivery.name();
    }

    @Override
    public String getLabel() {
        return confirmdelivery.getI18NDesc();
    }

}
