package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.BatchAddRelatedObjectTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.data.TaskDataOfBatchAddRelatedObjectHandler;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.detail.model.StandardDataOfBatchAddRelatedObject;
import com.facishare.bpm.manage.TaskDataManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * desc:
 * version: 7.2.0
 * Created by cuiyongxu on 2020/6/4 7:22 下午
 */
@Slf4j
@Service
public class BatchAddRelatedObjectHandler implements UserGetTaskDetailHandler {

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;
    @Autowired
    private TaskDataManager taskDataManager;

    @Autowired
    private BatchAddRelatedObjectTaskButtonHandler batchAddRelatedObjectHandler;


    @Override
    public StandardDataOfBatchAddRelatedObject getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams){
        StandardDataOfBatchAddRelatedObject standardData =
                BeanUtils.transfer(StandardData.getStandardData(task), StandardDataOfBatchAddRelatedObject.class,
                        (src,rst)->rst.setRelatedObjectList((Map<String, List<TaskDataOfBatchAddRelatedObjectHandler.TaskDataOfBatchAddRelatedObjectVO>>)taskDataManager.getHandler(getTaskType()).getData(serviceManager, task.getId())));

        Map<String, Object> bpmExtension = task.getBpmExtension();
        standardData.setRelatedEntityId((String) bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId));
        UserGetTaskDetailHandler.relatedOrMDObjectSetExt(serviceManager, paasWorkflowServiceProxy, standardData, task.getCompleted(), task.getBpmExtension(), taskParams, task.getEntityId(), task.getObjectId(), task.getExecutionType(), task.getWorkflowInstanceId());
        //数据权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        if (taskParams.isFromTaskDetail()) {
            //添加对象数据
            standardData.setData(serviceManager.findDataById(task.getEntityId(), task.getObjectId()));
        }
        /**
         * 如果任务已完成且没有添加任务数据，actionLabel 展示成 完成任务
         */
        //2021年11月01日15:07:04  暂时考虑不到什么场景会出现此类问题
//        if(TaskState.pass.equals(task.getState())&& MapUtils.isEmpty(standardData.getRelatedObjectList())){
//            standardData.setActionCode(BPMConstants.Button.Complete.name());
//            standardData.setActionLabel(I18N.text(PAAS_FLOW_BPM_CONFIG_BUTTON_COMPLETE.key));
//        }
        return standardData;
    }


    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardDataOfBatchAddRelatedObject standardData = getStandardData(serviceManager, task, taskParams);
        taskDetail.setData(standardData);
        FormButtonResult formButtonResult = batchAddRelatedObjectHandler.setButtons(serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        taskDetail.setData(standardData);

        return taskDetail;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.batchAddRelatedObject;
    }
}
