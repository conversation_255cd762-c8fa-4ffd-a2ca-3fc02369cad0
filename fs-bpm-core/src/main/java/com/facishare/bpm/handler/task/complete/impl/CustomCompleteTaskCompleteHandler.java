package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.NumberFormatUtil;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.element.plugin.api.FlowElement;
import com.facishare.flow.element.plugin.api.FlowElementVariable;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * wansong
 */
@Slf4j
@Service
public class CustomCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {

    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager,
                                               Task task,
                                               String opinion,
                                               Map<String, Object> completedData,
                                               Integer addOrReplaceNextTaskAssignee,
                                               Map<String, Object> nextTaskAssignee,
                                               DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate
    ) throws Exception {

        RemoteContext context = serviceManager.getContext();

        String taskId = task.getId();
        String objectId = task.getObjectId();
        String entityId = task.getEntityId();
        String activityId = task.getActivityId();

        Map<String, Pair<String, String>> objectVariable;
        //https://wiki.firstshare.cn/pages/viewpage.action?pageId=412058032
        if (Boolean.TRUE.equals(task.getImportObject())){
            objectVariable = getCurrentAndRelatedObjectVariable(task, completedData, context, objectId, entityId, activityId);
        }else {
            objectVariable = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                    objectId));
        }

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                objectVariable, dataCacheHandler);
        //将业务元素配置的变量，从业务传入的变量 进行设置
        setCustomVariables(serviceManager, task, completedData, variables);

        reportFlowBizLog(context, task ,task.getElementApiName());
        return paasWorkflowServiceProxy.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);
    }

    private Map<String, Pair<String, String>> getCurrentAndRelatedObjectVariable(Task task, Map<String, Object> completedData, RemoteContext context, String objectId, String entityId, String activityId) {
        Map<String, Pair<String, String>> objectVariable;
        if(completedData == null) {
            log.info("completedData is null! :  CONTEXT={}, TASK_ID={}", context, task.getId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA);
        }

        String relatedObjectId = (String) completedData.get(WorkflowKey.ActivityKey.ExtensionKey.relatedObjectId);
        if (Strings.isNullOrEmpty(relatedObjectId)) {
            log.info("completeTask failed : no relatedObjectId! CONTEXT={}, TASK_ID={}", context, task.getId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA);
        }
        String relatedEntityId = task.getRelatedEntityId();
        objectVariable = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId), new Pair<>(relatedEntityId, relatedObjectId));
        return objectVariable;
    }

    private void setCustomVariables(RefServiceManager serviceManager, Task task, Map<String, Object> completedData, Map<String, Object> variables) {
        if (MapUtils.isNotEmpty(completedData)) {
            FlowElement element = serviceManager.getFlowElement(task.getElementApiName());
            List<FlowElementVariable> customVariables = element.getVariables();
            if (CollectionUtils.isNotEmpty(customVariables)) {
                customVariables.forEach(variable -> {
                    if (completedData.containsKey(variable.getId())) {
                        variables.put(variable.getId(), formatData(variable.getType(), completedData.get(variable.getId())));
                    }
                });
            }
        }
    }

    private Object formatData(String type, Object value) {
        Object result;
        switch (type) {
            case "number":
                result = NumberFormatUtil.instance.getLongByString(value);
                break;
            case "text":
                result = Objects.nonNull(value) ? value.toString() : null;
                break;
            case "list":
                result = Iterable.class.isInstance(value) ? value : null;
                break;
            default:
                result = Boolean.TRUE.equals(value);
        }
        return result;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.custom;
    }
}
