package com.facishare.bpm.handler.task.detail;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.TaskDetail;


/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/18 6:57 PM
 */
public interface GetTaskDetailHandler {

    TaskDetail execute(RefServiceManager serviceManager, Task task, TaskParams taskParams);

    StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams);

    ExecutionTypeEnum getTaskType();
}
