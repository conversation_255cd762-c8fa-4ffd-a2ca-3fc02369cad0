package com.facishare.bpm.handler.task.form.helper;

import com.facishare.bpm.handler.task.form.model.LayoutGroupConfig;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.internal.LinkedTreeMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class GroupTypeSortHelper {

    private volatile static Map<String, LayoutGroupConfig> groupConfig;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-bpm-form-group-field-config", config -> {
            Type tempType = new TypeToken<LinkedTreeMap<String, LayoutGroupConfig>>() {
            }.getType();
            groupConfig = JsonUtil.fromJson(new String(config.getContent()), tempType);
        });
    }

    public static List<Map<String, Object>> sortGroupField(Map<String, Object> group, List<Map<String, Object>> childs) {
        try {
            Map<String, Map<String, Object>> fieldsMap = childs.stream().collect(Collectors.toMap(field -> String.valueOf(field.get(WorkflowKey.ActivityKey.ExtensionKey.name)), field -> field));
            List<String> list = groupConfig.get(group).getChildSort();
            List<Map<String, Object>> rst = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(list)) {
                Map<String, String> childFields = (Map<String, String>) group.get(BPMConstants.MetadataKey.fields);
                list.forEach(item -> {
                    String childFieldName = String.valueOf(childFields.get(item));
                    Map<String, Object> child = fieldsMap.remove(childFieldName);
                    if (MapUtils.isNotEmpty(child)) {
                        rst.add(child);
                    }
                });
                fieldsMap.values().forEach(child -> rst.add(child));
            }
            return rst;
        } catch (Exception e) {
            return childs;
        }
    }

    public static boolean childDisplay(String group, String childRenderType, TaskParams taskParams) {
        try {
            if (taskParams.isIgnoreSignAndPayGroupLayout()) {
                return !groupConfig.get(group).cantDisplay(childRenderType);
            } else {
                //如果是web端,需要特殊处理下
                // 1. 支付组件需要下发所有字段 ， 签到组件也需要下发
                if ("payment".equals(group)) {
                    return true;
                }
                // 终端不能下发签到组件的form,但是新版本ui需要下发签到组件,但是不能编辑,故添加此处特殊逻辑
                if ("sign_in".equals(group) && !taskParams.isNotNeedSignIn()) {
                    return true;
                }
                return !groupConfig.get(group).cantDisplay(childRenderType);
            }
        } catch (Exception e) {
            return true;
        }
    }

}