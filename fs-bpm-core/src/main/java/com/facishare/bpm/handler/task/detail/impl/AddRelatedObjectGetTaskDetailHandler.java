package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.AddRelatedObjectTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.ParallelUtils;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/19 3:24 PM
 */
@Slf4j
@Service
public class AddRelatedObjectGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private AddRelatedObjectTaskButtonHandler addRelatedObjectTaskButtonHandler;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @SneakyThrows
    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        Map<String, Object> bpmExtension = task.getBpmExtension();
        ParallelUtils.createParallelTask()
          .submit(() -> {
              try {
                  serviceManager.findDataById(task.getEntityId(), task.getObjectId(), false, true, false,true);
              }catch (RestProxyBusinessException e){}
          })
          .submit(() -> serviceManager.hasObjectFunctionPrivilege(task.getEntityId()))
          .submit(() -> serviceManager.getSimpleEntityNames())
          .await(10, TimeUnit.SECONDS);
        standardData.setOnlyRelatedObject(MapUtil.instance.getBool(bpmExtension, WorkflowKey.ActivityKey.ExtensionKey.onlyRelatedObject));
        standardData.setRelatedEntityId((String) bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId));
        UserGetTaskDetailHandler.relatedOrMDObjectSetExt(serviceManager, paasWorkflowServiceProxy, standardData, task.getCompleted(), task.getBpmExtension(),taskParams,task.getEntityId() ,task.getObjectId() ,task.getExecutionType() ,task.getWorkflowInstanceId());
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        if (taskParams.isFromTaskDetail()) {
            //添加对象数据
            standardData.setData(serviceManager.findDataById(task.getEntityId(), task.getObjectId()));
        }
        return standardData;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task ,TaskParams taskParams) {

        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager,task);
        StandardData standardData = getStandardData(serviceManager,task,taskParams);
        taskDetail.setData(standardData);
        FormButtonResult formButtonResult = addRelatedObjectTaskButtonHandler.setButtons( serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));

        return taskDetail;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.addRelatedObject;
    }
}
