package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.impl.operationTaskHandler.OperationTaskBuildManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * 6.7
 * <p>
 * 1. 编辑对象 ,审批,会签 web端保持现状,展示成去处理;
 * 2. 终端 编辑对象可以自定义,终端不支持审批会签的自定义
 */


/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/26 2:12 PM
 */
@Service
public class OperationTaskButtonHandler implements TaskButtonHandler {

    @Autowired
    private OperationTaskBuildManager operationTaskManager;

    public static Map<String, String> getRealActionCodes(boolean isCombinedActionCode, String actionCode, String actionLabels) {
        Map<String, String> ret = Maps.newLinkedHashMap();
        if (isCombinedActionCode) {
            final String splitChar = "/";
            // 无效/进行中/转换
            String[] realActionLabels = actionLabels.split(splitChar);
            for (int i = 0; i < realActionLabels.length; i++) {
                ret.put(BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes.get(i), realActionLabels[i]);
            }
        } else {
            ret.put(actionCode, actionLabels);
        }

        return ret;
    }

    public static Map<String, String> filterActions(Map<String, String> actions,
                                                    Map<String, Boolean> func,
                                                    boolean isTransformed) {
        Map<String, String> ret = Maps.newLinkedHashMap();
        //H5 查询签到/签退任务对应的actionCode没有功能权限
        actions.keySet().forEach(actionCode -> {
            if (func.getOrDefault(actionCode, false)) {
                ret.put(actionCode, actions.get(actionCode));
            }
        });

        //如果是销售线索 并且是已转换  则删除所有按钮
        if (isTransformed) {
            for (int i = 0; i < BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes.size(); i++) {
                ret.remove(BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes.get(i));
            }
        }
        return ret;
    }

    /**
     * 2019年09月26日11:17:39  添加销售线索的判断,删除action
     * 待处理
     * 无效,跟进中,转换
     * 无效
     * 跟进中,转换
     * 跟进中
     * 无效,跟进中,转换
     * 已转换
     * 都不可执行
     */
    public static boolean leadsStatusTransfor(RefServiceManager serviceManager,
                                              String entityId,
                                              String objectId,
                                              String actionCode) {

        if (BPMConstants.MetadataKey.APINAME_LEADSOBJ.equals(entityId) && BPMConstants.MetadataKey.leadsObjHandleActionCode.equals(actionCode)) {
            Map data = serviceManager.findDataById(entityId, objectId, true, true);
            String bizStatus = (String) data.get("biz_status");
            //如果是已转换  则将无效 转换 跟进删除
            return !Strings.isNullOrEmpty(bizStatus) && "transformed".equals(bizStatus);
        }
        return false;
    }

    /**
     * 800 添加签到签退时， 由于业务逻辑比较多，将原有的操作逻辑 进行 拆分到 各自的handler中
     */
    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        return operationTaskManager.setBtnAndErrorMsg(serviceManager, standardData, taskParams);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operation;
    }

}
