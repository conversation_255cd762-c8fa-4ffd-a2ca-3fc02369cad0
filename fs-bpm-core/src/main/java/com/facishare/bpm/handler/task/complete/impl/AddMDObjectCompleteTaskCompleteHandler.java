package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:40 PM
 */
@Service
public class AddMDObjectCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {

    @Autowired
    private AddRelatedObjectCompleteTaskCompleteHandler addRelatedObjectCompleteHandler;

    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        return addRelatedObjectCompleteHandler.executeUserTask(serviceManager, task, opinion, completedData, addOrReplaceNextTaskAssignee, nextTaskAssignee, dataCacheHandler, ignoreNoBlockValidate);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.addMDObject;
    }
}
