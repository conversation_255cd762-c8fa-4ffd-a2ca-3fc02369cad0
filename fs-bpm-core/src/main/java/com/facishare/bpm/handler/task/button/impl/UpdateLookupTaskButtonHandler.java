package com.facishare.bpm.handler.task.button.impl;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_INCONSISTENT;
import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_NULL;

@Service
@Slf4j
public class UpdateLookupTaskButtonHandler implements TaskButtonHandler {

    @Autowired
    private BpmUpdateTaskButtonHandler bpmUpdateTaskButtonHandler;

    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        if(standardData.getRelatedFieldObjectIsIdentical() && standardData.getRelatedFieldObjectHasValue()){
            return bpmUpdateTaskButtonHandler.getFormButtonResult(getTaskType(), standardData, taskParams);
        }else {
            List<ActionButton> buttons = TaskButtonConfigHelper.getTaskFormButtons(getTaskType(), standardData.getOnlyRelatedObject());
            String errorMsg = "";
            //后动作进行不下发按钮
            if (standardData.isAfterActionWaiting()) {
                buttons = Lists.newArrayList();
            }else {
                //没有值  下发完成和错误信息
                buttons = buttons.stream().filter(item -> item.getAction()
                        .equalsIgnoreCase(BPMConstants.MetadataKey.COMPLETE_CODE)).collect(Collectors.toList());
                if (!standardData.getRelatedFieldObjectIsIdentical()){
                    errorMsg = I18N.text(PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_INCONSISTENT.key);
                }else {
                    errorMsg = I18N.text(PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_NULL.key);
                }
            }

            return new FormButtonResult(
                    TaskButtonHandler.getCustomButtons(getTaskType(), standardData.getDefaultButtons(), buttons)
                            .stream()
                            .peek(actionButton -> {
                                String label = actionButton.getLabel();
                                if (actionButton.isI18nConvert()) {
                                    String i18nLabel = I18N.text(label);
                                    log.info("convert:true, local:{},update buttons:{},{}", I18N.getContext().getLanguage(), label, i18nLabel);
                                    actionButton.setLabel(i18nLabel);
                                } else {
                                    log.info("convert:false,local:{},update buttons:{}", I18N.getContext().getLanguage(), label);
                                }
                            })
                            .collect(Collectors.toList()), errorMsg);
            }
        }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.updateLookup;
    }
}
