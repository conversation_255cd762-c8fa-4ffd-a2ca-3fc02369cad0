package com.facishare.bpm.handler.task.detail.model;

import com.facishare.bpm.handler.task.button.model.DefaultActionLabel;
import com.facishare.bpm.handler.task.form.model.CustomerData;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class StandardData {

    private volatile String taskId;
    private String appCode;
    private String actionCode;
    private String actionLabel;
    //TODO 序列化的时候改名字
    private String object_describe_api_name;
    private String relatedFieldApiName;
    private String entityName;
    private String entityId;
    private String objectId;
    private String relatedEntityName;
    private Object objectName;
    private Object relatedObjectId;
    private Object relatedObjectName;
    private Object target_related_list_name;
    private String relatedEntityId;
    private Boolean onlyRelatedObject;
    private Object reminders;
    protected Map<String, Object> relatedDescribe;
    /**
     * 是否需要拆分字符串  无效/跟进/转换
     */
    private transient boolean combinedActionCode;
    protected Map<String, Object> data;
    protected Map<String, Object> layout;
    protected Map<String, Object> describe;
    protected Map<String, Object> describeExt;
    //流程对象布局是否存在（false-布局已禁用，true-布局可用）
    private Boolean objectFlowLayoutExists;
    private String layoutType;
    private String layoutApiName;
    //定义中的默认button,需要将值读出,并保存到button中
    private transient Map<String, DefaultActionLabel> defaultButtons;
    //是否存在表单,目前只有更新时用到
    private transient Task.UpdateFromProperty updateFromProperty;
    private Boolean hasPermissions;
    /**
     * 是否引用布局规则
     */
    private Boolean enableLayoutRules;
    /**
     * 数据权限
     */
    protected Map<String, Boolean> objectPermissions;

    /**
     * 后动作是否处于等待中
     */
    private boolean isAfterActionWaiting;

    /**
     * 去处理按钮跳转地址
     * 只有终端会使用此地址
     * web不会使用
     */
    private String todoJumpUrl;

    /**
     * 下游深研下发的地址  需要终端拼接参数, 就会出现  ?a=1&b=1
     * 但是有的地址  深研是已经拼接了参数的  例如  www.fxiaoke.com/data?m=1&n=2  ,然后终端又拼接了?a=1&b=1
     * 地址为 www.fxiaoke.com/data?m=1&n=2?a=1&b=1  这就导致页面跳转的时候  深研侧无法获取到a和b的值  故添加此参数,告诉终端
     * 是拼接 ?  还是 &
     */
    private String joiner;

    /**
     * 编辑的关联字段对象是否有值 编辑关联字段对象专用
     */
    private Boolean relatedFieldObjectHasValue = Boolean.FALSE;

    /**
     * 编辑的关联entityId是否一致 编辑关联字段对象专用
     */
    private Boolean relatedFieldObjectIsIdentical = Boolean.TRUE;

    /**
     * 自定义按钮的ApiNames
     */
    private List<String> commonButtonApiNames;

    /**
     * 审批节点是否支持编辑 840
     */
    private Boolean formEditable;

    private transient String instanceId;

    /**
     * 是否隐藏 完成 按钮
     */
    private Boolean hideCompleteBtn;

    private Boolean sequence;

    private List<String> sequenceTaskCandidateIds;

    private List<OperateLog> operateLogs;

    private TagInfo tagInfo;

    private List<Opinion> opinions;

    public void initCustomerData(CustomerData customerData) {
        this.setData(customerData.getData());
        this.setDescribe(customerData.getDescribe());
        this.setLayout(customerData.getLayout());
        this.setDescribeExt(customerData.getDescribeExt());
    }

    public void initFlowLayoutData(Task task) {
        this.setObjectFlowLayoutExists(task.getObjectFlowLayoutExists());
        this.setLayoutType(task.getLayoutType());
        this.setLayoutApiName(task.getLayoutApiName());
    }

    public void setObjectPermissions(Map<String, Boolean> objectPermissions) {
        if (MapUtils.isEmpty(objectPermissions)) {
            return;
        }
        this.objectPermissions = objectPermissions;
    }

    /**
     * 所有操作通用逻辑
     *
     * @param task
     * @return
     */
    public static StandardData getStandardData(Task task) {
        StandardData standardData = new StandardData();
        standardData.setReminders(task.getReminders());
        standardData.setEntityId(task.getEntityId());
        standardData.setObjectId(task.getObjectId());
        standardData.setTaskId(task.getId());
        standardData.setAfterActionWaiting(task.isAfterActionWaiting());
        standardData.setLayoutType(task.getLayoutType());

        Map<String, Object> bpmExtension = task.getBpmExtension();
        standardData.setDefaultButtons(JacksonUtil.fromJson(
                JacksonUtil.toJson(bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.DEFAULT_BUTTONS)),
                new TypeReference<Map<String, DefaultActionLabel>>() {
                }));
        standardData.setHideCompleteBtn((Boolean) task.getBpmExtension().get(BPMConstants.HIDE_COMPLETE_BTN));
        if(Boolean.TRUE.equals(task.getSequence())){
            standardData.setSequence(task.getSequence());
            standardData.setSequenceTaskCandidateIds(task.getSequenceTaskCandidateIds());
        }
        return standardData;
    }


    /**
     * 兼容18年老接口  getBpmTask,应用节点主要参数是appCode
     *
     * 深研需求  应用节点 支持多按钮
     *
     * @return
     */
    public static StandardData createStandardDataByExternalApplyTask(BPMTask bpmTask) {
        StandardData standardData = new StandardData();
        standardData.setAppCode(bpmTask.getAppCode());
        standardData.setActionCode(bpmTask.getActionCode());
        standardData.setEntityId(bpmTask.getEntityId());
        standardData.setObjectId(bpmTask.getObjectId());
        standardData.setTaskId(bpmTask.getId());
        return standardData;
    }


    public Boolean getOnlyRelatedObject() {
        return onlyRelatedObject != null && onlyRelatedObject;
    }

    public Boolean getHasPermissions() {
        return hasPermissions != null && hasPermissions;
    }

    public Boolean getEnableLayoutRules() {
        return enableLayoutRules != null && enableLayoutRules;
    }

    //根据签到组件描述设置相关的签到的ApiName
    public SignInInfoApiName createSignInInfoApiNameBySignInDescribe(Map singInAssemblyDescribe){
        SignInInfoApiName result = new SignInInfoApiName();

        Map<String, Object> signInFields = (Map<String, Object>) singInAssemblyDescribe.get(BPMConstants.MetadataKey.fields);
        //设置签到地点、签到时间、签退地点、签退时间ApiName、
        result.setSignInPlace((String) signInFields.get("sign_in_location_field"));
        result.setSignInTime((String) signInFields.get("sign_in_time_field"));
        result.setSignOutPlace((String) signInFields.get("sign_out_location_field"));
        result.setSignOutTime((String) signInFields.get("sign_out_time_field"));
        return result;
    }

    @Data
    public class SignInInfoApiName{
        private String signInPlace;
        private String signInTime;
        private String signOutPlace;
        private String signOutTime;


        public List<String> getApiNameListByActionType(BPMConstants.OperationCodeType operationCodeType){
            switch (operationCodeType){
                case signin:
                    return Lists.newArrayList(this.signInPlace, this.signInTime);
                case signout:
                    return Lists.newArrayList(this.signOutPlace, this.signOutTime);
            }
            return Lists.newArrayList();
        }
    }
    //不序列化
    public Map<String, String> createActionCodeAndLabels() {
        Map<String, String> ret = Maps.newLinkedHashMap();
        // 线索一转三
        if (BPMConstants.OperationCodeType.HandleThree.name().equals(actionCode)) {
            final String splitChar = "/";
            // 无效/进行中/转换
            String[] realActionLabels = this.getActionLabel().split(splitChar);
            for (int i = 0; i < realActionLabels.length; i++) {
                ret.put(BPMConstants.MetadataKey.leadsObjThreeHandleActionCodes.get(i), realActionLabels[i]);
            }
        } else {
            ret.put(actionCode, this.getActionLabel());
        }

        return ret;
    }

    public List<String> getSequenceTaskCandidateIds(){
        if(Objects.isNull(this.sequenceTaskCandidateIds)){
            this.sequenceTaskCandidateIds = Lists.newArrayList();
        }
        return this.sequenceTaskCandidateIds;
    }

}