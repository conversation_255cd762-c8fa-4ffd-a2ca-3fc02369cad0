package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:41 PM
 */
@Slf4j
@Service
public class BatchAddRelatedObjectCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {

    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {

        RemoteContext context = serviceManager.getContext();

        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(task.getActivityId(), new Pair<>(task.getEntityId(),
                task.getObjectId()));

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager, task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKey,
                dataCacheHandler);

        CompleteTask.Result completeTaskResult = paasWorkflowServiceProxy.completeTask(context,
                task.getId(),
                CompleteTask.ActionType.AUTO_AGREE,
                variables,
                opinion,
                addOrReplaceNextTaskAssignee,
                nextTaskAssignee, ignoreNoBlockValidate);

        log.info("batchAddRelatedObjectCompleteHandler complete Task :tenantId:{},taskId:{}", task.getTenantId(), task.getId());
        reportFlowBizLog(context, task ,null);
        return completeTaskResult;
    }


    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.batchAddRelatedObject;
    }

}
