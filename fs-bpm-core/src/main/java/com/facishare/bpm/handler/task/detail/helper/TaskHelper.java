package com.facishare.bpm.handler.task.detail.helper;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.model.resource.metadata.RW;
import com.facishare.bpm.util.MetadataUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 08/03/2017.
 */
@Slf4j
public class TaskHelper {
    /**
     * 国家省市区，在CRM老对象中时一个字段，校验必填字段时，不分别校验
     */
    static List<String> countryCascadeFields = Lists.newArrayList("country", "province", "city", "district");

    public static void setCompletedTaskForm(RefServiceManager serviceManager,
                                            Map<String, Object> bpmExtension,
                                            String entityId,
                                            String objectId) {

        Map<String, Object> objectData = serviceManager.findDataById(entityId, objectId, false, true,false,true);
        Map<String, Object> desc = serviceManager.findDescribe(entityId, true, true);
        Map<String, Integer> fieldsAuth = serviceManager.getFieldPermissions(entityId);

        Map<String, Object> fieldDescs = (Map<String, Object>) desc.get(MetadataKey.fields);
        List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) bpmExtension.get(ExtensionKey.form);
        if (forms != null && objectData != null) {
            for (List<Map<String, Object>> form : forms) {
                List<Map<String, Object>> hiddenItems = Lists.newArrayList();
                for (Map<String, Object> formItem : form) {
                    String fieldName = (String) formItem.get(ExtensionKey.name);
                    if (isHidden(fieldsAuth, formItem)) {
                        hiddenItems.add(formItem);
                        continue;
                    }

                    Map<String, Object> fieldDesc = (Map<String, Object>) fieldDescs.get(fieldName);
                    // 字段描述为空|| 字段描述中的isActive=false,获取不到默认为true
                    if (MapUtils.isEmpty(fieldDesc) || !(boolean) fieldDesc.getOrDefault(MetadataKey.isActive, true)) {
                        hiddenItems.add(formItem);
                        continue;
                    }

                    //1.设置value
                    Object expObj = objectData.get(fieldName);
                    formItem.put(ExtensionKey.value, expObj);
                    //2. select类型加上options,lookup类型加上relatedObjectName
                    setFormFieldsExtension(objectData,formItem, fieldDesc);
                    setOtherSelectOneValue(expObj, fieldDesc, formItem, objectData, fieldName);


                    //todo 商机和订单有几个字段，描述不对，应该是is_required 携程 required了。 这里暂时做下兼容，需要open-api刷库
                    boolean isRequired = (boolean) fieldDesc.getOrDefault(MetadataKey.isRequired, false);

                    if (isRequired && !TaskHelper.hasInheritType(fieldDesc)) {
                        formItem.put(ExtensionKey.required, true);
                    }

                    //3.label动态加载
                    formItem.put(MetadataKey.label, fieldDesc.get(MetadataKey.label));

                    setFormFieldProperty(fieldsAuth, formItem, objectData,fieldDesc);
                }
                form.removeAll(hiddenItems);
            }

        }

    }


    public static String setUncompletedTaskFrom(RefServiceManager serviceManager,
                                                Map<String, Object> bpmExtension,
                                                String entityId,
                                                String objectId) {

        Map<String, Object> objectData = serviceManager.findDataById(entityId, objectId, false, true,false,true);
        Map<String, Object> desc = serviceManager.findDescribe(entityId, true, true);
        Map<String, Integer> fieldsAuth = serviceManager.getFieldPermissions(entityId);
        boolean hasCountry = true;
        //必填字段但为空的字段.在更新时如果为空,会更新失败.在此遍历,记录,给出提示
        if (desc != null) {
            List<String> noValueFields = verifyRequiredFields(objectData, desc);
            objectData = MetadataUtils.validateAndCorrectDataValue(objectData, desc);
            Map<String, Object> fieldDescs = (Map<String, Object>) desc.get(MetadataKey.fields);
            List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) bpmExtension.get(ExtensionKey.form);
            if (forms != null) {
                for (List<Map<String, Object>> form : forms) {
                    List<Map<String, Object>> hiddenItems = Lists.newArrayList();
                    for (Map<String, Object> formItem : form) {
                        String fieldName = (String) formItem.get(ExtensionKey.name);
                        //1.如果没有权限，就不再value填充了，待下面统一去除没有权限字段
                        if (isHidden(fieldsAuth, formItem)) {
                            hiddenItems.add(formItem);
                            continue;
                        }

                        //2.填充字段value（默认值/表达式/节点对象值）; select类型加上options,lookup类型加上relatedObjectName
                        Map<String, Object> fieldDesc = (Map<String, Object>) fieldDescs.get(fieldName);
                        // 字段描述为空|| 字段描述中的isActive=false,获取不到默认为true
                        if (MapUtils.isEmpty(fieldDesc) || !(boolean) fieldDesc.getOrDefault(MetadataKey.isActive, true)) {
                            hiddenItems.add(formItem);
                            continue;
                        }
                        setUncompletedFormValue(objectData, formItem, fieldDesc, serviceManager);

                        String fieldType = (String) fieldDesc.get(MetadataKey.type);

                        if (BPMConstants.MetadataKey.accountObjApiName.equals(entityId) && BPMConstants.AREA_TYPE.contains(fieldType) && hasCountry) {
                            hasCountry = false;
                            Map<String, Object> areaLocation = (Map) fieldDescs.get(BPMConstants.MetadataKey.areaLocation);
                            if (MapUtils.isNotEmpty(areaLocation)) {
                                hiddenItems.add(areaLocation);
                            }
                        }

                        //3.label动态加载
                        formItem.put(MetadataKey.label, fieldDesc.get(MetadataKey.label));
                        formItem.put(MetadataKey.type, fieldDesc.get(MetadataKey.type));
                        formItem.put(MetadataKey.RETURN_TYPE, fieldDesc.get(MetadataKey.RETURN_TYPE));
                        //4.判读当前字段是否是必填字段
                        //todo 商机和订单有几个字段，描述不对，应该是is_required 写成了 required。 这里暂时做下兼容，需要open-api刷库
                        boolean isRequired = (boolean) fieldDesc.getOrDefault(MetadataKey.isRequired, false);
                        if (isRequired && !TaskHelper.hasInheritType(fieldDesc)) {
                            formItem.put(ExtensionKey.required, true);
                            //form中已经存在的必填字段就不再提示文案了
                            noValueFields.remove(fieldDesc.get(MetadataKey.label));
                        }
                        setFormFieldProperty(fieldsAuth, formItem, objectData,fieldDesc);

                    }
                    form.removeAll(hiddenItems);
                }
            }
            if (CollectionUtils.isNotEmpty(noValueFields)) {
                return BPMI18N.PAAS_FLOW_BPM_CURRENT_TASK_FORM_HAS_MUST_FIELD.text().replaceFirst("-", String.join(",",
                        noValueFields));
            }
        }
        return null;
    }

    private static void setUncompletedFormValue(Map<String, Object> objectData,
                                                Map<String, Object> formItem,
                                                Map<String, Object> fieldDesc,
                                                RefServiceManager serviceManager) {

        String fieldName = (String) formItem.get(ExtensionKey.name);

        //1.如果是表达式，用表达式数据填充；如果有默认值不用动；如果都没有，就用节点业务对象数据填充
        if (objectData != null) {
            formItem.put(ExtensionKey.value, objectData.get(fieldName));
        }

        Object expObj = formItem.get(ExtensionKey.value);
        setOtherSelectOneValue(expObj, fieldDesc, formItem, objectData, fieldName);

        //2.填充select option/关联对象信息
        setFormFieldsExtension(objectData,formItem, fieldDesc);
    }

    /**
     * 单选字段中 其他字段放开后,值永远=other,可以输入内容数据在字段__o中存储
     *
     * @param expObj
     * @param fieldDesc
     * @param formItem
     * @param objectData
     */
    private static void setOtherSelectOneValue(Object expObj, Map<String, Object> fieldDesc, Map<String, Object> formItem, Map<String, Object> objectData, String fieldName) {
        if (null != fieldDesc) {
            String fieldType = (String) fieldDesc.get(ExtensionKey.type);
            if (Objects.nonNull(expObj)) {
                if (ExtensionKey.select_one.equals(fieldType) && ExtensionKey.other.equals(expObj + "")) {
                    String otherKey = fieldName + ExtensionKey.select_suffix;
                    formItem.put(ExtensionKey.value + ExtensionKey.select_suffix, objectData.get(otherKey));
                }

                if (ExtensionKey.select_many.equals(fieldType) && expObj instanceof List) {
                    List<Object> value = (List<Object>) expObj;
                    if (CollectionUtils.isNotEmpty(value) && ExtensionKey.other.equals(value.get(0))) {
                        String otherKey = fieldName + ExtensionKey.select_suffix;
                        formItem.put(ExtensionKey.value + ExtensionKey.select_suffix, objectData.get(otherKey));
                    }
                }

            }
        }
    }

    /**
     * 设置一些描述相关的信息
     *
     * @param formItem
     * @param fieldDesc
     */
    protected static void setFormFieldsExtension(Map<String, Object> objectData,Map<String, Object> formItem,
                                                 Map<String, Object> fieldDesc) {

        if (fieldDesc != null) {
            String fieldType = (String) formItem.get(ExtensionKey.type);
            if (fieldDesc.get(ExtensionKey.options) != null) {
                List<Map<String, Object>> options = (List<Map<String, Object>>) fieldDesc.get(ExtensionKey.options);
                List<Map<String, Object>> availableOptions = options.stream()
                        .filter(m -> !Boolean.TRUE.equals(m.get("not_usable")))
                        .collect(Collectors.toList());
                formItem.put(ExtensionKey.options, availableOptions);
            }
            if (MetadataKey.objectReference.equals(fieldType)) {
                String relatedEntityId = (String) fieldDesc.get(MetadataKey.targetApiName);
                String relatedObjectId = (String) formItem.get(ExtensionKey.value);
                formItem.put(ExtensionKey.relatedEntityId, relatedEntityId);
                formItem.put(MetadataKey.targetDisplayName, fieldDesc.get(MetadataKey
                        .targetDisplayName));
                formItem.put(ExtensionKey.relatedListName, fieldDesc.get(ExtensionKey.relatedListName));
                String lookupField=formItem.get("name")+"__r";
                if (StringUtils.isNotBlank(relatedObjectId)&&objectData!=null&&objectData.get(lookupField)!=null) {
                    log.debug("getTasksByInstanceIds:entityId:{},objectId:{}", relatedEntityId, relatedObjectId);
                    formItem.put(ExtensionKey.relatedObjectName, objectData.get(lookupField));
                }
            }
//            将自定义字段的描述信息  填充到form的字段上
            fieldDesc.forEach((key, value) -> {
                if (!(key.equals(MetadataKey.apiName) || key.equals(MetadataKey.isRequired) || key.equals(MetadataKey.isReadonly))) {
                    formItem.putIfAbsent(key, value);
                }
            });
        }
    }

    private static List<String> verifyRequiredFields(Map<String, Object> data, Map<String, Object> desc) {
        if (data == null || desc == null) {
            return Lists.newArrayList();
        }

        List<String> fieldLabels = Lists.newLinkedList();

        Map<String, Map<String, Object>> fieldDescs = (Map<String, Map<String, Object>>) desc.get(MetadataKey.fields);
        fieldDescs.forEach((field, fieldDesc) -> {
            //当必填值为空时
            if (Boolean.TRUE.equals(fieldDesc.get(MetadataKey.isRequired)) && Boolean.TRUE.equals(fieldDesc.get(MetadataKey.isActive))) {
                if (countryCascadeFields.contains(field)) {
                    if ("country".equals(field) && Strings.isNullOrEmpty((String) data.get("country"))) {
                        fieldLabels.add(BPMI18N.PAAS_FLOW_BPM_NATIONAL_PROVINCIAL_CITY_AND_DISTRICT.text());
                    }
                } else if (null == data.get(field) || "".equals(data.get(field).toString())) {
                    fieldLabels.add((String) fieldDesc.get(MetadataKey.label));
                }
            }
        });
        return fieldLabels;
    }


    public static void setFormFieldProperty(Map<String, Integer> fieldsAuth, Map<String, Object> item, Map<String, Object> objectData,Map<String, Object> fieldDesc) {
        String fieldName = (String) item.get(BPMConstants.NAME);
        boolean readonly = (boolean) item.getOrDefault(BPMConstants.READONLY, false);
        boolean hidden = (boolean) item.getOrDefault(BPMConstants.HIDDEN, false);
        //字段权限的设置 可参考 表单权限

        /*
         * 若是掩码字段，为只读; 若是被阶段推进器引用 ,为只读 @松哥 @帅哥 2020年09月17日17:46:32
         */
        if (isMask(objectData, item)||isStageUse(fieldDesc)||hasInheritType(fieldDesc)) {
            readonly = true;
        } else {
            RW rw = MetadataUtils.getFieldAccess(fieldsAuth, fieldName);

            if (readonly || rw == RW.Read) {
                readonly = true;
            } else if (rw == RW.Write && !readonly) {
                readonly = false;
            }
            log.debug("getUserPrivilegeOfFields: field:{},form:{},auth:{},result:{}", fieldName,
                    item.getOrDefault(BPMConstants.READONLY, false) + "," + item.getOrDefault(BPMConstants.HIDDEN, false), rw.name(), readonly + "," + hidden);
        }

        item.put(BPMConstants.READONLY, readonly);
        item.put(BPMConstants.HIDDEN, hidden);
    }

    private static boolean isHidden(Map<String, Integer> fieldsAuth, Map<String, Object> item) {
        String fieldName = (String) item.get("name");
        RW rw = MetadataUtils.getFieldAccess(fieldsAuth, fieldName);

        if (rw == RW.Denied) {
            log.debug("current user has no permission with the FIELD={}, should be removed", fieldName);
            return true;
        } else {
            return false;
        }
    }

    public static void setTaskForm(
            RefServiceManager serviceManager,
            Boolean completed,
            Map<String, Object> bpmExtension,
            String entityId,
            String objectId) {

        if (!Boolean.TRUE.equals(completed)) {
            TaskHelper.setUncompletedTaskFrom(serviceManager, bpmExtension, entityId, objectId);
        } else {
            TaskHelper.setCompletedTaskForm(serviceManager, bpmExtension, entityId, objectId);
        }
    }

    /**
     * 是否掩码
     */
    public static boolean isMask(Map<String, Object> objectData, Map<String, Object> item) {
        String fieldName = (String) item.get(MetadataKey.name);
        return Objects.nonNull(objectData.get(fieldName + MetadataKey.maskFieldNameValuePostfix));
    }

    /**
     * 是否被阶段推进器使用  如果是  则不允许在业务流中进行编辑
     *
     * @param fieldDesc
     * @return
     */
    private static boolean isStageUse(Map<String, Object> fieldDesc) {
        return Optional.ofNullable(fieldDesc)
                .map(item -> Optional.ofNullable((Boolean) fieldDesc.get(MetadataKey.IS_USED_BY_STAGE))
                        .orElse(false)).orElse(false);
    }

    public static boolean hasInheritType(Map<String, Object> fieldDesc) {
        boolean inherit = false;
        if (fieldDesc == null) {
            return inherit;
        }
        //存在继承关系(主子字段)
        Integer inheritType = (Integer) fieldDesc.get(MetadataKey.INHERIT_TYPE);
        if (inheritType != null && (inheritType == 1 || inheritType == 2)) {
            inherit = true;
        }
        return inherit;
    }

}
