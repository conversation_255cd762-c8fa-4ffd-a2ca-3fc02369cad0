package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_LAYOUT_DELETED;

/**
 * 6.7
 * <p>
 * 1. 编辑对象 ,审批,会签 web端保持现状,展示成去处理;
 * 2. 终端 编辑对象可以自定义,终端不支持审批会签的自定义
 * <p>
 * 3. 假如form中没有字段,则不下发更新,更新并完成任务,之下发完成任务即可
 */

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/26 11:55 AM
 */
@Slf4j
@Service
public class BpmUpdateTaskButtonHandler implements TaskButtonHandler {


    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        return getFormButtonResult(getTaskType(), standardData, taskParams);
    }

    public FormButtonResult getFormButtonResult(ExecutionTypeEnum taskType, StandardData standardData, TaskParams taskParams) {
        String errorMsg = "";

        List<ActionButton> buttons = TaskButtonConfigHelper.getTaskFormButtons(taskType, standardData.getOnlyRelatedObject());

        //数据权限
        Map<String, Boolean> func = standardData.getObjectPermissions();
        /*
        1. 用户没有对象的功能权限的时候,只下发完成任务按钮,且字段只显示,不能编辑
        2. form中只有签到组件时,下发 保存并完成任务 和保存  俩按钮
        3. form中没有选择字段时,只下发完成任务按钮
        4. 假如form中选择了4个只读字段, 下发保存并完成任务 和保存  俩按钮
         */
        //1. 用户没有对象的功能权限的时候,只下发完成任务按钮,且字段只显示,不能编辑
        standardData.setHasPermissions(func.get(BPMConstants.MetadataKey.EDIT));

        //没权限  只下发完成任务按钮
        if (!standardData.getHasPermissions()) {
            //下发完成任务按钮
            buttons = buttons.stream().filter(item -> item.getAction()
                    .equalsIgnoreCase(BPMConstants.MetadataKey.COMPLETE_CODE)).collect(Collectors.toList());
        }
        if(BPMConstants.LayoutType.objectFlowLayout.equals(standardData.getLayoutType())){
            buttons = getLayoutButtons(taskParams, buttons, standardData.getObjectFlowLayoutExists());
            if (Boolean.FALSE.equals(standardData.getObjectFlowLayoutExists())) {
                errorMsg = I18N.text(PAAS_FLOW_BPM_LAYOUT_DELETED.key);
            }
        }else {
            //没有使用流程对象布局
            buttons = getUpdateButtons(standardData, (taskParams.isMobile() || taskParams.isH5()), taskParams.isTaskDetail(), buttons);
        }

        if (standardData.isAfterActionWaiting()) {
            buttons = Lists.newArrayList();
        }

        return new FormButtonResult(
                TaskButtonHandler.getCustomButtons(taskType, standardData.getDefaultButtons(), buttons)
                        .stream()
                        .peek(actionButton -> {
                            String label = actionButton.getLabel();
                            if (actionButton.isI18nConvert()) {
                                String i18nLabel = I18N.text(label);
                                log.info("convert:true, local:{},update buttons:{},{}", I18N.getContext().getLanguage(), label, i18nLabel);
                                actionButton.setLabel(i18nLabel);
                            } else {
                                log.info("convert:false,local:{},update buttons:{}", I18N.getContext().getLanguage(), label);
                            }
                        })
                        .collect(Collectors.toList()), errorMsg);
    }

    public List<ActionButton> getUpdateButtons(StandardData standardData, boolean isMobile, boolean isWebDetail, List<ActionButton> buttons) {
        if (isMobile) {
            //如果是签到组件
            if (standardData.getUpdateFromProperty().isOnlyExistsSigninGroup()) {
                //移除:保存  保存并完成任务
                buttons = buttons.stream().filter(item ->
                        !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.SAVE_CODE)
                                && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE)
                ).collect(Collectors.toList());
            } else {
                //如果存在字段 返回  填写  保存 保存并完任务 完成任务
                if (!standardData.getUpdateFromProperty().isFormExistsNormalFields()) {
                    // 如果没有字段移除:填写  保存 保存并完任务
                    buttons = buttons.stream().filter(item ->
                            !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.SAVE_CODE)
                                    && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE)
                                    && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_CODE)
                    ).collect(Collectors.toList());
                }
            }
        } else {
            //如果只有签到组件  || from中没有字段
            if (standardData.getUpdateFromProperty().isOnlyExistsSigninGroup() || !standardData.getUpdateFromProperty().isFormExistsNormalFields()) {
                //移除:填写  保存 保存并完任务
                buttons = buttons.stream().filter(item ->
                        !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.SAVE_CODE)
                                && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE)
                                && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_CODE)
                ).collect(Collectors.toList());
            } else {
                //任务落地页  && 有表单 && 也存在权限时 需要将 完成任务 按钮移除
                if (isWebDetail && standardData.getUpdateFromProperty().isFormExistsNormalFields() && standardData.getHasPermissions()) {
                    buttons = buttons.stream().filter(item ->
                            !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.COMPLETE_CODE)
                    ).collect(Collectors.toList());
                }
            }
        }
        return buttons;
    }

    public List<ActionButton> getLayoutButtons(TaskParams taskParams, List<ActionButton> buttons, Boolean layoutIsExists) {
        if(Boolean.FALSE.equals(layoutIsExists)){
            return buttons.stream().filter(item -> item.getAction()
                    .equalsIgnoreCase(BPMConstants.MetadataKey.COMPLETE_CODE)).collect(Collectors.toList());
        }
        return buttons;

    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.update;
    }
}
