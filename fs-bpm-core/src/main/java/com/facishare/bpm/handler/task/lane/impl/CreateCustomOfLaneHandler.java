package com.facishare.bpm.handler.task.lane.impl;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.CustomTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


/**
 * Created by wangzhx on 2019/4/29.
 */
@Slf4j
@Service
public class CreateCustomOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private CustomTaskButtonHandler customTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        Map customExtension = task.getCustomExtension();
        laneTask.elementConfigData(customExtension,serviceManager.getFlowElementWrapper(task.getElementApiName()).getPlugin());
        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)) {
            return laneTask;
        }
        setButtonsAndErrorMsg(serviceManager, customTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.custom;
    }
}
