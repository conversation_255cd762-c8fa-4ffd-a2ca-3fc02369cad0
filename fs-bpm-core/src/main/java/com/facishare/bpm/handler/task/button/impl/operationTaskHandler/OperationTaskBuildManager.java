package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.TaskDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OperationTaskBuildManager extends ApplicationObjectSupport {

    private Map<String, IOperationTaskHandler> operationTaskBuildHandlerMap;


    @PostConstruct
    public void init() {
        Map<String, IOperationTaskHandler> operationTaskHandler = getApplicationContext().getBeansOfType(IOperationTaskHandler.class);
        operationTaskBuildHandlerMap = operationTaskHandler.values().stream().collect(Collectors.toMap(IOperationTaskHandler::getType, k -> k));
    }

    /**
     * 获取button 及error message
     */
    public FormButtonResult setBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        IOperationTaskHandler handler = operationTaskBuildHandlerMap.getOrDefault(standardData.getActionCode(), operationTaskBuildHandlerMap.get(IOperationTaskHandler.defaultHandlerType));
        FormButtonResult result = handler.getBtnAndErrorMsg(serviceManager, standardData, taskParams);
        result.setButtons(TaskButtonHandler.getCustomButtons(ExecutionTypeEnum.operation, standardData.getDefaultButtons(), result.getButtons()));
        return result;
    }

    /**
     * 获取任务详情信息
     *
     * @param serviceManager
     * @param task
     * @param taskParams
     * @return
     */
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        String actionCode = task.getActionCode();
        IOperationTaskHandler handler = operationTaskBuildHandlerMap.getOrDefault(actionCode, operationTaskBuildHandlerMap.get(IOperationTaskHandler.defaultHandlerType));
        return handler.getTaskDetail(serviceManager, task, taskParams);
    }

    /**
     * 获取数据详情页任务信息
     *
     * @param serviceManager
     * @param task
     * @param taskParams
     * @return
     */
    public LaneTask getLaneTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        String actionCode = task.getActionCode();
        IOperationTaskHandler handler = operationTaskBuildHandlerMap.getOrDefault(actionCode, operationTaskBuildHandlerMap.get(IOperationTaskHandler.defaultHandlerType));
        return handler.getLaneTaskDetail(serviceManager, task, taskParams);
    }

}
