package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.OperationMultiTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.TaskDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class OperationMultiGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private OperationMultiTaskButtonHandler operationMultiTaskButtonHandler;

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        //下发数据
        standardData.setData(serviceManager.findDataById(standardData.getEntityId(), standardData.getObjectId(), false, true, false,true));
        taskDetail.setData(standardData);
        FormButtonResult formButtonResult = operationMultiTaskButtonHandler.setButtons(
                serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        return taskDetail;
    }

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams){
        StandardData standardData = StandardData.getStandardData(task);
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        standardData.setCommonButtonApiNames((List<String>) task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.COMMON_BUTTON_API_NAMES));
        return standardData;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operationMulti;
    }
}
