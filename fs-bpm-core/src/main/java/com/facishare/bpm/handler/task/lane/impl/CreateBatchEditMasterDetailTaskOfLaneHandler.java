package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.BatchEditMasterDetailButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardDataOfBatchEditMasterDetailObject;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.proxy.PaasLogProxy;
import com.facishare.bpm.utils.bean.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/19 12:14 PM
 */
@Slf4j
@Component
public class CreateBatchEditMasterDetailTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private BatchEditMasterDetailButtonHandler batchEditMasterDetailButtonHandler;


    @Autowired
    private PaasLogProxy paasLogProxy;


    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {

        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardDataOfBatchEditMasterDetailObject standardData =
                BeanUtils.transfer(laneTask.getData(), StandardDataOfBatchEditMasterDetailObject.class);
        if (Objects.isNull(standardData)) {
            return laneTask;
        }

        standardData.setShowDataChangeLog(paasLogProxy.hasEditLog(serviceManager.getContext(),task.getId(),task.getEntityId(),task.getObjectId(), task.getCreateTime()));
        laneTask.setData(standardData);
        setButtonsAndErrorMsg(serviceManager, batchEditMasterDetailButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.batchEditMasterDetailObject;
    }
}
