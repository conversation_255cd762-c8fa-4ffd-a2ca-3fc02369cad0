package com.facishare.bpm.handler.task.form.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.form.UserFormToMetadataManager;
import com.facishare.bpm.handler.task.form.helper.GroupTypeSortHelper;
import com.facishare.bpm.handler.task.form.model.FormAssembleType;
import com.facishare.bpm.handler.task.form.model.FormField;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_AREA_LOCATION;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey.group_type;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 7:25 PM
 */
@Service
public class GenerateLayoutManagerImpl implements UserFormToMetadataManager {


    public final String buttons = "buttons";
    public List typeCountry = Lists.newArrayList("country","province", "city", "district");

    public String field = "{\"field_name\":\"%s\",\n" +
            "                \"is_readonly\":%s,\n" +
            "                \"render_type\":\"%s\",\n" +
            "                \"is_required\":%s}";



    static Type type = new TypeToken<List<List<Map<String, Object>>>>() {
    }.getType();

    @Override
    public Map<String, Object> execute(RefServiceManager serviceManager, String entityId, String objectId, List<List<Map<String, Object>>> src,TaskParams taskParams) {
        List<List<Map<String, Object>>> forms = JsonUtil.fromJson(JsonUtil.toJson(src), type);
        Map<Map<String, Object>, List<Map<String, Object>>> groupFields = getGroups(forms);
        Map<String, Object> layout = Maps.newHashMap();
        layout.put(BPMConstants.MetadataKey.apiName, "bpmLayout");
        layout.put(BPMConstants.MetadataKey.refObjectApiName, entityId);
        layout.put(BPMConstants.MetadataKey.layoutType, "edit");
        List<Map<String, Object>> components = Lists.newArrayList();
        layout.put(BPMConstants.MetadataKey.components, components);
        Map<String, Object> component = Maps.newHashMap();
        components.add(component);
        List<ActionButton> actionButtons = Lists.newArrayList();
        Map<String, Object> desc = serviceManager.getFields(entityId);
        //675 需求,给终端下发按钮,之下发保存,保存并完成任务
        Collection<ActionButton> customButtons = taskParams.getButtons();
        if (CollectionUtils.isNotEmpty(customButtons)) {
            Map<String, String> actionOrLabel = customButtons.stream().collect(Collectors.toMap(ActionButton::getAction, ActionButton::getLabel));
            String saveLabel = actionOrLabel.get(BPMConstants.MetadataKey.SAVE_CODE);
            if (Strings.isNullOrEmpty(saveLabel)) {
                saveLabel = BPMI18N.PAAS_FLOW_BPM_BUTTON_SAVE.text();
            }
            actionButtons.add(new ActionButton("update", saveLabel));

            String updateAndCompleteLabel = actionOrLabel.get(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE);
            if (Strings.isNullOrEmpty(updateAndCompleteLabel)) {
                updateAndCompleteLabel = BPMI18N.PAAS_FLOW_BPM_BUTTON_SAVE_AND_COMPLETE.text();
            }
            actionButtons.add(new ActionButton("updateAndComplete", updateAndCompleteLabel));
        } else {
            actionButtons.add(new ActionButton("update", BPMI18N.PAAS_FLOW_BPM_BUTTON_SAVE.text()));
            actionButtons.add(new ActionButton("updateAndComplete", BPMI18N.PAAS_FLOW_BPM_BUTTON_SAVE_AND_COMPLETE.text()));
        }

        component.put(buttons, actionButtons);
        component.put(BPMConstants.MetadataKey.apiName, "edit_component");
        component.put(BPMConstants.MetadataKey.type, "form");
//        普通字段区域
        List<FormField> fieldSection = Lists.newArrayList();
        component.put("field_section", fieldSection);

        List<Map<String, Object>> fields = Lists.newArrayList();
        FormField fieldForm = new FormField("field_form", BPMI18N.PAAS_FLOW_BPM_BASIC_INFORMATION.text(), fields,"");

        //组 field
        List<Map<String, Object>> groupFieldFormList = Lists.newArrayList();
        FormField groupFieldForm = new FormField("area_location", PAAS_FLOW_BPM_AREA_LOCATION.text(), groupFieldFormList,PAAS_FLOW_BPM_AREA_LOCATION.text());

        forms.forEach(formSet -> formSet.stream().filter(Objects::nonNull).forEach(temp -> {
                    String fieldApiName=(String)temp.get(BPMConstants.MetadataKey.name);
                    Map fieldDesc = (Map)desc.get(fieldApiName);
                    if(MapUtils.isNotEmpty(fieldDesc)){
                        temp.put(BPMConstants.MetadataKey.type,fieldDesc.get(BPMConstants.MetadataKey.type));
                    }
                    if (BPMConstants.MetadataKey.accountObjApiName.equals(entityId)) {
                        String type = String.valueOf(temp.get(WorkflowKey.ActivityKey.ExtensionKey.type));

                        //如果是国家省市区,设置第二个list
                        if (typeCountry.contains(type)) {
                            boolean required = Boolean.parseBoolean(String.valueOf(temp.get(WorkflowKey.ActivityKey.ExtensionKey.required)));
                            Map<String, Object> accountObjField = JsonUtil.fromJson(
                                    String.format(field, temp.get(WorkflowKey.ActivityKey.ExtensionKey.name), temp.get(WorkflowKey.ActivityKey.ExtensionKey.readonly),
                                            temp.get(WorkflowKey.ActivityKey.ExtensionKey.type), required), Map.class);
                            if (MapUtils.isNotEmpty(accountObjField)) {
                                groupFieldFormList.add(accountObjField);
                            }
                        } else {
                            boolean required = Boolean.parseBoolean(String.valueOf(temp.get(WorkflowKey.ActivityKey.ExtensionKey.required)));
                            Map<String, Object> accountObjField = JsonUtil.fromJson(
                                    String.format(field, temp.get(WorkflowKey.ActivityKey.ExtensionKey.name), temp.get(WorkflowKey.ActivityKey.ExtensionKey.readonly),
                                            temp.get(WorkflowKey.ActivityKey.ExtensionKey.type), required), Map.class);
                            if (MapUtils.isNotEmpty(accountObjField)) {
                                fields.add(accountObjField);
                            }
                        }
                    } else {
                        fields.add(getFormField(temp));
                    }
                })
        );
        if (CollectionUtils.isNotEmpty(fields)) {
            fieldSection.add(fieldForm);
        }

        if (CollectionUtils.isNotEmpty(groupFieldFormList)) {
            fieldSection.add(groupFieldForm);
        }

        //将组上使用的字段中 不展示的字段隐藏
        if (MapUtils.isNotEmpty(groupFields)) {
            List emptyGroup = Lists.newArrayList();
            groupFields.forEach((key, value) -> {
                if (CollectionUtils.isNotEmpty(value)) {
                    List removeChildList = Lists.newArrayList();
                    for (Map<String, Object> stringObjectMap : value) {
                        boolean display = GroupTypeSortHelper.childDisplay((String) key.get(group_type), (String) stringObjectMap.get("group_render_type"),taskParams);
                        if (!display) {
                            removeChildList.add(stringObjectMap);
                        }
                    }
                    value.removeAll(removeChildList);
                    if (value.size() == 0) {
                        emptyGroup.add(key);
                    }
                }
            });
            groupFields.keySet().removeAll(emptyGroup);
        }
//        组字段区域
        List<FormField> groupFieldForms = getGroupFieldFormFields(groupFields);
        if (CollectionUtils.isNotEmpty(groupFieldForms)) {
            fieldSection.addAll(groupFieldForms);
        }

        return layout;
    }


    private List<FormField> getGroupFieldFormFields(Map<Map<String, Object>, List<Map<String, Object>>> groupFields) {
        List<FormField> groupFieldForms = Lists.newArrayList();
        if (MapUtils.isNotEmpty(groupFields)) {
            groupFields.forEach((group, childs) -> {
                childs = GroupTypeSortHelper.sortGroupField(group, childs);
                groupFieldForms.add(
                        new FormField(
                                String.valueOf(group.get(WorkflowKey.ActivityKey.ExtensionKey.name)),
                                String.valueOf(group.get(WorkflowKey.ActivityKey.ExtensionKey.label)),
                                childs.stream().map(item -> getFormField(item)).collect(Collectors.toList()),
                                "",
                                (Boolean) group.get(WorkflowKey.ActivityKey.ExtensionKey.signin),
                                (Boolean) group.get(WorkflowKey.ActivityKey.ExtensionKey.signout))
                        );
            });
        }
        return groupFieldForms;
    }

    private Map<String, Object> getFormField(Map<String, Object> temp) {
        boolean required = Boolean.parseBoolean(String.valueOf(temp.get(WorkflowKey.ActivityKey.ExtensionKey.required)));
        return JsonUtil.fromJson(
                String.format(field, temp.get(WorkflowKey.ActivityKey.ExtensionKey.name), temp.get(WorkflowKey.ActivityKey.ExtensionKey.readonly),
                        temp.get(WorkflowKey.ActivityKey.ExtensionKey.type), required), Map.class);
    }


    private Map<Map<String, Object>, List<Map<String, Object>>> getGroups(List<List<Map<String, Object>>> forms) {
        Map<String, Map<String, Object>> fieldMaps = Maps.newHashMap();
        for (List<Map<String, Object>> formFields : forms) {
            for (Map<String, Object> formField : formFields) {
                if (MapUtils.isNotEmpty(formField)) {
                    fieldMaps.put(String.valueOf(formField.get(WorkflowKey.ActivityKey.ExtensionKey.name)), formField);
                }
            }
        }

        Map<Map<String, Object>, List<Map<String, Object>>> groups = Maps.newHashMap();
        for (List<Map<String, Object>> formFields : forms) {
            for (Map<String, Object> formField : formFields) {
                if (MapUtils.isNotEmpty(formField) && formField.get(BPMConstants.MetadataKey.type).equals(BPMConstants.MetadataKey.group)) {
                    List<Map<String, Object>> childs = Lists.newArrayList();
                    Map<String, Object> groupField = fieldMaps.remove(formField.get(WorkflowKey.ActivityKey.ExtensionKey.name));
                    groups.put(groupField, childs);
                    Map<String, String> subFields = (Map<String, String>) formField.get(BPMConstants.MetadataKey.fields);
                    if (MapUtils.isNotEmpty(subFields)) {
                        subFields.forEach((key, value) -> {
                            Map<String, Object> temp = fieldMaps.remove(value);
                            if (MapUtils.isNotEmpty(temp)) {
                                temp.put("group_render_type", key);
                                childs.add(temp);
                            }
                        });
                    }
                }
            }
        }
        if (MapUtils.isNotEmpty(groups)) {
            groups.forEach((key, value) -> {
                for (List<Map<String, Object>> form : forms) {
                    form.remove(key);
                    if (CollectionUtils.isNotEmpty(value)) {
                        List removeChildList = Lists.newArrayList();
                        for (Map<String, Object> stringObjectMap : value) {
                            form.remove(stringObjectMap);
                        }
                        value.removeAll(removeChildList);
                    }
                }
            });
        }
        return groups;
    }

    @Override
    public FormAssembleType getTaskType() {
        return FormAssembleType.layout;
    }

}
