package com.facishare.bpm.handler.task.form.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.form.UserFormToMetadataManager;
import com.facishare.bpm.handler.task.form.model.FormAssembleType;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GenerateDescribeExtManagerImpl implements UserFormToMetadataManager {
    @Override
    public Map<String, Object> execute(RefServiceManager serviceManager, String entityId, String objectId, List<List<Map<String, Object>>> forms, TaskParams taskParams) {
        return serviceManager.findDescribeExtra(entityId, FindDescribeExtra.FindDescribeExtraType.DescribeLayout);
    }

    @Override
    public FormAssembleType getTaskType() {
        return FormAssembleType.describeExt;
    }
}
