package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.BaseTask;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.util.MetadataUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 签到
 */
@Slf4j
@Component
public class OperationTaskSignInHandler extends OperationTaskDefaultHandler {

    @Autowired
    private GetTaskFormManager getTaskFormManager;


    @Override
    public FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        FormButtonResult result = new FormButtonResult();
        Map matadataDescribes = serviceManager.findDescribe(standardData.getEntityId(), true, false);
        //判断签到组件是否存在或已开启
        Map signInDescribe = MetadataUtils.getAppointFieldDescribeByFieldCondition(matadataDescribes, BPMConstants.MetadataKey.group_type, BPMConstants.MetadataKey.SIGN_IN);
        if (MapUtils.isEmpty(signInDescribe) || Boolean.FALSE.equals(signInDescribe.get(BPMConstants.MetadataKey.isActive))) {
            //签到组件删除或已禁用  提示
            result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_NOT_EXIST_SIGN_IN.text());
            return result;
        }
        Map matadataData = serviceManager.findDataById(standardData.getEntityId(), standardData.getObjectId());
        boolean isSignIn = MetadataUtils.objectIsSignIn(signInDescribe, matadataData);
        //按钮和提示信息下发规则  http://wiki.firstshare.cn/pages/viewpage.action?pageId=172146380
        if (taskParams.isMobile()) {
            if (isSignIn) {
                result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
            } else {
                result.addButton(BPMConstants.Button.signin.name(), BPMConstants.Button.signin.getI18NDesc());
            }
        } else {
            //web
            if (isSignIn) {
                result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
            } else {
                result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_PLEASE_TO_MOBILE_SIGN_IN.text());
            }
        }
        return result;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        setTask(serviceManager,standardData,taskParams,taskDetail,task);
        taskDetail.setData(standardData);
        return taskDetail;
    }

    @Override
    public LaneTask getLaneTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        setTask(serviceManager,standardData,taskParams,laneTask,task);
        laneTask.setData(standardData);
        return laneTask;
    }

    @Override
    public String getType() {
        return BPMConstants.OperationCodeType.signin.name();
    }

    public void setTask(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams, BaseTask baseTask,Task paasTask) {
        //是签到/签退 添加相关data及标志位
        Map<String, Object> entityDescribe = serviceManager.findDescribe(baseTask.getEntityId(), true, false);
        //获取签到组件描述
        Map<String, Object> signFieldDescribe = MetadataUtils.getAppointFieldDescribeByFieldCondition(entityDescribe, BPMConstants.MetadataKey.group_type, BPMConstants.MetadataKey.SIGN_IN);
        //下发全描述
        standardData.setDescribe(entityDescribe);
        //组件删除或禁用
        if (MapUtils.isEmpty(signFieldDescribe) || Boolean.FALSE.equals(signFieldDescribe.get(BPMConstants.MetadataKey.isActive))) {
            baseTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_NOT_EXIST_SIGN_IN.text());
            return;
        }
        if (MapUtils.isNotEmpty(signFieldDescribe)) {
            //存在签到组件
            StandardData.SignInInfoApiName signInInfoApiName = standardData.createSignInInfoApiNameBySignInDescribe(signFieldDescribe);
            Map<String, Object> data = serviceManager.findDataById(baseTask.getEntityId(), baseTask.getObjectId());
            standardData.setData(data);
            //下发layout，他人处理不需要下发layout
            if (baseTask.getIsOwner()) {
                Map<String, Object> extension = paasTask.getBpmExtension();
                //需要构建form，bpmExtension中添加，签到组件、签到地点、签到时间、签退地点、签退时间
                extension.put(WorkflowKey.ActivityKey.ExtensionKey.form, OperationTaskSignInHandler.getEntitySignInForm(entityDescribe, signFieldDescribe, signInInfoApiName, BPMConstants.OperationCodeType.signin));
                try {
                    TaskHelper.setTaskForm(serviceManager, paasTask.getCompleted(), extension, baseTask.getEntityId(), baseTask.getObjectId());
                    standardData.initCustomerData(getTaskFormManager.getData(serviceManager, baseTask.getEntityId(), baseTask.getObjectId(), extension, taskParams));
                } catch (Exception e) {
                    log.warn("", e);
                }
            }
            if (!paasTask.isInprogress() && Objects.isNull(data.get(signInInfoApiName.getSignInPlace())) && !TaskState.suspend.equals(paasTask.getState())) {
                //结束的任务，签到字段没有签到信息 || 签退字段没有签退信息  下发错误信息
                baseTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_SIGNED_IN_GROUP_DELETED.text());
            }
        }
        baseTask.setData(standardData);
        setButtons(serviceManager, standardData, paasTask.needAssignNextTask(), taskParams, baseTask);
    }

    /**
     * 构建签到组件的layout
     *
     * @param entityDescribe
     * @param signInInfoApiName
     * @param operationCodeType
     * @return
     */
    public static List<List<Map<String, Object>>> getEntitySignInForm(Map<String, Object> entityDescribe,
                                                                      Map<String, Object> signInDescribe,
                                                                      StandardData.SignInInfoApiName signInInfoApiName,
                                                                      BPMConstants.OperationCodeType operationCodeType) {
        List<List<Map<String, Object>>> result = Lists.newArrayList();
        if (MapUtils.isEmpty(entityDescribe) || MapUtils.isEmpty(signInDescribe) || Objects.isNull(signInInfoApiName) || Objects.isNull(operationCodeType)) {
            return result;
        }
        //[[{基础字段}],[{组件字段}]]
        result.add(Lists.newArrayList());
        List<Map<String, Object>> signInfo = Lists.newArrayList();
        //构建签到group信息
        signInDescribe.put("name", signInDescribe.get("api_name"));
        signInDescribe.put("required", signInDescribe.get("is_required"));
        Map<String, Object> fields = (Map<String, Object>) entityDescribe.get("fields");
        for (String apiName : signInInfoApiName.getApiNameListByActionType(operationCodeType)) {
            Map<String, Object> apiDescribe = (Map<String, Object>) fields.get(apiName);
            apiDescribe.put("name", apiName);
            apiDescribe.put("required", apiDescribe.get("is_required"));
            signInfo.add(apiDescribe);
        }
        signInfo.add(signInDescribe);
        result.add(signInfo);
        return result;
    }

}