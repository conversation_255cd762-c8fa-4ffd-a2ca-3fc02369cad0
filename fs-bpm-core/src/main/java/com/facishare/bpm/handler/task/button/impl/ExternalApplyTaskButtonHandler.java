package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo;
import com.facishare.paas.I18N;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExternalApplyTaskButtonHandler implements TaskButtonHandler {


    /**
     * 显示去处理按钮
     * 下发按钮的时候 会调用深研接口(appcode,actionCode)  深研返回(//TODO 看一下)  按钮是否下发通过深研接口来判断
     * <p>
     * web端拿到按钮后,再去执行具体的事
     * <p>
     *
     * @param serviceManager
     * @param standardData
     * @param assignNextTask
     * @return
     */
    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        GetBpmSupportInfo.Result actionCodeSupportInfo = serviceManager.getActionCodeSupportInfo(standardData.getAppCode(), standardData.getActionCode(), taskParams, standardData.getEntityId(), standardData.getObjectId(), standardData.getTaskId());
        String url = actionCodeSupportInfo.getUrl();
        log.info("support:{},url:{}", actionCodeSupportInfo.isSupport(), url);
        List<ActionButton> actionButtons = Lists.newArrayList();
        if (actionCodeSupportInfo.isSupport()) {
            //780 新增 应用节点多按钮
            if (taskParams.isApplyButtons()) {
                List<GetBpmSupportInfo.Result> buttonList = actionCodeSupportInfo.getButtonList();
                if (CollectionUtils.isNotEmpty(buttonList)) {
                    /*
                    action: "todo"
                    code: "externalApplyTask"
                    label: "工单指派"
                     */
                    //TODO 通知终端(文浩)  不下发joiner
                    //String action, String label,String code, String todoJumpUrl,String event
                    actionButtons = buttonList.stream().map(result ->
                            new ActionButton(result.getEventParam(), result.getLabel(), getTaskType().name(), result.getUrl(), result.getEventParam(),result.getBtnClass())).collect(Collectors.toList());
                }
            } else {
                //手机端url为空的时候 不下发按钮;web端直接下发按钮
                List<ActionButton> buttons = TaskButtonConfigHelper.getTaskFormButtons(getTaskType(), standardData.getOnlyRelatedObject());
                if (!Strings.isNullOrEmpty(url)) {
                    standardData.setTodoJumpUrl(url);
                    standardData.setJoiner(url.contains("?") ? "&" : "?");
                }
                actionButtons = buttons.stream().peek(actionButton -> {
                    if (!Strings.isNullOrEmpty(actionCodeSupportInfo.getLabel())) {
                        actionButton.setLabel(actionCodeSupportInfo.getLabel());
                    } else {
                        actionButton.setLabel(I18N.text(actionButton.getLabel()));
                    }
                }).collect(Collectors.toList());
            }
        }
        return new FormButtonResult(actionButtons, actionCodeSupportInfo.getPrompt());
    }


    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.externalApplyTask;
    }
}
