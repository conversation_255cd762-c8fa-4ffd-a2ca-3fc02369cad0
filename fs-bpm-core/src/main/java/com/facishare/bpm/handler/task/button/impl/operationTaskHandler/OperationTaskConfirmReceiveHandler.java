package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import org.springframework.stereotype.Component;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.Button.confirmreceive;

/**
 * 确认收货
 */
@Component
public class OperationTaskConfirmReceiveHandler extends OperationTaskDefaultHandler {

    @Override
    public FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        String actionCode = standardData.getActionCode();
        FormButtonResult result = new FormButtonResult();
        //actionCodeAndLabels 过滤后为空 && 销售订单已转换 的话 需要完成任务
        if (standardData.getObjectPermissions().get(actionCode)) {
            String i18nLabel = getLabel();
            if (serviceManager.getDeliveryNoteEnable()) {
                //判断是否开通了发货单  开通了发货单，只下发 完成 按钮
                result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_INVOICE_HAS_OPEN.text(i18nLabel));
                result.addButton(
                        BPMConstants.Button.Complete.name(),
                        BPMConstants.Button.Complete.getI18NDesc());
            } else if(BPMConstants.OperationCodeType.confirmdelivery.name().equals(actionCode) && "5".equals(serviceManager.findDataById(standardData.getEntityId(), standardData.getObjectId()).get(BPMConstants.MetadataKey.logistics_status))){
                //是确认发货，并且发货状态是 已收货 下发完成按钮和提示信息
                result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_SALES_ORDER_OBJ_RECEIVED.text());
                result.addButton(
                        BPMConstants.Button.Complete.name(),
                        BPMConstants.Button.Complete.getI18NDesc());
            } else if(BPMConstants.OperationCodeType.confirmreceive.name().equals(actionCode) && "5".equals(serviceManager.findDataById(standardData.getEntityId(), standardData.getObjectId()).get(BPMConstants.MetadataKey.logistics_status))){
                //是确认收货，并且发货状态是 已收货 下发完成按钮和提示信息
                result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_SALES_ORDER_OBJ_REPEAT_RECEIVED.text());
                result.addButton(
                        BPMConstants.Button.Complete.name(),
                        BPMConstants.Button.Complete.getI18NDesc());
            }else if(BPMConstants.OperationCodeType.confirmreceive.name().equals(actionCode) && "1".equals(serviceManager.findDataById(standardData.getEntityId(), standardData.getObjectId()).get(BPMConstants.MetadataKey.logistics_status))){
                //是确认收货，并且发货状态是 未发货 下发提示信息
                result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_SALES_ORDER_OBJ_NOT_RECEIVED.text());
            }else {
                result.addButton(actionCode, i18nLabel);
            }
        } else {
            result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_NOT_AUTH_COMPLETE.text());
        }
        return result;
    }

    @Override
    public String getType() {
        return BPMConstants.OperationCodeType.confirmreceive.name();
    }

    public String getLabel() {
        return confirmreceive.getI18NDesc();
    }

}