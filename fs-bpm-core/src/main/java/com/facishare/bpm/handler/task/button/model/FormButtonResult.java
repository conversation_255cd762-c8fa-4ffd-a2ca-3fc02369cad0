package com.facishare.bpm.handler.task.button.model;

import com.facishare.bpm.model.ActionButton;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/28 8:30 PM
 */
@Data
public class FormButtonResult {
    private List<ActionButton> buttons = Lists.newArrayList();
    private String errorMsg;
    private Boolean custom=false;
    private Map customExtension;
    private FlowElementPluginWrapper plugin;

    public FormButtonResult(){}

    public FormButtonResult(List<ActionButton> buttons) {
        setButtons(buttons);
    }


    public FormButtonResult(List<ActionButton> buttons, String errorMsg) {
        setButtons(buttons);
        this.errorMsg = errorMsg;
    }


    public List<ActionButton> getButtons() {
        return buttons == null ? Lists.newArrayList() : buttons;
    }

    public FormButtonResult(String errorMsg) {
        this.errorMsg = errorMsg;
    }
    public void addButton(String code,String label) {
        this.buttons.add(new ActionButton(code,label, this.buttons.size()));
    }

    public void setButtons(List<ActionButton> buttons){
        if(CollectionUtils.isEmpty(buttons)){
            if(Objects.isNull(this.buttons)){
                this.buttons = Lists.newArrayList();
            }
            return;
        }
        AtomicInteger count = new AtomicInteger(0);
        for (ActionButton button : buttons) {
            button.setOrder(count.getAndIncrement());
        }
        this.buttons = buttons;
    }

}
