package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.paas.I18N;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/19 12:00 PM
 */
@Slf4j
@Service
public class BatchEditMasterDetailButtonHandler implements TaskButtonHandler {

    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        List<ActionButton> buttons = TaskButtonConfigHelper.getTaskFormButtons(getTaskType(), standardData.getOnlyRelatedObject());
        buttons.forEach(item -> item.setLabel(I18N.text(item.getLabel())));
        return new FormButtonResult(TaskButtonHandler.getCustomButtons(getTaskType(), standardData.getDefaultButtons(), buttons));
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.batchEditMasterDetailObject;
    }


}
