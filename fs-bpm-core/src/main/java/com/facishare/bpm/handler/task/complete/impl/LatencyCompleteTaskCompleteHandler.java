package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.complete.TaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.TaskUtils;
import com.facishare.bpm.utils.model.Pair;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 5:05 PM
 */
@Slf4j
@Service
public class LatencyCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements TaskCompleteHandler {
    @Override
    public CompleteTask.Result execute(RefServiceManager serviceManager,
                                       Task task, String opinion,
                                       Map<String, Object> completedData,
                                       Integer addOrReplaceNextTaskAssignee,
                                       Map<String, Object> nextTaskAssignee,
                                       DataCacheHandler dataCacheHandler,
                                       boolean neetValidateNextTaskAssignee, Boolean ignoreNoBlockValidate) throws Exception {
        TaskUtils.assertTaskCompleted(task);
        String objectId = task.getObjectId();
        String entityId = task.getEntityId();
        String activityId = task.getActivityId();
        if (Strings.isNullOrEmpty(opinion)) {
            opinion = "";
        }
        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId));

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager, task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKey,
                dataCacheHandler);
       return paasWorkflowServiceProxy.completeLatencyTask(serviceManager.getContext(), task.getId(), variables, opinion);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.latency;
    }
}
