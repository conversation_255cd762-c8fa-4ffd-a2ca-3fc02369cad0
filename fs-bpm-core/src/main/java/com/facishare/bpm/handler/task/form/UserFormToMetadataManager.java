package com.facishare.bpm.handler.task.form;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.form.model.FormAssembleType;
import com.facishare.bpm.model.TaskParams;

import java.util.List;
import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 7:58 PM
 */
public interface UserFormToMetadataManager {

    Map<String, Object> execute(RefServiceManager serviceManager, String entityId, String objectId, List<List<Map<String, Object>>> forms, TaskParams taskParams);

    FormAssembleType getTaskType();
}
