package com.facishare.bpm.handler.task.button;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ExecutionTaskButtonHandler;
import com.facishare.bpm.handler.task.button.impl.ExternalApplyTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.DefaultActionLabel;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.flow.mongo.bizdb.model.FlowConfigTerminal;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_EDIT_FORM_IS_EMPTY;

/**
 * desc:TODO 将按钮进行统一,放到同一个包下
 * version: 6.6
 * Created by cuiyongxu on 2019/4/19 4:28 PM
 */
public interface TaskButtonHandler extends UserTaskButtonHandler {

    /**
     * 复合button
     */
    List<ExecutionTypeEnum> combinedExecutionTypes = Lists.newArrayList(
            ExecutionTypeEnum.update,
            ExecutionTypeEnum.approve,
            ExecutionTypeEnum.batchAddRelatedObject,
            ExecutionTypeEnum.batchEditMasterDetailObject,
            ExecutionTypeEnum.operation,
            ExecutionTypeEnum.updateLookup);

    @Override
    FormButtonResult setButtons(RefServiceManager serviceManager,
                                StandardData standardData,
                                boolean assignNextTask,
                                TaskParams taskParams);


    /**
     * {
     * "defaultButtons":{
     * "changeowner":{
     * "label":"变更负责人"
     * },
     * "addteammember":null,
     * "addteammember":{
     * "label":""
     * }
     * "update":{
     * "label":"更新订单对象的值"
     * }
     * },
     * <p>
     * <p>
     * <p>
     * {
     * "defaultButtons":{
     * "agree":{
     * "label":"同意了"
     * },
     * "reject":{
     * "label":"不同意"
     * }
     * }
     *
     * @param executionTypeEnum
     * @param customButtons
     * @param buttons
     * @return
     */
    static List<ActionButton> getCustomButtons(ExecutionTypeEnum executionTypeEnum, Map<String, DefaultActionLabel> customButtons, List<ActionButton> buttons) {

        //获取默认buttons
        if (MapUtils.isEmpty(customButtons)) {
            return buttons;
        }
        //统一转小写
        Map<String, DefaultActionLabel> lowerCaseCustomButton = customButtons.keySet().stream().collect(Collectors.toMap(String::toLowerCase, customButtons::get));
        //通过actionCode/executionType 获取默认按钮
        return buttons.stream().peek(button -> {
            //batchAddRelatedObject
            String code = executionTypeEnum.name().toLowerCase();
            if (combinedExecutionTypes.contains(executionTypeEnum)) {
                code = button.getAction().toLowerCase();
            }
            DefaultActionLabel customActionLabel = lowerCaseCustomButton.get(code);

            if (Objects.nonNull(customActionLabel) && StringUtils.isNoneEmpty(customActionLabel.getLabel())) {
                button.setI18nConvert(false);
                button.setLabel(customActionLabel.getLabel());
            }
        }).collect(Collectors.toList());
    }

    @Override
    default FormButtonResult setButtons(RefServiceManager serviceManager,
                                        StandardData standardData,
                                        boolean assignNextTask,
                                        TaskState state,
                                        List<String> candidateIds,
                                        List<String> assigneeIds,
                                        AfterActionExecution.SimpleAfterActionExecution execution,
                                        TaskParams taskParams) {

        if (Boolean.TRUE.equals(standardData.getSequence())){
            candidateIds = standardData.getSequenceTaskCandidateIds();
        }
        if (starting(state)) {
            //是应用节点 ||(是处理人&& 不在已处理人中)
            boolean isTaskOwner = isTaskOwner(serviceManager, candidateIds);
            if ((this instanceof ExternalApplyTaskButtonHandler)
                    || (this instanceof ExecutionTaskButtonHandler)
                    || (isTaskOwner && !hasProcessed(serviceManager, assigneeIds))) {
                //没有后动作 || 不是后动作异常
                if (Objects.isNull(execution) || !execution.isError()) {
                    FormButtonResult btnResult = setButtons(serviceManager, standardData, assignNextTask, taskParams);
                    hideButton(btnResult, standardData);
                    addSuspendBtn(serviceManager, isTaskOwner, btnResult, standardData.isAfterActionWaiting());
                    return btnResult;
                }
            }
        }else if(suspending(state)){
            boolean isTaskOwner = isTaskOwner(serviceManager, candidateIds);
            if ((this instanceof ExternalApplyTaskButtonHandler)
                    || (this instanceof ExecutionTaskButtonHandler)
                    || serviceManager.isAdmin()
                    || (isTaskOwner && !hasProcessed(serviceManager, assigneeIds))) {
                ActionButton btn = new ActionButton(BPMConstants.RESUME_OPERATE, BPMI18N.PAAS_FLOW_BPM_BUTTON_RESUME.text(), 0);
                btn.setBtnClass("primary");
                return new FormButtonResult(Lists.newArrayList(btn));
            }
            return new FormButtonResult();
        }
        return new FormButtonResult();
    }


    ExecutionTypeEnum getTaskType();


    static boolean starting(TaskState state) {
        return state.equals(TaskState.in_progress) || state.equals(TaskState.error);
    }

    static boolean suspending(TaskState state) {
        return state.equals(TaskState.suspend);
    }

    static boolean isTaskOwner(RefServiceManager serviceManager, List<String> candidateIds) {
        //若是下游企业调用，则userId用下游企业的人员
        return !CollectionUtils.isEmpty(candidateIds) && candidateIds.contains(serviceManager.getUserIdWithOuterUserId());
    }


    static boolean hasProcessed(RefServiceManager serviceManager, List<String> assigneeIds) {
        return !CollectionUtils.isEmpty(assigneeIds) && assigneeIds.contains(serviceManager.getUserIdWithOuterUserId());
    }

    default void hideButton(FormButtonResult btnResult, StandardData standardData){
        if(Objects.isNull(btnResult) || Objects.isNull(standardData) || CollectionUtils.isEmpty(btnResult.getButtons())){
            return;
        }
        if(Boolean.TRUE.equals(standardData.getHideCompleteBtn())){
            btnResult.getButtons().removeIf(btn -> BPMConstants.MetadataKey.COMPLETE_CODE.equals(btn.getAction()));
            if(CollectionUtils.isEmpty(btnResult.getButtons())){
                btnResult.setErrorMsg(PAAS_FLOW_BPM_EDIT_FORM_IS_EMPTY.text());
            }
        }
    }

    default void addSuspendBtn(RefServiceManager serviceManager, boolean isTaskOwner, FormButtonResult btnResult, boolean isAfterActionWaiting){
        //如果应用节点没有下发按钮 则不下发 暂不处理 按钮  深研
        if(this instanceof ExternalApplyTaskButtonHandler && CollectionUtils.isEmpty(btnResult.getButtons())){
            return;
        }
        Map<String, Object> config = serviceManager.getFlowConfig(FlowConfigTerminal.ALL.name(), Lists.newArrayList(BPMConstants.SUSPEND_OPERATE_CONFIG));
        if(MapUtils.isNotEmpty(config) && config.containsKey(BPMConstants.SUSPEND_OPERATE_CONFIG) && Boolean.FALSE.equals(config.get(BPMConstants.SUSPEND_OPERATE_CONFIG))){
            return;
        }
        if(isTaskOwner && !isAfterActionWaiting){
            btnResult.addButton(BPMConstants.SUSPEND_OPERATE,BPMI18N.PAAS_FLOW_BPM_BUTTON_SUSPEND.text());
        }
    }


}
