package com.facishare.bpm.handler.task.data;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMTaskReferObjectNotFoundException;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTaskDataEntity;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND;

/**
 * <AUTHOR>
 * @creat_date: 2020/6/23
 * @creat_time: 18:37
 * @since 7.2.0
 */
@Component
public class TaskDataOfBatchAddRelatedObjectHandler extends TaskDataHandler<Map<String, String>, Map<String, List<TaskDataOfBatchAddRelatedObjectHandler.TaskDataOfBatchAddRelatedObjectVO>>> {

  @Override
  public void saveData(RefServiceManager serviceManager,String taskId, String activityId, Integer activityInstanceId, Map<String, String> taskData) {
    String entityId = taskData.get(WorkflowKey.entityId);
    String objectId = taskData.get(WorkflowKey.ActivityKey.ExtensionKey.objectId);
    if(StringUtils.isBlank(entityId)||StringUtils.isBlank(objectId)){
      throw new BPMTaskReferObjectNotFoundException(PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND);
    }
    TaskDataOfBatchAddRelatedObjectVO batchAddRelatedObject = new TaskDataOfBatchAddRelatedObjectVO();
        batchAddRelatedObject.setEntityId(entityId);
        batchAddRelatedObject.setObjectId(objectId);
        batchAddRelatedObject.setUserId(serviceManager.getUserId());
        batchAddRelatedObject.setTimestamp(System.currentTimeMillis());
        WorkflowTaskDataEntity data = taskDataDao.find(serviceManager.getTenantId(), taskId);
        List<Object> datas = Lists.newArrayList(batchAddRelatedObject);
        if (data != null&&data.getData()!=null) {
          datas.addAll((List) data.getData());
        }
        taskDataDao.snapshotTaskData(serviceManager.getContext(), taskId, activityId, activityInstanceId, JacksonUtil.fromJson(JacksonUtil.toJson(datas), new TypeReference<List<Map>>() {
        }));
  }

  @Override
  public Map<String, List<TaskDataOfBatchAddRelatedObjectVO>> getData(RefServiceManager serviceManager,String taskId) {
    Map<String, List<TaskDataOfBatchAddRelatedObjectVO>> rst = Maps.newHashMap();
    WorkflowTaskDataEntity taskData = taskDataDao.find(serviceManager.getTenantId(), taskId);
    if (Objects.nonNull(taskData)&&Objects.nonNull(taskData.getData())) {

      List<TaskDataOfBatchAddRelatedObjectVO> datas = JacksonUtil.fromJson(JacksonUtil.toJson(taskData.getData()),
        new TypeReference<List<TaskDataOfBatchAddRelatedObjectVO>>() {
        }).stream().filter(item-> StringUtils.isNotBlank(item.getEntityId())&&StringUtils.isNotBlank(item.getObjectId())).collect(Collectors.toList());

      if(CollectionUtils.isEmpty(datas)){
        return rst;
      }
      /**
       * 获取对象的apiName;
       */
      String entityId=datas.get(0).getEntityId();
      Map<String, String> objectIdAndNames = serviceManager.getObjectNameAndDisplayName(entityId,datas.stream().map(item->item.getObjectId()).collect(Collectors.toList()));

      datas.stream().peek(item -> item.setName(objectIdAndNames.get(item.getObjectId()))).forEach(item -> {
        List<TaskDataOfBatchAddRelatedObjectVO> old = rst.get(item.getUserId());
        if (Objects.nonNull(old)) {
          old.add(item);
        } else {
          rst.put(item.getUserId(),Lists.newArrayList(item));
        }
      });
    }
    return rst;
  }

  @Override
  public ExecutionTypeEnum getTaskType() {
    return ExecutionTypeEnum.batchAddRelatedObject;
  }
  @Data
  public static class TaskDataOfBatchAddRelatedObjectVO{
    private String entityId;
    private String name;
    private String objectId;
    private String userId;
    private Long timestamp;
  }
}
