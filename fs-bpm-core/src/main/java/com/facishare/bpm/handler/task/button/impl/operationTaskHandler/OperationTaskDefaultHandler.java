package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.OperationTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.BaseTask;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 通用操作
 */
@Slf4j
@Component
public class OperationTaskDefaultHandler implements IOperationTaskHandler {

    @Autowired
    private OperationTaskButtonHandler operationTaskButtonHandler;


    @Override
    public FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        FormButtonResult result = new FormButtonResult();
        Map<String, String> actionCodeAndLabels = filterActionByFunction(standardData.createActionCodeAndLabels(), standardData.getObjectPermissions());
        actionCodeAndLabels.forEach((code, label) -> result.addButton(code, label));
        return result;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        taskDetail.setData(standardData);
        setButtons(serviceManager, standardData,task.needAssignNextTask(), taskParams, taskDetail);
        return taskDetail;
    }

    @Override
    public LaneTask getLaneTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        laneTask.setData(standardData);
        setButtons(serviceManager, standardData, task.needAssignNextTask(), taskParams, laneTask);
        return laneTask;
    }

    /**
     * 拼装给端上 标准数据结构
     *
     * @param serviceManager
     * @param task
     * @param taskParams
     * @return
     */
    protected StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        Map<String, Object> bpmExtension = task.getBpmExtension();
        //todo 只有1转3需要
        boolean isCombinedActionCode = BPMConstants.MetadataKey.APINAME_LEADSOBJ.equals(bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.entityId))
                && BPMConstants.MetadataKey.leadsObjHandleActionCode.equals(bpmExtension.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode));
        standardData.setCombinedActionCode(isCombinedActionCode);
        standardData.setDescribe(serviceManager.getDescribe(task.getEntityId()));
        setActionCodeAndActionLabel(serviceManager, task, standardData);
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        //功能权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        return standardData;
    }

    /**
     * 设置 操作的actionCode 及 actionLabel
     * @param serviceManager
     * @param paasTask
     * @param standardData
     */
    public void setActionCodeAndActionLabel(RefServiceManager serviceManager, Task paasTask, StandardData standardData) {
        standardData.setActionCode(paasTask.getActionCode());
        standardData.setActionLabel(serviceManager.getActionNameByActionCode(paasTask.getEntityId(), paasTask.getActionCode()));
    }

    protected void setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams,BaseTask baseTask){
        FormButtonResult formButtonResult = operationTaskButtonHandler.setButtons(serviceManager, standardData, assignNextTask, baseTask.getState(), baseTask.getCandidateIds(), baseTask.getProcessIds(), baseTask.getExecution(), taskParams);
        if (StringUtils.isBlank(baseTask.getErrorMsg())) {
            baseTask.setErrorMsg(formButtonResult.getErrorMsg());
        }
        baseTask.setButton(formButtonResult.getButtons().stream().collect(Maps::newLinkedHashMap, (m, button) -> m.put(button.getAction(), button), Map::putAll));
    }

    @Override
    public String getType() {
        return defaultHandlerType;
    }

}