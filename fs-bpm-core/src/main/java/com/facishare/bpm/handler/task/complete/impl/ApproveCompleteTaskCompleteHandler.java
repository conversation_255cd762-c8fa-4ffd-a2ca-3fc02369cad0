package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:35 PM
 */
@Slf4j
@Service
public class ApproveCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {


    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        RemoteContext context = serviceManager.getContext();

        if (completedData == null) {
            log.info("complete task error : approve completedData is null, CONTEXT={}. TASK_ID={}", context, task
                    .getId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_ABSENCE_APPROVAL_RESULT);
        }

        String taskId = task.getId();
        String objectId = task.getObjectId();
        String entityId = task.getEntityId();
        Object result = completedData.get(BPMConstants.ApproveResult.RESULT);
        String activityId = task.getActivityId();
        if (!StringUtils.isEmpty(result)) {
            if (!(CompleteTask.ActionType.AGREE.equals(result) || CompleteTask.ActionType.REJECT.equals(result))) {
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_PARAMS_ERROR);
            }
            dataCacheHandler.getVariableMap(task.getWorkflowInstanceId(), task.getWorkflowId())
                    .get(InstanceVariableManager.getActivityVariableEntityId(activityId, BPMConstants.ApproveResult.RESULT)).put("value", result);
        } else {
            log.info("complete task error : approve result is null, CONTEXT={}, TASK_ID={}", context, task
                    .getId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_ABSENCE_APPROVAL_RESULT);
        }
        //如果是同意 且 可以编辑审批内容 且 使用的是普通布局 需要校验必填
        if(CompleteTask.ActionType.AGREE.equals(result) && Boolean.TRUE.equals(task.getBpmExtension().get(BPMConstants.FORM_EDITABLE)) && !task.isDefaultLayoutAndFormNotEmpty()){
            Map<String, Object> metaData = dataCacheHandler.getData(entityId, objectId);

            UpdateCompleteTaskCompleteHandler.verifyRequiredField(serviceManager.findDescribe(entityId, false, false),
                    metaData, task.getForm());
        }
        if (Strings.isNullOrEmpty(opinion)) {
            if (CompleteTask.ActionType.AGREE.equals(result)) {
                opinion = BPMI18N.PAAS_FLOW_BPM_CONFIG_BUTTON_AGREE.text();
            } else if (CompleteTask.ActionType.REJECT.equals(result)) {
                opinion = BPMI18N.PAAS_FLOW_BPM_CONFIG_BUTTON_REJECT.text();
            } else {
                opinion = "";
            }
        }
        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId));

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager, task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKey,
                dataCacheHandler);
        reportFlowBizLog(context, task ,null);
        return paasWorkflowServiceProxy.completeTask(context, taskId, (String) result, variables, opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);

    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.approve;
    }
}
