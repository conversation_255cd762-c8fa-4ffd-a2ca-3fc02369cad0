package com.facishare.bpm.handler.task.form.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

@Data
public class CustomerData {

    @JSONField(name = "M1")
    protected Map<String, Object> data;
    @<PERSON><PERSON><PERSON>ield(name = "M2")
    protected Map<String, Object> layout;
    @J<PERSON><PERSON>ield(name = "M3")
    protected Map<String, Object> describe;
    @JSONField(name = "M4")
    protected Map<String, Object> describeExt;


    public CustomerData(Map<String, Object> data, Map<String, Object> layout, Map<String, Object> describe, Map<String, Object> describeExt) {
        this.data = data;
        this.layout = layout;
        this.describe = describe;
        this.describeExt = describeExt;
    }

    public CustomerData() {
    }
}