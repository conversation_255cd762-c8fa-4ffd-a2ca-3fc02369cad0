package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class OperationMultiCompleteTaskCompleteHandler  extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {

    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        RemoteContext context = serviceManager.getContext();

        String taskId = task.getId();
        String objectId = task.getObjectId();
        String entityId = task.getEntityId();
        String activityId = task.getActivityId();


        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId));


        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKey, dataCacheHandler);

        reportFlowBizLog(context,task,null);
        return paasWorkflowServiceProxy.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operationMulti;
    }
}
