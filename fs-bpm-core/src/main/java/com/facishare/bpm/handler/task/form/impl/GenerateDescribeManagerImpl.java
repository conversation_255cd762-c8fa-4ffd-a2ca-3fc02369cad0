package com.facishare.bpm.handler.task.form.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.form.UserFormToMetadataManager;
import com.facishare.bpm.handler.task.form.model.FormAssembleType;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 7:24 PM
 */
@Service
public class GenerateDescribeManagerImpl implements UserFormToMetadataManager {


    @Override
    public Map<String, Object> execute(RefServiceManager serviceManager, String entityId, String objectId, List<List<Map<String, Object>>> forms, TaskParams taskParams) {
        Map<String, Object> describe = Maps.newHashMap();
        describe.put(BPMConstants.MetadataKey.apiName, entityId);

        Map<String, Object> fields = Maps.newHashMap();

        Map<String, Object> fieldDescs = serviceManager.getFields(entityId);


        forms.forEach(formSet ->
                formSet.stream()
                        .filter(Objects::nonNull)
                        .forEach(field -> {
                            String fieldKey = String.valueOf(field.get(WorkflowKey.ActivityKey.ExtensionKey.name));
                            Map<String, Object> fieldValue = getFieldDescribe(field, fieldDescs.get(field.get(WorkflowKey.ActivityKey.ExtensionKey.name)));
                            fields.put(fieldKey, fieldValue);
                        }));
        fields.put(BPMConstants.MetadataKey.id, getIdField());

        if (BPMConstants.MetadataKey.accountObjApiName.equals(entityId)) {
            fields.put(BPMConstants.MetadataKey.areaLocation, getAccountObjAreaField());
        }
        // 开关控制 表单描述下发全量
        if(SwitchConfigManager.isSupportAllFormDescribe(serviceManager.getTenantId()) || SwitchConfigManager.isMergeTasksByLaneDataAndDesc(serviceManager.getTenantId()).gray()){
            for (String descKey : fieldDescs.keySet()) {
                fields.putIfAbsent(descKey,fieldDescs.get(descKey));
            }
        }

        describe.put(BPMConstants.MetadataKey.fields, fields);

        return describe;
    }

    private Map<String, Object> getFieldDescribe(Map<String, Object> formField, Object fieldDesc) {
        Map<String, Object> result = getResult();
        result.put(BPMConstants.MetadataKey.defineType, formField.getOrDefault(BPMConstants.MetadataKey.defineType, BPMConstants.MetadataKey.define_type_custom));
        result.put(BPMConstants.MetadataKey.isRequired, formField.get(WorkflowKey.ActivityKey.ExtensionKey.required));
        result.put(BPMConstants.MetadataKey.isReadonly, formField.get(WorkflowKey.ActivityKey.ExtensionKey.readonly));
        result.put(BPMConstants.MetadataKey.apiName, formField.get(WorkflowKey.ActivityKey.ExtensionKey.name));
        result.put(BPMConstants.MetadataKey.label, formField.get(WorkflowKey.ActivityKey.ExtensionKey.label));
        result.put(BPMConstants.MetadataKey.description, formField.get(WorkflowKey.ActivityKey.ExtensionKey.label));
        if (fieldDesc != null && fieldDesc instanceof Map) {
            Map<String, Object> field = (Map<String, Object>) fieldDesc;
            field.forEach(result::putIfAbsent);
        }
        return result;
    }


    private Map getResult() {
        String fieldStrTemplate = "{\n" +
                "                    \"define_type\": \"%s\",\n" +
                "                    \"is_required\": %s,\n" +
                "                    \"is_readonly\": %s,\n" +
                "                    \"api_name\": \"%s\",\n" +
                "                    \"label\": \"%s\",\n" +
                "                    \"description\": \"%s\"" +
                "        }";
        return JsonUtil.fromJson(fieldStrTemplate, Map.class);
    }


    private Map getIdField() {
        String idFieldJson = "{\n" +
                "            \"type\" : \"text\",\n" +
                "            \"define_type\" : \"system\",\n" +
                "            \"is_index\" : false,\n" +
                "            \"is_need_convert\" : false,\n" +
                "            \"max_length\" : 200,\n" +
                "            \"pattern\" : \"\",\n" +
                "            \"label\" : \"_id\",\n" +
                "            \"api_name\" : \"_id\",\n" +
                "            \"is_unique\" : false,\n" +
                "            \"description\" : \"_id\",\n" +
                "            \"status\" : \"released\",\n" +
                "            \"is_required\" : false\n" +
                "        }";

        return JsonUtil.fromJson(idFieldJson, Map.class);
    }

    private Map getAccountObjAreaField() {
        String accountObjAreaFieldJson = "{\n" +
                "                    \"type\": \"group\",\n" +
                "                    \"group_type\": \"area\",\n" +
                "                    \"define_type\": \"package\",\n" +
                "                    \"fields\": {\n" +
                "                        \"area_country\": \"country\",\n" +
                "                        \"area_province\": \"province\",\n" +
                "                        \"area_city\": \"city\",\n" +
                "                        \"area_district\": \"district\",\n" +
                "                        \"area_location\": \"location\",\n" +
                "                        \"area_detail_address\": \"address\"\n" +
                "                    },\n" +
                "                    \"is_index\": false,\n" +
                "                    \"is_need_convert\": false,\n" +
                "                    \"is_required\": false,\n" +
                "                    \"is_unique\": false,\n" +
                "                    \"api_name\": \"area_location\",\n" +
                "                    \"status\": \"released\",\n" +
                "                    \"label\": \"" + BPMI18N.PAAS_FLOW_BPM_AREA_LOCATION.text() + "\"\n" +
                "                }";

        return JsonUtil.fromJson(accountObjAreaFieldJson, HashMap.class);
    }

    @Override
    public FormAssembleType getTaskType() {
        return FormAssembleType.describe;
    }
}
