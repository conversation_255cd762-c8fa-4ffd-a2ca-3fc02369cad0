package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ExecutionTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.TaskDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.facishare.bpm.handler.task.lane.impl.CreateExecutionTaskOfLaneHandler.getExecutionTaskPassMessage;

@Slf4j
@Service
public class ExecutionGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private ExecutionTaskButtonHandler executionTaskButtonHandler;

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        //功能权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        standardData.setInstanceId(task.getWorkflowInstanceId());
        return standardData;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.execution;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = getStandardData(serviceManager,task,taskParams);
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        FormButtonResult formButtonResult = executionTaskButtonHandler.setButtons(
                serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setData(standardData);
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        taskDetail.setMessage(getExecutionTaskPassMessage(serviceManager, task));
        return taskDetail;
    }
}
