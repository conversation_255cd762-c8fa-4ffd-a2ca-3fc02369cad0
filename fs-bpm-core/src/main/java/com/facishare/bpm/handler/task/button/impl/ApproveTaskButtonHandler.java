package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.paas.I18N;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/26 1:29 PM
 */
@Service
public class ApproveTaskButtonHandler implements TaskButtonHandler {
    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        List<ActionButton> buttons = TaskButtonConfigHelper.getTaskFormButtons(getTaskType(), standardData.getOnlyRelatedObject());
        //是否需要编辑数据 且 有编辑的数据权限
        if (standardData.getFormEditable() && standardData.getObjectPermissions().get(BPMConstants.MetadataKey.EDIT)){
            buttons = filterFormEditableBtn(buttons, standardData, taskParams);
        }else {
            //只需要下发同意、不同意
            buttons = buttons.stream().filter(item ->
                    item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.AGREE)
                    || item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.REJECT)
            ).collect(Collectors.toList());
        }
        return new FormButtonResult(TaskButtonHandler.getCustomButtons(getTaskType(), standardData.getDefaultButtons(),  buttons).stream().peek(actionButton -> {
            if (actionButton.isI18nConvert()) {
                actionButton.setLabel(I18N.text(actionButton.getLabel()));
            }
        }).collect(Collectors.toList()));
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.approve;
    }

    private List<ActionButton> filterFormEditableBtn(List<ActionButton> buttons, StandardData standardData, TaskParams taskParams){
        //判断是否是任务详情页
        if(Boolean.TRUE.equals(taskParams.isTaskDetail())){
            //判断布局
            if(BPMConstants.LayoutType.objectFlowLayout.equals(standardData.getLayoutType())){
                if (Boolean.FALSE.equals(standardData.getObjectFlowLayoutExists())) {
                    buttons = buttons.stream().filter(item ->
                            item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.AGREE)
                                    || item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.REJECT)
                    ).collect(Collectors.toList());
                }
            }else {
                //判读是否有编辑内容
                if(standardData.getUpdateFromProperty().isFormExistsNormalFields()){
                    //去掉同意按钮、是web的任务详情页需要去掉填写
                    buttons.removeIf(item -> (!taskParams.isMobile() && item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.AGREE))
                            || (item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_CODE) && !taskParams.isTaskDetail()));
                }else {
                    buttons = buttons.stream().filter(item ->
                            item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.AGREE)
                                    || item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.REJECT)
                    ).collect(Collectors.toList());
                }
            }
        }else {
            //数据详情页及卡片按钮 下发 同意、不同意、填写（没有编辑内容不下发）
            buttons = buttons.stream().filter(item ->
                    item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.AGREE)
                            || item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.REJECT)
                            || (item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_CODE) && (standardData.getUpdateFromProperty().isFormExistsNormalFields() || BPMConstants.LayoutType.objectFlowLayout.equals(standardData.getLayoutType())))
            ).collect(Collectors.toList());
        }
        return buttons;
    }
}
