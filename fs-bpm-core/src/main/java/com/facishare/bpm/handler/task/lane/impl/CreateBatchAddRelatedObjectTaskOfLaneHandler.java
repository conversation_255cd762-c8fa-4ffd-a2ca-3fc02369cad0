package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.BatchAddRelatedObjectTaskButtonHandler;
import com.facishare.bpm.handler.task.data.TaskDataOfBatchAddRelatedObjectHandler;
import com.facishare.bpm.handler.task.detail.model.StandardDataOfBatchAddRelatedObject;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.manage.TaskDataManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.paas.I18N;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_CONFIG_BUTTON_COMPLETE;

/**
 * Created by wangzhx on 2019/4/29.
 */
@Service
public class CreateBatchAddRelatedObjectTaskOfLaneHandler implements UserGetLaneTasksHandler {
    @Autowired
    private TaskDataManager taskDataManager;
    @Autowired
    private BatchAddRelatedObjectTaskButtonHandler batchAddRelatedObjectTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = execute(serviceManager, task, batchAddRelatedObjectTaskButtonHandler, taskParams);
        StandardDataOfBatchAddRelatedObject temp;
        laneTask.setData(temp=BeanUtils.transfer(laneTask.getData(), StandardDataOfBatchAddRelatedObject.class,
            (src,rst)->rst.setRelatedObjectList((Map<String, List<TaskDataOfBatchAddRelatedObjectHandler.TaskDataOfBatchAddRelatedObjectVO>>)taskDataManager.getHandler(getTaskType()).getData(serviceManager, task.getId()))));
        /**
         * 如果任务已完成且没有添加任务数据，actionLabel 展示成 完成任务
         */
        if(TaskState.pass.equals(laneTask.getState())&& MapUtils.isEmpty(temp.getRelatedObjectList())){
            temp.setActionLabel(I18N.text(PAAS_FLOW_BPM_CONFIG_BUTTON_COMPLETE.key));
            temp.setActionCode(BPMConstants.Button.Complete.name());
        }
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.batchAddRelatedObject;
    }
}
