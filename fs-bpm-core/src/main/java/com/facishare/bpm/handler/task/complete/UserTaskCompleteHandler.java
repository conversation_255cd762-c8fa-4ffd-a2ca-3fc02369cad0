package com.facishare.bpm.handler.task.complete;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.TaskUtils;
import com.facishare.bpm.utils.log.FlowBizLogUtil;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.rest.core.model.RemoteContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 5:50 PM
 */
public interface UserTaskCompleteHandler extends TaskCompleteHandler{


    @Override
    default CompleteTask.Result execute(
            RefServiceManager serviceManager,
            Task task,
            String opinion,
            Map<String, Object> completedData,
            Integer addOrReplaceNextTaskAssignee,
            Map<String, Object> nextTaskAssignee,
            DataCacheHandler dataCacheHandler,
            boolean needValidateNextTaskAssignee, Boolean ignoreNoBlockValidate) throws Exception {

        //判断任务是否被完成，当前人员是否有权去完成任务
        TaskUtils.validateCompleteTask(serviceManager.getContext(),task);
        //重新规整参数，判断是否需要指定处理人员
        //2019年01月17日15:18:38 修改, 原来 task.needAssignNextTask() =true,则是需要指定下一节点处理人,6.5
        // web端支持更新并完成任务,由于在弹出的form中完成,故不需要指定下一节点处理人,故添加neetValidateNextTaskAssignee
        if (task.needAssignNextTask() && needValidateNextTaskAssignee) {
            if (MapUtils.isEmpty(nextTaskAssignee) || CollectionUtils.isEmpty((Collection) nextTaskAssignee.get(WorkflowKey.ActivityKey.Assignee.person))) {
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_CHOICE_NEXT_ASSIGNEED);
            }
        }


        return executeUserTask(serviceManager, task, opinion, completedData, addOrReplaceNextTaskAssignee,
                nextTaskAssignee, dataCacheHandler, ignoreNoBlockValidate);
    }



    CompleteTask.Result executeUserTask(RefServiceManager serviceManager,
                                        Task task,
                                        String opinion,
                                        Map<String, Object> completedData,
                                        Integer addOrReplaceNextTaskAssignee,
                                        Map<String, Object> nextTaskAssignee,
                                        DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception;

    /**
     * 上报任务完成日志
     * generic-biz-log->flow_biz_log
     */
    default void reportFlowBizLog(RemoteContext context, Task task,String subEventId) {
        if(Objects.nonNull(task.getExecutionType())){
            FlowBizLogUtil.log(context.getTenantId(), task.getEntityId(), task.getObjectId(), context.getUserId(), context.getOutAppId(), context.getOuterUserId()!=0? context.getOuterUserId()+"":"", FlowType.workflow_bpm.name(),"task_handler_"+task.getExecutionType().name(),subEventId,null,0);
        }
    }
}
