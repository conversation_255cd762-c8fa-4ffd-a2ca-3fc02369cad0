package com.facishare.bpm.handler.task.lane;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 6:19 PM
 */
public interface GetLaneTasksHandler {

    LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams);

    ExecutionTypeEnum getTaskType();
}
