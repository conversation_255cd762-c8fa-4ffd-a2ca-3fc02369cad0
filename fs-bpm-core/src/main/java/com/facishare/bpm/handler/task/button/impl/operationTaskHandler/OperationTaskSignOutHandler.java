package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.BaseTask;
import com.facishare.bpm.util.MetadataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 签退
 */
@Slf4j
@Component
public class OperationTaskSignOutHandler extends OperationTaskSignInHandler {

    @Autowired
    private GetTaskFormManager getTaskFormManager;


    @Override
    public FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        FormButtonResult result = new FormButtonResult();
        Map matadataDescribes = serviceManager.findDescribe(standardData.getEntityId(), true, false);
        //判断签到组件是否存在或已开启
        Map signInDescribe = MetadataUtils.getAppointFieldDescribeByFieldCondition(matadataDescribes, BPMConstants.MetadataKey.group_type, BPMConstants.MetadataKey.SIGN_IN);
        if (MapUtils.isEmpty(signInDescribe) || Boolean.FALSE.equals(signInDescribe.get(BPMConstants.MetadataKey.isActive))) {
            //签到组件删除或已禁用  提示
            result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_NOT_EXIST_SIGN_IN.text());
            return result;
        }
        Map data = serviceManager.findDataById(standardData.getEntityId(), standardData.getObjectId());
        boolean isSignIn = MetadataUtils.objectIsSignIn(signInDescribe, data);
        boolean isSignOut = MetadataUtils.objectIsSignOut(signInDescribe, data);
        //按钮和提示信息下发规则  http://wiki.firstshare.cn/pages/viewpage.action?pageId=172146380
        if (taskParams.isMobile()) {
            //签到组件是否开启签退
            boolean isDisableSignOut = MetadataUtils.objectIsDisableSignOut(signInDescribe);
            if (isDisableSignOut) result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_BUTTON_SIGN_OUT_DISABLE.text());
            else {
                if (isSignOut)
                    result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
                else {
                    if (isSignIn)
                        result.addButton(BPMConstants.Button.signout.name(), BPMConstants.Button.signout.getI18NDesc());
                    else result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_BUTTON_NOT_SIGN_OUT.text());
                }
            }
        } else {
            //web
            //签到组件是否开启签退
            boolean isDisableSignOut = MetadataUtils.objectIsDisableSignOut(signInDescribe);
            if (isDisableSignOut) result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_BUTTON_SIGN_OUT_DISABLE.text());
            else {
                if (isSignOut)
                    result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
                else {
                    if (isSignIn)
                        result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_PLEASE_TO_MOBILE_SIGN_OUT.text());
                    else result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_BUTTON_WEB_NOT_SIGN_OUT.text());
                }
            }
        }
        return result;
    }

    @Override
    public void setTask(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams, BaseTask baseTask, Task paasTask) {
        //是签到/签退 添加相关data及标志位
        Map<String, Object> entityDescribe = serviceManager.findDescribe(paasTask.getEntityId(), true, false);
        //获取签到组件描述
        Map<String, Object> signFieldDescribe = MetadataUtils.getAppointFieldDescribeByFieldCondition(entityDescribe, BPMConstants.MetadataKey.group_type, BPMConstants.MetadataKey.SIGN_IN);
        //组件删除或禁用
        if (MapUtils.isEmpty(signFieldDescribe) || Boolean.FALSE.equals(signFieldDescribe.get(BPMConstants.MetadataKey.isActive))) {
            baseTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_NOT_EXIST_SIGN_IN.text());
            return;
        }
        if (MapUtils.isNotEmpty(signFieldDescribe)) {
            StandardData.SignInInfoApiName signInInfoApiName = standardData.createSignInInfoApiNameBySignInDescribe(signFieldDescribe);
            Map<String, Object> data = serviceManager.findDataById(paasTask.getEntityId(), paasTask.getObjectId());
            standardData.setData(data);
            //他人处理不需要下发layout
            if (baseTask.getIsOwner()) {
                Map<String, Object> extension = paasTask.getBpmExtension();
                //需要构建form，bpmExtension中添加，签到组件、签到地点、签到时间、签退地点、签退时间
                extension.put(WorkflowKey.ActivityKey.ExtensionKey.form, OperationTaskSignInHandler.getEntitySignInForm(entityDescribe, signFieldDescribe, signInInfoApiName, BPMConstants.OperationCodeType.signout));
                try {
                    TaskHelper.setTaskForm(serviceManager, paasTask.getCompleted(), extension, paasTask.getEntityId(), paasTask.getObjectId());
                    standardData.initCustomerData(getTaskFormManager.getData(serviceManager, paasTask.getEntityId(), paasTask.getObjectId(), extension, taskParams));
                } catch (Exception e) {
                    log.warn("", e);
                }
            }
            if (!paasTask.isInprogress() && Objects.isNull(data.get(signInInfoApiName.getSignOutPlace())) && !TaskState.suspend.equals(paasTask.getState())) {
                //结束的任务，签到字段没有签到信息 || 签退字段没有签退信息  下发错误信息
                baseTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_SIGNED_IN_GROUP_DELETED.text());
            }
        }
        standardData.setDescribe(entityDescribe);
        baseTask.setData(standardData);
        setButtons(serviceManager, standardData, paasTask.needAssignNextTask(), taskParams, baseTask);
    }

    @Override
    public String getType() {
        return BPMConstants.OperationCodeType.signout.name();
    }

}