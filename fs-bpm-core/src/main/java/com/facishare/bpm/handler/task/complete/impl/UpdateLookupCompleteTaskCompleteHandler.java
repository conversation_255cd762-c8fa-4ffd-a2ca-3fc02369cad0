package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class UpdateLookupCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {


    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        RemoteContext context = serviceManager.getContext();

        String taskId = task.getId();
        String activityId = task.getActivityId();

        if(!task.isDefaultLayoutAndFormNotEmpty()){
            String lookupFieldApiName  = task.getLookupFieldApiName();
            String lookupObjectId = (String)serviceManager.findDataById(task.getEntityId(), task.getObjectId(), lookupFieldApiName, true);
            Map<String, Object> lookupFieldDesc = (Map<String, Object>)serviceManager.getFields(task.getEntityId()).get(lookupFieldApiName);

            Boolean relatedFieldObjectHasValue = MapUtils.isNotEmpty(lookupFieldDesc) && BPMConstants.MetadataKey.objectReference.equals(lookupFieldDesc.get(BPMConstants.MetadataKey.type)) && StringUtils.isNotBlank(lookupObjectId);

            //lookup有数据 需要校验必填
            if(relatedFieldObjectHasValue){
                String lookupEntityId = (String) lookupFieldDesc.get(BPMConstants.MetadataKey.targetApiName);
                Map<String, Object> lookupMetaData = dataCacheHandler.getData(lookupEntityId, lookupObjectId);
                UpdateCompleteTaskCompleteHandler.verifyRequiredField(serviceManager.findDescribe(lookupEntityId, true, true),
                        lookupMetaData, task.getForm());
            }
        }

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                InstanceVariableManager.generateVariableKey(activityId, new Pair<>(task.getEntityId(),
                task.getObjectId())),dataCacheHandler);

        CompleteTask.Result completeTaskResult = paasWorkflowServiceProxy.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);
        reportFlowBizLog(context, task ,null);
        return completeTaskResult;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.updateLookup;
    }
}
