package com.facishare.bpm.handler.task.form.model;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Data
public class LayoutGroupConfig {
    private List<String> childSort;
    private List<String> cantDisplayChild;

    public boolean cantDisplay(String renderType) {
        return CollectionUtils.isNotEmpty(cantDisplayChild) && cantDisplayChild.contains(renderType);
    }
}