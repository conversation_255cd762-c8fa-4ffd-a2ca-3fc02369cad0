package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ExecutionTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.util.DateUtils;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_DELAY_TASK_AUTOMATIC_PROCESSING;
import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_DELAY_TASK_PERSON_PROCESSING;


@Service
public class CreateExecutionTaskOfLaneHandler  implements UserGetLaneTasksHandler {

    @Autowired
    private ExecutionTaskButtonHandler executionTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)) {
            return laneTask;
        }
        //数据权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        standardData.setInstanceId(task.getWorkflowInstanceId());
        setButtonsAndErrorMsg(serviceManager, executionTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        laneTask.setMessage(getExecutionTaskPassMessage(serviceManager, task));
        return laneTask;
    }

    public static String getExecutionTaskPassMessage(RefServiceManager serviceManager, Task task){
        if(TaskState.pass.equals(task.getState()) && task.getDelay()){
            String timeStr = DateUtils.getFormatDate(Objects.isNull(task.getFinishedTime()) ? task.getExecuteTime() : task.getFinishedTime());
            String executor = task.getExecutor();
            if(StringUtils.isBlank(executor) || BPMConstants.CRM_SYSTEM_USER.equals(executor)){
                return I18N.text(PAAS_FLOW_BPM_DELAY_TASK_AUTOMATIC_PROCESSING.key) + " " + timeStr;
            }else {
                Map<Integer, Employee> employees = serviceManager.getMembersByIds(Lists.newArrayList(executor));
                String userName = MapUtils.isNotEmpty(employees) && employees.containsKey(Integer.valueOf(executor)) ? employees.get(Integer.valueOf(executor)).getName() : StringUtils.EMPTY;
                return userName + " " + I18N.text(PAAS_FLOW_BPM_DELAY_TASK_PERSON_PROCESSING.key) + " " + timeStr;
            }
        }
        return null;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.execution;
    }
}
