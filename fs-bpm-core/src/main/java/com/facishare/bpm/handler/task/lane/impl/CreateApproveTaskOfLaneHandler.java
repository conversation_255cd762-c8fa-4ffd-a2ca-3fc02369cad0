package com.facishare.bpm.handler.task.lane.impl;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ApproveTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;


/**
 * Created by wangzhx on 2019/4/29.
 */
@Slf4j
@Service
public class CreateApproveTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private GetTaskFormManager getTaskFormManager;

    @Autowired
    private ApproveTaskButtonHandler approveTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = laneTask.getStandardData();
        String entityId = task.getEntityId();
        String objectId = task.getObjectId();
        Map<String, Object> extension = task.getBpmExtension();

        if (Objects.isNull(standardData)) {
            return laneTask;
        }
        //添加原因:  第一个节点选择新建(A数据)   第二个节点审批节点,且使用第一个节点新建的(A数据),然后不同意,第一个节点再次新建数据(B数据),到达第二个节点;这是将
        //A数据作废并删除 ,数据详情页刷新 提示:数据已作废或已删除
        //优化根据标志位和参数中的标志

        //设置Form
        getTaskFormManager.setForm(serviceManager, task, taskParams, laneTask, entityId, objectId, standardData, extension);
        //数据权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        standardData.setFormEditable(task.approveFormSupportEdit());
        standardData.setUpdateFromProperty(task.existForm());
        setButtonsAndErrorMsg(serviceManager, approveTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.approve;
    }
}
