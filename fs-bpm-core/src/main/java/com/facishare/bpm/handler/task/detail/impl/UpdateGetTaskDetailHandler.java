package com.facishare.bpm.handler.task.detail.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.BpmUpdateTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.UserGetTaskDetailHandler;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.utils.ParallelUtils;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/18 6:58 PM
 */
@Slf4j
@Service
public class UpdateGetTaskDetailHandler implements UserGetTaskDetailHandler {

    @Autowired
    private BpmUpdateTaskButtonHandler bpmUpdateTaskButtonHandler;

    @Autowired
    private GetTaskFormManager getTaskFormManager;

    @Override
    public StandardData getStandardData(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        StandardData standardData = StandardData.getStandardData(task);
        UserGetTaskDetailHandler.setTaskObjectProperty(serviceManager, standardData,taskParams);
        standardData.setUpdateFromProperty(task.existForm());
        /**
         * 兼容enableLayoutRules 为null
         */
        standardData.setEnableLayoutRules(MapUtils.getBooleanValue(task.getBpmExtension(), BPMConstants.ENABLELAYOUTRULES));

        //功能权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        return standardData;
    }

    @SneakyThrows
    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task,TaskParams taskParams) {
        StandardData standardData = getStandardData(serviceManager,task,taskParams);
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        buildTaskUpdateForm(serviceManager,standardData, task, taskParams);
        FormButtonResult formButtonResult = bpmUpdateTaskButtonHandler.setButtons(
                serviceManager,
                standardData,
                task.needAssignNextTask(),
                taskDetail.getState(),
                taskDetail.getCandidateIds(),
                taskDetail.getProcessIds(),
                taskDetail.getExecution(),
                taskParams
        );
        taskDetail.setData(standardData);
        taskDetail.setErrorMsg(formButtonResult.getErrorMsg());
        taskDetail.setButton(transButtons(formButtonResult.getButtons()));
        return taskDetail;
    }

    /**
     * 设置自定义button
     * @param taskParams
     * @param defaultButtons
     */
    private void defaultButtonSettings(TaskParams taskParams, Object defaultButtons) {
        Collection<ActionButton> buttons = Lists.newArrayList();
        //获取默认按钮
        Map<String, Object> rootButton = (Map<String, Object>) defaultButtons;

        rootButton.forEach((action, label) -> {
            ActionButton actionButton = new ActionButton();
            //设置code 为update
            actionButton.setCode(ExecutionTypeEnum.update.name());
            //不需要国际化转换
            actionButton.setI18nConvert(false);

            //设置action
            if (action.equals(ExecutionTypeEnum.update.name())) {
                //如果是update Action首字母要大写
                actionButton.setAction("Update");
            } else {
                actionButton.setAction(action);
            }
            /**
             {
             "update":{
                "label":"aaa"
             },
             "UpdateAndComplete":{
                "label":"bbb"
             }
             "update":"这是错误的数据,但是得兼容"
             }

             */
            if(label instanceof String){
                actionButton.setLabel((String) label);
            }else if (label instanceof Map){
                Map value = (Map) label;
                actionButton.setLabel((String) value.get(WorkflowKey.ActivityKey.ExtensionKey.label));
            }
            buttons.add(actionButton);
        });
        taskParams.buttons(buttons);
    }

    public void buildTaskUpdateForm(RefServiceManager serviceManager, StandardData standardData, Task task, TaskParams taskParams) throws  TimeoutException{
        buildTaskUpdateFormWithoutBtn(serviceManager, getTaskFormManager, task, taskParams, standardData);
        Map<String, Object> extension = task.getBpmExtension();
        Object defaultButtons = extension.get(WorkflowKey.ActivityKey.ExtensionKey.DEFAULT_BUTTONS);
        if(Objects.nonNull(defaultButtons)){
            defaultButtonSettings(taskParams,defaultButtons);
        }
    }

    public static void buildTaskUpdateFormWithoutBtn(RefServiceManager serviceManager, GetTaskFormManager getTaskFormManager, Task task, TaskParams taskParams, StandardData standardData) throws  TimeoutException{
            //判断是否是应用了流程布局  需要下发流程对象布局信息
            if (task.isUsedLayout()) {
                standardData.initFlowLayoutData(task);
            }else {
                String entityId = standardData.getEntityId();
                String objectId = standardData.getObjectId();
                Boolean completed = task.getCompleted();
                //设置form
                Map<String, Object> extension = task.getBpmExtension();
                if (!Boolean.TRUE.equals(taskParams.getIsTaskNotGetData())){
                    //------ 2022.05.14 提前缓存数据
                    ParallelUtils.createParallelTask()
                            .submit(() -> {
                                try {
                                    serviceManager.findDataById(entityId, objectId, false, true, false,true);
                                }catch (RestProxyBusinessException e){}
                            })
                            .submit(() -> serviceManager.getFieldPermissions(entityId))
                            .submit(() -> serviceManager.hasObjectFunctionPrivilege(entityId))
                            .submit(() -> serviceManager.findDescribeExtra(entityId, FindDescribeExtra.FindDescribeExtraType.DescribeLayout))
                            .await(10, TimeUnit.SECONDS);

                    //------
                    try {
                        TaskHelper.setTaskForm(serviceManager, completed, extension, entityId, objectId);
                    }catch (Exception e) {
                        log.warn("", e);
                    }
                standardData.initCustomerData(getTaskFormManager.getData(serviceManager, entityId, objectId, extension, taskParams));
                if(formIsEmpty(standardData.getLayout())){
                    standardData.getUpdateFromProperty().setFormExistsNormalFields(Boolean.FALSE);
                }
            }
        }
        //TaskHelper.setTaskForm 会将当前人没有字段权限的 从extension中删除   注意  注意  注意
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.update;
    }

    public static boolean formIsEmpty(Map<String, Object> layout){
        if(MapUtils.isNotEmpty(layout) && layout.get(BPMConstants.LAYOUT_COMPONENTS) instanceof List){
            List components = (List) layout.get(BPMConstants.LAYOUT_COMPONENTS);
            if(components.size() > 0
                    && components.get(0) instanceof Map
                    && ((Map) components.get(0)).containsKey(BPMConstants.LAYOUT_FIELD_SECTION)
                    && ((Map) components.get(0)).get(BPMConstants.LAYOUT_FIELD_SECTION) instanceof List
                    && ((List<?>) ((Map) components.get(0)).get(BPMConstants.LAYOUT_FIELD_SECTION)).size() == 0){
                return true;
            }
        }
        return false;
    }
}
