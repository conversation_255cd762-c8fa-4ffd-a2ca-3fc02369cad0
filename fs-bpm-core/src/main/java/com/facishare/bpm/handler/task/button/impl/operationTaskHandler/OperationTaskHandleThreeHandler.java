package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.bpm.handler.task.button.impl.OperationTaskButtonHandler.leadsStatusTransfor;

/**
 * 线索一转三
 */
@Slf4j
@Component
public class OperationTaskHandleThreeHandler extends OperationTaskDefaultHandler {

    @Autowired
    private BizTaskDataDao bizTaskDataDao;


    @Override
    public FormButtonResult getBtnAndErrorMsg(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams) {
        FormButtonResult result = new FormButtonResult();
        //判断销售线索是否为已转换
        boolean isTransformed = leadsStatusTransfor(serviceManager, standardData.getEntityId(), standardData.getObjectId(), standardData.getActionCode());
        Map<String, String> actionCodeAndLabels;
        //如果是销售线索 并且是已转换  返回异常信息
        if (isTransformed) {
            result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
        } else {
            //actionCodeAndLabels 过滤后为空 && 销售订单已转换 的话 需要完成任务
            actionCodeAndLabels = filterActionByFunction(standardData.createActionCodeAndLabels(), standardData.getObjectPermissions());
            if (MapUtils.isEmpty(actionCodeAndLabels)) {
                result.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_TASK_NOT_AUTH_COMPLETE.text());
            } else {
                actionCodeAndLabels.forEach((code, label) -> result.addButton(code, label));
            }
        }
        return result;
    }

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        //如果是销售线索,则需要下发部分字段的描述信息,例如:无效字段的描述,跟进字段的描述 6.6 添加 终端需要使用@李汝健
        //设置数据值
        Map<String, Object> customData = Maps.newHashMap();
        Object closeReasonValue = serviceManager.findDataById(task.getEntityId(), task.getObjectId(), BPMConstants.CLOSE_REASON, true);
        customData.put(BPMConstants.CLOSE_REASON, closeReasonValue);
        standardData.setData(customData);
        //设置字段描述
        Map<String, Object> describe = serviceManager.getDescribe(task.getEntityId());
        Map<String, Object> fields = (Map<String, Object>) describe.get(BPMConstants.MetadataKey.fields);
        Map<String, Object> filterFields = fields.keySet().stream().filter(key -> key.equals(BPMConstants.CLOSE_REASON)).collect(Collectors.toMap(k -> k, fields::get));
        describe.put(BPMConstants.MetadataKey.fields, filterFields);
        standardData.setDescribe(describe);
        taskDetail.setData(standardData);
        setButtons(serviceManager, standardData, task.needAssignNextTask(), taskParams, taskDetail);
        return taskDetail;
    }

    @Override
    public LaneTask getLaneTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        //销售线索需要全量数据,退回转移跟进中的转换所需要
        standardData.setData(serviceManager.findDataById(task.getEntityId(), task.getObjectId()));
        laneTask.setData(standardData);
        setButtons(serviceManager, standardData, task.needAssignNextTask(), taskParams, laneTask);
        return laneTask;
    }

    /**
     * 任务完成时 从snapshot 获取actionCode 及actionLabel
     *
     * @param serviceManager
     * @param paasTask
     * @param standardData
     */
    @Override
    public void setActionCodeAndActionLabel(RefServiceManager serviceManager, Task paasTask, StandardData standardData) {
        //如果已经完成  则从snapshot 中获取
        if (standardData.isCombinedActionCode() && Boolean.TRUE.equals(paasTask.getCompleted())) {
            Map<String, Object> snapshot = (Map) bizTaskDataDao.find(serviceManager.getTenantId(), paasTask.getId()).getData();
            if (snapshot != null) {
                standardData.setActionCode((String) snapshot.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode));
                standardData.setActionLabel((String) snapshot.get(WorkflowKey.ActivityKey.ExtensionKey.actionLabel));
            } else {
                log.error("taskId:{}", paasTask.getId() + " isCombinedActionCode,但是完成任务时由于h5问题" + "，遗失了actionCode和actionLabel,以此作为记录");
            }
        } else {
            super.setActionCodeAndActionLabel(serviceManager, paasTask, standardData);
        }
    }

    @Override
    public String getType() {
        return BPMConstants.OperationCodeType.HandleThree.name();
    }

}