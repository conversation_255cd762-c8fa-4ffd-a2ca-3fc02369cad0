package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OperationMultiTaskButtonHandler implements TaskButtonHandler {

    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        FormButtonResult result = new FormButtonResult();
        //查询一下按钮是否可见（不可见概率可能高一点，先做下过滤）
        Map<String, String> visibleBtnMap = serviceManager.findDataExhibitButton(standardData.getEntityId(), standardData.getObjectId(), BPMConstants.MetadataKey.OBJECT_CUSTOM_BTN_DETAIL_TYPE);
        //判断自定按钮中是否含有 转换按钮（销售线索的UI按钮）
        if(standardData.getCommonButtonApiNames().contains(BPMConstants.MetadataKey.LEADS_OBJ_TRANSFER_UI_BTN) && visibleBtnMap.containsKey(BPMConstants.MetadataKey.LEADS_OBJ_TRANSFER_BTN)){
            visibleBtnMap.put(BPMConstants.MetadataKey.LEADS_OBJ_TRANSFER_UI_BTN
                    , serviceManager.findCustomButtonList(standardData.getEntityId(), Boolean.TRUE).stream().filter(item -> BPMConstants.MetadataKey.LEADS_OBJ_TRANSFER_UI_BTN.equals(item.getApi_name())).findFirst().get().getLabel());
        }
        //取到 可见且不是删除和禁用的自定义按钮ApiName
        List<String> taskVisibleBtnApiNameList = standardData.getCommonButtonApiNames().stream()
                .filter(item -> visibleBtnMap.containsKey(item))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(taskVisibleBtnApiNameList)){
            result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
            return result;
        }

        //查询下是否有功能权限
        // 观察下是否需要缓存
        /*
        //上面接口是走了功能权限的不需要再过滤了
        Map<String, Boolean> customBtnPrivilege = metaDataAuthService.getObjectsFunctionPrivilegeSupportOuter(serviceManager.getContext(), standardData.getEntityId(), taskVisibleBtnApiNameList);
        startGetCommonBtn.lap("getPrivilege");
         */
        List<ActionButton> commonActionBtnList = taskVisibleBtnApiNameList.stream()
                .map(item -> new ActionButton(item, visibleBtnMap.get(item)))
                .collect(Collectors.toList());

        result.setButtons(commonActionBtnList);
        result.addButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc());
        return result;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operationMulti;
    }

}
