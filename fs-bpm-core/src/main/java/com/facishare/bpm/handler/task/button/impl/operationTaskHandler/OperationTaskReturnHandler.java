package com.facishare.bpm.handler.task.button.impl.operationTaskHandler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.BaseTask;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.TaskDetail;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客户退回
 */
@Component
public class OperationTaskReturnHandler extends OperationTaskDefaultHandler {

    @Override
    public TaskDetail getTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        TaskDetail taskDetail = TaskDetail.fromTaskDetail(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        setTask(serviceManager,standardData,taskParams,taskDetail, task);
        taskDetail.setData(standardData);
        return taskDetail;
    }

    @Override
    public LaneTask getLaneTaskDetail(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = getStandardData(serviceManager, task, taskParams);
        setTask(serviceManager,standardData,taskParams,laneTask, task);
        laneTask.setData(standardData);
        return laneTask;
    }

    public void setTask(RefServiceManager serviceManager, StandardData standardData, TaskParams taskParams,BaseTask baseTask, Task paasTask) {
        standardData.setData(getData(serviceManager.findDataById(baseTask.getEntityId(), baseTask.getObjectId(), true, true)));
        baseTask.setData(standardData);
        setButtons(serviceManager, standardData, paasTask.needAssignNextTask(), taskParams, baseTask);
    }

    public static Map<String, Object> getData(Map<String, Object> allData) {
        return allData.keySet().stream().filter(k -> k.equals(BPMConstants.HIGH_SEAS_ID) && Objects.nonNull(allData.get(k))).collect(Collectors.toMap((key -> key), (allData::get)));
    }

    @Override
    public String getType() {
        return "return";
    }

}
