package com.facishare.bpm.handler.task.lane;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.UserTaskButtonHandler;
import com.facishare.bpm.handler.task.button.impl.ExecutionTaskButtonHandler;
import com.facishare.bpm.handler.task.button.impl.ExternalApplyTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.Pair;
import com.google.common.base.Strings;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;


/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 5:55 PM
 */
public interface UserGetLaneTasksHandler extends GetLaneTasksHandler {

    @Override
    LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams);

    /**
     * 选择或新建关联对象
     * 新建从对象
     *
     * 只有以上两种情况  才会调用此方法
     * @param serviceManager
     * @param task
     * @param taskButtonHandler
     * @param taskParams
     * @return
     */
    default LaneTask execute(RefServiceManager serviceManager, Task task, TaskButtonHandler taskButtonHandler, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)) {
            return laneTask;
        }
        Map<String, Object> taskExtension = task.getBpmExtension();
        Pair<String, String> relatedDetail = task.getRelatedEntityIdAndListName();
        Map relatedDescribe = serviceManager.findDescribe(relatedDetail.getKey(), true, false);
        String relatedDisplayName = (String) relatedDescribe.get(BPMConstants.MetadataKey.displayName);
        String relatedEntityId = MapUtil.instance.getString(taskExtension, WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId);
        String relatedObjectId = MapUtil.instance.getString(taskExtension, WorkflowKey.ActivityKey.ExtensionKey.relatedObjectId);

        standardData.setObject_describe_api_name(relatedDetail.getKey());
        String referenceField = laneTask.getReferenceAndMasterDetailField(relatedDescribe, relatedDetail.getValue());
        standardData.setRelatedFieldApiName(referenceField);
        standardData.setOnlyRelatedObject(MapUtil.instance.getBool(taskExtension, WorkflowKey.ActivityKey.ExtensionKey.onlyRelatedObject));
        if (StringUtils.isNotBlank(relatedEntityId)) {
            standardData.setRelatedEntityId(relatedEntityId);
            standardData.setRelatedObjectId(relatedObjectId);
            standardData.setRelatedEntityName(Strings.isNullOrEmpty(relatedDisplayName) ? BPMConstants.REPLACE_WHEN_NOT_FOUND : relatedDisplayName);
        }

        setButtonsAndErrorMsg(serviceManager, taskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());


        return laneTask;
    }


    /**
     * 设置自定义按钮和errorMsg,前提web端传递过来entityId,objectId和任务上的是一致的,才会设置buttons
     *
     * @param serviceManager
     * @param userTaskButtonHandler
     * @param standardData
     * @param laneTask
     * @param taskParams
     * @param needAssignNextTask
     */
    default void setButtonsAndErrorMsg(RefServiceManager serviceManager,
                                       UserTaskButtonHandler userTaskButtonHandler,
                                       StandardData standardData,
                                       LaneTask laneTask,
                                       TaskParams taskParams,
                                       boolean needAssignNextTask) {

        if (TaskButtonHandler.starting(laneTask.getState()) || TaskButtonHandler.suspending(laneTask.getState())) {
            //是应用节点 ||在当前数据上,非当前数据不下发button,web端显示去处理||(是处理人&& 不在已处理人中)
            if ((userTaskButtonHandler instanceof ExternalApplyTaskButtonHandler)
                    || (userTaskButtonHandler instanceof ExecutionTaskButtonHandler)
                    || (TaskButtonHandler.isTaskOwner(serviceManager, laneTask.getCandidateIds()) && !TaskButtonHandler.hasProcessed(serviceManager, laneTask.getProcessIds()))
                    || serviceManager.isAdmin()) {

                //没有后动作 或者 后动作没有异常的时候
                if (Objects.isNull(laneTask.getExecution()) || !laneTask.getExecution().isError()) {
                    FormButtonResult formButtonResult = userTaskButtonHandler.setButtons(serviceManager,
                            standardData,
                            needAssignNextTask,
                            laneTask.getState(),
                            laneTask.getCandidateIds(),
                            laneTask.getProcessIds(),
                            laneTask.getExecution(),
                            taskParams
                    );
                    laneTask.setButtons(formButtonResult.getButtons());
                    if (StringUtils.isBlank(laneTask.getErrorMsg())) {
                        laneTask.setErrorMsg(formButtonResult.getErrorMsg());
                    }
                }

            }
        }
    }
}
