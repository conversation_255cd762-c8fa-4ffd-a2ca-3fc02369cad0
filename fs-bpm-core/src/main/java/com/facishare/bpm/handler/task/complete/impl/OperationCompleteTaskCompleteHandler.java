package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.handler.task.complete.UserTaskCompleteHandler;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:31 PM
 */

@Slf4j
@Service
public class OperationCompleteTaskCompleteHandler extends BaseTaskCompleteHandler implements UserTaskCompleteHandler {



    @Override
    public CompleteTask.Result executeUserTask(RefServiceManager serviceManager, Task task, String opinion, Map<String, Object> completedData, Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, DataCacheHandler dataCacheHandler, Boolean ignoreNoBlockValidate) throws Exception {
        RemoteContext context = serviceManager.getContext();

        String taskId = task.getId();
        String objectId = task.getObjectId();
        String entityId = (String) task.getBpmExtension().get("entityId");
        String activityId = task.getActivityId();
        int activityInstanceId = task.getActivityInstanceId();


        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                objectId));


        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                task.getWorkflowInstanceId(),
                task.getWorkflowId(),
                variableKey, dataCacheHandler);
        String operationId=task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.actionCode).toString();
        if (needSnapShot(context.getTenantId(), task, completedData) && !Boolean.TRUE.equals(ignoreNoBlockValidate)) {
            Map<String,Object> snapshotData=snapshot(completedData);
            bizTaskDataDao.snapshotTaskData(context,taskId, activityId, activityInstanceId,snapshotData);
            operationId=snapshotData.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode).toString();
        }

        reportFlowBizLog(context, task ,operationId);

        return paasWorkflowServiceProxy.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, ignoreNoBlockValidate);
    }

    private boolean needSnapShot(String tenantId,Task task, Map<String, Object> snapshot) {
        String entityId = (String) task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.entityId);
        String actionCode = (String) task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.actionCode);

        if (BPMConstants.MetadataKey.APINAME_LEADSOBJ.equals(entityId) && BPMConstants.MetadataKey.leadsObjHandleActionCode
                .equals(actionCode)) {
            verifySnapshot(tenantId,task.getId(), snapshot);
            return true;
        } else {
            return false;
        }
    }

    private void verifySnapshot(String tenantId, String taskId, Map<String, Object> snapshot) {
        if (snapshot == null) {
            snapshot = Maps.newHashMap();
            snapshot.put(WorkflowKey.ActivityKey.ExtensionKey.actionCode, "--");
            snapshot.put(WorkflowKey.ActivityKey.ExtensionKey.actionLabel, "--");
            log.info("verifySnapshot:not set actionCode and actionLabel");
        }
        String actionCode = (String) snapshot.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode);
        String actionLabel = (String) snapshot.get(WorkflowKey.ActivityKey.ExtensionKey.actionLabel);

        if (Strings.isNullOrEmpty(actionCode) || Strings.isNullOrEmpty(actionLabel)) {
            log.error("complete task error : LeadsObj operation snapshot without real action code ! " +
                    "TENANT_ID={}," + "TASK_ID={}", tenantId, taskId);
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_SETTING_ACTION_CODE);
        }
    }

    private Map<String, Object> snapshot(Map<String, Object> data) {
        Map<String, Object> snapshot = Maps.newHashMap();
        if (MapUtils.isNotEmpty(data)) {
            snapshot.put(WorkflowKey.ActivityKey.ExtensionKey.actionCode, data.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode));
            snapshot.put(WorkflowKey.ActivityKey.ExtensionKey.actionLabel, data.get(WorkflowKey.ActivityKey.ExtensionKey.actionLabel));
        } else {
            snapshot.put(WorkflowKey.ActivityKey.ExtensionKey.actionCode, "--");
            snapshot.put(WorkflowKey.ActivityKey.ExtensionKey.actionLabel, "--");
        }
        return snapshot;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.operation;
    }

}
