package com.facishare.bpm.handler.task.form.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.form.UserFormToMetadataManager;
import com.facishare.bpm.handler.task.form.model.FormAssembleType;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc: 生成数据
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 7:24 PM
 */
@Slf4j
@Service
public class GenerateDataManagerImpl implements UserFormToMetadataManager {


    @Override
    public Map<String, Object> execute(RefServiceManager serviceManager, String entityId, String objectId, List<List<Map<String, Object>>> forms, TaskParams taskParams) {
        Map<String, Object> objectData = Maps.newHashMap();
        Map<String, Object> data;
        try {
            data = serviceManager.findDataById(entityId, objectId, true, true,false,true);
        }catch (Exception e){
            log.warn("",e);
            return objectData;
        }

        return data;
    }


    public static void setObjectDataByFieldDescribe(Map<String, Object> fields, Map<String, Object> formField, Map<String, Object> objectData) {
        //给终端或前段下发form参数,终端和前端更新数据的时候,根据layout中form的字段个数进行更新提交,不能全量提交,需要通知景姐和周振沟通下
        //获取字段
        String fieldName = String.valueOf(formField.get(WorkflowKey.ActivityKey.ExtensionKey.name));
        Map fieldDescribe = (Map) fields.get(fieldName);

        Object value = formField.get(WorkflowKey.ActivityKey.ExtensionKey.value);
        //百分数&&不为空，转成string
        if (MapUtils.isNotEmpty(fieldDescribe) &&
                BPMConstants.MetadataKey.PERCENTILE.equals(fieldDescribe.get(BPMConstants.MetadataKey.type)) &&
                Objects.nonNull(value)) {
            objectData.put(fieldName, String.valueOf(value));
        } else {
            objectData.put(fieldName, value);
        }
        //如果是关联对象  需要添加下__r
        if (formField.get(WorkflowKey.ActivityKey.ExtensionKey.type).equals(BPMConstants.MetadataKey.objectReference)) {
            objectData.put(fieldName + BPMConstants.MetadataKey.lookupFieldNameValuePostfix, formField.get(WorkflowKey.ActivityKey.ExtensionKey.relatedObjectName));
        }
    }

    /**
     * 补充字段
     */
    private void compensateField(String field, String postfix, Map<String, Object> objectData, Object value, Map<String, Object> data) {
        if (field.endsWith(postfix)) {
            String apiName = field.replace(postfix, StringUtils.EMPTY);
            objectData.put(apiName, data.get(apiName));
            objectData.put(field, value);
        }
    }

    @Override
    public FormAssembleType getTaskType() {
        return FormAssembleType.data;
    }
}
