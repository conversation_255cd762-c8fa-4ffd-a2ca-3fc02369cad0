package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.springframework.stereotype.Service;

@Service
public class CustomTaskButtonHandler implements TaskButtonHandler {
    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        return new FormButtonResult();
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.custom;
    }
}
