package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.BpmUpdateTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/29 6:21 PM
 */
@Slf4j
@Service
public class CreateUpdateTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private GetTaskFormManager getTaskFormManager;

    @Autowired
    private BpmUpdateTaskButtonHandler bpmUpdateTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);

        String entityId = task.getEntityId();
        String objectId = task.getObjectId();

        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)) {
            return laneTask;
        }

        Map<String, Object> extension = task.getBpmExtension();
        //添加原因:  第一个节点选择新建(A数据)   第二个节点审批节点,且使用第一个节点新建的(A数据),然后不同意,第一个节点再次新建数据(B数据),到达第二个节点;这是将
        //A数据作废并删除 ,数据详情页刷新 提示:数据已作废或已删除
        //优化根据标志位和参数中的标志

        //设置Form
        getTaskFormManager.setForm(serviceManager, task, taskParams, laneTask, entityId, objectId, standardData, extension);
        standardData.setUpdateFromProperty(task.existForm());
        //数据权限
        standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        //获取到自定义按钮  Save/Update/UpdateAndComplete
        //异常的情况下,不下发任何按钮 ,前端逻辑,异常后,前端显示`异常处理`按钮,如果后端下发了按钮,则会覆盖掉 `异常处理`
        //从对象的话,不下发任何按钮,用户需要点击去处理,跳转到任务落地页处理任务
        //1. 本对象更新,有form,无自定义按钮,下发填写
        //2. 本对象更新,有form,有自定义按钮,下发自定义按钮
        //3. 本对象更新,有form,仅有签到组件,无自定义按钮,下发完成任务
        //4. 本对象更新,有form,仅有签到组件,有自定义按钮,下发完成任务
        //5. 本对象更新,无form,无自定义按钮,下发完成任务
        //6. 本对象更新,无form,有自定义按钮,下发完成任务
        //7. 关联对象更新,无form,不下发按钮,到任务详情页处理任务
        //8. 关联对象更新,有form,无自定义按钮,不下发按钮,到任务详情页处理任务
        //9. 关联对象更新,有form,有自定义按钮,不下发按钮,到任务详情页处理任务

        setButtonsAndErrorMsg(serviceManager, bpmUpdateTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.update;
    }
}
