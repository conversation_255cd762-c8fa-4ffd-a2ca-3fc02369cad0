package com.facishare.bpm.handler.task.button.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskLog;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.DateUtils;
import com.facishare.paas.I18N;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_DELAY_TASK_AUTOMATIC_PROCESSING;

@Service
@Slf4j
public class ExecutionTaskButtonHandler implements TaskButtonHandler {




    @Autowired
    private BPMInstanceService bpmInstanceService;
    @Autowired
    private BPMTaskService bpmTaskService;

    @Override
    public FormButtonResult setButtons(RefServiceManager serviceManager, StandardData standardData, boolean assignNextTask, TaskParams taskParams) {
        WorkflowInstance instance = bpmInstanceService.getWorkflowInstance(serviceManager, standardData.getInstanceId());
        List<Task> autoTasks = bpmTaskService.getAutoTaskByConditions(serviceManager, standardData.getInstanceId(), null, standardData.getTaskId(),  null, null);
        Optional<Task> taskOptional = autoTasks.stream().filter(t -> standardData.getTaskId().equals(t.getId())).findFirst();
        if (Objects.isNull(instance) || !taskOptional.isPresent()){
            log.info("获取等待节点实例或任务信息失败，taskId:{}", standardData.getTaskId());
            return new FormButtonResult();
        }
        Task task = taskOptional.get();
        if(!task.getDelay()){
            return new FormButtonResult(TaskState.in_progress.equals(task.getState()) ? null : task.getErrMsg());
        }
        String errorMsg;
        if (TaskState.in_progress.equals(task.getState())){
            String timeStr = DateUtils.getFormatDate(task.getExecuteTime());
            errorMsg = timeStr + "，" + I18N.text(PAAS_FLOW_BPM_DELAY_TASK_AUTOMATIC_PROCESSING.key);
            if (StringUtils.isNotBlank(task.getErrMsg())){
                errorMsg += "。" + task.getErrMsg();
            }
        }else {
            errorMsg = task.getErrMsg();
        }
        List<ActionButton> buttons = TaskLog.getTaskLogActions(serviceManager, task, instance).stream().map(action -> new ActionButton(action.getActionCode(), action.getActionName())).collect(Collectors.toList());

        return new FormButtonResult(buttons, errorMsg);
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.execution;
    }
}
