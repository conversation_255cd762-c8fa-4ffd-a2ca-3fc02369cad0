package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.UpdateLookupTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
public class CreateUpdateLookupTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private GetTaskFormManager getTaskFormManager;

    @Autowired
    private UpdateLookupTaskButtonHandler updateLookupTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {
        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)) {
            return laneTask;
        }
        String fieldApiName  = task.getLookupFieldApiName();
        String objectId = (String)serviceManager.findDataById(task.getEntityId(), task.getObjectId(), fieldApiName, true);
        Map<String, Object> lookupFieldDesc = (Map<String, Object>)serviceManager.getFields(task.getEntityId()).get(fieldApiName);
        if (StringUtils.isNotBlank(objectId) && MapUtils.isNotEmpty(lookupFieldDesc) && BPMConstants.MetadataKey.objectReference.equals(lookupFieldDesc.get(BPMConstants.MetadataKey.type))) {
            standardData.setRelatedFieldObjectHasValue(Boolean.TRUE);
            String nowLookupFieldObjApiName = (String) lookupFieldDesc.get(BPMConstants.MetadataKey.targetApiName);
            standardData.setEntityId(nowLookupFieldObjApiName);
            standardData.setObjectId(objectId);
            //判断关联对象entityId是否一致
            standardData.setRelatedFieldObjectIsIdentical(nowLookupFieldObjApiName.equals(task.getLookupFieldEntityId()));

        }
        //设置Form
        if(standardData.getRelatedFieldObjectIsIdentical() && standardData.getRelatedFieldObjectHasValue()){
            getTaskFormManager.setForm(serviceManager, task, taskParams, laneTask, standardData.getEntityId(), standardData.getObjectId(), standardData,  task.getBpmExtension());
            //数据权限
            standardData.setObjectPermissions(serviceManager.hasObjectFunctionPrivilege(standardData.getEntityId()));
        }
        standardData.setUpdateFromProperty(task.existForm());
        setButtonsAndErrorMsg(serviceManager, updateLookupTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.updateLookup;
    }
}
