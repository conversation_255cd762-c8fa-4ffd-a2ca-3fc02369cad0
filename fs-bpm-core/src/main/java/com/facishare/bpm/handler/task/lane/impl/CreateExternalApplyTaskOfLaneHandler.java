package com.facishare.bpm.handler.task.lane.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.impl.ExternalApplyTaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.lane.UserGetLaneTasksHandler;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * Created by wangzhx on 2019/8/21.
 */
@Service
public class CreateExternalApplyTaskOfLaneHandler implements UserGetLaneTasksHandler {

    @Autowired
    private ExternalApplyTaskButtonHandler externalApplyTaskButtonHandler;

    @Override
    public LaneTask execute(RefServiceManager serviceManager, Task task, TaskParams taskParams) {

        LaneTask laneTask = LaneTask.fromTask(serviceManager, task);
        StandardData standardData = laneTask.getStandardData();
        if (Objects.isNull(standardData)) {
            return laneTask;
        }
        standardData.setAppCode(task.getExternalApplyAppCode());
        standardData.setActionCode(task.getExternalApplyActionCode());
        /**
         * 当上一节点设置指定下一节点处理人时，应用节点会有人，不应该下发
         */
        //laneTask.setCandidateIds(null);
        laneTask.setTodoJumpUrl(standardData.getTodoJumpUrl());
        /**
         * 应用节点下发"去处理"按钮
         */
        setButtonsAndErrorMsg(serviceManager, externalApplyTaskButtonHandler, standardData, laneTask, taskParams, task.needAssignNextTask());
        return laneTask;
    }

    @Override
    public ExecutionTypeEnum getTaskType() {
        return ExecutionTypeEnum.externalApplyTask;
    }
}
