package com.facishare.bpm.handler.task.complete.impl;

import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:36 PM
 */
@Service
public class BaseTaskCompleteHandler {

    @Autowired
    protected InstanceVariableManager instanceVariableManager;

    @Autowired
    protected PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @Autowired
    protected BizTaskDataDao bizTaskDataDao;
}
