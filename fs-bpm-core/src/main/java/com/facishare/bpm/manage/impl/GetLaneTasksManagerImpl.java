package com.facishare.bpm.manage.impl;

import com.facishare.bpm.handler.task.lane.GetLaneTasksHandler;
import com.facishare.bpm.manage.GetLaneTasksManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by wangzhx on 2019/4/30.
 */
@Service
public class GetLaneTasksManagerImpl extends ApplicationObjectSupport implements GetLaneTasksManager {

    private Map<ExecutionTypeEnum, GetLaneTasksHandler> handlerMap;

    @PostConstruct
    public void init() {
        Map<String, GetLaneTasksHandler> laneTasksHandlerBeanMap = getApplicationContext().getBeansOfType(GetLaneTasksHandler.class);
        handlerMap = laneTasksHandlerBeanMap.values().stream().collect(Collectors.toMap(GetLaneTasksHandler::getTaskType, v -> v));
    }

    @Override
    public GetLaneTasksHandler getHandler(ExecutionTypeEnum executionTypeEnum) {
        return handlerMap.get(executionTypeEnum);
    }
}
