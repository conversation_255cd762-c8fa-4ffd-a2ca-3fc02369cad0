package com.facishare.bpm.manage.impl;

import com.facishare.bpm.handler.task.complete.TaskCompleteHandler;
import com.facishare.bpm.manage.TaskExecutionManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 4:21 PM
 */
@Service
public class TaskExecutionManagerImpl extends ApplicationObjectSupport implements TaskExecutionManager {

    private Map<ExecutionTypeEnum, TaskCompleteHandler> handlers;

    @PostConstruct
    public void init() {
        Map<String, TaskCompleteHandler> taskCompleteHandler = getApplicationContext().getBeansOfType(TaskCompleteHandler.class);
        handlers = taskCompleteHandler.values().stream().collect(Collectors.toMap(TaskCompleteHandler::getTaskType, k -> k));
    }


    @Override
    public TaskCompleteHandler getHandler(ExecutionTypeEnum executionTypeEnum) {
        return handlers.get(executionTypeEnum);
    }

}
