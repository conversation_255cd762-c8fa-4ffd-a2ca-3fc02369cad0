package com.facishare.bpm.manage;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.form.model.CustomerData;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.LaneTask;

import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/30 9:56 AM
 */
public interface GetTaskFormManager {

    CustomerData getData(RefServiceManager serviceManager, String entityId, String objectId, Map<String, Object> extension, TaskParams taskParams);

    Map<String, Object> getLayout(RefServiceManager serviceManager, String entityId, String objectId, Map<String, Object> extension,TaskParams taskParams);

    void setForm(RefServiceManager serviceManager, Task task, TaskParams taskParams, LaneTask laneTask, String entityId, String objectId, StandardData standardData, Map<String, Object> extension);
}
