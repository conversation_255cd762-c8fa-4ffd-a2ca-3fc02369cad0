package com.facishare.bpm.manage.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.MetaDataToWorkflowVariableUtil;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.util.verifiy.util.CustomVariableUtil;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.OWNER_MAIN_DEPT_PATH;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.SELF_REF_KEY;

/**
 * <AUTHOR>
 * @since 5.7
 */
@Slf4j
@Service
public class InstanceVariableManagerImpl implements InstanceVariableManager {

    @Override
    public Map<String, Object> getWorkflowVariableInstances(RefServiceManager serviceManager, String workflowInstanceId, String workflowId, Map<String, Pair<String, String>> variableKeys, DataCacheHandler dataCacheHandler) {
        List<String> currentTaskReferObjectIds = variableKeys.values().stream().map(Pair::getValue).collect(Collectors.toList());
        Map<String, VariableExt> variableInstanceMap = dataCacheHandler.getVariableMap(workflowInstanceId, workflowId);
        resetOldVariableKeys(variableInstanceMap.values(), variableKeys);
        variableKeys.forEach((variableKey, entityIdAndObjectId) -> {
            String objectId = entityIdAndObjectId.getValue();
            VariableExt variableInstance = variableInstanceMap.get(variableKey);
            if (variableInstance != null) {
                log.info("getWorkflowVariableInstances:variableKey:{},objectId:{}", variableKey, objectId);
                variableInstance.put("value", objectId);
            } else {
                variableInstance = new VariableExt(variableKey, objectId);
                variableInstance.put("value", objectId);
                variableInstanceMap.put(variableKey, variableInstance);
                log.info("getWorkflowVariableInstances:variableKey:{},objectId:{},instanceMap:{}", variableKey, objectId, JsonUtil.toJson(variableInstanceMap));
            }
        });
        if(SwitchConfigManager.isUpgradeTaskInfo(serviceManager.getTenantId())){
            List<String> hasProcessObjectVariableIds = Lists.newArrayList();
            variableKeys.forEach((variableKey, entityIdAndObjectId) -> {
                  String apiName = entityIdAndObjectId.getKey();
                  String objectId = entityIdAndObjectId.getValue();
                  try {
                      if (!hasProcessObjectVariableIds.contains(objectId)) {
                          //TODO 优化下,去掉获取变量的逻辑
                          //需要使用-10000 获取,否则会提示:任务相关对象已被删除或作废,无法完成任务
                          Map<String, Object> metaData = null;
                          try {
                              metaData = serviceManager.findDataById(apiName, objectId, true, false);
                          } catch (Throwable throwable) {
                              log.warn("", throwable);
                          }
                          //用当前人身份先去查询  如果没权限  在用-10000去查询,解决重复数据查询的问题,提高响应速度
                          if(MapUtils.isEmpty(metaData)){
                              RemoteContext cnt = BeanUtils.transfer(serviceManager.getContext(), RemoteContext.class,(src,rst)->{
                                  rst.setUserId(BPMConstants.CRM_SYSTEM_USER);
                              });
                              metaData = serviceManager.findDataById(cnt,apiName, objectId, true, false);
                          }
                          hasProcessObjectVariableIds.add(objectId);
                          if (MapUtils.isNotEmpty(metaData) && metaData.keySet().contains(BPMConstants.MetadataKey.id)) {
                              setFieldsValue(serviceManager, variableInstanceMap, metaData);
                          } else if (currentTaskReferObjectIds.contains(objectId)) {
                              throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_RELATION_OBJECT_DELETE);
                          }
                      }
                  } catch (Throwable e) {
                      log.error("setWorkflowVariable TENANT_ID = " + serviceManager.getTenantId() +
                        ", ENTITY_ID=" + apiName + ", DATA_ID = " + objectId + ". " + e.getMessage());
                      throw e;
                  }
              });
        }
        Map<String, Object> variables = Maps.newHashMap();
        variableInstanceMap.values().forEach(item -> variables.put(item.getId(), item.get("value")));

        log.info("getWorkflowVariableInstances:{}", JacksonUtil.toJson(variables));

        return variables;
    }


    private void resetOldVariableKeys(Collection<VariableExt> variables, Map<String, Pair<String, String>> variableKeys) {
        //将已经有id的节点涉及到的变量重新赋值时使用
        try {
            variables.forEach(item -> {
                String key = item.getId();
                if(CustomVariableUtil.isCustomVariable(key)){
                    return;
                }

                Object value = item.get("value");
                if (value != null) {
                    String[] parts = key.split("##");
                    String entityId = null;
                    if (parts.length == 2 && !parts[1].equals(BPMConstants.ApproveResult.RESULT)) {
                        entityId = parts[1];
                    } else if (parts.length == 3 && parts[1].equals(SELF_REF_KEY)) {
                        entityId = parts[2];
                    }
                    if (entityId != null && StringUtils.isNotBlank((String) value)) {
                        //2018年09月03日15:50:55
                        Pair<String, String> pair;
                        //如果从前端获取到的对象为空,则还是用之前var中的值
                        if (Objects.isNull(variableKeys.get(key))) {
                            pair = new Pair(entityId, value);
                        } else {
                            //如果有新的值,则采用最新的
                            pair = variableKeys.get(key);
                        }
                        variableKeys.put(key, pair);
                        log.info("设置重新计算的对象:key:{},entityId:{},objectId:{}", key, entityId, value);
                    }
                }
            });
        } catch (RuntimeException e) {
            log.error("resetOldVariableKeys", e);
        }
    }

    private void setFieldsValue(RefServiceManager serviceManager, Map<String, VariableExt> variableInstanceMap, Map<String, Object> data) {
        String id = (String) data.get(BPMConstants.MetadataKey.id);
        variableInstanceMap.keySet().forEach(key -> {
            if(CustomVariableUtil.isCustomVariable(key)){
                return;
            }
            VariableExt variableInstance = variableInstanceMap.get(key);
            String[] parts = key.split("##");
            if (parts.length == 3 && !BPMConstants.SELF_REF_KEY.equals(parts[1])) {
                String fieldName = parts[2];//Level
                String objectKey = parts[0] + "##" + parts[1];//Activity_0##AccountObj
                String currentVariableObjectId = (String) variableInstanceMap.get(objectKey).get("value");
                if (null != currentVariableObjectId && id.equals(currentVariableObjectId)) {
                    Object value = null;
                    if (data.keySet().contains(fieldName)) {
                        value = MetaDataToWorkflowVariableUtil.parseValue(data.get(fieldName), variableInstance.getTypeName());
                        variableInstance.put("value", value);
                    } else if (fieldName.equals(OWNER_MAIN_DEPT_PATH)) {
                        String owner = getOwner(data);
                        if (owner != null) {

                            String mainDeptId = serviceManager.getMainDeptsByUserIds(Lists.newArrayList(owner)).getMainDeptId(owner);
                            if (mainDeptId != null) {
                                value = Lists.newArrayList(mainDeptId);
                                variableInstance.put("value", value);
                            }

                        }
                    }
                    if (null != value) {
                        log.info("setFieldsValue:key:{},value:{},variableType:{}", key, variableInstance.get("value"), variableInstance.getTypeName());
                    }
                }
            } else if (parts.length == 4 && BPMConstants.SELF_REF_KEY.equals(parts[1])) {
                String fieldName = parts[3];//Level
                String objectKey = parts[0] + "##" + parts[1] + "##" + parts[2];//Activity_0##SelfRef##AccountObj
                String currentVariableObjectId = (String) variableInstanceMap.get(objectKey).get("value");
                if (null != currentVariableObjectId && id.equals(currentVariableObjectId)) {
                    Object value = MetaDataToWorkflowVariableUtil.parseValue(data.get(fieldName), variableInstance.getTypeName());
                    variableInstance.put("value", value);
                    if (value != null) {
                        log.info("setFieldsValue:key:{},value:{},variableType:{}", key, variableInstance.get("value"), variableInstance.getTypeName());
                    }
                }
            }
        });
    }

    private static String getOwner(Map<String, Object> data) {
        Object object = data.get("owner");
        if (object instanceof List) {
            List owners = (List) object;
            if (!CollectionUtils.isEmpty(owners)) {
                return owners.get(0) + "";
            }
        }
        if (NumberUtils.isNumber(object + "")) {
            return object + "";
        }
        return null;
    }


}
