package com.facishare.bpm.manage;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMRuntimeException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * 管理实例变量的获取相关的逻辑，
 * 主要使用在：
 * 1. 流程启动时变量的赋值
 * 2. 任务完成时变量的赋值
 * 3. 目前支持 activity_id##entity_id##field
 *            activity_id##entity_id##@OWNER_MAIN_DEPT_PATH 对象负责人的主属部门
 * @since 5.7
 */
public interface InstanceVariableManager {

    Map<String,Object> getWorkflowVariableInstances(RefServiceManager serviceManager, String workflowInstanceId, String workflowId,
                                                    Map<String, Pair<String, String>> variableKeys,
                                                    DataCacheHandler dataCacheHandler);

    static String getActivityVariableEntityId(String activityId,String entityId){
        return String.format(FORMAT_VARIABLE_KEY, activityId,entityId);
    }

    static void setInstanceObjectVariable(Map variables, Map<String, Map<String, Object>> variableKeyAndData) {
        variableKeyAndData.forEach((variableKey, data) -> variables.put(variableKey, data.get(BPMConstants.MetadataKey.id)));
    }
    static Map<String, Pair<String, String>> generateVariableKey(String activityId, Pair<String, String>...
            entityIdAndObjectIds) {

        Map<String, Pair<String, String>> variableKey = Maps.newHashMap();
        int length = entityIdAndObjectIds.length;
        switch (length) {
            /**
             * 普通任务  activity_0##EntityId   <entityId objectId> 选择和创建关联对象节点，有两个对象
             */
            case 1:
                variableKey.put(String.format(FORMAT_VARIABLE_KEY, activityId, entityIdAndObjectIds[0].getKey()),
                        entityIdAndObjectIds[0]);
                break;
            /**
             * 选择和创建关联对象
             * activity_1##EntityId   <entityId objectId> 选择和创建关联对象节点，有两个对象
             * activity_1##SelfRef##EntityId   <entityId objectId>
             */
            case 2:
                variableKey.put(String.format(FORMAT_VARIABLE_KEY, activityId, entityIdAndObjectIds[0].getKey()),
                        entityIdAndObjectIds[0]);
                if (entityIdAndObjectIds[0].getKey().equals(entityIdAndObjectIds[1].getKey())) {
                    variableKey.put(String.format(FORMAT_VARIABLE_SELF_REF_KEY, activityId, entityIdAndObjectIds[1]
                            .getKey()), entityIdAndObjectIds[1]);
                } else {
                    variableKey.put(String.format(FORMAT_VARIABLE_KEY, activityId, entityIdAndObjectIds[1].getKey
                            ()), entityIdAndObjectIds[1]);
                }
                break;

            default:
                throw new BPMRuntimeException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NODE_OBJECT_ERROR,JsonUtil.toJson(entityIdAndObjectIds));
        }

        return variableKey;
    }
    String FORMAT_VARIABLE_KEY = "activity_%s##%s";
    String FORMAT_VARIABLE_SELF_REF_KEY = "activity_%s##SelfRef##%s";

}
