package com.facishare.bpm.manage.impl;

import com.facishare.bpm.handler.task.detail.GetTaskDetailHandler;
import com.facishare.bpm.manage.GetTaskManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/18 7:00 PM
 */
@Service
public class GetTaskManagerImpl extends ApplicationObjectSupport implements GetTaskManager {

    private Map<ExecutionTypeEnum, GetTaskDetailHandler> handlers;

    @PostConstruct
    public void init() {
        Map<String, GetTaskDetailHandler> taskCompleteHandler = getApplicationContext().getBeansOfType(GetTaskDetailHandler.class);
        handlers = taskCompleteHandler.values().stream().collect(Collectors.toMap(GetTaskDetailHandler::getTaskType, k -> k));
    }


    @Override
    public GetTaskDetailHandler getHandler(ExecutionTypeEnum executionTypeEnum) {
        return handlers.get(executionTypeEnum);
    }
}
