package com.facishare.bpm.manage.impl;

import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowTemplateException;
import com.facishare.bpm.manage.DefinitionInitManager;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.utils.BeanConvertUtil;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * Created by wangzhx on 2018/11/20.
 * <p>
 * 不需要每次都初始化 只需要在fs-bpm-biz中交给容器来管理
 */
@Slf4j
public class DefinitionInitManagerImpl implements DefinitionInitManager {


    @Autowired
    private BpmSimpleDefinitionDao outlineDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    private DefinitionExtensionDao workflowExtensionDao;

    private volatile static Map<String, WorkflowOutline> workflowOutlines;


    @PostConstruct
    public void init() {
        workflowOutlines = loadTemplate(DefinitionInitManagerImpl.class.getResource("/template").getPath());
    }

    public WorkflowOutline getInitDefinition(String sourceWorkflowId) {
        WorkflowOutline workflowOutline = workflowOutlines.get(sourceWorkflowId);

        if (Objects.isNull(workflowOutline)) {
            log.error("{} 对应的模版不存在", sourceWorkflowId);
            throw new BPMWorkflowTemplateException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_WORKFLOW_TEMPLATE_NO_DEFINITION_FOUND);
        }

        return workflowOutline;
    }

    private Map<String, WorkflowOutline> loadTemplate(String path) {
        Map<String, WorkflowOutline> templates = Maps.newHashMap();
        try {
            File[] files = new File(path).listFiles();
            if (files != null) {
                for (File templateFile : files) {
                    String templateStr = IOUtils.toString(templateFile.toURI());
                    templates.put(getTemplateKey(templateFile.getName()), JacksonUtil.fromJson(templateStr, WorkflowOutline.class));
                    log.info("加载预设对象 业务流模板 {}", templateFile.getName());
                }
            } else {
                log.error("没有加载到 模版文件");
            }
        } catch (IOException e) {
            log.error("{},{}", "加载业务流模版时出现异常", path, e);

        }
        return templates;

    }

    /**
     * 单独提出来的原因:如果放到BPMDefinitionServiceImpl 中 每次初始化都会触发化这个类,还必须有模板文件,没必要,只有在fs-bpm-biz注入此容器即可
     *
     * @param context
     * @param sourceWorkflowId
     * @return
     */
    @Override
    public WorkflowOutline definitionInit(RemoteContext context, String sourceWorkflowId) {

        // 获取模板
        WorkflowOutline workflowOutline = getInitDefinition(sourceWorkflowId);
        // 防止深研给的模板中存在id
        BeanConvertUtil.toWorkflowOutline(workflowOutline, context);
        // 获取要给引擎的参数
        ExecutableWorkflowExt executableWorkflow = workflowOutline.getWorkflow();

        workflowOutline.setSourceWorkflowId(sourceWorkflowId);
        executableWorkflow.setSourceWorkflowId(sourceWorkflowId);

        // 如果rule不为空,删除掉ruleId
        if (workflowOutline.getRule() != null) {
            workflowOutline.getRule().setRuleId(null);
        }

        //设置引擎的基础变量
        workflowOutline.setEngineExecutableWorkflow(executableWorkflow);
        //校验名称
        outlineDao.validateWorkflowNameDuplicate(context.getTenantId(), workflowOutline.getId(), workflowOutline.getName());
        //部署到引擎
        WorkflowOutline deployWorkflowOutline = paasWorkflow.deploy(context, false, workflowOutline);
        //保存扩展
        workflowExtensionDao.save(context.getTenantId(), WorkflowOutline.fromWorkflowOutLine(workflowOutline));
        //创建和更新 流程定义
        WorkflowOutlineEntity entity = outlineDao.createOrUpdate(context.getTenantId(), WorkflowOutline.toOutlineEntity(context, workflowOutline));
        log.info("context:{},sourceWorkflowId:{}, definition initialization successful", context, deployWorkflowOutline.getSourceWorkflowId());
        return WorkflowOutline.fromEntity(entity);
    }

    // 9999_XT  ->  9999
    private String getTemplateKey(String fileName) {
        return Splitter.on("_").splitToList(fileName).get(0);
    }

}
