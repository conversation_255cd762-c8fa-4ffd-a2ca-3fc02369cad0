package com.facishare.bpm.manage;

import java.util.Map;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2018/12/27 6:00 PM
 */
public interface RedisManager {

    boolean setValueWithExpire(String key, String data);

    boolean setValueWithExpire(String key, String data, int expireTime);

    boolean setValueWithExpireTime(String key, String data, int expireTime);

    Map<String,String> getValueMapByKey(String key);

    boolean setValueOfMap(String key, Map<String, String> value);

    long delete(String key);

    Long incrBy(String key, Long number);
}
