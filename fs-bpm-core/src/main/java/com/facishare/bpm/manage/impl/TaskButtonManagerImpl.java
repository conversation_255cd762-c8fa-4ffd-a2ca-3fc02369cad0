package com.facishare.bpm.manage.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.button.helper.TaskButtonConfigHelper;
import com.facishare.bpm.handler.task.button.impl.OperationTaskButtonHandler;
import com.facishare.bpm.handler.task.button.model.DefaultActionLabel;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.ButtonCustomManager;
import com.facishare.bpm.manage.FormButtonManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.util.CompleteTaskFormValidateManager;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.i18n.I18NUtils;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.Button.confirmdelivery;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.Button.confirmreceive;

/**
 * Created by Aaron on 26/04/2017.
 * TODO 前端依旧在调用,推进下线中
 */
@Deprecated
@Service
@Slf4j
@Data
public class TaskButtonManagerImpl implements FormButtonManager {

    @Autowired
    private ButtonCustomManager buttonCustomManager;

    @Override
    public void setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams) {
        if (mTask.starting()) {
            if (mTask.getExecutionType().isExternalApplyTask() ||
                    (mTask.getIsTaskOwner() && !mTask.hasProcessed(serviceManager.getUserIdWithOuterUserId()))) {
                if (Objects.isNull(mTask.getExecution()) || !mTask.getExecution().isError()) {
                    TaskButtonHandler taskButtonHandler = buttonCustomManager.getHandler(mTask.getExecutionType());

                    List<ActionButton> buttons = ButtonHandlerImpl.valueOf(mTask.getExecutionType().name()).setButtons(serviceManager, mTask, taskParams,taskButtonHandler);

                    mTask.setButtons(TaskButtonHandler.getCustomButtons(mTask.getExecutionType(), JacksonUtil.fromJson(
                            JacksonUtil.toJson(mTask.getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.DEFAULT_BUTTONS)),
                            new TypeReference<Map<String, DefaultActionLabel>>() {
                            }),
                            buttons));
                }
            }
        }
        if (CollectionUtils.isEmpty(mTask.getButtons())) {
            mTask.setButtons(Lists.newArrayList());
        }
    }


    public interface ButtonHandler {
        List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler);
    }

    public enum ButtonHandlerImpl implements ButtonHandler {
        update {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                List<ActionButton> buttons = super.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
                //如果需要指定下一节点处理人,则不下发更新并完成任务
                if (isOuterPerson(serviceManager.getUserId())) {
                    return buttons.stream().peek(actionButton -> actionButton.setLabel(I18NUtils.text(actionButton.getLabel(),actionButton.getLabel()))).collect(Collectors.toList());
                }

                Map<String, Boolean> func = serviceManager.hasObjectFunctionPrivilege(mTask.getEntityId());


                //没有编辑的功能权限,则过滤掉update和save
                if (!func.get(BPMConstants.MetadataKey.EDIT)) {
                    buttons = buttons.stream().filter(item -> {
                        return !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_CODE) && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.SAVE_CODE);
                    }).collect(Collectors.toList());
                }

                if (mTask.needAssignNextTask() && !taskParams.isUpdateAndCompleteAssignNextNodeProcessor()) {
                    buttons = buttons.stream().filter(item -> !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE)).collect(Collectors.toList());
                }
                //true  表示有其他的字段
                CompleteTaskFormValidateManager.ValidateFormButton validateFormButton = CompleteTaskFormValidateManager.validateFormButtonIsShow(serviceManager, mTask.getEntityId(), mTask.getExtension(),taskParams);
                // 没有字段 或者只有签到组件时
                if (!validateFormButton.isShowButton()) {
                    if(taskParams.isH5() && validateFormButton.isOnlySignIn()){
                        //如果只有签到签退,则删除掉更新和更新并完成任务
                        buttons = buttons.stream().filter(item -> {
                            return !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE)
                                    && !item.getAction().equalsIgnoreCase(MetadataKey.SAVE_CODE);
                        }).collect(Collectors.toList());

                    }else{
                        //如果只有签到签退,则删除掉更新和更新并完成任务
                        buttons = buttons.stream().filter(item -> {
                            return !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_AND_COMPLETE)
                                    && !item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.UPDATE_CODE)
                                    && !item.getAction().equalsIgnoreCase(MetadataKey.SAVE_CODE);
                        }).collect(Collectors.toList());
                    }
                }


                return buttons.stream().peek(actionButton -> actionButton.setLabel(I18NUtils.text(actionButton.getLabel(),actionButton.getLabel()))).collect(Collectors.toList());
            }
        },
        approve {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                List<ActionButton> buttons = super.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
                return buttons.stream().peek(actionButton -> actionButton.setLabel(I18NUtils.text(actionButton.getLabel(), actionButton.getLabel()))).collect(Collectors.toList());
            }
        },
        operation {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask task, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                //TODO 该代码H5可能不支持签到签退  @高俊

                //判读需不需要拆分action
                String actionCode = BPMTask.getActionCode(task.getExtension());
                String actionLabel = BPMTask.getActionLabel(task.getExtension());
                Map<String, String> actionCodeAndLabels = OperationTaskButtonHandler.getRealActionCodes(task.isCombinedActionCode(), actionCode, actionLabel);

                Map<String, Boolean> func = serviceManager.hasObjectFunctionPrivilege(task.getEntityId());
                boolean isTransformed = OperationTaskButtonHandler.leadsStatusTransfor(serviceManager, task.getEntityId(), task.getObjectId(), actionCode);

                actionCodeAndLabels = OperationTaskButtonHandler.filterActions(actionCodeAndLabels, func, isTransformed);

                boolean isOpenDeliveryNote = false;
                // 如果发货单开启,将确认发货和确认收货按钮修改为完成任务
                if (MetadataKey.SALES_ORDER_OBJ_API_NAME.equals(task.getEntityId())) {
                    // actioncode 不为空,并且是确认收货和确认发货 则去调用深研 判断是否已开发发货单
                    if (!Strings.isNullOrEmpty(actionCode) && Lists.newArrayList(
                            confirmdelivery.name(),
                            confirmreceive.name()
                    ).contains(actionCode)) {
                        isOpenDeliveryNote = serviceManager.getDeliveryNoteEnable();
                    }
                }

                boolean finalIsOpenDeliveryNote = isOpenDeliveryNote;
                List<ActionButton> buttons = actionCodeAndLabels.entrySet().stream()
                        .map(cAndL -> {
                            if (finalIsOpenDeliveryNote) {
                                String code = confirmdelivery.getI18NDesc();
                                if (confirmreceive.name().equals(cAndL.getKey())) {
                                    code = confirmreceive.getI18NDesc();
                                }
                                task.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_INVOICE_HAS_OPEN.text(code));
                                return new ActionButton(
                                        BPMConstants.Button.Complete.name(),
                                        BPMConstants.Button.Complete.getI18NDesc());
                            }
                            return new ActionButton(cAndL.getKey(), cAndL.getValue());
                        })
                        .collect(Collectors.toList());

                if (isTransformed) {
                    buttons.add(new ActionButton(BPMConstants.Button.Complete.name(), BPMConstants.Button.Complete.getI18NDesc()));
                }
                return buttons;
            }
        },
        addMDObject {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                return addRelatedObject.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
            }
        },
        addRelatedObject {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {

                String relatedEntityId = MapUtil.instance.getString(mTask.getExtension(), ExtensionKey.relatedEntityId);


                Map<String, Object> relatedDescribe = serviceManager.findDescribe(relatedEntityId, false, false);

                boolean isActive = MapUtil.instance.getBool(relatedDescribe, MetadataKey.isActive);
                if (!isActive) {
                    mTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST.text());
                    return Lists.newArrayList();
                }

                if (mTask.onlyRelatedObject()) {
                    mTask.setRelatedDescribe(relatedDescribe);
                }

                //如果被关联的字段删除了或者禁用了,则不下发选择或新建按钮
                String refApiName = MapUtil.instance.getString(mTask.getExtension(), ExtensionKey.relatedFieldApiName);
                if (Strings.isNullOrEmpty(refApiName)) {
                    mTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST.text());
                    return Lists.newArrayList();
                }
                List<ActionButton> buttons = super.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
                buttons.forEach(item -> {
                    if (item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.ADDRELATEDOBJECT_CODE)
                            || item.getAction().equalsIgnoreCase(BPMConstants.MetadataKey.ADDMDOBJECT)) {
                        item.setLabel(
                                I18NUtils.text(item.getLabel(), "选择并新建{0}",mTask.getExtension().get(ExtensionKey.relatedEntityName))//ignoreI18n
                        );
                    }
                });
                return buttons;
            }
        },
        batchAddRelatedObject {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {

                String relatedEntityId = MapUtil.instance.getString(mTask.getExtension(), ExtensionKey.relatedEntityId);
                Map<String, Object> relatedDescribe = serviceManager.findDescribe(relatedEntityId, false, false);
                boolean isActive = MapUtil.instance.getBool(relatedDescribe, MetadataKey.isActive);
                if (!isActive) {
                    mTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST.text());
                    return Lists.newArrayList();
                }

                String refApiName = MapUtil.instance.getString(mTask.getExtension(), ExtensionKey.relatedFieldApiName);
                if (Strings.isNullOrEmpty(refApiName)) {
                    mTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST.text());
                    return Lists.newArrayList();
                }
                List<ActionButton> buttons = super.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
                buttons.forEach(item -> item.setLabel(I18NUtils.text(item.getLabel(), "批量添加关联对象{0}",mTask.getExtension().get(ExtensionKey.relatedEntityName))));//ignoreI18n
                return buttons;
            }
        },
        externalApplyTask {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                if(taskParams.isApplyButtons()){
                   return taskButtonHandler.setButtons(serviceManager, StandardData.createStandardDataByExternalApplyTask(mTask), mTask.assignNextTask(), taskParams).getButtons();
                }
                GetBpmSupportInfo.Result actionCodeSupportInfo = serviceManager.getActionCodeSupportInfo(mTask.getAppCode(), mTask.getActionCode(), taskParams, mTask.getEntityId(), mTask.getObjectId(), mTask.getId());
                log.info("support:{},url:{}", actionCodeSupportInfo.isSupport(), actionCodeSupportInfo.getUrl());
                if (actionCodeSupportInfo.isSupport()) {
                    //手机端url为空的时候 不下发按钮;web端直接下发按钮
                    if (!Strings.isNullOrEmpty(actionCodeSupportInfo.getUrl())||!taskParams.isMobile()) {
                        List<ActionButton> buttons = super.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
                        mTask.setTodoJumpUrl(actionCodeSupportInfo.getUrl());
                        buttons.stream().peek(actionButton -> {
                            if (!Strings.isNullOrEmpty(actionCodeSupportInfo.getLabel())) {
                                actionButton.setLabel(actionCodeSupportInfo.getLabel());
                            } else {
                                actionButton.setLabel(I18NUtils.text(actionButton.getLabel(),"{0}",actionButton.getLabel()));
                            }
                        }).collect(Collectors.toList());
                        return buttons;
                    }
                }
                return Lists.newArrayList();
            }
        },
        batchEditMasterDetailObject {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                List<ActionButton> buttons = super.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
                buttons.forEach(item -> item.setLabel(I18NUtils.text(item.getLabel(),"{0}",item.getLabel())));
                return buttons;
            }
        },
        updateLookup {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                return update.setButtons(serviceManager, mTask, taskParams, taskButtonHandler);
            }
        },
        operationMulti{
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask task, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                return Lists.newArrayList();
            }
        },
        custom {
            @Override
            public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
                return Lists.newArrayList();
            }
        };

        public static boolean isOuterPerson(String userId) {
            Long employeeId = Long.parseLong(userId);
            return employeeId > 100000000;
        }

        @Override
        public List<ActionButton> setButtons(RefServiceManager serviceManager, BPMTask mTask, TaskParams taskParams, TaskButtonHandler taskButtonHandler) {
            return TaskButtonConfigHelper.getTaskFormButtons(mTask.getExecutionType(), mTask.onlyRelatedObject());
        }
    }

}
