package com.facishare.bpm.manage.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.MTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.proxy.AuthServiceProxy;
import com.facishare.bpm.proxy.MetaDataAuthProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.utils.ButtonFunctionUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * Created by Aaron on 04/05/2017.
 */
@Service
@Slf4j
@Data
public class MoreOperationManagerImpl implements MoreOperationManager {

    public static MoreOperation changeBPMApprover = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_CHANGE_OWNER.key, "ChangeBPMApprover");
    public static MoreOperation stopBPM = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_BUTTON_CANCEL_BPM_FLOW.key, "StopBPM");
    public static MoreOperation viewEntireBPM = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_VIEW_ENTIRE.key, "ViewEntireBPM");
    public static MoreOperation viewBPMInstanceLog = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_VIEW_BPM_INSTANCE_LOG.key, "ViewBPMInstanceLog");
    public static MoreOperation discuss = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_BUTTION_FORWARD.key, "Discuss");
    public static MoreOperation refreshHandler = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_BUTTON_REFRESH_HANDLER.key, "RefreshHandler");
    public static MoreOperation addTag = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_BUTTON_ADD_TAG.key, "AddTag");
    public static MoreOperation remind = new MoreOperation(BPMI18N.PAAS_FLOW_BPM_BUTTON_REMIND.key, "Remind");


    @Autowired
    private MetaDataAuthProxy metaDataAuthProxy;
    @Autowired
    private AuthServiceProxy authServiceProxy;
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Override
    public List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, MTask mTask) {
        String entityId = mTask.getTaskExtension().get(ExtensionKey.entityId) + "";
        Map<String, Boolean> privileges = serviceManager.hasObjectFunctionPrivilege(entityId);
        boolean hasPrivilege = privileges.getOrDefault(changeBPMApprover.getCode(), false);
        boolean isNeedChangeBPMApprover = ButtonFunctionUtil.isNeedChangeBPMApprover(
                hasPrivilege,
                mTask.isInProgress(),
                mTask.isError(),
                mTask.getExecution().isError(),
                StringUtils.isNotBlank(mTask.getLinkAppName()),
                !serviceManager.isOuterUserId(), Boolean.TRUE);

        if (isNeedChangeBPMApprover) {
            return Lists.newArrayList(changeBPMApprover.i18nMoreOperation());
        }
        return Lists.newArrayList();
    }

    /**
     * 重构 List<MoreOperation> getTaskMoreOperations(RemoteContext context, MTask mTask)
     *
     * @param serviceManager
     * @param task
     * @return
     */
    @Override
    public List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, BPMTask task) {
        List<MoreOperation> moreOperations = Lists.newArrayList();
        String entityId = task.getExtension().get(ExtensionKey.entityId) + "";
        Map<String, Boolean> funs = serviceManager.hasObjectFunctionPrivilege(entityId);
        boolean needChangeBPMApprover = ButtonFunctionUtil.isNeedChangeBPMApprover(funs.get(
                changeBPMApprover.getCode()),
                task.isInProgress(),
                task.isError(),
                task.getExecution().isError(),
                StringUtils.isNotBlank(task.getLinkAppName()),
                !serviceManager.isOuterUserId(), Boolean.TRUE);

        if (needChangeBPMApprover) {
            moreOperations.add(changeBPMApprover.i18nMoreOperation());
        }
        //950下游也下发转发按钮
        if (serviceManager.isNeedDiscussButton()) {
            moreOperations.add(discuss.i18nMoreOperation());
        }
        return moreOperations;
    }

    @Override
    public List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, TaskDetail taskDetail, Task task) {
        if(ExecutionTypeEnum.execution.equals(taskDetail.getExecutionType())){
            return Lists.newArrayList();
        }
        return getTaskMoreOperations(serviceManager,
                taskDetail.getEntityId(),
                taskDetail.isInProgress(),
                taskDetail.isError(),
                taskDetail.getExecution().isError(),
                StringUtils.isNotBlank(taskDetail.getLinkAppName()),
                (!taskDetail.isExternalApplyTask() || SwitchConfigManager.externalApplyTaskChangeCandidates(serviceManager.getTenantId(), taskDetail.getExternalApplyActionCode())),
                Boolean.TRUE.equals(task.getSequence())? task.getSequenceTaskCandidateIds() : taskDetail.getCandidateIds(),
                taskDetail.isExternalApplyTask(),
                taskDetail.isAfterActionWaiting(),
                task);
    }

    @Override
    public List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, LaneTask laneTask, Task task) {
        if(ExecutionTypeEnum.execution.equals(laneTask.getExecutionType())){
            return Lists.newArrayList();
        }
        return getTaskMoreOperations(serviceManager,
                laneTask.getEntityId(),
                laneTask.isInProgress(),
                laneTask.isError(),
                laneTask.getExecution().isError(),
                StringUtils.isNotBlank(laneTask.getLinkAppName()),
                (!laneTask.isExternalApplyTask() || SwitchConfigManager.externalApplyTaskChangeCandidates(serviceManager.getTenantId(), laneTask.getExternalApplyActionCode())),
                Boolean.TRUE.equals(task.getSequence()) ? task.getSequenceTaskCandidateIds() : laneTask.getCandidateIds(),
                laneTask.isExternalApplyTask(),
                laneTask.isAfterActionWaiting(),
                task);

    }

    private List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager,
                                                      String entityId, boolean isInProgress,
                                                      boolean isError, boolean isExecutionError,
                                                      boolean isLinkAppNode, boolean isSupportTaskType,
                                                      List<String> candidateIds, boolean isExternalApplyTask, boolean isAfterActionWaiting, Task task) {
        List<MoreOperation> moreOperations = Lists.newArrayList();
        //后动作进行中不下发按钮
        if(isAfterActionWaiting){
            return moreOperations;
        }
        Map<String, Boolean> functions = serviceManager.hasObjectFunctionPrivilege(entityId);

        boolean needChangeBPMApprover = ButtonFunctionUtil.isNeedChangeBPMApprover(
                functions.get(changeBPMApprover.getCode()),
                isInProgress,
                isError,
                isExecutionError,
                isLinkAppNode,
                !serviceManager.isOuterUserId(),
                isSupportTaskType);

        if (needChangeBPMApprover) {
            if(isExternalApplyTask){
                if(!(CollectionUtils.isEmpty(candidateIds) || candidateIds.contains(BPMConstants.CRM_SYSTEM_USER) || candidateIds.stream().anyMatch(candidateId -> candidateId.length() >= 8))){
                    moreOperations.add(changeBPMApprover.i18nMoreOperation());
                }
            }else {
                moreOperations.add(changeBPMApprover.i18nMoreOperation());
                //不存在处理人 && 有更换业务流处理人的功能权限    下发"重新解析处理人按钮"
                if(CollectionUtils.isEmpty(candidateIds)){
                    moreOperations.add(refreshHandler.i18nMoreOperation());
                }
            }
        }
        if(ButtonFunctionUtil.isNeedAddTag(serviceManager.getUserId(), task)){
            moreOperations.add(addTag.i18nMoreOperation());
        }
        if(ButtonFunctionUtil.canRemindTask(serviceManager, task)){
            moreOperations.add(remind.i18nMoreOperation());
        }
        //950下游也下发转发按钮
        //人员必须有值 才能支持转发,否则终端会显示  正在加载中,最后返回-1001
        if (!isExternalApplyTask && CollectionUtils.isNotEmpty(candidateIds) && serviceManager.isNeedDiscussButton()) {
            moreOperations.add(discuss.i18nMoreOperation());
        }
        return moreOperations;
    }


    @Override
    public List<MoreOperation> getInstanceMoreOperations(RefServiceManager serviceManager, Map<String, Boolean> functionPrivilegeRst,
                                                         WorkflowInstance workflowInstance) {
        List<MoreOperation> moreOperations = Lists.newArrayList();
        if (functionPrivilegeRst.getOrDefault(viewBPMInstanceLog.getCode(), false)) {
            moreOperations.add(viewBPMInstanceLog.i18nMoreOperation());
        }
        setInstanceMoreOperations(serviceManager, moreOperations, functionPrivilegeRst, workflowInstance);
        return moreOperations;
    }


    private void setInstanceMoreOperations(RefServiceManager serviceManager, List<MoreOperation> moreOperations,
                                           Map<String, Boolean> functionPrivilegeRst, WorkflowInstance workflowInstance) {
        String entityId = workflowInstance.getEntityId() + "";
        if (MapUtils.isEmpty(functionPrivilegeRst)) {
            log.info("context: {},获取 {} 的功能权限为空", serviceManager.getContext(), entityId);
            //950下游也下发转发按钮
            if (serviceManager.isNeedDiscussButton()) {
                moreOperations.add(discuss.i18nMoreOperation());
            }
            return;
        }
        boolean stopBpmFunction = functionPrivilegeRst.getOrDefault(stopBPM.getCode(), false);
        //看是否为实例后动作异常,实例后动作异常的话,则不下发终止按钮
        boolean instanceAfterError = workflowInstance.isInstanceAfterError();
        //实例后动作不是error && 有权限（只走有  终止业务流程  功能权限---7.9.5）
        if (!instanceAfterError && ButtonFunctionUtil.isNeedStopBPM(!workflowInstance.isCompleted(),stopBpmFunction)) {
            moreOperations.add(stopBPM.i18nMoreOperation());
        }
        if (functionPrivilegeRst.getOrDefault(viewEntireBPM.getCode(), false)) {
            moreOperations.add(viewEntireBPM.i18nMoreOperation());
        }
        //950下游也下发转发按钮
        if (serviceManager.isNeedDiscussButton()) {
            moreOperations.add(discuss.i18nMoreOperation());
        }
    }

    @Override
    public void setTaskMoreOperationsWithInstanceOperation(RefServiceManager serviceManager, List<TaskOutline> tasks, boolean isOwner) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        Set<String> entryEntityIds = tasks.stream().map(TaskOutline::getEntryType).collect(Collectors.toSet());
        tasks.forEach(taskOutline -> entryEntityIds.add(taskOutline.getEntityId()));
        tasks.forEach(taskOutline -> {
            Map<String, Boolean> functionPrivilege = serviceManager.hasObjectFunctionPrivilege(taskOutline.getEntryType());
            List<MoreOperation> moreOperations = Lists.newArrayList();
            if (functionPrivilege.getOrDefault(viewBPMInstanceLog.getCode(), false)) {
                moreOperations.add(viewBPMInstanceLog.i18nMoreOperation());
            }
            if (functionPrivilege.getOrDefault(viewEntireBPM.getCode(), false)) {
                moreOperations.add(viewEntireBPM.i18nMoreOperation());
            }
            if (taskOutline.starting()) {
                boolean stopFunction = functionPrivilege.getOrDefault(stopBPM.getCode(), false);
                //只有  终止业务流程  功能权限可终止业务流---7.9.0
                if (ButtonFunctionUtil.isNeedStopBPM(taskOutline.starting(), stopFunction)) {
                    moreOperations.add(stopBPM.i18nMoreOperation());
                }

                boolean needChangeBPMApprover = ButtonFunctionUtil.isNeedChangeBPMApprover(
                        functionPrivilege.get(changeBPMApprover.getCode()),
                        taskOutline.isInProgress(),
                        taskOutline.isError(),
                        false,
                        StringUtils.isNotBlank(taskOutline.getLinkAppName()),
                        !serviceManager.isOuterUserId(),
                        !taskOutline.getExecutionTypeEnum().equals(ExecutionTypeEnum.externalApplyTask));

                if (needChangeBPMApprover) {
                    moreOperations.add(changeBPMApprover.i18nMoreOperation());
                }
            }
            //950 下游也下发转发按钮
            if (serviceManager.isNeedDiscussButton()) {
                moreOperations.add(discuss.i18nMoreOperation());
            }
            taskOutline.setMoreOperations(moreOperations);
        });
    }

    @Override
    public List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, Task task){
        if(Objects.isNull(task) || ExecutionTypeEnum.execution.equals(task.getExecutionType())){
            return Lists.newArrayList();
        }
        return getTaskMoreOperations(serviceManager,
                task.getEntityId(),
                TaskState.in_progress.equals(task.getState()),
                TaskState.error.equals(task.getState()),
                Objects.nonNull(task.getExecution()) && task.getExecution().getErrorOrWaitingSimpleAfter().isError(),
                StringUtils.isNotBlank(task.getLinkAppName()),
                (!ExecutionTypeEnum.externalApplyTask.equals(task.getExecutionType()) || SwitchConfigManager.externalApplyTaskChangeCandidates(serviceManager.getTenantId(), task.getExternalApplyActionCode())),
                Boolean.TRUE.equals(task.getSequence()) ? task.getSequenceTaskCandidateIds() : task.getCandidateIds(),
                ExecutionTypeEnum.externalApplyTask.equals(task.getExecutionType()),
                task.isAfterActionWaiting(),
                task);
    }



}
