package com.facishare.bpm.manage.impl;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.TransitionExt;
import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.bpmn.condition.ConditionExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.DefineGenerateManager;
import com.facishare.bpm.model.BpmConstant;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.util.verifiy.handler.bean.BPMFieldBean;
import com.facishare.bpm.util.verifiy.util.CustomVariableUtil;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.bpm.utils.ExpressionUtil;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.bpmn.definition.model.CustomVariable;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.stage.api.model.contant.StageConstants;
import com.github.trace.TraceContext;
import com.github.trace.TraceRecorder;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * http://git.firstshare.cn/fe-paas/paasui/blob/master/src/modules/utils/utils.js
 */
@Slf4j
@Service
public class DefineGenerateManagerImpl implements DefineGenerateManager {

    private final String activityPre = "activity_";

    @Override
    public void generateVariables(WorkflowOutline outline, RefServiceManager serviceManager) {
        ExecutableWorkflowExt executableWorkflowExt = outline.getWorkflow();
        Set<VariableExt> variableExts = Sets.newHashSet();
        // 1.生成activity_0##enittyId
        variableExts.add(new VariableExt("activity_0##" + outline.getEntryType(), BPMConstants.MetadataKey.TEXT));
        generateActivities(executableWorkflowExt.getActivities(), variableExts);
        generateTransitions(executableWorkflowExt.getTransitions(), variableExts,executableWorkflowExt.getCustomVariables(),serviceManager);
        setVarByDiff(serviceManager.getTenantId(),outline.getWorkflowId(),outline.getSourceWorkflowId(),variableExts, executableWorkflowExt.getVariables());
//        generateVariablesType(executableWorkflowExt.getVariables(), serviceManager);
        //TODO 后期再放开
        //executableWorkflowExt.setVariables(setVarByDiff(variableExts, executableWorkflowExt.getVariables()));
        //outline.setExecutableWorkflowExtFromOutline(executableWorkflowExt);
    }
    /**
     * 判断定义中 variables 的 type
     * @param serviceManager
     * @param variables
     */
    private void generateVariablesType(List<VariableExt> variables, RefServiceManager serviceManager){
        Map<String, VariableExt> variablesMap = variables.stream().collect(Collectors.toMap(VariableExt::getId, v -> v));
        StringBuilder errorMsg = new StringBuilder();
        variablesMap.forEach((expression, variableExt)->{
            if(expression.contains("field")){
                String entityId = expression.split(UtilConstans.WELL)[1];
                String field = expression.split(UtilConstans.WELL)[2];
                Map<String, Object> fieldDesc = serviceManager.getFieldDesc(entityId, field);
                if(Objects.isNull(fieldDesc)){
                    log.warn("field is not found entityId:{},field:{}", entityId, field);
                    return;
                }
                if(!variableExt.getTypeName().equals(getEngineType((String) fieldDesc.get(UtilConstans.TYPE)))){
                    errorMsg.append(BPMI18N.PAAS_FLOW_BPM_WORKFLOW_VARIABLES_TYPE_ERROR.text(fieldDesc.get("field_label"))).append(";\n");
                }
            }
        });
        if(!Strings.isNullOrEmpty(errorMsg.toString())){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, errorMsg);
        }
    }

    /**
     * @param generateVariables 为自动生成的
     * @param variables         为前端生成的
     * @return 如果前端传递的没有, 后端插入进去
     */
    private List<VariableExt> setVarByDiff(String tenantId,String workflowId,String sourceWorkflowId,Set<VariableExt> generateVariables, List<VariableExt> variables) {
        Map<String, VariableExt> generateMap = generateVariables.stream().collect(Collectors.toMap(VariableExt::getId, v -> v));
        Map<String, VariableExt> variablesMap = variables.stream().collect(Collectors.toMap(VariableExt::getId, v -> v));

        Collection rdVariable = CollectionUtils.subtract(generateMap.keySet(), variablesMap.keySet());
        if (CollectionUtils.isNotEmpty(rdVariable)) {
            log.info("tenantId:{} ,workflowId:{},sourceWorkflowId:{},后端variables补充 key:{}",tenantId,workflowId,sourceWorkflowId, JacksonUtil.toJson(rdVariable));
        }

        Collection feVariable = CollectionUtils.subtract(variablesMap.keySet(), generateMap.keySet());
        if (CollectionUtils.isNotEmpty(feVariable)) {
            log.info("tenantId:{} ,workflowId:{},sourceWorkflowId:{},后端漏掉variables key:{}",tenantId,workflowId,sourceWorkflowId, JacksonUtil.toJson(feVariable));

        }

        variablesMap.forEach((key, value) -> {
            VariableExt generateVariable = generateMap.get(key);
            if (Objects.nonNull(generateVariable)) {
                if (!value.getTypeName().equals(generateVariable.getTypeName())) {
                    log.error("前后端生成variable类型不匹配,后端{}:前端{}",
                            JacksonUtil.toJson(generateVariable), JacksonUtil.toJson(value));
                    TraceRecorder.getInstance().post(TraceContext.get()
                            .copy()
                            .setMethod("generateVariables")
                            .setServerName("DefineGenerateManager")
                            .setFail(true));

                }
            }
        });

        return Lists.newArrayList(variables);
    }

    /**
     * 将transitions上的变量,添加到var中
     * <p>
     * 目前支持and 和or
     * <p>
     * and 下只有一个conditions
     * or 下有两个conditions
     *
     * @param transitions
     * @param variableExts
     * @param serviceManager
     */
    private void generateTransitions(List<TransitionExt> transitions, Set<VariableExt> variableExts, List<CustomVariable> customVariables, RefServiceManager serviceManager) {
        transitions.forEach(transitionExt -> {
            ConditionExt rootConditionExt = transitionExt.getCondition();
            generateConditionExt(rootConditionExt, variableExts, customVariables,serviceManager);
        });
    }

    private void generateConditionExt(ConditionExt condition, Set<VariableExt> variableExts,List<CustomVariable> customVariables, RefServiceManager serviceManager) {
        if (condition != null) {
            List<ConditionExt> conditionExts = condition.getConditions();
            if (CollectionUtils.isNotEmpty(conditionExts)) {
                conditionExts.forEach(conditionExt -> {
                    generateConditionExt(conditionExt, variableExts,customVariables, serviceManager);
                });
            } else {
                setVariableOfExpression(condition, variableExts,customVariables, serviceManager);
            }
        }
    }


    private void setVariableOfExpression(ConditionExt conditionExt, Set<VariableExt> variableExts, List<CustomVariable> customVariables, RefServiceManager serviceManager) {
        ConditionExt.ValueExt valueExt = conditionExt.getLeft();
        //这里是设置variable 自定义的没有依据设置varaible,只能从customVariable放到variable 意义不大
        if (!CustomVariableUtil.isCustomVariable(valueExt.getExpression())) {
            // 通过字段获取类型
            String nameType = getType(valueExt.getExpression(), serviceManager);
            if (Strings.isNullOrEmpty(nameType)) {
                return;
            }
            VariableExt variableExt = new VariableExt(valueExt.getExpression(), nameType);
            if (BPMConstants.EngineVariableType.LIST.equals(nameType)) {
                Map<String, String> elementType = Maps.newHashMap();
                elementType.put(BPMConstants.MetadataKey.name, BPMConstants.MetadataKey.TEXT);

                variableExt.put(BPMConstants.ELEMENT_TYPE, elementType);
            }
            variableExts.add(variableExt);
        }

    }

    private List<String> otherExpression = Lists.newArrayList("@OWNER_MAIN_DEPT_PATH");

    /**
     * @param expression     表达式
     * @param serviceManager
     * @return
     */
    private String getType(String expression, RefServiceManager serviceManager) {
        expression = ExpressionUtil.getInternalExpression(expression);
        //负责人所属主部门,直接是list
        BPMFieldBean bpmFieldBean = BPMFieldBean.analysisFieldExpression(Splitter.on(UtilConstans.WELL).splitToList(ValidateVariableAndContentUtils.getInnerKeyByFirst(expression)));
        if (otherExpression.contains(bpmFieldBean.getMainField())) {
            return BPMConstants.EngineVariableType.LIST;
        }
        //||BPMConstants.LATENCY_RESULT.equals(bpmFieldBean.getEntityId()) ? returnType?
        if (BPMConstants.ApproveResult.RESULT.equals(bpmFieldBean.getEntityId())||BPMConstants.EXECUTIONTYPE.equals(bpmFieldBean.getEntityId())) {
            return BPMConstants.MetadataKey.TEXT;
        }
        String fieldType;
        //如果不是关联的,则只获取当前对象的字段进行校验

        BPMFieldBean.MainObjOrField mainObjOrField = BPMFieldBean.getMainObjOrField(bpmFieldBean, serviceManager);

        Map<String, Object> fieldDesc = serviceManager.getFieldDesc(mainObjOrField.getEntityId(), mainObjOrField.getField());
        if(Objects.isNull(fieldDesc)){
            log.warn("field is not found entityId:{},field:{}", mainObjOrField.getEntityId(), mainObjOrField.getField());
            return null;
        }
        fieldType = (String) fieldDesc.get(UtilConstans.TYPE);

        // 如果是计算字段和统计字段,使用它们的resultType
        if (BPMConstants.MetadataKey.FORMULA.equals(fieldType) || BPMConstants.MetadataKey.COUNT.equals(fieldType)) {
            fieldType = (String) fieldDesc.get(BPMConstants.MetadataKey.RETURN_TYPE);
        }

        // 如果是引用字段，使用其quote_field_type
        if (BpmConstant.QUOTE.equals(fieldType)) {
            String quoteFieldType = (String) fieldDesc.get(BpmConstant.QUOTE_FIELD_TYPE);
            if ("image".equals(quoteFieldType) || "file_attachment".equals(quoteFieldType) || "big_file_attachment".equals(quoteFieldType)) {
                fieldType = (String) fieldDesc.get(BpmConstant.QUOTE_FIELD_TYPE);
            }
        }

        return getEngineType(fieldType);
    }

    private String getEngineType(String fieldType) {
        switch (fieldType) {
            case "number":
            case "currency":
            case "date":
            case "date_time":
            case "time":
            case "percentile":
                fieldType = BPMConstants.EngineVariableType.NUMBER;
                break;
            case "auto_number":
                fieldType = BPMConstants.EngineVariableType.TEXT;
                break;
            case "true_or_false":
                fieldType = BPMConstants.EngineVariableType.BOOLEAN;
                break;
            case "select_many":
            case StageConstants.MetadataKey.OBJECT_REFERENCE_MANY:
            case BpmConstant.OUT_EMPLOYEE:
            case "employee":
            case "employee_many":
            case "department":
            case "department_many":
            case "image":
            case "file_attachment":
            case "big_file_attachment":
            case "dimension":
            case "dimension_d1":
            case "dimension_d2":
            case "dimension_d3":
                fieldType = BPMConstants.EngineVariableType.LIST;
                break;
            default:
                fieldType = BPMConstants.EngineVariableType.TEXT;
                break;
        }
        return fieldType;
    }


    /**
     * 将activities上的变量,添加到var中
     * <p>
     * 应用节点
     * 业务节点
     * 审批节点
     * 会签节点
     *
     * @param userTaskExts
     * @param variableExts
     */
    private void generateActivities(List<ActivityExt> userTaskExts, Set<VariableExt> variableExts) {
        userTaskExts.forEach(userTaskExt -> {

            if (!BPMConstants.userTask.equals(userTaskExt.getType())) {
                return;
            }

            String id = userTaskExt.getId();
            String entityId = userTaskExt.getEntityId();

            // 2. 所有任务节点 都将当前节点的对象id添加到var中
            variableExts.add(new VariableExt(activityPre + id + UtilConstans.WELL + entityId, BPMConstants.MetadataKey.TEXT));

            //3. 是关联对象或者主从的时候,则也需要放到var中
            if (ExecutionTypeEnum.isMDObjectOrRelated(userTaskExt.getExtensionType().name())) {
                //activity_1##SelfRef##EntityId
                // 4. 如果是自关联,则添加SelfRef
                String relatedEntityId = userTaskExt.getRelatedEntityId();
                if (entityId.equals(relatedEntityId)) {
                    variableExts.add(new VariableExt(activityPre + id + UtilConstans.WELL + BPMConstants.SELF_REF_KEY + UtilConstans.WELL + relatedEntityId, BPMConstants.MetadataKey.TEXT));
                } else {
                    variableExts.add(new VariableExt(activityPre + id + UtilConstans.WELL + relatedEntityId, BPMConstants.MetadataKey.TEXT));
                }
            }
            ExecutionTypeEnum executionType = userTaskExt.getExtensionType();
            //7. 如果executionType=approve,则向var中添加result
            if (executionType.isApprove()) {
                variableExts.add(new VariableExt(activityPre + id + UtilConstans.WELL + BPMConstants.ApproveResult.RESULT, BPMConstants.MetadataKey.TEXT));
            }
        });
    }


}
