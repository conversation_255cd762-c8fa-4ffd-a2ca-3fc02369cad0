package com.facishare.bpm.manage;

import com.facishare.bpm.exception.BPMLoopBreakerException;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BPMLoopBreakerManager {

    @Autowired
    private RedisManager redisManager;

    //http://redisdoc.com/string/setex.html
    public void incAndCheck(RemoteContext context, String outlineId, String entityId, String objectId) {
        String key = Joiner.on("_").join(context.getTenantId(), entityId, objectId, outlineId);
        boolean success = redisManager.setValueWithExpireTime(key, "1", SwitchConfigManager.getSwitchConfig().getDurationSeconds());
        log.info("key:{},success:{}",key,success);
        Long count = 0L;
        if (!success) {
            count = redisManager.incrBy(key, 1L);
        }
        log.info("key:{},success:{},count:{}",key,success,count);
        if (count > SwitchConfigManager.getSwitchConfig().getLimitCount()) {
            log.warn("key:{},已经达到触发上限,最大触发上限为:{}", key, SwitchConfigManager.getSwitchConfig().getLimitCount());
            throw new BPMLoopBreakerException();
        }
    }
}

