package com.facishare.bpm.manage;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.model.task.MTask;
import com.facishare.bpm.model.task.TaskDetail;
import com.facishare.bpm.utils.ButtonFunctionUtil;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import static com.facishare.bpm.manage.impl.MoreOperationManagerImpl.remind;

/**
 * Created by <PERSON> on 26/04/2017.
 */
public interface MoreOperationManager {

    /**
     * 1. 数据权限
     * 2. 操作权限
     */
    List<MoreOperation> getTaskMoreOperations(
            RefServiceManager serviceManager,
            MTask mTask
    );

    List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, BPMTask task);

    List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, TaskDetail taskDetail, Task task);

    List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, LaneTask laneTask, Task task);

    List<MoreOperation> getInstanceMoreOperations(RefServiceManager serviceManager, Map<String, Boolean> functionPrivilegeRst,
                                                  WorkflowInstance workflowInstance);

    void setTaskMoreOperationsWithInstanceOperation(
            RefServiceManager serviceManager,
            List<TaskOutline> tasks,
            boolean isOwner
    );

    static List<MoreOperation> getTaskLogMoreOperations(RefServiceManager serviceManager, Task task){
        return ButtonFunctionUtil.canRemindTask(serviceManager, task) ? Lists.newArrayList(remind.i18nMoreOperation()) : null;
    }

    List<MoreOperation> getTaskMoreOperations(RefServiceManager serviceManager, Task task);

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class MoreOperation {
        private String label;
        private String code;

        public MoreOperation i18nMoreOperation() {
            String cnLable = I18N.text(this.getLabel());
            return new MoreOperation(cnLable, code);
        }
    }
}
