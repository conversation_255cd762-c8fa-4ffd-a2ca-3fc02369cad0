package com.facishare.bpm.manage.impl;

import com.facishare.bpm.manage.RedisManager;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2018/12/27 6:01 PM
 */
@Slf4j
@Service
public class RedisManagerImpl implements RedisManager {


    @Qualifier("afterRedisClient")
    @Autowired
    private MergeJedisCmd redisClient;

    private static final int EXPIRE_TIME = 15; //超时时间设置为5s
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "EX";
    private static final String LOCK_SUCCESS = "OK";


    @Override
    public boolean setValueWithExpire(String key, String data) {
        String result = redisClient.set(key, data, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, EXPIRE_TIME);
        return LOCK_SUCCESS.equals(result);
    }


    @Override
    public boolean setValueWithExpire(String key, String data, int expireTime) {
        String result = redisClient.set(key, data, SET_IF_NOT_EXIST, "PX", expireTime);
        return LOCK_SUCCESS.equals(result);
    }

    @Override
    public boolean setValueWithExpireTime(String key, String data, int expireTime) {
        String result = redisClient.set(key, data, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);
        log.info("setValueWithExpireTime,key:{},data:{},expireTime:{},result:{}",key,data,expireTime,result);
        return LOCK_SUCCESS.equals(result);
    }

    @Override
    public Map<String, String> getValueMapByKey(String key) {
        redisClient.hgetAll(key);
        return redisClient.hgetAll(key);
    }

    @Override
    public boolean setValueOfMap(String key, Map<String, String> value) {
        String result = redisClient.hmset(key, value);
        redisClient.expire("FOMF.BASESITE", EXPIRE_TIME);
        return LOCK_SUCCESS.equals(result);
    }

    @Override
    public long delete(String key) {
        if (Strings.isNullOrEmpty(key)) {
            return 0L;
        }
        Long del = redisClient.del(key);
        log.info("del,key:{},del:{}",key,del);
        return del;
    }

    @Override
    public Long incrBy(String key, Long number) {
        return redisClient.incrBy(key, number);
    }

}
