package com.facishare.bpm.manage.impl;

import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.manage.ButtonCustomManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ButtonCustomManagerImpl extends ApplicationObjectSupport implements ButtonCustomManager {

    private Map<ExecutionTypeEnum, TaskButtonHandler> handlers;


    @PostConstruct
    public void init() {
        Map<String, TaskButtonHandler> taskCompleteHandler = getApplicationContext().getBeansOfType(TaskButtonHandler.class);
        handlers = taskCompleteHandler.values().stream().collect(Collectors.toMap(TaskButtonHandler::getTaskType, k -> k));
    }

    @Override
    public TaskButtonHandler getHandler(ExecutionTypeEnum executionTypeEnum) {
        return handlers.get(executionTypeEnum);
    }
}
