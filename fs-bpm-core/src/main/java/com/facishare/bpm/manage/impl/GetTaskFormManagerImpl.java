package com.facishare.bpm.manage.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.form.UserFormToMetadataManager;
import com.facishare.bpm.handler.task.form.model.CustomerData;
import com.facishare.bpm.handler.task.form.model.FormAssembleType;
import com.facishare.bpm.manage.GetTaskFormManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.LaneTask;
import com.facishare.bpm.utils.DescribeUtil;
import com.facishare.bpm.utils.DescribeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GetTaskFormManagerImpl extends ApplicationObjectSupport implements GetTaskFormManager {

    private Map<FormAssembleType, UserFormToMetadataManager> handlers;

    @PostConstruct
    public void init() {
        Map<String, UserFormToMetadataManager> taskCompleteHandler = getApplicationContext().getBeansOfType(UserFormToMetadataManager.class);
        handlers = taskCompleteHandler.values().stream().collect(Collectors.toMap(UserFormToMetadataManager::getTaskType, k -> k));
    }

    @Override
    public CustomerData getData(RefServiceManager serviceManager, String entityId, String objectId, Map<String, Object> extension, TaskParams taskParams) {
        List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) extension.get(WorkflowKey.ActivityKey.ExtensionKey.form);

        if (CollectionUtils.isEmpty(forms)) {
            return new CustomerData();
        }
        Map<String, Object> data = handlers.get(FormAssembleType.data).execute(serviceManager, entityId, objectId, forms, taskParams);
        Map<String, Object> describe = handlers.get(FormAssembleType.describe).execute(serviceManager, entityId, objectId, forms, taskParams);
        Map<String, Object> layout = getLayout(serviceManager, entityId, objectId, extension, taskParams);
        Map<String, Object> describeExt =  handlers.get(FormAssembleType.describeExt).execute(serviceManager, entityId, objectId, forms, taskParams);
        DescribeUtil.replaceDescFieldLabelByFieldMapping(serviceManager, describe, (String) data.get(BPMConstants.RECORD_TYPE));
        DescribeUtil.replaceDescFieldLabelByFieldMapping(serviceManager, describeExt, (String) data.get(BPMConstants.RECORD_TYPE));
        DescribeUtils.mergeExtra(describe, describeExt);
        return new CustomerData(data, layout, describe, describeExt);
    }

    @Override
    public Map<String, Object> getLayout(RefServiceManager serviceManager, String entityId, String objectId, Map<String, Object> extension, TaskParams taskParams) {
        List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) extension.get(WorkflowKey.ActivityKey.ExtensionKey.form);
        return handlers.get(FormAssembleType.layout).execute(serviceManager, entityId, objectId, forms, taskParams);
    }


    @Override
    public void setForm(RefServiceManager serviceManager, Task task, TaskParams taskParams, LaneTask laneTask, String entityId, String objectId, StandardData standardData, Map<String, Object> extension) {
        //判断是否是应用了流程布局  需要下发流程对象布局信息
        if (task.isUsedLayout()) {
            standardData.initFlowLayoutData(task);
            laneTask.setHasForm(task.getObjectFlowLayoutExists());
        }else {
            if(Boolean.TRUE.equals(taskParams.getNotGetDatas())){
                //下发是否有form的标识
                laneTask.setHasForm(task.existForm().isFormExistsNormalFields());
            }else {
                try {
                    TaskHelper.setTaskForm(serviceManager, task.getCompleted(), extension, entityId, objectId);
                    // 设置form 如果有error  则提示error
                    standardData.initCustomerData(getData(serviceManager, entityId, objectId, extension, TaskParams.create()));
                }catch (Exception e){
                    log.warn("",e);
                }
                /**
                 * 兼容enableLayoutRules 为null
                 */
                standardData.setEnableLayoutRules(MapUtils.getBooleanValue(extension, BPMConstants.ENABLELAYOUTRULES));
            }

        }
    }


}