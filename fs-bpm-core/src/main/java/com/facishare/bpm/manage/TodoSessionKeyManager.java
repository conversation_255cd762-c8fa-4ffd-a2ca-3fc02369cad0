package com.facishare.bpm.manage;

import com.alibaba.druid.util.StringUtils;
import com.facishare.bpm.model.paas.engine.approvalflow.CRMConstants;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.proxy.EServiceResourceProxy;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.flow.postgre.BPMTaskDao;
import com.facishare.rest.core.model.RemoteContext;
import com.github.autoconf.helper.ConfigHelper;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;


/**
 * 760 添加 工单对象获取sessionKey
 * <p>
 * 待审批的工单【新】 业务流工单对象的审批节点和会签节点的任务 sessionKey= 457WaitApproval
 * 待指派的工单【新】 应用节点调用深研接口返回actionCode=waitAssign的工单任务  sessionKey= 457WaitAssign
 * 待执行外勤的工单   应用节点调用深研接口返回actionCode=waitCheckins的工单任务  sessionKey= 457WaitCheckins
 * 待处理的工单【新】 业务流工单对象的业务节点任务 || 应用节点调用深研接口返回actionCode不为waitAssign和waitCheckins  返回:waitDeal   sessionKey= 457WaitDeal
 * 待接单的工单   应用节点调用深研接口返回actionCode=waitToOrder的工单任务  sessionKey= 457WaitToOrder
 */
@Slf4j
@Component
public class TodoSessionKeyManager {

    private static final String WAIT_ASSIGN = "waitAssign", WAIT_CHECKINS = "waitCheckins", WAIT_DEAL = "waitDeal", WAIT_TO_ORDER = "waitToOrder";

    @Autowired
    private EServiceResourceProxy eServiceResourceProxy;
    @Autowired
    private BPMTaskDao bpmTaskDao;
    @Qualifier("afterRedisClient")
    @Autowired
    private MergeJedisCmd redisClient;

    public String getSessionKey(RemoteContext context, String entityId, ExecutionTypeEnum executionType, String actionCode, Integer externalFlow, String taskId, String elementApiName) {
        log.info("get session key,taskId:{},entityId:{},executionType:{},actionCode:{},externalFlow:{}",taskId,entityId,executionType,actionCode,externalFlow);

        //对象!=工单 || 是外部流程
        //待办2.0灰度开启所有待办都是457
        if (!BPMConstants.MetadataKey.CASESOBJ_API_NAME.equals(entityId) || SwitchConfigManager.getSessionKeyCombine(context.getTenantId())) {
            return "";
        }
        //是自定义节点走单独的sessionKey配置
        if(ExecutionTypeEnum.custom.equals(executionType)){
            return SwitchConfigManager.getCustomElementSessionKey(elementApiName);
        }
        //如果是应用节点
        if (ExecutionTypeEnum.externalApplyTask.equals(executionType)) {
            if (Strings.isEmpty(actionCode)) {
                log.warn("actionCode is null,{},taskId:{},entityId:{},externalFlow:{}", context,taskId, entityId, externalFlow);
                return "";
            }
            //郭明回复一定会返回一下3个值
            String todoType = eServiceResourceProxy.getAppActionTodoType(context, entityId, actionCode, externalFlow);
            switch (todoType) {
                case WAIT_ASSIGN:
                    return "457WaitAssign";
                case WAIT_CHECKINS:
                    return "457WaitCheckins";
                case WAIT_DEAL:
                    return "457WaitDeal";
                case WAIT_TO_ORDER:
                    return "457WaitToOrder";
                default:
                    log.warn("getTodoType is null,tenantId:{},taskId:{},entityId:{},actionCode:{},externalFlow:{}", context.getTenantId(), taskId,entityId, actionCode, externalFlow);
                    return "";
            }
        }

        //如果是审批节点/会签节点
        if (ExecutionTypeEnum.approve.equals(executionType)) {
            return "457WaitApproval";
        }

        // 其他一律返回
        return "457WaitDeal";
    }

    public void updateSessionKey(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            log.warn("tenantId is empty");
            return;
        }

        String appName = ConfigHelper.getProcessInfo().getAppName();
        if(!appName.contains("processor") && !appName.contains("cloud")) {
            return;
        }

        String key = tenantId + "_update_session_key";
        String lockValue = String.valueOf(System.currentTimeMillis());

        // 尝试获取分布式锁，设置过期时间为5分钟
        boolean lockAcquired = "OK".equals(redisClient.set(key, lockValue, SetParams.setParams().nx().ex(300L)));

        // 不释放锁，因为如果配置中心同步速度慢，会导致锁过期，其他节点会获取到锁，导致重复更新
        if (lockAcquired) {
            try {
                // 获取锁成功，执行更新操作
                RemoteContext context = new RemoteContext(null, tenantId, CRMConstants.APP_ID, CRMConstants.SYSTEM);
                bpmTaskDao.updateSessionKeyTo457(context);
                log.info("更新企业{}的sessionKey成功", tenantId);
            } catch (Exception e) {
                log.error("更新企业{}的sessionKey失败", tenantId, e);
            }
        } else {
            log.info("企业{}的sessionKey更新操作已在进行中，跳过本次更新", tenantId);
        }
    }
}
