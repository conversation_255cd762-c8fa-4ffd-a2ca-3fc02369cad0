package com.facishare.bpm.manage.impl;

import com.facishare.bpm.handler.task.data.TaskDataHandler;
import com.facishare.bpm.manage.TaskDataManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 7.2.0
 * Created by wansong on 2020/6/23 18:35 PM
 */
@Service
public class TaskDataManagerImpl extends ApplicationObjectSupport implements TaskDataManager {

    private Map<ExecutionTypeEnum, TaskDataHandler> handlers;

    @PostConstruct
    public void init() {
        Map<String, TaskDataHandler> taskCompleteHandler = getApplicationContext().getBeansOfType(TaskDataHandler.class);
        handlers = taskCompleteHandler.values().stream().collect(Collectors.toMap(TaskDataHandler::getTaskType, k -> k));
    }

    @Override
    public <E,R> TaskDataHandler<E,R> getHandler(ExecutionTypeEnum executionTypeEnum) {
        return handlers.get(executionTypeEnum);
    }

}
