package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;

/**
 * Created by wangz on 17-2-25.
 */
public interface BPMTemplateService {
    /**
     * 6.2 以后建议使用
     *
     * @param page
     * @return
     */
    @Deprecated
    PageResult<WorkflowOutline> getTemplateList(String tenantId, Page page);

    /**
     * 包含校验其是否可以支持外部流程的逻辑
     * @param serviceManager
     * @param page
     * @return
     */
    PageResult<WorkflowOutline> getTemplateList(RefServiceManager serviceManager, Page page);

    WorkflowOutline getTemplateDetail(RefServiceManager serviceManager, String id);
}
