package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;

import java.util.List;
import java.util.Map;


/**
 * Created by <PERSON> on 26/02/2017.
 */
public interface BPMWorkflowDraftService {


    PageResult<WorkflowOutlineDraft> getWorkflowDrafts(RefServiceManager serviceManager, WorkflowOutlineQuery query, Page page, boolean supportPagingQuery);

    WorkflowOutlineDraft getWorkflowDraftById(RefServiceManager serviceManager, String id);

    String saveWorkflowDraft(RefServiceManager serviceManager, WorkflowOutlineDraft outline, boolean needValidateQuota);

    void updateWorkflowDraft(RefServiceManager serviceManager, WorkflowOutlineDraft outline);

    void delete(RefServiceManager serviceManager, String id);

    Map<String, String> getWorkflowDraftMapByOutlineIds(RefServiceManager serviceManager, List<String> outlineIds);

    int getDraftUseQuota(RefServiceManager serviceManager);

    long getDraftQuota(RefServiceManager serviceManager);
}
