package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.tenant.DefinitionConfig;
import com.facishare.bpm.service.BPMTemplateService;
import com.facishare.bpm.service.BPMTenantService;
import com.facishare.flow.mongo.bizdb.WorkflowTemplateDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTemplateEntity;
import com.facishare.paas.I18N;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-2-25.
 */
@Service
public class BPMTemplateServiceImpl implements BPMTemplateService {
    @Autowired
    private WorkflowTemplateDao templateDao;
    @Autowired
    private BPMTenantService tenantService;


    @Override
    public PageResult<WorkflowOutline> getTemplateList(String tenantId, Page page) {
        if (null == page) {
            page = new Page();
            page.setAsc(true);
            page.setOrderBy(WorkflowTemplateEntity.Fields.createTime);
        }
        PageResult<WorkflowTemplateEntity> result = templateDao.getPageResult(tenantId, page, false);
        PageResult<WorkflowOutline> pageResult = new PageResult<>();
        pageResult.setTotal(result.getTotal());
        pageResult.setDataList(result.getDataList().stream().map(item -> WorkflowOutline.fromBrieflyEntity(item)).collect(Collectors.toList()));
        return pageResult;
    }

    @Override
    public PageResult<WorkflowOutline> getTemplateList(RefServiceManager serviceManager, Page page) {
        if (null == page) {
            page = new Page();
            page.setPageSize(100);
            page.setAsc(true);
            page.setOrderBy(WorkflowTemplateEntity.Fields.createTime);
        }
        //获取企业所有支持业务流程的对象的信息

        Set<String> entities = serviceManager.getSimpleEntityNames().keySet();
        DefinitionConfig config = tenantService.getDefinitionConfig(serviceManager);
        String language = I18N.getContext().getLanguage();
        PageResult<WorkflowTemplateEntity> result = templateDao.getPageResult(serviceManager.getTenantId(), page, false, config.isSupportExternalFlow(), language);
        PageResult<WorkflowOutline> pageResult = new PageResult<>();
        pageResult.setTotal(result.getTotal());
        //过滤掉不支持的对象的模板
        pageResult.setDataList(result.getDataList().stream().filter(item -> entities.contains(item.getEntryType()))
                .map(item -> WorkflowOutline.fromBrieflyEntity(item)).collect(Collectors.toList()));
        return pageResult;
    }
    @Override
    public WorkflowOutline getTemplateDetail(RefServiceManager serviceManager, String id) {
        return WorkflowOutline.fromEntity(templateDao.find(serviceManager.getTenantId(), id, false));
    }
}
