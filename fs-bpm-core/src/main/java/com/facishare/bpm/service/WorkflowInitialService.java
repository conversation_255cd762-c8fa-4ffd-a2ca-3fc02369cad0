package com.facishare.bpm.service;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2018/8/8 11:15
 */
public interface WorkflowInitialService {
    void initTenant(String fromTenantId, String toTenantId, boolean includeDescribe, boolean includeData);

    void initTenant(String fromTenantId, String toTenantId, String syncOutlineId);

    void destroyTenant(String TenantId, boolean includeDescribe);

    void copyAllFlowConfig(String fromTenantId, String toTenantId);

    void deleteAllFlowConfig(String tenantId);
}
