package com.facishare.bpm.service;

import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.meta.BaseBPMObj;
import com.facishare.rest.core.model.RemoteContext;

/**
 * Created by wangz on 17-7-30.
 */
public interface WorkflowTransferService {
    BPMInstanceObj transfer(RemoteContext context, String instanceId);

    void setMetadataOwner(RemoteContext context, BaseBPMObj metadata, String defaultOwnerId, String descAPiName, String objectDataId);
}
