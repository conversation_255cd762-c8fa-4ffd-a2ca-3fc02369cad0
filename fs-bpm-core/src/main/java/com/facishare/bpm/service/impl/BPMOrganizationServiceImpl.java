package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.OrgOutline;
import com.facishare.bpm.model.org.BPMOrg;
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId;
import com.facishare.bpm.remote.model.org.CRMGroup;
import com.facishare.bpm.remote.model.org.Department;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.remote.model.org.Role;
import com.facishare.bpm.service.BPMOrganizationService;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by cuiyongxu on 17/5/4.
 */
@Slf4j
@Service
public class BPMOrganizationServiceImpl implements BPMOrganizationService {

    @Override
    public BPMOrg getOrganization(RefServiceManager serviceManager, OrgOutline orgOutline) {

        StopWatch stopWatch = StopWatch.createUnStarted("getOrganization");
        BPMOrg bpmOrg = new BPMOrg();
        //用户信息
        List personList = orgOutline.getPerson();
        if (CollectionUtils.isNotEmpty(personList)) {
            Map<Integer, Employee> employeeMap = serviceManager.getMembersByIds(personList);
            stopWatch.lap("getEmpSimpleEntityList");
            bpmOrg.setPerson(employeeMap);
        }
        //组信息
        List<String> crmGroup = orgOutline.getCRMGroup();
        if (CollectionUtils.isNotEmpty(crmGroup)) {
            Map<String, CRMGroup> crmGroupMap = serviceManager.getGroupByIds(crmGroup);
            stopWatch.lap("getGroupByIds");
            bpmOrg.setCRMGroup(crmGroupMap);
        }

        //角色信息
        List<String> roleList = orgOutline.getRole();
        if (CollectionUtils.isNotEmpty(roleList)) {
            Map<String, Role> roleMap = serviceManager.getRoleByCode(roleList);
            stopWatch.lap("getRoleByCode");
            bpmOrg.setRole(roleMap);
        }
        //部门信息
        List deptIds = orgOutline.getDept();
        if (CollectionUtils.isNotEmpty(deptIds)) {
            Map<Integer, Department> departmentMap = serviceManager.getDeptByIDs(deptIds);
            stopWatch.lap("getDepartmentsByIDs");
            bpmOrg.setDept(departmentMap);
        }

        //外部人员
        List externalPerson = orgOutline.getExternalPerson();
        if (CollectionUtils.isNotEmpty(externalPerson)) {
            Map<Integer, Employee> externalPersonMap = serviceManager.getExternalUserIdInfo(externalPerson);
            stopWatch.lap("getExternalPerson");
            bpmOrg.setExternalPerson(externalPersonMap);
        }

        /**
         * 解析外部角色名称
         */
        List<String> externalRole = orgOutline.getExternalRole();
        String linkAppId = orgOutline.getLinkAppId();
        Integer linkAppType = orgOutline.getLinkAppType();
        if (CollectionUtils.isNotEmpty(externalRole) && !Strings.isNullOrEmpty(linkAppId) && Objects.nonNull(linkAppType)) {
            List<GetOutRolesByTenantId.SimpleRoleResult> rolesByAppId = serviceManager.getRolesByAppId(linkAppId, linkAppType);
            if (CollectionUtils.isNotEmpty(rolesByAppId)) {
                Map<String, GetOutRolesByTenantId.SimpleRoleResult> externalRoleIdOfName = rolesByAppId.stream()
                        .filter(k -> !Strings.isNullOrEmpty(k.getRoleId())
                                && !Strings.isNullOrEmpty(k.getRoleName())
                                && externalRole.contains(k.getRoleId()))
                        .collect(Collectors.toMap(GetOutRolesByTenantId.SimpleRoleResult::getRoleId, k-> k, (k1, k2) -> k2));
                stopWatch.lap("externalRole");
                bpmOrg.setExternalRole(externalRoleIdOfName);
            }
        }
        stopWatch.logSlow(100);
        return bpmOrg;
    }
}
