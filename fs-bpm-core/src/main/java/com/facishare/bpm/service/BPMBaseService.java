package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.exception.BPMWorkflowNotFoundException;
import com.facishare.bpm.helper.SocketConfigHelper;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.resource.after.AfterGetValue;
import com.facishare.bpm.model.resource.apibuspaasmetadata.GetFieldPrivilege;
import com.facishare.bpm.model.resource.app.GetAppActions;
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList;
import com.facishare.bpm.model.resource.emali.QuerySystemEmailList;
import com.facishare.bpm.model.resource.enterpriserelation.BatchGetOuterByRoles;
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId;
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo;
import com.facishare.bpm.model.resource.newmetadata.*;
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch;
import com.facishare.bpm.model.resource.paas.org.GetDeptByDeptIds;
import com.facishare.bpm.model.resource.paas.org.GetUserInfoByUserIds;
import com.facishare.bpm.model.resource.paas.org.Member;
import com.facishare.bpm.proxy.*;
import com.facishare.bpm.remote.app.ErAppProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.org.*;
import com.facishare.bpm.resource.AfterResource;
import com.facishare.bpm.utils.DateUtil;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.bpmn.definition.model.ValidateResult;
import com.facishare.bpmn.definition.util.GetAssigneeHandler;
import com.facishare.bpmn.definition.validate.AssigneeValidateHandler;
import com.facishare.flow.element.plugin.api.FlowElement;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper;
import com.facishare.flow.element.plugin.facade.ElementManagerFacade;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.model.FlowConfigTerminal;
import com.facishare.flow.repository.FlowConfigRepository;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.model.ClientInfo;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.ENTITY_FIELD_SPLIT;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.REPLACE_WHEN_NOT_FOUND;

@Slf4j
public class BPMBaseService {

    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private AfterResource afterResource;

    @Autowired
    private OrganizationServiceProxy organizationServiceProxy;

    @Autowired
    private BpmSimpleDefinitionDao outlineDao;

    @Autowired
    private AuthServiceProxy authServiceProxy;

    @Autowired
    private MetaDataAuthProxy metaDataAuthService;

    @Autowired
    protected EnterpriseProxy enterpriseProxy;

    @Autowired
    protected EServiceResourceProxy eServiceResourceProxy;

    @Autowired
    private SocketConfigHelper socketConfigHelper;

    @Autowired
    private ErAppProxy erAppProxy;

    @Autowired
    private DataPrivilegeProxy dataPrivilegeProxy;

    @Autowired
    private NewPaasMetadataProxy newPaasMetadataProxy;

    @Autowired
    private OpenEmailProxy openEmailProxy;
    @Autowired
    private ElementManagerFacade elementManagerFacade;

    @Autowired
    private FlowConfigRepository flowConfigRepository;

    @Autowired
    private WebPageProxy webPageProxy;

    public RefServiceManager getServiceManager(RemoteContext context) {
        return new RefServiceManager() {

            Map<String, Map> describeCache = Maps.newConcurrentMap();
            Map<String, Map> dataCache = Maps.newConcurrentMap();
            Map<String, List<String>> ownersCache = Maps.newConcurrentMap();
            //TODO review 的时候沟通下,每次请求的缓存,高并发下,内存会比较高,是否可用redisManager
            Map<String, Map<String, String>> entityIdOrDisplayName = Maps.newConcurrentMap();
            Map<String, Map<String, Boolean>> objectsFunctionPrivilegeCache = Maps.newConcurrentMap();
            Map<String, Map<String, List<BatchGetOuterByRoles.SimpleOuterUserData>>> outRoleCache = Maps.newConcurrentMap();

            Boolean isAdmin = null;
            Boolean isOuterMainOwner = null;

            /**
             * 查询数据,包含作废数据
             * @param entityId
             * @param objectId
             * @return
             */
            @Override
            public Map findDataById(String entityId, String objectId) {
                Map data = dataCache.get(getKey(entityId, objectId));
                if (MapUtils.isEmpty(data)) {
                    data = findDataById(entityId, objectId, true, true);
                    dataCache.put(getKey(entityId, objectId), data);
                    return data;
                }
                return data;
            }


            @Override
            public Object findDataById(String entityId, String objectId, String field, boolean includeDescribe) {
                Map<String, Object> data = findDataById(entityId, objectId, true, includeDescribe,true,false);
                if (MapUtils.isNotEmpty(data)) {
                    return data.get(field);
                }
                return null;
            }

            @Override
            public Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe) {
                return findDataById(entityId, objectId, includeLookupName, includeDescribe, true, false);
            }


            @Override
            public Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData,boolean skipRelevantTeam) {
                Map data = dataCache.get(getKey(entityId, objectId));
                if (MapUtils.isEmpty(data)) {
                    // 优先判断下缓存中是否存在,如果存在,则不需要让自定义对象返回,能节省50ms左右
                    // 代码中有的地方先获取描述 ,后获取数据
                    Map describe = describeCache.get(getKey(entityId, null));
                    if (MapUtils.isNotEmpty(describe)) {
                        includeDescribe = false;
                    }
                    try {
                        FindInternalDataById.FindInternalDataByIdResultDetail detail = metadataService.findDataById(context,entityId,objectId,includeLookupName,includeDescribe,true,true,formatData,skipRelevantTeam,null);
                        if (Objects.nonNull(detail)) {
                            if (MapUtils.isNotEmpty(detail.getObject_data())) {
                                data = detail.getObject_data();
                                dataCache.put(getKey(entityId, objectId), data);
                            }
                            /**
                             * 自定义对象返回的数据中没有 includeStatistics
                             */
                            if (MapUtils.isNotEmpty(detail.getDescribe())) {
                                describeCache.put(getKey(entityId, null), detail.getDescribe());
                            }
                        }
                    } catch (RestProxyRuntimeException e) {
                        //TODO  添加一下errormsg
                        if (e.getCode() == 320001400) {
                            log.warn("", e);
                            return Maps.newHashMap();
                        }
                        throw e;
                    }
                    return data;
                }
                return data;
            }


            /**
             * 不走缓存,获取数据时会通过-10000进行获取,走缓存会影响掩码的显示
             * @param cnt
             * @param entityId
             * @param objectId
             * @param includeLookupName
             * @param includeDescribe
             * @return
             */
            @Override
            public Map findDataById(RemoteContext cnt, String entityId, String objectId, boolean includeLookupName, boolean includeDescribe) {
                try {
                    FindInternalDataById.FindInternalDataByIdResultDetail detail = metadataService.findDataById(cnt,entityId,objectId,includeLookupName,includeDescribe,true,true,true,false,null);
                    if (Objects.nonNull(detail)) {
                        if (MapUtils.isNotEmpty(detail.getObject_data())) {
                            return detail.getObject_data();
                        }
                    }
                } catch (RestProxyBusinessException e) {
                    log.warn("findDataById -10000 : field value failed ! CONTEXT={}, " + "ENTITY_ID={}, OBJECT_ID={}", cnt, entityId, objectId);
                }
                return Maps.newHashMap();
            }

            /**
             * TODO 使用时需要注意,此接口依赖SocketConfig,目前设置时长为2s,目前只有获取数据负责人的接口使用
             * @param entityId
             * @param objectId
             * @param includeLookupName
             * @param incluedeDescribe
             * @return
             */
            @Override
            public Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {

                Map data = dataCache.get(getKey(entityId, objectId));
                try {
                    if (MapUtils.isEmpty(data)) {

                        Map describe = describeCache.get(getKey(entityId, null));
                        if (MapUtils.isNotEmpty(describe)) {
                            incluedeDescribe = false;
                        }
                        FindInternalDataById.FindInternalDataByIdResultDetail detail = metadataService.findDataById(context,entityId,objectId,includeLookupName,incluedeDescribe,true,false,false,false,socketConfigHelper.getSocketConfig("getDataOrDescribeBySocketConfig"));
                        if (Objects.nonNull(detail)) {
                            if (MapUtils.isNotEmpty(detail.getObject_data())) {
                                data = detail.getObject_data();
                                dataCache.put(getKey(entityId, objectId), data);
                            }
                            /**
                             * 自定义对象返回的数据中没有 includeStatistics
                             */
                            if (MapUtils.isNotEmpty(detail.getDescribe())) {
                                describeCache.put(getKey(entityId, null), detail.getDescribe());
                            }
                        }
                    }
                } catch (Throwable e) {
                    if (e.getCause() instanceof SocketTimeoutException) {
                        log.warn("获取数据超时:{},entityId:{},objectId:{}", context, entityId, objectId);
                        return Maps.newHashMap();
                    } else if (e instanceof RestProxyBusinessException) {
                        RestProxyBusinessException exception = (RestProxyBusinessException) e;
                        if (BPMBusinessExceptionCode.metadataNotFound(exception.getCode()) || BPMBusinessExceptionCode.metadataNoPermission(exception.getCode())) {
                            return Maps.newHashMap();
                        } else {
                            throw e;
                        }
                    } else {
                        throw e;
                    }
                }
                return data;
            }

            /**
             * 查询描述
             * @param entityId
             * @param containAllFields
             * @param includeStatistics
             * @return
             */
            @Override
            public Map<String, Object> findDescribe(String entityId, boolean containAllFields, boolean includeStatistics) {
                Map<String, Object> describe = describeCache.get(getKey(entityId, null));
                if (MapUtils.isEmpty(describe)) {
                    describe = metadataService.findDescribe(context, entityId, containAllFields, includeStatistics);
                    describeCache.put(getKey(entityId, null), describe);
                }
                return describe;
            }

            /**
             * 查询描述扩展
             */
            @Override
            public Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
                Map<String, Object> describeExt = describeCache.get(getKey(apiName, describeExtraType.name()));
                if (MapUtils.isEmpty(describeExt)) {
                    describeExt = metadataService.findDescribeExtra(context, apiName, describeExtraType);
                    describeCache.put(getKey(apiName, describeExtraType.name()), describeExt);
                }
                return describeExt;
            }

            /**
             * 通过entityid获取所有字段的描述信息
             * @param entityId
             * @return
             */
            @Override
            public Map<String, Object> getFields(String entityId) {
                return MapUtil.instance.getMapOfGeneric(
                        findDescribe(
                                entityId,
                                true,
                                true), UtilConstans.FIELDS);
            }


            @Override
            public FindDataBySearchTemplate.Result findDataBySearchTemplate(String apiName, SearchTemplateQuery query) {
                return metadataService.findDataBySearchTemplate(context, apiName, query);
            }


            @Override
            public Map<String, String> getSimpleEntityNames() {

                //Map<String, String> entityIdOrName = redisManager.getValueMapByKey(getTenantId());
                Map<String, String> entityIdOrName = entityIdOrDisplayName.get(getTenantId());

                if (MapUtils.isEmpty(entityIdOrName)) {
                    List<FindDisplayNames.FindDisplayNamesResultDetail> entityNames = metadataService.getEntityNames(context);
                    entityIdOrName = entityNames.stream().collect(Collectors.toMap(
                            FindDisplayNames.FindDisplayNamesResultDetail::getDescribeApiName,
                            FindDisplayNames.FindDisplayNamesResultDetail::getDescribeDisplayName,
                            (v1, v2) -> v1));
                    //redisManager.setValueOfMap(getTenantId(), entityIdOrName);
                    entityIdOrDisplayName.put(getTenantId(), entityIdOrName);
                }
                return entityIdOrName;
            }


            /**
             * TODO 慎用, 3s不返回 则没有数据返回
             * @return
             */
            @Override
            public Map<String, String> getSimpleEntityNamesBySocketConfig() {
                List<FindDisplayNames.FindDisplayNamesResultDetail> entityNames = metadataService.getEntityNames(context, 3, 2);
                if (CollectionUtils.isEmpty(entityNames)) {
                    return Maps.newHashMap();
                }
                return entityNames.stream().filter(object -> {
                    String describeApiName = object.getDescribeApiName();
                    String describeDisplayName = object.getDescribeDisplayName();
                    if (Strings.isNullOrEmpty(describeApiName) || Strings.isNullOrEmpty(describeDisplayName)) {
                        log.warn("tenantId:{},对象apiName:{} 或者 displayName:{}为空", getTenantId(), describeApiName, describeDisplayName);
                        return false;
                    }
                    return true;
                }).collect(Collectors.toMap(
                        FindDisplayNames.FindDisplayNamesResultDetail::getDescribeApiName,
                        FindDisplayNames.FindDisplayNamesResultDetail::getDescribeDisplayName,
                        (v1, v2) -> v1));
            }


            /**
             * 通过entityId,字段,获取该字段的描述信息
             * @param entityId
             * @param field
             * @return
             */
            @Override
            public Map<String, Object> getFieldDesc(String entityId, String field) {
                Map<String, Object> fieldDescribe = MapUtil.instance.getMapOfGeneric(findDescribe(entityId, true, false), UtilConstans.FIELDS);
                return MapUtil.instance.getMapOfGeneric(fieldDescribe, field);
            }

            /**
             * 通过entityId 以及字段,获取字段的类型
             * @param entityId
             * @param field
             * @return
             */
            @Override
            public String getFieldType(String entityId, String field) {
                Map<String, Object> fieldDesc = getFieldDesc(entityId, field);
                if (MapUtils.isNotEmpty(fieldDesc)) {
                    return (String) fieldDesc.get(UtilConstans.TYPE);
                } else {
                    return null;
                }
            }


            /**
             * 获取key,,企业id+entityId, 如果object不为空,则继续相加
             * @param entityId
             * @param objectId
             * @return
             */
            @Override
            public String getKey(String entityId, String objectId) {
                String key = getTenantId() + entityId;
                if (!Strings.isNullOrEmpty(objectId)) {
                    key += objectId;
                }
                return key;
            }

            /**
             * 通过entityId获取中文名称
             * @param entityId
             * @return
             */
            @Override
            public String getDescDisplayName(String entityId) {
                Map<String, Object> desc;
                try {
                    desc = findDescribe(entityId, true, false);
                    if (MapUtils.isEmpty(desc) || desc.get(BPMConstants.MetadataKey.isActive).equals(false)) {
                        return REPLACE_WHEN_NOT_FOUND;
                    }
                } catch (Exception e) {
                    log.warn("findDescribe is null ,tenantId:{},entityId:{}", getTenantId(), entityId);
                    return REPLACE_WHEN_NOT_FOUND;
                }
                return desc.get(BPMConstants.MetadataKey.displayName).toString();
            }

            /**
             * 通过entityId 及objectId 获取数据中文名称列表
             * @param entityIdAndObjectIdList
             * @return
             */
            @Override
            public Map<String, String> getPaaSObjectNames(Collection<Pair<String, String>> entityIdAndObjectIdList) {
                return metadataService.getPaaSObjectNames(context, entityIdAndObjectIdList);
            }

            /**
             * 通过entityId 及objectId 获取数据中文名称
             * @param entityId
             * @param objectId
             * @return
             */
            @Override
            public String getPaaSObjectName(String entityId, String objectId) {
                return metadataService.getPaaSObjectName(context, entityId, objectId);
            }

            /**
             * 获取制定对象 actionCode 的 actionName
             * @param entityId
             * @param actionCode
             * @return
             */
            @Override
            public String getActionNameByActionCode(String entityId, String actionCode) {
                return metadataService.getActionNameByActionCode(context, entityId, actionCode);
            }


            /**
             * 获取某一个对象下object_reference 类型的字段,(key,value)=>(字段apiName,关联的对象apiName)
             * @param entityId
             * @return
             */
            @Override
            public Map<String, String> getRefEntityIdByEntityId(String entityId) {
                Map<String, String> refMap = Maps.newHashMap();
                Map<String, Object> fields = getFields(entityId);
                fields.forEach((key, value) -> {
                    String type = getFieldType(entityId, key);
                    if (UtilConstans.OBJECT_REFERENCE.equals(type)) {
                        Map<String, Object> fieldDesc = (Map<String, Object>) value;
                        refMap.put(key, (String) fieldDesc.get(UtilConstans.TARGET_API_NAME));
                    }
                });
                return refMap;
            }

            @Override
            public Map<String, Object> findDataByIdWithEmptyMap(String descApiName, String dataId) {
                try {
                    return findDataById(descApiName, dataId, true, true);
                } catch (Throwable e) {
                    Map<String, Object> data = Maps.newHashMap();
                    data.put(UtilConstans.NAME, BPMConstants.REPLACE_WHEN_NO_PERMISSION);
                    data.put(UtilConstans._ID, dataId);
                    return data;
                }
            }

            @Override
            public List<String> getDataOwner(String apiName, String id) {
                List<String> owner = ownersCache.get(getKey(apiName, id));
                if(CollectionUtils.isEmpty(owner)){
                    owner = metadataService.getDataOwner(context, apiName, id);
                    ownersCache.put(getKey(apiName,id),owner);
                }
                return owner;
            }

            @Override
            public boolean isDataOwnerByQueryMetadata(String apiName,String objectId){
                return getDataOwner(apiName, objectId).contains(context.getUserId());
            }

            @Override
            public Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
                return metadataService.getDataOwners(context, entityId, ids);
            }

            @Override
            public RemoteContext getContext() {
                return context;
            }

            @Override
            public String getTenantId() {
                return context.getTenantId();
            }

            @Override
            public String getUserId() {
                return context.getUserId();
            }

            @Override
            public RemoteContext getNotExistEAContext() {
                if (context.isOuterPerson()) {
                    return new RemoteContext(null, getTenantId(), context.getAppId(), BPMConstants.CRM_SYSTEM_USER);
                }
                return context;
            }


            @Override
            public List<String> getValue(boolean convert, String entityId, String objectId, List<String> extUserType, String instanceId, Map<String, Object> variables) {
                AfterGetValue.Arg getValueArg = new AfterGetValue.Arg();
                getValueArg.setConvert(false);
                getValueArg.setFields(entityId, objectId, extUserType);
                getValueArg.setInstanceId(instanceId);
                getValueArg.setType(UtilConstans.BPM);
                getValueArg.setVariables(variables);

                AfterGetValue.Result rst = afterResource.getValue(getValueArg, getTenantId(), BPMConstants.CRM_SYSTEM_USER);
                List<AfterGetValue.ObjectFieldValue> rstList = rst.getData();
                List<String> persons = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(rstList)) {
                    rstList.forEach(item -> {
                        Collection<Object> values = item.getFields().values();
                        if (CollectionUtils.isNotEmpty(values)) {
                            values.forEach(temp -> {
                                if (temp instanceof Collection) {
                                    persons.addAll(((Collection<Object>) temp).stream().map(v -> v.toString()).collect(Collectors.toList()));
                                }
                            });

                        }
                    });
                    return persons;
                }
                return Lists.newArrayList();
            }

            @Override
            public List<Integer> getDeptIdsByUserId() {
                return organizationServiceProxy.getDeptIdsByUserId(context);
            }

            @Override
            public List<String> getGroupByUserId() {
                return organizationServiceProxy.getGroupByUserId(context);
            }

            @Override
            public List<String> getMembersByGroupIds(List groups) {
                if (CollectionUtils.isEmpty(groups)) {
                    return Lists.newArrayList();
                }
                RemoteContext ctx = new RemoteContext();
                ctx.setAppId(BPMConstants.CRM_APP_ID);
                ctx.setUserId(getContext().getUserId());
                ctx.setTenantId(getContext().getTenantId());
                return organizationServiceProxy.getGroupMembers(ctx, groups, 0);
            }

            @Override
            public List<String> getMembersByDeptIds(List<Object> deptIds) {
                List<String> allPerson = Lists.newArrayList();
                if (CollectionUtils.isEmpty(deptIds)) {
                    return allPerson;
                }
                Map<String, List<Member>> deptMembers = organizationServiceProxy.getMembersByDeptIdOfObjects(getContext(), deptIds);
                deptMembers.values().forEach(members -> {
                    members.stream().filter(Member::isActive).forEach(member -> allPerson.add(member.getId()));
                });
                return allPerson;
            }

            @Override
            public List<String> getDeptLeaders(List<Object> deptIds) {
                if (CollectionUtils.isEmpty(deptIds)) {
                    return Lists.newArrayList();
                }
                Collection<Integer> persons = organizationServiceProxy.getDeptLeaders(getContext(), deptIds);
                if (CollectionUtils.isNotEmpty(persons)) {
                    return persons.stream().filter(Objects::nonNull).map(item -> item + "").collect(Collectors.toList());
                }
                return Lists.newArrayList();
            }

            @Override
            public List<String> getDeptLeadersByUserIds(List<String> userIds) {
                if (CollectionUtils.isEmpty(userIds)) {
                    return Lists.newArrayList();
                }
                return organizationServiceProxy.getDeptLeadersByUserIds(getContext(), userIds);
            }

            @Override
            public List<String> getLeaders(List<String> userIds) {
                if (CollectionUtils.isEmpty(userIds)) {
                    return Lists.newArrayList();
                }
                return organizationServiceProxy.getReportingObjectsByUserIds(getContext(), userIds);
            }

            @Override
            public List<String> getCRMUserOfRoles(List<Object> roles) {
                if (CollectionUtils.isEmpty(roles)) {
                    return Lists.newArrayList();
                }
                Collection<Integer> userIds = authServiceProxy.getCRMUserOfRoles(getContext(), roles);
                if (CollectionUtils.isNotEmpty(userIds)) {
                    return userIds.stream().filter(Objects::nonNull).map(item -> item + "").collect(Collectors.toList());
                }
                return Lists.newArrayList();
            }

            @Override
            public RemoteContext getAdminContext() {
                String crmAdmin = authServiceProxy.getOneOfCRMAdmin(getContext());
                return new RemoteContext(context.getEa(), getTenantId(), context.getAppId(), crmAdmin);
            }


            @Override
            public boolean isAdmin() {
                if (isAdmin == null) {
                    isAdmin = authServiceProxy.isAdmin(getContext());
                }
                return isAdmin;
            }

            //  抛出异常 ,暂且保留原始业务逻辑,不做修改,后期再做优化
            @Override
            public Map<String, Object> getDescribe(String entityId) {
                Map<String, Object> rst = describeCache.get(getKey(entityId, null));
                if (rst == null) {
                    rst = findDescribe(entityId, true, false);
                }
                if (rst == null || !MapUtil.instance.getBool(rst, BPMConstants.MetadataKey.isActive)) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_DELETE_DISABLE, entityId);
                }
                return rst;
            }


            @Override
            public Map<String, Map<String, Object>> getDescribes(Collection<String> entityIds) {

                Map<String, Map<String, Object>> rst = Maps.newHashMap();
                for (String entityId : entityIds) {
                    log.info("entityId:{}", entityId);
                    if (entityId.contains(ENTITY_FIELD_SPLIT)) {
                        String[] ids = entityId.split(ENTITY_FIELD_SPLIT);
                        String rootEntityId = ids[0];
                        String lookupField = ids[1];
                        Map<String, Object> desc = getDescribe(rootEntityId);
                        rst.put(rootEntityId, desc);
                        Map<String, Object> fieldDesc = (Map<String, Object>) ((Map) desc.get(BPMConstants.MetadataKey.fields)).get(lookupField);
                        if (MapUtils.isEmpty(fieldDesc)) {
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ENTITY_FIELD_DELETE_DISABLE, desc.get(BPMConstants.MetadataKey.displayName), lookupField);
                        }
                        log.info("entityId:{},field:{},fieldType:{}", entityId, lookupField, fieldDesc.get(BPMConstants.MetadataKey.type));
                        if (BPMConstants.MetadataKey.objectReference.equals(fieldDesc.get(BPMConstants.MetadataKey.type))) {
                            //将 RootEntityId##lookupField 作为key放入缓存
                            rst.put(entityId, getDescribe((String) fieldDesc.get(BPMConstants.MetadataKey.targetApiName)));
                        } else if (fieldDesc.get(BPMConstants.MetadataKey.type).equals(UtilConstans.EMPLOYEE)|| fieldDesc.get(BPMConstants.MetadataKey.type).equals(BPMConstants.MetadataKey.EMPLOYEE_MANY)) {
                            //将 RootEntityId##lookupField 作为key放入缓存
                            rst.put(entityId, getDescribe(UtilConstans.ORG_USER));
                        } else {
                            log.info("getDescribes missing:{} {}", entityId, desc);
                        }
                    } else if (BPMConstants.SPECIAL_ENTITYIDS.contains(entityId)) {
                        log.info("WEB Transitive EntityId :{}", entityId);
                    } else {
                        Map<String, Object> desc = getDescribe(entityId);
                        rst.put(entityId, desc);
                    }
                }
                return rst;
            }

            @Override
            public boolean workflowIsExists(String tenantId, String outLineId,String sourceWorkflowId) {
                WorkflowOutlineEntity rst = outlineDao.getWorkflowOutlineBySourceIdAndOutlineIdWithDeleted(tenantId,sourceWorkflowId,outLineId);
                if(rst==null) {
                    throw new BPMWorkflowNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
                }
                if (rst.isDeleted()) {
                    Map<Integer, Employee> lastModifiedBy = organizationServiceProxy.getMembersByIds(new RemoteContext(null, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER), Lists.newArrayList(rst.getLastModifiedBy()));
                    Employee employee = lastModifiedBy.get(Integer.valueOf(rst.getLastModifiedBy()));
                    if (MapUtils.isNotEmpty(lastModifiedBy) && employee != null) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_WHO_HAS_DELETED_DEF, rst.getName(), employee.getName(), DateUtil.instance.format(rst.getLastModifiedTime()));
                    }
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_SERVICE_DELETE, rst.getName());
                }
                if (!rst.isEnabled()) {
                    Map<Integer, Employee> lastModifiedBy = organizationServiceProxy.getMembersByIds(new RemoteContext(null, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER), Lists.newArrayList(rst.getLastModifiedBy()));
                    Employee employee = lastModifiedBy.get(Integer.valueOf(rst.getLastModifiedBy()));
                    if (MapUtils.isNotEmpty(lastModifiedBy) && employee != null) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_WHO_HAS_STOP_DEF, rst.getName(), employee.getName(), DateUtil.instance.format(rst.getLastModifiedTime()));
                    }
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_SERVICE_STOP, rst.getName());
                }
                return true;
            }

            @Override
            public boolean isFieldInactive(String entityId, String field) {
                Map<String, Object> fieldDesc = getFieldDesc(entityId, field);
                return !MapUtil.instance.getBool(fieldDesc, BPMConstants.MetadataKey.isActive);
            }

            @Override
            public List<String> getRoleByUserId() {
                return authServiceProxy.getRoleByUserId(context);
            }

            @Override
            public Map<String, Object> getAreaOption(String fieldType) {
                return metadataService.getAreaOption(fieldType);
            }


            @Override
            public void validateAssignee(Set<Object> deptIds, Set<Object> groupIds, Set<Object> userIds, Set<Object> roleCodes, Set<Object> deptLeader, String tipName) {
                AssigneeValidateHandler assigneeValidateHandler = new AssigneeValidateHandler();
                ValidateResult validateResult = assigneeValidateHandler.execute(new GetAssigneeHandler() {
                    @Override
                    public Map<String, Role> getRoleByCode(RemoteContext context, List<String> roleCodes) {
                        return authServiceProxy.getRoleByCode(context, roleCodes, null);
                    }

                    @Override
                    public Map<String, Dept> getDeptByDeptIds(RemoteContext context, List<String> deptIds) {
                        return organizationServiceProxy.getDeptByDeptIds(context, deptIds);
                    }

                    @Override
                    public Map<String, CRMGroup> getGroupByIds(RemoteContext context, List<String> groupIdList) {
                        return organizationServiceProxy.getGroupWithStopName(context, groupIdList, null);
                    }

                    @Override
                    public Map<Integer, Employee> getMembersByIds(RemoteContext context, List<Object> userIds) {
                        return organizationServiceProxy.getMembersByIds(context, userIds);
                    }

                    @Override
                    public Map<String, GetDeptByDeptIds.DeptInfo> getDeptMapLeaders(RemoteContext context, List<Object> deptIds) {
                        return organizationServiceProxy.getDeptMapByDeptIds(context,deptIds);
                    }

                    @Override
                    public Collection<Integer> getDeptLeaders(RemoteContext context, List<Object> deptIds) {
                        return organizationServiceProxy.getDeptLeaders(context, deptIds);
                    }
                }, getContext(), deptIds, groupIds, userIds, roleCodes, deptLeader);

                if (!validateResult.isValid()) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, tipName + " " + validateResult.getMessage());
                }
            }

            /**
             * 获取汇报对象
             * @param userId
             * @return
             */
            @Override
            public List<String> getEmployeesByReportingObjectId(String userId) {
                return organizationServiceProxy.getEmployeesByReportingObjectId(context, userId);
            }

            @Override
            public Map<String, Role> getRoleByCode(List<String> roleList) {
                return authServiceProxy.getRoleByCode(context, roleList, null);
            }

            @Override
            public Map<Integer, Employee> getMembersByIds(List personList) {
                return organizationServiceProxy.getMembersByIds(context, personList);
            }

            @Override
            public Map<String, CRMGroup> getGroupByIds(List crmGroup) {
                return organizationServiceProxy.getGroupByIds(context, crmGroup, null);
            }

            @Override
            public Map<Integer, Department> getDeptByIDs(List deptIds) {
                return organizationServiceProxy.getDeptByIDs(context, deptIds);
            }

            @Override
            public Set<String> getPersons(Map<String, Object> nextTaskAssigneeScope) {
                //获取到类别,其中人不处理
                List<String> person = MapUtil.instance.getListOfGeneric(nextTaskAssigneeScope, "person");
                List<Object> groupIds = MapUtil.instance.getListOfGeneric(nextTaskAssigneeScope, "group");
                List<Object> deptIds = MapUtil.instance.getListOfGeneric(nextTaskAssigneeScope, "dept");
                //解析组内成员
                List<String> groupPersons = getMembersByGroupIds(groupIds);
                //解析部门内成员
                List<String> deptPersons = getMembersByDeptIds(deptIds);
                //汇总所有人
                List<String> allPerson = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(person)) {
                    allPerson.addAll(person);
                }
                if (CollectionUtils.isNotEmpty(groupPersons)) {
                    allPerson.addAll(groupPersons);
                }
                if (CollectionUtils.isNotEmpty(deptPersons)) {
                    allPerson.addAll(deptPersons);
                }
                //去重
                Set<String> persons = Sets.newHashSet(allPerson);

                if (CollectionUtils.isNotEmpty(persons)) {
                    List<GetUserInfoByUserIds.EmployeeInfo> userInfoByUserIds = organizationServiceProxy
                            .getUserInfoByUserIds(getContext(), persons.stream().collect(Collectors.toList()));

                    return userInfoByUserIds.stream().filter(user -> user.getStatus() == 0).map(value -> value.getId()).collect(Collectors.toSet());
                }
                return persons;
            }


            @Override
            public Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
                return organizationServiceProxy.getDeptByDeptIds(context, deptIds);
            }

            @Override
            public boolean getDeliveryNoteEnable() {
                return metadataService.getDeliveryNoteEnable(getContext());
            }

            @Override
            public GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
                return metadataService.getCountryAreaOptions(new RemoteContext("", "1", BPMConstants.CRM_APP_ID, BPMConstants.CRM_SYSTEM_USER));
            }

            @Override
            public Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
                return metadataService.getAreaLabelByCodes(new RemoteContext("", tenantId, BPMConstants.CRM_APP_ID, BPMConstants.CRM_SYSTEM_USER), codes);
            }


            @Override
            public GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
                return organizationServiceProxy.getMainDeptsByUserIds(context, ownerId);
            }

            @Override
            public Map<String, Boolean> hasObjectFunctionPrivilege(String entityId) {
                Map<String, Boolean> objectsFunctionPrivilegeNew = objectsFunctionPrivilegeCache.get(entityId);
                if (Objects.isNull(objectsFunctionPrivilegeNew)) {
                    objectsFunctionPrivilegeNew = metaDataAuthService.getObjectsFunctionPrivilegeSupportOuter(getContext(), entityId, BPMConstants.FUNCCODE);
                    objectsFunctionPrivilegeCache.put(entityId, objectsFunctionPrivilegeNew);
                }
                return objectsFunctionPrivilegeNew;
            }

            /**
             * 查询数据权限  770
             * @param entityId
             * @param objectId
             * @return  true :有权限
             */
            @Override
            public boolean dataPrivilege(String entityId,String objectId){
                return !dataPrivilegeProxy.objectPermission(context,entityId,Lists.newArrayList(objectId)).noPermission(objectId);
            }

            @Override
            public String getAppId() {
                return context.getAppId();
            }

            @Override
            public ClientInfo getClientInfo() {
                return context.getClientInfo();
            }

            @Override
            public Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                UpdateData.UpdateDataResultDetail updateResult = metadataService.updateData(context,
                  entityId,
                  objectId,
                  data,
                  true,
                  applyValidationRule,
                  applyDataPrivilegeCheck,
                  getDescribe(entityId),
                  Boolean.FALSE,
                  modelName);

                if (MapUtils.isNotEmpty(updateResult.getObject_data()) && !dataCache.containsKey(getKey(entityId, objectId))) {
                    dataCache.put(getKey(entityId, objectId), updateResult.getObject_data());
                }
                if (MapUtils.isNotEmpty(updateResult.getDescribe()) && !describeCache.containsKey(getKey(entityId, null))) {
                    describeCache.put(getKey(entityId, null), updateResult.getDescribe());
                }

                return updateResult.getObject_data();
            }

            /***
             * 查数据时,没有权限,数据作废了的话都在这里处理
             * @param entityId
             * @param objectId
             * @return
             */
            @Override
            public Map<String, Object> findDataHandlePermissionsById(String entityId, String objectId) {
                Map<String, Object> data;
                try {
                    data = findDataById(entityId, objectId, true, true);
                    if (data == null) {
                        data = Maps.newHashMap();
                    }
                } catch (RestProxyBusinessException e) {
                    data = Maps.newHashMap();
                    // 填充form时，表达式对应的数据可能查不到（删除或没权限）
                    if (BPMBusinessExceptionCode.metadataNotFound(e.getCode())) {
                        data.put(WorkflowKey.ActivityKey.ExtensionKey.name, BPMConstants.REPLACE_WHEN_NOT_FOUND);
                    } else if (BPMBusinessExceptionCode.metadataNoPermission(e.getCode())) {
                        data.put(WorkflowKey.ActivityKey.ExtensionKey.name, BPMConstants.REPLACE_WHEN_NO_PERMISSION);
                    }
                    log.warn("setTaskForm : get task form expression field value failed ! CONTEXT={}, " + "ENTITY_ID={}, OBJECT_ID={}", getContext(), entityId, objectId);
                }

                return data;
            }

            /**
             * 应用节点判断actionCode是否存在
             * @param entityId
             * @param isExternalFlow
             * @return
             */
            private Map<String,List<GetAppActions.App>> appActionsCache=Maps.newConcurrentMap();
            @Override
            public Map<String, String> getAppActions(String entityId, Boolean isExternalFlow,String appId,Integer appType) {
                String key = entityId + "+" + isExternalFlow + "+" + appId + "+" + appType;
                List<GetAppActions.App> data;
                if(appActionsCache.containsKey(key)){
                    data = appActionsCache.get(key);
                }else {
                    GetAppActions.Result result = erAppProxy.getAppActions(getTenantId(),getUserId(),entityId,isExternalFlow,appId,appType);
                    data = result.getData();
                    if(Objects.nonNull(result.getData())){
                        appActionsCache.put(key, result.getData());
                    }
                }

                Map<String, String> actionCodes = Maps.newHashMap();
                //获取所有父级actionCode
                data.stream().map(GetAppActions.App::getActions).collect(Collectors.toList()).forEach(root -> root.forEach(father -> {
                    if (Objects.nonNull(father) && !Strings.isNullOrEmpty(father.getActionCode())) {
                        actionCodes.put(father.getActionCode(), father.getActionName());
                    }
                    List<GetAppActions.AppAction> childrens = father.getChildren();
                    if (CollectionUtils.isNotEmpty(childrens)) {
                        childrens.forEach(children -> {
                            if (Objects.nonNull(children) && !Strings.isNullOrEmpty(children.getActionCode())) {
                                actionCodes.put(children.getActionCode(), children.getActionName());
                            }
                        });
                    }
                }));
                return actionCodes;
            }

            @Override
            public Map<String, Object> getFieldDesc(String entityId, String field, boolean includeCountFieldLookupType) {
                String cacheKey = entityId + includeCountFieldLookupType;
                Map describe = describeCache.get(cacheKey);
                if (MapUtils.isEmpty(describe)) {
                    describe = metadataService.findDescsByApiNames(context, Lists.newArrayList(entityId), true, includeCountFieldLookupType)
                            .stream().findFirst().orElse(Maps.newHashMap());
                    describeCache.put(cacheKey, describe);
                    describeCache.put(getKey(entityId,null),describe);
                }
                Map<String, Object> fieldDescribe = MapUtil.instance.getMapOfGeneric(describe, UtilConstans.FIELDS);

                Map<String, Object> mapOfGeneric = MapUtil.instance.getMapOfGeneric(fieldDescribe, field);
                return MapUtils.isEmpty(mapOfGeneric) ? Maps.newHashMap() : mapOfGeneric;
            }

            @Override
            public Map<Integer, Employee> getExternalUserIdInfo(List externalUserIds) {
                return organizationServiceProxy.getOutMembersByIds(context, externalUserIds,true);
            }
            private Map<String,Map<String, Integer>> fieldPermissions=Maps.newConcurrentMap();
            @Override
            public Map<String, Integer> getFieldPermissions(String entityId) {
                if(!fieldPermissions.containsKey(entityId)){
                    GetFieldPrivilege.GetFieldPrivilegeResultDetail fieldPrivilegeResultDetail = metaDataAuthService.getNewFieldsPrivilegeOfUser(context, entityId);
                    fieldPermissions.put(entityId,fieldPrivilegeResultDetail.getPrivilege());
                }
                return fieldPermissions.get(entityId);
            }


            @Override
            public boolean isOuterUserId() {
                return context.getOuterUserId() != 0;
            }

            @Override
            public List<Long> getOuterMainOwner() {
                if (isOuterUserId()) {
                    Map<String, List<BatchGetOuterByRoles.SimpleOuterUserData>> outRole = Maps.newHashMap();
                    String key = String.valueOf(context.getOuterTenantId()) + context.getOutAppId() + context.getOutLinkType();
                    if (outRoleCache.containsKey(key)) {
                        outRole = outRoleCache.get(key);
                    } else {
                        //2020年03月30日16:38:34  优化下代码,从数据中获取到下游企业id为0,则不执行rpc
                        if (context.getOuterTenantId() != 0) {
                            try {
                                outRole = enterpriseProxy.getUserListByOutRoleId(context,
                                        String.valueOf(context.getOuterTenantId()), context.getOutAppId(), context.getOutLinkType(),
                                        Lists.newArrayList(BPMConstants.OUT_TENANT_MANAGER_LEADER_ROLE)).getSimpleOuterUserData();
                                outRoleCache.put(key, outRole);
                            } catch (Exception e) {
                                log.error("查询外部角色信息异常", e);
                            }
                        }
                    }

                    List<BatchGetOuterByRoles.SimpleOuterUserData> simpleOuterUserData = outRole.get(BPMConstants.OUT_TENANT_MANAGER_LEADER_ROLE);
                    if (MapUtils.isNotEmpty(outRole) && CollectionUtils.isNotEmpty(simpleOuterUserData)) {
                        return simpleOuterUserData.stream()
                                .map(BatchGetOuterByRoles.SimpleOuterUserData::getOuterUid)
                                .collect(Collectors.toList());
                    }
                }
                return Lists.newArrayList();
            }

            @Override
            public Map<String, Employee> getEmployeeInfo(List<Object> userIds) {
                // 查询的人员信息都下发
                return organizationServiceProxy.getOutMembersByIds(context, userIds, true).values().stream()
                        .collect(Collectors.toMap((k -> String.valueOf(k.getId())), (v -> v), (k1, k2) -> k1));
            }

            @Override
            public List<String> getExternalRole(List externalRole, String lowerTenantId, String linkAppId, Integer linkAppType) {
                //2020年03月30日16:38:34  优化下代码,从数据中获取到下游企业id为0,则不执行rpc
                if ("0".equals(lowerTenantId)) {
                    return Lists.newArrayList();
                }
                BatchGetOuterByRoles.Result userListByOutRoleId = enterpriseProxy.getUserListByOutRoleId(context, lowerTenantId, linkAppId, linkAppType, externalRole);
                if (Objects.isNull(userListByOutRoleId)) {
                    return Lists.newArrayList();
                }
                return userListByOutRoleId.getOuterUserByRule();
            }

            /**
             * 获取外部角色
             * @return
             */
            @Override
            public List<GetOutRolesByTenantId.SimpleRoleResult> getRolesByAppId(String appId, int appType) {
                return enterpriseProxy.getOutRolesByTenantId(context, getTenantId(), appId, appType);
            }

            @Override
            public String getUserIdWithOuterUserId() {
                return isOuterUserId() ? String.valueOf(context.getOuterUserId()) : context.getUserId();
            }

            @Override
            public boolean isOuterMainOwner() {
                if(isOuterMainOwner==null){
                    List<Long> outManagerLeader = getOuterMainOwner();
                    if (isOuterUserId() && CollectionUtils.isNotEmpty(outManagerLeader)) {
                        isOuterMainOwner =  outManagerLeader.contains(context.getOuterUserId());
                    }else{
                        isOuterMainOwner = Boolean.FALSE;
                    }
                }
                return isOuterMainOwner;
            }

            @Override
            public GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
                try {
                    //1: web
                    //2: 终端
                    return eServiceResourceProxy.getBpmSupportInfo(context, appCode, actionCode, taskParams.isMobile() ? EServiceResourceProxy.MOBILE : EServiceResourceProxy.WEB, entityId, objectId, taskId, context.getClientInfo().getFullStr());
                } catch (Throwable e) {
                    log.warn("getBpmSupportInfo", e);
                    return new GetBpmSupportInfo.Result();
                }
            }

            @Override
            public Map<String, String> getPaaSObjectNames(String entityId, List<String> ids) {
                return metadataService.getPaaSObjectNames(getContext(),
                        entityId, ids);
            }

            @Override
            public Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
                return metadataService.getObjectNameAndDisplayName(getContext(),
                        entityId, ids);
            }

            Map<String, Object> objectCache = Maps.newConcurrentMap();
            @Override
            public <E> E getObjectFromCache(String key, Function<String,E> function) {
                if(objectCache.containsKey(key)){
                    return (E)objectCache.get(key);
                }else{
                    E data=function.apply(key);
                    if(Objects.nonNull(data)){
                        objectCache.put(key,data);
                    }
                    return data;
                }
            }

            @Override
            public Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet){
                if (CollectionUtils.isEmpty(entityLayoutApiNameSet)){
                    return Sets.newHashSet();
                }
                IsLayoutExist.Result layoutIsExist = newPaasMetadataProxy.layoutIsExist(getContext(), entityId, Lists.newArrayList(entityLayoutApiNameSet));
                return entityLayoutApiNameSet.stream().filter(layoutApiName -> !layoutIsExist.existsLayout(layoutApiName)).collect(Collectors.toSet());
            }

            Map<String, List<FindCustomButtonList.CustomButton>> customBtnCache = Maps.newConcurrentMap();

            @Override
            public List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction){
                List<FindCustomButtonList.CustomButton> result = customBtnCache.get(apiName);
                if(Objects.isNull(result)){
                    result = metadataService.findCustomButtonList(context, apiName, includeUIAction);
                    customBtnCache.put(apiName, result);
                }
                return result;
            }

            Map<String, Map<String, String>> dataExhibitBtnCache = Maps.newConcurrentMap();
            @Override
            public Map<String, String> findDataExhibitButton(String apiName, String objectId,  String usePageType){
                Map<String, String> result = dataExhibitBtnCache.get(getKey(apiName,objectId));
                if(Objects.isNull(result)){
                    result = metadataService.findDataExhibitButton(context, apiName, objectId, usePageType).stream().collect(Collectors.toMap(FindCustomButtonList.CustomButton::getApi_name, FindCustomButtonList.CustomButton::getLabel));
                    dataExhibitBtnCache.put(getKey(apiName, objectId), result);
                }
                return result;
            }

            @Override
            public boolean checkEmailEnable(String sender) {
                return openEmailProxy.checkEmailEnable(sender, QuerySystemEmailList.Arg1.createArg1(getTenantId(), getUserId(), "5"));
            }

            @Override
            public FlowElementWrapper getFlowElementWrapper(String elementApiName) {
                return elementManagerFacade.getElementWrapper(elementApiName);
            }

            @Override
            public FlowElement getFlowElement(String elementApiName) {
                return elementManagerFacade.getElement(elementApiName);
            }

            @Override
            public String getI18nLinkAppName(String linkApp, String linkAppName) {
                return enterpriseProxy.getI18nLinkAppName(context, linkApp, linkAppName);
            }

            @Override
            public boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
                return newPaasMetadataProxy.convertRuleIsEnable(context, ruleName, ruleApiName);
            }

            @Override
            public Map<String, Object> getFlowConfig(String terminal, List<String> types) {
                if(StringUtils.isBlank(terminal) || CollectionUtils.isEmpty(types)){
                    return Maps.newHashMap();
                }
                try {
                    return flowConfigRepository.queryOld(context, FlowType.workflow_bpm, FlowConfigTerminal.valueOf(terminal), types);
                }catch (Exception e){
                    log.info("getFlowConfig fail, tenantId:{}, types:{}", context.getTenantId(), Arrays.toString(types.toArray()), e);
                    return Maps.newHashMap();
                }
            }

            @Override
            public boolean isNeedDiscussButton() {
                return webPageProxy.isEnterpriseMessageEnable(context);
            }

            @Override
            public UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName){
                UpdateData.UpdateDataResultDetail updateResult = metadataService.updateDataCheckConflicts(context,
                        entityId,
                        objectId,
                        data,
                        true,
                        applyValidationRule,
                        applyDataPrivilegeCheck,
                        getDescribe(entityId),
                        Boolean.FALSE,
                        modelName);

                if (MapUtils.isNotEmpty(updateResult.getObject_data()) && !dataCache.containsKey(getKey(entityId, objectId))) {
                    dataCache.put(getKey(entityId, objectId), updateResult.getObject_data());
                }
                if (MapUtils.isNotEmpty(updateResult.getDescribe()) && !describeCache.containsKey(getKey(entityId, null))) {
                    describeCache.put(getKey(entityId, null), updateResult.getDescribe());
                }

                return updateResult;
            }

            private final Map<String, Map<String, Object>> recordFieldMappingCache = Maps.newHashMap();
            @Override
            public Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
                String key = getKey(entityId, recordType);
                Map<String, Object> result = recordFieldMappingCache.get(key);
                if (Objects.isNull(result)) {
                    result = newPaasMetadataProxy.findRecordFieldMapping(context, entityId, recordType);
                    recordFieldMappingCache.put(key, result);
                }
                return result;
            }

            @Override
            public Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
                return newPaasMetadataProxy.getDimensionObjDataList(context, query);
            }
        };
    }
}
