package com.facishare.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMQueryMongoNotFoundException;
import com.facishare.bpm.manage.TodoSessionKeyManager;
import com.facishare.bpm.model.RelevantTeam;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.meta.BPMTaskObj;
import com.facishare.bpm.model.meta.BaseBPMObj;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.proxy.OrganizationServiceProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.DataTransferUtils;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.repository.BPMTaskRepository;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.common.StopWatch;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-7-30.
 */

@Service
@Slf4j
public class WorkflowTransferServiceImpl implements WorkflowTransferService {
    private static final String FIELD_ID = "_id";

    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private DefinitionExtensionDao extensionDao;
    @Autowired
    private OrganizationServiceProxy organizationServiceProxy;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @Autowired
    private BPMTaskRepository bpmTaskRepository;

    @Autowired
    private TodoSessionKeyManager getTodoSessionKeyManager;

    @Autowired
    private ManageGroupProxy manageGroupProxy;

    @Override
    public BPMInstanceObj transfer(RemoteContext context, String instanceId) {
        log.info("old code transfer:{}", instanceId);
        WorkflowInstance instance = paasWorkflowServiceProxy.getWorkflowInstance(context, instanceId);
        if (Objects.isNull(instance)) {
            log.error("实例查询失败,{},{}", context, instanceId);
            throw new BPMQueryMongoNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PRE_SYNCHRONIZED_TO_CUSTOM_OBJECT_QUERY_ENGINE_DB_RETURNS_NULL.getMessage());
        }
        StopWatch stopWatch = StopWatch.createUnStarted("transfer by connector");

        List<Task> sourceTasks = paasWorkflowServiceProxy.getTasksByInstanceId(context, instanceId);

        if (CollectionUtils.isEmpty(sourceTasks)) {
            log.error("query task info is null,context:{},instanceId:{},return", context, instanceId);
            return null;
        }
        List<PoolEntity> pools = extensionDao.getPools(context.getTenantId(), instance.getWorkflowId());
        if (CollectionUtils.isEmpty(pools)) {
            log.error("get pools is null ,tenantId:{},instanceId:{}", context.getTenantId(), instanceId);
        }
        stopWatch.lap("getSourceTasks");
        BPMInstanceObj mdInstance = transfer2MetadataInstance(context, instance, pools, sourceTasks);
        stopWatch.lap("transferMDInstance");
        List<BPMTaskObj> mdTasks = transfer2MetadataTasks(context, sourceTasks, mdInstance, pools);
        stopWatch.lap("transferMDTasks");
        Map<String, Object> instanceData = createInstance(context, mdInstance);
        stopWatch.lap("createMDInstance");
        createTasks(context, mdTasks);
        stopWatch.lap("createMDTasks");
        stopWatch.logSlow(400);
        return MapUtils.isEmpty(instanceData) ? null : findMetaInstanceById(context, instanceId);
    }


    private BPMInstanceObj transfer2MetadataInstance(RemoteContext context, WorkflowInstance sourceInstance,
                                                     List<PoolEntity> pools, List<Task> sourceTasks) {
        try {
            //METADATA instance

            Set<String> referenceObjectIds = bpmTaskRepository.getTaskObjectIdByInstanceId(context.getTenantId(), sourceInstance.getId());
            BPMInstanceObj mdInstance = DataTransferUtils.transferInstance(sourceInstance, pools, true, referenceObjectIds, sourceInstance.calculateActualDuration(context, manageGroupProxy));
            Map<Integer, Employee> employees = organizationServiceProxy.getOutMembersByIds(context, getAllTaskCandidateIds(sourceTasks));
            //此处只设置了团队成员, 4,未设置owner
            DataTransferUtils.setParticipantsAndObjectIds(mdInstance, sourceTasks, employees, referenceObjectIds);
            //设置owner,否则调用自定义对象 owenr会被删除掉
            setMetadataOwner(context, mdInstance,
                    mdInstance.getApplicantId().stream().findFirst().get(),
                    mdInstance.getObjectApiName(),
                    mdInstance.getObjectDataId());
            return mdInstance;
        } catch (Exception e) {
            log.error("TRANSFER_ERROR_getMDInstance : error! TENANT_ID={}, INSTANCE_ID={}. ", context.getTenantId(), sourceInstance.getId(), e);
            throw e;
        }
    }


    private List<BPMTaskObj> transfer2MetadataTasks(RemoteContext context, List<Task> sourceTasks,
                                                    BPMInstanceObj mdInstance, List<PoolEntity> pools) {
        try {
            //2019年09月05日17:44:26   同步待办时,需要过滤掉应用节点的任务
            Map<Integer, Employee> outMembers = organizationServiceProxy.getOutMembersByIds(context, getAllTaskCandidateIds(sourceTasks));

            return sourceTasks.stream().map(sourceTask -> DataTransferUtils.transferTask(
                    sourceTask,
                    mdInstance,
                    pools,
                    true,
                    userIds -> outMembers,
                    getTodoSessionKeyManager.getSessionKey(context, sourceTask.getEntityId(), sourceTask.getExecutionType(), sourceTask.getExternalApplyTaskActionCode(), sourceTask.getExternalFlow(), sourceTask.getId(), sourceTask.getElementApiName()),
                    sourceTask.calculateActualDuration(context, manageGroupProxy)
            )).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("transfer_error_getmdtask : error! TENANT_ID={}, INSTANCE_ID={}. ", context.getTenantId(),
                    mdInstance.get_id(), e);
            throw e;
        }
    }


    private Map<String, Object> createInstance(RemoteContext context, BPMInstanceObj mdInstance) {
        Map<String, Object> result = findAndModify(context, TransferDataConstants.APINAME_INSTANCE, mdInstance);
        log.info("transfer_info_createinstance : success! TENANT_ID={}, INSTANCE_ID={}", context.getTenantId(),
                mdInstance.get_id());
        return result;
    }

    private void createTasks(RemoteContext context, List<BPMTaskObj> mdTasks) {
        mdTasks.forEach(mdTask -> {
            findAndModify(context, TransferDataConstants.APINAME_TASK, mdTask);
            log.info("transfer_info_createtask : success! TENANT_ID={}, INSTANCE_ID={}, TASK_ID={}! ",
                    context.getTenantId(), mdTask.getWorkflowInstanceId(),
                    mdTask.get_id());
        });
    }

    private Map<String, Object> findAndModify(RemoteContext context, String apiName, BaseBPMObj data) {
        String id = data.get_id();
        if (findDataById(context, apiName, id) == null) {
            if (apiName.equals(TransferDataConstants.APINAME_INSTANCE)) {
                // 当插入实例数据时 存owner
                setMetadataOwner(context, data,
                  ((BPMInstanceObj)data).getApplicantId().stream().findFirst().get(),
                        data.getObjectApiName(),
                        data.getObjectDataId());
            }
            return metadataService.createData(context, apiName, JSON.toJSONString(data));
        } else {
            TraceContext.get().setSourceProcessId(data.getSourceWorkflowId());
            return metadataService.updateData(context, apiName, id, JSON.toJSONString(data), false, Boolean.FALSE);
        }
    }

    private Map<String, Object> findDataById(RemoteContext context, String entityId, String objectId) {
        try {
            return metadataService.findDataById(context, entityId, objectId, false, false, false, true, true,false, null).getObject_data();
        } catch (Throwable e) {
            return null;
        }
    }

    public static RemoteContext initContext(String tenantId, String userId) {
        return new RemoteContext(tenantId, tenantId, BPMConstants.APP_ID, userId);
    }


    /**
     * 获取所有任务的处理人  为了添加到实例的团队成员中
     *
     * @param sourceTasks
     * @return
     */
    private List<Object> getAllTaskCandidateIds(List<Task> sourceTasks) {
        if (CollectionUtils.isEmpty(sourceTasks)) {
            return Lists.newArrayList();
        }
        return sourceTasks.stream()
                .collect(Lists::newArrayList, (list, task) -> {
                    List<String> candidates = task.getCandidateIds();
                    if (CollectionUtils.isNotEmpty(candidates)) {
                        list.addAll(candidates);
                    }
                }, List::addAll);
    }

    public static String getWorkflowInstanceId(Map<String, Object> sourceInstance) {
        Object fieldId = sourceInstance.get(FIELD_ID);
        String instanceId;
        if (fieldId instanceof ObjectId) {
            instanceId = ((ObjectId) fieldId).toHexString();
        } else {
            instanceId = (String) fieldId;
        }
        if (Strings.isNullOrEmpty(instanceId)) {
            //控制台修复的数据，从引擎查过来的，是id,而不是_id;
            instanceId = (String) sourceInstance.get("id");
            sourceInstance.put(FIELD_ID, instanceId);
        }
        return instanceId;
    }


    /**
     * 设置流程实例数据负责人
     *
     * @param context
     * @param metadata
     * @param defaultOwnerId
     * @param descAPiName
     * @param objectDataId
     */
    @Override
    public void setMetadataOwner(RemoteContext context, BaseBPMObj metadata, String defaultOwnerId, String descAPiName, String objectDataId) {
        String ownerId = null;
        if (BPMConstants.CRM_SYSTEM_USER.equals(defaultOwnerId)) {
            Map<String, Object> data = findDataById(context, descAPiName, objectDataId);
            Object owner = data == null ? null : data.get(TransferDataConstants.MDField.owner.getValue());
            if (owner instanceof List && ((List) owner).size() > 0) {
                ownerId = String.valueOf(((List) owner).get(0));
            } else if (owner instanceof String) {
                ownerId = owner.toString();
            }

            if (Strings.isNullOrEmpty(ownerId) || "null".equals(ownerId)) {
                ownerId = defaultOwnerId;
            }
        } else {
            ownerId = defaultOwnerId;
        }

        setDataOwnerAndRelevantTeam(context, metadata,ownerId);
    }


    /**
     * 设置instance 或任务的初始owner和relevant_team
     */
    public void setDataOwnerAndRelevantTeam(RemoteContext context, BaseBPMObj metadata,String ownerId) {

        Set<String> owner = Sets.newHashSet(ownerId);
        metadata.setOwner(owner);
        //原来是覆盖团队成员
        Set<RelevantTeam> relevantTeams = Sets.newHashSet();
        //在owner的基础上  添加原有的
        Set<RelevantTeam> relevant_team = metadata.getRelevant_team();
        if (Objects.nonNull(relevant_team)) {
            HashSet<RelevantTeam> instanceTeam = (HashSet<RelevantTeam>) relevant_team;
            if (CollectionUtils.isNotEmpty(instanceTeam)) {
                relevantTeams.addAll(instanceTeam);
            }
        }

        RelevantTeam ownerRelevantTeam = new RelevantTeam(
                owner,
                RelevantTeam.TeamMemberRole.owner.getValue(),
                RelevantTeam.TeamMemberPermissionType.readOnly.getValue());


        RelevantTeam commonRelevantTeam = new RelevantTeam(
                owner,
                RelevantTeam.TeamMemberRole.common.getValue(),
                RelevantTeam.TeamMemberPermissionType.readOnly.getValue());


        //只有外部人员能获取到employee  内部人员是无法获取到的
        if (!Strings.isNullOrEmpty(ownerId) && ownerId.length() >= 9) {
            try {
                Map<Integer, Employee> employees = organizationServiceProxy.getOutMembersByIds(context, owner.stream().collect(Collectors.toList()));
                if (Objects.nonNull(employees)) {
                    Employee employee = employees.get(Integer.parseInt(ownerId));

                    if (Objects.nonNull(employee)) {
                        ownerRelevantTeam.setOutTenantId(employee.getOutTenantId());
                        ownerRelevantTeam.setSourceType(RelevantTeam.SourceType.outUser.getValue());

                        commonRelevantTeam.setOutTenantId(employee.getOutTenantId());
                        commonRelevantTeam.setSourceType(RelevantTeam.SourceType.outUser.getValue());
                    }
                }
            } catch (Exception e) {
                log.warn("", e);
            }
        }

        relevantTeams.add(ownerRelevantTeam);
        relevantTeams.add(commonRelevantTeam);
        metadata.setRelevant_team(relevantTeams);
    }

    private BPMInstanceObj findMetaInstanceById(RemoteContext context, String objectId) {
        return JacksonUtil.fromJson(JacksonUtil.toJson(findDataById(context,TransferDataConstants.APINAME_INSTANCE,objectId)),BPMInstanceObj.class);
    }

}
