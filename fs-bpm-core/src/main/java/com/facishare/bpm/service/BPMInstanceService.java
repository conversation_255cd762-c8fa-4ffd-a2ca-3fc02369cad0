package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.WorkflowInstanceLog;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.WorkflowStats;
import com.facishare.bpm.model.instance.EntireWorkflow;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import com.facishare.bpm.model.instance.TriggerWorkflow;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;

import java.util.List;

/**
 * Created by wangz on 17-2-25.
 */
public interface BPMInstanceService {

    String startWorkflow(RefServiceManager serviceManager, TriggerSource triggerSource, String outlineId, String objectId);

    Pair<Pair<WorkflowOutline, String>, List<WorkflowStats>> getWorkflowStatsDataByPG(RefServiceManager serviceManager, String sourceWorkflowId);

    boolean recoveryCancelInstance(RefServiceManager serviceManager, String instanceId);

    WorkflowInstanceLog getWorkflowInstanceLog(RefServiceManager serviceManager, String instanceId);

    void cancelWorkflowInstance(RefServiceManager serviceManager, String workflowInstanceId,String reason);

    WorkflowInstance getWorkflowInstance(RefServiceManager serviceManager, String instanceId);

    PageResult<WorkflowInstanceVO> getWorkflowInstances(RefServiceManager serviceManager, InstanceState state, String objectId, int pageSize,
                                                        int pageNumber, String orderBy, String sourceWorkflowId);

    PageResult<WorkflowInstanceVO> getWorkflowInstances(RefServiceManager serviceManager, InstanceState state, String entityId,String objectId, int pageSize,
                                                        int pageNumber, String orderBy, String sourceWorkflowId);

    PageResult<WorkflowInstanceVO> getWorkflowInstancesSkipDataAuth(RefServiceManager serviceManager, InstanceState state, String objectId, int pageSize, int pageNumber, String orderBy, String sourceWorkflowId);

    //后面会将上面这个替换掉
    EntireWorkflow getEntireWorkflowInstance(RefServiceManager serviceManager, String instanceId);

    TriggerWorkflow triggerWorkflow(RefServiceManager serviceManager, TriggerSource triggerSource, String triggerSourceId, String outlineId, String objectId);

    TriggerWorkflow triggerWorkflow(RefServiceManager serviceManager, TriggerSource triggerSource, String triggerSourceId, String outlineId, String objectId, String startInstanceId);

    TriggerWorkflow triggerWorkflowForRest(RefServiceManager serviceManager, TriggerSource triggerSource, String triggerSourceId, String outlineId, String entityId,String objectId,String sourceWorkflowId, String startInstanceId);

    List<GetInstanceByObject> getInstancesByObject(RefServiceManager serviceManager, String entityId, String objectId);

    void cancelWorkflowInstances( RefServiceManager serviceManager, String workflowInstanceId, String reason);

    AfterRetry.RetryResult afterActionRetry(RefServiceManager serviceManager, String instanceId, int rowNum, int executeType);

    String getReason(RefServiceManager serviceManager, String entityId, String objectId);

    String getTaskOrInstanceRelatedEntityId(RefServiceManager serviceManager, String instanceId, String taskId);

    Integer fixedPGCancelReason(RemoteContext context, Page page, BPMInstanceObj pgInstance);

}
