package com.facishare.bpm.service.impl;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskReferDescNotFoundException;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.WorkflowInitialService;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.FlowConfigDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.rest.core.model.RemoteContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: WangSong
 * @Date: 2018/8/8 11:16
 */
@Service
@Slf4j
public class WorkflowInitialServiceImpl extends BPMBaseService implements WorkflowInitialService {

    @Autowired
    private BPMDefinitionService bpmDefinitionService;
    @Autowired
    private BpmSimpleDefinitionDao outlineDao;

    @Override
    public void initTenant(String fromTenantId, String toTenantId, boolean includeDescribe, boolean includeData) {
        log.info("来源企业:{},目标企业:{}", fromTenantId, toTenantId);
        RemoteContext fromContext = getContext(fromTenantId);
        WorkflowOutlineQuery query = new WorkflowOutlineQuery();
        List<WorkflowOutlineEntity> workflowOutlineEntityList = outlineDao.find(fromTenantId, query, new Page(2000,
                1, null,
                false),true, null, Boolean.TRUE).getDataList();
        log.info("查询来源企业的BPM定义个数为:{}", workflowOutlineEntityList.size());
        RemoteContext toContext = getContext(toTenantId);

        workflowOutlineEntityList.forEach(workflowOutlineEntity -> {
            WorkflowOutline workflowOutline = bpmDefinitionService.getWorkflowOutlineByIdOfClearRule(getServiceManager(fromContext), workflowOutlineEntity.getId());
            String oldOutlineId = workflowOutline.getId();
            workflowOutline.clearWorkflowOutline(workflowOutline, toTenantId);
            try {
                String outlineId = bpmDefinitionService.deployWorkflow(getServiceManager(toContext), workflowOutline, false, false, true);
                log.info("数据同步成功:将 {} 同步到 {} ,新流程定义Id为:{} name:{}", fromTenantId, toTenantId, outlineId, workflowOutline.getName());
            } catch (Exception e) {
                log.error("数据同步失败:将{},同步到{},{},{}", fromTenantId, toTenantId, oldOutlineId, e);
            }
        });
        log.info("创建已完成:{}", toTenantId);
    }


    @Override
    public void initTenant(String fromTenantId, String toTenantId, String syncOutlineId) {
        log.info("来源企业:{},目标企业:{}", fromTenantId, toTenantId);
        RemoteContext fromContext = getContext(fromTenantId);
        RemoteContext toContext = getContext(toTenantId);
        WorkflowOutline workflowOutline = bpmDefinitionService.getWorkflowOutlineByIdOfClearRule(getServiceManager(fromContext), syncOutlineId);
        if(Objects.isNull(workflowOutline)){
            throw new BPMTaskReferDescNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        String oldOutlineId = workflowOutline.getId();
        workflowOutline.clearWorkflowOutline(workflowOutline, toTenantId);
        try {
            String outlineId = bpmDefinitionService.deployWorkflow(getServiceManager(toContext), workflowOutline, false, false, true);
            log.info("数据同步成功:将 {} 同步到 {} ,新流程定义Id为:{} name:{}", fromTenantId, toTenantId, outlineId, workflowOutline.getName());
        } catch (Exception e) {
            log.error("数据同步失败:将{},同步到{},{},{}", fromTenantId, toTenantId, oldOutlineId, e);
        }
        log.info("创建已完成:{}", toTenantId);
    }

    @Override
    public void destroyTenant(String tenantId, boolean includeDescribe) {
        log.info("销毁企业:{},是否销毁流程定义{}", tenantId, includeDescribe);
        RemoteContext remoteContext = getContext(tenantId);
        bpmDefinitionService.destroyTenant(getServiceManager(remoteContext), includeDescribe);
        log.info("销毁已完成:{}", tenantId);
    }
    @Autowired
    private FlowConfigDao flowConfigDao;
    @Override
    public void copyAllFlowConfig(String fromTenantId, String toTenantId) {
        flowConfigDao.copyAllFLowConfig(fromTenantId, toTenantId);
        log.info("从 {} 到 {} 同步流程高级配置成功", fromTenantId, toTenantId);
    }

    @Override
    public void deleteAllFlowConfig(String tenantId) {
        flowConfigDao.deleteAllFlowConfig(tenantId);
        log.info("清除流程高级配置:{}", tenantId);
    }


    private RemoteContext getContext(String tenantId) {
        return new RemoteContext("", tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
    }
}
