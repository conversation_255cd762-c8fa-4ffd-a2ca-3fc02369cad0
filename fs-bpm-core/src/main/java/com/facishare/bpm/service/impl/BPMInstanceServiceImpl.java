package com.facishare.bpm.service.impl;

import com.alibaba.fastjson.JSON;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.ExecutionTaskExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.exception.*;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.BPMLoopBreakerManager;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.manage.RedisManager;
import com.facishare.bpm.model.*;
import com.facishare.bpm.model.instance.EntireWorkflow;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import com.facishare.bpm.model.instance.TriggerWorkflow;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.statics.LaneStatics;
import com.facishare.bpm.model.statics.TaskStatics;
import com.facishare.bpm.model.statics.WorkflowBaseStatics;
import com.facishare.bpm.model.statics.WorkflowInstanceStatics;
import com.facishare.bpm.model.task.MetadataInstance;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.rule.RuleMessage;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.CustomI18nProcessor;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.util.WorkflowJsonUtil;
import com.facishare.bpm.util.verifiy.util.WorkflowGraph;
import com.facishare.bpm.utils.ButtonFunctionUtil;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.MetadataQuery;
import com.facishare.bpm.utils.SearchTemplateUtils;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.I18NUtils;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.bpmn.definition.model.GetOrganization;
import com.facishare.bpmn.definition.util.ConditionUtil;
import com.facishare.bpmn.definition.util.GetValueHandler;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.engine.EngineDefinitionDao;
import com.facishare.flow.postgre.entity.BpmTaskCountEntity;
import com.facishare.flow.repository.BPMInstanceRepository;
import com.facishare.flow.repository.BPMTaskRepository;
import com.facishare.paas.I18N;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.lang.reflect.UndeclaredThrowableException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.bpm.manage.impl.MoreOperationManagerImpl.stopBPM;
import static com.facishare.bpm.utils.i18n.CustomI18NHandler.workflowBase;


/**
 * Created by wangz on 17-2-25.
 */
@Slf4j
@Service
public class BPMInstanceServiceImpl implements BPMInstanceService {
    @Autowired
    private DefinitionExtensionDao workflowExtensionDao;
    @Autowired
    private BpmSimpleDefinitionDao outlineDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    private BPMDefinitionService bpmDefinitionService;
    @Autowired
    private InstanceVariableManager instanceVariableManager;
    @Autowired
    private BPMTaskService bpmTaskService;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MoreOperationManager moreOperationManager;

    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private BPMInstanceRepository bpmInstanceRepository;

    @Autowired
    private BPMTaskRepository bpmTaskRepository;

    @Autowired
    private BPMLoopBreakerManager bpmLoopBreakerManager;

    /**
     * 启动流程实例,应该移动到BPMInstance中
     */
    @Override
    public String startWorkflow(RefServiceManager serviceManager, TriggerSource triggerSource, String outlineId, String objectId) {

        StopWatch stopWatch = StopWatch.createUnStarted("startWorkflow");
        WorkflowOutlineEntity outlineEntity = outlineDao.find(serviceManager.getTenantId(), outlineId);
        //0. 查询是否是单实例运行
        isSingleInstance(outlineEntity, serviceManager, objectId);

        String sourceWorkflowId = outlineEntity.getSourceWorkflowId();
        stopWatch.lap("query outline");
        log.debug("startWorkflow | context:{},workflowId:{},entityId:{},objectId:{}", serviceManager.getContext(), sourceWorkflowId, outlineEntity.getEntryType(), objectId);
        boolean enabled = outlineEntity.isEnabled();
        if (!enabled) {
            log.info("{} Process Is Disabled", outlineId);
            throw new BPMStartException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_HAS_STOP, outlineEntity.getName());
        }
        stopWatch.lap("check outline enabled");

        ExecutableWorkflowExt executableWorkflow = WorkflowJsonUtil.convertExecutableWorkflowFromJson(outlineEntity.getWorkflowJson());
        List<VariableExt> workflowVariables = executableWorkflow.getVariables();
        Map<String, Object> variableMap = Maps.newHashMap();
        if (workflowVariables != null) {
            for (VariableExt workflowVariable : workflowVariables) {
                variableMap.put(workflowVariable.getId(), null);
            }
        }
        stopWatch.lap("fetch workflow variable list");

        String entityId = outlineEntity.getEntryType();
        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey("0", new Pair<>(entityId,
                objectId));

        DataCacheHandler dataHandler = bpmTaskService.getDataHandler(serviceManager);
        Map<String, Object> data = dataHandler.getData(entityId, objectId);
        //数据为空或者生命状态为作废 不允许发起流程
        if (MapUtils.isEmpty(data) || "invalid".equals(data.get(BPMConstants.MetadataKey.LIFE_STATUS))) {
            throw new BPMStartException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DATA_INVALID_OR_DELETED_NOT_TRIGGER_FLOW);
        }
        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                null,
                outlineEntity.getWorkflowId(),
                variableKey,
                dataHandler
        );
        stopWatch.lap("set variables");

        String instanceId = paasWorkflow.start(serviceManager.getContext(), triggerSource, outlineEntity.getEntryType(), objectId, outlineEntity.getSourceWorkflowId(), variables);
        stopWatch.lap("start workflow by Paas, instanceId : {} " + instanceId);
        log.info("start:sourceWorkflowId:{},objectId:{},var:{},instanceId:{}", sourceWorkflowId, objectId, variables, instanceId);
        outlineEntity = outlineDao.incCount(serviceManager.getTenantId(), outlineId);
        stopWatch.lap("incCount");
        log.debug("outlineCount: tenantId={},outlineId={},instanceCount={}", serviceManager.getTenantId(), outlineId, outlineEntity.getCount());
        stopWatch.logSlow(100);
        return instanceId;
    }


    /**
     * 取消流程实例
     */
    @Override
    public void cancelWorkflowInstance(RefServiceManager serviceManager, String workflowInstanceId, String reason, String cancelSource) {
        StopWatch stopWatch = StopWatch.createUnStarted("cancelWorkflowInstance");
        WorkflowInstance workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), workflowInstanceId);
        stopWatch.lap("获取流程实例 getWorkflowInstance");//ignoreI18n
        //为空校验
        assertInstanceNotNull("cancelWorkflowInstance", workflowInstance, workflowInstanceId);
        if (workflowInstance.isCompleted()) {
            log.debug("该流程已完成或已取消");
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_COMPLETE_OR_CANCEL);
        }
        // type !=null && type=workflow_bpm
        if (!Strings.isNullOrEmpty(workflowInstance.getType()) && !BPMConstants.APP_TYPE.equals(workflowInstance.getType())) {
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_CANCEL_INSTANCE_TYPE_ERROR);
        }

        String applicantId = workflowInstance.getApplicantId();
        Map<String, Boolean> functions = serviceManager.hasObjectFunctionPrivilege(workflowInstance.getEntityId());
        boolean stopBpmFunction = functions.getOrDefault(stopBPM.getCode(), false);
        stopWatch.lap(" authServiceProxy isCRMAdmin");
        //终止业务流程 只有  终止业务流程  功能权限---7.9.5
        if (ButtonFunctionUtil.isNeedStopBPM(Boolean.TRUE, stopBpmFunction) || serviceManager.isAdmin()) {
            String parallelRedisKey = getInstanceLockKey(serviceManager.getTenantId(), workflowInstanceId);
            if (!redisManager.setValueWithExpireTime(parallelRedisKey, org.apache.commons.lang.StringUtils.EMPTY, 10)) {
                throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NOT_CANCEL);
            }
            try {
                RemoteContext remoteContext = serviceManager.getContext();
                paasWorkflow.cancel(remoteContext, getOpinionExtensions(remoteContext, cancelSource), workflowInstanceId, reason);
            }
            finally {
                redisManager.delete(parallelRedisKey);
            }
            stopWatch.lap(" cancel ");
            log.debug("{} Cancel WorkflowInstance :{}", applicantId, workflowInstanceId);
        } else {
            log.debug("isCRMAdmin:{}, {} 无法取消 {} 创建的流程实例", false, serviceManager.getUserId(), applicantId);
            throw new BPMPermissionException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_AUTH_COMPLETE);
        }
    }


    /**
     * 获取流程实例详情
     */
    @Override
    public WorkflowInstance getWorkflowInstance(RefServiceManager serviceManager, String instanceId) {
        WorkflowInstance instance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), instanceId);
        //为空校验
        assertInstanceNotNull("cancelWorkflowInstance", instance, instanceId);
        return instance;
    }

    @Override
    public WorkflowInstanceLog getWorkflowInstanceLog(RefServiceManager serviceManager, String instanceId) {

        WorkflowInstance instance = getWorkflowInstance(serviceManager, instanceId);

        List<Task> tasks = bpmTaskService.getTasksByInstanceId(serviceManager, instanceId);
        List<Task> automaticAndQuartzTask = bpmTaskService.getAutomaticAndQuartzTaskByInstanceId(serviceManager, instanceId);


        WorkflowInstanceLog instanceLog = new WorkflowInstanceLog();

        instanceLog.setWorkflowId(instance.getWorkflowId());
        instanceLog.setSourceWorkflowId(instance.getSourceWorkflowId());
        instanceLog.setName(instance.getWorkflowName());
        instanceLog.setState(instance.getState());
        instanceLog.setCancelPersonId(instance.getModifier());
        instanceLog.setApplicantId(instance.getApplicantId());
        instanceLog.setStartTime(instance.getStart());
        instanceLog.setEndTime(instance.getEnd());
        instanceLog.setReason(instance.getReason());
        instanceLog.setSubmitter(Objects.nonNull(instance.getOuterSubmitter())?
                instance.getOuterSubmitter():Objects.nonNull(instance.getSubmitter())?instance.getSubmitter():instance.getApplicantId());

        List<TaskLog> taskLogs = TaskLog.fromAllTasks(serviceManager,tasks, automaticAndQuartzTask,instance);
        TaskLog.setTaskLogAfterOperations(serviceManager, taskLogs, instance);
        instanceLog.setSortedLogs(!serviceManager.isOuterUserId(), taskLogs);
        instanceLog.setEmployeeInfo(serviceManager.getEmployeeInfo(instanceLog.getPersonIds()));
        TaskLog.setInstanceLogAfterOperations(serviceManager, instanceLog, instance);
        if (StringUtils.isNotEmpty(instance.getCancelSource())){
            instanceLog.setCancelSourceMessage(CancelSourceEnum.valueOf(instance.getCancelSource()).getMessage(instance.getCancelSourceInfo()));
        }
        return instanceLog;
    }


    /**
     * 6.6 获取全部流程实例,目前只有对象详情页中需要; 所以不需要对象名称和数据名称
     * <p>
     * pageSize=2, pageNumber=1, orderBy=null, asc=false, objectId=5cafc9ea25cd7a0001cded72, state=null
     * <p>
     * 6.7  2019年07月30日16:02:51   营销流程支持实例的分页  添加了按照 sourceWorkflowId筛选 并返回entityId和objectId
     */
    @Override
    public PageResult<WorkflowInstanceVO> getWorkflowInstances(RefServiceManager serviceManager, InstanceState state, String objectId, int pageSize,
                                                               int pageNumber, String orderBy, String sourceWorkflowId) {

        PageResult<WorkflowInstance> result = new PageResult<>();
        if (Strings.isNullOrEmpty(objectId) && Strings.isNullOrEmpty(sourceWorkflowId)) {
            return new PageResult<>();
        }
        PageResult<MetadataInstance> instances = MetadataQuery.getInstancesByObjectId(serviceManager, objectId, sourceWorkflowId, state, pageNumber, pageSize);
        List<WorkflowInstance> workflowInstanceVOList = instances.getResult().stream().map(instance -> BeanUtils.transfer(instance, WorkflowInstance.class, (src, dst) -> {
            dst.setId(src.get_id());
            dst.setApplicantId(Objects.isNull(src.getApplicantId()) ? null : src.getApplicantId().get(0));
            dst.setStart(src.getStartTime());
            if (Objects.nonNull(src.getEndTime())) {
                dst.setEnd(Long.valueOf(src.getEndTime()));
            }
            dst.setEntityId(src.getObjectApiName());
            dst.setObjectId(src.getObjectDataId());
            if(StringUtils.isNotBlank(src.getTriggerSource())){
                dst.setTriggerSource(TriggerSource.valueOf(src.getTriggerSource()));
            }
            dst.setCurrentCandidateIds(src.getCurrent_candidate_ids());
            dst.setLastModifiedTime(src.getLast_modified_time());
            dst.setCancelPerson(CollectionUtils.isEmpty(src.getCancel_from_person()) ? null : src.getCancel_from_person().get(0));
            dst.setReason(src.getCancel_reason());
            dst.setErrorReason(src.getError_reason());
            if(CollectionUtils.isNotEmpty(src.getOut_owner())) {
                dst.setOuterSubmitter(src.getOut_owner().get(0));
            }
        })).collect(Collectors.toList());

        result.setDataList(workflowInstanceVOList);
        result.setTotal(instances.getTotal());

        if (result.getDataList() == null) {
            return new PageResult<>();
        }

        List<WorkflowInstanceVO> vos = result.getResult().stream().sorted((o1, o2) -> {
            long temp = o1.getStart() - o2.getStart();
            int cr = temp == 0L ? 0 : temp > 0L ? 1 : -1;
            return -cr;
        }).map(item ->
                WorkflowInstanceVO.fromWorkflowInstance(item)).collect(Collectors.toList());

        PageResult<WorkflowInstanceVO> rst = new PageResult<>();
        rst.setDataList(vos);
        rst.setTotal(result.getTotal());
        return rst;
    }

    @Override
    public PageResult<WorkflowInstanceVO> getWorkflowInstances(RefServiceManager serviceManager, InstanceState state, String entityId, String objectId, int pageSize, int pageNumber, String orderBy, String sourceWorkflowId) {
        PageResult<WorkflowInstance> result = paasWorkflow.getWorkflowInstances(serviceManager.getContext(),
                sourceWorkflowId, null, null, entityId, objectId, state, new Page(pageSize, pageNumber, orderBy, false), null);
        List<WorkflowInstance> dataList = result.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return new PageResult<>();
        }

        List<WorkflowOutline> workflowOutlineBySourceId = bpmDefinitionService.getWorkflowOutlineBySourceId(serviceManager, dataList.stream().
                map(WorkflowInstance::getSourceWorkflowId).collect(Collectors.toSet()));

        Map<String, WorkflowOutline> sourceWorkflowOfOutline = workflowOutlineBySourceId.stream().collect(Collectors.toMap(WorkflowOutline::getSourceWorkflowId, item -> item, (k1, k2) -> k2));

        List<WorkflowInstanceVO> vos = result.getResult().stream().sorted((o1, o2) -> {
            long temp = o1.getStart() - o2.getStart();
            int cr = temp == 0L ? 0 : temp > 0L ? 1 : -1;
            return -cr;
        }).map(item -> WorkflowInstanceVO.fromWorkflowInstance(item,sourceWorkflowOfOutline)).collect(Collectors.toList());

        PageResult<WorkflowInstanceVO> rst = new PageResult<>();
        rst.setDataList(vos);
        rst.setTotal(result.getTotal());
        return rst;
    }


    /**
     * 6.6 获取全部流程实例,目前只有对象详情页中需要; 所以不需要对象名称和数据名称
     * <p>
     * pageSize=2, pageNumber=1, orderBy=null, asc=false, objectId=5cafc9ea25cd7a0001cded72, state=null
     * <p>
     * 6.7  2019年07月30日16:02:51   营销流程支持实例的分页  添加了按照 sourceWorkflowId筛选 并返回entityId和objectId
     *
     *  营销流程代码还原,700 添加了数据详情页下 流程列表  走数据权限,营销流程不需要,此代码为690代码,原版粘贴过来
     */
    @Override
    public PageResult<WorkflowInstanceVO> getWorkflowInstancesSkipDataAuth(RefServiceManager serviceManager, InstanceState state, String objectId, int pageSize,
                                                                           int pageNumber, String orderBy, String sourceWorkflowId) {

        PageResult<WorkflowInstance> result = paasWorkflow.getWorkflowInstances(serviceManager.getContext(),
                sourceWorkflowId, null, null, null, objectId, state, new Page(pageSize, pageNumber, orderBy, false), null);
        if (result.getDataList() == null) {
            return new PageResult<>();
        }

        List<WorkflowInstanceVO> vos = result.getResult().stream().sorted((o1, o2) -> {
            long temp = o1.getStart() - o2.getStart();
            int cr = temp == 0L ? 0 : temp > 0L ? 1 : -1;
            return -cr;
        }).map(item ->
                WorkflowInstanceVO.fromWorkflowInstance(item)).collect(Collectors.toList());

        PageResult<WorkflowInstanceVO> rst = new PageResult<>();
        rst.setDataList(vos);
        rst.setTotal(result.getTotal());
        return rst;
    }

    @Override
    public EntireWorkflow getEntireWorkflowInstance(RefServiceManager serviceManager, String instanceId) {

        EntireWorkflow entireWorkflow = new EntireWorkflow();
        log.info("getEntireWorkflowInstance:context:{},instanceId:{}", serviceManager.getContext(), instanceId);
        StopWatch stopWatch = StopWatch.createUnStarted("getEntireWorkflowInstance");
        WorkflowInstance workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), instanceId, Boolean.TRUE);
        if (workflowInstance.getId() == null) {
            throw new BPMWorkflowInstanceNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NOT_FOUND);
        }

        entireWorkflow.setWorkflowId(workflowInstance.getWorkflowId());
        entireWorkflow.setSourceWorkflowId(workflowInstance.getSourceWorkflowId());
        //获取未结束的 activityInstanceId
        List<String> notEndedActivityInstanceId = workflowInstance.getNotEndedActivityInstanceId();
        PageResult<Task> tasksByInstanceIdsWithPage = paasWorkflow.getTasksByInstanceIdsWithPage(serviceManager.getContext(), instanceId, notEndedActivityInstanceId, false,
                new Page(workflowInstance.getActivityInstances().size(), 1, null, false));
        //通过activityInstanceId设置状态 linkAppName
        workflowInstance.setStateByActivityInstanceId(tasksByInstanceIdsWithPage.getDataList());

        stopWatch.lap("getWorkflowInstance");
        //填充下triggerSourceDesc
        fillTriggerSourceDesc(serviceManager.getContext(), workflowInstance);
        entireWorkflow.setWorkflowInstance(workflowInstance);
        WorkflowOutlineEntity workflowOutlineEntity = outlineDao.findBySourceWorkflowId(serviceManager.getTenantId(),
                workflowInstance.getSourceWorkflowId());
        if (workflowOutlineEntity == null) {
            throw new BPMWorkflowNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        stopWatch.lap("find WorkflowOutlineEntity");
        Map workflow = paasWorkflow.getWorkflowMap(serviceManager.getContext(), workflowInstance.getWorkflowId());
        stopWatch.lap("getWorkflow");
        workflow = WorkflowOutline.addWorkflowExt(workflowOutlineEntity, workflow);
        entireWorkflow.setWorkflow(workflow);
        stopWatch.lap("convertWorkflowToMap");
        FlowExtensionEntity workflowExtension = workflowExtensionDao.findOneFlowExtension(serviceManager.getTenantId(),
                workflowInstance.getWorkflowId());
        stopWatch.lap("findWorkflowExtension");
        if (workflowExtension == null) {
            log.error("getEntireWorkflowInstance: tenantId:{},workflowId:{} extension is not found!",
                    serviceManager.getTenantId(), workflowInstance.getWorkflowId());
            throw new BPMWorkflowExtensionNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SUPPORT_NOT_FOUND);
        }

        String objectName = serviceManager.getObjectNameAndDisplayName(
                workflowInstance.getEntityId(), Lists.newArrayList(workflowInstance.getObjectId())).get(workflowInstance.getObjectId());
        stopWatch.lap("getPaaSObjectName");
        workflowInstance.setObjectName(objectName);
//        todo 这个应该放在流程定义上比较合适
        String entryTypeName = serviceManager.getDescDisplayName(workflowOutlineEntity.getEntryType());
        workflowInstance.setEntryTypeName(entryTypeName);
        entireWorkflow.setEntryTypeName(entryTypeName);
        entireWorkflow.setPoolAndSvg(workflowExtension);

        List<MoreOperationManager.MoreOperation> instanceMoreOperations = moreOperationManager.getInstanceMoreOperations(
                serviceManager,
                serviceManager.hasObjectFunctionPrivilege(workflowInstance.getEntityId()),
                workflowInstance
        );
        entireWorkflow.setMoreOperations(instanceMoreOperations);

        stopWatch.logSlow(50);
        entireWorkflow.setEmployeeInfo(serviceManager.getEmployeeInfo(entireWorkflow.getWorkflowInstance().getPersonIds()));
        return entireWorkflow;
    }

    private void fillTriggerSourceDesc(RemoteContext context, WorkflowInstance workflowInstance){
        TriggerSource triggerSource = workflowInstance.getTriggerSource();
        String triggerSourceId = workflowInstance.getTriggerSourceId();
        String triggerSourceDesc = I18N.text(triggerSource.getI18NKey());
        if(TriggerSource.function.equals(triggerSource) && StringUtils.isNotBlank(triggerSourceId)){
            //函数触发
            triggerSourceDesc += "(" + triggerSourceId + ")";
        }
        if((TriggerSource.bpm.equals(triggerSource)
                || TriggerSource.workflow.equals(triggerSource)
                || TriggerSource.approval.equals(triggerSource))
                && StringUtils.isNotBlank(triggerSourceId)){
            //流程触发
            RemoteContext newContext = BeanUtils.transfer(context, RemoteContext.class);
            newContext.setAppId(TriggerSource.bpm.equals(triggerSource) ? BPMConstants.APP_ID : BPMConstants.CRM_APP_ID);
            GetWorkflow.Result workflowResult = paasWorkflow.getWorkflowAndRule(newContext, triggerSourceId);
            ExecutableWorkflowExt workflow = ExecutableWorkflowExt.of(workflowResult.getWorkflowJson());
            String workflowJson=JacksonUtil.toJson(workflow);
            if(Objects.nonNull(workflowResult) && Objects.nonNull(workflowResult.getResult()) && StringUtils.isNotBlank(workflowJson)){
                String name = I18N.text(I18NUtils.TRANSLATE_PRE + workflow.getId() + ".name");
                if(StringUtils.isBlank(name)){
                    name = I18N.text(I18NUtils.TRANSLATE_PRE + workflow.getSourceWorkflowId() + ".name");
                }
                if(StringUtils.isBlank(name)){
                    name = workflow.getName();
                }
                triggerSourceDesc += "(" + name + ")";
            }
        }
        workflowInstance.setTriggerSourceDesc(triggerSourceDesc);
    }

    @Override
    public Pair<Pair<WorkflowOutline, String>, List<WorkflowStats>> getWorkflowStatsDataByPG(RefServiceManager serviceManager, String sourceWorkflowId) {
        StopWatch stopWatch = StopWatch.createUnStarted("getWorkflowStatsData");

        WorkflowOutline workflowOutline = bpmDefinitionService.getWorkflowOutlineBySourceId(serviceManager, sourceWorkflowId);
        Map<String, String> activityIdAndNames = workflowOutline.getWorkflow()
                .getActivities()
                .stream()
                .filter(activity -> activity.instanceOf(UserTaskExt.class))
                .collect(Collectors.toMap((task -> task.getId()), (task -> task.getName())));

        /**
         * ---------------------------start---------------------------
         * 1. 从引擎侧查一下异常的,异常的应该不会特别多,故查询使用10000条查询
         * 如果从引擎侧获取的异常不为空,则需要将其添加到异常数据中
         */

        List<BpmTaskCountEntity> errorTaskStatics = TaskStatics.errorTask2TaskStateStatics(paasWorkflow
                .getTasksBySourceWorkflowIdAndState(serviceManager.getContext(), new Page(MetadataQuery.LIMIT, 1, "", false),
                        TaskState.error, sourceWorkflowId).getDataList());

        List<BpmTaskCountEntity> inProgressTaskStatics = bpmTaskRepository.findTaskBySourceWorkflowId(serviceManager.getContext(), sourceWorkflowId);

        Map<String, TaskStatics> taskStaticsMap = TaskStatics
                .build(ListUtils.union(inProgressTaskStatics, errorTaskStatics), activityIdAndNames,workflowOutline.getWorkflowId(), workflowOutline.getSourceWorkflowId());

        stopWatch.lap("fetch taskStats");

        /**
         * 是否需要获取outline信息,只取
         */

        WorkflowExtension workflowExtension = workflowOutline.getExtension();
        List<PoolEntity> pools = workflowExtension.getPools();

        Map<String, LaneStatics> laneStaticsMap = LaneStatics.buildLanleStatics(pools, taskStaticsMap,workflowOutline.getWorkflowId(), workflowOutline.getSourceWorkflowId());
        stopWatch.lap("fetch laneStats");
        /**
         * 4.统计流程实例数据
         */
        WorkflowInstanceStatics workflowInstanceStats = WorkflowInstanceStatics.build(sourceWorkflowId,
                workflowOutline.getName(),
                bpmInstanceRepository.findInstanceBySourceWorkflowId(serviceManager.getContext(), sourceWorkflowId),
                taskStaticsMap,workflowOutline.getWorkflowId());

        stopWatch.lap("fetch InstanceStatics");

        /**
         * 汇总
         */
        List<WorkflowBaseStatics> rstStats = Lists.newArrayList();
        rstStats.addAll(taskStaticsMap.values());
        rstStats.addAll(laneStaticsMap.values());
        rstStats.add(workflowInstanceStats);
        /**
         * ------------------------end----------------------------
         */
        I18NParser.parse(serviceManager.getTenantId(), rstStats);
        stopWatch.logSlow(100);
        List result = (List)CustomI18nProcessor.custom(workflowBase,serviceManager.getTenantId(), rstStats, null);
        return new Pair(new Pair(workflowOutline, CustomI18nProcessor.getI18NSvg(serviceManager.getTenantId(),workflowOutline.getSourceWorkflowId(), workflowOutline.getWorkflowId(),workflowExtension.getSvg())), result);
    }


    private void isSingleInstance(WorkflowOutlineEntity outlineEntity, RefServiceManager serviceManager, String objectId) {
        if (outlineEntity.isSingle()) {
            //1 查询寻是否有进行中的实例
            boolean hasProcessing = hasProcessingSourceWorkflow(serviceManager, outlineEntity.getSourceWorkflowId(), outlineEntity.getEntryType(), objectId);
            //2. 只处理没有实例的.如果为true 走老逻辑
            if (hasProcessing) {
                throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REPEAT_TRIGGER, outlineEntity.getName());
            } else {
                try {
                    //3. 如果为false,存一下
                    boolean hasRedisProcessing = redisManager.setValueWithExpire(Joiner.on("_").join(
                            serviceManager.getTenantId(),
                            outlineEntity.getSourceWorkflowId(),
                            outlineEntity.getEntryType(),
                            objectId,
                            Boolean.FALSE.toString()),
                            serviceManager.getTenantId() + outlineEntity.getSourceWorkflowId()
                    );
                    if (!hasRedisProcessing) {
                        throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REPEAT_TRIGGER, outlineEntity.getName());
                    }
                }catch (BPMInstanceException e){
                    log.warn("single instance process stop triggered multiple times,tenantId:{},entityId:{},objectId:{}", serviceManager.getTenantId(), outlineEntity.getEntryType(), objectId);
                    throw e;
                }catch (Exception e){
                    log.error("{},redis error, ", serviceManager.getTenantId(), e);
                }
            }
        }
    }
    @Override
    public TriggerWorkflow triggerWorkflow(RefServiceManager serviceManager, TriggerSource triggerSource, String triggerSourceId, String outlineId, String objectId) {
        return triggerWorkflow(serviceManager, triggerSource, triggerSourceId, outlineId, objectId, null);
    }

    private static final List<String> LIFE_STATUS_FIELD_LIST = Collections.singletonList(BPMConstants.MetadataKey.LIFE_STATUS);

    //TODO 优化下
    @Override
    public TriggerWorkflow triggerWorkflow(RefServiceManager serviceManager, TriggerSource triggerSource, String triggerSourceId, String outlineId, String objectId, String startInstanceId) {


        StopWatch stopWatch = StopWatch.createUnStarted("startWorkflow");
        WorkflowOutlineEntity outlineEntity = outlineDao.find(serviceManager.getTenantId(), outlineId);
        //0. 查询是否是单实例运行
        isSingleInstance(outlineEntity, serviceManager, objectId);

        String sourceWorkflowId = outlineEntity.getSourceWorkflowId();
        stopWatch.lap("query outline");
        log.debug("triggerWorkflow | context:{},sourceWorkflowId:{},entityId:{},objectId:{}", serviceManager.getContext(), sourceWorkflowId, outlineEntity.getEntryType(), objectId);
        boolean enabled = outlineEntity.isEnabled();
        if (!enabled) {
            log.info("{} Process Is Disabled", outlineId);
            throw new BPMStartException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_HAS_STOP, outlineEntity.getName());
        }
        stopWatch.lap("check outline enabled");


        ExecutableWorkflowExt executableWorkflow = WorkflowJsonUtil.convertExecutableWorkflowFromJson(outlineEntity.getWorkflowJson());
        List<VariableExt> workflowVariables = executableWorkflow.getVariables();
        Map<String, Object> variableMap = Maps.newHashMap();
        if (workflowVariables != null) {
            for (VariableExt workflowVariable : workflowVariables) {
                variableMap.put(workflowVariable.getId(), null);
            }
        }
        stopWatch.lap("fetch workflow variable list");

        String entityId = outlineEntity.getEntryType();
        Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey("0", new Pair<>(entityId,
                objectId));
        DataCacheHandler dataHandler = bpmTaskService.getDataHandler(serviceManager);
        Map<String, Object> data;
        boolean findDataTimeOut = false;
        try {
             data = dataHandler.findPartDataById(entityId, objectId, LIFE_STATUS_FIELD_LIST);
        }catch (UndeclaredThrowableException e){
            if(e.getUndeclaredThrowable() instanceof SocketTimeoutException){
                data = Maps.newHashMap();
                findDataTimeOut = true;
            }else {
                throw e;
            }
        }

        //数据为空或者生命状态为作废 不允许发起流程
        if (!findDataTimeOut &&
                (MapUtils.isEmpty(data) || "invalid".equals(data.get(BPMConstants.MetadataKey.LIFE_STATUS)))) {
            throw new BPMStartException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DATA_INVALID_OR_DELETED_NOT_TRIGGER_FLOW);
        }

        Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                null,
                outlineEntity.getWorkflowId(),
                variableKey,
                dataHandler);
        stopWatch.lap("set variables");


        TriggerWorkflowByRule.Result triggerRule = paasWorkflow.trigger(serviceManager.getContext(), triggerSource, triggerSourceId, outlineEntity.getEntryType(), objectId, outlineEntity.getSourceWorkflowId(), variables, startInstanceId);
        TriggerWorkflowByRule.TriggerResult result = triggerRule.getResult();
        String errMessage = triggerRule.getErrMessage();
        //解析异常rule信息
        RuleMessage ruleMessage = ConditionUtil.workflow_bpm.analysis(serviceManager.getTenantId(),
                new GetValueHandler() {
                    @Override
                    public Map<String, Object> getDesc(String tenantId, String entityId) {
                        return serviceManager.findDescribe(entityId, true, false);
                    }

                    @Override
                    public GetOrganization.OrganizationResult getOrganization(String tenantId, List<String> persons, List<String> deptIds) {
                        return new GetOrganization.OrganizationResult(serviceManager.getDeptByDeptIds(deptIds), serviceManager.getMembersByIds(persons));
                    }

                    @Override
                    public Map<String, String> getAreaLabelsAndCodes(String tenantId, List<String> codes) {
                        return serviceManager.getAreaLabelByCodes(tenantId, codes);
                    }

                    @Override
                    public List<Map<String, Object>> getCustomVariableTable() {
                        return executableWorkflow.getCustomVariableTable();
                    }

                    @Override
                    public Map<String, String> getDimensionObjNameByIds(List<String> dimensionIdList) {
                        RemoteContext context = serviceManager.getContext();
                        RemoteContext systemContext = new RemoteContext(context.getEa(), context.getTenantId(), context.getAppId(), BPMConstants.CRM_SYSTEM_USER);
                        return serviceManager.getDimensionObjDataList(systemContext, SearchTemplateUtils.getDimensionObjObjectList(dimensionIdList));
                    }
                },
                result.getRule());

        String instanceId = result.getInstanceId();
        stopWatch.lap("start workflow by Paas, instanceId : {} " + instanceId);
        log.info("start:sourceWorkflowId:{},objectId:{},var:{},instanceId:{}", sourceWorkflowId, objectId, variables, instanceId);
        outlineEntity = outlineDao.incCount(serviceManager.getTenantId(), outlineId);
        stopWatch.lap("incCount");
        log.debug("outlineCount: tenantId={},outlineId={},instanceCount={}", serviceManager.getTenantId(), outlineId, outlineEntity.getCount());
        stopWatch.logSlow(100);
        try{
            redisManager.delete(Joiner.on("_").join(
                            serviceManager.getTenantId(),
                            outlineEntity.getSourceWorkflowId(),
                            outlineEntity.getEntryType(),
                            objectId,
                            Boolean.FALSE.toString()));
        }catch (Exception e){
            log.info("remove single instance lock error : ", e);
        }
        return new TriggerWorkflow(instanceId, ruleMessage, errMessage);
    }


    @Override
    public TriggerWorkflow triggerWorkflowForRest(RefServiceManager serviceManager, TriggerSource triggerSource, String triggerSourceId, String outlineId, String entityId, String objectId,String sourceWorkflowId, String startInstanceId) {
        //兼容下函数 传的id可能是sourceWorkflowId
        if(StringUtils.isBlank(sourceWorkflowId)){
            if(Objects.nonNull(bpmDefinitionService.getWorkflowOutlineBySourceWorkflowId(serviceManager, outlineId))){
                sourceWorkflowId = outlineId;
            }
        }
        WorkflowOutline workflowOutline = bpmDefinitionService.getWorkflowOutlineBySourceIdAndOutlineId(serviceManager, sourceWorkflowId,outlineId);
        if (Objects.isNull(workflowOutline)) {
            throw new BPMWorkflowNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }

        if (workflowOutline.isDeleted()) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_SERVICE_DELETE, workflowOutline.getName());
        }

        if (!workflowOutline.isEnabled()) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_SERVICE_STOP, workflowOutline.getName());
        }

        ExecutableWorkflowExt workflow = workflowOutline.getWorkflow();
        outlineId = workflowOutline.getId();
        //TODO 看下 WorkflowGraph 算法
        WorkflowGraph workflowGraph = new WorkflowGraph(workflow);

        boolean needCheckDeadLoop = workflow.getActivities().stream().filter(
                activity -> (activity.instanceOf(ExecutionTaskExt.class))).anyMatch(
                activity -> workflowGraph.toExecutionTaskNoUserTask(activity.getId()));

        // 同一个对象,同一条数据,同一个流程 在指定时间内(20s) 只能触发2次
        if (needCheckDeadLoop) {
            bpmLoopBreakerManager.incAndCheck(serviceManager.getContext(), outlineId, entityId, objectId);
        }
        // TODO 以下方法还会查询outline
        return triggerWorkflow(serviceManager, triggerSource, triggerSourceId, outlineId, objectId, startInstanceId);
    }
    @Autowired
    private EngineDefinitionDao engineDefinitionDao;
    /**
     * 获取数据下所有进行中的实例
     * 查询扩展信息
     * 获取实例中没有end的activityId
     * 下发操作权限
     *
     * @param serviceManager
     * @param entityId
     * @param objectId
     * @return
     */
    @Override
    public List<GetInstanceByObject> getInstancesByObject(RefServiceManager serviceManager, String entityId, String objectId) {
        StopWatch stopWatch = StopWatch.createUnStarted("getInstancesByObject");

        Pair<List<Task>, List<WorkflowInstance>> queryDatas = paasWorkflow.getUncompletedTasks(serviceManager.getContext(), objectId);
        List<Task> tasks = queryDatas.getKey();
        stopWatch.lap("get all uncompletedTasks");

        List<String> workflowInstanceIds = tasks.stream().map(Task::getWorkflowInstanceId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workflowInstanceIds)) {
            return Lists.newArrayList();
        }
        Map<String, WorkflowInstance> workflowInstanceMap = queryDatas.getValue().stream()
                .filter(item-> workflowInstanceIds.contains(item.getId()))
                .collect(Collectors.toMap(WorkflowInstance::getId, v-> v,(v1, v2)->v2));

        stopWatch.lap("getWorkflowInstanceMap");

        Collection<WorkflowInstance> workflowInstances = workflowInstanceMap.values();

        if (CollectionUtils.isEmpty(workflowInstances)) {
            return Lists.newArrayList();
        }

        Set<String> workflowIds = workflowInstances.stream().map(WorkflowInstance::getWorkflowId).collect(Collectors.toSet());
        Map<String, List<PoolEntity>> workflowPools = workflowExtensionDao.findPoolsByWorkflowIds(
                serviceManager.getTenantId(),
                workflowIds);
        stopWatch.lap("findPoolsByWorkflowIds");
        Map<String, Map<String, String>> workflowAndActivityIdAndTypeMap = engineDefinitionDao.findActivityTypeMapByWorkflowIds(serviceManager.getTenantId(), WorkflowConstants.AppId.APP_ID_BPM, FlowType.workflow_bpm.name(), workflowIds.stream().collect(Collectors.toList()));
        stopWatch.lap("findDefinitionByWorkflowIds");
        // 同一条数据的,多个实例,获取功能权限的时候,只会获取同一个entityId的
        Map<String, Boolean> functionPrivilegeRst = serviceManager.hasObjectFunctionPrivilege(entityId);

        List<GetInstanceByObject> rst = workflowInstances.stream().map(workflowInstance -> {
            // 遍历时需要用到实例上的信息
            List<MoreOperationManager.MoreOperation> moreOperations =
                    moreOperationManager.getInstanceMoreOperations(serviceManager, functionPrivilegeRst,
                            workflowInstance);
            stopWatch.lap("getInstanceMoreOperations");
            //不需要查询流程日志操作
            return BeanUtils.transfer(workflowInstance, GetInstanceByObject.class, (src, dst) -> {
                dst.setMoreOperations(moreOperations);
                dst.setLanes(src,workflowAndActivityIdAndTypeMap.get(src.getWorkflowId()), workflowPools.get(src.getWorkflowId()));
            });
        }).sorted((o1,o2)-> o1.getStart() > o2.getStart() ? -1 : 0).collect(Collectors.toList());
        stopWatch.logSlow(10);
        for (GetInstanceByObject instance : rst) {
            instance.sortLanes();
        }
        return rst;
    }


    /**
     * 未找到实例抛出异常
     */
    private void assertInstanceNotNull(String methodName, WorkflowInstance instance, String instanceId) {
        if (null == instance) {
            log.debug(" methodName: {} ,instanceId : {} 未查询到流程实例", methodName, instanceId);
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NOT_EXISTS);
        }
        if (Strings.isNullOrEmpty(instance.getId())) {
            log.debug(" methodName: {} ,instanceId : {} 未查询到流程实例,实例数据为空", methodName, instanceId);
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NOT_EXISTS);
        }
    }


    /**
     * 通过对象sourceWorkflowId objectId,entityId 获取是否有正在运行的流程实例
     */
    private boolean hasProcessingSourceWorkflow(RefServiceManager serviceManager, String sourceWorkflowId, String entityId, String objectId) {
        StopWatch stopWatch = StopWatch.createUnStarted("hasProcessingSourceWorkflow");
        if (objectId == null) {
            log.error("hasProcessingSourceWorkflow:{} objectId is null", serviceManager.getContext());
            throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_NULL);
        }
        Page page = new Page();
        page.setPageNumber(1);
        page.setPageSize(10);
        PageResult<WorkflowInstance> currentInstances = paasWorkflow.getWorkflowInstances(
                serviceManager.getContext(), sourceWorkflowId, null, null, entityId, objectId, InstanceState.in_progress_or_error, page,
                "");
        log.info("fetch data,total:{},data.size:{}", currentInstances.getTotal(), currentInstances.getDataList().size());
        stopWatch.lap("fetch data");
        stopWatch.logSlow(100);
        return currentInstances.getTotal() > 0;
    }


    @Override
    public boolean recoveryCancelInstance(RefServiceManager serviceManager, String instanceId) {
        RecoveryCancelInstance.Result result = paasWorkflow.recoveryCancelInstance(serviceManager.getContext(), instanceId);

        return result.isSuccess();
    }

    @Override
    public void cancelWorkflowInstances(RefServiceManager serviceManager, String workflowInstanceId, String reason, String cancelSource) {
        try {
            RemoteContext remoteContext = serviceManager.getContext();
            paasWorkflow.cancel(remoteContext, getOpinionExtensions(remoteContext, cancelSource), workflowInstanceId, reason);
            log.info("cancel process:tenantId:{},:instanceId:{},reason:{}", serviceManager.getTenantId(), workflowInstanceId,reason);
        } catch (RuntimeException e) {
            if (!(e instanceof BPMBusinessException)) {
                throw e;
            }
        }
    }

    @Override
    public AfterRetry.RetryResult afterActionRetry(RefServiceManager serviceManager, String instanceId, int rowNum, int executeType) {
        return paasWorkflow.instanceAfterActionRetry(serviceManager.getContext(), instanceId, rowNum, executeType);
    }

    @Override
    public String getReason(RefServiceManager serviceManager, String entityId, String objectId) {
        Map<String, Object> data = null;
        try {
            data = metadataService.findDataById(serviceManager.getContext(),entityId,objectId,false,false,false,true,true,true,null).getObject_data();
        } catch (Throwable e) {
            //已删除 code:320002500
            log.warn("find data error,context:{},entityId:{},objectId:{}", serviceManager.getContext(), entityId, objectId, e);
        }
        String name = BPMI18N.PAAS_FLOW_BPM_CURRENT_INSTANCE_OR_TASK.text();
        if (MapUtils.isNotEmpty(data)) {
            name = String.valueOf(data.get(BPMConstants.MetadataKey.name));
        }
        return BPMI18N.PAAS_FLOW_BPM_OBJECT_DATA_REMOVE_INSTANCE_CANCEL.text(name);
    }

    @Override
    public String getTaskOrInstanceRelatedEntityId(RefServiceManager serviceManager, String instanceId, String taskId) {
        if (!Strings.isNullOrEmpty(taskId)) {
            Task task = bpmTaskService.getPaaSTask(serviceManager, taskId);
            if (task != null) {
                return task.getEntityId();
            }
        } else if (!Strings.isNullOrEmpty(instanceId)) {
            WorkflowInstance workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), instanceId);
            if (workflowInstance != null) {
                return workflowInstance.getEntityId();
            }
        }
        return StringUtils.EMPTY;
    }

    public static String getInstanceLockKey(String tenantId, String workflowInstanceId){
        return tenantId + UtilConstans.UNDERLINE + workflowInstanceId+ FlowType.workflow_bpm.name();
    }
    @Override
    public Integer fixedPGCancelReason(RemoteContext context, Page page, BPMInstanceObj pgInstance){
        Integer result = null;
        boolean shouldContinue = true;

        while (shouldContinue) {
            try {
                List<WorkflowInstance> workflowInstanceList = paasWorkflow.getWorkflowInstances(context, null, null, null, null, null, null, InstanceState.cancel, page).getResult();
                if(CollectionUtils.isEmpty(workflowInstanceList)){
                    result = (page.getPageNumber() - 1) * page.getPageSize();
                    shouldContinue = false;
                    break;
                }
                for (WorkflowInstance workflowInstance : workflowInstanceList) {
                    pgInstance.set_id(workflowInstance.getId());
                    pgInstance.setCancel_reason(StringUtils.isBlank(workflowInstance.getReason()) ? "" : workflowInstance.getReason());
                    pgInstance.setCancel_from_person(Lists.newArrayList(workflowInstance.getCanceler()));
                    metadataService.updateData(context, TransferDataConstants.APINAME_INSTANCE, pgInstance.get_id(), JSON.toJSONString(pgInstance), false, Boolean.FALSE);
                }
                if(workflowInstanceList.size() != page.getPageSize()){
                    result = (page.getPageNumber() - 1) * page.getPageSize() + workflowInstanceList.size();
                    shouldContinue = false;
                    break;
                }
            }catch (Exception e){
                log.error("fixedPGCancelReason is error, pageNumber:{}, e:{}", page.getPageNumber(), e);
                // 发生异常时也要继续下一页，避免无限循环
                shouldContinue = false;
                result = (page.getPageNumber() - 1) * page.getPageSize();
                break;
            }

            // 移动到下一页，继续处理
            page.setPageNumber(page.getPageNumber() + 1);
        }

        return result;
    }

    private Map<String, Object> getOpinionExtensions(RemoteContext context, String cancelSource) {
        Map<String, Object> opinionExtensions = Maps.newHashMap();
        String rstCancelSource;
        switch (CancelSourceEnum.valueOf(cancelSource)) {
            case function:
                rstCancelSource = CancelSourceEnum.function.name();
                opinionExtensions.put(BpmConstant.CANCEL_SOURCE_INFO, context.getFsPeerDisplayName());
                break;
            case person:
            case invalid:
            case console:
                rstCancelSource = cancelSource;
                break;
            case unknow:
            default:
                rstCancelSource = CancelSourceEnum.unknow.name();
                break;
        }
        opinionExtensions.put(BpmConstant.CANCEL_SOURCE, rstCancelSource);
        return opinionExtensions;
    }

}
