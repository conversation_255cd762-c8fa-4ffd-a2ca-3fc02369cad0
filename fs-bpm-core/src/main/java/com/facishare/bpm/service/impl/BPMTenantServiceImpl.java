package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMNoQuotaException;
import com.facishare.bpm.helper.LicenseHelper;
import com.facishare.bpm.model.GetBPMLicense;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.resource.managegroup.GetManageGroupConfig;
import com.facishare.bpm.model.resource.paas.license.GetProductVersion;
import com.facishare.bpm.model.tenant.DefinitionConfig;
import com.facishare.bpm.model.tenant.SkipPageFormToDo;
import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.remote.app.ErAppProxy;
import com.facishare.bpm.service.BPMTenantService;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.OneFlowDao;
import com.facishare.flow.mongo.bizdb.TenantDao;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;
import com.facishare.flow.mongo.bizdb.entity.TenantEntity;
import com.facishare.paas.I18N;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * Created by Aaron on 17/3/6.
 */
@Service
@Slf4j
public class BPMTenantServiceImpl implements BPMTenantService {

    @Autowired
    private TenantDao tenantDao;
    @Autowired
    private LicenseHelper licenseHelper;

    @Autowired
    private ErAppProxy appProxy;

    @Autowired
    private BpmSimpleDefinitionDao workflowOutlineDao;

    @Autowired
    private ManageGroupProxy manageGroupProxy;

    private static volatile Map<String, String> quatoMsgs = Maps.newConcurrentMap();

    static {
        ConfigFactory.getConfig("fs-bpm-quota-msgs", config -> quatoMsgs = config.getAll());
    }
    @Autowired
    private OneFlowDao oneFlowDao;
    /**
     * 如果是标准版：您所在企业购买的标准版，“业务流程管理”为赠送使用，如果您想新建更多业务流程，请购买企业版。
     * 如果是企业版：您所在企业目前支持｛10｝个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。
     *
     *
     * standard_edition=您所在企业购买的%s，“业务流程管理”为赠送使用，如果您想新建更多业务流程，请购买更高版本。
     * enterprise_edition=您所在企业目前支持%s个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。
     * other=当前版本只支持定义%s个业务流程，请升级版本或购买资源扩展包。
     */
    @Override
    public boolean hasQuota(RefServiceManager serviceManager) {
        TenantEntity tenant = tenantDao.findAndModify(serviceManager.getContext());
        if (tenant.isValidate()) {
            long quota = licenseHelper.getBPMQuota(serviceManager.getContext());
            if (quota == 0 && tenant.getSourceWorkflowQuota() == 0) {
                throw new BPMNoQuotaException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TENANT_NOT_QUOTA);
            }
            long useSourceWorkflowCount = workflowOutlineDao.getUseSourceWorkflowCount(serviceManager.getTenantId(), SupportFlow.simple.code);
            //配额信息全部来自于paas
            long last = quota - useSourceWorkflowCount;
            log.debug("hasQuota:tenantId:{},quota:{},last:{}", serviceManager.getTenantId(), quota, last);
            ProductVersionPojo version = licenseHelper.getCRMVersion(serviceManager.getContext());

            ////您所在企业目前支持%s个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。

            //version.getCurrentVersion() =  旗舰版,标准版,旗舰增强版
            String i18nKey = quatoMsgs.get("other");//quatoMsgs.getOrDefault(version.getCurrentVersion(), quatoMsgs.get("other"));
            if (last <= 0) {
                // 标准版 :您所在企业购买的%s，“业务流程管理”为赠送使用，如果您想新建更多业务流程，请购买更高版本。
                if (version.getCurrentVersion().equals(GetProductVersion.CRMVersion.standard_edition.name())) {
                    throw new BPMNoQuotaException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, I18N.text(i18nKey, quota));
                } else if (version.getCurrentVersion().equals(GetProductVersion.CRMVersion.enterprise_edition.name())) {
                    //企业版:您所在企业目前支持%s个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。
                    throw new BPMNoQuotaException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, I18N.text(i18nKey, quota));
                } else {
                    throw new BPMNoQuotaException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, I18N.text(i18nKey, quota));
                }
            }
        }
        return true;
    }

    @Override
    public long getQuota(RefServiceManager serviceManager) {
        return licenseHelper.getBPMQuota(serviceManager.getContext());
    }

    @Override
    public GetBPMLicense getQuotaDetail(RefServiceManager serviceManager) {
        //总数
        long quota = getQuota(serviceManager);
        //营销流程数量
        long marketQuota = workflowOutlineDao.getUseSourceWorkflowCountByFlowType(serviceManager.getTenantId(), BPMConstants.MARKET_FLOW_TYPE);
        TenantEntity tenant = tenantDao.find(serviceManager.getTenantId());
        boolean belongManageGroup = false;
        try {
            GetManageGroupConfig.FindDefinitionType findDefinitionType = manageGroupProxy.getManageGroupConfig(serviceManager.getContext(), null).getResult().getFindDefinitionType();
            belongManageGroup = !GetManageGroupConfig.FindDefinitionType.ALL_DEFINITION.equals(findDefinitionType);
        } catch (Exception e) {
            log.error("调用分管小组异常", e);
        }
        if (tenant != null) {
            // 总数 - 当前已使用的 = 剩余的
            long last = quota - workflowOutlineDao.getUseSourceWorkflowCount(serviceManager.getTenantId(), SupportFlow.simple.code);
            // 剩余配额没有负数,顺便把0一起给设置了
            if (last <= 0) {
                last = 0;
            }
            if (tenant.isValidate()) {
                return new GetBPMLicense(serviceManager.getTenantId(), quota, last, marketQuota, belongManageGroup);
            } else {
                return new GetBPMLicense(serviceManager.getTenantId(), 100, 99, marketQuota, belongManageGroup);
            }
        }
        return new GetBPMLicense(serviceManager.getTenantId(), quota, quota, marketQuota, belongManageGroup);
    }

    @Override
    public TenantEntity incSourceWorkflowCount(String tenantId) {
        TenantEntity tenant = tenantDao.incSourceWorkflowCount(tenantId);
        log.debug("tenant:incSourceWorkflowCount:tenantId:{},count:{}", tenant,
                tenant.getSourceWorkflowCount());
        return tenant;
    }

    @Override
    public TenantEntity reduceSourceWorkflowCount(String tenantId) {
        return tenantDao.reduceSourceWorkflowCount(tenantId);
    }

    @Override
    public TenantEntity incWorkflowCount(String tenantId) {
        TenantEntity tenant = tenantDao.incWorkflowCount(tenantId);
        log.debug("tenant:incWorkflowCount:tenantId:{},count:{}", tenant,
                tenant.getSourceWorkflowCount());
        return tenant;
    }

    @Override
    public DefinitionConfig getDefinitionConfig(RefServiceManager serviceManager) {
        return new DefinitionConfig(appProxy.hasPrivilege(serviceManager));
    }

    @Override
    public SkipPageFormToDo getSkipPageFromToDo(String tenantId) {
        TenantEntity tenantEntity = tenantDao.find(tenantId);
        if (Objects.isNull(tenantEntity)) {
            return SkipPageFormToDo.taskPage;
        }
        String skipPageFromToDo = tenantEntity.getSkipPageFromToDo();
        return StringUtils.isBlank(skipPageFromToDo) ? SkipPageFormToDo.taskPage : SkipPageFormToDo.valueOf(skipPageFromToDo);
    }

    @Override
    public TenantEntity updateSkipPageFromToDo(String tenantId, SkipPageFormToDo skipPageFromToDo) {
        return tenantDao.updateSkipPageFromToDo(tenantId, Objects.isNull(skipPageFromToDo) ?
                SkipPageFormToDo.taskPage.name() : skipPageFromToDo.name());
    }

    public void setTenantDao(TenantDao tenantDao) {
        this.tenantDao = tenantDao;
    }

}
