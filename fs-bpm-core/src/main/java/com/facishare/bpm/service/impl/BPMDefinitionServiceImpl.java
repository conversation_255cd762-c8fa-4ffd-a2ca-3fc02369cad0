package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.exception.*;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.DefineGenerateManager;
import com.facishare.bpm.manage.RedisManager;
import com.facishare.bpm.model.SimpleTransition;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.managegroup.GetManageGroupConfig;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.producer.OpenAPIProducerManager;
import com.facishare.bpm.proxy.I18NServiceProxy;
import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.proxy.OrganizationServiceProxy;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMWorkflowDraftService;
import com.facishare.bpm.util.MetadataUtils;
import com.facishare.bpm.util.WorkflowJsonUtil;
import com.facishare.bpm.util.verifiy.VerifyManager;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.BeanConvertUtil;
import com.facishare.bpm.utils.JsonUtil;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.I18NUtils;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.bpmn.definition.validate.UpdateHistoryDefinitionValidateHandler;
import com.facishare.fcp.util.MD5Util;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.TenantDao;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.paas.I18N;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.CONTROLLED_WORKFLOW_CANNOT_BE_CHANGED;
import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.CONTROLLED;

/**
 * <AUTHOR>
 * @date 17-2-25
 */
@Slf4j
@Service
@Data
public class BPMDefinitionServiceImpl implements BPMDefinitionService {
    @Autowired
    private BpmSimpleDefinitionDao outlineDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    private DefinitionExtensionDao workflowExtensionDao;
    @Autowired
    private BPMTenantServiceImpl bpmTenantService;
    @Autowired
    private TenantDao tenantDao;
    @Autowired
    private OpenAPIProducerManager openAPIProducerManager;
    @Autowired
    private BPMWorkflowDraftService bpmWorkflowDraftService;
    @Autowired
    private DefineGenerateManager defineGenerateManager;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private OrganizationServiceProxy organizationServiceProxy;
    @Autowired
    private ManageGroupProxy manageGroupProxy;
    @Autowired
    private I18NServiceProxy i18NServiceProxy;

    /**
     * 发布流程定义
     * <p>
     * needValidateQuota 是否需要检验配额
     * needValidateData  是否需要检验数据结构
     */
    @Override
    public String deployWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean needValidateQuota, boolean needValidateData) {
        if (needValidateQuota) {
            bpmTenantService.hasQuota(serviceManager);
        }
        return updateWorkflow(serviceManager, workflowOutline, false, needValidateData).getId();
    }

    @Override
    public String deployWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean needValidateQuota, boolean needValidateData, boolean skipTranslate) {
        if (needValidateQuota) {
            bpmTenantService.hasQuota(serviceManager);
        }
        return updateWorkflow(serviceManager, workflowOutline, false, needValidateData, skipTranslate).getId();
    }

    @Override
    public WorkflowOutlineEntity updateWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean isUpdate, boolean needValidateData){
        return updateWorkflow(serviceManager, workflowOutline, isUpdate, needValidateData, false);
    }

    /**
     * 更新流程定义
     */
    @Override
    public WorkflowOutlineEntity updateWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean isUpdate, boolean needValidateData, boolean skipTranslate) {
        checkControlled(workflowOutline.getControlStatus());
        if (isUpdate) {
            assertDefinitionNotFound(serviceManager.getTenantId(), workflowOutline.getId());
        }
        ExecutableWorkflowExt executableWorkflow = WorkflowOutline.fillExecutableWorkflowDetail(workflowOutline, needValidateData);
        if (needValidateData) {
            /**
             * 校验流程定义
             */
            Workflow workflow = new Workflow(
                    serviceManager,
                    workflowOutline.getName(),
                    workflowOutline.getEntryType(),
                    workflowOutline.getEntryTypeName(),
                    executableWorkflow,
                    workflowOutline.getRule(),
                    JacksonUtil.toJson(workflowOutline.getExtension()),
                    workflowOutline.getExternalFlow());

            workflow.setRangeAssignee(workflowOutline.getRangeAssignee());
            workflow.setServiceManager(serviceManager);
            VerifyManager.instance.execute(workflow);
        }

        // 已经存在了,并且是新建
        if (!isUpdate && !redisManager.setValueWithExpire(MD5Util.toMD5Hex(workflowOutline.getName().getBytes()), serviceManager.getTenantId())) {
            throw new BPMWorkflowNameDuplicateException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_RESET_BPM_FLOW_NAME);
        }

        defineGenerateManager.generateVariables(workflowOutline, serviceManager);
        /**
         * 校验workflowName是否重复
         */
        outlineDao.validateWorkflowNameDuplicate(serviceManager.getTenantId(), workflowOutline.getId(), workflowOutline.getName());

        /**
         * 部署
         */
        paasWorkflow.deploy(serviceManager.getContext(), isUpdate, workflowOutline);
        /**
         * 保存扩展
         */
        workflowExtensionDao.save(serviceManager.getTenantId(), WorkflowOutline.fromWorkflowOutLine(workflowOutline));
        String draftId = workflowOutline.getDraftId();
        // 定义保存时，如果是草稿，使用草稿id去取多语
        if (!isUpdate && !Strings.isNullOrEmpty(draftId)) {
            Pair<Map<String, String>, Map<String, String>> translate = i18NServiceProxy.getDefTranslateList(serviceManager.getTenantId(), draftId);
            workflowOutline.setNameTranslateInfo(translate.getKey());
            workflowOutline.setDescTranslateInfo(translate.getValue());
        }
        /**
         * 创建和更新 流程定义
         */
        WorkflowOutlineEntity workflowOutlineEntity = outlineDao.createOrUpdate(serviceManager.getTenantId(), WorkflowOutline.toOutlineEntity(serviceManager.getContext(), workflowOutline));

        if (workflowOutlineEntity != null && !Strings.isNullOrEmpty(draftId)) {
            bpmWorkflowDraftService.delete(serviceManager, draftId);
        }
        if (!skipTranslate && Objects.nonNull(workflowOutlineEntity)) {
            Map<String, String> nameTranslateInfo = workflowOutline.getNameTranslateInfo();
            Map<String, String> descTranslateInfo = workflowOutline.getDescTranslateInfo();
            I18NUtils.saveDefinitionNameAndDescTranslate(serviceManager.getTenantId(), workflowOutlineEntity.getSourceWorkflowId(), nameTranslateInfo, descTranslateInfo, workflowOutlineEntity.getName(), workflowOutlineEntity.getDescription());
        }
        return workflowOutlineEntity;
    }

    @Override
    public void updateHistory(RefServiceManager serviceManager, WorkflowOutline workflowOutline) {
        WorkflowOutline originDefinition = getDefinitionByWorkflowId(serviceManager, workflowOutline.getWorkflowId());
        UpdateHistoryDefinitionValidateHandler.getInstance(FlowType.workflow_bpm).execute(JsonUtil.fromJson(JsonUtil.toJson(originDefinition.getWorkflow()), Map.class),
                JsonUtil.fromJson(JsonUtil.toJson(workflowOutline.getWorkflow()), Map.class));
        paasWorkflow.updateHistoryDefinition(serviceManager.getContext(), workflowOutline);
    }

    private void checkControlled(String controlStatus) {
        if(CONTROLLED.equals(controlStatus)) {
            throw new BPMWorkflowDefVerifyException(CONTROLLED_WORKFLOW_CANNOT_BE_CHANGED);
        }
    }


    /**
     * 获取可用流程,根据主部门,组,角色进行筛查 todo 在工作流/BPM后动作触发工作流的地方,不能过滤 . 现因只有管理员才能编辑流程定义,管理员能够查询所有定义,不需要另外的系统身份.
     * 如有其他变更,注意!
     */
    @Override
    public List<WorkflowOutline> getAvailableWorkflows(RefServiceManager serviceManager, String entryType, String objectId, boolean filterCurrentObjectInstance, Integer externalFlow, Boolean validateScopes, boolean containOtherTypeFlow) {

        boolean isCRMAdmin = serviceManager.isAdmin();
        List<WorkflowOutlineEntity> outlineEntities;
        if (isCRMAdmin || Boolean.FALSE.equals(validateScopes)) {
            log.info("getAvailableWorkflows:isAdmin:true,context:{},entityId:{},objectId:{}", serviceManager.getContext(), entryType, objectId);
            outlineEntities = outlineDao.findByEntryType(serviceManager.getTenantId(), entryType, null, null, null, null, externalFlow, true, containOtherTypeFlow);
        } else {
            //查询当前人所属部门List<Integer>
            List<Integer> circleIds = serviceManager.getDeptIdsByUserId();
            //getDeptIdsByUserId不会返回全集团（999999） 手动加下 获取定义列表的时候能匹配上全集团
            if(Objects.nonNull(circleIds)){
                if(!circleIds.contains(BPMConstants.ORG_ALL_GROUP)){
                    circleIds.add(BPMConstants.ORG_ALL_GROUP);
                }
            }else {
                circleIds = Lists.newArrayList(BPMConstants.ORG_ALL_GROUP);
            }
            //查询当前人有哪些组List<GroupId>
            List<String> groupList = serviceManager.getGroupByUserId();
            //查询当前人有哪些角色List<RoleId>
            List<String> roleList = serviceManager.getRoleByUserId();
            outlineEntities = outlineDao.findByEntryType(serviceManager.getTenantId(), entryType,
                    Integer.valueOf(serviceManager.getUserId()), circleIds, groupList,
                    roleList, externalFlow, true, containOtherTypeFlow);

            log.info("getAvailableWorkflows:isAdmin:false,context:{},entityId:{},objectId:{}" +
                            ",circleIds:{},groupList:{},roleList:{}",
                    serviceManager.getContext(), entryType, objectId, circleIds, groupList, roleList);
        }
        // 流程名称和描述的数据级多语填充
        if (CollectionUtils.isNotEmpty(outlineEntities)) {
            Map<String, Pair<String, String>> translate = I18NUtils.getDefNameAndDescI18nByLangCode(
                    serviceManager.getTenantId(),
                    outlineEntities.stream().map(WorkflowOutlineEntity::getSourceWorkflowId).collect(Collectors.toSet()));
            if (MapUtils.isNotEmpty(translate)) {
                outlineEntities.forEach(outlineEntity -> {
                    String sourceWorkflowId = outlineEntity.getSourceWorkflowId();
                    Pair<String, String> translation = translate.get(sourceWorkflowId);
                    if (Objects.nonNull(translation)) {
                        if (StringUtils.isNotBlank(translation.getKey())) {
                            outlineEntity.setName(translation.getKey());
                        }
                        if (StringUtils.isNotBlank(translation.getValue())) {
                            outlineEntity.setDescription(translation.getValue());
                        }
                    }
                });
            }
        }


        if (filterCurrentObjectInstance && !Strings.isNullOrEmpty(objectId)) {
            Page page = new Page();
            page.setPageNumber(1);
            page.setPageSize(1000);
            //TODO 过滤掉当前正在进行的流程
            PageResult<WorkflowInstance> currentInstances = paasWorkflow.getWorkflowInstances(serviceManager.getContext(), null, null,
                    null, entryType, objectId,
                    InstanceState.in_progress_or_error,
                    page, null);
            if (currentInstances.getTotal() > 0) {
                List<String> sourceWorkflowIds = currentInstances.getDataList().stream().map(WorkflowInstance::getSourceWorkflowId).collect(Collectors.toList());
                log.info("getAvailableWorkflows:tenantId:{},entityId:{},objectId:{},sourceWorkflowIds:{}",
                        serviceManager.getTenantId(), entryType, objectId, sourceWorkflowIds);
                outlineEntities = outlineEntities.stream()
                        .filter(item -> !(item.isSingle() && sourceWorkflowIds.contains(item.getSourceWorkflowId())))
                        .collect(Collectors.toList());
            }

        }
        return outlineEntities.stream().map(WorkflowOutline::fromEntity).collect(Collectors.toList());
    }


    /**
     * 查询企业下所有的流程列表
     */
    @Override
    public PageResult<WorkflowOutline> getWorkflowOutlines(RefServiceManager serviceManager, WorkflowOutlineQuery query, Page page, String from, boolean isSupportPagingQuery) {


        log.debug("getWorkflowOutlines start : CONTEXT={}, QUERY={}", serviceManager.getContext(), query);
        StopWatch stopWatch = StopWatch.createUnStarted("getWorkflowOutlines");
        if (query == null) {
            query = new WorkflowOutlineQuery();
        }
        // 全量查询，置空name查询条件，由前端搜索
        if (!isSupportPagingQuery) {
            query.setName(null);
        }
        //获取分管小组配置信息
        PageResult<WorkflowOutlineEntity> outlinePageResult;
        GetManageGroupConfig.Result manageGroupConfig= manageGroupProxy.getManageGroupConfig(serviceManager.getContext(), from);
        GetManageGroupConfig.FindDefinitionType findDefinitionType = manageGroupConfig.getResult().getFindDefinitionType();
        if(GetManageGroupConfig.FindDefinitionType.NO_DEFINITION.equals(findDefinitionType)) {
            outlinePageResult = new PageResult<>();
        } else {
            outlinePageResult = outlineDao.find(serviceManager.getTenantId(), query, page, false, manageGroupConfig.getResult().getApiNames(), isSupportPagingQuery);
        }
        PageResult<WorkflowOutline> ret = new PageResult<>();
        List<WorkflowOutline> outlines = WorkflowOutline.fromBrieflyEntities(outlinePageResult.getDataList());
        //1 获取outlineid
        List<String> outlineIds = outlines.stream().map(WorkflowOutline::getId).collect(Collectors.toList());
        //2 通过outlineids获取所有的草稿列表
        Map<String, String> drafts = bpmWorkflowDraftService.getWorkflowDraftMapByOutlineIds(serviceManager, outlineIds);
        //3 将草稿set到outline中
        outlines.forEach(outline -> outline.setDraftId(drafts.get(outline.getId())));

        stopWatch.lap("findOutlines");
        if (!CollectionUtils.isEmpty(outlines)) {
            try {
                Map<String, String> displayNames = serviceManager.getSimpleEntityNames();
                stopWatch.lap("findDisplayNames");
                outlines.forEach(outline -> outline.setEntryTypeName(displayNames.getOrDefault(outline.getEntryType(),
                        BPMI18N.PAAS_FLOW_BPM_DRAFT_STOP_OR_DELETE.text(outline.getEntryTypeName()))));

                ;
            } catch (Throwable e) {
                log.error("查询流程定义列表,请求自定义对象获取当前企业下所有对象描述失败,自动跳过:{}", e);
            }
            Map<String, Pair<String, String>> translate = i18NServiceProxy.getDefNameAndDescI18nByLangCode(serviceManager.getTenantId(), outlines.stream().map(o -> o.getSourceWorkflowId()).collect(Collectors.toSet()), I18N.getContext().getLanguage());
            if(MapUtils.isNotEmpty(translate)) {
                for (WorkflowOutline outline : outlines) {
                    if(translate.containsKey(outline.getSourceWorkflowId())){
                        if(StringUtils.isNotBlank(translate.get(outline.getSourceWorkflowId()).getKey())) outline.setName(translate.get(outline.getSourceWorkflowId()).getKey());
                        if(StringUtils.isNotBlank(translate.get(outline.getSourceWorkflowId()).getValue())) outline.setDescription(translate.get(outline.getSourceWorkflowId()).getValue());
                    }
                }
            }
        }
        ret.setDataList(outlines);
        ret.setTotal(outlinePageResult.getTotal());
        stopWatch.lap("getWorkflowOutlines");
        stopWatch.logSlow(200);
        return ret;
    }

    /**
     * 删除流程定义,只要启动过流程实例,则不允许删除
     */
    @Override
    public boolean deleteWorkflowById(RefServiceManager serviceManager, String id) {
        WorkflowOutlineEntity workflowOutlineEntity = assertDefinitionNotFound(serviceManager.getTenantId(), id);
        checkControlled(workflowOutlineEntity.getControlStatus());
        WorkflowOutlineEntity entity = outlineDao.delete(serviceManager.getTenantId(), serviceManager.getUserId(), id);
        if (null != entity && entity.isDeleted()) {
//            TenantEntity tenant = bpmTenantService.reduceSourceWorkflowCount(serviceManager.getTenantId());
            openAPIProducerManager.sendDefinitionChange(serviceManager, id, entity.getName(), entity.getSourceWorkflowId()
                    , entity.getEntryType(), entity.isEnabled(), entity.isDeleted());
            log.info("deleteWorkflowById : CONTEXT={}, ID={}, tenantId:{}", serviceManager.getContext(), id, serviceManager.getTenantId());
            paasWorkflow.deleteDefine(serviceManager.getContext(), entity.getSourceWorkflowId());
            // 如果定义删除了,则将其草稿也一并删除
            bpmWorkflowDraftService.delete(serviceManager, id);
            return true;
        } else {
            log.warn("deleteWorkflowById faield : please check outline detail! TENANT_ID={}, OUTLINE_ID={}", serviceManager.getTenantId(), id);
            //throw new BPMWorkflowDefVerifyException("删除失败,请确认该流程是否已经停用");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_DELETE_ERROR_OF_STOP);
        }

    }


    /**
     * 根据id获取流程定义详情
     */
    @Override
    public WorkflowOutline getWorkflowOutlineById(RefServiceManager serviceManager, String id) {
        StopWatch stopWatch = StopWatch.createUnStarted("getWorkflowOutlineById");
        WorkflowOutlineEntity outlineEntity = outlineDao.find(serviceManager.getTenantId(), id);
        stopWatch.lap("findOutline");
        outlineEntity.setEntryTypeName(serviceManager.getDescDisplayName(outlineEntity.getEntryType()));
        GetWorkflow.Result workflowAndRule = paasWorkflow.getWorkflowAndRule(serviceManager.getContext(), outlineEntity.getWorkflowId());
        outlineEntity.setWorkflowJson(workflowAndRule.getWorkflowJson());
        outlineEntity.setRuleJson(workflowAndRule.getRuleJson());
        stopWatch.lap("setDisplayNames");
        stopWatch.logSlow(300);
        return WorkflowOutline.fromEntity(outlineEntity);
    }

    @Override
    public String getSourceWorkflowIdByOutlineId(RefServiceManager serviceManager, String id) {
        WorkflowOutlineEntity outlineEntity = outlineDao.find(serviceManager.getTenantId(), id);
        if (Objects.nonNull(outlineEntity)) {
            return outlineEntity.getSourceWorkflowId();
        }
        return null;
    }

    /**
     * 查询数据信息,并清空rule , TODO 后期和getWorkflowOutlineById 合并
     */
    @Override
    public WorkflowOutline getWorkflowOutlineByIdOfClearRule(RefServiceManager serviceManager, String id) {
        StopWatch stopWatch = StopWatch.createUnStarted("getWorkflowOutlineById");
        WorkflowOutlineEntity outlineEntity = outlineDao.find(serviceManager.getTenantId(), id);
        stopWatch.lap("findOutline");
        GetWorkflow.Result workflow = paasWorkflow.getWorkflowAndRule(serviceManager.getContext(), outlineEntity.getWorkflowId());
        stopWatch.lap("setDisplayNames");
        GetWorkflow.Detail detail = workflow.getResult();
        WorkflowRule workflowRule = WorkflowRule.convertFromJson(detail.getRuleJson());
        if (workflowRule != null) {
            workflowRule.clearWorkflowRule();
        }
        stopWatch.logSlow(300);
        return WorkflowOutline.fromEntity(outlineEntity, ExecutableWorkflowExt.of(workflow.getWorkflowJson()), workflowRule);
    }

    /**
     * 设置流程停用启用
     */
    @Override
    public boolean enableWorkflow(RefServiceManager serviceManager, String id, boolean enable) {
        assertDefinitionNotFound(serviceManager.getTenantId(), id);
        WorkflowOutlineEntity workflow = outlineDao.enable(serviceManager.getTenantId(), serviceManager.getUserId(), id, enable);
        if(Objects.nonNull(workflow) && StringUtils.isNotBlank(workflow.getSourceWorkflowId())){
            paasWorkflow.updateDefinitionStatus(serviceManager.getContext(), workflow.getSourceWorkflowId(), enable);
        }
        openAPIProducerManager.sendDefinitionChange(serviceManager,
                id,
                workflow.getName(),
                workflow.getSourceWorkflowId(),
                workflow.getEntryType(),
                workflow.isEnabled(),
                workflow.isDeleted());
        return true;
    }


    /**
     * 获取流程定义扩展
     */
    @Override
    public WorkflowExtension getWorkflowExtensionByWorkflowId(RefServiceManager serviceManager, String workflowId) {
        FlowExtensionEntity extensionEntity = workflowExtensionDao.findOneFlowExtension(serviceManager.getTenantId(), workflowId);
        return BeanUtils.transfer(extensionEntity, WorkflowExtension.class);
    }

    @Override
    public WorkflowExtension getWorkflowExtensionByWorkflowId(RemoteContext context, String workflowId) {
        FlowExtensionEntity extensionEntity = workflowExtensionDao.findOneFlowExtension(context.getTenantId(), workflowId);
        return BeanUtils.transfer(extensionEntity, WorkflowExtension.class);
    }


    /**
     * 根据sourceWorkflowId查询流程定义,不区分删除与否
     */
    @Override
    public WorkflowOutline getWorkflowOutlineBySourceId(RefServiceManager serviceManager, String sourceId) {
        WorkflowOutlineEntity outlineEntity = outlineDao.findBySourceWorkflowId(serviceManager.getTenantId(), sourceId);
        if (outlineEntity == null) {
            log.error("workflow not found from bpm db:tenantId:{},sourceId:{}", serviceManager.getTenantId(), sourceId);
            throw new BPMWorkflowNotFoundException(PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        GetWorkflow.Result workflowAndRule = paasWorkflow.getWorkflowAndRule(serviceManager.getContext(), outlineEntity.getWorkflowId());
        outlineEntity.setRuleJson(workflowAndRule.getRuleJson());
        outlineEntity.setWorkflowJson(workflowAndRule.getWorkflowJson());
        return WorkflowOutline.fromEntity(outlineEntity);
    }

    @Override
    public WorkflowOutline getWorkflowOutlineBySourceWorkflowId(RefServiceManager serviceManager, String sourceId) {
        WorkflowOutlineEntity outlineEntity = outlineDao.findBySourceWorkflowId(serviceManager.getTenantId(), sourceId);
        if (outlineEntity == null) {
            return null;
        }
        outlineEntity.setEntryTypeName(serviceManager.getDescDisplayName(outlineEntity.getEntryType()));
        GetWorkflow.Result workflowAndRule = paasWorkflow.getWorkflowAndRule(serviceManager.getContext(), outlineEntity.getWorkflowId());
        outlineEntity.setWorkflowJson(workflowAndRule.getWorkflowJson());
        outlineEntity.setRuleJson(workflowAndRule.getRuleJson());
        return WorkflowOutline.fromEntity(outlineEntity);
    }


    @Override
    public List<WorkflowOutline> getWorkflowOutlineBySourceId(RefServiceManager serviceManager, Set<String> sourceIds) {
        List<WorkflowOutlineEntity> outlineEntitys = outlineDao.findSimpleOutlineBySourceWorkflowIds(serviceManager.getTenantId(), sourceIds);
        if (CollectionUtils.isEmpty(outlineEntitys)) {
            return Lists.newArrayList();
        }
        return outlineEntitys.stream().map(WorkflowOutline::fromEntity).collect(Collectors.toList());
    }


    /**
     * 根据sourceIds 批量查询入口对象
     */
    @Override
    public Map<String, String> getWorkflowEntryTypeNameMap(RefServiceManager serviceManager, Collection<String> sourceIds) {
        if (sourceIds == null) {
            return null;
        }
        return outlineDao.getWorkflowEntryTypeNameMap(serviceManager.getTenantId(), sourceIds);
    }

    /**
     * 通过workflowId查询流程描述ExecutableWorkflow
     */
    @Override
    public ExecutableWorkflowExt getWorkflowById(RefServiceManager serviceManager, String workflowId) {
        return paasWorkflow.getWorkflow(serviceManager.getContext(), workflowId);
    }


    @Override
    public Map getActivityDefByActivityId(RefServiceManager serviceManager, String workflowId, String activityId) {
        ExecutableWorkflowExt executableWorkflow = getWorkflowById(serviceManager, workflowId);
//        获取当前节点信息
        ActivityExt activity = executableWorkflow.getActivityMaps().get(activityId);
        if (activity == null) {
            throw new BPMActivityNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NODE_DEFINE_NOT_FOUND);
        }
        Map result = WorkflowJsonUtil.convertActivityToMap(activity);
        //设置条件信息
        if (activity.instanceOf(ExclusiveGatewayExt.class) || (activity.instanceOf(UserTaskExt.class) && BPMTask.isApprovelActivity(activity))) {
            result.put("transitions", getActivityTransitionsDefByActivityId(serviceManager, executableWorkflow, activityId));
        }
        return result;
    }


    @Override
    public List<SimpleTransition> getActivityTransitionsDefByActivityId(RefServiceManager serviceManager, ExecutableWorkflowExt executableWorkflow, String activityId) {

        ActivityExt activity = executableWorkflow.getActivityMaps().get(activityId);
        //设置条件信息
        if (activity.instanceOf(ExclusiveGatewayExt.class) || (activity.instanceOf(UserTaskExt.class) && BPMTask.isApprovelActivity(activity))) {
            Map<String, String> activityId2Names = executableWorkflow.getActivities().stream().collect(Collectors.toMap(BaseModel::getId, BaseModel::getName));
            return executableWorkflow.getTransitions().stream()
                    .filter(transition -> transition.getFromId().equals(activity.getId())
                            || transition.getId().equals(activity.getDefaultTransitionId()))
                    .map(transition -> {
                        String toActivityName = activityId2Names.get(transition.getToId());
                        String description = transition.getDescription();
                        Integer orderNum = transition.getSerialNumber();
                        boolean isDefault = transition.getId().equals(activity.getDefaultTransitionId());
                        String id = transition.getId();
                        if (BPMTask.isApprovelActivity(activity)) {
                            description = "reject".equals(((Map) ((Map) ((List) (transition.getCondition()).get("conditions")).get(0)).get("right")).get("value")) ?
                                    BPMI18N.PAAS_FLOW_BPM_DISAGREE.text() : BPMI18N.PAAS_FLOW_BPM_AGREE.text();
                        }
                        return new SimpleTransition(id, description, toActivityName, orderNum, isDefault);
                    }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<WorkflowOutline> getAllWorkflowOutlines(RefServiceManager serviceManager) {
        List<WorkflowOutlineEntity> outlineEntites = outlineDao.findAll(serviceManager.getTenantId());
        return WorkflowOutline.fromBrieflyEntities(outlineEntites);
    }

    @Override
    public boolean destroyTenant(RefServiceManager serviceManager, boolean includeDescribe) {
        outlineDao.destroyWorkflowOutLineEntity(serviceManager.getTenantId(), includeDescribe);
        tenantDao.destroyTenant(serviceManager.getTenantId(), includeDescribe);
        paasWorkflow.batchDeleteInstance(serviceManager.getContext());
        if (includeDescribe) {
            paasWorkflow.batchDeleteDefinition(serviceManager.getContext());
        }
        return true;
    }

    @Override
    public WorkflowOutline getDefinitionByWorkflowId(RefServiceManager serviceManager, String workflowId) {

        // 去引擎查询历史版本
        GetWorkflow.Result workflowAndRule = paasWorkflow.getWorkflowAndRule(serviceManager.getContext(), workflowId);
        // 从数据库中查询流程定义详情(最新的)
        //TODO 存在关联关系的dao,需要提出一层
        String tenantId = serviceManager.getTenantId();
        ExecutableWorkflowExt workflow = ExecutableWorkflowExt.of(workflowAndRule.getWorkflowJson());
        String sourceWorkflowId = workflow.getSourceWorkflowId();
        WorkflowOutlineEntity outlineEntity = outlineDao.findBySourceWorkflowId(tenantId, sourceWorkflowId);
        outlineEntity.setWorkflowId(workflowId);
        // 流程名称多语处理
        outlineEntity.setName(i18NServiceProxy.getWorkflowName(tenantId, workflowId, sourceWorkflowId, outlineEntity.getName()));
        // 设置entityId
        outlineEntity.setEntryTypeName(serviceManager.getDescDisplayName(outlineEntity.getEntryType()));
        // 查询历史扩展信息
        FlowExtensionEntity extensionEntity = workflowExtensionDao.findOneFlowExtension(tenantId, workflowId);
        // 合并定义
        WorkflowOutline workflowOutline = WorkflowOutline.fromEntity(outlineEntity, workflow, WorkflowRule.convertFromJson(workflowAndRule.getRuleJson()));
        // 合并扩展信息,历史详情
        workflowOutline.setExtension(WorkflowExtension.fromEntity(extensionEntity));
        return workflowOutline;
    }

    // 获取正在使用的对象列表的时候,要全部,原因:任务进行中,但是定义没了
    @Override
    public Map<String, String> getUseApiNames(RefServiceManager serviceManager) {

        StopWatch stopWatch = StopWatch.createUnStarted("getUseApiNames");
        Map<String, String> entryTypes = outlineDao.findAllDefinitionEntryTypes(serviceManager.getTenantId());
        stopWatch.lap("findAllDefinitionEntryTypes");
        Map<String, String> simpleEntityNames = serviceManager.getSimpleEntityNamesBySocketConfig();
        stopWatch.lap("getSimpleEntityNamesBySocketConfig");
        Map<String, String> entityNames = Maps.newHashMap();
        if (MapUtils.isNotEmpty(simpleEntityNames)) {
            entityNames = entryTypes.keySet().stream().filter(k -> {
                return !Strings.isNullOrEmpty(simpleEntityNames.get(k));
            }).collect(Collectors.toMap(k1 -> k1, k2 -> {
                return simpleEntityNames.get(k2);
            }));
        }
        stopWatch.lap("traverse");
        Map<String, String> sortMap;
        if (MapUtils.isNotEmpty(entityNames)) {
            sortMap = MetadataUtils.sortMapByKey(entityNames);
        } else {
            sortMap = MetadataUtils.sortMapByKey(entryTypes);
        }

        stopWatch.lap("sort");
        stopWatch.logSlow(1000);
        return sortMap;
    }

    @Override
    public WorkflowLogs.WorkflowLogsPage getWorkflowLogs(RefServiceManager serviceManager, String sourceWorkflowId, Page page) {
        return paasWorkflow.getWorkflowLogs(serviceManager.getContext(), sourceWorkflowId, page).getResult();
    }

    @Override
    public WorkflowOutline getWorkflowOutlineBySourceIdAndOutlineId(RefServiceManager serviceManager, String sourceWorkflowId, String outlineId) {
        WorkflowOutlineEntity workflowOutlineEntity = outlineDao.getWorkflowOutlineBySourceIdAndOutlineIdWithDeleted(serviceManager.getTenantId(), sourceWorkflowId, outlineId);
        if (Objects.nonNull(workflowOutlineEntity) && workflowOutlineEntity.isEnabled()) {
            GetWorkflow.Result workflowAndRule = paasWorkflow.getWorkflowAndRule(serviceManager.getContext(), workflowOutlineEntity.getWorkflowId());
            workflowOutlineEntity.setWorkflowJson(workflowAndRule.getWorkflowJson());
            workflowOutlineEntity.setRuleJson(workflowAndRule.getRuleJson());
        }
        return WorkflowOutline.fromEntity(workflowOutlineEntity);
    }

    @Override
    public String convertDefinitionFromOtherType(RefServiceManager serviceManager, String id, String flowName){
        //先根据sourceWorkflowId查询
        WorkflowOutline originOutline = this.getWorkflowOutlineBySourceWorkflowId(serviceManager, id);
        if(Objects.isNull(originOutline)){
            //根据outLineId查询
            originOutline = this.getWorkflowOutlineById(serviceManager, id);
        }
        if(Objects.isNull(originOutline)){
            throw new BPMWorkflowNotFoundException(PAAS_FLOW_BPM_DEFINE_NOT_FOUND);
        }
        //判断是否是其他类型的定义,当前仅支持简易流程
        if(Objects.isNull(originOutline.getSupportFlow())){
            throw new BPMWorkflowNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NOT_SUPPORT_TRANSFER_DEFINITION);
        }
        WorkflowOutline newOutline = WorkflowOutline.transferOutlineToCreate(originOutline, flowName);
        BeanConvertUtil.toWorkflowOutline(newOutline, serviceManager.getContext());
        return this.deployWorkflow(serviceManager, newOutline, true, true);
    }

    private WorkflowOutlineEntity assertDefinitionNotFound(String tenantId, String id) {
        return outlineDao.find(tenantId, id);
    }

}
