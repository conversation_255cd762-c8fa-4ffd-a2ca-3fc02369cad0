package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.GetBPMLicense;
import com.facishare.bpm.model.tenant.DefinitionConfig;
import com.facishare.bpm.model.tenant.SkipPageFormToDo;
import com.facishare.flow.mongo.bizdb.entity.TenantEntity;

/**
 * Created by <PERSON> on 17/3/6.
 */
public interface BPMTenantService {
    /**
     * 获取企业 是否还有配额
     * @return
     */
    boolean hasQuota(RefServiceManager serviceManager);

    /**
     * 获获取配额数
     * @return
     */
    long getQuota(RefServiceManager serviceManager);

    /**
     * 增加流程数
     * @param tenantId
     * @return
     */
    TenantEntity incSourceWorkflowCount(String tenantId);

    /**
     * 删除时减少流程数
     * @param tenantId
     * @return
     */
    TenantEntity reduceSourceWorkflowCount(String tenantId);

    /**
     * 增加流程数目  不分版本
     * @param tenantId
     * @return
     */
    TenantEntity incWorkflowCount(String tenantId);

    DefinitionConfig getDefinitionConfig(RefServiceManager serviceManager);

    //获取业务流程配额数量 ,总数,剩余数
    GetBPMLicense getQuotaDetail(RefServiceManager serviceManager);

    SkipPageFormToDo getSkipPageFromToDo(String tenantId);

    TenantEntity updateSkipPageFromToDo(String tenantId, SkipPageFormToDo skipPageFromToDo);
}
