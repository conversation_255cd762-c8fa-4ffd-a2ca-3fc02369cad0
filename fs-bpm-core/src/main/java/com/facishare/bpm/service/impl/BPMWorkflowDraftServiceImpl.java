package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMDraftException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.proxy.I18NServiceProxy;
import com.facishare.bpm.service.BPMWorkflowDraftService;
import com.facishare.bpm.utils.i18n.I18NUtils;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.DefinitionDraftDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowDraftEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 26/02/2017.
 */
@Slf4j
@Service
public class BPMWorkflowDraftServiceImpl implements BPMWorkflowDraftService {

    @Autowired
    private DefinitionDraftDao definitionDraftDao;

    @Autowired
    private BPMTenantServiceImpl bpmTenantService;

    @Autowired
    private I18NServiceProxy i18NServiceProxy;


    //TODO 添加一个配额的校验逻辑
    @Override
    public String saveWorkflowDraft(RefServiceManager serviceManager, WorkflowOutlineDraft outline, boolean needValidateQuota) {
        //如果需要检验配额
        if (needValidateQuota) {
            if ((getDraftQuota(serviceManager) <= getDraftUseQuota(serviceManager))) {
                log.info("{}草稿配额数量为:{},已用配额数为:{}", serviceManager.getContext().toString(), getDraftQuota(serviceManager), getDraftUseQuota(serviceManager));
                // throw new BPMDraftException("草稿配额数量已达上限:草稿配额为定义配额的两倍");
                throw new BPMDraftException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DRAFT_QUOTA_ERROR);
            }
        }

        log.debug("saveWorkflowDraft start");
        outline.setLastModifiedBy(serviceManager.getUserId());
        String outlineId = outline.getOutlineId();
        // 如果outlineId 为空,则表示没有定义,直接保存即可
        String tenantId = serviceManager.getTenantId();
        if (!Strings.isNullOrEmpty(outlineId)) {
            List<WorkflowDraftEntity> drafts = definitionDraftDao.getWorkflowDraftByOutlineIds(tenantId, Lists.newArrayList(outlineId));
            if (CollectionUtils.isNotEmpty(drafts)) {
                throw new BPMDraftException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DRAFT_ALREADY_EXIST);
            }
        }
        outline.checkDiagram();
        WorkflowDraftEntity entity = WorkflowOutlineDraft.createDraft(outline);
        String id = definitionDraftDao.createOrUpdate(serviceManager.getTenantId(), entity).getId();
        Map<String, String> nameTranslateInfo = outline.getNameTranslateInfo();
        Map<String, String> descTranslateInfo = outline.getDescTranslateInfo();
        I18NUtils.saveDefinitionNameAndDescTranslate(tenantId, outline.getDraftId(), nameTranslateInfo, descTranslateInfo, outline.getName(), outline.getDescription()); // 这里用草稿id去保存多语
        return id;
    }


    //TODO 适配对接流程的配额
    @Override
    public long getDraftQuota(RefServiceManager serviceManager) {
        //获取所有流程定义的配额数量
        long quotaCount = bpmTenantService.getQuota(serviceManager);
        return quotaCount * 2;
    }


    @Override
    public int getDraftUseQuota(RefServiceManager serviceManager) {
        return definitionDraftDao.getWorkflowDraftCount(serviceManager.getTenantId());
    }


    @Override
    public void updateWorkflowDraft(RefServiceManager serviceManager, WorkflowOutlineDraft outline) {
        log.debug("updateWorkflowDraft start");
        outline.setLastModifiedBy(serviceManager.getUserId());
        outline.checkDiagram();
        WorkflowDraftEntity entity = WorkflowOutlineDraft.createDraft(outline);
        entity = definitionDraftDao.createOrUpdate(serviceManager.getTenantId(), entity);
        if (Objects.isNull(entity)) {
            throw new BPMDraftException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DRAFT_NOT_FOUND);
        }
        Map<String, String> nameTranslateInfo = outline.getNameTranslateInfo();
        Map<String, String> descTranslateInfo = outline.getDescTranslateInfo();
        I18NUtils.saveDefinitionNameAndDescTranslate(serviceManager.getTenantId(), outline.getDraftId(), nameTranslateInfo, descTranslateInfo, outline.getName(), outline.getDescription()); // 这里用草稿id去保存多语
        log.info("updateWorkflowDraft success : id={}", entity.getId());

    }

    @Override
    public void delete(RefServiceManager serviceManager, String id) {
        definitionDraftDao.delete(serviceManager.getTenantId(), id);
    }


    @Override
    public Map<String, String> getWorkflowDraftMapByOutlineIds(RefServiceManager serviceManager, List<String> outlineIds) {
        List<WorkflowDraftEntity> workflowDraftEntities = definitionDraftDao.getWorkflowDraftByOutlineIds(serviceManager.getTenantId(), outlineIds);
        if (CollectionUtils.isNotEmpty(workflowDraftEntities)) {
            return workflowDraftEntities.stream().collect(Collectors.toMap((WorkflowDraftEntity::getOutlineId), (WorkflowOutlineEntity::getId)));
        }
        return Maps.newHashMap();
    }


    @Override
    public PageResult<WorkflowOutlineDraft> getWorkflowDrafts(RefServiceManager serviceManager, WorkflowOutlineQuery query, Page page, boolean supportPagingQuery) {

        log.debug("getWorkflowDrafts start : CONTEXT={}, QUERY={}", serviceManager.getContext(), query);
        StopWatch stopWatch = StopWatch.createUnStarted("getWorkflowDrafts");
        PageResult<WorkflowDraftEntity> draftPageResult = definitionDraftDao.find(serviceManager.getTenantId(), query, page, supportPagingQuery);
        PageResult<WorkflowOutlineDraft> ret = new PageResult<>();
        List<WorkflowOutlineDraft> draftOutlines = WorkflowOutlineDraft.fromBrieflyDraftEntities(draftPageResult.getDataList());
        stopWatch.lap("findDrafts");
        if (!CollectionUtils.isEmpty(draftOutlines)) {
            Map<String, Pair<String, String>> translateMap = i18NServiceProxy.getDefNameAndDescI18nByLangCode(serviceManager.getTenantId(), draftOutlines.stream().map(WorkflowOutlineDraft::getDraftId).collect(Collectors.toSet()), I18NUtils.getContext().getLanguage());
            Map<String, String> displayNames = serviceManager.getSimpleEntityNames();
            stopWatch.lap("findDisplayNames");
            Map<String, Pair<String, String>> finalTranslateMap = MapUtils.isEmpty(translateMap) ? Maps.newHashMap() : translateMap;
            draftOutlines.forEach(outline -> {
                outline.setEntryTypeName(displayNames.getOrDefault(outline.getEntryType(),
                        BPMI18N.PAAS_FLOW_BPM_DRAFT_STOP_OR_DELETE.text(outline.getEntryTypeName())));
                Pair<String, String> translation = finalTranslateMap.get(outline.getDraftId());
                if (Objects.nonNull(translation)) {
                    if (StringUtils.isNotBlank(translation.getKey())) {
                        outline.setName(translation.getKey());
                    }
                    if (StringUtils.isNotBlank(translation.getValue())) {
                        outline.setDescription(translation.getValue());
                    }
                }
            });
        }
        ret.setDataList(draftOutlines);
        ret.setTotal(draftPageResult.getTotal());
        stopWatch.lap("getWorkflowOutlineDrafts");
        stopWatch.logSlow(200);
        return ret;
    }

    @Override
    public WorkflowOutlineDraft getWorkflowDraftById(RefServiceManager serviceManager, String id) {
        WorkflowDraftEntity draftEntity = definitionDraftDao.findById(serviceManager.getTenantId(), id);
        draftEntity.setEntryTypeName(serviceManager.getDescDisplayName(draftEntity.getEntryType()));
        Map<String, Pair<String, String>> translateMap = i18NServiceProxy.getDefNameAndDescI18nByLangCode(serviceManager.getTenantId(), Sets.newHashSet(id), I18NUtils.getContext().getLanguage());
        if (MapUtils.isNotEmpty(translateMap) && Objects.nonNull(translateMap.get(id))) {
            Pair<String, String> translation = translateMap.get(id);
            if (StringUtils.isNotBlank(translation.getKey())) {
                draftEntity.setName(translation.getKey());
            }
            if (StringUtils.isNotBlank(translation.getValue())) {
                draftEntity.setDescription(translation.getValue());
            }
        }
        return WorkflowOutlineDraft.fromEntity(draftEntity);
    }


}
