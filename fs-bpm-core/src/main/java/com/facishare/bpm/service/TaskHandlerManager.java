package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.InstanceVariableManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey;
import com.facishare.bpm.model.paas.engine.bpm.CompleteTask;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.util.MetadataUtils;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * Created by wangz on 17-3-9.
 */
@Service
public class TaskHandlerManager {
    @Autowired
    private BizTaskDataDao bizTaskDataDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    private InstanceVariableManager instanceVariableManager;
    public TaskHandlerManager() {
    }

    @PostConstruct
    public void init() {
        TaskHandlerType.bizTaskDataDao = bizTaskDataDao;
        TaskHandlerType.paasWorkflow = paasWorkflow;
        TaskHandlerType.instanceVariableManager = instanceVariableManager;
    }

    public TaskHandler createTaskHandler(String taskType) {
        return TaskHandlerType.valueOf(taskType);
    }


    public interface TaskHandler {
        /**
         * @param task          任务的基本信息
         * @param opinion       当前处理的意见  审批时会用到
         * @param completedData 当前节点的数据  目前里只是在审批时 放审批结果使用
         */
        CompleteTask.Result execute(
                RefServiceManager serviceManager,
                Task task,
                String opinion,
                Map<String, Object> completedData,
                Integer addOrReplaceNextTaskAssignee,
                Map<String, Object> nextTaskAssignee,
                DataCacheHandler dataCacheHandler) throws Exception;
    }


    public enum TaskHandlerType implements TaskHandler {
        update {
            @Override
            public CompleteTask.Result execute(
                    RefServiceManager serviceManager,
                    Task task,
                    String opinion,
                    Map<String, Object> completedData,
                    Integer addOrReplaceNextTaskAssignee,
                    Map<String, Object> nextTaskAssignee,
                    DataCacheHandler dataCacheHandler) throws Exception {

                RemoteContext context = serviceManager.getContext();

                String taskId = task.getId();
                String objectId = task.getObjectId();
                String activityId = task.getActivityId();
                String entityId = task.getEntityId();

                Map<String, Object> metaData = dataCacheHandler.getData(entityId, objectId);

                verifyRequiredField(serviceManager.findDescribe(entityId, false, false),
                        metaData, task.getForm());

                Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                        objectId));

                Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                        task.getWorkflowInstanceId(),
                        task.getWorkflowId(),
                        variableKey,
                        dataCacheHandler);

                CompleteTask.Result completeTaskResult = paasWorkflow.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                        opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, null);

                //TODO update 看是否可以删除
                //bizTaskDataDao.snapshotTaskData(taskId, context.getTenantId(), activityId, metaData);
                log.info("update:setFieldsValue:{}", JacksonUtil.toJson(variables));
                return completeTaskResult;
            }

            /**
             *必填字段不能为空，不然不能完成任务
             **/
            List areaType = Lists.newArrayList("province", "city", "district");

            private void verifyRequiredField(Map<String, Object> desc, Map<String, Object> data, List<List<Map<String,
                    Object>>> forms) {
                if (data == null) {
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND);
                }

                Map<String, Object> fieldDescs = MapUtil.instance.getMap(desc, MetadataKey.fields);
                List<String> noValueFieldLabels = Lists.newArrayList();
                if (forms != null) {
                    for (List<Map<String, Object>> fields : forms) {
                        for (Map<String, Object> field : fields) {
                            String fieldName = (String) field.get(ExtensionKey.name);
                            Map<String, Object> fieldDesc = MapUtil.instance.getMap(fieldDescs, fieldName);

                            // 字段描述为空|| 国家省市区||字段描述中的isActive=false,获取不到默认为true,即  字段被删除或者字段被禁用了,则不去校验这类字段,让其可以保存入库
                            if (MapUtils.isEmpty(fieldDesc) || areaType.contains(field.get(ExtensionKey.type)) || !(boolean) fieldDesc.getOrDefault(BPMConstants.MetadataKey.isActive, true)) {
                                continue;
                            }

                            boolean required = oneTrue(field.getOrDefault(ExtensionKey.required, false), fieldDesc.get
                                    (MetadataKey.isRequired));
                            //必填 & 字段描述存在 & 数据不存在，则验证不通过
                            if (required && MetadataUtils.isNullOrEmptyObject(data.get(fieldName)) && !TaskHelper.hasInheritType(fieldDesc)) {
                                noValueFieldLabels.add((String) field.get(MetadataKey.label));
                            }
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(noValueFieldLabels)) {
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION,
                            BPMI18N.PAAS_FLOW_BPM_REQUIRED_FILED_ERROR_MSG2.text().replaceFirst("-", String.join(",", noValueFieldLabels)));
                }
            }

            private boolean oneTrue(Object condition, Object... conditions) {
                Boolean ret = Boolean.class.isInstance(condition) ? (Boolean) condition : Boolean.FALSE;
                if (Boolean.TRUE.equals(ret)) {
                    return true;
                }

                for (Object c : conditions) {
                    ret = Boolean.class.isInstance(c) ? (Boolean) c : Boolean.FALSE;
                    if (Boolean.TRUE.equals(ret)) {
                        return true;
                    }
                }

                return false;
            }
        },
        operation {
            @Override
            public CompleteTask.Result execute(
                    RefServiceManager serviceManager,
                    Task task,
                    String opinion,
                    Map<String, Object> completedData,
                    Integer addOrReplaceNextTaskAssignee,
                    Map<String, Object> nextTaskAssignee,
                    DataCacheHandler dataCacheHandler) throws Exception {

                RemoteContext context = serviceManager.getContext();

                String taskId = task.getId();
                String objectId = task.getObjectId();
                String entityId = task.getEntityId();
                String activityId = task.getActivityId();
                int activityInstanceId = task.getActivityInstanceId();


                Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                        objectId));


                Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                        task.getWorkflowInstanceId(),
                        task.getWorkflowId(),
                        variableKey, dataCacheHandler);

                if (needSnapShot(context.getTenantId(), task, completedData)) {
                    bizTaskDataDao.snapshotTaskData(context,taskId, activityId,activityInstanceId, snapshot(completedData));
                }
                return paasWorkflow.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                        opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, null);
            }

            private boolean needSnapShot(String tenantId, Task task, Map<String, Object> snapshot) {
                String entityId = (String) task.getBpmExtension().get(ExtensionKey.entityId);
                String actionCode = (String) task.getBpmExtension().get(ExtensionKey.actionCode);



                if (MetadataKey.APINAME_LEADSOBJ.equals(entityId) && MetadataKey.leadsObjHandleActionCode
                        .equals(actionCode)) {
                    verifySnapshot(tenantId, task.getId(), snapshot);
                    return true;
                } else {
                    return false;
                }
            }

            private void verifySnapshot(String tenantId, String taskId, Map<String, Object> snapshot) {
                if (snapshot == null) {
                    snapshot = Maps.newHashMap();
                    snapshot.put(ExtensionKey.actionCode, "--");
                    snapshot.put(ExtensionKey.actionLabel, "--");
                    log.info("verifySnapshot:not set actionCode and actionLabel");
                }
                String actionCode = (String) snapshot.get(ExtensionKey.actionCode);
                String actionLabel = (String) snapshot.get(ExtensionKey.actionLabel);

                if (Strings.isNullOrEmpty(actionCode) || Strings.isNullOrEmpty(actionLabel)) {
                    log.error("complete task error : LeadsObj operation snapshot without real action code ! " +
                            "TENANT_ID={}," + "TASK_ID={}", tenantId, taskId);
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_SETTING_ACTION_CODE);
                }
            }

            private Map<String, Object> snapshot(Map<String, Object> data) {
                Map<String, Object> snapshot = Maps.newHashMap();
                if (MapUtils.isNotEmpty(data)) {
                    snapshot.put(ExtensionKey.actionCode, data.get(ExtensionKey.actionCode));
                    snapshot.put(ExtensionKey.actionLabel, data.get(ExtensionKey.actionLabel));
                } else {
                    snapshot.put(ExtensionKey.actionCode, "--");
                    snapshot.put(ExtensionKey.actionLabel, "--");
                }
                return snapshot;
            }


        },
        approve {
            @Override
            public CompleteTask.Result execute(
                    RefServiceManager serviceManager,
                    Task task,
                    String opinion,
                    Map<String, Object> completedData,
                    Integer addOrReplaceNextTaskAssignee,
                    Map<String, Object> nextTaskAssignee,
                    DataCacheHandler dataCacheHandler) throws Exception {

                RemoteContext context = serviceManager.getContext();

                if (completedData == null) {
                    log.info("complete task error : approve completedData is null, CONTEXT={}. TASK_ID={}", context, task
                            .getId());
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_ABSENCE_APPROVAL_RESULT);
                }

                String taskId = task.getId();
                String objectId = task.getObjectId();
                String entityId = task.getEntityId();
                Object result = completedData.get(BPMConstants.ApproveResult.RESULT);
                String activityId = task.getActivityId();
                if (!StringUtils.isEmpty(result)) {
                    if (!(CompleteTask.ActionType.AGREE.equals(result) || CompleteTask.ActionType.REJECT.equals(result))) {
                        throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_PARAMS_ERROR);
                    }
                    dataCacheHandler.getVariableMap(task.getWorkflowInstanceId(), task.getWorkflowId())
                            .get(InstanceVariableManager.getActivityVariableEntityId(activityId, BPMConstants.ApproveResult.RESULT)).put("value", result);
                } else {
                    log.info("complete task error : approve result is null, CONTEXT={}, TASK_ID={}", context, task
                            .getId());
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_ABSENCE_APPROVAL_RESULT);
                }
                if (Strings.isNullOrEmpty(opinion)) {
                    opinion = "";
                }
                Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                        objectId));

                Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager, task.getWorkflowInstanceId(),
                        task.getWorkflowId(),
                        variableKey,
                        dataCacheHandler);

                return paasWorkflow.completeTask(context, taskId, (String) result, variables, opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, null);
            }
        },
        addRelatedObject {
            @Override
            public CompleteTask.Result execute(
                    RefServiceManager serviceManager,
                    Task task,
                    String opinion,
                    Map<String, Object> completedData,
                    Integer addOrReplaceNextTaskAssignee,
                    Map<String, Object> nextTaskAssignee,
                    DataCacheHandler dataCacheHandler) throws Exception {

                RemoteContext context = serviceManager.getContext();

                String relatedObjectId = (String) completedData.get(ExtensionKey.relatedObjectId);
                if (Strings.isNullOrEmpty(relatedObjectId)) {
                    log.info("completeTask failed : no relatedObjectId! CONTEXT={}, TASK_ID={}", context, task.getId());
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA);
                }

                String taskId = task.getId();
                String objectId = task.getObjectId();
                String activityId = task.getActivityId();
                String entityId = task.getEntityId();
                String relatedEntityId = task.getRelatedEntityId();

                Map<String, Pair<String, String>> variableKeys = InstanceVariableManager.generateVariableKey(activityId, new Pair<>(entityId,
                        objectId), new Pair<>(relatedEntityId, relatedObjectId));

                Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager,
                        task.getWorkflowInstanceId(),
                        task.getWorkflowId(),
                        variableKeys,
                        dataCacheHandler);

                return paasWorkflow.completeTask(context, taskId, CompleteTask.ActionType.AUTO_AGREE, variables,
                        opinion, addOrReplaceNextTaskAssignee, nextTaskAssignee, null);
            }
        },
        addMDObject {
            @Override
            public CompleteTask.Result execute(
                    RefServiceManager serviceManager,
                    Task task,
                    String opinion,
                    Map<String, Object> completedData,
                    Integer addOrReplaceNextTaskAssignee,
                    Map<String, Object> nextTaskAssignee,
                    DataCacheHandler dataCacheHandler) throws Exception {
                return addRelatedObject.execute(serviceManager, task, opinion, completedData, addOrReplaceNextTaskAssignee, nextTaskAssignee, dataCacheHandler);
            }
        },
        externalApplyTask {
            @Override
            public CompleteTask.Result execute(
                    RefServiceManager serviceManager,
                    Task task,
                    String opinion,
                    Map<String, Object> completedData,
                    Integer addOrReplaceNextTaskAssignee,
                    Map<String, Object> nextTaskAssignee,
                    DataCacheHandler dataCacheHandler) throws Exception {

                RemoteContext context = serviceManager.getContext();

                Map<String, Pair<String, String>> variableKey = InstanceVariableManager.generateVariableKey(task.getActivityId(), new Pair<>(task.getEntityId(),
                        task.getObjectId()));

                Map<String, Object> variables = instanceVariableManager.getWorkflowVariableInstances(serviceManager, task.getWorkflowInstanceId(),
                        task.getWorkflowId(),
                        variableKey,
                        dataCacheHandler);
                CompleteTask.Result completeTaskResult = paasWorkflow.completeTask(context,
                        task.getId(),
                        CompleteTask.ActionType.AUTO_AGREE,
                        variables,
                        opinion,
                        addOrReplaceNextTaskAssignee,
                        nextTaskAssignee, null);

                log.info("complete appTask :tenantId:{},taskId:{}", task.getTenantId(), task.getId());
                return completeTaskResult;

            }
        };
        public static BizTaskDataDao bizTaskDataDao;
        public static PaasWorkflowServiceProxy paasWorkflow;
        public static InstanceVariableManager instanceVariableManager;
        public static Logger log = LoggerFactory.getLogger(TaskHandlerType.class.getName());

    }
}
