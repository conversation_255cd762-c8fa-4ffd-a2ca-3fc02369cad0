package com.facishare.bpm.service.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.event.BPMEventBus;
import com.facishare.bpm.event.model.UpdateTaskReadEmployeeEvent;
import com.facishare.bpm.exception.*;
import com.facishare.bpm.handler.task.button.model.FormButtonResult;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.handler.task.form.model.CustomerData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.*;
import com.facishare.bpm.model.*;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey;
import com.facishare.bpm.model.resource.metadata.enums.OldObjectMappingEnum;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.TaskLog;
import com.facishare.bpm.model.task.*;
import com.facishare.bpm.proxy.NewPaasMetadataProxy;
import com.facishare.bpm.proxy.PaasLogProxy;
import com.facishare.bpm.remote.model.config.BPMObjectSupportConfig;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.rule.RuleMessage;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.*;
import com.facishare.bpm.util.memory.page.Comparation;
import com.facishare.bpm.util.memory.page.MemoryPageUtil;
import com.facishare.bpm.utils.*;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.helper.StopWatch;
import com.facishare.bpm.utils.i18n.I18NParseContext;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.bpmn.definition.model.GetOrganization;
import com.facishare.bpmn.definition.util.ConditionUtil;
import com.facishare.bpmn.definition.util.GetValueHandler;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.mongo.bizdb.model.FlowConfigTerminal;
import com.facishare.flow.postgre.entity.BpmTaskCountEntity;
import com.facishare.flow.repository.BPMTaskRepository;
import com.facishare.paas.I18N;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_PARAMETER_ANOMALY;
import static com.facishare.bpm.service.impl.BPMInstanceServiceImpl.getInstanceLockKey;


/**
 * Created by wangz on 17-2-25.
 */
@Slf4j
@Service
@Setter
public class BPMTaskServiceImpl implements BPMTaskService {
    @Autowired
    private DefinitionExtensionDao workflowExtensionDao;
    @Autowired
    private BizTaskDataDao bizTaskDataDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    private BPMDefinitionService bpmDefinitionService;
    @Autowired
    private FormButtonManager formButtonManager;
    @Autowired
    private TaskExecutionManager taskExecutionManager;

    @Autowired
    private MoreOperationManager moreOperationManager;

    @Autowired
    private GetTaskManager getTaskManager;

    @Autowired
    private GetLaneTasksManager getLaneTasksManager;

    @Autowired
    private GetTaskFormManager getTaskFormManager;

    @Autowired
    private BPMTaskRepository bpmTaskRepository;

    @Autowired
    private PaasLogProxy paasLogProxy;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private ButtonCustomManager buttonCustomManager;

    @Autowired
    private NewPaasMetadataProxy newPaasMetadataProxy;

    @Override
    public PageResult<BPMTask> getTasksByPage(RefServiceManager serviceManager, Page page, TaskQuery query) {
        PageResult<Task> list = paasWorkflow.getTasksByCondition(serviceManager.getContext(), null, query.getInstanceId(), page, null,
                query.getAssignee(), null, null, query.getSourceWorkflowId());
        List<BPMTask> bpmTasks = Lists.newArrayList();
        int total = 0;
        if (list != null && list.getDataList() != null) {
            total = list.getTotal();
            list.getDataList().forEach(task -> bpmTasks.add(BeanUtils.transfer(task, BPMTask.class, (src, ret) -> {
                ret.setExtension(src.getBpmExtension());
                ret.setTimeout(src.isTimeout());
            })));
        }

        PageResult<BPMTask> bpmTaskList = new PageResult<>();
        bpmTaskList.setResult(bpmTasks);
        bpmTaskList.setTotal(total);

        return bpmTaskList;
    }


    @Override
    public List<TaskLog> getTaskLogs(RefServiceManager serviceManager, String workflowInstanceId) {
        if (workflowInstanceId == null) {
            return new ArrayList<>();
        }
        WorkflowInstance instance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), workflowInstanceId);
        Page page = new Page();
        page.setPageNumber(1);
        page.setPageSize(100);
        List<Task> tasks = paasWorkflow.getTasksByCondition(serviceManager.getContext(), null, workflowInstanceId, page, null, null, null,
                null, null).getDataList();

        return TaskLog.getTaskLogs(instance, tasks);
    }


    @Override
    public boolean changeTaskHandlers(RefServiceManager serviceManager, String taskId, List<String> candidateIds, String modifyOpinion) {
        log.info("changeTaskHandlers : CONTEXT={}, TASK_ID={}, CANDIDATE_IDS={}", serviceManager.getContext(), taskId, candidateIds);

        Task task = getPaaSTask(serviceManager,taskId);

        if(TaskState.tag_waiting.equals(task.getState())){
            throw new BPMChangeApproverException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TAG_WAITING_NOT_CHANGE_APPROVER);
        }

        candidateIds= TaskUtils.validateAndDistinctCandidateIds(task,candidateIds);

        if (Strings.isNullOrEmpty(task.getLinkApp()) && CollectionUtils.isNotEmpty(candidateIds)) {
            if (candidateIds.stream().anyMatch(candidateId -> candidateId.length() >= 8)) {
                throw new BPMChangeApproverException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_CHANGE_APPROVER_INCLUDE_OUTSIDERS_ERROR);
            }
        }

        if(Objects.equals(task.getCandidateIds(),candidateIds)){
            return true;
        }

        return paasWorkflow.changeCandidates(serviceManager.getContext(), taskId,candidateIds ,modifyOpinion);
    }

    @Override
    public Boolean replaceTaskHandlers(RefServiceManager serviceManager, String taskId, List<String> candidateIds) {
        log.info("replaceTaskHandlers : CONTEXT={}, TASK_ID={}, CANDIDATE_IDS={}", serviceManager.getContext(), taskId, candidateIds);
        //只有系统才可以执行替换处理人操作
        if (!BPMConstants.CRM_SYSTEM_USER.equals(serviceManager.getUserId())) {
            throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ONLY_SUPPORT_SYSTEM_USER_ERROR);
        }
        Task task = getPaaSTask(serviceManager,taskId);
        //不支持互联流程 互联流程 直接抛异常
        if (Objects.nonNull(task.getLinkAppEnable()) && task.getLinkAppEnable() && Strings.isNullOrEmpty(task.getLinkApp())) {
            throw new BPMChangeApproverException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_FOUND_LINK_APP_ID);
        }

        if(TaskState.tag_waiting.equals(task.getState())){
            throw new BPMChangeApproverException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TAG_WAITING_NOT_CHANGE_APPROVER);
        }

        candidateIds=TaskUtils.validateAndDistinctCandidateIds(task,candidateIds);

        if(Objects.equals(task.getCandidateIds(),candidateIds)){
            return true;
        }
        return paasWorkflow.replaceTaskHandlers(serviceManager.getContext(), taskId, candidateIds);
    }

    @Override
    public List<LaneBriefTaskVO> getWorkflowUncompletedTasks(RefServiceManager serviceManager, String sourceWorkflowId, LaneBriefTaskVO
            .QueryType type, String id, LaneBriefTaskVO.QueryState state) {

        StopWatch stopWatch = StopWatch.create("getWorkflowUncompletedTasks");
        List<Task> tasks = paasWorkflow.getAllInprogressTasksBySourceWorkflowId(serviceManager.getContext(), sourceWorkflowId);
        stopWatch.lap("getAllTasks");
        List<String> activityIds = Lists.newArrayList();

        //1.获取要查询的任务Ids/
        //如果 type 是 lane，查询阶段下的所有任务；如果type 是 activity ，查询activityId对应的流程
        if (LaneBriefTaskVO.QueryType.activity.equals(type)) {
            activityIds.add(id);
        } else if (LaneBriefTaskVO.QueryType.lane.equals(type)) {
            List<PoolEntity> pools = bpmDefinitionService.getWorkflowOutlineBySourceId(serviceManager, sourceWorkflowId)
                    .getExtension().getPools();
            activityIds = PoolEntity.getLaneActivityIds(pools, id);
        }

        List<Task> filteredTasks = Lists.newArrayList();
        for (Task task : tasks) {
            if (activityIds.contains(task.getActivityId())) {
                if (state == null) {
                    filteredTasks.add(task);
                } else {
                    TaskState taskState = task.getState();
                    boolean isError = LaneBriefTaskVO.QueryState.error.equals(state) && state.equalWithTaskState(taskState);
                    boolean isTimeOut = LaneBriefTaskVO.QueryState.timeout.equals(state) && !task.isError() && task.isTimeout();
                    boolean isNormal = LaneBriefTaskVO.QueryState.normal.equals(state) && !task.isError() && !task.isTimeout();
                    if (isError || isTimeOut || isNormal) {
                        filteredTasks.add(task);
                    }
                }
            }
        }

        List<LaneBriefTaskVO> rets = filteredTasks.stream().map(LaneBriefTaskVO::fromTask).collect(Collectors
                .toList());

        if (CollectionUtils.isEmpty(rets)) {
            stopWatch.logSlow(100);
            return rets;
        }
        stopWatch.lap("filterTasks");

        Set<String> instanceIds = rets.stream().map(LaneBriefTaskVO::getInstanceId).collect(Collectors.toSet());
        Map<String, WorkflowInstance> instances = paasWorkflow.getWorkflowInstanceMap(serviceManager.getContext(), instanceIds);
        stopWatch.lap("getInstances");

        Map<String, String> entityNames = serviceManager.getSimpleEntityNames();
        stopWatch.lap("getDisplayNames");

        Set<Pair<String, String>> entityIdObjectIds = rets.stream().map(r -> new Pair<>(r.getDetailEntityId(), r
                .getDetailObjectId())).collect(Collectors.toSet());

        Map<String, String> objectIdAndNames = serviceManager.getPaaSObjectNames(entityIdObjectIds);
        stopWatch.lap("getObjectNames");
        rets.stream().peek(r -> {
            r.setDetailEntityName(entityNames.getOrDefault(r.getDetailEntityId(), BPMConstants.REPLACE_WHEN_NOT_FOUND));
            r.setDetailObjectName(objectIdAndNames.get(r.getDetailObjectId()));
            r.setStartTime(instances.getOrDefault(r.getInstanceId(), new WorkflowInstance()).getStart());
        }).collect(Collectors.toList());


        stopWatch.logSlow(100);
        return rets;
    }

    /**
     * 最新的逻辑,支持分页
     *
     * @param serviceManager
     * @param sourceWorkflowId
     * @param type
     * @param id
     * @param state
     * @param page
     * @return
     */
    @Override
    public PageResult<LaneBriefTaskVO> getAllWorkflowTasks(RefServiceManager serviceManager,
                                                           String sourceWorkflowId,
                                                           LaneBriefTaskVO.QueryType type,
                                                           String id,
                                                           LaneBriefTaskVO.QueryState state,
                                                           Page page) {
        PageResult<LaneBriefTaskVO> ret = new PageResult<>();
        StopWatch stopWatch = StopWatch.create("getAllWorkflowTasks");

        List<String> activityIds = Lists.newArrayList();

        List<PoolEntity> pools = bpmDefinitionService.getWorkflowOutlineBySourceId(serviceManager, sourceWorkflowId)
                .getExtension().getPools();
        //1.获取要查询的任务Ids/
        //如果 type 是 lane，查询阶段下的所有任务；如果type 是 activity ，查询activityId对应的流程
        if (LaneBriefTaskVO.QueryType.activity.equals(type)) {
            activityIds.add(id);
        } else if (LaneBriefTaskVO.QueryType.lane.equals(type)) {
            activityIds = PoolEntity.getLaneActivityIds(pools, id);
        }

        List<MetadataTask> filteredTasks = Lists.newArrayList();
        //TODO 查元数据,返回全部
        if (state == null) {
            //从元数据查进行中的,取消的,完成的
            SearchTemplateQuery query = MetadataQuery.getInProgressOrActivityIdTasks(sourceWorkflowId, activityIds, page);
            MetadataTaskResult tasks = MetadataTaskResult.getTaskByMetadata(serviceManager, query);
            filteredTasks.addAll(tasks.getData());
            ret.setTotal(tasks.getTotal());
        } else {
            //超时的时候,调用元数据
            List<MetadataTask> lstData = Lists.newArrayList();
            if (LaneBriefTaskVO.QueryState.timeout.equals(state)) {

                List<BpmTaskCountEntity> timeoutTask = bpmTaskRepository.findTimeoutTask(serviceManager.getContext(),
                        sourceWorkflowId,
                        activityIds,
                        page.getPageSize(),
                        MetadataQuery.getStartOffset(page.getPageNumber(),
                                page.getPageSize()));
                if (CollectionUtils.isNotEmpty(timeoutTask)) {
                    lstData = timeoutTask.stream().map(MetadataTask::of).collect(Collectors.toList());
                }

                filteredTasks.addAll(lstData);
                ret.setTotal(filteredTasks.size());
            } else if (LaneBriefTaskVO.QueryState.error.equals(state)) {
                PageResult<Task> errorTasks = paasWorkflow.getTasksByCondition(
                        serviceManager.getContext(), null, null, page, TaskState.error, null, null, null, sourceWorkflowId);
                List<Task> tasks = errorTasks.getDataList();
                List<String> finalActivityIds = activityIds;
                tasks.forEach(task -> {
                    if (finalActivityIds.contains(task.getActivityId())) {
                        filteredTasks.add(MetadataTask.formTask(task, pools));
                    }
                });
                ret.setTotal(errorTasks.getTotal());
            } else if (LaneBriefTaskVO.QueryState.normal.equals(state)) {
                SearchTemplateQuery query = MetadataQuery.getNormalTasks(sourceWorkflowId, activityIds, page);
                MetadataTaskResult tasks = MetadataTaskResult.getTaskByMetadata(serviceManager, query);
                filteredTasks.addAll(tasks.getData());
                ret.setTotal(tasks.getTotal());
            }
        }

        List<LaneBriefTaskVO> rets;
        //超时  并且开启了灰度 或全网
        if (LaneBriefTaskVO.QueryState.timeout.equals(state)) {
            Map<String, String> entityNames = serviceManager.getSimpleEntityNames();
            stopWatch.lap("getDisplayNames");
            rets = filteredTasks.stream().map(fromTask -> LaneBriefTaskVO.fromTask(fromTask, entityNames)).collect(Collectors
                    .toList());
        } else {
            rets = filteredTasks.stream().map(LaneBriefTaskVO::fromTask).collect(Collectors
                    .toList());

        }
        if (CollectionUtils.isEmpty(rets)) {
            stopWatch.logSlow(100);
            return new PageResult<>();
        }
        stopWatch.lap("filterTasks");

        Set<Pair<String, String>> entityIdObjectIds = rets.stream().map(r -> new Pair<>(r.getDetailEntityId(), r
                .getDetailObjectId())).collect(Collectors.toSet());

        Map<String, String> objectIdAndNames = serviceManager.getPaaSObjectNames(entityIdObjectIds);
        stopWatch.lap("getObjectNames");

        Set<String> instanceIds = rets.stream().map(LaneBriefTaskVO::getInstanceId).collect(Collectors.toSet());
        //List<MetadataInstance> instancesList = MetadatQuery.getInstancesByIds(serviceManager, instanceIds);
        //Map<String, MetadataInstance> instances = instancesList.stream().collect(Collectors.toMap(MetadataInstance::get_id, i -> i));
        Map<String, WorkflowInstance> instances = paasWorkflow.getWorkflowInstanceMap(serviceManager.getContext(), instanceIds);
        stopWatch.lap("getInstances");

        rets.stream().peek(r -> {
            //r.setDetailEntityName(entityNames.getOrDefault(r.getDetailEntityId(), BPMConstants.REPLACE_WHEN_NOT_FOUND));
            r.setDetailObjectName(objectIdAndNames.get(r.getDetailObjectId()));
            //r.setStartTime(instances.getOrDefault(r.getInstanceId(), new MetadataInstance()).getStartTime());
            r.setStartTime(instances.getOrDefault(r.getInstanceId(), new WorkflowInstance()).getStart());
        }).collect(Collectors.toList());

        ret.setResult(rets);
        stopWatch.logSlow(100);
        return ret;
    }


    @Override
    public PageResult<LaneBriefTaskVO> getTasksByLane(RefServiceManager serviceManager, CircleType circleType, String
            sourceWorkflowId, List<Comparation> comparations, Page page) {


        if (Strings.isNullOrEmpty(sourceWorkflowId)) {
            log.error("没有选择业务流程:context:{}", serviceManager.getContext());
            throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_CHOICE_BPM_FLOW);
        }
        if (page == null) {
            page = new Page();
        }
        StopWatch stopWatch = StopWatch.create("getTasksByLane");
        PageResult<Task> totalResult = new PageResult<>();
        List<Task> allTasks = paasWorkflow.getAllInprogressTasksBySourceWorkflowId(serviceManager.getContext(), sourceWorkflowId);
        totalResult.setDataList(allTasks);
        totalResult.setTotal(allTasks.size());
        stopWatch.lap("fetch all handle task from paas workflow");
        Set<String> workflowIds = Sets.newTreeSet();
        Set<String> sourceWorkflowIds = Sets.newTreeSet();
        totalResult.getDataList().forEach(item -> {
            workflowIds.add(item.getWorkflowId());
            sourceWorkflowIds.add(item.getSourceWorkflowId());
        });

        Map<String, FlowExtensionEntity> workflowIdExtensions = workflowExtensionDao.findToMap(serviceManager.getTenantId(), workflowIds);

        stopWatch.lap("获取扩展"); //ignoreI18n
        Map<String, String> entityNames = serviceManager.getSimpleEntityNames();
        Set<Pair<String, String>> entityIdObjectIds = allTasks.stream().map(t -> new Pair<>(t.getEntityId(), t
                .getObjectId())).collect(Collectors.toSet());
        Map<String, String> objectNames = serviceManager.getPaaSObjectNames(entityIdObjectIds);

        stopWatch.lap("获取入口对象类型的名称");//ignoreI18n
        boolean isCRMAdmin = serviceManager.isAdmin();
        List<LaneBriefTaskVO> bpmTasks = allTasks.stream().filter(item -> {
            String userId = serviceManager.getUserId();
            String applicantId = item.getApplicantId();
            if (circleType != null) {
                switch (circleType) {
                    case ALL:
                        return (applicantId.equals(userId) || isCRMAdmin);
                    case CHILD:
                        return serviceManager.getEmployeesByReportingObjectId(userId).contains(applicantId);
                    case SELF:
                        return applicantId.equals(userId);
                }
            } else if (isCRMAdmin) {
                return true;
            }
            return applicantId.equals(userId);
        }).map(task -> {
            LaneBriefTaskVO temp = LaneBriefTaskVO.fromTask(task);
            temp.setDetailObjectName(objectNames.get(task.getObjectId()));
            temp.setDetailEntityName(entityNames.getOrDefault(task.getEntityId(), BPMConstants.REPLACE_WHEN_NOT_FOUND));
            if (workflowIdExtensions.get(task.getWorkflowId()) != null) {
                temp.setLaneId(workflowIdExtensions.get(task.getWorkflowId()).getLaneId(task.getActivityId()));
            }
            return temp;

        }).collect(Collectors.toList());
        stopWatch.lap("转成laneTask");//ignoreI18n
        PageResult<LaneBriefTaskVO> result = MemoryPageUtil.getPageResult(
                bpmTasks, LaneBriefTaskVO.getFilter(comparations), null, page.getPageSize(), page.getPageNumber()
        );
        stopWatch.lap("MemoryPageUtil");

        stopWatch.logSlow(100);
        return result;

    }

    @Override
    public BPMTask getTask(RefServiceManager serviceManager, String taskId) {

        //1.获取任务
        Task task = getPaaSTask(serviceManager,taskId);
        BPMTask bpmTask = getBpmTask(serviceManager, task,TaskParams.create());
        bpmTask.setAreaOptions(serviceManager);
        bpmTask.getExtension().remove(ActivityKey.ExtensionKey.objectData);
        return bpmTask;
    }

    @Override
    public MTask getMTask(RefServiceManager serviceManager, String instanceId, String activityInstanceId,String source,boolean applyButtons) {

        Task task = paasWorkflow.getTaskByInstanceId(serviceManager.getContext(), instanceId, activityInstanceId, true);
        TaskParams taskParams = TaskParams.create().updateAndCompleteAssignNextNodeProcessor(true).notNeedSignIn(true).isIgnoreSignAndPayGroupLayout(true).isMobile(true).source(source).applyButtons(applyButtons);
        BPMTask bpmTask = getBpmTask(serviceManager, task, taskParams);
        List<MoreOperationManager.MoreOperation> moreOperations = moreOperationManager.getTaskMoreOperations(serviceManager, bpmTask);
        CustomerData customerData = null;
        //isSupportForm 在此处添加原因是  新接口中  明确了更新和审批会调用此方法,而老接口中需要判断一下
        if(bpmTask.getExecutionType().isSupportForm()){
            //2019年10月18日11:00:50 cuiyx H5 签到组件不需要下发,而新版本的UE是需要下发签到组件,然后展示到页面中的,
            //且两块逻辑调用同一块代码,添加这个逻辑,终端调用过来的则不包含签到组件
            customerData = getTaskFormManager.getData(serviceManager,bpmTask.getEntityId() , bpmTask.getObjectId(), bpmTask.getExtension(),taskParams.buttons(bpmTask.getButtons()));
        }
        Map<String, Employee> employeeInfo = serviceManager.getEmployeeInfo(bpmTask.getPersons());
        return MTask.getInstance(bpmTask, moreOperations, customerData, employeeInfo);
    }

    @Override
    public List<BPMTask> getTasksByInstanceIds(RefServiceManager serviceManager, String workflowInstanceId,
                                               List<String> activityInstanceIds) {

        if (activityInstanceIds == null || activityInstanceIds.size() == 0) {
            return new ArrayList<>();
        }
        PageResult<Task> tasksResult = paasWorkflow.getTasksByInstanceIds(serviceManager.getContext(), workflowInstanceId,
                activityInstanceIds);

        List<Task> tasks = tasksResult.getDataList();

        //如果tasks是空 需要查询下自动节点
        if (CollectionUtils.isEmpty(tasks)) {
            PageResult<Task> automaticAndQuartzTasks = paasWorkflow.getAutomaticAndQuartzTasks(serviceManager.getContext(), workflowInstanceId, NodeType.executionTask, null, null, null, null);
            tasks = filterTaskListByActivityInstanceIds(automaticAndQuartzTasks.getDataList(), activityInstanceIds);
            for (Task task : tasks) {
                if(TaskState.pass.equals(task.getState())){
                    task.setCompleted(Boolean.TRUE);
                }
            }
        }

        ExecutableWorkflowExt executableWorkflow;
        if (CollectionUtils.isNotEmpty(tasks)) {
            executableWorkflow = paasWorkflow.getWorkflow(serviceManager.getContext(), tasks.get(0).getWorkflowId());
            return tasks.stream().map(task -> {
                BPMTask bpmTask = getBpmTask(serviceManager, task,TaskParams.create());
                bpmTask.setAreaOptions(serviceManager);
                bpmTask.getExtension().remove(ActivityKey.ExtensionKey.objectData);
                List<SimpleTransition> transitions = bpmDefinitionService.getActivityTransitionsDefByActivityId(serviceManager, executableWorkflow, task.getActivityId());
                bpmTask.setTransitions(transitions);
                bpmTask.extensionFormAddApiName();
                return bpmTask;
            }).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }


    private void assertException(Exception e) {
        if (e.getCause() instanceof SocketTimeoutException) {
            throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION,
                    BPMI18N.PAAS_FLOW_BPM_EXECUTE_TIMEOUT_TIP.text());
        }
        if (e instanceof SocketTimeoutException) {
            throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION,
                    BPMI18N.PAAS_FLOW_BPM_EXECUTE_TIMEOUT_TIP.text());
        } else if (e instanceof BPMBusinessException) {
            throw new BPMBusinessException(((BPMBusinessException) e).getErrorCode(), e.getMessage());
        } else {
            log.error("",e );
            throw new RestProxyBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVER_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 单独写个方法是因为不想转成BPMTask 涉及到很多获取自定义对象的数据
     * @param taskId
     * @return
     */
    @Override
    public Map<String,Object>  getBPMExtensionByTaskId(RefServiceManager serviceManager,String taskId){
        return getPaaSTask(serviceManager,taskId).getBpmExtension();
    }

    @Override
    public Task getPaaSTask(RefServiceManager serviceManager, String taskId){
        return paasWorkflow.getTask(serviceManager.getContext(), taskId);
    }

    @Override
    public Task getPaaSTaskNotThrowException(RefServiceManager serviceManager, String taskId){
        return paasWorkflow.getTask(serviceManager.getContext(), taskId, false);
    }

    @Override
    public Task getPaaSTask(RefServiceManager serviceManager, String taskId, boolean useCache){
        return serviceManager.getObjectFromCache(taskId,id->getPaaSTask(serviceManager,id));
    }

    /**
     * @param serviceManager
     * @param taskId
     * @param opinion
     * @param data
     * @param addOrReplaceNextTaskAssignee
     * @param nextTaskAssignee
     * @return
     */
    @Override
    public CompleteTaskResult completeTask(RefServiceManager serviceManager, String taskId, String opinion, Map<String, Object> data, Integer addOrReplaceNextTaskAssignee,
                                    Map<String, Object> nextTaskAssignee, boolean needValidateNextTaskAssignee,Boolean needGetCurrentNextTask, Boolean ignoreNoBlockValidate) {

        StopWatch stopWatch = StopWatch.create("completeTask");
        RuleMessage ruleMessage = null;
        Integer sleepTime = null;
        NextTask nextTask = null;

        String parallelRedisKey = "";
        Task task = getPaaSTask(serviceManager,taskId,true);
        if (!BPMConstants.APP_ID.equals(task.getAppId())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_TASK_ERROR);
        }
        if (TaskState.suspend.equals(task.getState())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_SUSPEND_NOT_COMPLETE);
        }
        if (TaskState.tag_waiting.equals(task.getState())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_TAG_WAITING_NOT_COMPLETE);
        }
        if(StringUtils.isNotBlank(opinion) && opinion.length() > 500){
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_TASK_OPINION_TOO_LONG);
        }
        parallelRedisKey = getInstanceLockKey(serviceManager.getTenantId(), task.getWorkflowInstanceId());
        if (!redisManager.setValueWithExpireTime(parallelRedisKey, StringUtils.EMPTY, 10)) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.ENGINE_TASK_COMPLETE_PARALLEL_ERROR);//engin_task_complete_parallel_time_short
        }
        try {
            stopWatch.lap("getTask");

            CompleteTask.Result engineResult = taskExecutionManager.getHandler(task.getExecutionTypeByTaskType())
                    .execute(serviceManager, task, opinion, data, addOrReplaceNextTaskAssignee,
                            nextTaskAssignee, getDataHandler(serviceManager), needValidateNextTaskAssignee, ignoreNoBlockValidate);

            //根据任务类型，获取TaskHandler，并执行任务
            CompleteTask.CompleteResult triggerResult = engineResult.getResult();
            if (Objects.isNull(triggerResult)) {
                if (needGetCurrentNextTask) {
                    String userIdWithOuterUserId = serviceManager.getUserIdWithOuterUserId();
                    List<Task> currentUserInProgressTasks = paasWorkflow.getCurrentUserInProgressTasks(
                            serviceManager.getContext(),
                            task.getApplicantId(),
                            task.getWorkflowInstanceId(),
                            userIdWithOuterUserId,
                            null,
                            null,
                            task.getSourceWorkflowId());
                    if (CollectionUtils.isNotEmpty(currentUserInProgressTasks)) {
                        Task workflowNextTask = currentUserInProgressTasks.get(0);
                        nextTask = NextTask.create(workflowNextTask.getId(), workflowNextTask.getActivityInstanceId(), workflowNextTask.getWorkflowInstanceId());
                    }
                }

                return CompleteTaskResult.create(null, null,nextTask);
            }
            sleepTime = triggerResult.getSleepTime();

            //不满足条件异常信息解析
            ruleMessage = ConditionUtil.workflow_bpm.analysis(serviceManager.getTenantId(),
                    new GetValueHandler() {
                        @Override
                        public Map<String, Object> getDesc(String tenantId, String entityId) {
                            return serviceManager.findDescribe(entityId, true, false);
                        }

                        @Override
                        public GetOrganization.OrganizationResult getOrganization(String tenantId, List<String> persons, List<String> deptIds) {
                            return new GetOrganization.OrganizationResult(serviceManager.getDeptByDeptIds(deptIds), serviceManager.getMembersByIds(persons));
                        }

                        @Override
                        public Map<String, String> getAreaLabelsAndCodes(String tenantId, List<String> codes) {
                            return serviceManager.getAreaLabelByCodes(tenantId, codes);
                        }

                        @Override
                        public List<Map<String, Object>> getCustomVariableTable() {
                            ExecutableWorkflowExt workflow = paasWorkflow.getWorkflow(serviceManager.getContext(), task.getWorkflowId());
                            return workflow.getCustomVariableTable();
                        }

                        @Override
                        public Map<String, String> getDimensionObjNameByIds(List<String> dimensionIdList) {
                            RemoteContext context = serviceManager.getContext();
                            RemoteContext systemContext = new RemoteContext(context.getEa(), context.getTenantId(), context.getAppId(), BPMConstants.CRM_SYSTEM_USER);
                            return serviceManager.getDimensionObjDataList(systemContext, SearchTemplateUtils.getDimensionObjObjectList(dimensionIdList));
                        }
                    },
                    triggerResult.getRule());


        } catch (Exception e) {
            assertException(e);
        } finally {
            redisManager.delete(parallelRedisKey);
        }
        stopWatch.logSlow(100);
       return CompleteTaskResult.create(ruleMessage, sleepTime,nextTask);
    }

    @Override
    public PageResult<TaskOutline> getHandleTaskList(RefServiceManager serviceManager, Boolean isCompleted, String taskName, Page
            page) {


        StopWatch stopWatch = StopWatch.create("getHandleTaskList");
        PageResult<Task> pageResult = paasWorkflow.getTasksByAssigneeId(serviceManager.getContext(), isCompleted, taskName, page);
        stopWatch.lap("getTasksFromEngine");

        List<Task> tasks = pageResult.getDataList();
        Set<String> instanceIds = Sets.newHashSet();
        Set<String> workflowIds = Sets.newHashSet();
        tasks.forEach(item -> {
            instanceIds.add(item.getWorkflowInstanceId());
            workflowIds.add(item.getWorkflowId());
        });

        //批量查询做缓存
        Map<String, WorkflowInstance> workflowInstanceCache = paasWorkflow.getWorkflowInstanceMap(serviceManager.getContext(), instanceIds);
        stopWatch.lap("getWorkflowInstanceCache");
        Map<String, FlowExtensionEntity> workflowExtensionCache = workflowExtensionDao.findToMap(serviceManager.getTenantId(), workflowIds);
        stopWatch.lap("getWorkflowExtensionCache");
        List<TaskOutline> taskOutlines = TaskUtils.getTaskOutlineFromTasks(tasks, workflowInstanceCache,
                workflowExtensionCache, (task, taskOutline, instance, extension) -> {

                    if (instance == null) {
                        log.error("TaskOutLineUtil.setTaskOutlineFromTasks:instance:{} not found", task.getWorkflowInstanceId());
                    } else {
                        task.setWorkflowInstanceId(instance.getId());
                        taskOutline.setState(task.getState());
                        if (instance.getStart() != null) {
                            taskOutline.setStartTime(instance.getStart());
                        }
                        if (instance.getEnd() != null) {
                            taskOutline.setEndTime(instance.getEnd());
                        }
                    }

                    //查询阶段信息
                    String activityId = task.getActivityId();
                    if (extension != null) {
                        LaneEntity laneEntity = extension.getLane(activityId);
                        if (laneEntity != null) {
                            taskOutline.setLaneId(laneEntity.getId());
                            taskOutline.setLaneName(laneEntity.getName());
                        } else {
                            log.error("getTaskOutlineFromTasks error : no lane contains the activity={}, TASK_ID={}",
                                    activityId, task.getId());
                        }
                    }

                }, setTaskObjectNamesHandler(serviceManager), stopWatch);

        if (!CollectionUtils.isEmpty(taskOutlines)) {
            Map<String, String> entityNames = serviceManager.getSimpleEntityNames();
            stopWatch.lap("getDisplayNames");
            taskOutlines.forEach(t -> t.setEntityName(entityNames.getOrDefault(t.getEntityId(), BPMConstants.REPLACE_WHEN_NOT_FOUND)));
        }
        stopWatch.logSlow(200);
        PageResult<TaskOutline> ret = new PageResult<>();
        ret.setTotal(pageResult.getTotal());
        ret.setResult(taskOutlines);

        return ret;
    }

    //TODO 优化下 2018年09月14日19:52:41
    @Override
    public List<TaskOutline> getUncompletedTasksByObject(RefServiceManager serviceManager, String objectId) {
        StopWatch stopWatch = StopWatch.create("getUncompletedTaskOutlines");
        // TODO: 2017/6/16 stopWatch统计的信息可能不准确
        //1.查询所有待办任务
        Pair<List<Task>, List<WorkflowInstance>> queryDatas = paasWorkflow.getUncompletedTasks(serviceManager.getContext(), objectId);
        List<Task> tasks = queryDatas.getKey();
        stopWatch.lap("get all uncompletedTasks");

        Set<String> workflowIds = Sets.newHashSet();
        tasks.forEach(t -> workflowIds.add(t.getWorkflowId()));
        Map<String, FlowExtensionEntity> workflowExtensionCache = workflowExtensionDao.findToMap(serviceManager.getTenantId(), workflowIds);
        stopWatch.lap("getWorkflowExtensionCache");

        Map<String, WorkflowInstance> workflowInstanceCache = queryDatas.getValue().stream().collect(Collectors.toMap
                (WorkflowInstance::getId, instance -> instance));
        stopWatch.lap("getWorkflowInstanceMap");
        //3.将tasks组合成taskoutline
        List<TaskOutline> taskOutlines = TaskUtils.getTaskOutlineFromTasks(tasks, workflowInstanceCache, workflowExtensionCache,
                //待办任务列表的处理逻辑
                (task, taskOutline, instance, extension) -> {
                    taskOutline.setEntryType(instance.getEntityId());//用来填充更多的权限
                    if (extension != null) {
                        LaneEntity laneEntity = extension.getLane(task.getActivityId());
                        taskOutline.setLaneName(laneEntity.getName());
                        taskOutline.setLaneId(laneEntity.getId());
                    }
                    List<String> candidateIds = task.getCandidateIds();
                    if (candidateIds != null) {
                        taskOutline.setIsTaskOwner(candidateIds.contains(serviceManager.getUserIdWithOuterUserId()));
                    } else {
                        log.warn("getTaskOutlineFromTasks : the task has no candidates. TASK_ID={}", task.getId());
                    }
                    //HandleThree  线索1转三  需要终端强升,在getUnCompleteTaskByObject下发此标识
                    String actionCode = task.getActionCode();
                    taskOutline.setActionCode(actionCode);
                    String activityId = task.getActivityId();
                    if (extension != null) {
                        List<PoolEntity> poolEntities = extension.getPools();
                        List<Pool> pools = Lists.newArrayList();
                        if (poolEntities != null) {
                            poolEntities.forEach(poolEntity -> pools.add(Pool.toPool(poolEntity, activityId)));
                        }
                        taskOutline.setPools(pools);
                    }
                    taskOutline.setBelongToCurrentObj(objectId.equals(task.getObjectId()));
                }, null, stopWatch);

        //4.优先处理人和最近生任务排序
        Collections.sort(taskOutlines);
        stopWatch.lap("sort task list");
        stopWatch.log();
        return taskOutlines.stream().filter(TaskOutline::starting).peek((taskOutline) -> {
            if (!taskOutline.getExecutionTypeEnum().isExternalApplyTask()) {
                if (CollectionUtils.isEmpty(taskOutline.getCandidateIds())) {
                    taskOutline.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_HAS_NO_HANDLER.text());
                }
            }
        }).collect(Collectors.toList());

    }

    /**
     * 终端详情页获取任务列表
     * @param serviceManager
     * @param entityId
     * @param objectId
     * @return
     */
    @Override
    public List<LaneTask> getUncompletedTaskInfosByObject(RefServiceManager serviceManager, String entityId, String objectId,boolean applyButtons, Boolean notGetDatas) {
        StopWatch stopWatch = StopWatch.create("getUncompletedTaskInfosByObject");
        //1.查询所有待办任务
        Pair<List<Task>, List<WorkflowInstance>> queryDatas = paasWorkflow.getUncompletedTasks(serviceManager.getContext(), objectId);
        List<Task> tasks = queryDatas.getKey();
        stopWatch.lap("get all uncompletedTasks");

        Set<String> workflowIds = Sets.newHashSet();
        tasks.forEach(t -> workflowIds.add(t.getWorkflowId()));
        Map<String, FlowExtensionEntity> workflowExtensionCache = workflowExtensionDao.findToMap(serviceManager.getTenantId(), workflowIds);
        stopWatch.lap("get workflowExtensionCache");
        List<LaneTask> laneTasks = getLaneTasks(serviceManager, tasks,applyButtons, notGetDatas).stream()
                .peek(laneTask-> {
                    laneTask.fillLaneDetail(workflowExtensionCache.get(laneTask.getWorkflowId()));
                    if(Boolean.TRUE.equals(laneTask.getLinkAppEnable()) && StringUtils.isNotEmpty(laneTask.getLinkApp())) {
                        laneTask.setLinkAppName(serviceManager.getI18nLinkAppName(laneTask.getLinkApp(), laneTask.getLinkAppName()));
                    }
                })
                .collect(Collectors.toList());

        stopWatch.lap("getUncompletedTaskInfosByObject.getHandler");
        stopWatch.logSlow(10);

        return laneTasks;
    }

    @Override
    public AfterRetry.RetryResult afterActionRetry(RefServiceManager serviceManager, String taskId, int rownum, int executeType) {
        //定时节点
        if (taskId.contains(BPMConstants.LATENCY_SUFFIX)) {
            taskId = taskId.replace(BPMConstants.LATENCY_SUFFIX, "");
            return paasWorkflow.autoTaskAfterRetry(serviceManager.getContext(), taskId, rownum, executeType);
        }
        //自动节点
        if (taskId.contains(BPMConstants.EXECUTION_SUFFIX)) {
            taskId = taskId.replace(BPMConstants.EXECUTION_SUFFIX, "");
            return paasWorkflow.autoTaskAfterRetry(serviceManager.getContext(), taskId, rownum, executeType);
        }

        return paasWorkflow.afterActionRetry(serviceManager.getContext(), taskId, rownum, executeType);
    }

    @Override
    public List<Task> getTasksByInstanceId(RefServiceManager serviceManager, String instanceId) {
        return paasWorkflow.getTasksByInstanceId(serviceManager.getContext(), instanceId);
    }

    @Override
    public List<Task> getAutomaticAndQuartzTaskByInstanceId(RefServiceManager serviceManager, String instanceId) {
        PageResult<Task> taskPageResult = paasWorkflow.getAutomaticAndQuartzTasks(serviceManager.getContext(), instanceId, null, null, null, null, null);
        // 添加id的属性,用与区分调用引擎的哪个接口,国宏那边不答应处理
        return taskPageResult.getDataList().stream().map(k -> {
            if (NodeType.latencyTask.name().equals(k.getTaskType())) {
                k.setId(BPMConstants.LATENCY_SUFFIX + k.getId());
                return k;
            }

            if (NodeType.executionTask.name().equals(k.getTaskType())) {
                k.setId(BPMConstants.EXECUTION_SUFFIX + k.getId());
                return k;
            }

            return k;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Task> getDelayAutoTaskByConditions(RefServiceManager serviceManager, String instanceId, List<String> activityInstanceIds, String id, String objectId) {
        return getAutoTaskByConditions(serviceManager, instanceId, activityInstanceIds, id, objectId, Boolean.TRUE);
    }

    @Override
    public List<Task> getAutoTaskByConditions(RefServiceManager serviceManager, String instanceId, List<String> activityInstanceIds, String id, String objectId, Boolean isDelayTask) {
        if(StringUtils.isNotBlank(id) && !ObjectId.isValid(id)){
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PARAMETER_ANOMALY);
        }
        PageResult<Task> taskPageResult = paasWorkflow.getAutomaticAndQuartzTasks(serviceManager.getContext(), instanceId, null, null, isDelayTask, id, objectId);
        if (CollectionUtils.isEmpty(taskPageResult.getDataList())) {
            return Lists.newArrayList();
        }
        List <Task> delayTasks = taskPageResult.getDataList();
        if(CollectionUtils.isEmpty(activityInstanceIds)){
            return delayTasks;
        }
        delayTasks.removeIf(task -> !activityInstanceIds.contains(String.valueOf(task.getActivityInstanceId())));
        return delayTasks;
    }

    @Override
    public TaskDetail getBpmTaskDetail(RefServiceManager serviceManager, String taskId, String instanceId, String activityInstanceId, String activityId,TaskParams taskParams) {
        //1. get task and instance
        Pair<Task, WorkflowInstance> getTaskByTaskIdOrInstanceParam = getTaskByTaskIdOrInstanceParam(serviceManager, taskId, instanceId, activityInstanceId, activityId);
        Task task = getTaskByTaskIdOrInstanceParam.getKey();
        if(task!=null){
            if (!BPMConstants.APP_ID.equals(task.getAppId())) {
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_TASK_ERROR);
            }
        }
        WorkflowInstance workflowInstance = getTaskByTaskIdOrInstanceParam.getValue();
        // 2. 获取对象数据,预初始化
        // 3. 将引擎的数据转化成BPMTask
        // 4. 获取到任务数据,填充任务信息
        // 5. 获取到对象的apiName
        // 6. 填充bpmExtension中entityName（ relatedObjectName）
        // 7. 通过不同的操作,下发不同的数据信息

        //2021年04月25日10:11:06  函数中查询审批流任务调用的是业务流接口  审批流任务中没有executionType  会导致业务流空指针,如果后期存在此种情况  则返回空
        ExecutionTypeEnum executionType = task.getExecutionType();
        if (executionType == null) {
            log.warn("executionType is null ,tenantId:{},taskId:{}", serviceManager.getTenantId(), task.getId());
            return null;
        }
        fillFlowLayoutExists(serviceManager, Lists.newArrayList(task));
        TaskDetail taskDetail = getTaskManager.getHandler(task.getExecutionType()).execute(serviceManager, task,taskParams);

        //此行代码从UserGetTaskDetailHandler 42行提出,获取实例信息,实例cancel的话 任务不下发重试和忽略按钮
        if(Objects.isNull(workflowInstance)){
            workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(),task.getWorkflowInstanceId() );
        }
        //如果实例状态为cancel 但是任务是in_progress 则重新终止下流程
        BPMEventBus.post(serviceManager,task,workflowInstance);

        SimpleAfterActionExecutionUtil.setAfterActionOperations(serviceManager, workflowInstance, taskDetail.getEntityId(), taskDetail.getExecution());

        // 8. 通过任务上的workflowId获取扩展信息,并获取到对应的laneName
        FlowExtensionEntity flowExtensionEntity = workflowExtensionDao.findOneFlowExtension(serviceManager.getTenantId(), task.getWorkflowId());
        //  阶段名称
        taskDetail.setLaneName(flowExtensionEntity.getLaneName(task.getActivityId()));
        taskDetail.setLaneId(flowExtensionEntity.getLaneId(task.getActivityId()));
        //770 需要下发所有阶段 并标识当前任务所在阶段;790 业务流web端任务落地页优化,顶部需要阶段信息,直接下发
        taskDetail.setPools(flowExtensionEntity.getSortPools());
        //5. 下发不同的action
        taskDetail.setMoreOperations(moreOperationManager.getTaskMoreOperations(serviceManager, taskDetail, task));

        taskDetail.setEmployeeInfo(serviceManager.getEmployeeInfo(taskDetail.getPersons()));
        taskDetail.sortPersons(!serviceManager.isOuterUserId());
        //异步更新已读人员
        if(TaskState.in_progress.equals(taskDetail.getState()) && taskDetail.getIsOwner()){
            BPMEventBus.post(UpdateTaskReadEmployeeEvent.create(serviceManager, taskDetail.getId(), taskDetail.getCandidateIds()));
        }
        //获取任务详情页 要展示数据的哪些信息的 配置， 从对象的流程列表页配置 进行配置
        if(taskParams.getIncludeTaskFeedDetailConfig()){
            taskDetail.setTaskFeedDetailConfig(newPaasMetadataProxy.queryObjectFeedConfig(serviceManager.getContext(),FlowType.workflow_bpm.name(),taskDetail.getEntityId()));
        }
        if(StringUtils.isBlank(taskDetail.getSourceWorkflowId()) && Objects.nonNull(workflowInstance)){
            taskDetail.setSourceWorkflowId(workflowInstance.getSourceWorkflowId());
        }
        if(Objects.nonNull(workflowInstance)){
            taskDetail.setWorkflowName(workflowInstance.getWorkflowName());
            taskDetail.setInstanceStartTime(workflowInstance.getStart());
        }
        return taskDetail;
    }



    private Pair<Task, WorkflowInstance> getTaskByTaskIdOrInstanceParam(RefServiceManager serviceManager, String taskId,
                                                                        String instanceId, String activityInstanceId, String activityId) {
        Task task = null;
        WorkflowInstance workflowInstance = null;
        // 1. 从引擎获取任务详情
        if (!Strings.isNullOrEmpty(taskId) && ObjectId.isValid(taskId)) {
            task = getPaaSTaskNotThrowException(serviceManager,taskId);
        } else if (!Strings.isNullOrEmpty(instanceId)) {
            if (!Strings.isNullOrEmpty(activityInstanceId)) {
                //终端阶段跳转  只有instanceId 和 activityInstanceId
                task = paasWorkflow.getTaskByInstanceId(serviceManager.getContext(), instanceId, activityInstanceId, false);
            } else if (!Strings.isNullOrEmpty(activityId)) {
                //获取实例
                workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), instanceId);
                List<ActivityInstance> activityInstances = workflowInstance.getActivityInstances();
                //通过activityId获取进行中的activityInstanceIds
                List<String> activityInstanceIds = activityInstances.stream()
                        .filter(k -> activityId.equals(k.getActivityId()) && k.isOpen())
                        .map(k -> String.valueOf(k.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(activityInstanceIds)) {
                    task = paasWorkflow.getTaskByInstanceId(serviceManager.getContext(), instanceId, activityInstanceIds.get(0), false);
                }
            }
        }

        if (Objects.isNull(task)) {
            List<Task> autoTasks = getAutoTaskByConditions(serviceManager, instanceId, StringUtils.isNotBlank(activityInstanceId) ? Lists.newArrayList(activityInstanceId) : null, taskId, null, null);
            if(CollectionUtils.isEmpty(autoTasks) || !autoTasks.stream().anyMatch(t ->
                    t.getId().equals(taskId) || (StringUtils.isNotBlank(activityInstanceId) && t.getActivityInstanceId() == Integer.valueOf(activityInstanceId)))){
                throw new BPMTaskNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_FOUND);
            }
            task = autoTasks.stream().filter(t -> t.getId().equals(taskId) || (StringUtils.isNotBlank(activityInstanceId) &&t.getActivityInstanceId() == Integer.valueOf(activityInstanceId))).findFirst().get();
        }
        return new Pair<>(task, workflowInstance);
    }

    private BPMTask getBpmTask(RefServiceManager serviceManager, Task task,TaskParams taskParams) {

        StopWatch stopWatch = StopWatch.create("getTask");
        if(ExecutionTypeEnum.updateLookup.equals(task.getExecutionType())){
            String lookupEntityId = task.getEntityIdOrLookupFieldEntityId();
            //查询下数据
            Object lookupObjectId = serviceManager.findDataById(task.getEntityId(), task.getObjectId(), task.getLookupFieldApiName(), Boolean.FALSE);
            if(Objects.isNull(lookupObjectId)){
                throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_NULL);
            }
            task.setEntityId(lookupEntityId);
            task.setObjectId((String) lookupObjectId);
        }

        BPMTask bpmTask = BPMTask.fromTask(serviceManager.getContext(), task);

        bpmTask.assignNextTask(task.needAssignNextTask(), task.getNextTaskAssigneeScope(), serviceManager);

        //1.查询任务节点的对象记录（业务对象记录和关联对象记录）
        DataCacheHandler dataCache = getDataHandler(serviceManager);
        Map<Pair<String, String>, Map<String, Object>> taskObjectData = getTaskData(serviceManager, task, dataCache);
        stopWatch.lap("get taskObjectData");
        //Map<String, String> displayNames = serviceManager.getSimpleEntityNames();
        //stopWatch.lap("get displayNames");
        //2.填充bpmExtension中entityName（ relatedObjectName）
        bpmTask.setTaskObjectProperty(taskObjectData,serviceManager);
        /**
         *  是否引用布局规则
         */
        bpmTask.setEnableLayoutRules(MapUtils.getBoolean(task.getBpmExtension(), BPMConstants.ENABLELAYOUTRULES));
        ExecutionTypeEnum executionType = bpmTask.getExecutionType();
        //3.如果是更新节点，填充form表单 || 如果是审批节点，填写审批意见
        if (bpmTask.getExecutionType().isSupportForm()) {
            Map<String, Object> desc = dataCache.getDescribe(task.getEntityId(), true);
            if (Objects.isNull(desc)) {
                throw new BPMTaskReferDescNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_RELATION_DESCRIBE_DELETE);
            }
            Map<String, Object> data = dataCache.getData(new Pair<>(task.getEntityId(), task.getObjectId()));
            bpmTask.setData(data);
            DescribeUtil.replaceDescFieldLabelByFieldMapping(serviceManager, desc, (String) data.get(BPMConstants.RECORD_TYPE));
            bpmTask.setTaskForm(
                    bpmTask,
                    desc,
                    serviceManager.getFieldPermissions(task.getEntityId()),
                    dataCache
            );
            stopWatch.lap("set taskForm");

            List<List<Map<String, Object>>> forms = task.getForm();
            if (CollectionUtils.isNotEmpty(forms)) {
                Map<String, Object> layout = getTaskFormManager.getLayout(serviceManager, task.getEntityId(), task.getObjectId(), task.getBpmExtension(),TaskParams.create());
                bpmTask.setLayout(layout);
            }

        } else if (executionType.isApprove()) {

            bpmTask.setApproveTaskOpinion();

        } else if (executionType.isOperationType()) {

            String actionCode = "";
            String actionLabel = "";
            if (bpmTask.isCombinedActionCode() && Boolean.TRUE.equals(bpmTask.getCompleted())) {
                Map<String, Object> snapshot = (Map) bizTaskDataDao.find(serviceManager.getTenantId(), bpmTask.getId())
                        .getData();
                if (snapshot != null) {
                    actionCode = (String) snapshot.get(ActivityKey.ExtensionKey.actionCode);
                    actionLabel = (String) snapshot.get(ActivityKey.ExtensionKey.actionLabel);
                } else {
                    log.error("taskId:{}", task.getId() + " isCombinedActionCode,但是完成任务时由于h5问题，遗失了actionCode和actionLabel,以此作为记录"); //ignoreI18n
                }
            } else {
                actionCode = task.getActionCode();
                actionLabel = serviceManager.getActionNameByActionCode(task.getEntityId(), actionCode);
            }
            bpmTask.setAction(actionCode, actionLabel);
            //终端解决退回公海 退回线索池 ,在不知道原始公海和原始线索池的情况下,调用终端组件,会认为是没有公海 ,导致可以选择公海
            if (BPMConstants.MetadataKey.accountObjApiName.equals(task.getEntityId())) {
                Map<String, Object> data = dataCache.getData(new Pair<>(task.getEntityId(), task.getObjectId()));
                bpmTask.setData(data.keySet().stream().filter(k -> k.equals(BPMConstants.HIGH_SEAS_ID) && Objects.nonNull(data.get(k))).collect(Collectors.toMap((k -> k), (data::get))));
            }

            //如果是销售线索,则需要下发部分字段的描述信息,例如:无效字段的描述,跟进字段的描述 6.6 添加 终端需要使用@李汝健
            if (BPMConstants.MetadataKey.APINAME_LEADSOBJ.equals(task.getEntityId())) {
                //设置数据值
                Map<String, Object> data = Maps.newHashMap();
                Object closeReasonValue = serviceManager.findDataById(task.getEntityId(), task.getObjectId(), BPMConstants.CLOSE_REASON, true);
                data.put(BPMConstants.CLOSE_REASON, closeReasonValue);
                bpmTask.setData(data);
                //设置字段描述
                Map<String, Object> describe = serviceManager.getDescribe(task.getEntityId());
                Map<String, Object> fields = (Map<String, Object>) describe.get(BPMConstants.MetadataKey.fields);
                Map<String, Object> filterFields = fields.keySet().stream().filter(key -> key.equals(BPMConstants.CLOSE_REASON)).collect(Collectors.toMap(k -> k, fields::get));
                describe.put(BPMConstants.MetadataKey.fields, filterFields);
                bpmTask.setDescribe(describe);
            }


            stopWatch.lap("get actionNames");
        } else if (executionType.isMDObjectOrRelated()||executionType.isBatchAddRelatedObject()) {
            // 终端从 商机 选择新建订单的时候，需要 关联客户的name，所以，在这把节点数据给终端，而在biz层web接口删除
            bpmTask.setObjectData(taskObjectData.get(new Pair<>(bpmTask.getEntityId(), bpmTask
                    .getObjectId())));
            Pair<String, String> relatedDetail = task.getRelatedEntityIdAndListName();
            Map<String, Object> relatedDesc = serviceManager.findDescribe(relatedDetail.getKey(), true, false);
            bpmTask.setRelatedEntityName(relatedDesc);
            bpmTask.setReleatedFieldApiName(relatedDesc, relatedDetail.getValue());
            if(executionType.isBatchAddRelatedObject()){
                bpmTask.setData((Map)taskDataManager.getHandler(task.getExecutionType()).getData(serviceManager, task.getId()));
            }
        }else if (executionType.isBatchEditMasterDetailObject()) {
            bpmTask.setShowDataChangeLog(paasLogProxy.hasEditLog(serviceManager.getContext(),task.getId(),task.getEntityId(),task.getObjectId(), task.getCreateTime()));
        }
        //不是应用节点,并且没有处理人 ,添加无处理人的errorMsg, 线上目前提示是 正在处理,感觉不太友好
        if (!bpmTask.getExecutionType().isExternalApplyTask()) {
            if (CollectionUtils.isEmpty(bpmTask.getCandidateIds())) {
                bpmTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_HAS_NO_HANDLER.text());
            }
        }
        formButtonManager.setButtons(serviceManager, bpmTask,taskParams);
        WorkflowInstance workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), task.getWorkflowInstanceId());
        //如果实例状态为cancel 但是任务是in_progress 则重新终止下流程
        BPMEventBus.post(serviceManager,task,workflowInstance);
        SimpleAfterActionExecutionUtil.setAfterActionOperations(serviceManager, workflowInstance, task.getEntityId(), bpmTask.getExecution());
        bpmTask.setAreaDefaultValueOptions(serviceManager);
        stopWatch.lap("getButtonsByExecutionType");
        stopWatch.logSlow(100);

        //TODO 必需校验验证规则 下版本优化下 需要和终端沟通下
        task.getBpmExtension().put("validationRule", 1);
        bpmTask.sortPersons(!serviceManager.isOuterUserId());
        //异步更新已读人员
        if(TaskState.in_progress.equals(bpmTask.getState()) && bpmTask.getIsTaskOwner()){
            BPMEventBus.post(UpdateTaskReadEmployeeEvent.create(serviceManager, bpmTask.getId(), bpmTask.getCandidateIds()));
        }
        return bpmTask;
    }


    private Map<Pair<String, String>, Map<String, Object>> getTaskData(RefServiceManager serviceManager, Task task, DataCacheHandler dataCacheHandler) {
        Boolean completed = task.getCompleted();
        ExecutionTypeEnum executionType = task.getExecutionType();
        Map<Pair<String, String>, Map<String, Object>> ret = Maps.newHashMap();
        String entityId = task.getEntityId();
        String objectId = task.getObjectId();
        //已完成的并且是更新节点的 取snapshot. 其他从元数据查
        if (Boolean.TRUE.equals(completed)) {

            if (executionType.isMDObjectOrRelated()) {
                Map<String, Object> variables = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), task.getWorkflowInstanceId()).getVariables();

                String relatedEntityId = task.getRelatedEntityId();
                String relatedObjectId = getObjectIdFromVariable(variables, task.getRelatedObjectId());
                if (!Strings.isNullOrEmpty(relatedObjectId)) {
                    ret.put(new Pair<>(relatedEntityId, relatedObjectId),
                            serviceManager.findDataByIdWithEmptyMap(relatedEntityId, relatedObjectId));
                } else {
                    log.error("getTaskData:data not found:tenantId:{},taskId:{},relatedEntityId:{},relatedObjectId:{}"
                            , serviceManager.getTenantId(), task.getId(),
                            relatedEntityId, relatedObjectId);
                }
            }
        }

            log.info("getTaskData:taskId:{},entityId:{},objectId:{}", task.getId(), task.getEntityId(), task.getObjectId());
            if (!Strings.isNullOrEmpty(objectId)) {
                ret.put(new Pair<>(entityId, objectId), dataCacheHandler.getData(new Pair<>(entityId, objectId)));
            } else {
                log.error("getTaskData error : objectId null, wrong definition. CONTEXT={}" +
                        "OBJECT_ID={},TASK_ID={}", serviceManager.getContext(), objectId, task.getId());
                throw new BPMTaskReferObjectNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_CHOICE_OR_CREATE_OBJECT_NOT_FOUND);
            }
        return ret;
    }


    private String getObjectIdFromVariable(Map<String, Object> variables, Object objectExpression) {
        if (objectExpression == null) {
            return null;
        }

        String objectId = null;
        BPMExtensionUtils.Expression expression = BPMExtensionUtils.transferExpression(objectExpression);
        if (expression != null) {
            objectId = (String) variables.get(expression.getInstanceVariableKey());
        }
        return objectId;
    }

    @Override
    public DataCacheHandler getDataHandler(RefServiceManager serviceManager) {
        return new DataCacheHandler() {
            final Map<String, Map<String, Object>> describeCache = Maps.newConcurrentMap();
            final Map<String, Map<String, Object>> dataCache = Maps.newConcurrentMap();
            final Map<String, Map<String, Object>> dataPartCache = Maps.newConcurrentMap();
            Map<String, VariableExt> variableInstanceMap;

            @Override
            public Map<String, Object> getData(Pair<String, String> entityIdObjectId) {
                String objectId = entityIdObjectId.getValue();
                if(Strings.isNullOrEmpty(objectId)){
                    return Maps.newHashMap();
                }
                Map<String, Object> data = dataCache.get(entityIdObjectId.getValue());
                log.info("getDataHandler:entityId:{},objectId:{}", entityIdObjectId.getKey(), entityIdObjectId.getValue());
                if (data == null) {
                    if (!Strings.isNullOrEmpty(entityIdObjectId.getKey()) && !Strings.isNullOrEmpty(entityIdObjectId.getValue())) {
                        try {
                            data = serviceManager.findDataById(entityIdObjectId.getKey(), entityIdObjectId.getValue(), true, true,false,true);
                            if (data == null) {
                                data = Maps.newHashMap();
                            }
                            dataCache.put(entityIdObjectId.getValue(), data);
                        } catch (RestProxyBusinessException e) {
                            data = Maps.newHashMap();
                            // 填充form时，表达式对应的数据可能查不到（删除或没权限）
                            if (BPMBusinessExceptionCode.metadataNotFound(e.getCode())) {
                                data.put(ActivityKey.ExtensionKey.name, BPMConstants.REPLACE_WHEN_NOT_FOUND);
                            } else if (BPMBusinessExceptionCode.metadataNoPermission(e.getCode())) {
                                data.put(ActivityKey.ExtensionKey.name, BPMConstants.REPLACE_WHEN_NO_PERMISSION);
                            }
                            log.warn("setTaskForm : get task form expression field value failed ! CONTEXT={}, " +
                                    "ENTITY_ID={}, OBJECT_ID={}", serviceManager.getContext(), entityIdObjectId.getKey(), entityIdObjectId.getValue());
                        }
                    } else {
                        data = Maps.newHashMap();
                    }
                }

                return data;
            }

            @Override
            public Map<String, Object> getData(String entityId, String objectId) {
                return getData(new Pair<>(entityId, objectId));
            }

            @Override
            public Map<String, Map<String, Object>> getDataCache() {
                return dataCache;
            }

            @Override
            public Map<String, Map<String, Object>> getDescribeCache() {
                return describeCache;
            }

            @Override
            public Map<String, Object> getDescribe(String apiName, boolean includeStatistics) {
                Map<String, Object> describe = describeCache.get(apiName);
                if (null == describe) {
                    describe = serviceManager.findDescribe(apiName, true, includeStatistics);
                    describeCache.put(apiName, describe);
                }
                return describe;
            }

            @Override
            public Map<String, VariableExt> getVariableMap(String workflowInstanceId, String workflowId) {
                if (null == variableInstanceMap) {
                    Map<String, Object> variableInstances;
                    if (null != workflowInstanceId) {
                        WorkflowInstance workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), workflowInstanceId);
                        variableInstances = workflowInstance.getVariables();
                    } else {
                        variableInstances = Maps.newHashMap();
                    }
                    List<VariableExt> variables = paasWorkflow.getWorkflow(serviceManager.getContext(), workflowId).getVariables();
                    variableInstanceMap = variables.stream().collect(Collectors.toMap((item -> item.getId()), (item -> {
                        item.put("value", variableInstances.get(item.getId()));
                        return item;
                    })));
                }
                return variableInstanceMap;
            }

            @Override
            public Map<String, Object> getVariableInstances(String workflowInstanceId) {
                return paasWorkflow.getWorkflowInstance(serviceManager.getContext(), workflowInstanceId).getVariables();
            }

            @Override
            public Map<String, Object> findPartDataById(String entityId, String objectId, List<String> fields) {
                Map<String, Object> data = dataCache.get(objectId);
                if(MapUtils.isNotEmpty(data)) {
                    return data;
                }
                data = dataPartCache.get(objectId);
                if(MapUtils.isNotEmpty(data) && data.keySet().containsAll(fields)) {
                    return data;
                }
                data = newPaasMetadataProxy.findDataByIdWithFields(serviceManager.getContext(), entityId, objectId, fields);
                if (data == null) {
                    data = Maps.newHashMap();
                }
                dataPartCache.put(objectId, data);
                return data;
            }
        };
    }


    /**
     * @param serviceManager
     * @param laneId
     * @param instanceId
     * @return
     */
    @Override
    public GetTasksByLaneIdResult getTasksByLaneId(RefServiceManager serviceManager,String laneId, String instanceId,String entityId,String objectId,boolean applyButtons, Boolean notGetDatas, int page, int pageSize) {
        StopWatch stopWatch = StopWatch.create("getTasksByLaneId");

        //1. 通过instanceId获取到实例详情,获取到 activityInstances,workflowId
        WorkflowInstance workflowInstance = paasWorkflow.getWorkflowInstance(serviceManager.getContext(), instanceId);
        stopWatch.lap("paasWorkflow.getWorkflowInstance");
        if(Objects.isNull(workflowInstance)){
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_ID_NOT_FOUND);
        }

        //2. 通过workflowId查询其扩展信息,然后通过laneId获取到当前阶段下的activityId
        FlowExtensionEntity flowExtensionEntity = workflowExtensionDao.findOneFlowExtension(serviceManager.getTenantId(), workflowInstance.getWorkflowId());
        stopWatch.lap("workflowExtensionDao.find");


        //3. 通过activityId获取到对应的activityId
        List<String> activityIds = PoolEntity.getLaneActivityIds(flowExtensionEntity.getPools(), laneId);
        stopWatch.lap("getActivityInstanceIdsByLaneId");

        //当阶段没有进行中的任务时,此处为空
        if (CollectionUtils.isEmpty(activityIds)) {
            return new GetTasksByLaneIdResult(Collections.EMPTY_LIST, false);
        }

        //4. 通过instanceId和activityInstanceId查询所有的任务信息
        FindTasksByActivityIds.FindTasksResult searchResult = paasWorkflow.findTasksByActivityIds(serviceManager.getContext(), instanceId, activityIds, new Page(pageSize, page, "", false));
        List<Task> tasks = searchResult.getDataList();
        stopWatch.lap("paasWorkflow.getTasksByInstanceIds");

        // 查询第一页时，等待节点全部下发，后续不下发等待节点
        if(page == 1) {
            addDelayTask(serviceManager, instanceId, activityIds, flowExtensionEntity, workflowInstance, tasks);
        }

        List<LaneTask> laneTasks = transferLaneTasks(serviceManager, tasks,applyButtons, notGetDatas);
        stopWatch.lap("getLaneTasksManager.getHandler");
        stopWatch.logSlow(10);
        return new GetTasksByLaneIdResult(laneTasks, searchResult.getHasMore());
    }

    private void addDelayTask(RefServiceManager serviceManager, String instanceId, List<String> activityIds, FlowExtensionEntity flowExtensionEntity, WorkflowInstance workflowInstance, List<Task> tasks) {
        //查询等待节点
        List<Task> delayAutoTasks = getAutoTaskByConditions(
                serviceManager,
                instanceId,
                PoolEntity.getActivityInstanceIdsByActivityIds(activityIds, workflowInstance.getActivityInstances()),
                null,
                null,
                null
        );
        if (CollectionUtils.isNotEmpty(delayAutoTasks)) {
            delayAutoTasks.removeIf(t -> TaskState.pass.equals(t.getState()));
            if(CollectionUtils.isNotEmpty(delayAutoTasks)){
                for (Task delayAutoTask : delayAutoTasks) {
                    delayAutoTask.setSourceWorkflowId(workflowInstance.getSourceWorkflowId());
                }
                tasks.addAll(getInsertIndex(tasks), delayAutoTasks);
            }
        }
    }

    /**
     * 获取要插入延迟节点的位置
     * @param tasks
     * @return
     */
    private int getInsertIndex(List<Task> tasks) {
        if(CollectionUtils.isEmpty(tasks)) {
            return 0;
        }
        int index = -1;
        for(Task task:tasks) {
            if(TaskState.in_progress.equals(task.getState()) || TaskState.suspend.equals(task.getState())) {
                index ++;
            }
        }
        return index;
    }

    /**
     * 组装web和终端带有form信息的task列表
     * 获取所有任务的数据的负责人，判断是否有权限
     * @param serviceManager
     * @param tasks
     * @return
     */
    private List<LaneTask> getLaneTasks(RefServiceManager serviceManager, List<Task> tasks,boolean applyButtons, Boolean notGetDatas) {
        List<LaneTask> laneTasks = transferLaneTasks(serviceManager, tasks, applyButtons, notGetDatas);

        //5. 排序:待处理>已处理(按最后修改时间降序)
        return LaneTask.sort(laneTasks);
    }

    private List<LaneTask> transferLaneTasks(RefServiceManager serviceManager, List<Task> tasks,boolean applyButtons, Boolean notGetDatas) {
        Map<String, Set<String>> objectIdsByTasks = getObjectIdsByTasks(tasks);
        Map<String, Map<String, List<String>>> dataListOwner = getDataListOwner(objectIdsByTasks, serviceManager);
        TaskParams taskParams = TaskParams.create().clientInfo(serviceManager.getClientInfo()).dataListOwner(dataListOwner).applyButtons(applyButtons).notGetDatas(notGetDatas).isTaskDetail(false);

        List<LaneTask> laneTasks = Lists.newArrayList();
        fillFlowLayoutExists(serviceManager, tasks);
        for (Task task : tasks) {
            laneTasks.add(getLaneTask(serviceManager, taskParams, task));
        }
        return laneTasks;
    }

    private LaneTask getLaneTask(RefServiceManager serviceManager, TaskParams taskParams, Task task) {
        StopWatch stopWatch = StopWatch.create("getLaneTask");
        LaneTask laneTask = getLaneTasksManager.getHandler(task.getExecutionType())
                .execute(serviceManager, task, taskParams);
        stopWatch.lap("getHandler");
        laneTask.setMoreOperations(moreOperationManager.getTaskMoreOperations(serviceManager, laneTask, task));
        stopWatch.lap("getTaskMoreOperations");
        laneTask.sortPersons(!serviceManager.isOuterUserId());
        //设置当前人是否为处理人
        List<String> candidateIds = task.getCandidateIds();
        if (candidateIds != null) {
            laneTask.setIsTaskOwner(candidateIds.contains(serviceManager.getUserIdWithOuterUserId()));
        }
        SimpleAfterActionExecutionUtil.setAfterActionOperations(serviceManager, task.getEntityId(),laneTask.getExecution());
        stopWatch.lap("setAfterActionOperations");
        if (task.needAssignNextTask() && TaskState.in_progress.equals(task.getState())) {
            Map<String, Object> nextTaskAssigneeScope = task.getNextTaskAssigneeScope();
            laneTask.setAssignNextTask(1);
            if (MapUtils.isNotEmpty(nextTaskAssigneeScope)) {
                laneTask.setNextTaskAssigneeScope(serviceManager.getPersons(nextTaskAssigneeScope));
            }
        }
        stopWatch.lap("getPersons");
        laneTask.setWorkflowId(task.getWorkflowId());

        //2020年03月30日14:11:49  新版ue 进入数据详情页后,将当前看到的任务设置为已读
        // 获取进行中任务的id, 如果是error 不需要同步已读人员
        if(TaskState.in_progress.equals(laneTask.getState()) && laneTask.getIsTaskOwner()){
            BPMEventBus.post(UpdateTaskReadEmployeeEvent.create(serviceManager, laneTask.getId(), laneTask.getCandidateIds()));
        }
        stopWatch.lap("updateTaskReadEmployeeEvent");
        stopWatch.logSlow(100);
        return laneTask;
    }

    @Autowired
    private TaskDataManager taskDataManager;
    @Override
    public Boolean createTaskData(RefServiceManager serviceManager,
                                  String taskId,
                                  Map<String, Object> data,
                                  String activityId,
                                  Integer activityInstanceId,
                                  ExecutionTypeEnum executionType) {
        /**
         * 当时 @see BPMConstants.ExecutionTypeEnum.batchAddRelatedObject 时存储的
         */
        taskDataManager.getHandler(executionType).saveData(serviceManager,taskId,activityId,activityInstanceId,data);
        return Boolean.TRUE;
    }


    //可能taskId.size = 10, return.size = 8,需要终端判空
    @Override
    public TaskButtonList getButtonByTaskIds(RefServiceManager serviceManager, Set<String> taskIds, TaskParams taskParams) {

        StopWatch stopWatch = StopWatch.create("getButtonByTaskId");
        List<Task> tasks = paasWorkflow.getTasks(serviceManager.getContext(), taskIds);
        if(CollectionUtils.isEmpty(tasks)){
            return new TaskButtonList(Maps.newHashMap(), Maps.newHashMap());
        }
        stopWatch.lap("getTasks");
        Map<String, List<ActionButton>>  buttons = Maps.newHashMap();
        Map<String, TaskButtonList.CustomElementData>  customTaskConfigs = Maps.newHashMap();
        Map<String, String>  errorMsgs = Maps.newHashMap();
        taskParams.setFromTaskDetail(Boolean.FALSE);
        Map<String, Object> config = serviceManager.getFlowConfig(FlowConfigTerminal.ALL.name(), Lists.newArrayList(BPMConstants.TODO_CARD_MORE_BTN_CONFIG));
        boolean showMoreBtn = MapUtils.isNotEmpty(config) && config.containsKey(BPMConstants.TODO_CARD_MORE_BTN_CONFIG) && Boolean.TRUE.equals(config.get(BPMConstants.TODO_CARD_MORE_BTN_CONFIG));
        Map<String, List<MoreOperationManager.MoreOperation>>  moreOperations = showMoreBtn ? Maps.newHashMap() : null;
        for (Task task : tasks) {
            //如果是业务定制元素， 设置配置信息，返回
            if(ExecutionTypeEnum.custom.equals(task.getExecutionType())){
                customTaskConfigs.put(task.getId(),new TaskButtonList.CustomElementData(task.getCustomExtension(),serviceManager.getFlowElementWrapper(task.getElementApiName()).getPlugin()));
            }
            //操作类的  老对象不支持小程序  2021年11月15日15:08:45   排除签到/签退任务 790
            if (ExecutionTypeEnum.operation.equals(task.getExecutionType()) && !BPMConstants.OperationCodeType.signin.name().equals(task.getActionCode()) && !BPMConstants.OperationCodeType.signout.name().equals(task.getActionCode())) {
                //如果是预制对象 且不在白名单中 不下发按钮
                // return 返回false;addteammember 返回true;changeowner 返回true;
                if (OldObjectMappingEnum.getCrmObjectType(task.getEntityId()) != OldObjectMappingEnum.None && !BPMObjectSupportConfig.objectSupports.hasTaskListShowOperationButton(task.getEntityId(), task.getActionCode()) && taskParams.isMobile()) {
                    buttons.put(task.getId(), Lists.newArrayList());
                    continue;
                }
            }

            //获取standardData抛异常，跳过下发按钮  by 金蝶医疗流程图配置问题（task中没有objectId）
            StandardData standardData;
            try {
                standardData = getTaskManager.getHandler(task.getExecutionType()).getStandardData(serviceManager, task, taskParams);
            } catch (Exception e) {
                continue;
            }

            FormButtonResult formButtonResult = buttonCustomManager.getHandler(task.getExecutionType()).setButtons(
                    serviceManager, standardData,
                    task.needAssignNextTask(),
                    task.getState(),
                    task.getCandidateIds(),
                    task.getProcessIds(),
                    task.getExecution() != null ? task.getExecution().getErrorOrWaitingSimpleAfter() : new AfterActionExecution.SimpleAfterActionExecution(),
                    taskParams
            );
            List<ActionButton> btnResult = formButtonResult.getButtons();
            I18NParser.parse(serviceManager.getTenantId(), btnResult, I18NParseContext.of(task));
            buttons.put(task.getId(), btnResult);
            if (StringUtils.isNotBlank(formButtonResult.getErrorMsg())) {
                errorMsgs.put(task.getId(), formButtonResult.getErrorMsg());
            }
            if(showMoreBtn){
                moreOperations.put(task.getId(), moreOperationManager.getTaskMoreOperations(serviceManager,  task));
            }
        }
        stopWatch.lap("handle data");
        stopWatch.logSlow(1000);
        return new TaskButtonList(buttons, customTaskConfigs, errorMsgs, moreOperations, showMoreBtn);
    }

    /**
     *
     * @param serviceManager
     * @param taskId
     */
    @Override
    public void refreshHandlerByTaskId(RefServiceManager serviceManager, String taskId) {
        if (!ObjectId.isValid(taskId)) {
            throw new ValidationException(I18N.text(PAAS_FLOW_BPM_PARAMETER_ANOMALY.key));
        }
        paasWorkflow.regenerateHandler(serviceManager.getContext(), taskId);
    }
    /**
     * @param tasks
     * @return entityId,["objectId1","objectId2"]
     */
    private Map<String, Set<String>> getObjectIdsByTasks(List<Task> tasks ) {
        Map<String, Set<String>> objectIdsByEntityId = Maps.newHashMap();
        for (Task task : tasks) {
            Set<String> objectIds = objectIdsByEntityId.get(task.getEntityId());
            if (Objects.isNull(objectIds)) {
                objectIdsByEntityId.put(task.getEntityId(), Sets.newHashSet(task.getObjectId()));
            } else {
                objectIds.add(task.getObjectId());
            }
        }
        return objectIdsByEntityId;
    }

    @Override
    public List<AutoTask> findDelayTask(RefServiceManager serviceManager, String objectId, String workflowInstanceId, Integer pageNumber, Integer pageSize) {
        return paasWorkflow.findDelayTask(serviceManager.getContext(), objectId, workflowInstanceId, pageNumber, pageSize);
    }

    @Override
    public Map edit(RefServiceManager serviceManager, String taskId, String objectData, String details, Map<String, Object> optionInfo, Boolean notValidate, String originalData, String originalDetails){
        if (!ObjectId.isValid(taskId)) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ID_IS_ILLEGAL);
        }
        //查询任务
        Task task = getPaaSTask(serviceManager, taskId);
        if(Boolean.TRUE.equals(task.getCompleted())){
            //任务已经完成
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_COMPLETED_CANNOT_MODIFY_DATA);
        }
        TaskUtils.validateCompleteTask(serviceManager.getContext(),task);
        //获取entityId  可能是编辑查找关联对象 需要替换下
        String entityId = task.getEntityIdOrLookupFieldEntityId();
        //是否需要去掉修改对象的version
        Boolean needRemoveDataVersion = Objects.nonNull(serviceManager)&&SwitchConfigManager.ignoreDataVersion(serviceManager.getTenantId(),entityId);
        EditParams editParams = EditParams.of().notValidate(notValidate);
        return newPaasMetadataProxy.edit(serviceManager.getContext(), entityId, objectData, details,originalData, originalDetails, Boolean.FALSE, FlowType.workflow_bpm, task.getId(), task.getWorkflowInstanceId(), optionInfo, needRemoveDataVersion, editParams, EncoderUtils.encode(task.getWorkflowName()), task.getSourceWorkflowId()).getResult();
    }



    /**
     *
     * @param objectIds     entityId,["objectId1","objectId2"]
     * @param serviceManager serviceManager
     * @return
     */
    private Map<String, Map<String, List<String>>> getDataListOwner(Map<String, Set<String>> objectIds, RefServiceManager serviceManager) {
        /**
         * entityId,[objectId1:[1000],objectId2:[1222]]
         */
        Map<String, Map<String, List<String>>> dataListOwner = Maps.newHashMap();
        objectIds.forEach((entityId, ids) -> dataListOwner.put(entityId, serviceManager.getDataOwners(entityId, ids)));
        return dataListOwner;
    }


    private Consumer<Map<Pair<String, String>, List<TaskOutline>>> setTaskObjectNamesHandler(RefServiceManager serviceManager) {
        return entityTaskMaps -> {
            List<Pair<String, String>> entityIdObjectIds = Lists.newArrayList();
            entityIdObjectIds.addAll(entityTaskMaps.keySet());
            Map<String, String> result = serviceManager.getPaaSObjectNames(entityIdObjectIds);
            entityTaskMaps.forEach((k, v) -> v.forEach(item -> item.setObjectName(result.get(k.getValue()))));

        };
    }

    private List<Task> filterTaskListByActivityInstanceIds(List<Task> taskList, List<String> activityInstanceIds){
        List<Task> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(taskList) || CollectionUtils.isEmpty(activityInstanceIds)) {
            return result;
        }
        for (String activityInstanceId : activityInstanceIds) {
            for (Task task : taskList) {
                if (String.valueOf(task.getActivityInstanceId()).equals(activityInstanceId)) {
                    result.add(task);
                    continue;
                }
            }
        }
        return result;
    }

    public static void fillFlowLayoutExists(RefServiceManager serviceManager, List<Task> taskList){
        Map<String, Set<String>> entityLayoutMap = Maps.newHashMap();
        for (Task task : taskList) {
            if(Boolean.TRUE.equals(task.isUsedLayout())){
                entityLayoutMap.computeIfAbsent(task.getEntityIdOrLookupFieldEntityId(), k->Sets.newHashSet()).add(task.getLayoutApiName());
            }
        }
        if(MapUtils.isNotEmpty(entityLayoutMap)){
            Map<String, Set<String>> noExistsEntityLayoutMap = Maps.newHashMap();
            for (String entity : entityLayoutMap.keySet()) {
                noExistsEntityLayoutMap.put(entity, serviceManager.flowLayoutIsNoExist(entity, entityLayoutMap.get(entity)));
            }
            taskList.stream().filter(task -> Boolean.TRUE.equals(task.isUsedLayout()))
                .forEach(t -> t.setObjectFlowLayoutExists(!noExistsEntityLayoutMap.get(t.getEntityIdOrLookupFieldEntityId()).contains(t.getLayoutApiName())));
        }
    }

    @Override
    public List<Task> findTaskByIds(RefServiceManager manager, Set<String> taskIds) {
        return paasWorkflow.getTasks(manager.getContext(),taskIds);
    }

    @Override
    public void operateTask(RefServiceManager serviceManager, String taskId, String type, String opinion, TaskTagInfoArg tagInfoArg){
        Task task = getPaaSTask(serviceManager,taskId);
        if (!BPMConstants.APP_ID.equals(task.getAppId())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_TASK_ERROR);
        }
        String parallelRedisKey = getInstanceLockKey(serviceManager.getTenantId(), task.getWorkflowInstanceId());
        if (!redisManager.setValueWithExpireTime(parallelRedisKey, StringUtils.EMPTY, 10)) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.ENGINE_TASK_OPERATE_PROCESSING);
        }
        try {
            if(BPMConstants.SUSPEND_OPERATE.equals(type)){
                if(!TaskState.in_progress.equals(task.getState()) || task.getCompleted() || task.isWaiting()){
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_OPERATE_NOT_SUSPEND);
                }
            }else if (BPMConstants.RESUME_OPERATE.equals(type)){
                if(!TaskState.suspend.equals(task.getState())){
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_OPERATE_NOT_RESUME);
                }
            }else if (BPMConstants.ADD_TAG_OPERATE.equals(type)){
                if (!ButtonFunctionUtil.isNeedAddTag(serviceManager.getUserId(), task)) {
                    //当前任务无法加签，请重试
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_UNABLE_ADD_TAG);
                }
                if(Objects.isNull(tagInfoArg) || Objects.isNull(tagInfoArg.getSequence()) || CollectionUtils.isEmpty(tagInfoArg.getExtraNodeAssignee())){
                    //加签参数异常，请重试
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ADD_TAG_ARG_ERROR);

                }
            }else {
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_SUPPORTED_OPERATE);
            }
            paasWorkflow.operateTask(serviceManager.getContext(), taskId, type, opinion, tagInfoArg);
        }finally {
            redisManager.delete(parallelRedisKey);
        }
    }

    @Override
    public List<Task> findAutoTasksByInstanceId(RefServiceManager serviceManager, String instanceId,String entityId, TaskState state, String sourceWorkflowId,Long startTime, Long endTime, int pageNumber, int pageSize){
        List<Task> result = paasWorkflow.getAutoTasksByInstanceId(serviceManager.getContext(), instanceId,entityId,state, sourceWorkflowId, startTime, endTime, pageNumber, pageSize).getResult();
        if(CollectionUtils.isEmpty(result)){
            return Lists.newArrayList();
        }
        for (Task task : result) {
            task.setId(Boolean.TRUE.equals(task.getDelay()) ? task.getId() + BPMConstants.LATENCY_SUFFIX : task.getId() + BPMConstants.EXECUTION_SUFFIX);
        }
        return result;
    }

    @Override
    public void remindTask(RefServiceManager serviceManager, String taskId, String content, List<String> remindPersons){
        Task task = getPaaSTask(serviceManager,taskId);
        if (!BPMConstants.APP_ID.equals(task.getAppId())) {
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_COMPLETE_TASK_ERROR);
        }
        if(!TaskState.in_progress.equals(task.getState())){
            //进行中的任务才能催办，请重试
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_IN_PROGRESS_CAN_ONLY_REMIND);
        }
        if(CollectionUtils.isEmpty(remindPersons)){
            //催办人员是空，请重试
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_REMIND_PERSONS_IS_NULL);
        }
        paasWorkflow.remindTask(serviceManager.getContext(), taskId, content, remindPersons);
    }


}
