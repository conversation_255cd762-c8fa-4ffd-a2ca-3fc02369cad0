package com.facishare.bpm.service;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.CircleType;
import com.facishare.bpm.model.GetTasksByLaneIdResult;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.*;
import com.facishare.bpm.util.memory.page.Comparation;
import com.facishare.bpm.utils.DataCacheHandler;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wangz on 17-2-25.
 */
public interface BPMTaskService {
    PageResult<BPMTask> getTasksByPage(RefServiceManager serviceManager, Page page, TaskQuery query);

    PageResult<LaneBriefTaskVO> getAllWorkflowTasks(RefServiceManager serviceManager,
                                                    String sourceWorkflowId,
                                                    LaneBriefTaskVO.QueryType type,
                                                    String id,
                                                    LaneBriefTaskVO.QueryState state,
                                                    Page page);

    PageResult<LaneBriefTaskVO> getTasksByLane(RefServiceManager serviceManager, CircleType circleType, String sourceWorkflowId, List<Comparation> comparations, Page page);

    BPMTask getTask(RefServiceManager serviceManager, String taskId);

    Task getPaaSTask(RefServiceManager serviceManager, String taskId);

    Task getPaaSTaskNotThrowException(RefServiceManager serviceManager, String taskId);

    Map<String,Object> getBPMExtensionByTaskId(RefServiceManager serviceManager, String taskId);

  Task getPaaSTask(RefServiceManager serviceManager, String taskId, boolean useCache);

    CompleteTaskResult completeTask(RefServiceManager serviceManager,
                                    String taskId,String opinion,
                                    Map<String, Object> data,
                                    Integer addOrReplaceNextTaskAssignee,
                                    Map<String, Object> nextTaskAssignee,
                                    boolean needValidateNextTaskAssignee,
                                    Boolean needGetCurrentNextTask,
                                    Boolean ignoreNoBlockValidate
                                    );

    /**
     * @param serviceManager
     * @param objectId
     * @return
     */
    List<TaskOutline> getUncompletedTasksByObject(RefServiceManager serviceManager, String objectId);

    List<LaneTask> getUncompletedTaskInfosByObject(RefServiceManager serviceManager, String entityId, String objectId,boolean applyButtons, Boolean notGetDatas);

    MTask getMTask(RefServiceManager serviceManager, String instanceId, String activityInstanceId,String source,boolean applyButtons);

    List<BPMTask> getTasksByInstanceIds(RefServiceManager serviceManager, String workflowInstanceId,
                                        List<String> activityInstanceIds);

    PageResult<TaskOutline> getHandleTaskList(RefServiceManager serviceManager, Boolean isCompleted, String taskName, Page page);

    List<TaskLog> getTaskLogs(RefServiceManager serviceManager, String workflowInstanceId);

    boolean changeTaskHandlers(RefServiceManager serviceManager, String taskId, List<String> candidateIds, String modifyOpinion);

    Boolean replaceTaskHandlers(RefServiceManager serviceManager, String taskId, List<String> candidateIds);

    List<LaneBriefTaskVO> getWorkflowUncompletedTasks(RefServiceManager serviceManager, String sourceWorkflowId, LaneBriefTaskVO.QueryType type,
                                                      String id, LaneBriefTaskVO.QueryState state);

    AfterRetry.RetryResult afterActionRetry(RefServiceManager serviceManager, String taskId, int rownum, int executeType);

    List<Task> getTasksByInstanceId(RefServiceManager serviceManager, String instanceId);

    List<Task> getAutomaticAndQuartzTaskByInstanceId(RefServiceManager serviceManager, String instanceId);

    List<Task> getDelayAutoTaskByConditions(RefServiceManager serviceManager, String instanceId, List<String> activityInstanceIds, String id, String objectId);

    List<Task> getAutoTaskByConditions(RefServiceManager serviceManager, String instanceId, List<String> activityInstanceIds, String id, String objectId, Boolean isDelayTask);

    TaskDetail getBpmTaskDetail(RefServiceManager serviceManager, String taskId, String instanceId, String activityInstanceId, String activityId, TaskParams taskParams);

    DataCacheHandler getDataHandler(RefServiceManager serviceManager);

    GetTasksByLaneIdResult getTasksByLaneId(RefServiceManager serviceManager, String laneId, String instanceId, String entityId, String objectId, boolean applyButtons, Boolean notGetDatas, int page, int pageSize);

    Boolean createTaskData(RefServiceManager serviceManager, String taskId, Map<String, Object> data, String activityId, Integer activityInstanceId, ExecutionTypeEnum executionType);

    TaskButtonList getButtonByTaskIds(RefServiceManager serviceManager, Set<String> taskIds, TaskParams taskParams);

    void refreshHandlerByTaskId(RefServiceManager serviceManager, String taskId);

    Map edit(RefServiceManager serviceManager, String taskId, String objectData, String details, Map<String, Object> optionInfo, Boolean notValidate, String originalData, String originalDetails);

    List<AutoTask> findDelayTask(RefServiceManager serviceManager, String objectId, String workflowInstanceId, Integer pageNumber, Integer pageSize);

    List<Task> findTaskByIds(RefServiceManager manager, Set<String> taskIds);

    void operateTask(RefServiceManager serviceManager, String taskId, String type, String opinion, TaskTagInfoArg tagInfoArg);

    List<Task> findAutoTasksByInstanceId(RefServiceManager serviceManager, String instanceId, String entityId, TaskState state, String sourceWorkflowId, Long startTime, Long endTime, int pageNumber, int pageSize);

    void remindTask(RefServiceManager serviceManager, String taskId, String content, List<String> remindPersons);

}
