package com.facishare.bpm.service;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.model.SimpleTransition;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowLogs;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.rest.core.model.RemoteContext;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wangz on 17-2-25.
 */
public interface BPMDefinitionService {
    /**
     * @param serviceManager
     * @param entryType
     * @param externalFlow   1 外部流程，0 内部流程  null表示所有
     * @param containOtherTypeFlow
     * @return
     */
    List<WorkflowOutline> getAvailableWorkflows(RefServiceManager serviceManager, String entryType, String objectId, boolean filterCurrentObjectInstance, Integer externalFlow, Boolean validateScopes, boolean containOtherTypeFlow);

    PageResult<WorkflowOutline> getWorkflowOutlines(RefServiceManager serviceManager, WorkflowOutlineQuery query, Page page, String from, boolean isSupportPagingQuery);

    String deployWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean needValidateQuota, boolean needValidateData);
    
    String deployWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean needValidateQuota, boolean needValidateData, boolean skipTranslate);

    WorkflowOutlineEntity updateWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean isUpdate, boolean needValidate);

    WorkflowOutlineEntity updateWorkflow(RefServiceManager serviceManager, WorkflowOutline workflowOutline, boolean isUpdate, boolean needValidateData, boolean skipTranslate);

    void updateHistory(RefServiceManager serviceManager, WorkflowOutline workflowOutline);

    WorkflowExtension getWorkflowExtensionByWorkflowId(RefServiceManager serviceManager, String workflowId);

    boolean deleteWorkflowById(RefServiceManager serviceManager, String id);

    WorkflowOutline getWorkflowOutlineById(RefServiceManager serviceManager, String id);

    String getSourceWorkflowIdByOutlineId(RefServiceManager serviceManager, String id);

    /**
     * 查询数据信息,并清空rule
     *
     * @param serviceManager
     * @param id
     * @return
     */
    WorkflowOutline getWorkflowOutlineByIdOfClearRule(RefServiceManager serviceManager, String id);

    boolean enableWorkflow(RefServiceManager serviceManager, String id, boolean enable);

    WorkflowExtension getWorkflowExtensionByWorkflowId(RemoteContext context, String workflowId);

    WorkflowOutline getWorkflowOutlineBySourceId(RefServiceManager serviceManager, String sourceId);

    WorkflowOutline getWorkflowOutlineBySourceWorkflowId(RefServiceManager serviceManager, String sourceId);

    List<WorkflowOutline> getWorkflowOutlineBySourceId(RefServiceManager serviceManager, Set<String> sourceIds);

    Map<String, String> getWorkflowEntryTypeNameMap(RefServiceManager serviceManager, Collection<String> sourceIds);

    ExecutableWorkflowExt getWorkflowById(RefServiceManager serviceManager, String workflowId);

    Map getActivityDefByActivityId(RefServiceManager serviceManager, String workflowId, String activityId);

    List<SimpleTransition> getActivityTransitionsDefByActivityId(RefServiceManager serviceManager, ExecutableWorkflowExt executableWorkflow, String activityId);

    List<WorkflowOutline> getAllWorkflowOutlines(RefServiceManager serviceManager);

    boolean destroyTenant(RefServiceManager serviceManager, boolean includeDescribe);

    WorkflowOutline getDefinitionByWorkflowId(RefServiceManager serviceManager, String workflowId);

    Map<String, String> getUseApiNames(RefServiceManager serviceManager);

    WorkflowLogs.WorkflowLogsPage getWorkflowLogs(RefServiceManager serviceManager, String sourceWorkflowId, Page page);

    WorkflowOutline getWorkflowOutlineBySourceIdAndOutlineId(RefServiceManager serviceManager, String sourceWorkflowId, String outlineId);

    String convertDefinitionFromOtherType(RefServiceManager serviceManager, String id, String flowName);
}
