package com.facishare.bpm.utils;

import com.facishare.bpm.model.RelevantTeam;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.meta.BPMTaskObj;
import com.facishare.bpm.model.meta.BPMTaskOpinionObj;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.util.NumberUtil;
import com.facishare.bpm.util.TransferDataConstants.MDTaskField;
import com.facishare.bpm.utils.i18n.I18NUtils;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.bpm.util.TransferDataConstants.APINAME_INSTANCE;
import static com.facishare.bpm.util.TransferDataConstants.APINAME_TASK;

/**
 * Created by wangz on 17-7-4.
 */
@Slf4j
public class DataTransferUtils {
    private static final String FORMAT_THEME = "%s(%s)";
    private static final String FORMAT_DATETIME = "yyyy-MM-dd HH:mm";
    private static final String VALUE_RECORD_TYPE = "default__c";


    /**
     * 将引擎的实例转换为 自定义对象的map数据结构
     *
     * @param workflowInstance
     * @param pools
     * @param isNew
     * @return
     */
    public static BPMInstanceObj transferInstance(WorkflowInstance workflowInstance, List<PoolEntity> pools, boolean isNew, Set<String> referenceObjectIds, Long actualDuration) {
        BPMInstanceObj customizeInstanceData=new BPMInstanceObj();
        customizeInstanceData.set_id(workflowInstance.getId());
        customizeInstanceData.setRecord_type(VALUE_RECORD_TYPE);


        //固定数据 在更新时不再写入
        if (isNew) {
            String workflowName = StringUtils.trim(workflowInstance.getWorkflowName());
            customizeInstanceData.setSourceWorkflowId(workflowInstance.getSourceWorkflowId());
            customizeInstanceData.setObjectApiName(workflowInstance.getEntityId());
            customizeInstanceData.setObjectDataId(workflowInstance.getObjectId());
            customizeInstanceData.setWorkflowId(workflowInstance.getWorkflowId());
            customizeInstanceData.setExternalFlow(workflowInstance.getExternalFlow());
            Map<String, String> workflowNameDataI18N = workflowInstance.getWorkflowNameDataI18N();
            customizeInstanceData.setWorkflowName(workflowName);
            customizeInstanceData.setWorkflowName__lang(workflowNameDataI18N);
            customizeInstanceData.setStartTime(workflowInstance.getStart());
            if(StringUtils.isNotEmpty(workflowInstance.getOuterSubmitter())){
                customizeInstanceData.setOut_owner(Sets.newHashSet(workflowInstance.getOuterSubmitter()));
            }
            customizeInstanceData.setApplicantId(Sets.newHashSet(workflowInstance.getApplicantId()));
            customizeInstanceData.setTriggerSource(workflowInstance.getTriggerSource());
            customizeInstanceData.setName(getThemeFromName(workflowName, workflowInstance.getStart()));
            customizeInstanceData.setName__lang(getThemeFromNameI18NMap(workflowNameDataI18N, workflowInstance.getStart()));

        }

        //更新变动数据
        //完成时间，状态更新
        InstanceState state = workflowInstance.getState();
        if (workflowInstance.hasEnd()) {
            customizeInstanceData.setEndTime(workflowInstance.getEnd());
            if (InstanceState.cancel == state) {
                customizeInstanceData.setState(InstanceState.cancel);
                customizeInstanceData.setCancel_reason(workflowInstance.getReason());
                customizeInstanceData.setCancel_from_person(workflowInstance.getCancelPerson());
            } else if (InstanceState.error == state) {
                customizeInstanceData.setState(InstanceState.error);
            } else {
                customizeInstanceData.setState(InstanceState.pass);
            }
            String duration = workflowInstance.getDuration();
            customizeInstanceData.setDuration(!Strings.isNullOrEmpty(duration) ? Long.valueOf(duration) : null);
            customizeInstanceData.setActual_duration(actualDuration);
        } else {
            if (Lists.newArrayList(InstanceState.cancel, InstanceState.error, InstanceState.in_progress).contains(state)) {
                customizeInstanceData.setState(state);
            }
        }
        customizeInstanceData.setError_reason(InstanceState.error.equals(state) ? workflowInstance.getErrMsg() : "");
        Map<String, Object> variables = workflowInstance.getVariables();
        Set<String> objectIds = Sets.newHashSet();
        for (String name : variables.keySet()) {
            if ((name.contains(BPMConstants.ENTITY_FIELD_SPLIT) &&
                    name.split(BPMConstants.ENTITY_FIELD_SPLIT).length == 2 &&
                    !name.split(BPMConstants.ENTITY_FIELD_SPLIT)[1].equals(BPMConstants.ApproveResult.RESULT)&&
                    !name.split(BPMConstants.ENTITY_FIELD_SPLIT)[0].equals(BPMConstants.CUSTOM_VARIABLE)) || name.contains(BPMConstants.SELF_REF_KEY)) {
                String variablesObjectId = (String) variables.get(name);
                if (!Strings.isNullOrEmpty(variablesObjectId)) {
                    objectIds.add(variablesObjectId);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(referenceObjectIds)) {
            objectIds.addAll(referenceObjectIds);
        }
        customizeInstanceData.setObjectIds(objectIds);
        //设置正在执行任务所在的阶段名
        setStageProperties(customizeInstanceData, workflowInstance.getActivityInstances(), pools
                , workflowInstance.getTenantId(), workflowInstance.getWorkflowId(), workflowInstance.getSourceWorkflowId());
        return customizeInstanceData;
    }

    /**
     * 设置当期阶段和当期任务
     *
     * @param mdInstance
     * @param activityInstances
     * @param pools
     */
    private static void setStageProperties(BPMInstanceObj mdInstance, List<ActivityInstance> activityInstances, List<PoolEntity> pools, String tenantId, String workflowId, String sourceWorkflowId) {

        if (InstanceState.in_progress.equals(mdInstance.getState()) || InstanceState.error.equals(mdInstance.getState())) {
            List<String> currentStageNames = Lists.newArrayList();
            List<String> currentTaskNames = Lists.newArrayList();
            List<String[]> stagesKeyList = Lists.newArrayList();
            List<String[]> tasksKeyList = Lists.newArrayList();
            for (ActivityInstance activityInstance : activityInstances) {
                if (activityInstance.getEnd() == null) {
                    String activityId = activityInstance.getActivityId();
                    String taskName = StringUtils.trim(activityInstance.getActivityName());
                    Pair<String, String> stageInfo = getStage(activityId, pools);
                    String laneName=StringUtils.trim(stageInfo.getValue());
                    currentStageNames.add(laneName);
                    stagesKeyList.add(new String[]{I18NUtils.TRANSLATE_PRE + workflowId + "." + stageInfo.getKey() + ".name", I18NUtils.TRANSLATE_PRE + sourceWorkflowId + "." + stageInfo.getKey() + ".name"});
                    currentTaskNames.add(taskName);
                    tasksKeyList.add(new String[]{I18NUtils.TRANSLATE_PRE + workflowId + "." + activityId + ".name", I18NUtils.TRANSLATE_PRE + sourceWorkflowId + "." + activityId + ".name"});
                }
            }
            mdInstance.setStageNames(currentStageNames);
            mdInstance.setStageNames__lang(I18NUtils.obtainI8NCodeAndValueByKeyLevelList(tenantId, stagesKeyList));
            mdInstance.setTaskNames(currentTaskNames);
            mdInstance.setTaskNames__lang(I18NUtils.obtainI8NCodeAndValueByKeyLevelList(tenantId, tasksKeyList));
        }
    }

    /**
     * 任务 task 转成 元数据格式 数据
     *
     * @param metadataInstance MD_workflowInstance  自定义
     */
    public static BPMTaskObj transferTask(Task task, BPMInstanceObj metadataInstance, List<PoolEntity> pools, boolean isNew,
                                          Function<List<String>, Map<Integer, Employee>> getEmployeeFunction, String sessionKey, Long actualDuration) {
        BPMTaskObj bpmTaskObj=new BPMTaskObj();
        bpmTaskObj.set_id(task.getId());
        bpmTaskObj.setRecord_type(VALUE_RECORD_TYPE);
        bpmTaskObj.setOwner(metadataInstance.getOwner());
        if (isNew) {
            bpmTaskObj.setWorkflowId(task.getWorkflowId());
            bpmTaskObj.setSourceWorkflowId(task.getSourceWorkflowId());
            bpmTaskObj.setObjectApiName(task.getEntityId());
            bpmTaskObj.setObjectDataId(task.getObjectId());
            bpmTaskObj.setActivity_instance_id(task.getActivityInstanceId());
            bpmTaskObj.setTask_type(task.transformTaskType());

            String taskName = StringUtils.trim(task.getName());
            Map<String, String> taskNameDataI18N = task.getTaskNameDataI18N();
            bpmTaskObj.setTaskName(taskName);
            bpmTaskObj.setTaskName__lang(taskNameDataI18N);
            bpmTaskObj.setWorkflowInstanceId(task.getWorkflowInstanceId());
            bpmTaskObj.setActivityId(task.getActivityId());
            bpmTaskObj.setStartTime(task.getCreateTime());
            bpmTaskObj.setName(getThemeFromName(taskName, task.getCreateTime()));
            bpmTaskObj.setName__lang(getThemeFromNameI18NMap(taskNameDataI18N, task.getCreateTime()));

            Pair<String, String> stage = getStage(task.getActivityId(), pools);
            bpmTaskObj.setStageId(stage.getKey());
            bpmTaskObj.setStageName(StringUtils.trim(stage.getValue()));
            bpmTaskObj.setStageName__lang(task.getStageNameDataI18N(stage.getKey()));

            //设置人员信息,定义
            Map<String, List<String>> assignee = task.getAssignee();
            Map<String, List<String>> customizeAssignee = Maps.newHashMap();
            if (MapUtils.isNotEmpty(assignee)) {
                customizeAssignee.put(MDTaskField.person.getValue(), assignee.get(MDTaskField.person.name()));
                customizeAssignee.put(MDTaskField.dept.getValue(), assignee.get(MDTaskField.dept.name()));
                customizeAssignee.put(MDTaskField.dept_leader.getValue(), assignee.get(MDTaskField.dept_leader.name()));
                customizeAssignee.put(MDTaskField.group.getValue(), assignee.get(MDTaskField.group.name()));
                customizeAssignee.put(MDTaskField.role.getValue(), assignee.get(MDTaskField.role.name()));
                customizeAssignee.put(MDTaskField.ext_bpm.getValue(), assignee.get(MDTaskField.ext_bpm.name()));
            }
            bpmTaskObj.setAssignee(customizeAssignee);
            bpmTaskObj.setWorkflowInstanceName(metadataInstance.getName());
            bpmTaskObj.setWorkflowInstanceName__lang(getThemeFromNameI18NMap(task.getWorkflowNameDataI18N(), metadataInstance.getStartTime()));
            bpmTaskObj.setObjectIds(metadataInstance.getObjectIds());
            bpmTaskObj.setLink_app_type(task.getLinkAppType());
            bpmTaskObj.setLink_app_id(task.getLinkApp());
            bpmTaskObj.setExecution_type(task.getExecutionTypeAndActionCode());
            bpmTaskObj.setAction_code(task.getExternalApplyTaskActionCode());
            bpmTaskObj.setSession_key(sessionKey);
            bpmTaskObj.setOut_owner(metadataInstance.getOut_owner());
            if(ExecutionTypeEnum.approve.equals(task.getExecutionType())){
                bpmTaskObj.setIs_tag(BPMConstants.TagType.TAG.equals(task.getNodeType()));
            }
        }

        List<String> candidateIds = task.getCandidateIds();
        //设置待处理人
        bpmTaskObj.setCandidateIds(candidateIds);
        //将待处理人保存到团队成员
        if (CollectionUtils.isNotEmpty(candidateIds)) {
            List<String> outCandidateIds = candidateIds.stream().filter(RelevantTeam::isOuterUser).collect(Collectors.toList());
            Map<Integer, Employee> employeeMap = null;
            if (CollectionUtils.isNotEmpty(outCandidateIds)) {
                employeeMap = getEmployeeFunction.apply(candidateIds);
            }
            bpmTaskObj.setRelevant_team(RelevantTeam.getRelevantTeams(candidateIds, employeeMap));
            //为current_candidate_ids字段增加值

        }

        //unit:1-天；2-小时；3-分钟  默认是小时
        Integer latencyUnit = task.getLatencyUnit();
        Double remindLatencyData = task.remindLatency();
        //unit:1-天；2-小时；3-分钟  默认是小时
        Double remindLatency = NumberUtil.getRemindLatency(latencyUnit, remindLatencyData);
        bpmTaskObj.setRemindLatency(remindLatency);

        long regularSuspendDuration = task.getRegularSuspendDuration();
        if(regularSuspendDuration > 0){
            bpmTaskObj.setSuspend_accumulate_time(regularSuspendDuration);
        }
        long lastSuspendTime = task.getLastSuspendTime();
        if(lastSuspendTime > 0){
            bpmTaskObj.setLast_suspend_time(lastSuspendTime);
        }

        //任务状态和结束时间
        if (Boolean.TRUE.equals(task.getCompleted())) {
            long lastOpinionTime = task.lastOpinionTime();
            bpmTaskObj.setEndTime(lastOpinionTime);
            if (task.getState().equals(TaskState.error)) {
                if (InstanceState.cancel.equals(metadataInstance.getState())) {
                    //如果实例状态为cancel 则将任务的状态修改为cancel
                    bpmTaskObj.setState(TaskState.cancel);
                } else {
                    bpmTaskObj.setState(TaskState.error);
                }
            } else if (task.getState().equals(TaskState.cancel)) {
                bpmTaskObj.setState(TaskState.cancel);
            } else {
                bpmTaskObj.setState(TaskState.pass);
            }
            long duration = lastOpinionTime - task.getCreateTime() - regularSuspendDuration;
            bpmTaskObj.setDuration(duration);
            // 保存的统一为毫秒
            boolean isTimeout = (remindLatency > 0L) && (duration > remindLatency);
            bpmTaskObj.setIsTimeout(isTimeout);
            bpmTaskObj.setTimeoutTime(isTimeout ? duration - remindLatency : 0L);
            bpmTaskObj.setProcessorIds(task.getProcessIds());
            bpmTaskObj.setCurrent_candidate_ids(Sets.newHashSet());
            //如果是审批任务（审批、会签），需要保存下结果action_type
            if(ExecutionTypeEnum.approve.equals(task.getExecutionType())){
                bpmTaskObj.setAction_type(task.getActionType());
            }
            bpmTaskObj.setActual_duration(actualDuration);
        } else {
            bpmTaskObj.setProcessorIds(task.getProcessIds());
            bpmTaskObj.setCurrent_candidate_ids(task.getSequenceTaskCandidateIds());
            TaskState state = task.getRealState();
            if (Lists.newArrayList(TaskState.cancel, TaskState.error, TaskState.in_progress, TaskState.suspend, TaskState.tag_waiting).contains(state)) {
                bpmTaskObj.setState(state);
            }
        }
        bpmTaskObj.setError_reason(TaskState.error.equals(task.getState()) ? task.getErrMsg() : "");

        return bpmTaskObj;
    }

    public static BPMTaskOpinionObj transferTaskOpinion(Task task, Opinion opinion){
        BPMTaskOpinionObj bpmTaskOpinionObj = new BPMTaskOpinionObj();
        bpmTaskOpinionObj.set_id(opinion.getId());
        bpmTaskOpinionObj.setTask_id(task.getId());
        bpmTaskOpinionObj.setTask_api_name(APINAME_TASK);
        bpmTaskOpinionObj.setActivity_id(task.getActivityId());
        bpmTaskOpinionObj.setWorkflow_id(task.getWorkflowId());
        bpmTaskOpinionObj.setSource_workflow_id(task.getSourceWorkflowId());
        bpmTaskOpinionObj.setWorkflow_instance_id(task.getWorkflowInstanceId());
        bpmTaskOpinionObj.setWorkflow_instance_api_name(APINAME_INSTANCE);
        bpmTaskOpinionObj.setReply_time(opinion.getReplyTime());
        bpmTaskOpinionObj.setOperator_id(Lists.newArrayList(opinion.getUserId()));
        bpmTaskOpinionObj.setAction_type(opinion.getActionType());
        bpmTaskOpinionObj.setOpinion(opinion.getOpinion());
        return bpmTaskOpinionObj;
    }

    public static BPMTaskOpinionObj transferTaskSuspendOpinion(Task task, OperateLog operate){
        BPMTaskOpinionObj bpmTaskOpinionObj = new BPMTaskOpinionObj();
        bpmTaskOpinionObj.set_id(operate.getId());
        bpmTaskOpinionObj.setTask_id(task.getId());
        bpmTaskOpinionObj.setTask_api_name(APINAME_TASK);
        bpmTaskOpinionObj.setActivity_id(task.getActivityId());
        bpmTaskOpinionObj.setWorkflow_id(task.getWorkflowId());
        bpmTaskOpinionObj.setSource_workflow_id(task.getSourceWorkflowId());
        bpmTaskOpinionObj.setWorkflow_instance_id(task.getWorkflowInstanceId());
        bpmTaskOpinionObj.setWorkflow_instance_api_name(APINAME_INSTANCE);
        bpmTaskOpinionObj.setReply_time(operate.getCreateTime());
        bpmTaskOpinionObj.setOperator_id(Lists.newArrayList(operate.getUserId()));
        bpmTaskOpinionObj.setAction_type(operate.getType());
        bpmTaskOpinionObj.setOpinion(operate.getOpinion());
        return bpmTaskOpinionObj;
    }

    public static void setParticipantsAndObjectIds(BPMInstanceObj mdInstance, List<Task> sourceTasks, Map<Integer, Employee> employeeMap, Set<String> referenceObjectIds) {
        Collection oldParticipants = mdInstance.getRelevant_team();
        Set<RelevantTeam> participants = RelevantTeam.getRelevantCollection(oldParticipants);

        Collection<String> oldObjectIds = mdInstance.getObjectIds();
        Set<String> objectIds = Sets.newHashSet(oldObjectIds);

        sourceTasks.forEach(sourceTask -> {
            List<String> candidateIds = sourceTask.getCandidateIds();
            participants.addAll(RelevantTeam.getRelevantTeams(candidateIds, employeeMap));
            String objectId = sourceTask.getObjectId();
            if (!Strings.isNullOrEmpty(objectId)) {
                objectIds.add(objectId);
            }
        });

        if (CollectionUtils.isNotEmpty(referenceObjectIds)) {
            objectIds.addAll(referenceObjectIds);
        }
        mdInstance.setRelevant_team(participants);
        mdInstance.setObjectIds(objectIds);
    }


    public static Pair<String, String> getStage(String activityId, List<PoolEntity> pools) {
        for (PoolEntity pool : pools) {
            if (pool.getLanes() != null) {
                for (LaneEntity lane : pool.getLanes()) {
                    if (lane.getActivities() != null && lane.getActivities().contains(activityId)) {
                        return new Pair<>(lane.getId(), lane.getName());
                    }
                }
            }
        }

        return new Pair<>("", "");
    }


    private static String getThemeFromName(String name, long time) {
        return String.format(FORMAT_THEME, StringUtils.trim(name), timeFormat(time));
    }

    public static Map getThemeFromNameI18NMap(Map<String, String> i18nData, long time){
        if(MapUtils.isEmpty(i18nData)){
            return null;
        }
        Map result = Maps.newHashMap();
        for (String k : i18nData.keySet()) {
            result.put(k, getThemeFromName(i18nData.get(k), time));
        }
        return result;
    }

    private static String timeFormat(long time) {

        SimpleDateFormat dateFormat = new SimpleDateFormat(FORMAT_DATETIME);
        return dateFormat.format(time);
    }

}
