package com.facishare.bpm.utils;


import com.facishare.bpm.exception.*;
import com.facishare.bpm.handler.task.detail.helper.TaskHelper;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.TaskOutline;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.model.resource.metadata.RW;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.util.MetadataUtils;
import com.facishare.bpm.utils.helper.StopWatch;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 08/03/2017.
 */
@Slf4j
public class TaskUtils {
    /**
     * 国家省市区，在CRM老对象中时一个字段，校验必填字段时，不分别校验
     */
    static List<String> countryCascadeFields = Lists.newArrayList("country", "province", "city", "district");

    public interface TaskOtherDetail<D, E, F, G> {
        void execute(D d, E e, F f, G g);
    }

    public static List<TaskOutline> getTaskOutlineFromTasks(List<Task> tasks, Map<String, WorkflowInstance> workflowInstanceCache,
                                                            Map<String, FlowExtensionEntity> extensionCache,
                                                            TaskOtherDetail<Task, TaskOutline, WorkflowInstance,
                                                                    FlowExtensionEntity> setOtherDetailFun,
                                                            Consumer<Map<Pair<String, String>, List<TaskOutline>>> setObjectNames,
                                                            StopWatch stopWatch
    ) {
        List<TaskOutline> taskOutlines = Lists.newArrayList();

        if (tasks != null) {
            Map<Pair<String, String>, List<TaskOutline>> entityTaskMaps = Maps.newHashMap();

            for (Task task : tasks) {

                TaskOutline taskOutline = new TaskOutline();
                taskOutline.setActivityId(task.getActivityId());
                taskOutline.setTaskId(task.getId());
                taskOutline.setProcessName(task.getWorkflowName());
                taskOutline.setDescription(task.getWorkflowDescription());
                taskOutline.setApplicantId(task.getApplicantId());
                taskOutline.setEntityId(task.getEntityId());
                taskOutline.setCreateTime(task.getCreateTime());
                taskOutline.setActivityInstanceId(task.getActivityInstanceId());
                taskOutline.setWorkflowInstanceId(task.getWorkflowInstanceId());
                taskOutline.setExecutionTypeEnum(task.getExecutionType());

                taskOutline.setTaskName(task.getName());
                taskOutline.setAssignee(task.getAssignee());
                taskOutline.setModifyTime(task.getModifyTime());
                taskOutline.setLatencyUnit(task.getLatencyUnit());
                taskOutline.setRemindLatency(task.getRemindLatency(), task.getLatencyUnit());
                taskOutline.setObjectId(task.getObjectId());
                taskOutline.setCandidateIds(task.getCandidateIds());
                taskOutline.setAssigneeIds(task.getAssigneeIds());
                taskOutline.setCompleted(task.getCompleted());
                taskOutline.setState(task.getState());
                taskOutline.setTaskType(task.getTaskType());
                taskOutline.setProcessIds(task.getProcessIds());
                taskOutline.setUnProcessIds(task.getUnProcessIds());
                taskOutline.setLinkAppName(task.getLinkAppName());
                taskOutline.setLinkApp(task.getLinkApp());
                taskOutline.setLinkAppEnable(task.getLinkAppEnable());
                taskOutline.setLinkAppType(task.getActionType());
                taskOutline.setWorkflowId(task.getWorkflowId());
                taskOutline.setSourceWorkflowId(task.getSourceWorkflowId());
                if (setObjectNames != null) {
                    Pair<String, String> entityIdObjectId = new Pair<>(task.getEntityId(), task.getObjectId());
                    if (entityTaskMaps.get(entityIdObjectId) != null) {
                        entityTaskMaps.get(entityIdObjectId).add(taskOutline);
                    } else {
                        entityTaskMaps.put(entityIdObjectId, Lists.newArrayList(taskOutline));
                    }
                }

                FlowExtensionEntity extension = null;
                if (extensionCache != null) {
                    extension = extensionCache.get(task.getWorkflowId());
                }

                WorkflowInstance instance = MapUtils.isEmpty(workflowInstanceCache) ? null : workflowInstanceCache.get(task
                        .getWorkflowInstanceId());

                setOtherDetailFun.execute(task, taskOutline, instance, extension);
                taskOutlines.add(taskOutline);
            }
            stopWatch.lap("transTasks to taskOutlines");
            if (setObjectNames != null) {
                setObjectNames.accept(entityTaskMaps);
                stopWatch.lap("setEntryObjectNames");
            }
        }

        return taskOutlines;
    }

    public static void setCompletedTaskForm(BPMTask bpmTask,
                                            Map<String, Object> desc,
                                            Map<String, Integer> fieldsAuth,
                                            Map<String, Object> objectData,
                                            DataCacheHandler formHandler) {
        log.debug("setCompletedTaskForm : TASK_ID={}", bpmTask.getId());
        Map<String, Object> bpmExtension = bpmTask.getExtension();
        Map<String, Object> fieldDescs = (Map<String, Object>) desc.get(MetadataKey.fields);
        List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) bpmExtension.get(
                ExtensionKey.form);
        if (forms != null && objectData != null) {
            for (List<Map<String, Object>> form : forms) {
                List<Map<String, Object>> hiddenItems = Lists.newArrayList();
                for (Map<String, Object> formItem : form) {
                    String fieldName = (String) formItem.get(ExtensionKey.name);
                    //如果没有权限，就不再value填充了，待下面统一去除没有权限字段
                    if (isHidden(fieldsAuth, formItem)) {
                        hiddenItems.add(formItem);
                        continue;
                    }
                    Map<String, Object> fieldDesc = (Map<String, Object>) fieldDescs.get(fieldName);
                    //如果字段已经被禁用或删除，则不再显示
                    // 字段描述为空|| 字段描述中的isActive=false,获取不到默认为true
                    if (MapUtils.isEmpty(fieldDesc) || !(boolean) fieldDesc.getOrDefault(MetadataKey.isActive, true)) {
                        hiddenItems.add(formItem);
                        continue;
                    }

                    //1.设置value
                    Object fieldValue = getFieldValue(objectData, (String) formItem.get(ExtensionKey.type), fieldName);
                    //若是掩码字段，则直接用__s 替换字段值
                    if (TaskHelper.isMask(objectData, formItem)) {
                        fieldValue = objectData.get(fieldName + MetadataKey.maskFieldNameValuePostfix);
                    }
                    formItem.put(ExtensionKey.value, fieldValue);
                    //2. select类型加上options,lookup类型加上relatedObjectName
                    setFormFieldsExtension(formItem, fieldDesc, formHandler);
                    setOtherSelectOneValue(fieldValue, fieldDesc, formItem, objectData, fieldName);


                    //todo 商机和订单有几个字段，描述不对，应该是is_required 携程 required了。 这里暂时做下兼容，需要open-api刷库
                    boolean isRequired = (boolean) fieldDesc.getOrDefault(MetadataKey.isRequired, false);

                    if (isRequired && !TaskHelper.hasInheritType(fieldDesc)) {
                        formItem.put(ExtensionKey.required, true);
                    }

                    //3.label动态加载
                    formItem.put(MetadataKey.label, fieldDesc.get(MetadataKey.label));

                    TaskHelper.setFormFieldProperty(fieldsAuth, formItem, objectData,fieldDesc);
                }
                form.removeAll(hiddenItems);
            }

        }

    }

    /**
     * @param bpmTask
     * @param desc
     * @param fieldsAuth
     * @param formHandler
     */
    public static void setUncompletedTaskFrom(BPMTask bpmTask,
                                              Map<String, Object> desc,
                                              Map<String, Integer> fieldsAuth,
                                              DataCacheHandler formHandler) {
        Map<String, Object> bpmExtension = bpmTask.getExtension();
        String objectId = bpmTask.getObjectId(); //获取objectId
        Map<String, Object> objectData = formHandler.getData(new Pair<>(bpmTask.getEntityId(), objectId));
        //必填字段但为空的字段.在更新时如果为空,会更新失败.在此遍历,记录,给出提示
        if (desc != null) {
            List<String> noValueFields = verifyRequiredFields(objectData, desc);
            objectData = MetadataUtils.validateAndCorrectDataValue(objectData, desc);
            Map<String, Object> fieldDescs = (Map<String, Object>) desc.get(MetadataKey.fields);
            List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) bpmExtension.get(
                    ExtensionKey.form);
            if (forms != null) {
                for (List<Map<String, Object>> form : forms) {
                    List<Map<String, Object>> hiddenItems = Lists.newArrayList();
                    for (Map<String, Object> formItem : form) {
                        String fieldName = (String) formItem.get(ExtensionKey.name);
                        //1.如果没有权限，就不再value填充了，待下面统一去除没有权限字段
                        if (isHidden(fieldsAuth, formItem)) {
                            hiddenItems.add(formItem);
                            continue;
                        }

                        //2.填充字段value（默认值/表达式/节点对象值）; select类型加上options,lookup类型加上relatedObjectName
                        Map<String, Object> fieldDesc = (Map<String, Object>) fieldDescs.get(fieldName);
                        // 字段描述为空|| 字段描述中的isActive=false,获取不到默认为true
                        if (MapUtils.isEmpty(fieldDesc) || !(boolean) fieldDesc.getOrDefault(MetadataKey.isActive, true)) {
                            hiddenItems.add(formItem);
                            continue;
                        }
                        setUncompletedFormValue(objectData, formItem, fieldDesc, formHandler);

                        //3.label动态加载
                        formItem.put(MetadataKey.label, fieldDesc.get(MetadataKey.label));
                        formItem.put(MetadataKey.type, fieldDesc.get(MetadataKey.type));
                        formItem.put(MetadataKey.RETURN_TYPE, fieldDesc.get(MetadataKey.RETURN_TYPE));
                        //4.判读当前字段是否是必填字段
                        //todo 商机和订单有几个字段，描述不对，应该是is_required 写成了 required。 这里暂时做下兼容，需要open-api刷库
                        boolean isRequired = (boolean) fieldDesc.getOrDefault(MetadataKey.isRequired, false);
                        if (isRequired && !TaskHelper.hasInheritType(fieldDesc)) {
                            formItem.put(ExtensionKey.required, true);
                            //form中已经存在的必填字段就不再提示文案了
                            noValueFields.remove(fieldDesc.get(MetadataKey.label));
                        }
                        TaskHelper.setFormFieldProperty(fieldsAuth, formItem, objectData,fieldDesc);

                    }
                    form.removeAll(hiddenItems);
                }
            }
            if (CollectionUtils.isNotEmpty(noValueFields)) {
                bpmTask.setErrorMsg(BPMI18N.PAAS_FLOW_BPM_CURRENT_TASK_FORM_HAS_MUST_FIELD.text().replaceFirst("-", String.join(",",
                        noValueFields)));
            }
        }
    }

    private static void setUncompletedFormValue(Map<String, Object> objectData,
                                                Map<String, Object> formItem,
                                                Map<String, Object> fieldDesc,
                                                DataCacheHandler formHandler) {

        String fieldName = (String) formItem.get(ExtensionKey.name);

        //1.如果是表达式，用表达式数据填充；如果有默认值不用动；如果都没有，就用节点业务对象数据填充
        if (objectData != null) {
            Object fieldValue = getFieldValue(objectData, (String) formItem.get(ExtensionKey.type), fieldName);
            //若是掩码字段，则直接用__s 替换字段值
            if (TaskHelper.isMask(objectData, formItem)) {
                fieldValue = objectData.get(fieldName + MetadataKey.maskFieldNameValuePostfix);
            }
            formItem.put(ExtensionKey.value, fieldValue);
        }

        Object expObj = formItem.get(ExtensionKey.value);
        setOtherSelectOneValue(expObj, fieldDesc, formItem, objectData, fieldName);

        //2.填充select option/关联对象信息
        setFormFieldsExtension(formItem, fieldDesc, formHandler);
    }

    private static final String TEXT = "text";
    private static final String LONG_TEXT = "long_text";

    /**
     * 获取表单的value时，优先获取__r
     * @param objectData
     * @param fieldName
     * @return
     */
    private static Object getFieldValue(Map<String, Object> objectData, String fieldType, String fieldName) {
        if(TEXT.equals(fieldType) || LONG_TEXT.equals(fieldType)) {
            Object value = objectData.get(fieldName + "__r");
            if (Objects.nonNull(value)) {
                return value;
            }
        }
        return objectData.get(fieldName);
    }

    /**
     * 单选字段中 其他字段放开后,值永远=other,可以输入内容数据在字段__o中存储
     *
     * @param expObj
     * @param fieldDesc
     * @param formItem
     * @param objectData
     */
    private static void setOtherSelectOneValue(Object expObj, Map<String, Object> fieldDesc, Map<String, Object> formItem, Map<String, Object> objectData, String fieldName) {
        if (null != fieldDesc) {
            String fieldType = (String) fieldDesc.get(ExtensionKey.type);
            if (Objects.nonNull(expObj)) {
                if (ExtensionKey.select_one.equals(fieldType) && ExtensionKey.other.equals(expObj + "")) {
                    String otherKey = fieldName + ExtensionKey.select_suffix;
                    formItem.put(ExtensionKey.value + ExtensionKey.select_suffix, objectData.get(otherKey));
                }

                if (ExtensionKey.select_many.equals(fieldType) && expObj instanceof List) {
                    List<Object> value = (List<Object>) expObj;
                    if (CollectionUtils.isNotEmpty(value) && ExtensionKey.other.equals(value.get(0))) {
                        String otherKey = fieldName + ExtensionKey.select_suffix;
                        formItem.put(ExtensionKey.value + ExtensionKey.select_suffix, objectData.get(otherKey));
                    }
                }

            }
        }
    }

    /**
     * 设置一些描述相关的信息
     *
     * @param formItem
     * @param fieldDesc
     * @param formHandler
     */
    protected static void setFormFieldsExtension(Map<String, Object> formItem,
                                                 Map<String, Object> fieldDesc,
                                                 DataCacheHandler formHandler) {

        if (fieldDesc != null) {
            String fieldType = (String) formItem.get(ExtensionKey.type);
            if (fieldDesc.get(ExtensionKey.options) != null) {
                List<Map<String, Object>> options = (List<Map<String, Object>>) fieldDesc.get(ExtensionKey.options);
                List<Map<String, Object>> availableOptions = options.stream()
                        .filter(m -> !Boolean.TRUE.equals(m.get("not_usable")))
                        .collect(Collectors.toList());
                formItem.put(ExtensionKey.options, availableOptions);
            }
            if (MetadataKey.objectReference.equals(fieldType)) {
                String relatedEntityId = (String) fieldDesc.get(MetadataKey.targetApiName);
                String relatedObjectId = (String) formItem.get(ExtensionKey.value);
                formItem.put(ExtensionKey.relatedEntityId, relatedEntityId);
                formItem.put(MetadataKey.targetDisplayName, fieldDesc.get(MetadataKey
                        .targetDisplayName));
                formItem.put(ExtensionKey.relatedListName, fieldDesc.get(ExtensionKey.relatedListName));
                if (formHandler != null && StringUtils.isNotBlank(relatedObjectId)) {
                    log.debug("getTasksByInstanceIds:entityId:{},objectId:{}", relatedEntityId, relatedObjectId);
                    formItem.put(ExtensionKey.relatedObjectName, formHandler.getData(new Pair<>(relatedEntityId, relatedObjectId)).get(ExtensionKey
                            .name));
                }
            }
//            将自定义字段的描述信息  填充到form的字段上
            fieldDesc.forEach((key, value) -> {
                if (!(key.equals(MetadataKey.apiName) || key.equals(MetadataKey.isRequired) || key.equals(MetadataKey.isReadonly))) {
                    formItem.putIfAbsent(key, value);
                }
            });
        }
    }

    private static List<String> verifyRequiredFields(Map<String, Object> data, Map<String, Object> desc) {
        if (data == null || desc == null) {
            return Lists.newArrayList();
        }

        List<String> fieldLabels = Lists.newLinkedList();

        Map<String, Map<String, Object>> fieldDescs = (Map<String, Map<String, Object>>) desc.get(MetadataKey.fields);
        fieldDescs.forEach((field, fieldDesc) -> {
            //当必填值为空时
            if (Boolean.TRUE.equals(fieldDesc.get(MetadataKey.isRequired)) && Boolean.TRUE.equals(fieldDesc.get(MetadataKey.isActive))) {
                if (countryCascadeFields.contains(field)) {
                    if ("country".equals(field) && Strings.isNullOrEmpty((String) data.get("country"))) {
                        fieldLabels.add(BPMI18N.PAAS_FLOW_BPM_NATIONAL_PROVINCIAL_CITY_AND_DISTRICT.text());
                    }
                } else if (null == data.get(field) || "".equals(data.get(field).toString())) {
                    fieldLabels.add((String) fieldDesc.get(MetadataKey.label));
                }
            }
        });
        return fieldLabels;
    }


    /**
     * 校验任务是否已经完成或取消
     */
    public static  void assertTaskCompleted(Task task) {
        if (Boolean.TRUE.equals(task.getCompleted()) || task.getState() == TaskState.pass) {
            log.warn("completeTask : task is completed. TASK_ID={}, TenantId={}", task.getId(), task.getTenantId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_STATUS_PAAS);
        } else if (TaskState.cancel.equals(task.getState())) {
            log.warn("completeTask : task is canceled. TASK_ID={}, TenantId={}", task.getId(), task.getTenantId());
            throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_STOP_CALL_CRM_SYSTEM);
        }
    }

    /**
     * 校验当前任务是否可以被完成
     * 当前操作人是否有权来完成任务
     *
     * @param context
     */
    public static  void validateCompleteTask(RemoteContext context,Task task) {
        assertTaskCompleted(task);

        //判断当前执行人是否已经执行过
        List<String> assigneeIds = task.getAssigneeIds();
        if (null != assigneeIds) {
            assigneeIds.forEach(e -> {
                if (WorkflowContext.getUserIdWithOutUserId(context).equals(e)) {
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_STATUS_PAAS_BY_USER);
                }
            });
        }

        //获取任务详情,判断是否有执行权限

        if (!task.isExternalApplyTask()) {
            List<String> candidateIds = task.getCandidateIds();
            if(CollectionUtils.isEmpty(candidateIds)){
                //待处理人为空 不允许完成任务
                throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_CANDIDATEIDS_IS_NULL_REJECT_COMPLETE_TASK);
            }
            if (!candidateIds.contains(WorkflowContext.getUserIdWithOutUserId(context))) {
                log.warn("completeTask : no permission to execute task. TASK_ID={}, CONTEXT={}", task.getId(), context);
                throw new BPMPermissionException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_AUTH_COMPLETE);
            }
        }
    }


    /**
     * 校验和将被更换的处理人去重
     * @return
     */
    public static  List<String> validateAndDistinctCandidateIds(Task task, List<String> candidateIds) {
        for (String candidateId : candidateIds) {
            if (!NumberUtils.isCreatable(candidateId)) {
                log.info("更换负责人时,人员id非数值类型:{}", candidateId);
                throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_CHANGE_OWNER_EXPRESSION_ERROR);
            }
        }

        candidateIds = candidateIds.stream().map(userId -> Integer.toString(Double.valueOf(userId).intValue())).distinct().collect(Collectors.toList());

        if (!BPMConstants.APP_ID.equals(task.getAppId())) {
            throw new BPMChangeApproverException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_CHANGE_APPROVER_TYPE_ERROR);
        }
        assertTaskCompleted(task);
        return candidateIds;
    }

    public static boolean isHidden(Map<String, Integer> fieldsAuth, Map<String, Object> item) {
        String fieldName = (String) item.get("name");
        RW rw = MetadataUtils.getFieldAccess(fieldsAuth, fieldName);

        if (rw == RW.Denied) {
            log.debug("current user has no permission with the FIELD={}, should be removed", fieldName);
            return true;
        } else {
            return false;
        }
    }

    /**
     * isUpStreamTenant:false
     * persons:[300052247, 1000, 300052248, 1001, 300052249]
     * sort:[300052247, 300052248, 300052249, 1000, 1002]
     */
    public static void sortPersons(boolean isUpStreamTenant, List<String> persons) {
        if(CollectionUtils.isEmpty(persons)){
            return;
        }
        persons.sort((o1, o2) -> {
            if (String.valueOf(o1).length() < 9 && String.valueOf(o2).length() > 8) {
                return isUpStreamTenant ? -1 : 0;
            } else {
                return isUpStreamTenant ? 0 : -1;
            }
        });
    }

}
