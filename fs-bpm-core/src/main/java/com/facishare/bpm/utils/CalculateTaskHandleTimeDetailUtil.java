package com.facishare.bpm.utils;

import com.facishare.bpm.model.meta.BPMTaskHandleTimeDetailObj;
import com.facishare.bpm.model.paas.engine.bpm.OperateLog;
import com.facishare.bpm.model.paas.engine.bpm.Opinion;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CalculateTaskHandleTimeDetailUtil {

    public static ModifyInfo getTaskHandleTimeDetailModifyInfo(TaskInfo task, String operateId){
        List<BPMTaskHandleTimeDetailObj> updateData = Lists.newArrayList();
        List<BPMTaskHandleTimeDetailObj> addData = Lists.newArrayList();
        List<BPMTaskHandleTimeDetailObj> allData = Lists.newArrayList();
        boolean currentFlag = StringUtils.equals(operateId, task.getId());

        List<String> startPerson = CollectionUtils.isNotEmpty(task.getApproverModifyLog()) ? task.getApproverModifyLog().get(0).getBeforeModifyPersons() : task.getCandidateIds();
        if(CollectionUtils.isNotEmpty(startPerson)){
            if(Boolean.TRUE.equals(task.getSequence())){
                startPerson = Lists.newArrayList(startPerson.get(0));
            }
            allData.addAll(BPMTaskHandleTimeDetailObj.createInsertList(task.getId(), task.getInstanceId(), startPerson, task.getCreateTime(), TransferDataConstants.TASK_HANDLE_TIME_DETAIL_NORMAL, task.getId()));
        }
        if(currentFlag){
            return new ModifyInfo(null, allData, allData);
        }

        List<OperateInfoAbstract> operateList = OperateInfoAbstract.mergeAndSortOperate(task.getApproverModifyLog(), task.getOperateLog(), task.getOpinionLog());
        if(CollectionUtils.isEmpty(operateList)){
            if(TaskState.cancel.name().equals(task.getState())){
                currentFlag = "cancel".equals(operateId);
                addEndTime(allData, task.getModifyTime(), null, currentFlag, updateData);
                if(currentFlag){
                    return new ModifyInfo(updateData, null, allData);
                }
            }
            return new ModifyInfo(null, allData, allData);
        }

        for (OperateInfoAbstract operate : operateList) {
            currentFlag = StringUtils.equals(operateId, operate.getId());
            ModifyInfo modifyInfo = operate.updateModifyData(task, currentFlag, allData, updateData, addData);
            if(currentFlag){
                return Objects.isNull(modifyInfo) ? new ModifyInfo() : modifyInfo;
            }
        }

        if(TaskState.cancel.name().equals(task.getState())){
            currentFlag = "cancel".equals(operateId);
            addEndTime(allData, task.getModifyTime(), null, currentFlag, updateData);
            if(currentFlag){
                return new ModifyInfo(updateData, null, allData);
            }
        }
        return new ModifyInfo(null, allData, allData);
    }

    public static ModifyInfo comparisonAcquisitionUpdateAndAdd(List<BPMTaskHandleTimeDetailObj> original, List<BPMTaskHandleTimeDetailObj> recent){
        List<String> originalIds = CollectionUtils.isNotEmpty(original) ? original.stream().map(t -> t.get_id()).collect(Collectors.toList()) : Lists.newArrayList();
        if(CollectionUtils.isEmpty(recent)){
            return new ModifyInfo(null, null, null, originalIds);
        }
        if(CollectionUtils.isEmpty(original)){
            return new ModifyInfo(null, recent, null, null);
        }
        List<String> removeIds = Lists.newArrayList();
        List<String> recentIds = CollectionUtils.isNotEmpty(recent) ? recent.stream().map(t -> t.get_id()).collect(Collectors.toList()) : Lists.newArrayList();
        for (String id : originalIds) {
            if(!recentIds.contains(id)){
                removeIds.add(id);
            }
        }
        Map<String, BPMTaskHandleTimeDetailObj> originalMap = original.stream().collect(Collectors.toMap(BPMTaskHandleTimeDetailObj::get_id, Function.identity()));
        Map<String, BPMTaskHandleTimeDetailObj> recentMap = recent.stream().collect(Collectors.toMap(BPMTaskHandleTimeDetailObj::get_id, Function.identity()));
        List<BPMTaskHandleTimeDetailObj> update = Lists.newArrayList();
        List<BPMTaskHandleTimeDetailObj> add = Lists.newArrayList();
        for (String key : recentMap.keySet()) {
            BPMTaskHandleTimeDetailObj recentObj = recentMap.get(key);
            if(originalMap.containsKey(key)){
                BPMTaskHandleTimeDetailObj originalObj = originalMap.get(key);
                if(!StringUtils.equals(originalObj.getState(), recentObj.getState())
                        || !Objects.equals(originalObj.getStart_time(), recentObj.getStart_time())
                        || !Objects.equals(originalObj.getEnd_time(), recentObj.getEnd_time())
                        || !CollectionUtils.isEqualCollection(originalObj.getUser_id(), recentObj.getUser_id())){
                    update.add(recentObj);
                }
            }else {
                add.add(recentObj);
            }
        }
        return new ModifyInfo(update, add, null, removeIds);
    }

    private static List<String> getProcessIdsBeforeThisSuspendTime(Long time, List<OpinionOperate> opinions){
        if(CollectionUtils.isEmpty(opinions)){
            return Lists.newArrayList();
        }
        return opinions.stream().filter(o -> o.getTime() < time).map(opinion -> opinion.getUserId()).collect(Collectors.toList());

    }

    private static void addEndTime(List<BPMTaskHandleTimeDetailObj> allData, Long endTime, List<String>excludePerson, boolean currentFlag, List<BPMTaskHandleTimeDetailObj> updateLog){
        if (CollectionUtils.isEmpty(allData)){
            return;
        }
        if(Objects.isNull(excludePerson)){
            excludePerson = Lists.newArrayList();
        }
        for (BPMTaskHandleTimeDetailObj date : allData) {
            if(Objects.isNull(date.getEnd_time()) && !excludePerson.contains(date.getUser_id().get(0))){
                date.setEnd_time(endTime);
                if(currentFlag){
                    updateLog.add(date);
                }
            }
        }
    }

    /**
     * 获取暂停时的人员信息
     * @param suspendTime
     * @param approverModifyLogList
     * @param currentCandidateIds
     * @return
     */
    private static List<String> getCandidateIdsBeforeThisSuspendTime(Long suspendTime, List<ApproverModifyOperate> approverModifyLogList, List<String>currentCandidateIds){
        if(CollectionUtils.isEmpty(approverModifyLogList)){
            return Lists.newArrayList(currentCandidateIds);
        }
        ApproverModifyOperate targetLog = null;
        boolean isBefore = true;
        for (ApproverModifyOperate log : approverModifyLogList) {
            if(log.getTime() < suspendTime){
                targetLog = log;
            }else {
                if(Objects.isNull(targetLog)){
                    isBefore = false;
                    targetLog = log;
                }
                break;
            }
        }
        if(Objects.isNull(targetLog)){
            return Lists.newArrayList(currentCandidateIds);
        }
        return isBefore ? Lists.newArrayList(targetLog.getAfterModifyPersons()) : Lists.newArrayList(targetLog.getBeforeModifyPersons());
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ModifyInfo{
        private List<BPMTaskHandleTimeDetailObj> update;
        private List<BPMTaskHandleTimeDetailObj> add;
        private List<BPMTaskHandleTimeDetailObj> all;
        private List<String> removeIds;


        public ModifyInfo(List<BPMTaskHandleTimeDetailObj> update, List<BPMTaskHandleTimeDetailObj> add, List<BPMTaskHandleTimeDetailObj> all) {
            this.update = update;
            this.add = add;
            this.all = all;
        }

        public void fillActualDuration(RemoteContext context, ManageGroupProxy manageGroupProxy){
            if(CollectionUtils.isNotEmpty(this.update)){
                for (BPMTaskHandleTimeDetailObj obj : this.update) {
                    if(Objects.nonNull(obj)){
                        obj.fillActualDuration(context, manageGroupProxy);
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(this.add)){
                for (BPMTaskHandleTimeDetailObj obj : this.add) {
                    if(Objects.nonNull(obj)){
                        obj.fillActualDuration(context, manageGroupProxy);
                    }
                }
            }
        }
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskInfo{
        private String id;
        private String state;
        private String instanceId;
        private String type;
        private boolean completed;
        private long createTime;
        private long modifyTime;
        private Integer allPassType;
        private List<String> candidateIds;
        private Boolean sequence;
        private List<ApproverModifyOperate> approverModifyLog;
        private List<OperateOperate> operateLog;
        private List<OpinionOperate> opinionLog;

        public static TaskInfo transfer(Task task){
            return new CalculateTaskHandleTimeDetailUtil.TaskInfo(task.getId(), task.getState().name(), task.getWorkflowInstanceId(), task.getTaskType(),
                    task.getCompleted(),
                    task.getCreateTime(),
                    task.getModifyTime(),
                    task.getAllPassType(),
                    task.getCandidateIds(),
                    task.getSequence(),
                    CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate.transfer(task.getApproverModifyLog()),
                    CalculateTaskHandleTimeDetailUtil.OperateOperate.transfer(task.getOperateLogs()),
                    CalculateTaskHandleTimeDetailUtil.OpinionOperate.transfer(task.getOpinions()));
        }
    }

    @Data
    static abstract class OperateInfoAbstract {
        String id;
        long time;

        /**
         * 合并操作 并且排序
         * @param approverModifyLog
         * @param operateLog
         * @param opinionLog
         * @return
         */
        public static List<OperateInfoAbstract> mergeAndSortOperate(List<ApproverModifyOperate> approverModifyLog, List<OperateOperate> operateLog, List<OpinionOperate> opinionLog){
            List<OperateInfoAbstract> result = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(approverModifyLog)){
                result.addAll(approverModifyLog);
            }
            if (CollectionUtils.isNotEmpty(operateLog)){
                result.addAll(operateLog);
            }
            if (CollectionUtils.isNotEmpty(opinionLog)){
                result.addAll(opinionLog);
            }
            Collections.sort(result, (o1, o2) -> Long.compare(o1.getTime(), o2.getTime()));
            return result;
        }

        abstract ModifyInfo updateModifyData(TaskInfo task, boolean currentFlag, List<BPMTaskHandleTimeDetailObj> allData,List<BPMTaskHandleTimeDetailObj> updateData, List<BPMTaskHandleTimeDetailObj> addData);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ApproverModifyOperate extends OperateInfoAbstract {
        private List<String> beforeModifyPersons;
        private List<String> afterModifyPersons;

        public static List<ApproverModifyOperate> transfer(List<Task.ApproverModifyLog> approverModifyLogs){
            return CollectionUtils.isEmpty(approverModifyLogs)
                    ? Collections.EMPTY_LIST
                    : approverModifyLogs.stream().map(log ->
                    BeanUtils.transfer(log, CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate.class, (src, target) -> {
                        target.setTime(src.getModifyTime());
                    })).collect(Collectors.toList());
        }

        @Override
        public ModifyInfo updateModifyData(TaskInfo task, boolean currentFlag, List<BPMTaskHandleTimeDetailObj> allData,List<BPMTaskHandleTimeDetailObj> updateData, List<BPMTaskHandleTimeDetailObj> addData){
            addEndTime(allData, this.getTime(), this.getAfterModifyPersons(), currentFlag, updateData);
            List<BPMTaskHandleTimeDetailObj> addList = Lists.newArrayList();
            if(Objects.nonNull(this.getAfterModifyPersons())){
                List<String> addNormalDetailList = Lists.newArrayList();
                if(Boolean.TRUE.equals(task.getSequence())){
                    addNormalDetailList = Lists.newArrayList(this.getAfterModifyPersons());
                    addNormalDetailList.removeAll(getProcessIdsBeforeThisSuspendTime(this.time, task.getOpinionLog()));
                    if(CollectionUtils.isNotEmpty(addNormalDetailList)){
                        String user = addNormalDetailList.get(0);
                        if(!addData.stream().anyMatch(d -> Objects.isNull(d.getEnd_time()) && user.equals(d.getUser_id().get(0)))){
                            addNormalDetailList = allData.stream().anyMatch(d -> StringUtils.equals(user, d.getUser_id().get(0)) && Objects.isNull(d.getEnd_time())) ? Lists.newArrayList() : Lists.newArrayList(user);
                        }
                    }
                }else {
                    for (String targetCandidateId : this.getAfterModifyPersons()) {
                        if(CollectionUtils.isEmpty(this.getBeforeModifyPersons()) || !this.getBeforeModifyPersons().contains(targetCandidateId)){
                            addNormalDetailList.add(targetCandidateId);
                        }
                    }
                    if("all".equals(task.getType()) && CollectionUtils.isNotEmpty(task.getOpinionLog())){
                        addNormalDetailList.removeAll(getProcessIdsBeforeThisSuspendTime(this.time, task.getOpinionLog()));
                    }
                }
                addList = BPMTaskHandleTimeDetailObj.createInsertList(task.getId(), task.getInstanceId(), addNormalDetailList, this.getTime(), TransferDataConstants.TASK_HANDLE_TIME_DETAIL_NORMAL, this.getId());
                allData.addAll(addList);
            }
            if(currentFlag){
                addData.addAll(addList);
                return new ModifyInfo(updateData, addData, allData);
            }
            return null;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OpinionOperate extends OperateInfoAbstract {
        private String actionType;
        private String userId;

        public static List<OpinionOperate> transfer(List<Opinion> opinions){
            return CollectionUtils.isEmpty(opinions)
                    ? Collections.EMPTY_LIST
                    : opinions.stream().map(log ->
                    BeanUtils.transfer(log, CalculateTaskHandleTimeDetailUtil.OpinionOperate.class, (src, target) -> {
                        target.setTime(src.getReplyTime());
                    })).collect(Collectors.toList());
        }

        @Override
        public ModifyInfo updateModifyData(TaskInfo task, boolean currentFlag, List<BPMTaskHandleTimeDetailObj> allData,List<BPMTaskHandleTimeDetailObj> updateData, List<BPMTaskHandleTimeDetailObj> addData){
            boolean allAndEndTime = true;
            if("all".equals(task.getType()) && Objects.nonNull(task.getAllPassType())){
                if(task.getAllPassType()==1 || (task.getAllPassType()==0 && "agree".equals(this.getActionType()))){
                    allAndEndTime = false;
                }
            }
            if(Boolean.TRUE.equals(task.getSequence())){
                allAndEndTime = false;
            }
            for (BPMTaskHandleTimeDetailObj data : allData) {
                if(Objects.isNull(data.getEnd_time()) && (allAndEndTime || this.getUserId().equals(data.getUser_id().get(0)))){
                    data.setEnd_time(this.getTime());
                    if(currentFlag){
                        updateData.add(data);
                    }
                }
            }

            if(Boolean.TRUE.equals(task.getSequence())){
                List<String> addCandidateIds = getCandidateIdsBeforeThisSuspendTime(this.getTime(), task.getApproverModifyLog(), task.getCandidateIds());
                addCandidateIds.removeAll(getProcessIdsBeforeThisSuspendTime(this.getTime(), task.getOpinionLog()));
                addCandidateIds.remove(this.getUserId());
                if(CollectionUtils.isNotEmpty(addCandidateIds)){
                    List<BPMTaskHandleTimeDetailObj> addList = BPMTaskHandleTimeDetailObj.createInsertList(task.getId(), task.getInstanceId(), Lists.newArrayList(addCandidateIds.get(0)), this.getTime(), TransferDataConstants.TASK_HANDLE_TIME_DETAIL_NORMAL, this.getId());
                    allData.addAll(addList);
                    if(currentFlag){
                        addData.addAll(addList);
                    }
                }
            }

            if(currentFlag){
                return new ModifyInfo(updateData, addData, allData);
            }
            return null;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OperateOperate extends OperateInfoAbstract {
        private String type;

        public static List<OperateOperate> transfer(List<OperateLog> operateLogs){
            return CollectionUtils.isEmpty(operateLogs)
                    ? Collections.EMPTY_LIST
                    : operateLogs.stream().map(log ->
                    BeanUtils.transfer(log, CalculateTaskHandleTimeDetailUtil.OperateOperate.class, (src, target) -> {
                        target.setTime(src.getCreateTime());
                    })).collect(Collectors.toList());
        }

        @Override
        public ModifyInfo updateModifyData(TaskInfo task, boolean currentFlag, List<BPMTaskHandleTimeDetailObj> allData,List<BPMTaskHandleTimeDetailObj> updateData, List<BPMTaskHandleTimeDetailObj> addData){
            addEndTime(allData, this.getTime(), null, currentFlag, updateData);
            List<String> addCandidateIds = getCandidateIdsBeforeThisSuspendTime(this.getTime(), task.getApproverModifyLog(), task.getCandidateIds());
            if("all".equals(task.getType())){
                addCandidateIds.removeAll(getProcessIdsBeforeThisSuspendTime(this.getTime(), task.getOpinionLog()));
            }
            if(Boolean.TRUE.equals(task.getSequence()) && CollectionUtils.isNotEmpty(addCandidateIds)){
                addCandidateIds = Lists.newArrayList(addCandidateIds.get(0));
            }
            if("suspend".equals(this.getType())){
                List<BPMTaskHandleTimeDetailObj> addList = BPMTaskHandleTimeDetailObj.createInsertList(task.getId(), task.getInstanceId(), addCandidateIds, this.getTime(), TransferDataConstants.TASK_HANDLE_TIME_DETAIL_SUSPEND, this.getId());
                allData.addAll(addList);
                if(currentFlag){
                    addData.addAll(addList);
                    return new ModifyInfo(updateData, addData, allData);
                }
            }else if("resume".equals(this.getType()) || "addTagEnd".equals(this.getType())){
                List<BPMTaskHandleTimeDetailObj> addList = BPMTaskHandleTimeDetailObj.createInsertList(task.getId(), task.getInstanceId(), addCandidateIds, this.getTime(), TransferDataConstants.TASK_HANDLE_TIME_DETAIL_NORMAL, this.getId());
                allData.addAll(addList);
                if(currentFlag){
                    addData.addAll(addList);
                    return new ModifyInfo(updateData, addData, allData);
                }
            }else if("addTag".equals(this.getType())){
                if(currentFlag){
                    return new ModifyInfo(updateData, addData, allData);
                }
            }
            return null;
        }
    }
}
