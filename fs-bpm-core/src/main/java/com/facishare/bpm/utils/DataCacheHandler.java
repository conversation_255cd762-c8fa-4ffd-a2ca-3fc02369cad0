package com.facishare.bpm.utils;

import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.utils.model.Pair;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date on 2018/6/14
 * @since 6.3
 */
public interface DataCacheHandler {
    Map<String, Object> getData(Pair<String, String> entityIdObjectId);

    Map<String, Object> getData(String entityId,String objectId);

    Map<String,Map<String,Object>> getDataCache();

    Map<String, Map<String, Object>> getDescribeCache();

    Map<String, Object> getDescribe(String apiName,boolean includeStatistics);

    Map<String,VariableExt> getVariableMap(String workflowInstanceId,String workflowId);

    Map<String,Object> getVariableInstances(String workflowInstanceId);
}
