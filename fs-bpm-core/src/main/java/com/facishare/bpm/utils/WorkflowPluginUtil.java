package com.facishare.bpm.utils;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMDeployException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;

import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/9 1:09 PM
 */
public class WorkflowPluginUtil {

    public static void checkBusinessCode(SupportFlow supportFlow) {
        if (Objects.isNull(supportFlow)) {
            throw new BPMDeployException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, BPMI18N.PAAS_FLOW_BPM_BUSS_CODE_NOT_FOUND.text());
        }
    }
}
