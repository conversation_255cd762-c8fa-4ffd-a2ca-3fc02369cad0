package com.facishare.bpm.utils;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.rest.core.model.RemoteContext;
import org.springframework.util.StringUtils;


/**
 * Created by wangz on 16-12-30.
 */
public class BeanConvertUtil {
    public static WorkflowOutline toWorkflowOutline(WorkflowOutline outline, RemoteContext context) {
        if (outline == null) {
            return null;
        }
        if (StringUtils.isEmpty(outline.getId())) {
            outline.setCreatedBy(context.getUserId());
            outline.setCreateTime(System.currentTimeMillis());
        }

        outline.setLastModifiedBy(context.getUserId());//在创建时也填写上更新人和更新时间
        outline.setLastModifiedTime(System.currentTimeMillis());

        outline.setTenantId(context.getTenantId());
        outline.setUserId(context.getUserId());
        return outline;

    }
}
