package com.facishare.bpm.utils;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.MetadataInstance;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/3/6 8:52 PM
 */
public class MetadataQuery {

    public static final int LIMIT = 10000;
    private static final String SOUTCE_WORKFLOW_ID = "sourceWorkflowId";
    private static final String STATE = "state";
    private static final String IN_PROGRESS = "in_progress";
    private static final String ACTIVITY_ID = "activityId";
    private static final String LAST_MODIFIED_TIME = "last_modified_time";
    private static final String BPM_INSTANCE = "BpmInstance";


    public static PageResult<MetadataInstance> getInstancesByObjectId(RefServiceManager serviceManager, String objectId, String sourceWorkflowId, InstanceState state, int pageNumber, int pageSize) {
        List<MetadataInstance> workflowInstances;
        PageResult<MetadataInstance> pageResult = new PageResult<>();

        if (Strings.isNullOrEmpty(objectId) && Strings.isNullOrEmpty(sourceWorkflowId)) {
            return pageResult;
        }

        SearchTemplateQuery instanceByObjectId = getInstanceByObjectId(serviceManager, objectId, sourceWorkflowId, state, pageNumber, pageSize);
        if (Objects.isNull(instanceByObjectId)) {
            return pageResult;
        }
        FindDataBySearchTemplate.Result instanceResult = serviceManager.findDataBySearchTemplate(BPM_INSTANCE, instanceByObjectId);
        FindDataBySearchTemplate.InnerResult instanceInnerResult = instanceResult.getData();
        if (Objects.nonNull(instanceInnerResult) && Objects.nonNull(instanceInnerResult.getQueryResult())) {
            List<Map<String, Object>> data = instanceInnerResult.getQueryResult().getData();
            workflowInstances = JacksonUtil.fromJson(JacksonUtil.toJson(data), new TypeReference<List<MetadataInstance>>() {
            });

            pageResult.setTotal(instanceInnerResult.getQueryResult().getTotalNumber());
            pageResult.setDataList(workflowInstances);
        }

        return pageResult;
    }


    /**
     * 查询进行中和异常的数据信息
     *
     * @param sourceWorkflowId
     * @return
     */
    public static SearchTemplateQuery getInProgressOrActivityIdTasks(
            String sourceWorkflowId,
            List<String> activityIds,
            Page page) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        Filter sourceWorkflowIdFilter = new Filter();
        sourceWorkflowIdFilter.setFieldName(SOUTCE_WORKFLOW_ID);
        sourceWorkflowIdFilter.setOperator(Operator.EQ);
        sourceWorkflowIdFilter.setFieldValues(Lists.newArrayList(sourceWorkflowId));


        Filter stateFilter = new Filter();
        stateFilter.setFieldName(STATE);
        stateFilter.setOperator(Operator.IN);
        stateFilter.setFieldValues(Lists.newArrayList(IN_PROGRESS));


        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(ACTIVITY_ID);
        activityIdFilter.setFieldValues(activityIds);
        activityIdFilter.setOperator(Operator.IN);


        query.setOrders(Lists.newArrayList(new OrderBy(LAST_MODIFIED_TIME, false)));
        query.setFilters(Lists.newArrayList(sourceWorkflowIdFilter, stateFilter, activityIdFilter));
        query.setOffset(getStartOffset(page.getPageNumber(), page.getPageSize()));
        query.setLimit(page.getPageSize());
        return query;
    }


    public static SearchTemplateQuery getNormalTasks(
            String sourceWorkflowId,
            List<String> activityIds,
            Page page) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        Filter sourceWorkflowIdFilter = new Filter();
        sourceWorkflowIdFilter.setFieldName(SOUTCE_WORKFLOW_ID);
        sourceWorkflowIdFilter.setOperator(Operator.EQ);
        sourceWorkflowIdFilter.setFieldValues(Lists.newArrayList(sourceWorkflowId));


        Filter stateFilter = new Filter();
        stateFilter.setFieldName(STATE);
        stateFilter.setOperator(Operator.IN);
        stateFilter.setFieldValues(Lists.newArrayList(IN_PROGRESS));


        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(ACTIVITY_ID);
        activityIdFilter.setFieldValues(activityIds);
        activityIdFilter.setOperator(Operator.IN);


        Filter timeOutFilter = new Filter();
        timeOutFilter.setFieldName("isTimeout");
        timeOutFilter.setOperator(Operator.EQ);
        timeOutFilter.setFieldValues(Lists.newArrayList("false"));

        query.setOrders(Lists.newArrayList(new OrderBy(LAST_MODIFIED_TIME, false)));
        query.setFilters(Lists.newArrayList(sourceWorkflowIdFilter, stateFilter, activityIdFilter, timeOutFilter));
        query.setOffset(getStartOffset(page.getPageNumber(), page.getPageSize()));
        query.setLimit(page.getPageSize());
        return query;
    }


    private static SearchTemplateQuery getInstanceByObjectId(RefServiceManager serviceManager, String objectId, String sourceWorkflowId, InstanceState state, int pageNumber, int pageSize) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        List<IFilter> filters = Lists.newArrayList();

        if (!Strings.isNullOrEmpty(objectId)) {
            Filter objectIdFilter = new Filter();
            objectIdFilter.setFieldName(TransferDataConstants.MDField.objectId.getValue());
            objectIdFilter.setOperator(Operator.EQ);
            objectIdFilter.setFieldValues(Lists.newArrayList(objectId));
            filters.add(objectIdFilter);
        }

        if (!Strings.isNullOrEmpty(sourceWorkflowId)) {
            Filter sourceWorkflowIdFilter = new Filter();
            sourceWorkflowIdFilter.setFieldName(TransferDataConstants.MDField.sourceWorkflowId.getValue());
            sourceWorkflowIdFilter.setOperator(Operator.EQ);
            sourceWorkflowIdFilter.setFieldValues(Lists.newArrayList(sourceWorkflowId));
            filters.add(sourceWorkflowIdFilter);
        }

        if (Objects.nonNull(state)) {
            Filter stateFilter = new Filter();
            stateFilter.setFieldName(TransferDataConstants.MDField.state.getValue());
            stateFilter.setOperator(Operator.EQ);
            stateFilter.setFieldValues(Lists.newArrayList(state.name()));
            filters.add(stateFilter);
        }

        if (CollectionUtils.isEmpty(filters)) {
            return null;
        }

        query.setFilters(filters);
        query.setOffset(getStartOffset(pageNumber, pageSize));
        query.setLimit(pageNumber * pageSize);
        OrderBy orderBy = new OrderBy(TransferDataConstants.MDInstanceField.start.getValue(), false);
        query.setOrders(Lists.newArrayList(orderBy));
        /**
         * 0:不走权限
         * 1:走上游权限
         * 2:下游权限
         */
        if (serviceManager.isOuterUserId()) {
            query.setPermissionType(2);
        } else {
            if(SwitchConfigManager.instanceListSkipDataPrivelige(serviceManager.getTenantId())){
                query.setPermissionType(0);
            }else{
                query.setPermissionType(1);
            }

        }

        /**
         * @赵琚 提供的固定模板
         */
        IDataRightsParameter dataRightsParameter = new DataRightsParameter();
        dataRightsParameter.setRoleType("1");
        dataRightsParameter.setSceneType("all");
        dataRightsParameter.setCascadeDept(true);
        dataRightsParameter.setCascadeSubordinates(true);
        dataRightsParameter.setIsDetailObject(false);
        query.setDataRightsParameter(dataRightsParameter);
        return query;
    }


    /**
     * 获取起始位置
     *
     * @param currentPage 底几页,从0开始
     * @return
     */
    public static int getStartOffset(int currentPage, int pageSize) {
        //起始索引=（当前页数-1）*每页显示的条数
        return (currentPage - 1) * pageSize;
    }

    /**
     * 获取总页数
     *
     * @param total    总条数据
     * @param pageSize 每页多少条
     * @return
     */
    public static int getPageTotal(int total, int pageSize) {
        return (total + pageSize - 1) / pageSize;
    }

}
