package com.facishare.bpm.utils;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

public class DescribeUtil {

    public static void replaceDescFieldLabelByFieldMapping(RefServiceManager serviceManager, Map<String, Object> describe, String recordType) {
        Map<String, Object> fieldMapping = serviceManager.findRecordFieldMapping((String) describe.get(BPMConstants.MetadataKey.apiName), recordType);
        if (MapUtils.isNotEmpty(describe)) {
            Map<String, Map<String, Object>> fields = (Map<String, Map<String, Object>>) describe.get(BPMConstants.MetadataKey.fields);
            fields.forEach((fieldName, desc) -> {
                if (fieldMapping.containsKey(fieldName)) {
                    desc.put(BPMConstants.MetadataKey.label, ((Map<String, String>) fieldMapping.get(fieldName)).get(BPMConstants.MetadataKey.label));
                }
            });
        }
    }
}
