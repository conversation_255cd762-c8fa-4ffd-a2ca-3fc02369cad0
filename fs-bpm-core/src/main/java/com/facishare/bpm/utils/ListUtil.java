package com.facishare.bpm.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * Created by wangzhx on 2019/7/19.
 */
public class ListUtil {
    /**
     * @param source [1,2,3,4]
     * @param n      2
     * @return [[1, 2], [3, 4]]
     */
    public static <T> List<List<T>> getSubList(List<T> source, int n) {
        if (null == source || source.size() == 0 || n <= 0) {
            return null;
        }
        List<List<T>> result = Lists.newArrayList();

        int sourceSize = source.size();
        int size = (source.size() / n) + 1;
        for (int i = 0; i < size; i++) {
            List<T> subset = Lists.newArrayList();
            for (int j = i * n; j < (i + 1) * n; j++) {
                if (j < sourceSize) {
                    subset.add(source.get(j));
                }
            }
            result.add(subset);
        }
        return result;
    }

    /**
     * @param source [1,2,3,4,5,6]
     * @param n      3
     * @return [[1, 2], [3, 4], [5, 6]]
     */
    public static <T> List<List<T>> getAverageList(List<T> source, int n) {
        List<List<T>> result = Lists.newArrayList();
        int remainder = source.size() % n;
        int number = source.size() / n;
        int offset = 0;//偏移量
        for (int i = 0; i < n; i++) {
            List<T> value;
            if (remainder > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remainder--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;
    }

    public static <T> Set<T> combineSubList(List<List<T>> source) {
        Set<T> set = Sets.newHashSet();
        if (CollectionUtils.isEmpty(source)) {
            return set;
        }
        source.forEach(set::addAll);
        return set;
    }

}
