package com.facishare.bpm.utils;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.util.verifiy.TaskType;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * Created by wangzhx on 2019/11/27.
 */
@UtilityClass
public class ButtonFunctionUtil {

    /**
     * 【上游】:
     * 更换处理人功能权限 + 任务进行中或者异常(非后动作异常)  下发更换负责人按钮
     *
     * 【下游】:
     * 更换处理人功能权限 && 任务进行中 && 非后动作异常 && 互联节点
     */
    //TODO cr进行到此处
    public boolean isNeedChangeBPMApprover(boolean hasChangeBPMApproverPrivilege,
                                           boolean isInProgressTaskState,
                                           boolean isErrorTaskState,
                                           boolean isExecutionState,
                                           boolean isLinkAppNode,
                                           boolean isUpstreamTenant,
                                           boolean isSupportTaskType) {

        //支持的节点
        if (isSupportTaskType) {
            //【上游】
            if (isUpstreamTenant) {
                //更换处理人功能权限+任务进行中或者异常+后动作非异常
                if (hasChangeBPMApproverPrivilege && (isInProgressTaskState || isErrorTaskState) && !isExecutionState) {
                    return true;
                }
                //【下游】
            } else {
                //更换处理人功能权限+任务进行中+后动作非异常+互联节点
                if (hasChangeBPMApproverPrivilege && isInProgressTaskState && !isExecutionState && isLinkAppNode) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 终止按钮
     *
     * @param iStarting          任务进行中或异常
     */
    public boolean isNeedStopBPM(boolean iStarting, boolean hasStopBpmPrivilege) {
        return iStarting && hasStopBpmPrivilege;
    }

    public boolean isNeedAddTag(String addTagUser, Task task){
        //对任务是否可以加签判断
        if(StringUtils.isBlank(addTagUser) || Objects.isNull(task)){
            return false;
        }
        if(!ExecutionTypeEnum.approve.equals(task.getExecutionType()) || !TaskType.anyone.name().equals(task.getTaskType())
                || task.getCompleted()
                || task.isWaiting()
                || !TaskState.in_progress.equals(task.getState())
                || BPMConstants.TagType.TAG.equals(task.getNodeType())){
            return false;
        }
        //对人员判断
        if(CollectionUtils.isEmpty(task.getCandidateIds()) || !task.getCandidateIds().contains(addTagUser)){
           return false;
        }
        return true;
    }

    public boolean canRemindTask(RefServiceManager serviceManager, Task task){
        if(Objects.isNull(task)){
            return false;
        }
        if(TaskState.in_progress.equals(task.getState())
                && CollectionUtils.isNotEmpty(task.getCandidateIds())
                && (serviceManager.isAdmin() || serviceManager.getUserId().equals(task.getApplicantId()))){
            return true;
        }
        return false;
    }
}
