package com.facishare.bpm.utils;

import com.facishare.bpm.define.conf.DefineConfigHelper;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.remote.model.config.BPMObjectSupportConfig;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.text.Collator;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc: 对象排序工具   老对象->预设自定义对象->自定义对象
 * <p>
 * 1. 老对象  按照字母顺序排序
 * 2. 预设自定义对象  按照字母顺序排序
 * 3. 自定义对象   按照字母顺序排序
 * version: 6.5
 * Created by cuiyongxu on 2019/1/21 4:09 PM
 */
@Slf4j
public class ObjectDescribeListSortUtil {


    public static List<Map<String, Object>> sort(String tenantId,BPMObjectSupportConfig objectSupports, List<Map<String, Object>> lists) {

        List<Map<String, Object>> rst = Lists.newArrayList();

        try {
            if (CollectionUtils.isEmpty(lists)) {
                return lists;
            }
            //sfa的对象
            Set<String> sfaEntityList = objectSupports.getObjectBaseConfig().keySet();
            //预设自定义对象
            Set<String> presetCustomEntityList = Sets.newHashSet(objectSupports.getWhiteList());

            List<Map<String, Object>> sfaMap = Lists.newArrayList();
            List<Map<String, Object>> presetCustomMap = Lists.newArrayList();
            List<Map<String, Object>> customMap = Lists.newArrayList();

            lists.forEach(list -> {
                String apiName = (String) list.get(BPMConstants.MetadataKey.apiName);
                if (CollectionUtils.isNotEmpty(sfaEntityList) && sfaEntityList.contains(apiName)) {
                    sfaMap.add(list);
                } else if ((CollectionUtils.isNotEmpty(presetCustomEntityList) && presetCustomEntityList.contains(apiName)) || DefineConfigHelper.getObjectSupportByType(tenantId,FlowType.workflow_bpm).contains(apiName)) {
                    presetCustomMap.add(list);
                } else {
                    customMap.add(list);
                }
            });


            sfaMap.sort(new AlphabeticalComparator());
            presetCustomMap.sort(new AlphabeticalComparator());

            rst.addAll(sfaMap);
            rst.addAll(presetCustomMap);
            rst.addAll(customMap);
        } catch (Exception e) {
            //不能影响正常业务逻辑,如果异常,先忽略
            log.error("排序失败,请检查:{},{}", JacksonUtil.toJson(objectSupports), JacksonUtil.toJson(lists));
            return lists;
        }


        return rst;
    }
}

class AlphabeticalComparator implements Comparator<Map> {
    private Collator collator = Collator.getInstance(java.util.Locale.CHINA);

    @Override
    public int compare(Map o1, Map o2) {
        String before = (String) o1.get(BPMConstants.MetadataKey.apiName);
        String after = (String) o2.get(BPMConstants.MetadataKey.apiName);
        //为空需要处理,防止报空指针
        if (Strings.isNullOrEmpty(after) || Strings.isNullOrEmpty(before)) {
            return -1;
        }
        if (collator.compare(before, after) > 0) {
            return 1;
        } else {
            return -1;
        }
    }
}
