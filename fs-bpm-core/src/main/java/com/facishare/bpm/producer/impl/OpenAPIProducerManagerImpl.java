package com.facishare.bpm.producer.impl;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.producer.OpenAPIProducerManager;
import com.facishare.bpm.producer.model.BPMEventBean;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.common.rocketmq.AutoConfRocketMQSender;
import com.facishare.common.rocketmq.util.TraceUtils;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 5.7
 */
@Slf4j
@Data
public class OpenAPIProducerManagerImpl implements OpenAPIProducerManager {
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;
    @Autowired
    private BPMDefinitionService definitionService;

    private AutoConfRocketMQSender producer;

    private String configName;

    @PostConstruct
    public void init() {
        log.info("load producer config");
        this.producer = new AutoConfRocketMQSender(configName);
        this.producer.init();
    }

    @Override
    public void send(BPMEventBean eventBean,String tenantId) {
        MessageExt message = new MessageExt();
        message.setTags(eventBean.getTag());
        message.putUserProperty("x-fs-ei", tenantId);
        message.putUserProperty("x-trace-id", TraceContext.get().getTraceId());
        message.setBody(JsonUtil.toJsonWithNull(eventBean).getBytes());
        message.setTopic(producer.getTopic());
        log.info("topic:{},context:{},tag:{}", producer.getTopic(), JsonUtil.toJsonWithNull(eventBean), eventBean.getTag());
        try {
            TraceUtils.setProducerTrace(message);
            SendResult result = producer.getSender().send(message, (mqs, msg1, arg) -> {
                int index = mqs.size();
                if (arg.hashCode() != 0) {
                    index = Math.abs(arg.hashCode()) % mqs.size();
                }
                return mqs.get(index);
            }, eventBean.getId());
            SendStatus status = result.getSendStatus();
            if (status.equals(SendStatus.SEND_OK)) {
                log.info("send_bpm_event to other app:{}.msgId:{}", JsonUtil.toJsonWithNull(eventBean),result.getMsgId());
            } else {
                log.error("msgId={}, status={}", result.getMsgId(), status);
            }
        } catch (Exception e) {
            log.error("SendError,message={}, e = ", message, e);
        }
    }


    /**
     * 发送自动节点和定时节点的mq消息
     *
     * @param task
     */
    @Override
    public void sendLatencyEvent(int flag, Task task,String subType) {
        String tenantId = task.getTenantId();
        task.setId(BPMConstants.LATENCY_SUFFIX + task.getId());

        RemoteContext context = new RemoteContext(null, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        WorkflowExtension extension = definitionService.getWorkflowExtensionByWorkflowId(context, task.getWorkflowId());

        List<PoolEntity> pools = Objects.isNull(extension) ? Lists.newArrayList() : extension.getPools();

        LaneEntity lane = PoolEntity.getLane(pools, task.getActivityId());

        BPMEventBean eventData = BPMEventBean.builder()
                .data(BPMEventBean.TaskLatencyData.builder()
                        .instanceId(task.getWorkflowInstanceId())
                        .laneName(lane.getName())
                        .laneId(lane.getId())
                        .workflowId(task.getWorkflowId())
                        .sourceWorkflowId(task.getSourceWorkflowId())
                        .activityId(task.getActivityId())
                        .taskType(task.getTaskType())
                        .executionType(BPMTask.getExecutionTypeByLatencyTask(task.getBpmExtension()))
                        .activityInstanceId(task.getActivityInstanceId())
                        .tenantId(tenantId).taskId(task.getId()).taskName(task.getName())
                        .workflowName(task.getWorkflowName())
                        .entityId(task.getEntityId())
                        .objectId(task.getObjectId())
                        .flag(flag)
                        .bpmExtension(task.getBpmExtension())
                        .taskState(task.getState()).build()).subType(subType)
                .tag("task_change").id(task.getId()).build();
        send(eventData, tenantId);
    }

    @Override
    public void sendAppEvent(RemoteContext context,
                             String instanceId,
                             String taskId,
                             String appCode,
                             String actionCode,
                             String entityId,
                             String objectId,
                             String workflowName,
                             String taskName,
                             Long startTime,
                             String childrenActionCode,
                             String childrenActionName,
                             String sourceWorkflowId,
                             String activityId,
                             String subType,String laneId,String laneName) {
        BPMEventBean eventBean = BPMEventBean.builder().data(BPMEventBean.AppEventData.builder().actionCode(actionCode)
                .appCode(appCode)
                .tenantId(context.getTenantId())
                .instanceId(instanceId)
                .entityId(entityId)
                .objectId(objectId)
                .workflowName(workflowName)
                .taskName(taskName).laneId(laneId).laneName(laneName)
                .childrenActionCode(childrenActionCode)
                .childrenActionName(childrenActionName)
                .sourceWorkflowId(sourceWorkflowId)
                .taskId(taskId)
                .activityId(activityId).build()).id(instanceId).tag("app_notify").subType(subType).build();
        send(eventBean, context.getTenantId());
    }

    @Override
    public void sendDefinitionChange(RefServiceManager serviceManager, String id, String workflowName, String sourceWorkflowId, String entityId, boolean enable, boolean delete) {
        BPMEventBean eventBean = BPMEventBean.builder().data(BPMEventBean.DefinitionEventData.builder().tenantId(serviceManager.getTenantId())
                .id(id)
                .sourceWorkflowId(sourceWorkflowId)
                .workflowName(workflowName)
                .entityId(entityId)
                .enable(enable).delete(delete).build()).id(id).tag("definition_change").subType(delete?"delete":"change").build();
        send(eventBean, serviceManager.getTenantId());
    }

}
