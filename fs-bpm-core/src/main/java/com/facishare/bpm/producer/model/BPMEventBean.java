package com.facishare.bpm.producer.model;

import com.facishare.bpm.model.paas.engine.bpm.*;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 5.7
 */
@Builder
@Data
public class BPMEventBean {
    private String tag;
    private String subType;
    private EventData data;
    private String id;
    @Data
    public static class EventData {
    }

    @Builder
    public static class InstanceData extends EventData {
        private InstanceState state;//error complete in_progress
        private String instanceId;
        private String workflowId;
        private String sourceWorkflowId;
        private String workflowName;
        private String objectId;
        private String entityId;
        private String tenantId;
        private Integer externalFlow;
        private String applicantId;
    }

    @Builder
    public static class TaskInstanceData extends EventData {
        private TaskState taskState;//error complete in_progress
        private boolean timeout;
        private String taskId;
        private String taskName;
        private String laneName;
        private String laneId;
        private String objectId;
        private String entityId;

        private String instanceId;
        private String workflowId;
        private String sourceWorkflowId;
        private String workflowName;
        private String tenantId;
        private String activityId;
        private String taskType;
        private String executionType;
        private int activityInstanceId;
        private List<String> candidateIds;
        private List<Opinion> opinions;
        private Object appDetail;

        private String childrenActionCode;
        private String childrenActionName;
        
        private Long suspendAccumulateDuration;
        private String elementApiName;
        private List<OperateLog> operateLogs;
    }

    @Builder
    public static class TaskLatencyData extends EventData {
        private String instanceId;
        private String laneName;
        private String laneId;
        private String workflowId;
        private String sourceWorkflowId;//目前空,需要引擎补充
        private String activityId;
        private String taskType;//空,需咨询引擎是否可补充
        private String executionType;//空,需咨询引擎是否可补充
        private int activityInstanceId;
        private String tenantId;
        private String workflowName;//空,需咨询引擎是否可补充
        private String entityId;//目前空,需要引擎补充
        private String objectId;
        private TaskState taskState;
        private String taskName;//空,需咨询引擎是否可补充
        private String taskId;
        private NodeType nodeType;
        private Map<String, Object> bpmExtension;
        private int flag;//flag=1 节点生成，flag=2 节点通过
    }

    @Builder
    public static class AppEventData extends EventData {
        private String tenantId;
        private String instanceId;
        private String taskId;
        private String appCode;
        private String sourceWorkflowId;
        private String actionCode;
        private String objectId;
        private String entityId;
        private String taskName;
        private String workflowName;
        private String childrenActionCode;
        private String childrenActionName;
        private String activityId;
        /**
         * 所属阶段及id
         */
        private String laneName;
        private String laneId;
    }

    @Builder
    public static class DefinitionEventData extends EventData {
        private String tenantId;
        private String id;
        private String sourceWorkflowId;
        private String workflowName;
        private String entityId;
        private boolean delete;
        private boolean enable;
    }
}
