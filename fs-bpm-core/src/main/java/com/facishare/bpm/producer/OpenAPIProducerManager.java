package com.facishare.bpm.producer;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.producer.model.BPMEventBean;
import com.facishare.rest.core.model.RemoteContext;
/**
 * <AUTHOR>
 * @since 5.7
 */
public interface OpenAPIProducerManager {

    void send(BPMEventBean eventBean, String tenantId);
    void sendLatencyEvent(int flag, Task task,String subType);

    void sendAppEvent(RemoteContext context,
                      String instanceId,
                      String taskId,
                      String appCode,
                      String actionCode,
                      String entityId,
                      String objectId,
                      String workflowName,
                      String taskName,
                      Long startTime,
                      String childrenActionCode,
                      String childrenActionName,
                      String sourceWorkflowId, String activityId,String subType,String laneId,String laneName);

    void sendDefinitionChange(RefServiceManager serviceManager, String id, String workflowName, String sourceWorkflowId, String entityId, boolean enable, boolean delete);
}
