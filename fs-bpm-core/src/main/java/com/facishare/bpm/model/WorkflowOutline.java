package com.facishare.bpm.model;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.bpmn.WorkflowRule;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.util.WorkflowJsonUtil;
import com.facishare.bpm.utils.IDCreatorUtil;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.CustomI18NHandler;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTemplateEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.beans.Transient;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MODIFY_RECORD;

/**
 * Created by Aaron on 22/12/2016.
 */
@Slf4j
@Data
public class WorkflowOutline implements Serializable {

    @DocDescribe(label = "outlineId",desc = "业务流程自存库数据id")
    private String id;

    @DocDescribe(label = "企业Id")
    private String tenantId;

    @DocDescribe(label = "用户id")
    private String userId;

    @DocDescribe(label = "流程定义唯一标识")
    private String sourceWorkflowId; //关联workflow soureid

    @DocDescribe(label = "流程定义版本id")
    private String workflowId;

    @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
    @DocDescribe(label = "流程名",desc = "流程名称不允许重复")
    private String name; //

    @DocDescribe(label = "启动次数")
    private int count; //

    @DocDescribe(label = "是否激活")
    private boolean enabled; //是否激活

    @DocDescribe(label = "定义描述")
    private String description;

    @DocDescribe(label = "入口对象类型")
    private String entryType; //

    @DocDescribe(label = "入口对象名称")
    private String entryTypeName; //

    @DocDescribe(label = "可见人范围")
    private List<Integer> rangeEmployeeIds;//

    @DocDescribe(label = "可见部门范围")
    private List<Integer> rangeCircleIds;//

    @DocDescribe(label = "可见组范围")
    private List<String> rangeGroupIds; //

    @DocDescribe(label = "角色范围")
    private List<String> rangeRoleIds;//

    @DocDescribe(label = "创建人")
    private String createdBy;

    @DocDescribe(label = "创建时间")
    private long createTime;

    @DocDescribe(label = "更新人")
    private String lastModifiedBy;

    @DocDescribe(label = "更新时间")
    private long lastModifiedTime;

    @I18NExpression(custom= CustomI18NHandler.outlineWorkflow)
    @DocDescribe(label = "存储的是引擎的定义结构")
    private ExecutableWorkflowExt workflow; //当存储草稿时，workflow的json字符串

    @DocDescribe(label = "触发规则")
    private WorkflowRule rule; //触发规则

    @DocDescribe(label = "保存定义的svg文件")
    private String svg;

    @DocDescribe(label = "保存扩展信息,泳道泳池")
    @I18NExpression(drill = true)
    private WorkflowExtension extension;//草稿时，扩展数据的字符串，包括pools\diagram等

    @DocDescribe(label = "模板id")
    private String templateId;//
    /**
     * 是外部流程1
     * 不是外部流程 0或空
     */
    @DocDescribe(label = "外部流程:1,非外部流程:0或空")
    private Integer externalFlow;
    /**
     * 是否为单实例流程 1 是
     * 0或空  不是
     */
    @DocDescribe(label = "单实例流程:1,非单实例流程:0或空")
    private Integer singleInstanceFlow;

    @DocDescribe(label = "删除状态")
    private boolean isDeleted;

    @DocDescribe(label = "流程定义是否被启用,更新的时候必填字段")
    private boolean hasInstance;

    /**
     * 草稿id,保存草稿->保存定义时需要知道是哪个草稿专的定义,要删除
     */
    @DocDescribe(label = "草稿id",desc = "草稿id,保存草稿->保存定义时需要知道是哪个草稿专的定义,要删除")
    private String draftId;

    //流程扩展类型  6.6添加 营销流程:1
    @DocDescribe(label = "扩展业务流程标识,目前支持营销流程:1，简易流程:2")
    private SupportFlow supportFlow;

    @DocDescribe(label = "是否启用互联")
    private Boolean linkAppEnable;

    @DocDescribe(label = "互联应用id")
    private String linkApp;

    @DocDescribe(label = "互联应用名称，如PRM")
    private String linkAppName;

    @DocDescribe(label = "互联应用类型，1:企业互联，2:客户互联")
    private Integer linkAppType;
    @DocDescribe(label = "管控状态", desc = "880新增，controlled——受管控，uncontrolled或null——不受管控")
    private String controlStatus;
    @DocDescribe(label = "流程发起人为系统时的指定发起人", desc = "910新增，不为空时applicantId为-10000时，解析指定发起人作为发起人")
    private Map<String, Set<String>> applicantWhenSystemUser;
    @DocDescribe(label = "名称多语翻译")
    private Map<String, String> nameTranslateInfo;
    @DocDescribe(label = "描述多语翻译")
    private Map<String, String> descTranslateInfo;

    public static WorkflowTemplateEntity toTemplateEntity(WorkflowOutline workflow) {
        return BeanUtils.transfer(workflow, WorkflowTemplateEntity.class, (src, ret) -> {
            ret.setExtensionJson(WorkflowOutline.convertWorkflowExtensionToJson(workflow.getExtension()));
            ret.setWorkflowJson(JsonUtil.toJson(workflow.getWorkflow()));
        });
    }
    public static FlowExtensionEntity fromWorkflowOutLine(WorkflowOutline outline){
        FlowExtensionEntity extension = new FlowExtensionEntity();
        extension.setWorkflowId(outline.getWorkflowId());
        extension.setDiagram(outline.getExtension().getDiagram());
        extension.setPools(outline.getExtension().getPools());
        extension.setSvg(outline.getExtension().getSvg());
        extension.setTenantId(outline.getTenantId());
        return extension;
    }
    public static WorkflowOutline fromEntity(WorkflowOutlineEntity entity) {
        if (entity == null) {
            return null;
        }

        return BeanUtils.transfer(entity, WorkflowOutline.class, (src, ret) -> {
            ret.setExtension(WorkflowOutline.convertWorkflowExtensionFromJson(src.getExtensionJson()));
            ret.setWorkflow(putExt(JacksonUtil.fromJson(src.getWorkflowJson(), ExecutableWorkflowExt.class)));
            ret.setRule(JsonUtil.fromJson(src.getRuleJson(), WorkflowRule.class));
            if(Objects.nonNull(src.getSupportFlowType())){
                ret.setSupportFlow(SupportFlow.getByCode(src.getSupportFlowType()));
            }
        });
    }

    /**
     * 如果存在扩展信息,则需要再此添加,对push到前端的json处理
     * TODO 前端checkbox 未选中,但是范围中有人 需要处理下
     * TODO 让前端传递一下,存库
     * @param executableWorkflowExt
     * @return
     */
    private static ExecutableWorkflowExt putExt(ExecutableWorkflowExt executableWorkflowExt) {
        List<ActivityExt> activityExts = executableWorkflowExt.getActivities();
        activityExts.forEach(activityExt -> {
            String taskType = activityExt.getType();
            if (BPMConstants.userTask.equals(taskType)) {
                Object assignNextTaskObj = activityExt.get(BPMConstants.assignNextTask);
                Integer assignNextTask = 0;
                if (assignNextTaskObj instanceof Integer) {
                    assignNextTask = (Integer) assignNextTaskObj;
                }else if (assignNextTaskObj instanceof Double) {
                    assignNextTask = (new Double((Double) assignNextTaskObj)).intValue();
                }

                //如果assignNextTask不为空,并且是需要指定下一节点处理人的
                if (assignNextTask == 1) {
                    //需要填充一个hasTaskAssignee, 告诉前端需要对其进行校验,这个校验后端没有加
                    /**
                     * {
                     * dept:[
                     * ],
                     * group:[
                     * ],
                     * person:[
                     * ]
                     * }
                     */
                    Map<String, List<String>> nextTaskAssigneeScope = (Map<String, List<String>>) activityExt.get(BPMConstants.nextTaskAssigneeScope);
                    if (MapUtils.isNotEmpty(nextTaskAssigneeScope)) {
                        boolean hasTaskAssignee = nextTaskAssigneeScope.values().stream().anyMatch(value -> value.size() > 0);
                        if (hasTaskAssignee) {
                            //表示需要校验指定范围中的人
                            activityExt.put(BPMConstants.hasTaskAssignee, true);
                        } else {
                            activityExt.put(BPMConstants.hasTaskAssignee, false);
                        }
                    }
                }
            }
        });
        return executableWorkflowExt;
    }


    public static WorkflowOutline fromEntity(WorkflowOutlineEntity entity, ExecutableWorkflowExt workflow, WorkflowRule rule) {
        if (entity == null) {
            return null;
        }
        return BeanUtils.transfer(entity, WorkflowOutline.class, (src, ret) -> {
            ret.setExtension(WorkflowOutline.convertWorkflowExtensionFromJson(src.getExtensionJson()));
            ret.setWorkflow(putExt(workflow));
            ret.setRule(rule);
        });
    }


    public static WorkflowOutline fromBrieflyEntity(WorkflowOutlineEntity entity) {
        if (entity == null) {
            return null;
        }
        WorkflowOutline workflowOutline = BeanUtils.transfer(entity, WorkflowOutline.class, (src, ret) -> {
            if (!Strings.isNullOrEmpty(src.getRuleJson())) {
                ret.setRule(JsonUtil.fromJson(src.getRuleJson(), WorkflowRule.class));
            }
            try {
                if (!Strings.isNullOrEmpty(src.getExtensionJson())) {
                    ret.setSvg(WorkflowOutline.convertWorkflowExtensionFromJson(src.getExtensionJson()).getSvg());
                }
            } catch (Exception e) {
                log.error("流程定义缺少流程不存在svg:{},{}", entity.getClass().getName(), entity.getId(), e);
            }
        });
        return workflowOutline;
    }


    public static List<WorkflowOutline> fromBrieflyEntities(List<WorkflowOutlineEntity> outlineEntities) {
        List<WorkflowOutline> outlines = Lists.newArrayList();
        if (outlineEntities != null) {
            outlineEntities.forEach(entity -> {
                outlines.add(WorkflowOutline.fromBrieflyEntity(entity));
            });
        }
        return outlines;
    }

    public static ExecutableWorkflowExt fillExecutableWorkflowDetail(WorkflowOutline outline,boolean needValidateData) {
        ExecutableWorkflowExt executableWorkflow = outline.getWorkflow();
        //沙箱复制后   会从executableWorkflow 中获取参数  如果更新的时候  不修改  在保存流程定义的时候 会存在问题
        executableWorkflow.setId(outline.getId());
        executableWorkflow.setProperty("tenantId",outline.getTenantId());
        executableWorkflow.setName(outline.getName());
        outline.setExecutableWorkflowExtFromOutline(executableWorkflow,needValidateData);
        WorkflowJsonUtil.proofreadAndCorrectWorkflow(executableWorkflow);
        executableWorkflow.put("entityId",outline.getEntryType());
        executableWorkflow.removeProperty(MODIFY_RECORD);
        return executableWorkflow;
    }

    public static String convertWorkflowExtensionToJson(WorkflowExtension extension) {
        return JsonUtil.toJson(extension);
    }

    public static WorkflowExtension convertWorkflowExtensionFromJson(String extensionJson) {
        return JsonUtil.fromJson(extensionJson, WorkflowExtension.class);
    }

    public boolean isNew() {
        return Strings.isNullOrEmpty(id);
    }

    public static Map<String, Object> addWorkflowExt(WorkflowOutlineEntity entity, Map<String, Object> workflowMap) {
        workflowMap.put("rangeCircleIds", entity.getRangeCircleIds());
        workflowMap.put("rangeRoleIds", entity.getRangeRoleIds());
        workflowMap.put("rangeGroupIds", entity.getRangeGroupIds());
        workflowMap.put("rangeEmployeeIds", entity.getRangeEmployeeIds());
        workflowMap.put("entryType", entity.getEntryType());
        workflowMap.put("entryTypeName", entity.getEntryTypeName());
        return workflowMap;
    }


    @Transient(value = false)
    public Map<String, Set<Object>> getRangeAssignee() {
        Map<String, Set<Object>> rangeAssignee = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(rangeCircleIds)) {
            rangeAssignee.put("dept", Sets.newHashSet(rangeCircleIds));
        }
        if (CollectionUtils.isNotEmpty(rangeGroupIds)) {
            rangeAssignee.put("group", Sets.newHashSet(rangeGroupIds));
        }
        if (CollectionUtils.isNotEmpty(rangeEmployeeIds)) {
            rangeAssignee.put("person", Sets.newHashSet(rangeEmployeeIds));

        }
        if (CollectionUtils.isNotEmpty(rangeRoleIds)) {
            rangeAssignee.put("role", Sets.newHashSet(rangeRoleIds));
        }
        return rangeAssignee;
    }
    /**
     * 当更新时或创建时 同步key到workflow 保存到引擎
     */
    public void setExecutableWorkflowExtFromOutline(ExecutableWorkflowExt executableWorkflow,boolean needValidateData) {
        if (isNew()) {
            //沙箱复制的时候 传递的是false   两个企业之间的copy  sourceWorkflowId是允许重复的,更改集复制的时候  会覆盖之前的
            if(needValidateData){
                executableWorkflow.setSourceWorkflowId(IDCreatorUtil.createId());
            }
            this.setSourceWorkflowId(executableWorkflow.getSourceWorkflowId());
            if (this.rule != null) {
                this.rule.setRuleId(null);
            }
        }
        setEngineExecutableWorkflow(executableWorkflow);
    }

    public String getLinkApp() {
        //外部流程只应用于服务通
        if (getExternalFlow() == 1) {
            return BPMConstants.externalFlowLinkApp;
        }
        return linkApp;
    }

    public String getLinkAppName() {
        //外部流程只应用于服务通
        if (getExternalFlow() == 1) {
            return BPMConstants.externalFlowLinkAppName;
        }
        return linkAppName;
    }

    public Integer getLinkAppType() {
        //外部流程只应用于服务通
        if (getExternalFlow() == 1) {
            return BPMConstants.externalFlowLinkAppType;
        }
        return linkAppType;
    }

    public Boolean getLinkAppEnable() {
        if (getExternalFlow() == 1) {
            return Boolean.FALSE;
        }
        return linkAppEnable;
    }

    public void setEngineExecutableWorkflow(ExecutableWorkflowExt executableWorkflow){
        executableWorkflow.setName(this.getName());
        executableWorkflow.setDescription(this.getDescription());
        /**
         * 6.2添加外部流程和是否为单实例流程 的标示
         */
        executableWorkflow.setProperty(WorkflowKey.externalFlow, this.getExternalFlow());
        executableWorkflow.setProperty(WorkflowKey.type, BPMConstants.APP_TYPE);
        executableWorkflow.setProperty(WorkflowKey.singleInstanceFlow, this.getSingleInstanceFlow());
        /**
         * 互联应用
         */
        executableWorkflow.setProperty(WorkflowKey.linkAppEnable, this.getLinkAppEnable());
        executableWorkflow.setProperty(WorkflowKey.linkApp, this.getLinkApp());
        executableWorkflow.setProperty(WorkflowKey.linkAppName, this.getLinkAppName());
        executableWorkflow.setProperty(WorkflowKey.linkAppType, this.getLinkAppType());
        executableWorkflow.setProperty(WorkflowKey.applicantWhenSystemUser, this.getWorkflow().get(WorkflowKey.applicantWhenSystemUser));
        /**
         * 由于 外部应用时引擎也需要 处理一些事情 ，故此处冗余一份到Task上
         */
        executableWorkflow.getActivities().stream().filter(activity -> activity.instanceOf(UserTaskExt.class)).forEach(activity -> {
            if (BPMTask.isExternalApplyTask(activity)) {
                activity.put(ExecutionTypeEnum.externalApplyTask.name(), 1);
            } else {
                activity.put(ExecutionTypeEnum.externalApplyTask.name(), 0);
            }
        });
        WorkflowRule rule = this.getRule();
        if (rule != null) {
            rule.setWorkflowProperties(this.getTenantId(), executableWorkflow.getSourceWorkflowId(),
                    this.getEntryType());
        }
    }

    public String getRuleJson() {
        if (rule != null) {
            if (CollectionUtils.isEmpty(rule.getConditions())) {
                return null;
            }
            return JsonUtil.toJson(rule);
        }
        return null;
    }

    public void setWorkflow(ExecutableWorkflowExt executableWorkflow) {
        this.workflow = executableWorkflow;
    }

    public void setRuleId(String ruleId) {
        if (this.getRule() != null && Strings.isNullOrEmpty(this.getRule().getRuleId())) {
            this.getRule().setRuleId(ruleId);
        }
    }

    public Integer getSingleInstanceFlow() {
        if (singleInstanceFlow == null || singleInstanceFlow == 0) {
            return 0;
        }
        return 1;
    }

    public Integer getExternalFlow() {
        if (externalFlow == null || externalFlow == 0) {
            return 0;
        }
        return 1;
    }


    public void clearWorkflowOutline(WorkflowOutline workflowOutline, String tenantId) {
        workflowOutline.setId("");
        workflowOutline.setTenantId(tenantId);
        workflowOutline.setCount(0);
        //更改集 沙箱复制 如果sourceWorkflowId为空,则会生成新的; 在执行入站出站的时候  无法执行更新   TId,ET,SWId
        //workflowOutline.getWorkflow().setSourceWorkflowId(null);
        workflowOutline.setWorkflowId(null);
        workflowOutline.getWorkflow().put(BPMConstants.TENANT_ID, tenantId);
        if(Objects.isNull(workflowOutline.getCreateTime())){
            workflowOutline.setCreateTime(System.currentTimeMillis());
        }
        workflowOutline.setCreatedBy(BPMConstants.CRM_SYSTEM_USER);
        workflowOutline.setLastModifiedBy(BPMConstants.CRM_SYSTEM_USER);
        if (Objects.isNull(workflowOutline.getLastModifiedTime())) {
            workflowOutline.setLastModifiedTime(System.currentTimeMillis());
        }
        workflowOutline.getExtension().setId(null);
        workflowOutline.setRuleId(null);
    }

    public static WorkflowOutlineEntity toOutlineEntity(RemoteContext context, WorkflowOutline workflow) {
        WorkflowOutlineEntity entity = new WorkflowOutlineEntity();
        entity.setUserId(workflow.getUserId());
        entity.setId(workflow.getId());
        entity.setTenantId(workflow.getTenantId());
        entity.setCreatedBy(workflow.getCreatedBy());
        entity.setCreateTime(workflow.getCreateTime());
        entity.setWorkflowId(workflow.getWorkflowId());
        entity.setDescription(workflow.getDescription());
        entity.setExtensionJson(WorkflowOutline.convertWorkflowExtensionToJson(workflow.getExtension()));
        entity.setWorkflowJson(JsonUtil.toJson(workflow.getWorkflow()));
        entity.setRuleJson(workflow.getRuleJson());
        entity.setSourceWorkflowId(workflow.getSourceWorkflowId());
        entity.setEnabled(workflow.isEnabled());
        entity.setLastModifiedTime(workflow.getLastModifiedTime());
        entity.setLastModifiedBy(context.getUserId());
        entity.setName(workflow.getName());
        entity.setRangeEmployeeIds(workflow.getRangeEmployeeIds());
        entity.setRangeCircleIds(workflow.getRangeCircleIds());
        entity.setRangeGroupIds(workflow.getRangeGroupIds());
        entity.setRangeRoleIds(workflow.getRangeRoleIds());
        entity.setEntryType(workflow.getEntryType());
        entity.setEntryTypeName(workflow.getEntryTypeName());
        entity.setTemplateId(workflow.getTemplateId());
        entity.setSingleInstanceFlow(workflow.getSingleInstanceFlow() == null ? 0 : workflow.getSingleInstanceFlow());
        entity.setExternalFlow(workflow.getExternalFlow() == null ? 0 : workflow.getExternalFlow());
        entity.setCount(workflow.getCount());
        entity.setHasInstance(workflow.isHasInstance());
        entity.setDeleted(workflow.isDeleted());
        if(Objects.nonNull(workflow.getSupportFlow())){
            entity.setSupportFlowType(workflow.getSupportFlow().code);
        }
        if (Strings.isNullOrEmpty(workflow.getId())) {
            entity.setCount(0);
            entity.setDeleted(false);
            entity.setHasInstance(false);
        }
        entity.setLinkApp(workflow.getLinkApp());
        entity.setLinkAppEnable(workflow.getLinkAppEnable());
        entity.setLinkAppName(workflow.getLinkAppName());
        entity.setLinkAppType(workflow.getLinkAppType());
        entity.setControlStatus(workflow.getControlStatus());
        entity.setApplicantWhenSystemUser(workflow.getApplicantWhenSystemUser());
        return entity;
    }
    public static WorkflowOutline transferOutlineToCreate(WorkflowOutline originOutline, String flowName){
        if(Objects.isNull(originOutline)){
            return null;
        }
        WorkflowOutline res = new WorkflowOutline();
        res.setDescription(originOutline.getDescription());
        res.setEnabled(originOutline.isEnabled());
        res.setEntryType(originOutline.getEntryType());
        res.setEntryTypeName(originOutline.getEntryTypeName());
        res.setExtension(originOutline.getExtension());
        res.setExternalFlow(originOutline.getExternalFlow());
        res.setLinkApp(originOutline.getLinkApp());
        res.setLinkAppEnable(originOutline.getLinkAppEnable());
        res.setLinkAppName(originOutline.getLinkAppName());
        res.setLinkAppType(originOutline.getLinkAppType());
        res.setName(flowName);
        res.setRangeCircleIds(originOutline.getRangeCircleIds());
        res.setRangeEmployeeIds(originOutline.getRangeEmployeeIds());
        res.setRangeGroupIds(originOutline.getRangeGroupIds());
        res.setRangeRoleIds(originOutline.getRangeRoleIds());
        res.setSingleInstanceFlow(originOutline.getSingleInstanceFlow());

        if(Objects.nonNull(originOutline.getRule())){
            WorkflowRule rule = new WorkflowRule();
            rule.setConditionPattern(originOutline.getRule().getConditionPattern());
            rule.setConditions(originOutline.getRule().getConditions());
            rule.setTip(originOutline.getRule().getTip());
            res.setRule(rule);
        }

        if(Objects.nonNull(originOutline.getWorkflow())){
            ExecutableWorkflowExt workflow = new ExecutableWorkflowExt();
            workflow.setActivities(originOutline.getWorkflow().getActivities());
            workflow.setDescription(originOutline.getWorkflow().getDescription());
            workflow.setErrorNotifyRecipients(originOutline.getWorkflow().getErrorNotifyRecipients());
            workflow.setName(flowName);
            workflow.setTransitions(originOutline.getWorkflow().getTransitions());
            workflow.setVariables(originOutline.getWorkflow().getVariables());
            res.setWorkflow(workflow);
        }

        return res;
    }

}
