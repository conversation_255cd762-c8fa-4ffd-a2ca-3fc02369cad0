package com.facishare.bpm.model.task;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.model.DefaultActionLabel;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.TaskUtils;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by wangzhx on 2019/4/18.
 */
@Data
public class LaneTask extends BaseTask {
    @I18NExpression(relation = {"${workflowId.activityId}.name", "${sourceWorkflowId.activityId}.name"})
    private String taskName;
    private Boolean isTaskOwner = Boolean.FALSE;
    private String todoJumpUrl;
    //是否包含form
    private Boolean hasForm;
    private String message;

    @Override
    public Boolean getIsOwner() {
        return isTaskOwner;
    }

    public StandardData getStandardData() {
        if (Objects.isNull(getData())) {
            return null;
        }
        return getData();
    }

    public static LaneTask fromTask(RefServiceManager serviceManager, Task task) {
        return BeanUtils.transfer(task, LaneTask.class, (src, rst) -> {
            rst.setTaskName(src.getName());
            if(task.getExecutionType().isExecutionTask() && CollectionUtils.isNotEmpty(task.getExecutionList())){
                AfterActionExecution afterActionExecution = AfterActionExecution.pass(task.getExecutionList());
                rst.setErrorMsgByExecution(afterActionExecution, !task.getDelay() || Objects.nonNull(task.getFinishedTime()));
                rst.setExecution(afterActionExecution);
            }else {
                rst.setErrorMsgByExecution(src.getExecution(), src.getCompleted());
                rst.setExecution(src.getExecution());
            }
            StandardData standardData = rst.getStandardData(task);
            rst.setData(standardData);
            rst.setErrorMsgByCandidateIds(task);
            rst.setOpinions(CollectionUtils.isEmpty(src.getOpinions()) ? Lists.newArrayList() : src.getOpinions());
            rst.setCandidateModifyLog(src.getApproverModifyLog());
            rst.setExternalApply(src.getExternalApply());
            List<String> candidateIds = src.getCandidateIds();
            if (candidateIds != null) {
                rst.setIsTaskOwner(candidateIds.contains(serviceManager.getUserIdWithOuterUserId()));
            }
            if(CollectionUtils.isNotEmpty(src.getOperateLogs())){
                rst.setTagInfos(src.getOperateLogs().stream().filter(o -> BPMConstants.ADD_TAG_OPERATE.equals(o.getType())).collect(Collectors.toList()));
            }
            rst.setRemindLogs(TaskState.in_progress.equals(src.getState()) && CollectionUtils.isNotEmpty(src.getCandidateIds()) && CollectionUtils.isNotEmpty(src.getRemindLogs())
                    ? src.getRemindLogs()
                    : null);
        });
    }

    private StandardData getStandardData(Task task) {
        return BeanUtils.transfer(task, StandardData.class, (src, rst) -> {
            rst.setAfterActionWaiting(task.isAfterActionWaiting());
            rst.setRelatedEntityId((String) src.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId));
            rst.setTarget_related_list_name(src.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.relatedListName));
            rst.setTaskId(task.getId());
            rst.setDefaultButtons(JacksonUtil.fromJson(
                    JacksonUtil.toJson(task.getBpmExtension().get(WorkflowKey.ActivityKey.ExtensionKey.DEFAULT_BUTTONS)),
                    new TypeReference<Map<String, DefaultActionLabel>>() {
                    }));
            rst.setObjectFlowLayoutExists(null);
            rst.setHideCompleteBtn((Boolean) task.getBpmExtension().get(BPMConstants.HIDE_COMPLETE_BTN));
            rst.setOperateLogs(ExecutionTypeEnum.approve.equals(src.getExecutionType()) && "anyone".equals(src.getTaskType()) ? src.getOperateLogs() : null);
            rst.setTagInfo(src.getTagInfo());
            rst.setOpinions(BPMConstants.TagType.TAG.equals(src.getNodeType()) ? src.getOpinions() : null);
        });
    }

    private void setErrorMsgByCandidateIds(Task task) {
        if (!executionType.isExecutionTask() && !executionType.isExternalApplyTask() && CollectionUtils.isEmpty(getCandidateIds())) {
            setErrorMsg(StringUtils.isNotBlank(task.getErrMsg()) ? task.getErrMsg() : BPMI18N.PAAS_FLOW_BPM_HAS_NO_HANDLER.text());
            /**
             * 若处理人为空，则不需要data
             */
            setData(null);
        }
    }

    private void setErrorMsgByExecution(AfterActionExecution execution, boolean completed) {
        if (Objects.nonNull(execution)) {
            AfterActionExecution.SimpleAfterActionExecution simpleAfterActionExecution = execution.getErrorOrWaitingSimpleAfter();
            /**
             * 有后动作并且当前任务已经完成，任务状态是异常的
             */
            if (completed) {
                if (!simpleAfterActionExecution.isPass()) {
                    //如果是等待中,则提示文案
                    if (simpleAfterActionExecution.isWaiting()) {
                        setState(TaskState.in_progress);
                        setErrorMsg(BPMI18N.PAAS_FLOW_BPM_AFTER_ACTION_IS_WAITING.text());
                    } else {
                        setState(TaskState.error);
                        List<AfterAction> actions = simpleAfterActionExecution.getActions();
                        actions.forEach(action -> {
                            String taskType = action.getTaskType();
                            if ("custom_function".equals(taskType)) {
                                setErrorMsg(BPMI18N.PAAS_FLOW_AFTER_SYSTEM_FUNCTION_EXCEPTION.text());
                            }
                        });
                        setErrorMsg(getAfterActionErrorMessage(actions));
                    }
                }
            }
        }
    }

    private String getAfterActionErrorMessage(List<AfterAction> actions) {
        if (CollectionUtils.isEmpty(actions)) {
            return null;
        }
        return actions.get(0).getActionErrorMsg();
    }

    public void setExecution(AfterActionExecution execution) {
        if (Objects.nonNull(execution)) {
            this.execution = execution.getErrorOrWaitingSimpleAfter();
        } else {
            this.execution = new AfterActionExecution.SimpleAfterActionExecution();
        }
    }

    public boolean isTaskOwner(String userId) {
        List<String> candidateIds = this.getCandidateIds();
        if (candidateIds != null) {
            return candidateIds.contains(userId);
        }
        return false;
    }

    public boolean needAssignNextTask(Integer assignNextTask) {
        return assignNextTask != null && assignNextTask == 1;
    }

    public void setButtons(List<ActionButton> buttons) {
        if (CollectionUtils.isEmpty(buttons)) {
            return;
        }
        this.button = buttons.stream().collect(Maps::newLinkedHashMap, (m, button) -> m.put(button.getAction(), button), Map::putAll);
    }


    public String getReferenceAndMasterDetailField(Map<String, Object> desc, String targetRelatedListName) {
        if (Strings.isNullOrEmpty(targetRelatedListName) || Objects.isNull(desc)) {
            return null;
        }
        Map<String, Map<String, Object>> fields = MapUtil.instance.getMapOfGeneric(desc, BPMConstants.MetadataKey.fields);
        for (Map.Entry<String, Map<String, Object>> entry : fields.entrySet()) {
            String fieldApiName = entry.getKey();
            Map<String, Object> fieldDesc = entry.getValue();
            if (targetRelatedListName.equals(fieldDesc.get(WorkflowKey.ActivityKey.ExtensionKey.relatedListName))
                    && MapUtil.instance.getBool(fieldDesc, BPMConstants.MetadataKey.isActive)) {
                return fieldApiName;
            }
        }

        return null;
    }

    public boolean isReturnHighSea(Map<String, Object> extension) {
        return BPMConstants.MetadataKey.accountObjApiName.equals(extension.get(WorkflowKey.ActivityKey.ExtensionKey.entityId))
                && BPMConstants.MetadataKey.RETURN.equals(extension.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode));
    }

    /**
     * 待办、异常状态在上面并且按最后修改时间降序
     * 已完成在下面并且按最后修改时间降序
     *
     * @param laneTasks
     * @return
     */
    public static List<LaneTask> sort(List<LaneTask> laneTasks) {
        if (CollectionUtils.isEmpty(laneTasks)) {
            return Lists.newArrayList();
        }
        List<LaneTask> laneTaskList = laneTasks.stream().filter(k -> TaskState.pass != k.getState())
                .sorted(Comparator.comparingLong(LaneTask::getModifyTime)).collect(Collectors.toList());
        laneTaskList.addAll(laneTaskList.size(), laneTasks.stream().filter(k -> TaskState.pass == k.getState())
                .sorted(Comparator.comparingLong(LaneTask::getModifyTime)).collect(Collectors.toList()));
        return laneTaskList;
    }

    /**
     * 获取人员
     */
    public static List<Object> getPersons(List<LaneTask> tasksByLaneId) {
        return tasksByLaneId.stream().collect(Lists::newArrayList, (list, tasksByLane) -> {
            if (CollectionUtils.isNotEmpty(tasksByLane.getCandidateIds())) {
                list.addAll(tasksByLane.getCandidateIds());
            }
            if (CollectionUtils.isNotEmpty(tasksByLane.getProcessIds())) {
                list.addAll(tasksByLane.getProcessIds());
            }
            if (CollectionUtils.isNotEmpty(tasksByLane.getRemindLogs())) {
                list.addAll(tasksByLane.getRemindLogs().stream().filter(r -> StringUtils.isNotBlank(r.getUserId())).map(Task.RemindLog::getUserId).collect(Collectors.toList()));
            }
        }, List::addAll);
    }

    public void sortPersons(boolean isUpStreamTenant) {
        TaskUtils.sortPersons(isUpStreamTenant, getCandidateIds());
        TaskUtils.sortPersons(isUpStreamTenant, getProcessIds());
    }

    // TODO 看下前端是如何使用的
    public boolean isInProgress() {
        return TaskState.in_progress == this.state;
    }

    public boolean isError() {
        return TaskState.error == this.state;
    }

    public String getAppCode() {
        if (MapUtils.isNotEmpty(this.externalApply)) {
            return MapUtil.instance.getString(this.externalApply, BPMConstants.APPCODE);
        }
        return StringUtils.EMPTY;
    }

    public String getActionCode() {
        if (MapUtils.isNotEmpty(this.externalApply)) {
            return MapUtil.instance.getString(this.externalApply, BPMConstants.ACTIONCODE);
        }
        return StringUtils.EMPTY;
    }

    public boolean isExternalApplyTask() {
        return ExecutionTypeEnum.externalApplyTask.equals(this.executionType);
    }

    public boolean isAfterActionWaiting() {
        if (Objects.nonNull(execution)) {
            return execution.isWaiting();
        } else {
            return false;
        }
    }

    /**
     * 给终端补全阶段信息
     * @param extension
     */
    public void fillLaneDetail(FlowExtensionEntity extension) {
        if (Objects.nonNull(extension)) {
            LaneEntity laneEntity = extension.getLane(this.getActivityId());
            if (Objects.nonNull(laneEntity)) {
                this.setLaneId(laneEntity.getId());
                this.setLaneName(laneEntity.getName());
            }
        }
    }
}
