package com.facishare.bpm.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * Created by wangz on 17-5-3.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowStats {
    private String id;  //activityId/laneId/sourceWorkflowId
    private String name;
    private int inProgressTaskCount;  //进行中全部的任务数 = normalTaskCount + timeoutTaskCount + errorTaskCount
    private int normalTaskCount;    //进行中正常的（非超时，非异常）任务数
    private int timeoutTaskCount;   //进行中 超时的任务数
    private int errorTaskCount;     //进行中异常的任务数
    private int inProgressWorkflowCount;  //进行中的流程实例数
    private int passWorkflowCount;  //完成的流程实例数
    private int canceledWorkflowCount; //取消的流程实例数
    private int totalWorkflowCount; //流程实例总数 = passWorkflowCount + inProgressWorkflowCount +totalWorkflowCount

    public WorkflowStats(String id, String name) {
        this.id = id;
        this.name = name;
    }
}
