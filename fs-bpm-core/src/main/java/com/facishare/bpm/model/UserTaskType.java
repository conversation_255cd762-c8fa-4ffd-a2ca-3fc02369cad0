package com.facishare.bpm.model;

import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 *  @IgnoreI18n
 */
@Data
public class UserTaskType {
    private static List<UserTaskType> taskTypes = Lists.newArrayList(
            new UserTaskType("应用节点", ExecutionTypeEnum.externalApplyTask.name()),
            new UserTaskType("多人审批", "approve", "anyone"),
            new UserTaskType("会签", "approve", "all"),
            new UserTaskType("操作节点", "operation"),
            new UserTaskType("更新节点", "update"),
            new UserTaskType("新建关联对象节点", "addRelatedObject"),
            new UserTaskType("新建从对象节点", "addMDObject"));

    private String type;
    private String subType;
    private String name;

    public UserTaskType(String name, String type) {
        this.type = type;
        this.name = name;
    }

    public UserTaskType(String name, String type, String subType) {
        this.type = type;
        this.name = name;
        this.subType = subType;
    }

    public static UserTaskType getUserType(Task task) {
        //如果是自动节点和定时节点,则不继续
        if (task.isAutoTask()) {
            return null;
        }

        String executionType = task.getExecutionType().name();
        for (UserTaskType taskType : taskTypes) {
            if (taskType.type.equals(executionType)) {
                taskType.setSubType(task.getTaskType());
                return taskType;
            }
        }
        return null;
    }
}
