package com.facishare.bpm.model.task;

import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TaskButtonList {
    private Map<String, List<ActionButton>> buttons;
    /**
     * 880 业务定制元素，下发配置信息
     */
    private Map<String,CustomElementData> customElementDataMap;
    private Map<String, String> errorMsgs;
    private Map<String, List> moreOperations;

    public TaskButtonList(Map<String, List<ActionButton>> buttons, Map<String, String> errorMsgs) {
        this.buttons = buttons;
        this.errorMsgs = errorMsgs;
    }
    public TaskButtonList(Map<String, List<ActionButton>> buttons,Map<String,CustomElementData> customElementDataMap, Map<String, String> errorMsgs, Map<String, List<MoreOperationManager.MoreOperation>> moreOperations, boolean showMoreBtn) {
        if (showMoreBtn){
            this.moreOperations = new HashMap<>(moreOperations);
            if (MapUtils.isNotEmpty(buttons)){
                this.buttons = new HashMap<>();
                for (String key : buttons.keySet()) {
                    List<ActionButton> buttonList = buttons.get(key);
                    if (CollectionUtils.isNotEmpty(buttonList)){
                        buttonList.removeIf(b -> BPMConstants.MetadataKey.UPDATE_AND_COMPLETE.equals(b.getAction()) || BPMConstants.MetadataKey.SAVE_CODE.equals(b.getAction()));
                    }
                    List moreList = this.moreOperations.get(key);
                    if(CollectionUtils.isNotEmpty(buttonList) && buttonList.size() > 3 && !(buttonList.size() == 4 && CollectionUtils.isEmpty(moreList))){
                        List<ActionButton> subList = Lists.newArrayList(buttonList.subList(3, buttonList.size()));
                        buttonList.removeAll(subList);
                        if(CollectionUtils.isEmpty(moreList)){
                            this.moreOperations.put(key, subList);
                        }else {
                            this.moreOperations.put(key, Lists.newArrayList(Iterables.concat(subList, moreList)));
                        }
                    }
                    this.buttons.put(key, buttonList);
                }
            }
        }else {
            this.buttons = buttons;
        }
        this.customElementDataMap = customElementDataMap;
        this.errorMsgs = errorMsgs;
    }
    @Data
    public static class CustomElementData {
        Map customExtension;
        FlowElementPluginWrapper elementPluginConfig;

        public CustomElementData(Map customExtension, FlowElementPluginWrapper elementPluginConfig) {
            this.customExtension = customExtension;
            this.elementPluginConfig = elementPluginConfig;
        }
    }
}
