package com.facishare.bpm.model;

import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by wangz on 17-2-13.
 */
@Setter
@Getter
public class TaskOutline implements Comparable<TaskOutline> {

    //getHandleTasks接口需要的返回值
    String taskId;
    String activityId;
    @I18NExpression(relation = {"${workflowId.activityId}.name", "${sourceWorkflowId.activityId}.name"})
    String taskName;
    TaskState state;
    List<String> candidateIds;
    private List<String> processIds;//执行人
    private String taskType;
    ExecutionTypeEnum executionTypeEnum;

    String errorMsg;
    Long createTime; //任务创建时间
    Long modifyTime;

    String entityId;
    String objectId;

    private String linkAppName;
    Map<String, MoreOperationManager.MoreOperation> moreOperations = Maps.newHashMap();
    String workflowInstanceId;
    int activityInstanceId;

    private String linkAppType;
    private String linkApp;
    private Boolean linkAppEnable;

    String laneId;
    @I18NExpression(relation = {"${workflowId.laneId}.name", "${sourceWorkflowId.laneId}.name"})
    String laneName;
    Boolean isTaskOwner;

    String workflowId;


    //activityId
    //externalApply
    //assignNextTask
    //nextTaskAssigneeScope
    //opinions
    //candidateModifyLog
    //execution
    //button
    //data
    //todoJumpUrl

    @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
    String processName;
    @I18NExpression(relation = {"${workflowId}.description", "${sourceWorkflowId}.description"})
    String description;
    Long startTime;
    Long endTime;
    String applicantId;
    String sourceWorkflowId;
    String entryType; //apiName
    String entityName;
    String objectName;
    boolean completed;
    boolean belongToCurrentObj = true;
    @I18NExpression(drill = true)
    List<Pool> pools;




    Map<String, List<String>> assignee;


    List<String> assigneeIds;
    private List<String> unProcessIds;//待处理人
    //unit:1-天；2-小时；3-分钟
    private Integer latencyUnit;
    Object remindLatency;
    Boolean isTimeout;

    /**
     * 更多操作
     */




    //卡梅隆数据详情页  点击 去处理  需要判断下是否是1转三,如果是,则需要强生  710
    private String actionCode;
    @Override
    public int compareTo(TaskOutline t2) {
        if (this.getIsTaskOwner() && !t2.getIsTaskOwner()) {
            return -1;
        } else if (!this.getIsTaskOwner() && t2.getIsTaskOwner()) {
            return 1;
        } else {
            return this.getCreateTime().compareTo(t2.getCreateTime()) > 0 ? -1 : 1;
        }
    }

    public boolean starting() {
        if (state.equals(TaskState.in_progress) || state.equals(TaskState.error)) {
            return true;
        }
        return false;
    }


    public boolean isInProgress() {
        return TaskState.in_progress == this.state;
    }

    public boolean isError() {
        return TaskState.error == this.state;
    }

    public void setMoreOperations(List<MoreOperationManager.MoreOperation> moreOperations) {
        if (!CollectionUtils.isEmpty(moreOperations)) {
            moreOperations.forEach(operation -> this.moreOperations.put(operation.getCode(), operation));
        }
    }

    /**
     *
     * @param remindLatency 具体数值
     * @param latencyUnit unit:1-天；2-小时；3-分钟
     */
    public void setRemindLatency(Object remindLatency, Integer latencyUnit) {
        Double d = Task.getRemindLatencyTime(remindLatency, latencyUnit);
        if (0 != d) {
            //当前时间-创建时间 得到一个时间戳  然后除以一个小时  得到一个小时
            if (((System.currentTimeMillis() - createTime) / (3600 * 1000)) > d) {
                this.setIsTimeout(true);
            }
        }
        this.remindLatency = remindLatency;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

}
