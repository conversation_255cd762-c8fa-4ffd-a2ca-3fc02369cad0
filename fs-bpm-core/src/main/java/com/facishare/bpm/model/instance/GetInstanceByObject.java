package com.facishare.bpm.model.instance;

import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.Pool;
import com.facishare.bpm.model.paas.engine.bpm.ActivityInstance;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wangzhx on 2019/4/18.
 */
@Data
public class GetInstanceByObject {
    private String id;
    private String workflowId;
    private String sourceWorkflowId;
    @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
    private String workflowName;
    private String entityId;
    private String entityName;
    private String applicantId;
    @I18NExpression(drill = true)
    private List<Pool.Lane> lanes;
    private Long start;
    /**
     * 更多操作
     */
    private Map<String, MoreOperationManager.MoreOperation> moreOperations = Maps.newHashMap();

    public void setMoreOperations(List<MoreOperationManager.MoreOperation> moreOperations) {
        if (CollectionUtils.isNotEmpty(moreOperations)) {
            moreOperations.forEach(operation -> this.moreOperations.put(operation.getCode(), operation));
        }
    }
    /**
     * 1. 找到  workflowInstance.getActivityInstances() 使用 start 进行倒序, 取出相关activityId 最新的activityInstance
     * 2. 使用 activityIdAndTypes 中 的 type 进行过滤,只保留 userTask, executionTask,latencyTask 类型的 activityInstance 
     * 3. 使用 Pool.Lane.create(laneEntity, 2中过滤的activityInstances))生成带有状态的阶段信息 
     * @param workflowInstance activityInstances 中activityId 与下面 activityIdAndTypes 的key 对应
     * @param activityIdAndTypes activityId->type 
     * @param pools 
     */
    public void setLanes(WorkflowInstance workflowInstance, Map<String, String> activityIdAndTypes, List<PoolEntity> pools) {
        List<Pool.Lane> lanes = Lists.newArrayList();

        // 1. 过滤出需要的任务类型并按开始时间倒序排序
        List<ActivityInstance> latestActivityInstances = workflowInstance.getActivityInstances().stream()
            // 过滤有效的任务类型
            .filter(instance -> {
                String type = activityIdAndTypes.get(instance.getActivityId());
                return type != null && isValidTaskType(type);
            })
            // 按开始时间倒序排序
            .sorted(Comparator.comparing(
                ActivityInstance::getStart,
                Comparator.nullsLast(Comparator.reverseOrder())
            ))
            .collect(Collectors.toList());

        // 2. 获取每个 activityId 对应的最新实例
        Map<String, ActivityInstance> latestInstanceMap = latestActivityInstances.stream()
            .collect(Collectors.toMap(
                ActivityInstance::getActivityId,
                instance -> instance,
                (existing, replacement) -> existing // 保留第一个（最新的）实例
            ));

        // 3. 生成带有状态的阶段信息
        pools.forEach(poolEntity ->
            poolEntity.getLanes().forEach(laneEntity ->
                lanes.add(Pool.Lane.create(laneEntity, latestInstanceMap))
            )
        );
        
        this.lanes = lanes;
    }

    /**
     * 判断任务类型是否有效
     */
    private boolean isValidTaskType(String type) {
        return "userTask".equals(type) 
            || "executionTask".equals(type) 
            || "latencyTask".equals(type);
    }

    public void sortLanes(){
        if(CollectionUtils.isEmpty(this.lanes) || Objects.isNull(this.lanes.get(0).getOrder())){
            return;
        }
        Collections.sort(this.lanes, Comparator.comparingInt(Pool.Lane::getOrder));
    }
}
