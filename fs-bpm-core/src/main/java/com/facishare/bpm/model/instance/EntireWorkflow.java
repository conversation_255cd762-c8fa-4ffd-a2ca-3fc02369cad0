package com.facishare.bpm.model.instance;

import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.paas.engine.bpm.ActivityInstance;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.i18n.CustomI18NHandler;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 12/04/2017.
 */
@Data
public class EntireWorkflow {
    @I18NExpression(custom = CustomI18NHandler.entireWorkflow)
    private Map<String, Object> workflow;
    @I18NExpression(drill = true)
    private WorkflowInstance workflowInstance;
    private String currentLaneId;
    @I18NExpression(custom = CustomI18NHandler.svg)
    private String svg;
    private List<MoreOperationManager.MoreOperation> moreOperations;
    private Map<String, Employee> employeeInfo;
    private String workflowId;
    private String sourceWorkflowId;


    public void setEntryTypeName(String entryTypeName) {
        workflow.put("entryTypeName", entryTypeName);
    }

    public void setPoolAndSvg(FlowExtensionEntity extension) {
        workflow.put("pools", extension.getSortPools());
        this.setSvg(extension.getSvg());
        List<ActivityInstance> activityInstances = this.workflowInstance.getActivityInstances();
        activityInstances.sort(Comparator.comparing(o -> Long.valueOf(o.getStart())));
        String currentActivity = activityInstances.get(activityInstances.size() - 1).getActivityId();
        this.currentLaneId = extension.getLane(currentActivity).getId();
    }

    public Map<String, Object> getRule() {
        return workflow.get("rule") == null ? null : MapUtil.instance.getMapOfGeneric(workflow,"rule");//Map<String, Object>) workflow.get("rule");
    }

    public Map<String, Object> getWorkflow() {
        return workflow;
    }

    public void setWorkflow(Map<String, Object> workflow) {
        this.workflow = workflow;
    }

    public WorkflowInstance getWorkflowInstance() {
        return workflowInstance;
    }

    public void setWorkflowInstance(WorkflowInstance workflowInstance) {
        if(workflowInstance!=null){
            this.workflowInstance=workflowInstance;
            workflowInstance.setState(workflowInstance.getState());
            if(CollectionUtils.isNotEmpty(workflowInstance.getActivityInstances())){
                workflowInstance.getActivityInstances().forEach(ActivityInstance::init);
            }
        }
    }

    public String getCurrentLaneId() {
        return currentLaneId;
    }

    public void setCurrentLaneId(String currentLaneId) {
        this.currentLaneId = currentLaneId;
    }

    public String getSvg() {
        return svg;
    }

    public void setSvg(String svg) {
        this.svg = svg;
    }
}
