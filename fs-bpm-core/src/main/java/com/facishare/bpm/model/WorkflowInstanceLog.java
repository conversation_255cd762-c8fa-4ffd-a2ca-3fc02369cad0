package com.facishare.bpm.model;

import com.facishare.bpm.model.paas.engine.bpm.AfterAction;
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Opinion;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.utils.TaskUtils;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/25 4:26 PM
 */
@Data
public class WorkflowInstanceLog {

    @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
    private String name;
    private InstanceState state;
    private String applicantId;
    private Long startTime;
    private Long endTime;
    @I18NExpression(drill = true)
    private List<TaskLog> logs;
    private String cancelPersonId;
    private String reason;
    private Map<String, Employee> employeeInfo;
    private String workflowId;
    private String sourceWorkflowId;
    private String submitter;
    private Map<String, AfterActionExecution.SimpleAfterActionExecution> execution;

    /**
     * applicantId 发起人
     * cancelPersonId 取消人
     * log:审批处理人，修改记录变更前后的人
     * 后动作执行人
     */
    public List<Object> getPersonIds() {

        List<Object> personIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(this.applicantId)) {
            personIds.add(this.applicantId);
        }
        if (StringUtils.isNotBlank(this.cancelPersonId)) {
            personIds.add(this.cancelPersonId);
        }
        if(StringUtils.isNotBlank(this.submitter)){
            personIds.add(this.submitter);
        }

        if (CollectionUtils.isNotEmpty(this.logs)) {
            List<Object> logPersons = this.logs.stream().collect(Lists::newArrayList, (list, log) -> {
                List<Opinion> opinions = log.getOpinions();
                if (CollectionUtils.isNotEmpty(opinions)) {
                    list.addAll(opinions.stream().filter(key -> !Strings.isNullOrEmpty(key.getUserId())).map(Opinion::getUserId).collect(Collectors.toList()));
                }

                AfterActionExecution.SimpleAfterActionExecution execution = log.getExecution();
                if (Objects.nonNull(execution)) {
                    List<AfterAction> actions = execution.getActions();
                    if (CollectionUtils.isNotEmpty(actions)) {
                        list.addAll(actions.stream().filter(key -> !Strings.isNullOrEmpty(key.getUserId())).map(AfterAction::getUserId).collect(Collectors.toList()));
                    }
                }

                List<String> unCompletedPersons = log.getUnCompletedPersons();
                if (CollectionUtils.isNotEmpty(unCompletedPersons)) {
                    list.addAll(unCompletedPersons);
                }

                List<ChangeCandidateLog> candidateLogs = log.getCandidateLogs();
                if (CollectionUtils.isNotEmpty(candidateLogs)) {
                    candidateLogs.forEach(candidateLog -> {
                        if (StringUtils.isNotBlank(candidateLog.getOperatorId())) {
                            list.add(candidateLog.getOperatorId());
                        }
                        if (CollectionUtils.isNotEmpty(candidateLog.getFrom())) {
                            list.addAll(candidateLog.getFrom());
                        }
                        if (CollectionUtils.isNotEmpty(candidateLog.getTo())) {
                            list.addAll(candidateLog.getTo());
                        }
                    });

                }

            }, List::addAll);
            if (CollectionUtils.isNotEmpty(logPersons)) {
                personIds.addAll(logPersons);
            }
        }
        return personIds;
    }

    public void setSortedLogs(boolean isUpStreamTenant, List<TaskLog> logs) {
        if (CollectionUtils.isEmpty(logs)) {
            return;
        }
        logs.forEach(log -> {
            List<ChangeCandidateLog> candidateLogs = log.getCandidateLogs();
            if (CollectionUtils.isNotEmpty(candidateLogs)) {
                candidateLogs.forEach(changeCandidateLog -> {
                            TaskUtils.sortPersons(isUpStreamTenant, changeCandidateLog.getFrom());
                            TaskUtils.sortPersons(isUpStreamTenant, changeCandidateLog.getTo());
                        }
                );
            }
            TaskUtils.sortPersons(isUpStreamTenant, log.getUnCompletedPersons());
        });
        this.logs = logs;
    }

    public boolean isError() {
        return this.state == InstanceState.error && endTime != null;
    }

}
