package com.facishare.bpm.model.task;

import com.facishare.bpm.rule.RuleMessage;
import lombok.Data;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/6/4 10:07 上午
 */
@Data
public class CompleteTaskResult {
    private RuleMessage ruleMessage;
    private Integer sleepTime;
    private NextTask nextTask;


    public static CompleteTaskResult create(RuleMessage ruleMessage, Integer sleepTime,NextTask nextTask) {
        CompleteTaskResult result = new CompleteTaskResult();
        result.setRuleMessage(ruleMessage);
        result.setSleepTime(sleepTime);
        result.setNextTask(nextTask);
        return result;
    }
}
