package com.facishare.bpm.model;

import com.facishare.bpm.model.paas.engine.bpm.WorkflowContext;
import lombok.Data;

import java.util.List;

/**
 * Created by wangzhx on 2019/7/31.
 */
@Data
public class MetaDataChangeEvent {
    private String tenantId;
    private String op;
    private String name;
    private List<DataBody> body;

    @Data
    public static class DataBody {
        private String eventId;
        private WorkflowContext context;
        private String name;
        private String entityId;
        private String triggerType;
        private String objectId;
    }

}
