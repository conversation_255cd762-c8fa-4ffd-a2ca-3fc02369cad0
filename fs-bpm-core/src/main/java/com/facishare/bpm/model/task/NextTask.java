package com.facishare.bpm.model.task;

import lombok.Data;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/10 12:01 PM
 */
@Data
public class NextTask {
    //TODO 删除任务id   问下汝健
    private String nextTaskId;
    private Integer activityInstanceId;
    private String workflowInstanceId;


    public static NextTask create(String nextTaskId, Integer activityInstanceId, String workflowInstanceId) {
        NextTask nextTask = new NextTask();
        nextTask.setNextTaskId(nextTaskId);
        nextTask.setActivityInstanceId(activityInstanceId);
        nextTask.setWorkflowInstanceId(workflowInstanceId);
        return nextTask;
    }
}
