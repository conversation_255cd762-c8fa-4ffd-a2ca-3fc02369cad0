package com.facishare.bpm.model.statics;

import com.facishare.flow.postgre.entity.BpmInstanceCountEntity;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @creat_date: 2019/4/16
 * @creat_time: 10:40
 * @since 6.6
 */
@Data public class WorkflowInstanceStatics extends TaskStatics {
    private int inProgressWorkflowCount;  //进行中的流程实例数
    private int passWorkflowCount;  //完成的流程实例数
    private int canceledWorkflowCount; //取消的流程实例数
    private int totalWorkflowCount;
    //流程实例总数 = passWorkflowCount + inProgressWorkflowCount +totalWorkflowCount

    public static WorkflowInstanceStatics build(String sourceWorkflowId, String name,
                                                List<BpmInstanceCountEntity> bpmInstanceEntities, Map<String, TaskStatics> taskStaticsMap, String workflowId) {
        WorkflowInstanceStatics stats = new WorkflowInstanceStatics();
        stats.setName(name);
        stats.setId(sourceWorkflowId);
        stats.setWorkflowId(workflowId);
        stats.setSourceWorkflowId(sourceWorkflowId);
        bpmInstanceEntities.forEach(item -> {
            int count = item.getCount();
            stats.totalWorkflowCount += count;
            if ("cancel".equals(item.getState())) {
                stats.canceledWorkflowCount += count;
            } else if ("pass".equals(item.getState())) {
                stats.passWorkflowCount += count;
            } else {
                stats.inProgressWorkflowCount += count;
            }
        });


        taskStaticsMap.forEach((s, taskStatics) -> stats.addTaskStateStatics(taskStatics));
        return stats;
    }

    private void addTaskStateStatics(TaskStatics taskStatics) {
        this.errorTaskCount += taskStatics.getErrorTaskCount();
        this.timeoutTaskCount += taskStatics.getTimeoutTaskCount();
        this.inProgressTaskCount += taskStatics.getInProgressTaskCount();
        this.normalTaskCount += taskStatics.getNormalTaskCount();
    }
}
