package com.facishare.bpm.model;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Data
public class ChangeCandidateLog {
    private String operatorId;
    private List<String> from;
    private List<String> to;
    private long modifyTime;
    private String opinion;


    public void setFrom(List<String> froms) {
        if (CollectionUtils.isEmpty(froms)) {
            return;
        }
        if (Objects.isNull(this.from)) {
            this.from = Lists.newArrayList();
        }
        for (String s : froms) {
            if (!this.from.contains(s)) {
                this.from.add(s);
            }
        }
    }

    public void setTo(List<String> tos) {
        if (CollectionUtils.isEmpty(tos)) {
            return;
        }
        if (Objects.isNull(this.to)) {
            this.to = Lists.newArrayList();
        }
        for (String s : tos) {
            if (!this.to.contains(s)) {
                this.to.add(s);
            }
        }
    }
}