package com.facishare.bpm.model;

import com.facishare.bpm.remote.model.org.Employee;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自定义对象 相关团队 对象
 * Created by wangz on 17-8-5.
 */
@AllArgsConstructor
@NoArgsConstructor
public class RelevantTeam {
    private Set<String> teamMemberEmployee;//团队成员 employee类型
    private String teamMemberRole;//团队角色 select_one类型 1:负责人 4:普通成员
    private String teamMemberPermissionType;//成员权限类型 select_one类型 1:只读 2:读写
    private String outTenantId; // 外部企业id
    private String sourceType; //2: 下游人员


    public RelevantTeam(Set<String> teamMemberEmployee, String teamMemberRole, String teamMemberPermissionType) {
        this.teamMemberEmployee = teamMemberEmployee;
        this.teamMemberRole = teamMemberRole;
        this.teamMemberPermissionType = teamMemberPermissionType;
    }
    //只要团队成员的employee相同,即是同一对象, 不去分其角色和权限
    @Override
    public boolean equals(Object o) {
        if (o == null || !(o instanceof RelevantTeam)) {
            return false;
        }

        RelevantTeam rt = (RelevantTeam) o;

        if (this.getTeamMemberEmployee() == null && rt.getTeamMemberEmployee() != null) {
            return false;
        }

        if (this.getTeamMemberEmployee() == null && rt.getTeamMemberEmployee() != null || this.getTeamMemberEmployee()
                != null && rt.getTeamMemberEmployee() == null) {
            return false;
        }

        if (this.getTeamMemberEmployee() != null && rt.getTeamMemberEmployee() != null && !this.getTeamMemberEmployee
                ().containsAll(rt.getTeamMemberEmployee())) {
            return false;
        }

        if (this.getTeamMemberEmployee() != null && rt.getTeamMemberEmployee() != null && !rt.getTeamMemberEmployee
                ().containsAll(this.getTeamMemberEmployee())) {
            return false;
        }

        if (this.getSourceType() != null && rt.getSourceType() != null && !rt.getSourceType().equals(this.getSourceType())) {
            return false;
        }

        if (this.getOutTenantId() != null && rt.getOutTenantId() != null && !rt.getOutTenantId().equals(this.getOutTenantId())) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int hash = teamMemberEmployee == null ? 0 : teamMemberEmployee.hashCode();
        hash = hash * 31 + (teamMemberRole == null ? 0 : teamMemberRole.hashCode());
        hash = hash * 31 + (teamMemberPermissionType == null ? 0 : teamMemberPermissionType.hashCode());
        hash = hash * 31 + (outTenantId == null ? 0 : outTenantId.hashCode());
        hash = hash * 31 + (sourceType == null ? 0 : sourceType.hashCode());

        return hash;
    }

    public static Set<RelevantTeam> getFromCandidateIds(List<String> ids) {
        Set<RelevantTeam> rets = Sets.newHashSet();
        ids.forEach(id -> rets.add(new RelevantTeam(Sets.newHashSet(id), TeamMemberRole.common.getValue(),
                TeamMemberPermissionType.readOnly.getValue())));

        return rets;
    }

    public static Set<RelevantTeam> getRelevantTeams(List<String> userIds, Map<Integer, Employee> employeeMap) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Sets.newHashSet();
        }
        return userIds.stream().filter(NumberUtils::isCreatable).map(userId -> {
            if (MapUtils.isEmpty(employeeMap)) {
                return RelevantTeam.build(userId);
            }
            Employee employee = employeeMap.get(Integer.valueOf(userId));
            if (Objects.nonNull(employee) && RelevantTeam.isOuterUser(userId)) {
                return RelevantTeam.build(userId, employee.getOutTenantId());
            } else {
                return RelevantTeam.build(userId);
            }
        }).collect(Collectors.toSet());
    }

    public static Set<RelevantTeam> getRelevantCollection(Collection relevantTeams) {
        Set<RelevantTeam> sets = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(relevantTeams)) {
            relevantTeams.forEach(item -> {
                if (item instanceof RelevantTeam) {
                    sets.add((RelevantTeam) item);
                } else if (item instanceof Map) {
                    sets.add(getFromMap((Map<String, Object>) item));
                }
            });
        }

        return sets;
    }

    public static RelevantTeam getFromMap(Map<String, Object> relevantTeamMap) {
        return new RelevantTeam(Sets.newHashSet((Collection) relevantTeamMap.get("teamMemberEmployee")),
                (String) relevantTeamMap.get("teamMemberRole"),
                relevantTeamMap.get("teamMemberPermissionType") + "");
    }

    public enum TeamMemberRole {
        owner("1"), common("4");

        @Getter
        String value;

        TeamMemberRole(String value) {
            this.value = value;
        }
    }

    public enum TeamMemberPermissionType {
        readOnly("1"), readAndWrite("2");

        @Getter
        String value;

        TeamMemberPermissionType(String value) {
            this.value = value;
        }
    }

    public enum SourceType {
        outUser("2");

        @Getter
        String value;

        SourceType(String value) {
            this.value = value;
        }
    }

    public Set<String> getTeamMemberEmployee() {
        if(teamMemberEmployee==null){
            teamMemberEmployee=Sets.newHashSet();
        }
        return teamMemberEmployee;
    }

    public void setTeamMemberEmployee(Set<String> teamMemberEmployee) {
        this.teamMemberEmployee = teamMemberEmployee;
    }

    public String getTeamMemberRole() {
        return teamMemberRole;
    }

    public void setTeamMemberRole(String teamMemberRole) {
        this.teamMemberRole = teamMemberRole;
    }

    public String getTeamMemberPermissionType() {
        return teamMemberPermissionType;
    }

    public void setTeamMemberPermissionType(String teamMemberPermissionType) {
        this.teamMemberPermissionType = teamMemberPermissionType;
    }

    public String getOutTenantId() {
        return outTenantId;
    }

    public void setOutTenantId(String outTenantId) {
        this.outTenantId = outTenantId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public static boolean isOuterUser(String userId){
        return userId.length() >= 9;
    }

    public static RelevantTeam build(String userId, String outTenantId) {
        return new RelevantTeam(Sets.newHashSet(userId), TeamMemberRole.common.getValue(),
                TeamMemberPermissionType.readOnly.getValue(), outTenantId, SourceType.outUser.getValue());
    }

    public static RelevantTeam build(String userId) {
        return new RelevantTeam(Sets.newHashSet(userId), TeamMemberRole.common.getValue(),
                TeamMemberPermissionType.readOnly.getValue());
    }
}
