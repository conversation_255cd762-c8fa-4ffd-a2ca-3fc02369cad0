package com.facishare.bpm.model.task;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/3/7 1:10 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetadataTaskResult {
    private List<MetadataTask> data;
    private int total;


    public static MetadataTaskResult getTaskByMetadata(RefServiceManager serviceManager, SearchTemplateQuery query) {
        FindDataBySearchTemplate.Result result = serviceManager.findDataBySearchTemplate("BpmTask", query);
        FindDataBySearchTemplate.QueryResultInfo queryResultInfo = result.getData().getQueryResult();
        List<Map<String, Object>> data = queryResultInfo.getData();
        List<MetadataTask> tasks = JacksonUtil.fromJson(JacksonUtil.toJson(data), new TypeReference<List<MetadataTask>>() {
        });
        return new MetadataTaskResult(tasks, queryResultInfo.getTotalNumber());
    }
}
