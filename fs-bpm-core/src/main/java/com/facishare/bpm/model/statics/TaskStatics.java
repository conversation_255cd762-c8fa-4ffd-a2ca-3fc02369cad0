package com.facishare.bpm.model.statics;

import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.flow.postgre.entity.BpmTaskCountEntity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @creat_date: 2019/4/16
 * @creat_time: 10:41
 * @since 6.6
 */
@Data
public class TaskStatics extends WorkflowBaseStatics {
    protected int inProgressTaskCount;
    //进行中全部的任务数 = normalTaskCount + timeoutTaskCount + errorTaskCount
    protected int normalTaskCount;    //进行中正常的（非超时，非异常）任务数
    protected int timeoutTaskCount;   //进行中 超时的任务数
    protected int errorTaskCount;     //进行中异常的任务数

    public static Map<String, TaskStatics> build(List<BpmTaskCountEntity> bpmTaskEntityList, Map<String, String> tasks, String workflowId, String sourceWorkflowId) {
        Map<String, TaskStatics> taskStatsMap = Maps.newHashMap();
        TaskStatics taskStatics;
        for (BpmTaskCountEntity bpmTaskEntity : bpmTaskEntityList) {
            String activityId = bpmTaskEntity.getActivityId();
            if (!tasks.keySet().contains(activityId)) {
                continue;
            }
            taskStatics = taskStatsMap.get(activityId);
            if (Objects.isNull(taskStatics)) {
                taskStatics = new TaskStatics();
                taskStatics.setName(tasks.get(activityId));
                taskStatics.setId(activityId);
                taskStatics.setWorkflowId(bpmTaskEntity.getWorkflowId());
                taskStatics.setSourceWorkflowId(bpmTaskEntity.getSourceWorkflowId());
                taskStatsMap.put(activityId, taskStatics);
            }
            taskStatics.addTaskStateStatics(bpmTaskEntity);
        }

        for (String activityId : tasks.keySet()) {
            if (!taskStatsMap.keySet().contains(activityId)) {
                TaskStatics temp = new TaskStatics();
                temp.setId(activityId);
                temp.setName(tasks.get(activityId));
                temp.setWorkflowId(workflowId);
                temp.setSourceWorkflowId(sourceWorkflowId);
                taskStatsMap.put(activityId,temp );
            }
        }
        return taskStatsMap;
    }

    protected void addTaskStateStatics(BpmTaskCountEntity bpmTaskEntity) {
        inProgressTaskCount += bpmTaskEntity.getStateCount();
        if ("timeout".equals(bpmTaskEntity.getState())) {
            timeoutTaskCount += bpmTaskEntity.getStateCount();
        } else if ("error".equals(bpmTaskEntity.getState())) {
            errorTaskCount += bpmTaskEntity.getStateCount();
        } else {
            normalTaskCount += bpmTaskEntity.getStateCount();
        }
    }

    public static List<BpmTaskCountEntity> errorTask2TaskStateStatics(List<Task> tasks) {
        Map<String, Integer> errorCount = Maps.newHashMap();
        //存在随机性期  同一个activityId对应多个workflowId
        Map<String,String> activityIdAndWorkflowId = Maps.newHashMap();
        tasks.forEach(task -> {
            String activityId = task.getActivityId();
            activityIdAndWorkflowId.put(activityId,task.getWorkflowId());
            errorCount.compute(activityId, (activityId1, count) -> {
                if (Objects.isNull(count)) {
                    count = 1;
                } else {
                    count += 1;
                }
                return count;
            });
        });
        List<BpmTaskCountEntity> data = Lists.newArrayList();
        errorCount.forEach((key, value) -> {
            BpmTaskCountEntity bpmTaskEntity = new BpmTaskCountEntity();
            bpmTaskEntity.setActivityId(key);
            bpmTaskEntity.setState(TaskState.error.name());
            bpmTaskEntity.setStateCount(value);
            bpmTaskEntity.setWorkflowId(activityIdAndWorkflowId.get(key));
            data.add(bpmTaskEntity);
        });
        return data;
    }
}
