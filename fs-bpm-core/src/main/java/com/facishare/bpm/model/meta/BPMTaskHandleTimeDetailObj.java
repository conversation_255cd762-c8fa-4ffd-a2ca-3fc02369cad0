package com.facishare.bpm.model.meta;

import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BPMTaskHandleTimeDetailObj {
    private String _id;
    private String name;
    private String task_id;
    private String task_api_name = TransferDataConstants.APINAME_TASK;
    private String instance_id;
    private String state;
    private Long start_time;
    private Long end_time;
    private List<String> user_id;
    private Long actual_duration;


    public static List<BPMTaskHandleTimeDetailObj> createInsertList(String taskId, String instanceId, List<String> candidateIds, Long startTime, String state, String operateId){
        List<BPMTaskHandleTimeDetailObj> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(candidateIds) || StringUtils.isBlank(operateId)){
            return result;
        }
        Set<String> candidateIdSet = Sets.newHashSet(candidateIds);
        for (String candidateId : candidateIdSet) {
            String id = operateId + "_" + candidateId;
            result.add(new BPMTaskHandleTimeDetailObj(id, id, taskId, TransferDataConstants.APINAME_TASK, instanceId, state, startTime, null, Lists.newArrayList(candidateId), null));
        }
        return result;
    }

    public void fillActualDuration(RemoteContext context, ManageGroupProxy manageGroupProxy){
        if(Objects.nonNull(this.end_time) && Objects.nonNull(this.start_time) && CollectionUtils.isNotEmpty(this.user_id)){
            String userId = this.user_id.get(0);
            Map<String, Long> res = manageGroupProxy.queryUserWorkHours(context, this.start_time, this.end_time, Sets.newHashSet(userId));
            this.actual_duration = Objects.nonNull(res) ? res.get(userId) : null;
        }
    }
}
