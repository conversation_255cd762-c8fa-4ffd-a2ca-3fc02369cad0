package com.facishare.bpm.model.task;

import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import lombok.Data;

import java.util.List;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/3/6 5:18 PM
 */
@Data
public class MetadataInstance {

    private String tenant_id;
    private int row_num;
    private List<String> objectIds;
    private String duration;
    private String sourceWorkflowId;
    private List<String> taskNames;
    private boolean is_deleted;
    private String object_describe_api_name;
    private int total_num;
    private String owner_department;
    private long startTime;
    private List<String> out_owner;
    private InstanceState state;
    private List<String> applicantId;
    private String triggerSource;
    private List<String> owner;
    private long last_modified_time;
    private String lastModifyBy;
    private long create_time;
    private String relatedObject;
    private String objectApiName__r;
    private String workflowName;
    private List<String> last_modified_by;
    private List<String> stageNames;
    private String out_tenant_id;
    private List<String> created_by;
    private String version;
    private List relevant_team;
    private List<String> data_own_department;
    private String object_describe_id;
    private String lastModifyTime;
    private String name;
    private String objectApiName;
    private String _id;
    private String endTime;
    private String objectDataId;
    private String workflowId;
    private List<String> current_candidate_ids;
    private List<String> cancel_from_person;
    private String cancel_reason;
    private String error_reason;

}
