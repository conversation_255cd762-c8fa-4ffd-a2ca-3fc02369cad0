package com.facishare.bpm.model.task;

import lombok.Data;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/9/17 6:12 PM
 */
@Data
public class GetTaskObjectIdByInstanceId {
    private String tenantId;
    private String instanceId;

    public static GetTaskObjectIdByInstanceId of(String tenantId, String instanceId) {
        GetTaskObjectIdByInstanceId arg = new GetTaskObjectIdByInstanceId();
        arg.setInstanceId(instanceId);
        arg.setTenantId(tenantId);
        return arg;
    }
}
