package com.facishare.bpm.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by cuiyongxu on 17/5/4.
 */
@Data
public class OrgOutline  implements Serializable {
    private List<Integer> person;

    private List<String> CRMGroup;

    private List<String> role;

    private List<Integer> dept;

    private List<String> externalPerson;

    private String linkAppId;
    private Integer linkAppType;
    private List<String> externalRole;

}
