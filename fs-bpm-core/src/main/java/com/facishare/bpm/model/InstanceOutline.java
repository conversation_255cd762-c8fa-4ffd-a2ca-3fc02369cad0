package com.facishare.bpm.model;

import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import lombok.Data;

import java.util.List;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/16 5:00 PM
 */
@Data
public class InstanceOutline {

    private String id;
    private String entityId;
    private String objectId;
    private String workflowId;
    private String workflowName;
    private Long startTime;
    private Long endTime;
    private InstanceState state;
    private List<Pool> pools;
    private MoreOperationManager.MoreOperation moreOperations;

}
