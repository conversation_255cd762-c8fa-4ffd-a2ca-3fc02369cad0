package com.facishare.bpm.model;

import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by <PERSON> on 21/12/2016.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowExtension {
    private String id;
    @I18NExpression(drill = true)
    private List<PoolEntity> pools;
    private Object diagram;
    private String svg;
    private String workflowId;

    public WorkflowExtension(List<PoolEntity> pools, Object diagram) {
        this.pools = pools;
        this.diagram = diagram;
    }


    public static WorkflowExtension fromEntity(FlowExtensionEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtils.transfer(entity, WorkflowExtension.class, (src, ret) -> {
            ret.setPools(src.getPools());
            ret.setDiagram(src.getDiagram());
            ret.setId(src.getId());
            ret.setWorkflowId(src.getWorkflowId());
            ret.setSvg(src.getSvg());
        });
    }

}
