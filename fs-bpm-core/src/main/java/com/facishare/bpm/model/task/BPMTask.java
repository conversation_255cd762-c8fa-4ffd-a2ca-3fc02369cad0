package com.facishare.bpm.model.task;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.SimpleTransition;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.util.verifiy.TaskType;
import com.facishare.bpm.utils.DataCacheHandler;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.MetadataUtils;
import com.facishare.bpm.utils.TaskUtils;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.persistence.Transient;
import java.util.*;

import static com.facishare.bpm.i18n.BPMI18N.*;

/**
 * Created by Aaron on 28/12/2016
 */
@Setter
@Getter
@Slf4j
public class BPMTask {
    private String id; //任务Id
    @I18NExpression(relation = {"${workflowId.activityId}.name", "${sourceWorkflowId.activityId}.name"})
    private String name;//任务名
    @I18NExpression(relation = {"${workflowId.activityId}.description", "${sourceWorkflowId.activityId}.description"})
    private String description;//任务描述
    private Long createTime;//当前任务创建时间
    private Long modifyTime;
    private List<String> candidateIds;//候选人
    private List<String> unProcessIds;//待处理人
    private List<String> processIds;//执行人
    private List<String> assigneeIds;//执行人
    private Boolean completed;
    private Boolean canceled;
    private String taskType;
    private String activityId;//当前任务结点Id
    private String activityInstanceId;
    private String workflowId;//运行时的流程Id
    private String sourceWorkflowId;
    protected String entityId;//当前任务结点对象类型
    private String entityName;
    private String objectName;
    protected String objectId;//当前任务结点对象数据Id
    private Object remindLatency;
    //unit:1-天；2-小时；3-分钟
    private Integer latencyUnit;
    private Integer candidateCount;
    private List<Opinion> opinions; //审批意见
    private Map<String, List<String>> assignee;

    private TaskState state;
    private Boolean isTaskOwner = Boolean.FALSE; //是否是负责人
    private Map<String, Object> extension;//当前任务结点的扩展字段
    private Map<String, ActionButton> buttonMap;
    private String errorMsg;
    private AfterActionExecution.SimpleAfterActionExecution execution;
    private Map<String, Object> rule;
    private Map<String, Object> rejectRule;
    private transient boolean timeout;
    private Integer assignNextTask;//是否需要指定下一节点处理人1 指定，非1 不指定
    private Object reminders;
    private String workflowInstanceId;
    private List<Task.ApproverModifyLog> candidateModifyLog;
    @Transient
    private Map<String, Object> data;
    private List<SimpleTransition> transitions;
    @Transient
    private Map<String, Object> currentEntityDescribe;
    @DocDescribe(label = "指定下一节点处理人范围")
    private Set<String> nextTaskAssigneeScope;

    @Transient
    @DocDescribe(label = "6.6 新增,只新建时下发要新建的对象的describe")
    private Map<String, Object> relatedDescribe;

    @DocDescribe(label = "6.6 新增,添加当前任务的describe,销售线索终端使用")
    private Map<String, Object> describe;

    @DocDescribe(label = "6.7,由于支持了布局规则,需要在老接口中添加此字段")
    protected Map<String, Object> layout;
    /**
     * 是否引用布局规则
     */
    private Boolean enableLayoutRules;

    /**
     * 互联应用名称
     */
    private String linkAppName;

    private Boolean showDataChangeLog;

    private Integer linkAppType;
    private String linkApp;
    private Boolean linkAppEnable;

    /**
     * 去处理按钮跳转地址
     */
    private String todoJumpUrl;

    private Boolean delay;//是否是延迟节点
    private String latencyInfo;//延迟节点等待时常信息

    @Override
    public String toString() {
        return JsonUtil.toJson(this);
    }

    public void setExtension(Map<String, Object> extension) {
        extension.remove(WorkflowKey.variables);
        this.extension = extension;
    }

    public void setButtons(List<ActionButton> buttons) {
        this.buttonMap = Maps.newLinkedHashMap();
        buttons.forEach(item -> this.buttonMap.put(item.getAction(), item));
    }

    public Collection<ActionButton> getButtons() {
        return this.buttonMap == null ? null : this.buttonMap.values();
    }

    public boolean assignNextTask() {
        return assignNextTask != null && assignNextTask == 1;
    }


    public boolean onlyRelatedObject() {
        return MapUtil.instance.getBool(this.getExtension(), ExtensionKey.onlyRelatedObject);
    }

    /**
     * @param taskDatas      会获取一次数据,获取数据的同时 也会获取描述信息,通过serviceManager获取到缓存中的描述,然后获取displayName即可,
     *                       关联对象的displayName,只有查找关联或主从才需要赋值
     *                       <p>
     *                       如果任务已完成,getTaskData一步已获取当前对象描述,entityName 是直接存在的
     *                       如果任务已完成,relatedEntityName已存在
     *                       <p>
     *                       如果任务未完成,getTaskData一步已获取当前对象描述,entityName 是直接存在的
     *                       如果任务未完成,且存在查找关联,则通过调用setRelatedEntityName设置
     * @param serviceManager
     */
    public void setTaskObjectProperty(Map<Pair<String, String>, Map<String, Object>> taskDatas, RefServiceManager serviceManager) {

        String entityId = getEntityId();
        String objectId = getObjectId();
        String relatedEntityId = String.valueOf(extension.get(ExtensionKey.relatedEntityId));
        //
        Map describe = serviceManager.findDescribe(entityId, true, true);
        String entityName = (String) describe.getOrDefault(BPMConstants.MetadataKey.displayName, BPMConstants.REPLACE_WHEN_NOT_FOUND);
        extension.put(ExtensionKey.entityName, entityName);
        extension.put(ExtensionKey.objectId, objectId);

        taskDatas.forEach((entityIdObjectId, data) -> {
            if (entityIdObjectId.getValue().equals(objectId)) {
                //id在前面已经填充，不再填充。当没有数据权限时，从data中拿不到id
                extension.put(ExtensionKey.objectName, MetadataUtils.getObjectName(data, BPMConstants.REPLACE_WHEN_NO_PERMISSION));

            } else if (entityIdObjectId.getKey().equals(relatedEntityId)) {
                extension.put(ExtensionKey.relatedObjectId, data.get(BPMConstants.MetadataKey.id));
                extension.put(ExtensionKey.relatedObjectName, MetadataUtils.getObjectName(data, BPMConstants.REPLACE_WHEN_NO_PERMISSION));
            }
        });
    }

    public void setRelatedEntityName(Map describe) {
        if (MapUtils.isNotEmpty(describe)) {
            String relatedEntityName = (String) describe.getOrDefault(BPMConstants.MetadataKey.displayName, BPMConstants.REPLACE_WHEN_NOT_FOUND);
            extension.put(ExtensionKey.relatedEntityName, relatedEntityName);
        } else {
            extension.put(ExtensionKey.relatedEntityName, BPMConstants.REPLACE_WHEN_NOT_FOUND);
        }
    }

    public void setApproveTaskOpinion() {
        if (!getExecutionType().isApprove()) {
            return;
        }
        if (TaskType.all.name().equals(this.getTaskType())) {
            this.candidateCount = candidateIds.size();
            List<Opinion> opinions = this.getOpinions();
            if (opinions == null) {
                opinions = Lists.newArrayList();
            }
            this.setOpinions(opinions);
        }
    }

    /**
     * 对国家省市区的类型不作处理,原来逻辑是将type = select_one
     *
     * @param serviceManager
     */
    public void setAreaOptions(RefServiceManager serviceManager) {
        List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) extension.get(ExtensionKey.form);
        if (forms != null) {
            forms.forEach(form -> form.forEach(formItem -> {
                String fieldType = (String) formItem.get(ExtensionKey.type);
                if (BPMConstants.MetadataKey.city.equals(fieldType) || BPMConstants.MetadataKey.country.equals(fieldType)
                        || BPMConstants.MetadataKey.district.equals(fieldType) || BPMConstants.MetadataKey.province.equals(fieldType)) {
                    Map<String, Object> areaDesc = serviceManager.getAreaOption(fieldType);
                    formItem.put(ExtensionKey.options, areaDesc.get(ExtensionKey.options));
                    if (!Objects.isNull(areaDesc.get(BPMConstants.MetadataKey.cascade_parent_api_name))) {
                        formItem.put(BPMConstants.MetadataKey.cascade_parent_api_name, areaDesc.get(BPMConstants.MetadataKey.cascade_parent_api_name));
                    }
                }
            }));
        }
    }

    /**
     * 给终端
     */
    public void setAreaDefaultValueOptions(RefServiceManager refServiceManager) {
        List<List<Map<String, Object>>> forms = (List<List<Map<String, Object>>>) extension.get(ExtensionKey.form);
        if (forms != null) {
            forms.forEach(form -> form.forEach(formItem -> {
                String fieldType = (String) formItem.get(ExtensionKey.type);
                if (BPMConstants.MetadataKey.city.equals(fieldType) || BPMConstants.MetadataKey.country.equals(fieldType)
                        || BPMConstants.MetadataKey.district.equals(fieldType) || BPMConstants.MetadataKey.province.equals(fieldType)) {
                    String value = String.valueOf(formItem.get(ExtensionKey.value));
                    if (!Strings.isNullOrEmpty(value)) {
                        formItem.put(ExtensionKey.options, refServiceManager.getAreaOption(fieldType).get(ExtensionKey.options));
                    }
                }
            }));
        }
    }


    public ExecutionTypeEnum getExecutionType() {
        return ExecutionTypeEnum.valueOf(this.getExtension().get(ExtensionKey.executionType) + "");
    }

    public boolean hasProcessed(String userId) {
        if (CollectionUtils.isEmpty(this.getAssigneeIds())) {
            return false;
        }
        return this.getAssigneeIds().contains(userId);
    }

    public boolean starting() {
        if (state.equals(TaskState.in_progress) || state.equals(TaskState.error)) {
            return true;
        }
        return false;
    }

    public boolean isCombinedActionCode() {
        return BPMConstants.MetadataKey.APINAME_LEADSOBJ.equals(extension.get(ExtensionKey.entityId))
                && BPMConstants.MetadataKey.leadsObjHandleActionCode.equals(extension.get(ExtensionKey
                .actionCode));
    }

    public static boolean isExternalApplyTask(ActivityExt activity) {
        if (activity.instanceOf(UserTaskExt.class)) {
            Map<String, Object> extension = (Map<String, Object>) activity.getProperty(ExtensionKey.bpmExtension);
            if (extension.get(ExtensionKey.executionType) == null) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_FOUND_TASKTYPE);
            }
            return extension.get(ExtensionKey.executionType).equals(ExecutionTypeEnum.externalApplyTask.name());
        }
        return false;
    }

    private static String getAppProperties(Map<String, Object> bpmExtensition, String key) {
        Object bpmAppData = bpmExtensition.get(ExtensionKey.externalApply);
        if (bpmAppData instanceof Map) {
            return (String) ((Map) bpmAppData).get(key);
        }
        return null;
    }

    public static String getAppCode(Map<String, Object> bpmExtension) {
        return getAppProperties(bpmExtension, ExtensionKey.appCode);
    }

    public static String getChildrenActionCode(Map<String, Object> bpmExtension) {
        return getAppProperties(bpmExtension, ExtensionKey.childrenActionCode);
    }

    public static String getChildrenActionName(Map<String, Object> bpmExtension) {
        return getAppProperties(bpmExtension, ExtensionKey.childrenActionName);
    }

    public static String getAppActionCode(Map<String, Object> bpmExtension) {
        return getAppProperties(bpmExtension, ExtensionKey.appActionCode);
    }

    public List<String> getProcesserIds() {
        if (assigneeIds == null) {
            this.processIds = Lists.newArrayList();
        }
        return processIds;
    }

    public static BPMTask fromTask(RemoteContext context, Task task) {
        BPMTask bpmTask = BeanUtils.transfer(task, BPMTask.class);
        //设置下任务完成的状态
        bpmTask.setCompleted(task.getCompleted());
        bpmTask.setExtension(task.getBpmExtension());
        bpmTask.setExecution(task.getExecution());
        bpmTask.setAssignNextTask(task.getAssignNextTask());
        bpmTask.setApproveTaskOpinion();
        bpmTask.setProcessIds(task.getProcessIds());
        bpmTask.setUnProcessIds(task.getUnProcessIds());
        bpmTask.setCandidateModifyLog(task.getApproverModifyLog());
        bpmTask.setReminders(task.getReminders());
        bpmTask.setIsTaskOwner(context);
        bpmTask.setDelay(task.getDelay());
        if(Boolean.TRUE.equals(task.getDelay())){
            bpmTask.setLatencyInfo(splicingLatencyStr(task.getLatencyTime(), task.getLatencyUnit()));
        }

        return bpmTask;
    }

    public void setExecution(AfterActionExecution execution) {
        if (execution != null) {
            this.execution = execution.getErrorOrWaitingSimpleAfter();
            /**
             * 有后动动作并且当前任务已经完成，任务状态是异常的
             */
            if (completed != null && completed) {
                //后动作异常或等待，任务状态为进行中
                if (!this.execution.isPass()) {
                    //如果是等待中,则提示文案
                    if (execution.getSimple().isWaiting()) {
                        setState(TaskState.in_progress);
                        setErrorMsg(BPMI18N.PAAS_FLOW_BPM_AFTER_ACTION_IS_WAITING.text());
                    } else {
                        setState(TaskState.error);
                        List<AfterAction> actions = execution.getSimple().getActions();
                        actions.forEach(action -> {
                            String taskType = action.getTaskType();
                            if ("custom_function".equals(taskType)) {
                                log.info("taskId:{},errorMsg:{}", getId(), action.getActionErrorMsg());
                                action.setActionErrorMsg(BPMI18N.PAAS_FLOW_AFTER_SYSTEM_FUNCTION_EXCEPTION.text());
                            }
                        });
                        setErrorMsg(BPMI18N.PAAS_FLOW_BPM_AFTER_ERROR.text());
                    }
                }
            }
        } else {
            this.execution = new AfterActionExecution.SimpleAfterActionExecution();
        }

    }

    public void setAction(String actionCode, String actionLabel) {
        extension.put(ExtensionKey.actionLabel, actionLabel);
        extension.put(ExtensionKey.actionCode, actionCode);
    }

    public void setObjectData(Map<String, Object> object) {

        if (MapUtils.isNotEmpty(object) && object.get(BPMConstants.MetadataKey.objectDescribeApiName) == null) {
            object.put(BPMConstants.MetadataKey.objectDescribeApiName, entityId);
        }

        extension.put(ExtensionKey.objectData, object);
    }


    public void setIsTaskOwner(RemoteContext context) {
        List<String> candidateIds = this.getCandidateIds();
        if (candidateIds != null) {
            //下游人员调用，用下游人员信息
            isTaskOwner = (candidateIds.contains(context.getOuterUserId() != 0 ? String.valueOf(context.getOuterUserId()) : context.getUserId()));
        }
    }

    public void setTaskOwner(Boolean taskOwner) {
        isTaskOwner = taskOwner;
    }

    public void setReleatedFieldApiName(Map<String, Object> relatedDesc, String relatedListName) {
        String referenceField = getReferenceAndMasterDetailField(relatedDesc, relatedListName);
        /**
         * 被关联对象拉取列表时会使用到
         */
        extension.put(ExtensionKey.relatedFieldApiName, referenceField);
    }

    public String getReferenceAndMasterDetailField(Map<String, Object> desc, String targetRelatedListName) {
        if (Strings.isNullOrEmpty(targetRelatedListName) || Objects.isNull(desc)) {
            return null;
        }
        Map<String, Map<String, Object>> fields = MapUtil.instance.getMapOfGeneric(desc, BPMConstants.MetadataKey.fields);
        for (Map.Entry<String, Map<String, Object>> entry : fields.entrySet()) {
            String fieldApiName = entry.getKey();
            Map<String, Object> fieldDesc = entry.getValue();
            if (targetRelatedListName.equals(fieldDesc.get(ExtensionKey.relatedListName))
                    && MapUtil.instance.getBool(fieldDesc, BPMConstants.MetadataKey.isActive)) {
                return fieldApiName;
            }
        }

        return null;
    }

    public void setTaskForm(BPMTask task, Map<String, Object> desc,
                            Map<String, Integer> fieldsAuth,
                            DataCacheHandler formHandler) {
        //        处理描述不存在的情况
        if (Objects.isNull(desc)) {
            desc = Maps.newHashMap();
            desc.put(BPMConstants.MetadataKey.fields, Maps.newHashMap());
        }
        task.setCurrentEntityDescribe(desc);
        if (!Boolean.TRUE.equals(task.getCompleted())) {
            TaskUtils.setUncompletedTaskFrom(task, desc, fieldsAuth, formHandler);
        } else {
            Map<String, Object> taskSnapshotData = Maps.newHashMap();
            Map<String, Map<String, Object>> dataCache = formHandler.getDataCache();
            if (MapUtils.isNotEmpty(dataCache) && CollectionUtils.isNotEmpty(dataCache.values())) {
                taskSnapshotData = (Map<String, Object>) dataCache.values().toArray()[0];
            }
            TaskUtils.setCompletedTaskForm(task, desc, fieldsAuth, taskSnapshotData, formHandler);
        }
    }

    public void setIsTaskOwner(Boolean isTaskOwner) {
        this.isTaskOwner = isTaskOwner;
    }

    public static ExecutionTypeEnum getExecutionType(Map<String, Object> bpmExtension) {
        return ExecutionTypeEnum.valueOf(String.valueOf(bpmExtension.get(ExtensionKey.executionType)));
    }

    public static String getExecutionTypeByLatencyTask(Map<String, Object> bpmExtension) {
        if (MapUtils.isEmpty(bpmExtension)) {
            return null;
        }
        Object executionType = bpmExtension.get(ExtensionKey.executionType);
        if (executionType == null) {
            return null;
        }
        return ExecutionTypeEnum.valueOf(String.valueOf(executionType)).name();
    }

    public static String getActionCode(Map<String, Object> bpmExtension) {
        return String.valueOf(bpmExtension.get(ExtensionKey.actionCode));
    }

    public static String getActionLabel(Map<String, Object> bpmExtension) {
        return String.valueOf(bpmExtension.get(ExtensionKey.actionLabel));
    }

    protected void setReminders(Object reminders) {
        this.reminders = reminders;
    }

    public static boolean isApprovelActivity(ActivityExt activity) {
        if (activity.instanceOf(UserTaskExt.class) &&
                ExecutionTypeEnum.approve.name().equals(((Map) activity.getProperty(ExtensionKey.bpmExtension)).get(ExtensionKey.executionType))) {
            return true;
        }
        return false;
    }

    public void assignNextTask(boolean needAssignNextTask, Map<String, Object> nextTaskAssigneeScope, RefServiceManager serviceManager) {
        if (needAssignNextTask) {
            if (MapUtils.isNotEmpty(nextTaskAssigneeScope)) {
                Set<String> allPerson = serviceManager.getPersons(nextTaskAssigneeScope);
                // 安卓和ios如果下发一个 {}的话,还需要特殊判断,如果直接下发null,则直接认为没有处理人
                if (CollectionUtils.isEmpty(allPerson)) {
                    this.setNextTaskAssigneeScope(null);
                } else {
                    this.setNextTaskAssigneeScope(allPerson);
                }
            }
        }
    }

    public List<Object> getPersons() {
        List<Object> persons = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(getCandidateIds())) {
            persons.addAll(getCandidateIds());
        }
        if (CollectionUtils.isNotEmpty(getProcessIds())) {
            persons.addAll(getProcessIds());
        }
        if (CollectionUtils.isNotEmpty(getNextTaskAssigneeScope())) {
            persons.addAll(getNextTaskAssigneeScope());
        }
        if (CollectionUtils.isNotEmpty(getAssigneeIds())) {
            persons.addAll(getAssigneeIds());
        }
        if (CollectionUtils.isNotEmpty(getUnProcessIds())) {
            persons.addAll(getUnProcessIds());
        }
        return persons;
    }


    public static List<Object> getPersons(List<BPMTask> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return Lists.newArrayList();
        }
        return tasks.stream().collect(Lists::newArrayList, (list, task) ->
                list.addAll(task.getPersons()), List::addAll);
    }

    public AfterActionExecution.SimpleAfterActionExecution getExecution() {
        if (this.execution == null) {
            return new AfterActionExecution.SimpleAfterActionExecution();
        }
        return this.execution;
    }

    public boolean isInProgress() {
        return TaskState.in_progress == this.state;
    }

    public boolean isError() {
        return TaskState.error == this.state;
    }

    public void sortPersons(boolean isUpStreamTenant) {
        TaskUtils.sortPersons(isUpStreamTenant, getCandidateIds());
        TaskUtils.sortPersons(isUpStreamTenant, getProcessIds());
        TaskUtils.sortPersons(isUpStreamTenant, getAssigneeIds());
        TaskUtils.sortPersons(isUpStreamTenant, getUnProcessIds());
    }

    public String getAppCode() {
        if (MapUtils.isNotEmpty(this.extension)) {
            Map<String, Object> externalApply = MapUtil.instance.getMap(this.extension, BPMConstants.EXTERNAL_APPLY);
            if (MapUtils.isNotEmpty(externalApply)) {
                return MapUtil.instance.getString(externalApply, BPMConstants.APPCODE);
            }
        }
        return StringUtils.EMPTY;
    }

    public String getActionCode() {
        if (MapUtils.isNotEmpty(this.extension)) {
            Map<String, Object> externalApply = MapUtil.instance.getMap(this.extension, BPMConstants.EXTERNAL_APPLY);
            if (MapUtils.isNotEmpty(externalApply)) {
                return MapUtil.instance.getString(externalApply, BPMConstants.ACTIONCODE);
            }
        }
        return StringUtils.EMPTY;
    }

    public boolean isExternalApplyTask() {
        if (MapUtils.isNotEmpty(this.extension)) {
            String executionType = MapUtil.instance.getString(this.extension, BPMConstants.EXECUTIONTYPE);
            return ExecutionTypeEnum.externalApplyTask.name().equals(executionType);
        }
        return false;
    }

    public boolean needAssignNextTask() {
        return assignNextTask != null && assignNextTask == 1;
    }

    public static String splicingLatencyStr(Object latencyTime, Integer latencyUnit){
        String result = "";
        if (Objects.isNull(latencyTime) || Objects.isNull(latencyUnit)) {
            return result;
        }
        return result + latencyTime.toString() + latencyUnitToStr(latencyUnit);

    }

    public static String latencyUnitToStr(Integer latencyUnit){
        switch (latencyUnit){
            case 1:
                return BPM_TIME_UNIT_DAY.text();
            case 2:
                return BPM_TIME_UNIT_HOUR.text();
            case 3:
                return BPM_TIME_UNIT_MINUTE.text();
        }
        return "";

    }

    public void extensionFormAddApiName(){
        if(MapUtils.isEmpty(this.extension) || !this.extension.containsKey("form")){
            return;
        }
        if(!(this.extension.get("form") instanceof List)){
            return;
        }
        List formList = (List) this.extension.get("form");
        if(CollectionUtils.isEmpty(formList)){
            return;
        }
        for (Object form : formList) {
            if(!(form instanceof List)){
               continue;
            }
            List list = (List) form;
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            for (Object item : list) {
                if(!(item instanceof Map)){
                    continue;
                }
                Map itemMap = (Map) item;
                if(MapUtils.isEmpty(itemMap)){
                    continue;
                }
                if(itemMap.containsKey("type") && ("object_reference".equals(itemMap.get("type")) || "object_reference_many".equals(itemMap.get("type"))) && itemMap.containsKey("name") && !itemMap.containsKey("api_name")){
                    itemMap.put("api_name", itemMap.get("name"));
                }
            }
        }
    }
}
