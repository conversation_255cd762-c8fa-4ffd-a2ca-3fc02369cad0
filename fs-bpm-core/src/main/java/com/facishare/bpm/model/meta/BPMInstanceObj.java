package com.facishare.bpm.model.meta;

import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @creat_date: 2021/9/20
 * @creat_time: 09:05
 * @since 7.5.0
 */
@Data
public class BPMInstanceObj extends BaseBPMObj {
  private Integer externalFlow;
  private TriggerSource triggerSource;
  private String activity_instance_id;
  private List<String> stageNames;
  private List<String> taskNames;
  private InstanceState state;
  private Set<String> applicantId;
  private String cancel_reason;
  private List<String> cancel_from_person;
  private Map stageNames__lang;
  private Map taskNames__lang;


  public BPMInstanceObj() {
  }
}
