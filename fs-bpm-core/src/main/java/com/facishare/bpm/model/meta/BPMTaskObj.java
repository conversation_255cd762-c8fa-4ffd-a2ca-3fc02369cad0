package com.facishare.bpm.model.meta;

import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @creat_date: 2021/9/20
 * @creat_time: 09:05
 * @since 7.5.0
 */
@Data
public class BPMTaskObj extends BaseBPMObj{
  private TaskState state;
  private Integer activity_instance_id;
  private String task_type;
  private String taskName;
  private String activityId;
  private String workflowInstanceId;
  private String stageId;
  private String stageName;
  private String execution_type;
  private String workflowInstanceName;
  private String action_code;
  private String session_key;
  private Double remindLatency;
  private Double timeoutTime;
  private Boolean isTimeout;
  public Map<String, List<String>> assignee;
  private Set<String> processorIds;
  private List<String> candidateIds;
  private String action_type;
  private String object_record_type;
  private String business_code;
  private Long suspend_accumulate_time;
  private Long last_suspend_time;
  private Map taskName__lang;
  private Map workflowInstanceName__lang;
  private Map stageName__lang;
  private Boolean is_tag;

  public BPMTaskObj() {
  }

  public Set<String> getProcessorIds() {
    if(Objects.isNull(processorIds)){
      processorIds= Sets.newHashSet();
    }
    return processorIds;
  }

  public void setProcessorIds(Set<String> processorIds) {
    this.processorIds = processorIds;
  }

  public void setProcessorIds(List<String> processorIds) {
    if(Objects.isNull(processorIds)){
      processorIds= Lists.newArrayList();
    }
    this.processorIds = processorIds.stream().collect(Collectors.toSet());
  }

  /**
   * 最大 999999 天
   */
  public void setRemindLatency(Double remindLatency) {
    if(remindLatency>8.63999136E13D){
      remindLatency=8.63999136E13D;
    }
    this.remindLatency = remindLatency;
  }
}
