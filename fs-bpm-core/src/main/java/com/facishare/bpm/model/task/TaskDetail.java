package com.facishare.bpm.model.task;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.handler.task.button.TaskButtonHandler;
import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.manage.impl.MoreOperationManagerImpl;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.udobj.QueryConfigData;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.utils.TaskUtils;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.TASK_FEED_DETAIL;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/18 5:37 PM
 */
@Slf4j
@Data
public class TaskDetail extends BaseTask {
    @I18NExpression(relation = {"${workflowId.activityId}.name", "${sourceWorkflowId.activityId}.name"})
    private String name;
    /**
     * 6.8 添加当前登入人 是否为任务处理人  为卡梅隆提供 lirujian
     */
    private Boolean isOwner = false;
    @I18NExpression(relation = {"${workflowId.activityId}.description", "${sourceWorkflowId.activityId}.description"})
    private String description;
    @I18NExpression(drill = true)
    private List<PoolEntity> pools;
    private String applicantId;
    @DocDescribe(desc = "1-天；2-小时；3-分钟")
    private Integer latencyUnit;
    private Object remindLatency;
    private boolean isTimeout;
    private Map<String, Employee> employeeInfo;
    /**
     * 是否有数据权限
     */
    private boolean hasDataPrivilege;

    /**
     * 流程有关的配置
     */
    private Map<String,Object> config;

    private String message;

    private Long suspendAccumulateTime;
    private Long lastSuspendTime;
    @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
    private String workflowName;
    private Long instanceStartTime;

    public static TaskDetail fromTaskDetail(RefServiceManager serviceManager, Task task) {
        TaskDetail taskDetail = BeanUtils.transfer(task, TaskDetail.class, (src, rst) -> {
            if (!task.getExecutionType().isExternalApplyTask()
                    && !task.getExecutionType().isExecutionTask()
                    && CollectionUtils.isEmpty(rst.getCandidateIds())) {
                rst.setErrorMsg(StringUtils.isNotBlank(task.getErrMsg()) ? task.getErrMsg() : BPMI18N.PAAS_FLOW_BPM_HAS_NO_HANDLER.text());
            }
            //节点进行中或异常
            if (TaskState.error == src.getState() || TaskState.in_progress == src.getState() || TaskState.suspend == src.getState()) {
                rst.isTimeout = src.isTimeout();
            } else {
                rst.isTimeout = false;
            }
            rst.setExternalApply(src.getExternalApply());
        });

        if (task.needAssignNextTask() && TaskState.in_progress.equals(task.getState())) {
            Map<String, Object> nextTaskAssigneeScope = task.getNextTaskAssigneeScope();
            taskDetail.setAssignNextTask(1);
            if (MapUtils.isNotEmpty(nextTaskAssigneeScope)) {
                Set<String> allPerson = serviceManager.getPersons(nextTaskAssigneeScope);
                // 安卓和ios如果下发一个 {}的话,还需要特殊判断,如果直接下发null,则直接认为没有处理人
                if (CollectionUtils.isEmpty(allPerson)) {
                    taskDetail.setNextTaskAssigneeScope(null);
                } else {
                    taskDetail.setNextTaskAssigneeScope(allPerson);
                }
            }
        }

        taskDetail.setIsOwner(TaskButtonHandler.isTaskOwner(serviceManager, taskDetail.getCandidateIds()));
        if(task.getExecutionType().isExecutionTask() && CollectionUtils.isNotEmpty(task.getExecutionList())){
            taskDetail.setExecution(AfterActionExecution.pass(task.getExecutionList()), !task.getDelay() || Objects.nonNull(task.getFinishedTime()));
        }else {
            taskDetail.setExecution(task.getExecution(), task.getCompleted());
        }
        taskDetail.setSuspendAccumulateTime(task.getRegularSuspendDuration());
        taskDetail.setLastSuspendTime(task.getLastSuspendTime());
        if(CollectionUtils.isNotEmpty(task.getOperateLogs())){
            taskDetail.setTagInfos(task.getOperateLogs().stream().filter(o -> BPMConstants.ADD_TAG_OPERATE.equals(o.getType())).collect(Collectors.toList()));
        }
        taskDetail.setRemindLogs(TaskState.in_progress.equals(task.getState()) && CollectionUtils.isNotEmpty(task.getCandidateIds()) && CollectionUtils.isNotEmpty(task.getRemindLogs())
                ? task.getRemindLogs()
                : null);
        return taskDetail;
    }



    private void setExecution(AfterActionExecution execution, Boolean completed) {
        if (execution != null) {
            AfterActionExecution.SimpleAfterActionExecution simpleAfter = execution.getErrorOrWaitingSimpleAfter();
            this.execution = simpleAfter;
            /**
             * 有后动动作并且当前任务已经完成，任务状态是异常的
             */
            if (completed != null && completed) {
                if (!this.execution.isPass()) {
                    //如果是等待中,则提示文案
                    if (simpleAfter.isWaiting()) {
                        setState(TaskState.in_progress);
                        setErrorMsg(BPMI18N.PAAS_FLOW_BPM_AFTER_ACTION_IS_WAITING.text());
                    } else {
                        setState(TaskState.error);
                        List<AfterAction> actions = simpleAfter.getActions();
                        actions.forEach(action -> {
                            String taskType = action.getTaskType();
                            if ("custom_function".equals(taskType)) {
                                log.info("taskId:{},errorMsg:{}", getId(), action.getActionErrorMsg());
                                action.setActionErrorMsg(BPMI18N.PAAS_FLOW_AFTER_SYSTEM_FUNCTION_EXCEPTION.text());
                            }
                        });
                        setErrorMsg(getAfterActionErrorMessage(actions));
                    }
                }
            }
        } else {
            this.execution = new AfterActionExecution.SimpleAfterActionExecution();
        }
    }

    private String getAfterActionErrorMessage(List<AfterAction> actions) {
        if (CollectionUtils.isEmpty(actions)) {
            return null;
        }
        return actions.get(0).getActionErrorMsg();
    }

    public Integer getLatencyUnit() {
        return  latencyUnit == null ? 2 : latencyUnit;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        if (!Strings.isNullOrEmpty(errorMsg)) {
            this.errorMsg = errorMsg;
        }
    }



    /**
     * 获取人员信息
     */
    public  List<Object> getPersons() {
        List<Object> useIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(getCandidateIds())) {
            useIds.addAll(getCandidateIds());
        }
        if (CollectionUtils.isNotEmpty(getProcessIds())) {
            useIds.addAll(getProcessIds());
        }
        if (CollectionUtils.isNotEmpty(getNextTaskAssigneeScope())) {
            useIds.addAll(getNextTaskAssigneeScope());
        }
        if (StringUtils.isNotBlank(getApplicantId())) {
            useIds.add(getApplicantId());
        }
        return useIds;
    }


    public boolean isInProgress() {
        return TaskState.in_progress == this.state;
    }

    public boolean isError() {
        return TaskState.error == this.state;
    }

    public void sortPersons(boolean isUpStreamTenant) {
        TaskUtils.sortPersons(isUpStreamTenant, getCandidateIds());
        TaskUtils.sortPersons(isUpStreamTenant, getProcessIds());
    }

    public boolean isExternalApplyTask() {
        return ExecutionTypeEnum.externalApplyTask.equals(this.executionType);
    }
    public boolean isAfterActionWaiting() {
        if (Objects.nonNull(execution)) {
            return execution.isWaiting();
        } else {
            return false;
        }
    }

    public void setTaskFeedDetailConfig(List<QueryConfigData.FeedDetailConfig> feedDetailConfig) {
        if(Objects.isNull(config)){
            config= Maps.newHashMap();
        }
        boolean hasViewBPMInstanceLogPermission=hasViewBPMInstanceLogPermission();
        feedDetailConfig=feedDetailConfig.stream()
                .filter(item-> item.isSelected())
                .filter(item-> !Objects.equals(item.getCode(), QueryConfigData.CONFIG_VALUES.COMMENTS_INFO)||hasViewBPMInstanceLogPermission)
                .collect(Collectors.toList());
        config.put(TASK_FEED_DETAIL,feedDetailConfig);
    }

    public boolean hasViewBPMInstanceLogPermission(){
        StandardData taskRelatedData = this.getData();
        if(Objects.nonNull(taskRelatedData)&&Objects.nonNull(taskRelatedData.getObjectPermissions())){
            return taskRelatedData.getObjectPermissions().getOrDefault(MoreOperationManagerImpl.viewBPMInstanceLog.getCode(),false);
        }
        return false;
    }
}
