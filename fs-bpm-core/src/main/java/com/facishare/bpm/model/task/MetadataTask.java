package com.facishare.bpm.model.task;

import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.utils.DataTransferUtils;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.postgre.entity.BpmTaskCountEntity;
import com.google.common.collect.Lists;
import lombok.Data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class MetadataTask {

    private String _id;
    private String object_describe_api_name;
    private String object_describe_id;
    private String tenant_id;
    private String activityId;
    private Map assignees;
    private Long duration;
    private String endTime;
    private boolean timeout;
    private String name;
    private String objectApiName;
    private String objectDataId;
    private List<String> objectIds;
    private List<String> owner;
    private List<String> processorIds;
    private String record_type;
    private String relatedObject;
    private List relevant_team;
    private Object remindLatency;
    private String sourceWorkflowId;
    private String stageId;
    private String stageName;
    private long startTime;
    private TaskState state;
    private String taskName;
    private Object timeoutTime;
    private String workflowId;
    private String workflowInstanceId;
    private String workflowInstanceName;
    private List<String> created_by;
    private List<String> last_modified_by;
    private String version;
    private long create_time;
    private long last_modified_time;
    private boolean is_deleted;
    private String out_tenant_id;
    private List<String> out_owner;
    private List<String> data_own_department;
    private String objectApiName__r;


    public boolean isError() {
        return state.equals(TaskState.error);//|| isAfterActionError();
    }

    public static MetadataTask formTask(Task task, List<PoolEntity> pools) {
        MetadataTask metadataTask = new MetadataTask();
        metadataTask.set_id(task.getId());
        //metadataTask.setObject_describe_api_name();
        //metadataTask.setObject_describe_id();
        metadataTask.setTenant_id(task.getTenantId());
        metadataTask.setActivityId(task.getActivityId());
        metadataTask.setAssignees(task.getAssignee());
        metadataTask.setDuration(task.getModifyTime() - task.getCreateTime());
        //metadataTask.setEndTime();
        metadataTask.setTimeout(task.isTimeout());
        metadataTask.setName(getThemeFromName(task.getName(), task.getCreateTime()));
        metadataTask.setObjectApiName(task.getEntityId());
        metadataTask.setObjectDataId(task.getObjectId());
        //metadataTask.setObjectIds();
        metadataTask.setOwner(Lists.newArrayList(task.getApplicantId()));
        metadataTask.setProcessorIds(task.getProcessIds());
        metadataTask.setRemindLatency(task.getRemindLatency());
        metadataTask.setSourceWorkflowId(task.getSourceWorkflowId());

        Pair<String, String> stage = DataTransferUtils.getStage(task.getActivityId(), pools);

        metadataTask.setStageId(stage.getKey());
        metadataTask.setStageName(stage.getValue());
        metadataTask.setStartTime(task.getCreateTime());
        metadataTask.setState(task.getState());
        metadataTask.setTaskName(task.getName());
        if (Objects.nonNull(task.getRemindLatency())) {
            BigDecimal remindLatencyDecimal = new BigDecimal((Double) task.getRemindLatency());
            metadataTask.setTimeoutTime(task.isTimeout() ? metadataTask.getDuration() - remindLatencyDecimal.longValue() : 0L);
        } else {
            metadataTask.setTimeoutTime(0L);
        }

        metadataTask.setWorkflowId(task.getWorkflowId());
        metadataTask.setWorkflowInstanceId(task.getWorkflowInstanceId());
        //metadataTask.setWorkflowInstanceName();
        metadataTask.setCreate_time(task.getCreateTime());
        return metadataTask;
    }

    private static final String FORMAT_THEME = "%s(%s)";
    private static final String FORMAT_DATETIME = "yyyy-MM-dd HH:mm";

    public static String getThemeFromName(String name, long time) {
        return String.format(FORMAT_THEME, name, timeFormat(time));
    }

    public boolean isTimeout() {
        if (getRemindLatency() == null) {
            return false;
        } else {
            if (Long.parseLong(getRemindLatency() + "") == 0) {
                return false;
            }
            return System.currentTimeMillis() - getStartTime() > Long.parseLong(getRemindLatency() + "");
        }
    }

    public static String timeFormat(long time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(FORMAT_DATETIME);
        return dateFormat.format(time);
    }

    public static MetadataTask of(BpmTaskCountEntity k) {
        MetadataTask metadataTask = new MetadataTask();
        metadataTask.setState(TaskState.timeout);
        metadataTask.setTimeout(true);
        metadataTask.set_id(k.getId());
        metadataTask.setName(k.getName());
        metadataTask.setCreate_time(k.getCreateTime());
        metadataTask.setActivityId(k.getActivityId());
        metadataTask.setWorkflowInstanceId(k.getWorkflowInstanceId());
        metadataTask.setStartTime(k.getStartTime());
        metadataTask.setObjectApiName(k.getObjectApiName());
        metadataTask.setObjectDataId(k.getObjectDataId());
        metadataTask.setRemindLatency(k.getRemindLatency());
        return metadataTask;
    }
}