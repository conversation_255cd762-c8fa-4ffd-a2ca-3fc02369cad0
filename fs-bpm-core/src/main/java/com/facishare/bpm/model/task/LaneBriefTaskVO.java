package com.facishare.bpm.model.task;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.util.memory.page.Comparation;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

/**
 * Created by <PERSON> on 19/02/2017.
 */
@Setter
@Getter
@Slf4j
public class LaneBriefTaskVO {

    private String id;
    @I18NExpression(relation = {"${workflowId.activityId}.name", "${sourceWorkflowId.activityId}.name"})
    private String name;    //    任务名称
    private String entryApiName;   //    所属业务对象

    private String entryName;//    业务记录：指流程发起时的业务记录，展示对象主属性值【本期：客户名称】
    private String entryObjectId;
    private TaskState state;

    private Long createTime;  //    任务开始时间：格式为:yyyy-mm-dd hh:MM
    //9. 创建人【支持过滤】支持
    private String applicantId;
    //8. 处理人【支持过滤】不支持
    private List<String> candidateIds;
    //6. 当前阶段【支持过滤，有难度往后放】 不支持
    private String laneId;

    private String activityId;
    private String detailEntityId; //业务记录的apiName
    private String detailEntityName; //业务记录的apiName的label
    private String detailObjectId; //业务记录的id
    private String detailObjectName; //业务记录主属性name
    private String instanceId;
    private Long startTime; //流程启动时间
    private String workflowId;
    private String sourceWorkflowId;

    public static  LaneBriefTaskVO fromTask(Task task){
        LaneBriefTaskVO temp = new LaneBriefTaskVO();
        temp.setId(task.getId());
        temp.setWorkflowId(task.getWorkflowId());
        temp.setCreateTime(task.getCreateTime());
        temp.setDetailEntityId(task.getEntityId());
        temp.setDetailObjectId(task.getObjectId());
        temp.setName(task.getName());
        temp.setApplicantId(task.getApplicantId());
        temp.setActivityId(task.getActivityId());
        temp.setInstanceId(task.getWorkflowInstanceId());
        temp.setCandidateIds(task.getCandidateIds());
        temp.setWorkflowId(task.getWorkflowId());
        temp.setSourceWorkflowId(task.getSourceWorkflowId());
        if (TaskState.error.equals(task.getState())) { //异常
            temp.setState(TaskState.error);
        } else if (task.isTimeout()) { //超时
            temp.setState(TaskState.timeout);
        } else {
            temp.setState(TaskState.normal); //进行中
        }
        return temp;
    }

    public static LaneBriefTaskVO fromTask(MetadataTask task, Map<String, String> entityNames) {
        LaneBriefTaskVO temp = new LaneBriefTaskVO();
        temp.setId(task.get_id());
        temp.setCreateTime(task.getCreate_time());
        temp.setDetailEntityId(task.getObjectApiName());
        temp.setWorkflowId(task.getWorkflowId());
        temp.setSourceWorkflowId(task.getSourceWorkflowId());
        temp.setDetailObjectId(task.getObjectDataId());
        temp.setName(task.getName());
        //temp.setApplicantId(task.getApplicantId());
        temp.setActivityId(task.getActivityId());
        temp.setInstanceId(task.getWorkflowInstanceId());
        //temp.setCandidateIds(task.getCandidateIds());
        if (TaskState.error.equals(task.getState())) { //异常
            temp.setState(TaskState.error);
        } else if (task.isTimeout()) { //超时
            temp.setState(TaskState.timeout);
        } else {
            temp.setState(TaskState.normal); //进行中
        }
        temp.setDetailEntityName(Strings.isNullOrEmpty(entityNames.get(task.getObjectApiName())) ? BPMConstants.REPLACE_WHEN_NOT_FOUND : entityNames.get(task.getObjectApiName()));
        return temp;
    }

    public static  LaneBriefTaskVO fromTask(MetadataTask task){
        LaneBriefTaskVO temp = new LaneBriefTaskVO();
        temp.setId(task.get_id());
        temp.setCreateTime(task.getCreate_time());
        temp.setDetailEntityId(task.getObjectApiName());
        temp.setDetailObjectId(task.getObjectDataId());
        temp.setName(task.getName());
        temp.setWorkflowId(task.getWorkflowId());
        temp.setSourceWorkflowId(task.getSourceWorkflowId());
        //temp.setApplicantId(task.getApplicantId());
        temp.setActivityId(task.getActivityId());
        temp.setInstanceId(task.getWorkflowInstanceId());
        //temp.setCandidateIds(task.getCandidateIds());
        if (TaskState.error.equals(task.getState())) { //异常
            temp.setState(TaskState.error);
        } else if (task.isTimeout()) { //超时
            temp.setState(TaskState.timeout);
        } else {
            temp.setState(TaskState.normal); //进行中
        }
        temp.setDetailEntityName(Strings.isNullOrEmpty(task.getObjectApiName__r()) ? BPMConstants.REPLACE_WHEN_NOT_FOUND : task.getObjectApiName__r());
        return temp;
    }

    public Object getProperty(String field){
        try{
            return this.getClass().getMethod("get"+field.substring(0, 1).toUpperCase() + field.substring(1)).invoke(this);
        }catch (Exception e){
            log.error("getProperty ",e);
            return "";
        }
    }


    public static Predicate<LaneBriefTaskVO>  getFilter(List<Comparation> comparations){
        return item -> {
            boolean b = true;
            for (int i = 0; i < comparations.size(); i++) {
                Comparation comparation = comparations.get(i);
                switch (comparation.getType()) {
                    case CONTAIN:
                        Object value = item.getProperty(comparation.getFieldName());
                        if (comparation.getValue() == null) {
                            continue;
                        } else if (value == null) {
                            b = b && false;
                            break;
                        } else if ((value + "").indexOf(comparation.getValue()) > -1) {
                            continue;
                        } else {
                            b = false;
                            break;
                        }
                    case EQUAL:
                        value = item.getProperty(comparation.getFieldName());
                        if (comparation.getValue() == null) {
                            continue;
                        } else if (value == null) {
                            b = b && false;
                            break;
                        } else if (value.equals(comparation.getValue())) {
                            continue;
                        } else {
                            b = false;
                            break;
                        }
                }
            }
            return b;
        };
    }

    public enum QueryType{
        lane, activity
    }

    public enum QueryState{
        in_progress,error,timeout,normal;

        public boolean equalWithTaskState(TaskState state){
            return this.name().equals(state.name());
        }
    }
}
