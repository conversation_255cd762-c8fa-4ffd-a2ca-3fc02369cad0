package com.facishare.bpm.model.task;

import com.facishare.bpm.handler.task.detail.model.StandardData;
import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementPluginWrapper;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public abstract class BaseTask {
    public String id;
    public TaskState state;
    public List<String> candidateIds;
    public List<String> processIds;
    public String taskType;
    public ExecutionTypeEnum executionType;
    public String errorMsg;
    public Long createTime;
    public Long modifyTime;
    public AfterActionExecution.SimpleAfterActionExecution execution;
    public String entityId;
    public String objectId;
    public List<Opinion> opinions;
    public List<Task.ApproverModifyLog> candidateModifyLog;
    public String linkAppName;
    public List<MoreOperationManager.MoreOperation> moreOperations;
    public String workflowInstanceId;
    public int activityInstanceId;
    public Map<String, Object> externalApply;
    public Integer linkAppType;
    public String linkApp;
    public Boolean linkAppEnable;
    public String laneId;
    @I18NExpression(relation = {"${workflowId.laneId}.name", "${sourceWorkflowId.laneId}.name"})
    public String laneName;
    public String activityId;
    public String sourceWorkflowId;
    public String workflowId;
    @I18NExpression(drill = true)
    public Map<String, ActionButton> button = Maps.newHashMap();
    public StandardData data;
    public int assignNextTask;
    public Set<String> nextTaskAssigneeScope;
    /**
     * 元素配置信息 880
     */
    public Map customExtension;
    public FlowElementPluginWrapper elementPluginConfig;
    public String nodeType;
    public List<OperateLog> tagInfos;
    public List<Task.RemindLog> remindLogs;

    public void elementConfigData(Map customExtension,FlowElementPluginWrapper elementConfig){
        this.customExtension=customExtension;
        this.elementPluginConfig = elementConfig;
    }

    public abstract Boolean getIsOwner();

    public String getExternalApplyActionCode(){
        if(MapUtils.isEmpty(this.externalApply)){
            return null;
        }
        return (String) this.externalApply.get("actionCode");
    }

}
