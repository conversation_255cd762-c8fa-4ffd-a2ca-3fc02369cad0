package com.facishare.bpm.model.instance;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.i18n.I18NExpression;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import spark.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 19/02/2017.
 */
@Setter
@Getter
/*
  实例信息，当实例完成时，没有当前任务和落地页面
 */
public class WorkflowInstanceVO {
    /**
     * 流程编号
     */
    private String id;
    /**
     * 流程名称
     */
    @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
    private String workflowName;
    /**
     * 状态
     */
    private InstanceState state;
    /**
     * 当前流程中发起人
     */
    private String applicantId;
    /**
     * 实例创建时间
     */
    private Long start;
    /**
     * 实例结束时间
     */
    private Long end;

    private String triggerSource;

    private String entityId;

    private String objectId;

    private String linkAppId;
    private Integer linkAppType;
    private String workflowId;
    private String sourceWorkflowId;
    private String canceler;
    private List<String> currentCandidateIds;
    private Long lastModifiedTime;
    private String cancelReason;
    private String errorReason;
    private String outerSubmitter;

    public void setEnd(Long end){
        if(end!=null&&end==0){
            this.end=null;
        }
        this.end=end;
    }

    public static WorkflowInstanceVO fromWorkflowInstance(WorkflowInstance item) {
        return fromWorkflowInstance(item,null);
    }

    public static WorkflowInstanceVO fromWorkflowInstance(WorkflowInstance item, Map<String, WorkflowOutline> sourceWorkflowOfOutline) {
        WorkflowInstanceVO vo = new WorkflowInstanceVO();
        vo.setId(item.getId());
        vo.setWorkflowName(item.getWorkflowName());
        //流程状态设置
        vo.setState(item.getState());
        vo.setApplicantId(item.getApplicantId());
        vo.setStart(item.getStart());
        vo.setEnd(item.getEnd());
        vo.setTriggerSource(item.getTriggerSource().name());
        vo.setEntityId(item.getEntityId());
        vo.setObjectId(item.getObjectId());
        vo.setWorkflowId(item.getWorkflowId());
        vo.setSourceWorkflowId(item.getSourceWorkflowId());
        vo.setCanceler(item.getCanceler());
        if (MapUtils.isNotEmpty(sourceWorkflowOfOutline)) {
            WorkflowOutline workflowOutline = sourceWorkflowOfOutline.get(item.getSourceWorkflowId());
            if (workflowOutline != null) {
                vo.setLinkAppId(workflowOutline.getLinkApp());
                vo.setLinkAppType(workflowOutline.getLinkAppType());
            }
        }
        vo.setCurrentCandidateIds(item.getCurrentCandidateIds());
        vo.setLastModifiedTime(item.getLastModifiedTime());
        vo.setCancelReason(item.getReason());
        vo.setErrorReason(item.getErrorReason());
        vo.setOuterSubmitter(item.getOuterSubmitter());
        return vo;
    }

    public static WorkflowInstanceVO create(Map<String, Object> instance) {
        WorkflowInstanceVO workflowInstanceVO = new WorkflowInstanceVO();
        workflowInstanceVO.setApplicantId((String) MapUtil.instance.getList(instance, TransferDataConstants.MDInstanceField.applicantId.getValue()).get(0));
        workflowInstanceVO.setStart(MapUtil.instance.getLong(instance, TransferDataConstants.MDInstanceField.start.getValue()));
        workflowInstanceVO.setEnd(MapUtil.instance.getLong(instance, TransferDataConstants.MDInstanceField.end.getValue()));
        workflowInstanceVO.setEntityId(MapUtil.instance.getString(instance, TransferDataConstants.MDInstanceField.objectApiName.getValue()));
        workflowInstanceVO.setObjectId(MapUtil.instance.getString(instance, TransferDataConstants.MDInstanceField.objectDataId.getValue()));
        workflowInstanceVO.setId(MapUtil.instance.getString(instance, TransferDataConstants.MDInstanceField.id.getValue()));
        workflowInstanceVO.setState(InstanceState.valueOf(MapUtil.instance.getString(instance, TransferDataConstants.MDInstanceField.state.getValue())));
        workflowInstanceVO.setWorkflowName(MapUtil.instance.getString(instance, TransferDataConstants.MDInstanceField.workflowName.getValue()));
        return workflowInstanceVO;
    }

    public static List<Object> getPersons(List<WorkflowInstanceVO> workflowInstanceVOS) {
        return workflowInstanceVOS.stream()
                .flatMap(vo -> {
                    List<Object> allIds = new ArrayList<>();
                    if (vo.getApplicantId() != null) {
                        allIds.add(vo.getApplicantId());
                    }
                    if (CollectionUtils.isNotEmpty(vo.getCurrentCandidateIds())) {
                        allIds.addAll(vo.getCurrentCandidateIds());
                    }
                    if(StringUtils.isNotEmpty(vo.getOuterSubmitter())) {
                        allIds.add(vo.getOuterSubmitter());
                    }
                    return allIds.stream();
                })
                .distinct()
                .collect(Collectors.toList());
    }

}
