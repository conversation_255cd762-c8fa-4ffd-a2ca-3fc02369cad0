package com.facishare.bpm.model.meta;

import com.facishare.bpm.model.RelevantTeam;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @creat_date: 2021/9/20
 * @creat_time: 09:44
 * @since 7.5.0
 */
@Data
public class BaseBPMObj {
  private String _id;
  private String record_type;
  private String name;
  private String workflowId;
  private String sourceWorkflowId;
  private String workflowName;
  private Long duration;
  private Integer link_app_type;
  private String link_app_id;
  private String objectApiName;
  private String objectDataId;
  /**
   * 流程发起人
   */
  private Set<String> owner;
  /**
   * 外部提交人
   */
  private Set<String> out_owner;
  private Set<RelevantTeam> relevant_team;
  private Set<String> current_candidate_ids;
  private Long startTime;
  private Long endTime;
  private String error_reason;
  private Map name__lang;
  private Map workflowName__lang;
  private Boolean is_deleted;

  private Set<String> objectIds;
  private Long actual_duration;
  public Set<String> getObjectIds() {
    if(Objects.isNull(objectIds)){
      objectIds= Sets.newHashSet();
    }
    return objectIds;
  }
  public void setCurrent_candidate_ids(Set<String> current_candidate_ids) {
    this.current_candidate_ids = current_candidate_ids;
  }
  public void setCurrent_candidate_ids(List<String> current_candidate_ids) {
    if(Objects.isNull(current_candidate_ids)){
      current_candidate_ids= Lists.newArrayList();
    }
    this.current_candidate_ids = current_candidate_ids.stream().collect(Collectors.toSet());
  }

}
