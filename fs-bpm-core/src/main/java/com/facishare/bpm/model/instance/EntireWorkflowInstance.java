package com.facishare.bpm.model.instance;


import com.facishare.bpm.manage.MoreOperationManager;
import com.facishare.bpm.model.paas.engine.bpm.ActivityInstance;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.utils.i18n.CustomI18NHandler;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 24/02/2017.
 */
@Data
public class EntireWorkflowInstance {
    @I18NExpression(drill = true)
    private Instance instance;
    @I18NExpression(custom = CustomI18NHandler.svg)
    private String svg;
    @I18NExpression(drill = true)
    private List<ActivityInstance> activityInstances;
    @I18NExpression(drill = true)
    private List<ActivityInstance> currentActivityInstances;
    private List<CurrentTransition> currentTransitions;
    private Map<String, Object> rule;
    private Map<String,MoreOperationManager.MoreOperation> moreOperations= Maps.newHashMap();
    private Map<String, Employee> employeeInfo;
    private String workflowId;
    private String sourceWorkflowId;
    public void setMoreOperations(List<MoreOperationManager.MoreOperation> moreOperations){
        if(!CollectionUtils.isEmpty(moreOperations)){
            moreOperations.forEach(operation->this.moreOperations.put(operation.getCode(),operation));
        }
    }

    public static EntireWorkflowInstance create(String tenantId,EntireWorkflow workflow) {
        EntireWorkflowInstance instance = new EntireWorkflowInstance();
        instance.setActivityInstances(workflow.getWorkflowInstance().getActivityInstances());
        instance.setInstance(EntireWorkflowInstance.Instance.fromWorkflowInstance(workflow.getWorkflowInstance()));
        instance.setCurrentActivityInstances(workflow.getWorkflowInstance().getActivityInstances());
        instance.setSvg(workflow.getSvg());
        instance.setRule(workflow.getRule());
        instance.setWorkflowId(workflow.getWorkflowId());
        instance.setSourceWorkflowId(workflow.getSourceWorkflowId());
        return instance;
    }

    @Data
    public static class Instance {
        private String id;
        private String workflowId;
        private String sourceWorkflowId;
        @I18NExpression(relation = {"${workflowId}.name", "${sourceWorkflowId}.name"})
        private String name;
        @I18NExpression(relation = {"${workflowId}.description", "${sourceWorkflowId}.description"})
        private String description;
        private String businessObjectName;
        private String businessObjectApiLabel;
        private String businessObjectApiName;
        private String businessObjectId;
        private Long createTime;
        private Long endTime;
        private String applicantId;
        private InstanceState state;
        private String reason;
        private String triggerSourceDesc;
        public static Instance fromWorkflowInstance(WorkflowInstance workflowInstance){
            Instance instance=new Instance();
            instance.setName(workflowInstance.getWorkflowName());
            instance.setId(workflowInstance.getId());
            instance.setWorkflowId(workflowInstance.getWorkflowId());
            instance.setSourceWorkflowId(workflowInstance.getSourceWorkflowId());
            instance.setBusinessObjectApiName(workflowInstance.getEntityId());
            instance.setBusinessObjectId(workflowInstance.getObjectId());
            instance.setBusinessObjectName(workflowInstance.getObjectName());
            instance.setCreateTime(workflowInstance.getStart());
            instance.setEndTime(workflowInstance.getEnd());
            instance.setState(workflowInstance.getState());
            instance.setBusinessObjectApiLabel(workflowInstance.getEntryTypeName());
            instance.setApplicantId(workflowInstance.getApplicantId());
            instance.setDescription(workflowInstance.getWorkflowDescription());
            instance.setReason(workflowInstance.getReason());
            instance.setTriggerSourceDesc(workflowInstance.getTriggerSourceDesc());
            return instance;
        }
    }

    public enum CurrentActivityInstanceState{
        PROGRESS,DONE
    }
    @Data
    private static class CurrentTransition{
        private String id;
        private String fromId;
        private String toId;
        private CurrentActivityInstanceState status;

    }
}
