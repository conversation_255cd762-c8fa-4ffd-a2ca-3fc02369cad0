package com.facishare.bpm.model;

import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.WorkflowRule;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMRuntimeException;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.flow.mongo.bizdb.entity.WorkflowDraftEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.stage.doc.annotations.DocDescribe;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by cuiyongxu  2018年07月23日11:25:44
 */
@Slf4j
@Data
public class WorkflowOutlineDraft extends WorkflowOutline {

    /**
     * 来源的流程名称 name
     */
    @DocDescribe(label = "草稿来源名称",desc = "定义保存草稿后,记录老版本定义的名称")
    private String sourceName;

    @DocDescribe(label = "outlineId",desc = "业务流程自存储Id")
    private String outlineId;



    /**
     * flag 用于区分是否删除id, 定义保存为草稿的时候,会将id传递过来,
     * true:创建
     * false:更新
     */
    public static WorkflowDraftEntity createDraft(WorkflowOutlineDraft workflow) {
        return BeanUtils.transfer(workflow,WorkflowDraftEntity.class,(draft,entity)->{
            entity.setSourceName(workflow.getName());
            entity.setExtensionJson(WorkflowOutline.convertWorkflowExtensionToJson(workflow.getExtension()));
            entity.setWorkflowJson(JsonUtil.toJson(workflow.getWorkflow()));
            if (workflow.getRule() != null) {
                entity.setRuleJson(JsonUtil.toJson(workflow.getRule()));
            }
            entity.setSingleInstanceFlow(workflow.getSingleInstanceFlow() == null ? 0 : workflow.getSingleInstanceFlow());
            entity.setExternalFlow(workflow.getExternalFlow() == null ? 0 : workflow.getExternalFlow());
        });
    }



    public static List<WorkflowOutlineDraft> fromBrieflyDraftEntities(List<WorkflowDraftEntity> draftEntities) {
        if (draftEntities != null) {
            return draftEntities.stream()
                    .map(WorkflowOutlineDraft::fromBrieflyEntity)
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public static WorkflowOutlineDraft fromBrieflyEntity(WorkflowOutlineEntity entity) {
        return BeanUtils.transfer(entity, WorkflowOutlineDraft.class, (src, ret) -> {
            ret.setDraftId(src.getId());
            ret.setSvg(WorkflowOutline.convertWorkflowExtensionFromJson(src.getExtensionJson()).getSvg());
            WorkflowRule rule = JsonUtil.fromJson(src.getRuleJson(), WorkflowRule.class);
            if (rule!=null&& CollectionUtils.isNotEmpty(rule.getConditions())) {
                ret.setRule(JsonUtil.fromJson(src.getRuleJson(), WorkflowRule.class));
            }
        });
    }

    public static WorkflowOutlineDraft fromEntity(WorkflowDraftEntity entity) {
        return BeanUtils.transfer(entity, WorkflowOutlineDraft.class, (src, ret) -> {
            ret.setExtension(WorkflowOutline.convertWorkflowExtensionFromJson(src.getExtensionJson()));
            ret.setWorkflow(JsonUtil.fromJson(src.getWorkflowJson(), ExecutableWorkflowExt.class));
            ret.setRule(JsonUtil.fromJson(src.getRuleJson(), WorkflowRule.class));
            ret.setDraftId(src.getId());
            ret.setId(src.getOutlineId());
        });

    }

    public void checkDiagram() {
        WorkflowExtension workflowExtension = getExtension();
        boolean flag = false;
        if (workflowExtension==null || workflowExtension.getDiagram() == null) {
            log.error("diagram=null");
            flag = true;
        } else {
            if (workflowExtension.getDiagram() instanceof List) {
                List diagram = (List) workflowExtension.getDiagram();
                if (CollectionUtils.isEmpty(diagram)) {
                    log.error("diagram=[]");
                    flag = true;
                }
            } else {
                log.error("diagram非list对象");
                flag = true;
            }
        }
        if (flag) {
            throw new BPMRuntimeException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DRAFT_DATA_CONFIG_ERROR);
        }
    }

    @Override
    public String toString() {
        return super.toString();
    }

}
