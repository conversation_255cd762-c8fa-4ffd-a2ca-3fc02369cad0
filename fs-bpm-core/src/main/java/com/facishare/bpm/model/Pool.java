package com.facishare.bpm.model;

import com.facishare.bpm.model.paas.engine.bpm.ActivityInstance;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.curator.shaded.com.google.common.base.Objects;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
/**
 * Created by wangz on 17-4-5.
 */
@Getter
@Setter
public class Pool implements Serializable{
    String id;
    String name;
    @I18NExpression(drill = true )
    List<Lane> lanes;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Lane implements Serializable{
        private String id;
        @I18NExpression(relation = {"${workflowId.id}.name", "${sourceWorkflowId.id}.name"})
        private String name;
        @I18NExpression(relation = {"${workflowId.id}.description", "${sourceWorkflowId.id}.description"})
        private String description;
        private boolean current;
        private LaneStatus status;
        private Integer order;


        public static Lane create(LaneEntity laneEntity, Map<String, ActivityInstance> activityIdAndActivityInstances) {
            return new Pool.Lane(laneEntity.getId(), laneEntity.getName(), laneEntity.getDescription(), currentLaneStatus(laneEntity, activityIdAndActivityInstances), laneEntity.getOrder());
        }

        public Lane(String id, String name, String description, LaneStatus status, Integer order) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.current = Objects.equal(status, LaneStatus.in_progress);
            this.status=status;
            this.order = order;
        }

        public static LaneStatus currentLaneStatus(LaneEntity laneEntity, Map<String, ActivityInstance> activityIdAndActivityInstances) {
            List<String> activityIds = laneEntity.getActivities();
            if (CollectionUtils.isNotEmpty(laneEntity.getActivities()) && MapUtils.isNotEmpty(activityIdAndActivityInstances)) {
                  // 检查是否有交集
                  boolean hasIntersection = false;
                  boolean hasOpenActivity = false;
                  // 遍历所有活动ID
                  for (String activityId : activityIds) {
                      ActivityInstance activityInstance = activityIdAndActivityInstances.get(activityId);
                      if (activityInstance != null) {
                          hasIntersection = true;
                          // 检查是否有进行中的活动
                          if (activityInstance.isOpen()) {
                              hasOpenActivity = true;
                              break;
                          }
                      }
                  }
                  // 如果没有交集，返回none状态
                  if (!hasIntersection) {
                      return LaneStatus.none;
                  }
                  // 如果有进行中的活动，返回in_progress状态，否则返回pass状态
                  return hasOpenActivity ? LaneStatus.in_progress : LaneStatus.pass;
            }
            return LaneStatus.none;
        }

    }

    public static Pool toPool(PoolEntity poolEntity, String activityId){
        if(poolEntity == null){
            return null;
        }

        Pool pool = new Pool();
        pool.setId(poolEntity.getId());
        pool.setName(poolEntity.getName());

        List<Lane> laneList = Lists.newArrayList();
        List<LaneEntity> laneEntityList = poolEntity.getLanes();
        if(laneEntityList != null){
            for(LaneEntity laneEntity : laneEntityList){
                Lane lane = new Lane();
                lane.setName(laneEntity.getName());
                lane.setId(laneEntity.getId());
                lane.setDescription(laneEntity.getDescription());

                if(laneEntity.getActivities() != null && laneEntity.getActivities().contains(activityId)){
                    lane.setCurrent(true);
                }

                laneList.add(lane);
            }
        }

        pool.setLanes(laneList);

        return pool;
    }

    enum LaneStatus{
        in_progress,pass,none
    }
}
