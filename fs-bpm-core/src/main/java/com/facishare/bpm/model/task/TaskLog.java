package com.facishare.bpm.model.task;

import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Opinion;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.Strings;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-3-7.
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TaskLog implements Comparable<TaskLog> {
    private long time;
    @I18NExpression(relation = {"${workflowId.activityId}.name", "${sourceWorkflowId.activityId}.name"})
    private String taskName;
    private String assigeeId;
    private String excutionType; //approve,update/opration/addRelatedObject/addMDObject/start/end/changeAssignee
    private String taskType; //anyone, all
    private String opinion;
    private String result; //auto-agree,agree,reject

    private Object oldData;
    private Object newData;
    private String workflowId;
    private String sourceWorkflowId;
    private String activityId;

    public static List<Object> getPersonIds(List<TaskLog> taskLogList){
        return taskLogList.stream().filter(key-> !Strings.isNullOrEmpty(key.getAssigeeId())).map(TaskLog::getAssigeeId).collect(Collectors.toList());
    }

    @Override
    public int compareTo(TaskLog o) {
        return this.getTime() > o.getTime() ? 1 : -1;
    }


    public static List<TaskLog> getTaskLogs(WorkflowInstance instance, List<Task> tasks) {
        List<TaskLog> taskLogs = Lists.newArrayList();
        setStartAndEndTaskLogs(instance, taskLogs);
        if (CollectionUtils.isNotEmpty(tasks)) {
            tasks.forEach((task)->setTaskLogs(taskLogs,task));
        }

        Collections.sort(taskLogs);
        return taskLogs;
    }

    private static void setStartAndEndTaskLogs(WorkflowInstance instance, List<TaskLog> taskLogs) {
        TaskLog startLog = new TaskLog(instance.getStart(), null, instance.getApplicantId(), "start", null, null,
                null, null, null,instance.getWorkflowId(), instance.getSourceWorkflowId(), "");
        taskLogs.add(startLog);
        if (InstanceState.pass.equals(instance.getState())) {
            TaskLog endLog = new TaskLog(instance.getEnd(), null, null, "end", null, null, instance.getState().name()
                    , null, null,instance.getWorkflowId(), instance.getSourceWorkflowId(), "");
            taskLogs.add(endLog);
        }
    }

    private static void setTaskLogs(List<TaskLog> taskLogs,Task task) {
        //1.更换任务处理人日志
        setApproverModifyLog(taskLogs,task);
        //2.任务处理日志
        setOpinionLogs(taskLogs,task);
    }

    private static void setOpinionLogs(List<TaskLog> taskLogs,Task task) {
        List<Opinion> opinions = task.getOpinions();
        if (!CollectionUtils.isEmpty(opinions)) {
            String taskType = task.getTaskType();
            String executionType = (String) task.getBpmExtension().get(ExtensionKey.executionType);
            for (Opinion opinion : opinions) {
                TaskLog taskLog = new TaskLog();
                taskLog.setTaskName(task.getName());
                taskLog.setTime(opinion.getReplyTime());
                taskLog.setResult(opinion.getActionType());
                taskLog.setOpinion(opinion.getOpinion());
                taskLog.setExcutionType(executionType);
                taskLog.setAssigeeId(opinion.getUserId());
                taskLog.setTaskType(taskType);
                taskLog.setWorkflowId(task.getWorkflowId());
                taskLog.setActivityId(task.getActivityId());
                taskLogs.add(taskLog);
            }
        }
    }

    private static void  setApproverModifyLog(List<TaskLog> taskLogs, Task task) {
        List<Task.ApproverModifyLog> approverModifyLogs = task.getApproverModifyLog();

        if (CollectionUtils.isEmpty(approverModifyLogs)) {
            return;
        }

        approverModifyLogs.forEach(approverModifyLog -> {
            TaskLog taskLog = new TaskLog();
            taskLog.setNewData(approverModifyLog.getAfterModifyPersons());
            taskLog.setOldData(approverModifyLog.getBeforeModifyPersons());
            taskLog.setAssigeeId(approverModifyLog.getUserId());
            taskLog.setExcutionType("changeHandler");
            taskLog.setTaskName(task.getName());
            taskLog.setWorkflowId(task.getWorkflowId());
            taskLog.setTime(approverModifyLog.getModifyTime());

            taskLogs.add(taskLog);
        });
    }
}
