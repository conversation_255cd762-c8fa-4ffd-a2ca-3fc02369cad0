package com.facishare.bpm.model.statics;

import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @creat_date: 2019/4/16
 * @creat_time: 11:01
 * @since 6.6
 */
@Data public class LaneStatics extends TaskStatics {

    public static Map<String, LaneStatics> buildLanleStatics(List<PoolEntity> pools, Map<String, TaskStatics> taskStaticsMap,String workflowId ,String sourceWorkflowId) {
        Map<String,LaneStatics> laneStaticsMap= Maps.newHashMap();
        pools.forEach(poolEntity -> poolEntity.getLanes().forEach(laneEntity -> {
            LaneStatics laneStatics = laneStaticsMap.get(laneEntity.getId());
            if(Objects.isNull(laneStatics)){
                laneStatics=new LaneStatics();
                laneStatics.setId(laneEntity.getId());
                laneStatics.setWorkflowId(workflowId);
                laneStatics.setSourceWorkflowId(sourceWorkflowId);
                laneStatics.setName(laneEntity.getName());
                laneStaticsMap.put(laneEntity.getId(),laneStatics);
            }
            TaskStatics defaultTaskStatics = new TaskStatics();
            for(String activityId:laneEntity.getActivities()){
                TaskStatics tempTaskStatics = taskStaticsMap.getOrDefault(activityId, defaultTaskStatics);
                if(Strings.isEmpty(tempTaskStatics.getWorkflowId())){
                    tempTaskStatics.setWorkflowId(workflowId);
                }
                tempTaskStatics.setSourceWorkflowId(sourceWorkflowId);
                laneStatics.errorTaskCount+=tempTaskStatics.getErrorTaskCount();
                laneStatics.inProgressTaskCount+=tempTaskStatics.getInProgressTaskCount();
                laneStatics.timeoutTaskCount+=tempTaskStatics.getTimeoutTaskCount();
                laneStatics.normalTaskCount+=tempTaskStatics.getNormalTaskCount();
            }
        }));
        return laneStaticsMap;
    }
}
