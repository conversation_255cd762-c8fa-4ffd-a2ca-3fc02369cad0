package com.facishare.bpm.model.org;

import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId;
import com.facishare.bpm.remote.model.org.CRMGroup;
import com.facishare.bpm.remote.model.org.Department;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.remote.model.org.Role;
import lombok.Data;

import java.util.Map;

/**
 * Created by cuiyongxu on 17/5/4.
 */
@Data
public class BPMOrg {

    private Map<Integer, Employee> person;

    private Map<String, CRMGroup> CRMGroup;

    private Map<String, Role> role;

    private Map<Integer, Department> dept;

    private Map<Integer, Employee> externalPerson;

    private Map<String, GetOutRolesByTenantId.SimpleRoleResult> externalRole;
}
