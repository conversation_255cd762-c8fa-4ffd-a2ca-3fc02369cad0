package com.facishare.bpm.remote.metadata;


import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList;
import com.facishare.bpm.model.resource.newmetadata.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.remote.model.SimpleMetadataAction;
import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.rest.core.annotation.SocketConfigParam;
import com.facishare.rest.core.model.RemoteContext;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wangz on 17-1-4.
 * <p>
 * 元数据自定义对象 接口封装
 */
public interface MetadataService {
    /**
     * 当数据被删除或者Id有误时，会抛出异常,调用方根据业务需求选择捕获还是抛出！
     *
     * @param context
     * @param descApiName throw BPMMetadataServiceException
     * @return
     */
    Map findDescribe(RemoteContext context, String descApiName, boolean containAllFields, boolean includeStatistics);

    Map<String, Object> findInternalDescribe(RemoteContext context, String descApiName, boolean containAllFields);


    String getActionNameByActionCode(RemoteContext context, String descApiName, String actionCode);

    Map<String, Object> createData(RemoteContext context, String descApiName, String jsonData);

    Map<String, Object> updateData(RemoteContext context, String descApiName, String dataId, String jsonData,boolean isPartUpdate, boolean isBatch);

    /**
     * @param context
     * @param descApiName
     * @param dataId
     * @param jsonData
     * @param isPartUpdate
     * @param applyValidationRule 添加验证规则
     * @param applyDataPrivilegeCheck 验证数据权限
     * @return
     */
    UpdateData.UpdateDataResultDetail updateData(RemoteContext context, String descApiName, String dataId, String jsonData, boolean isPartUpdate, boolean applyValidationRule, boolean applyDataPrivilegeCheck, Map<String,Object> describe, boolean isBatch, String modelName);

    UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String descApiName, String dataId, String jsonData, boolean isPartUpdate, boolean applyValidationRule, boolean applyDataPrivilegeCheck, Map<String, Object> describe, boolean isBatch, String modelName);

    UpdateData.UpdateDataResultDetail updateDataSkipRequired(RemoteContext context, String descApiName, String dataId, String jsonData, boolean isPartUpdate, boolean applyValidationRule, boolean applyDataPrivilegeCheck);

    FindInternalDataById.FindInternalDataByIdResultDetail findDataById(RemoteContext context, String descApiName, String dataId,
                                                                       boolean includeLookupName, boolean includeDescribe, boolean includeStatistics, boolean isFillExtendField, boolean formatData, boolean skipRelevantTeam, SocketConfigParam.SocketConfig socketConfig);

    FindInternalDataById.FindInternalDataByIdResultDetail findDataById(RemoteContext context, String descApiName, String dataId,
                                                                       boolean includeLookupName, boolean includeDescribe, boolean includeStatistics, boolean isFillExtendField, boolean formatData,boolean skipRelevantTeam,boolean includeDeleted, SocketConfigParam.SocketConfig socketConfig);

    List<Map<String, Object>> findDataByQuery(RemoteContext context,
                                              String descApiName,
                                              List<IConditions> conditions);

    List<Map<String, Object>> findDataByQuery(String tenantId,
                                                     String ea,
                                                     String appId,
                                                     String apiName,
                                                     Map<String, String> conditionMap,
                                                     int pageSize,
                                                     int pageNumber);

    PageResult<WorkflowInstanceVO> findDataByQuery(RemoteContext context,
                                                   String descApiName,
                                                   InstanceState state,
                                                   String sourceWorkflowId,
                                                   String workflowName,
                                                   String objectId,
                                                   int pageSize, int pageNumber);


    List<SimpleMetadataDesc> findDescsByTenantId(RemoteContext context,
                                                 List<String> apiNames, boolean includeFieldsDesc, boolean useGroupManager);


    List<Map<String, Object>> findDescsByApiNames(RemoteContext context, Collection<String> apiNames, boolean includeLookups, boolean includeCountFieldLookupType);

    List<Map<String, Object>> findDescsByApiNamesByCache(RemoteContext context, Collection<String> apiNames, boolean includeLookups, boolean includeCountFieldLookupType);

    List<SimpleMetadataDesc> findDescsByApiNames(RemoteContext context, List<String> apiNames, boolean
            includeLookups);

    List<SimpleMetadataAction> findActionsByDesc(RemoteContext context, String descApiName, String actionType);

    List<SimpleMetadataDesc> findReferences(RemoteContext context, String apiName);

    String getPaaSObjectName(RemoteContext context, String apiName, String objectId);

    Map<String, String> getObjectNameAndDisplayName(RemoteContext context, String entityId, List<String> objectIds);

    boolean getDeliveryNoteEnable(RemoteContext context);

    Map<String, String> getPaaSObjectNames(RemoteContext context, Collection<Pair<String, String>> entityIdAndObjectIds);

    Map<String, String> getPaaSObjectNames(RemoteContext context, String entityId,List<String> objectIds);

    List getDataOwner(RemoteContext context, String apiName, String id);

    Map<String, List<String>> getDataOwners(RemoteContext context, String entityId, Set<String> ids);

    Map<String, Object> findDescWithReference(RemoteContext context, String apiName);

    List<FindDisplayNames.FindDisplayNamesResultDetail> getEntityNames(RemoteContext context);

    List<FindDisplayNames.FindDisplayNamesResultDetail> getEntityNames(RemoteContext context, int socketReadTimeout, int retryTimes);

    FindDataBySearchTemplate.Result findDataBySearchTemplate(RemoteContext context, String apiName, Object query);

    Map<String, Object> getAreaOption(String fieldType);

    GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions(RemoteContext context);

    Map<String, String> getAreaLabelByCodes(RemoteContext remoteContext, List<String> codes);

    Map<String,Object> findDataDescribeLayout(RemoteContext context, String entityId, String objectId, String recordType,String layoutType);

    List<SimpleMetadataAction>  findActionsBySpecialCondition(RemoteContext context, String descApiName);

     Map<String, Object> findDescribeExtra(RemoteContext context, String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType);

    List<FindCustomButtonList.CustomButton> findCustomButtonList(RemoteContext context, String apiName, Boolean includeUIAction);

    List<FindCustomButtonList.CustomButton> findDataExhibitButton(RemoteContext context,String apiName, String objectId,  String usePageType);

    void invalidAndDeleteByDataId(RemoteContext context, String dataId, String entityId);

    void batchCreateData(RemoteContext context, String descApiName, String jsonData);

    void batchUpdateData(RemoteContext context, String descApiName, List<Map<String, Object>> updateList);

    void directlyBulkDelete(RemoteContext context, String descApiName, List<String> deleteDataIds);

    void deleteTaskDraft(RemoteContext context, String taskId);

}
