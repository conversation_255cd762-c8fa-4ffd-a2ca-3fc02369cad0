package com.facishare.bpm.remote.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Created by wangz on 17-1-11.
 *
 * 自定义对象的可操作类型
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class SimpleMetadataAction {
    private String actionCode;
    private String actionName;
    //是否开启了签退标志位 795
    //TODO @高俊  和华哥沟通下`
    private Boolean isOpenSignOut;

    public SimpleMetadataAction(String actionCode, String actionName) {
        this.actionCode = actionCode;
        this.actionName = actionName;
    }
}
