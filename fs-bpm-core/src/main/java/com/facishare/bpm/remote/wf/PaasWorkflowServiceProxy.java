package com.facishare.bpm.remote.wf;

import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMParamsException;
import com.facishare.bpm.exception.BPMTaskNotFoundException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.TaskTagInfoArg;
import com.facishare.bpm.remote.BPMPaasWorkflowResource;
import com.facishare.bpm.utils.ParallelUtils;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.RestConstant;
import com.fxiaoke.common.StopWatch;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 26/02/2017.
 */
@Component
@Slf4j
public class PaasWorkflowServiceProxy {

    private static final int DEFAULT_PAGE_SIZE = 1000;

    @Autowired
    private BPMPaasWorkflowResource paasWorkflowResource;

    public WorkflowOutline deploy(RemoteContext context, boolean update, WorkflowOutline outline) {
        ExecutableWorkflowExt executableWorkflow = outline.getWorkflow();
        org.apache.commons.configuration2.builder.fluent.Configurations a;
        DeployByRule.Arg arg = new DeployByRule.Arg();
        arg.setContext(context);
        arg.setUpdate(update);
        arg.setRuleJson(outline.getRuleJson());
        executableWorkflow.setId(null);

        arg.setWorkflowJson(executableWorkflow.toJson());
        DeployByRule.Result result = paasWorkflowResource.deployByRule(getHeaders(context), arg);
        executableWorkflow.setId(result.getWorkflowId());
        outline.setWorkflowId(result.getWorkflowId());
        outline.setRuleId(result.getRuleId());
        return outline;
    }

    public void updateHistoryDefinition(RemoteContext context, WorkflowOutline outline) {
        ExecutableWorkflowExt executableWorkflow = outline.getWorkflow();

        DeployByRule.Arg arg = new DeployByRule.Arg();
        arg.setContext(context);
        arg.setWorkflowJson(executableWorkflow.toJson());
        paasWorkflowResource.updateHistoryDefinition(getHeaders(context), arg);
    }


    public String start(RemoteContext context, TriggerSource triggerSource, String entityId, String objectId,
                        String sourceWorkflowId, Map<String, Object> variables) {
        StartWorkflowByRule.Arg arg = new StartWorkflowByRule.Arg();
        arg.setContext(context);
        arg.setEntityId(entityId);
        arg.setTriggerSource(triggerSource);

        arg.setObjectId(objectId);
        arg.setWorkflowSourceId(sourceWorkflowId);

        Map<String, Object> variableMap = Maps.newHashMap();
        variableMap.put("variables", variables);
        arg.setVaribles(variableMap);

        StartWorkflowByRule.Result result = paasWorkflowResource.startByRule(getHeaders(context), arg);

        return result.getResult();
    }

    public TriggerWorkflowByRule.Result trigger(RemoteContext context, TriggerSource triggerSource, String triggerSourceId, String entityId, String objectId,
                                                String sourceWorkflowId, Map<String, Object> variables, String startInstanceId) {
        TriggerWorkflowByRule.Arg arg = new TriggerWorkflowByRule.Arg();
        arg.setContext(context);
        arg.setEntityId(entityId);
        arg.setTriggerSource(triggerSource);

        arg.setObjectId(objectId);
        arg.setWorkflowSourceId(sourceWorkflowId);
        arg.setTriggerSourceId(triggerSourceId);

        Map<String, Object> variableMap = Maps.newHashMap();
        //TODO  variables 常量
        variableMap.put("variables", variables);
        arg.setVaribles(variableMap);
        arg.setStartInstanceId(startInstanceId);

        return paasWorkflowResource.triggerByRule(getHeaders(context), arg);
    }

    public WorkflowInstance getWorkflowInstance(RemoteContext context, String instanceId) {
        GetWorkflowInstance.Arg arg = new GetWorkflowInstance.Arg();
        arg.setContext(context);
        arg.setInstanceId(instanceId);
        return paasWorkflowResource.getWorkflowInstance(getHeaders(context), arg).getResult();
    }

    public PageResult<WorkflowInstance> getWorkflowInstances(RemoteContext context, String sourceWorkflowId,
                                                             String workflowId, Collection<String> workflowInstanceIds,
                                                             String entityId, String objectId,
                                                             InstanceState state, Page pageInfo, String workflowName) {
        return getWorkflowInstances(context, sourceWorkflowId, workflowId, workflowInstanceIds, entityId, objectId, workflowName, state, pageInfo);
    }

    public PageResult<WorkflowInstance> getWorkflowInstances(RemoteContext context, String sourceWorkflowId, String workflowId,
                                                             Collection<String> workflowInstanceIds,
                                                             String entityId, String objectId, String workflowName,
                                                             InstanceState states, Page pageInfo) {
        GetWorkflowInstances.Arg arg = new GetWorkflowInstances.Arg();
        arg.setContext(context);
        arg.setWorkflowId(workflowId);
        arg.setSourceWorkflowId(sourceWorkflowId);
        arg.setWorkflowInstanceIdList(workflowInstanceIds);
        arg.setEntityId(entityId);
        arg.setObjectId(objectId);
        arg.setState(states);
        arg.setPageInfo(pageInfo);
        arg.setWorkflowName(workflowName);

        GetWorkflowInstances.Result result = paasWorkflowResource.getWorkflowInstances(getHeaders(context), arg);
        if (result.getResult() == null) {
            return new PageResult<>();
        }
        return result.getResult();
    }

    public Map<String, WorkflowInstance> getWorkflowInstanceMap(RemoteContext context, Collection<String> instanceIds) {
        if (instanceIds == null) {
            return null;
        }
        //2018年08月22日15:16:14 添加 getPage(instanceIds.size()) 如果page为null  引擎只会返回20条
        List<WorkflowInstance> instances = this.getWorkflowInstances(context, null, null, instanceIds,
                null, null, null, getPage(instanceIds.size()), "").getDataList();

        return instances.stream().collect(Collectors.toMap(WorkflowInstance::getId, instance -> instance));
    }


    private Page getPage(int pageSize) {
        Page pageInfo = new Page();
        pageInfo.setPageSize(pageSize);
        return pageInfo;
    }

    public PageResult<Task> getTasksByAssigneeId(RemoteContext context, Boolean isCompleted, String taskName, Page page) {
        GetHandlerTaskByPage.Arg arg = new GetHandlerTaskByPage.Arg();
        arg.setContext(context);
        arg.setAssigneeId(context.getUserId());
        arg.setCompleted(isCompleted);
        arg.setPageInfo(page);
        arg.setName(taskName);

        GetHandlerTaskByPage.Result result = paasWorkflowResource.getHandlerTasks(getHeaders(context), arg);

        if (result.getResult() == null) {
            return new PageResult<>();
        }
        return result.getResult();
    }

    public GetWorkflow.Result getWorkflowAndRule(RemoteContext context, String workflowId) {
        GetWorkflow.Arg arg = new GetWorkflow.Arg();
        arg.setContext(context);
        arg.setWorkflowId(workflowId);
        return paasWorkflowResource.getWorkflow(getHeaders(context), arg);
    }

    public ExecutableWorkflowExt getWorkflow(RemoteContext context, String workflowId) {
        return ExecutableWorkflowExt.of(getWorkflowAndRule(context, workflowId).getWorkflowJson());
    }

    public Map getWorkflowMap(RemoteContext context, String workflowId) {
        return this.getWorkflowAndRule(context, workflowId).getWorkflowMap();
    }

    public boolean deleteDefine(RemoteContext context, String sourceWorkflowId) {
        DeleteDefinition.Arg arg = new DeleteDefinition.Arg();
        arg.setSourceWorkflowId(sourceWorkflowId);
        arg.setContext(context);
        paasWorkflowResource.definitionDelete(getHeaders(context), arg);
        return true;
    }

    public void cancel(RemoteContext context, String workflowInstanceId, String reason) {
        CancelWorkflowInstance.Arg arg = new CancelWorkflowInstance.Arg();
        arg.setWorkflowInstanceId(workflowInstanceId);
        arg.setOutContext(context);
        arg.setCancelReason(reason);
        paasWorkflowResource.cancelWorkflowInstance(getHeaders(context), arg);
    }

    public Task getTask(RemoteContext context, String taskId){
        return getTask(context, taskId, true);
    }

    public Task getTask(RemoteContext context, String taskId, boolean needThrowException) {

        if (taskId.contains(BPMConstants.LATENCY_SUFFIX) || taskId.contains(BPMConstants.EXECUTION_SUFFIX)) {
            return getLatencyTask(context, taskId);
        }

        GetTask.Arg getTaskArg = new GetTask.Arg();
        getTaskArg.setTaskId(taskId);
        getTaskArg.setContext(context);
        GetTask.Result getTaskResult = paasWorkflowResource.getTask(getHeaders(context), getTaskArg);
        Task task = getTaskResult.getResult();
        if(needThrowException){
            if (task == null || StringUtils.isEmpty(task.getId())) {
                throw new BPMParamsException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_FOUND);
            }
        }
        return task;
    }

    /**
     * 不支持查询自动节点和定时等待节点,此接口主要应用与  终端 待处理业务流程列表 底部按钮
     * @param context
     * @param taskIds
     * @return
     */
    public List<Task> getTasks(RemoteContext context, Set<String> taskIds) {
        GetTask.GetTasksArg getTaskArg = new GetTask.GetTasksArg();
        getTaskArg.setTaskIds(taskIds);
        getTaskArg.setContext(context);
        GetTask.GetTasksResult result = paasWorkflowResource.getTasks(getHeaders(context), getTaskArg);
        return result.getResult();
    }



    /**
     * 查询自动节点
     *
     * @param context
     * @param taskId
     * @return
     */
    public Task getLatencyTask(RemoteContext context, String taskId) {
        //id在这里处理,上层不需要关心替换的问题
        String id = taskId.replaceAll("latency_|execution_", StringUtils.EMPTY);
        GetAutoTask.Arg arg = new GetAutoTask.Arg();
        arg.setTaskId(id);
        arg.setContext(context);
        return paasWorkflowResource.getAutoTask(getHeaders(context), arg).getResult();
    }


    public CompleteTask.Result completeLatencyTask(RemoteContext context, String taskId, Map<String, Object> variables, String opinion) throws Exception {
        CompleteTask.Arg arg = new CompleteTask.Arg();
        arg.setContext(context);
        arg.setActionType(null);
        arg.setTaskId(taskId.replace(BPMConstants.LATENCY_SUFFIX, ""));
        arg.setConditionMap(variables);
        log.info("completeLatencyTask:taskId:{},variables:{}", taskId, variables);
        return paasWorkflowResource.completeLatencyTask(getHeaders(context), arg);
    }


    public CompleteTask.Result completeTask(RemoteContext context, String taskId, String actionType, Map<String, Object> variables, String opinion,
                                            Integer addOrReplaceNextTaskAssignee, Map<String, Object> nextTaskAssignee, Boolean ignoreNoBlockValidate) throws Exception {
        CompleteTask.Arg arg = new CompleteTask.Arg();
        arg.setOutContext(context);
        arg.setTaskId(taskId);
        arg.setActionType(actionType);
        arg.setConditionMap(variables);
        arg.setOpinion(opinion);
        arg.setNextTaskAssignee(nextTaskAssignee);
        arg.setAddOrReplaceNextTaskAssignee(addOrReplaceNextTaskAssignee);
        arg.setIgnoreNoBlockValidate(ignoreNoBlockValidate);

        log.info("completeTask:taskId:{},variables:{}", taskId, variables);
        return paasWorkflowResource.completeTask(getHeaders(context), arg);
    }

    public PageResult<Task> getTasksByCondition(RemoteContext context, String applicantId, String instanceId,
                                                Page pageInfo, TaskState state, String assigneeId,
                                                String entityId, String objectId, String sourceWorkflowId) {
        GetTaskByPage.Arg arg = new GetTaskByPage.Arg();
        arg.setContext(context);
        arg.setApplicantId(applicantId);
        arg.setInstanceId(instanceId);
        arg.setPageInfo(pageInfo);
        arg.setAssigneeId(assigneeId);
        arg.setEntityId(entityId);
        arg.setObjectId(objectId);
        arg.setSourceWorkflowId(sourceWorkflowId);
        arg.setState(state);

        GetTaskByPage.Result result = paasWorkflowResource.getTasks(getHeaders(context), arg);
        if (result.getResult() != null) {
            return result.getResult();
        } else {
            return new PageResult<>();
        }
    }

    /**
     * 获取当前人进行中的任务
     * @param context
     * @param applicantId
     * @param instanceId
     * @param assigneeId
     * @param entityId
     * @param objectId
     * @param sourceWorkflowId
     */
    public List<Task> getCurrentUserInProgressTasks(RemoteContext context, String applicantId, String instanceId, String assigneeId,
                                                    String entityId, String objectId, String sourceWorkflowId) {
        //查询当前实例下所有进行中当任务
        PageResult<Task> tasks = getTasksByCondition(
                context,
                applicantId,
                instanceId,
                new Page(1000, 1, null, true),
                TaskState.in_progress,
                assigneeId,
                entityId,
                objectId,
                sourceWorkflowId);

        List<Task> dataList = tasks.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            //获取当前人进行中的任务
            return dataList.stream().filter(tasksByInstanceId -> {
                List<String> candidateIds = tasksByInstanceId.getCandidateIds();
                if (CollectionUtils.isNotEmpty(candidateIds)) {
                    return candidateIds.contains(assigneeId) && TaskState.in_progress.equals(tasksByInstanceId.getState());
                }
                return false;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public PageResult<Task> getTasksBySourceWorkflowIdAndState(RemoteContext context,
                                                               Page pageInfo, TaskState state, String sourceWorkflowId) {
        return getTasksByCondition(context,null,null,pageInfo,state,null,null,null,sourceWorkflowId);
    }

    public List<Task> getTasksByInstanceId(RemoteContext context, String instanceId) {
        PageResult<Task> tasks = getTasksByCondition(context, null, instanceId, new Page(1000, 1, null, true), null, null, null, null, null);
        return tasks.getDataList();
    }

    public PageResult<Task> getAutomaticAndQuartzTasks(RemoteContext context, String instanceId, NodeType taskType, TaskState taskState, Boolean isDelayTask, String id, String objectId) {
        FindAutoTasks.Arg arg = new FindAutoTasks.Arg();
        arg.setContext(context);
        arg.setState(taskState);
        arg.setType(taskType);
        arg.setWorkflowInstanceId(instanceId);
        arg.setIsDelayTask(isDelayTask);
        arg.setId(id);
        arg.setObjectId(objectId);
        arg.setPageInfo(Page.getPage(1,1000));
        FindAutoTasks.Result result = paasWorkflowResource.getAutomaticAndQuartzTasks(getHeaders(context), arg);
        if (result.getResult() != null) {
            return result.getResult();
        } else {
            return new PageResult<>();
        }
    }

    public PageResult<Task> getAutoTasksByInstanceId(RemoteContext context, String instanceId,String entityId, TaskState state, String sourceWorkflowId,Long startTime, Long endTime, int pageNumber,int pageSize) {
        FindAutoTasks.Arg arg = new FindAutoTasks.Arg();
        arg.setContext(context);
        arg.setWorkflowInstanceId(instanceId);
        arg.setEntityId(entityId);
        arg.setState(state);
        arg.setSourceWorkflowId(sourceWorkflowId);
        arg.setStartTime(startTime);
        arg.setEndTime(endTime);
        arg.setPageInfo(Page.getPage(pageNumber,pageSize));
        FindAutoTasks.Result result = paasWorkflowResource.getAutomaticAndQuartzTasks(getHeaders(context), arg);
        if (result.getResult() != null) {
            return result.getResult();
        } else {
            return new PageResult<>();
        }
    }

    public List<Task> getAllInprogressTasksBySourceWorkflowId(RemoteContext context, String sourceWorkflowId) {
        if (Strings.isNullOrEmpty(sourceWorkflowId)) {
            return Lists.newArrayList();
        }
        List<Task> tasks = Lists.newArrayList();
        Page tempPage = getPage(1000);
        PageResult<Task> list = this.getTasksByCondition(context, null, null, tempPage, TaskState.in_progress_or_error,
                null, null, null, sourceWorkflowId);

        if (CollectionUtils.isEmpty(list.getDataList())) {
            return tasks;
        }
        tasks.addAll(list.getDataList());
//        while (list.getDataList().size() == tempPage.getPageSize()) {
//            tempPage.setPageNumber(tempPage.getPageNumber() + 1);
//            list = this.getTasksByCondition(context, null, null, tempPage, TaskState.in_progress_or_error,
//                    null, null, null, sourceWorkflowId);
//            tasks.addAll(list.getDataList());
//        }
        do {
            tempPage.setPageNumber(tempPage.getPageNumber() + 1);
            list = this.getTasksByCondition(context, null, null, tempPage, TaskState.in_progress_or_error,
                    null, null, null, sourceWorkflowId);
            tasks.addAll(list.getDataList());
        } while (list.getDataList().size() == tempPage.getPageSize());
        return tasks;
    }



    public List<Task> getAllInprogressTasks(RemoteContext context,String entityId) {
        List<Task> tasks = Lists.newArrayList();
        Page tempPage = getPage(1000);
        PageResult<Task> list = this.getTasksByCondition(context, null, null, tempPage, TaskState.in_progress_or_error,
                null, entityId, null, null);

        if (CollectionUtils.isEmpty(list.getDataList())) {
            return tasks;
        }
        tasks.addAll(list.getDataList());
        do {
            tempPage.setPageNumber(tempPage.getPageNumber() + 1);
            list = this.getTasksByCondition(context, null, null, tempPage, TaskState.in_progress_or_error,
                    null, entityId, null, null);
            tasks.addAll(list.getDataList());
        } while (list.getDataList().size() == tempPage.getPageSize());
        return tasks;
    }

    public PageResult<Task> getTasksByInstanceIds(RemoteContext context, String workflowInstanceId,
                                                  List<String> activityInstanceIds) {
        return getTasksByInstanceIdsWithPage(context, workflowInstanceId, activityInstanceIds, false,null);
    }

    public PageResult<Task> getTasksByInstanceIdsWithPage(RemoteContext context, String workflowInstanceId,
                                                          List<String> activityInstanceIds, Boolean containTagTask, Page pageInfo) {
        if (Strings.isNullOrEmpty(workflowInstanceId)) {
            return new PageResult<>();
        }
        GetTaskByActivityInstanceIds.Arg arg = new GetTaskByActivityInstanceIds.Arg();
        arg.setContext(context);
        arg.setInstanceId(workflowInstanceId);
        arg.setContainTagTask(containTagTask);

        if (CollectionUtils.isNotEmpty(activityInstanceIds)) {
            //[4.0]  [4]
            arg.setActivityInstanceIdList(activityInstanceIds.stream().map(item -> {
                int activityInstanceId = (int) Double.parseDouble(item);
                return String.valueOf(activityInstanceId);
            }).collect(Collectors.toList()));
        }


        arg.setPageInfo(pageInfo);

        GetTaskByActivityInstanceIds.Result restResult = paasWorkflowResource.getTaskByActivityInstanceIds(getHeaders
                (context), arg);
        if (restResult.getResult() == null) {
            return new PageResult<>();
        }
        return restResult.getResult();
    }

    public Task getTaskByInstanceId(RemoteContext context, String workflowInstanceId, String activityInstanceId, boolean needThrowException) {
        List<Task> tasks = getTasksByInstanceIds(context, workflowInstanceId, Lists.newArrayList(activityInstanceId)).getDataList();
        if (tasks.size() > 0) {
            return tasks.get(0);
        } else {
            if(needThrowException){
                throw new BPMTaskNotFoundException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_FOUND);
            }
            return null;
        }
    }

    public PageResult<WorkflowInstance> getAllWorkflowInstances(RemoteContext context, String entityId,String objectId,
                                                                InstanceState state, String workflowName, String sourceWorkflowId) {
        Page tempPage = getPage(DEFAULT_PAGE_SIZE);

        PageResult<WorkflowInstance> result = this.getWorkflowInstances(context,
                sourceWorkflowId, null, null, entityId, objectId, state, tempPage, workflowName);

        if (result == null || result.getTotal() == 0) {
            return new PageResult<>();
        }
        PageResult<WorkflowInstance> totalResult = result;
        //获取所有的实例
        while (result.getResult().size() == tempPage.getPageSize()) {
            tempPage.setPageNumber(tempPage.getPageNumber() + 1);
            result = this.getWorkflowInstances(context,
                    null, null, null, entityId, objectId, state, tempPage, workflowName);
            totalResult.getResult().addAll(result.getDataList());
        }

        return totalResult;

    }



    public boolean changeCandidates(RemoteContext context, String taskId, List<String> candidateIds,String modifyOpinion) {
        ChangeCandidates.Arg arg = new ChangeCandidates.Arg();
        arg.setCandidateIds(candidateIds);
        arg.setTaskId(taskId);
        arg.setContext(context);
        arg.setModifyOpinion(modifyOpinion);
        ChangeCandidates.Result result = paasWorkflowResource.changeCandidates(getHeaders(context), arg);
        return result.isSuccess();
    }

    private PageResult<Task> getAllTasksByObjectId(RemoteContext context, String objectId, TaskState state) {
        Page tempPage = getPage(DEFAULT_PAGE_SIZE);

        PageResult<Task> result = this.getTasksByCondition(context, null, null, tempPage, state, null, null,
                objectId, null);

        if (result == null || result.getTotal() == 0) {
            return new PageResult<>();
        }
        PageResult<Task> totalResult = result;
        //获取所有的实例
        while (result.getResult().size() == tempPage.getPageSize()) {
            tempPage.setPageNumber(tempPage.getPageNumber() + 1);
            result = this.getTasksByCondition(context, null, null, tempPage, state, null, null,
                    objectId, null);
            totalResult.getResult().addAll(result.getDataList());
        }

        return totalResult;
    }

    public AfterRetry.RetryResult afterActionRetry(RemoteContext context, String taskId, int rowNum, int executeType) {
        AfterRetry.RetryResult result;
        try{
            result = paasWorkflowResource.afterRetry(getHeaders(context), getAfterRetryArg(context, taskId, rowNum, executeType)).getResult();
        }catch (SocketTimeoutException e){
            throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_AFTER_ACTION_IS_WAITING);
        }
        return getAfterRetryResult(result, executeType);


    }

    private AfterRetry.Arg getAfterRetryArg(RemoteContext context, String taskId, int rowNum, int executeType) {
        AfterRetry.Arg arg = new AfterRetry.Arg();
        arg.setExecuteType(executeType);
        arg.setTaskId(taskId);
        arg.setRownum(rowNum);
        arg.setContext(context);
        return arg;
    }

    private AfterRetry.RetryResult getAfterRetryResult(AfterRetry.RetryResult result, int executeType) {
        if (result.isSuccess() && Strings.isNullOrEmpty(result.getMessage())) {
            if (("" + executeType).equals(AfterActionExecution.AfterActionOperation.ignore.getExecuteType())) {
                result.setMessage(BPMI18N.PAAS_FLOW_BPM_PAAS_EXECUTE_IGNORE_SUCCESS.text());
            } else {
                result.setMessage(BPMI18N.PAAS_FLOW_BPM_SUCCESSFUL_RE_IMPLEMENTATION.text());
            }
        }
        return result;
    }

    public AfterRetry.RetryResult autoTaskAfterRetry(RemoteContext context, String taskId, int rowNum, int executeType) {
        AfterRetry.RetryResult result = paasWorkflowResource.autoTaskAfterRetry(getHeaders(context), getAfterRetryArg(context, taskId, rowNum, executeType)).getResult();
        return getAfterRetryResult(result, executeType);
    }


    public RecoveryCancelInstance.Result recoveryCancelInstance(RemoteContext context, String instanceId) {
        return paasWorkflowResource.recoveryCancelInstance(getHeaders(context),
                context.getTenantId(), instanceId);
    }

    public Pair getUncompletedTasks(RemoteContext context, String objectId) {
        StopWatch stopWatch = StopWatch.createUnStarted("getUncompletedTasks");
        //1.查询对象下所有任务
        List<Task> tasks = this.getAllTasksByObjectId(context, objectId, null).getDataList();
        //查询等待节点
        List<Task> autoAndDelayTasks = getAutoAndDelayTasks(context, objectId);

        if(CollectionUtils.isNotEmpty(autoAndDelayTasks)){
            tasks.addAll(autoAndDelayTasks);
        }

        if (CollectionUtils.isEmpty(tasks)) {
            return new Pair(Lists.newArrayList(), Lists.newArrayList());
        }

        stopWatch.lap("getAllTasks");
        //3.获取所有task的instanceId
        Set<String> instanceIds = tasks.stream().map(Task::getWorkflowInstanceId).collect(Collectors.toSet());

        //4.查询所有未完成的流程实例
        List<WorkflowInstance> instances = getWorkflowInstances(context, null, null, instanceIds, null, null,
                InstanceState.in_progress_or_error, new Page(DEFAULT_PAGE_SIZE, 1, null, true), null).getDataList();
        stopWatch.lap("getInprogressInstances");

        //5.根据流程实例，查出所有未完成的(instanceId:activityInstanceIds)
        Map<String, WorkflowInstance> instanceIdAndInstanceMap = instances.stream().collect(Collectors.toMap(instance -> instance.getId(), instance -> instance));
        Set<String> finalInstanceIds = instanceIdAndInstanceMap.keySet();

        Map<String, List<String>> instanceIdAndActivityInstanceIds = getUncompletedActivityInstanceIds(instances);

        //6.筛选出待办任务
        List<Task> uncompletedTasks = tasks.stream()
                .filter(task -> (task.isInprogress() || (TaskState.suspend.equals(task.getState()) && BPMConstants.TagType.TAG.equals(task.getNodeType())))
                        && finalInstanceIds.contains(task.getWorkflowInstanceId()))
                // auto_task 没有sourceWorkflowId
                .peek(task->task.setSourceWorkflowId(instanceIdAndInstanceMap.get(task.getWorkflowInstanceId()).getSourceWorkflowId()))
                .collect(Collectors.toList());

        //过滤掉当前对象下的待办，不需要再查一遍了
        uncompletedTasks.forEach(t -> {
            String instanceId = t.getWorkflowInstanceId();
            List<String> activityInstanceIds = instanceIdAndActivityInstanceIds.get(instanceId);
            if (CollectionUtils.isNotEmpty(activityInstanceIds)) {
                activityInstanceIds.remove(t.getActivityInstanceId() + "");
                if (activityInstanceIds.size() == 0) {
                    instanceIdAndActivityInstanceIds.remove(instanceId);
                }
            }
        });

        //7.查询剩余未完成的任务
        Set<Task> otherTasks = getBatchTasks(context, instanceIdAndActivityInstanceIds);
        stopWatch.lap("getBatchTasks");
        //8.
        uncompletedTasks.addAll(otherTasks);
        stopWatch.logSlow(100);

        return new Pair<>(uncompletedTasks, instances);
    }

    private List<Task> getAutoAndDelayTasks(RemoteContext context, String objectId) {
        PageResult<Task> autoTaskRes = getAutomaticAndQuartzTasks(context, null, null, null, null, null, objectId);
        return CollectionUtils.isEmpty(autoTaskRes.getDataList()) ? Lists.newArrayList() : autoTaskRes.getResult();
    }

    private Map<String, List<String>> getUncompletedActivityInstanceIds(List<WorkflowInstance> instances) {
        Map<String, List<String>> instanceIdAndActivityInstanceIds = Maps.newHashMap();
        for (WorkflowInstance instance : instances) {
            String instanceId = instance.getId();
            if (!instance.isCompleted()) {
                List<String> uncompletedActivityInstanceIds = Lists.newArrayList();
                instance.getActivityInstances().forEach(activityInstance -> {
                    if (activityInstance.getEnd() == null) {
                        uncompletedActivityInstanceIds.add(activityInstance.getId() + "");
                    }
                });

                if (uncompletedActivityInstanceIds.size() > 0) {
                    instanceIdAndActivityInstanceIds.put(instanceId, uncompletedActivityInstanceIds);
                }
            }
        }

        return instanceIdAndActivityInstanceIds;
    }


    private Set<Task> getBatchTasks(RemoteContext context, Map<String, List<String>> instanceIdAndActivityInstanceIds) {
        ParallelUtils.ParallelTask processor = ParallelUtils.createParallelTask();
        Set<Task> tasks = Sets.newConcurrentHashSet();
        if (instanceIdAndActivityInstanceIds != null && instanceIdAndActivityInstanceIds.size() > 0) {
            instanceIdAndActivityInstanceIds.forEach((instanceId, activityInstanceIds) ->
                    processor.submit(Thread.currentThread().getName() + "-getBatchTasks-"+ TraceContext.get().getTraceId(), () -> {
                        List<Task> tempTasks = getTasksByInstanceIds(context, instanceId, activityInstanceIds).getDataList();
                        tasks.addAll(tempTasks);
                    })
            );
        }

        try {
            processor.await(20, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("getBatchTasks", e);
        }

        return tasks;
    }

    public boolean batchDeleteDefinition(RemoteContext context) {
        BatchDeleteDefinition.Arg arg = new BatchDeleteDefinition.Arg();
        arg.setContext(context);
        BatchDeleteDefinition.Result result = paasWorkflowResource.batchDeleteDefinition(getHeaders(context), arg);
        return result.isSuccess();
    }

    public boolean batchDeleteInstance(RemoteContext context) {
        BatchDeleteInstance.Result result = paasWorkflowResource.batchDeleteInstance(getHeaders(context), context.getTenantId());
        return result.isSuccess();
    }

    private Map<String, String> getHeaders(RemoteContext context) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put(RestConstant.GRAY_VALUE, context.getTenantId());
        return headers;
    }


    public WorkflowLogs.Result getWorkflowLogs(RemoteContext context, String sourceWorkflowId, Page page) {
        WorkflowLogs.Arg arg = new WorkflowLogs.Arg();
        arg.setContext(context);
        arg.setPageInfo(page);
        arg.setSourceWorkflowId(sourceWorkflowId);
        return paasWorkflowResource.getWorkflowLogs(getHeaders(context), arg);
    }

    public AfterRetry.RetryResult instanceAfterActionRetry(RemoteContext context, String instanceId, int rowNum, int executeType) {
        InstanceAfterRetry.Arg arg = new InstanceAfterRetry.Arg();
        arg.setContext(context);
        arg.setId(instanceId);
        arg.setExecuteIndex(rowNum);
        arg.setExecuteType(executeType);
        InstanceAfterRetry.Result result = paasWorkflowResource.instanceAfterRetry(getHeaders(context), arg);

        AfterRetry.RetryResult retryResult = new AfterRetry.RetryResult();
        if (result.isSuccess()) {
            retryResult.setSuccess(Boolean.TRUE);
            if ((String.valueOf(executeType)).equals(AfterActionExecution.AfterActionOperation.ignore.getExecuteType())) {
                retryResult.setMessage(BPMI18N.PAAS_FLOW_BPM_PAAS_EXECUTE_IGNORE_SUCCESS.text());
            } else {
                retryResult.setMessage(BPMI18N.PAAS_FLOW_BPM_SUCCESSFUL_RE_IMPLEMENTATION.text());
            }
        }else {
            retryResult.setSuccess(Boolean.FALSE);
            retryResult.setMessage(result.getErrMessage());
        }
        return retryResult;
    }

    public Boolean replaceTaskHandlers(RemoteContext context, String taskId, List<String> candidateIds) {
        ChangeCandidates.Arg arg = new ChangeCandidates.Arg();
        arg.setCandidateIds(candidateIds);
        arg.setTaskId(taskId);
        arg.setContext(context);
        ChangeCandidates.Result result = paasWorkflowResource.replaceTaskHandlers(getHeaders(context), arg);
        return result.isSuccess();
    }

    public RegenerateHandler.Result regenerateHandler(RemoteContext context, String taskId) {
        RegenerateHandler.Arg arg = new RegenerateHandler.Arg();
        arg.setContext(context);
        arg.setTaskId(taskId);
        return paasWorkflowResource.regenerateHandler(context.getTenantId(), arg);
    }

    public List<AutoTask> findDelayTask(RemoteContext context, String objectId, String workflowInstanceId, Integer pageNumber, Integer pageSize) {
        FindDelayTask.Arg arg = new FindDelayTask.Arg();
        arg.setContext(context);
        arg.setObjectId(objectId);
        arg.setWorkflowInstanceId(workflowInstanceId);
        Page page = new Page();
        page.setPageNumber(pageNumber);
        page.setPageSize(pageSize);
        arg.setPage(page);
        return paasWorkflowResource.findDelayTask(context.getTenantId(), arg).getResult();
    }

    public void updateDefinitionStatusMap(RemoteContext context, Map<String, Boolean> sourceWorkflowIdAndEnable) {
        UpdateDefinitionStatusMap.Arg arg = new UpdateDefinitionStatusMap.Arg();
        arg.setContext(context);
        arg.setSourceWorkflowIdAndEnable(sourceWorkflowIdAndEnable);
        paasWorkflowResource.updateDefinitionStatusMap(getHeaders(context), arg);
    }

    public void updateDefinitionStatus(RemoteContext context, String sourceWorkflowId, Boolean enable) {
        UpdateDefinitionStatus.Arg arg = new UpdateDefinitionStatus.Arg();
        arg.setContext(context);
        arg.setSourceWorkflowId(sourceWorkflowId);
        arg.setEnable(enable);
        paasWorkflowResource.updateDefinitionStatus(getHeaders(context), arg);
    }

    public OperateTask.Result operateTask(RemoteContext context, String taskId, String type, String opinion, TaskTagInfoArg tagInfoArg) {
        OperateTask.Arg arg = new OperateTask.Arg();
        arg.setOutContext(context);
        arg.setTaskId(taskId);
        arg.setType(type);
        arg.setOpinion(opinion);
        if(Objects.nonNull(tagInfoArg)){
            arg.setTagInfo(new OperateTask.TagInfo(tagInfoArg.getExtraNodeAssignee(), tagInfoArg.getSequence()));
        }
        return paasWorkflowResource.operateTask(getHeaders(context), arg);
    }

    public RemindTask.Result remindTask(RemoteContext context, String taskId, String content, List<String> remindPersons) {
        RemindTask.Arg arg = new RemindTask.Arg();
        arg.setOutContext(context);
        arg.setTaskId(taskId);
        arg.setContent(content);
        arg.setRemindPersons(remindPersons);
        return paasWorkflowResource.remindTask(getHeaders(context), arg);
    }


}
