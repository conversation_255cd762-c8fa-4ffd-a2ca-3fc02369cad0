package com.facishare.bpm.remote.model.config;

import com.facishare.bpm.define.conf.DefineConfigHelper;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.paas.I18N;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey.define_type_custom;

/**
 * <AUTHOR>
 * @since 5.7
 */
@Slf4j
@Data
public class BPMObjectSupportConfig {

    public static BPMObjectSupportConfig objectSupports;

    static  {
        ConfigFactory.getConfig("fs-bpm-object-support-config", (config) -> {
            objectSupports = JsonUtil.fromJson(config.getString(), BPMObjectSupportConfig.class);
            log.info("load config  action names :{} ", config.getString());
        });
    }

    private volatile Map<String, ObjectBaseConfig> objectBaseConfig;

    private volatile List<String> whiteList;

    /**
     * 除了预制自定义对象
     * 自定义对象外的
     * [预制对象]  配置支持在待处理业务流程列表中展示操作按钮
     */
    private volatile Map<String,List<String>> taskListSupportOperationButton;
    /**
     * 定义节点支持特殊操作的按钮信息
     */
    private volatile Map<String, SpecialOperationButtonInfo>  taskSupportSpecialOperationButton;


    @Data
     class ObjectBaseConfig {
        /**
         * 当前对象支持的操作
         */
        private Map<String, String> actions;
        /**
         * 关联到当前对象的对象apiName
         */
        private List<String> references;
        /**
         * 自定义对象使用
         *  有关 不支持 的选择和创建有关老对象的配置，
         */
        private List<String> notSupportOldReferences;

        /**
         * 不支持的关联或master对象
         */
        private List<String> blacklistMdOrReferences;


        public Map<String, String> getI18NActions() {
            return actions.keySet().stream().collect(Collectors.toMap((item) -> item, (item) -> I18N.text(actions.get(item))));
        }
    }

    @Data
    class SpecialOperationButtonInfo{
        private Map<String,String> operation;
        private List<String> objectApiName;
    }

    /**
     * 预设对象 支持操作白名单
     *
     * taskListSupportOperationButton:{
     *  AccountObj:[addteammember,return]
     * }
     */
    public boolean hasTaskListShowOperationButton(String entityId, String actionCode) {

        if (MapUtils.isEmpty(taskListSupportOperationButton) || Strings.isBlank(entityId) || Strings.isBlank(actionCode)) {
            return false;
        }

        //优先使用对象自己的白名单配置
        // 添加团队成员及更换负责人  目前看客户 订单都支持  其他对象待测试
        List<String> actionCodes = taskListSupportOperationButton.get(entityId);
        if (Objects.nonNull(actionCodes)) {
            return actionCodes.contains(actionCode);
        }

        //如果属于公用配置  则按照对象灰度
        List<String> commonOperation = taskListSupportOperationButton.get("commonOperation");

        return Objects.nonNull(commonOperation) && commonOperation.contains(actionCode);
    }


    public Boolean isCustomObj(String tenantId,String apiName){
        if(apiName.endsWith(BPMConstants.MetadataKey.postfix)){
            return true;
        }
        if(whiteList.contains(apiName)){
            return true;
        }
        if(DefineConfigHelper.getObjectSupportByType(tenantId,FlowType.workflow_bpm).contains(apiName)){
            return true;
        }
        if(objectBaseConfig.keySet().contains(apiName)){
            return false;
        }
        return false;
    }

    public Boolean isOldObj(String tenantId,String apiName){
        Boolean rst = isCustomObj(tenantId,apiName);
        if(rst==null){
            return false;
        }
        return !rst;
    }

    public boolean isBPMSupport(String tenantId,String apiName){
        if(apiName.endsWith(BPMConstants.MetadataKey.postfix)){
            return true;
        }
        if(whiteList.contains(apiName)){
            return true;
        }
        if(DefineConfigHelper.getObjectSupportByType(tenantId,FlowType.workflow_bpm).contains(apiName)){
            return true;
        }
        if(objectBaseConfig.keySet().contains(apiName)){
            return true;
        }
        return false;
    }

    public List<String> getOldObjectReferences(String apiName){
        if(objectBaseConfig.get(apiName)==null){
            return Lists.newArrayList();
        }
        return objectBaseConfig.get(apiName).getReferences();
    }

    public boolean isPreDefineObj(String apiName) {
        if(whiteList.contains(apiName)){
            return true;
        }
        if(objectBaseConfig.keySet().contains(apiName)){
            return true;
        }
        return false;
    }


    /**
     * 对象不支持的lookup对象或master_detail对象
     * @param apiName
     * @return
     */
    public List<String> getBlacklistObjectReferences(String apiName) {
        if (objectBaseConfig.get(apiName) == null) {
            return Lists.newArrayList();
        }
        List<String> blacklistMdOrReferences = objectBaseConfig.get(apiName).getBlacklistMdOrReferences();
        if (CollectionUtils.isEmpty(blacklistMdOrReferences)) {
            return Lists.newArrayList();
        }
        return blacklistMdOrReferences;
    }

    public Map<String,String> getActions(String tenantId,String apiName){
        if(isCustomObj(tenantId,apiName)){
            return objectBaseConfig.get(define_type_custom).getI18NActions();
        }else {
            return objectBaseConfig.get(apiName).getI18NActions();
        }


    }

    public List<String> getApiNameBySpecialOperationType(String operationType){
        List<String> result = Lists.newArrayList();
        if(StringUtils.isBlank(operationType) || Objects.isNull(objectSupports.getTaskSupportSpecialOperationButton())) {
            return result;
        }
        SpecialOperationButtonInfo specialOperationButtonInfo = objectSupports.getTaskSupportSpecialOperationButton().get(operationType);
        if(Objects.isNull(specialOperationButtonInfo)) {
            return result;
        }
        return specialOperationButtonInfo.getObjectApiName();
    }


    public Map<String, String> getOperationByOperationType(String operationType) {
        if (MapUtils.isEmpty(objectSupports.getTaskSupportSpecialOperationButton())) {
            return Maps.newHashMap();
        }
        SpecialOperationButtonInfo specialOperationButtonInfo = objectSupports.getTaskSupportSpecialOperationButton().get(operationType);
        if (Objects.isNull(specialOperationButtonInfo)) {
            return Maps.newHashMap();
        }
        return specialOperationButtonInfo.getOperation();
    }



}
