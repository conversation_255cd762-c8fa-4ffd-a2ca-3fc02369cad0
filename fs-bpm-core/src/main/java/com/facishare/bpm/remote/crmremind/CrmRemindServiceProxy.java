package com.facishare.bpm.remote.crmremind;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by wangz on 17-11-27.
 */
@Slf4j
@Component
public class CrmRemindServiceProxy {
    private final static String REMIND_BUSINESS_TASK = "457";

    @Autowired
    private PaasWorkflowServiceProxy workflowServiceProxy;

    /**
     * 消除待办任务的飘数
     *
     * @param serviceManager
     * @return
     */
    public boolean clearTaskRemind(RefServiceManager serviceManager, Integer remindCount) {
//        RemindRecord.Result ret = crmRemindResource.clear(getHeaders(context), REMIND_BUSINESS_TASK,
//                Lists.newArrayList(new RemindRecord.Arg(Integer.valueOf(context.getUserId()), remindCount)));
//        if (!ret.success()) {
//            log.error("clearTaskRemind failed : CONTEXT={}");
//        }
//        return ret.success();
        workflowServiceProxy.getTasksByAssigneeId(serviceManager.getContext(), false, null, new Page());
        return true;
    }


    private Map<String, String> getHeaders(RemoteContext context) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", context.getTenantId());
        headers.put("x-fs-userInfo", "0"); //userInfo 放在body中
        return headers;
    }
}
