package com.facishare.bpm.remote.metadata.impl;

import com.facishare.bpm.define.conf.DefineConfigHelper;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMRuntimeException;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList;
import com.facishare.bpm.model.resource.newmetadata.*;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.resource.sfabussiness.GetDescribeLayout;
import com.facishare.bpm.proxy.MetaDataAuthProxy;
import com.facishare.bpm.proxy.NewPaasMetadataProxy;
import com.facishare.bpm.proxy.SFABusinessProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.SimpleMetadataAction;
import com.facishare.bpm.remote.model.SimpleMetadataDesc;
import com.facishare.bpm.remote.model.config.BPMObjectSupportConfig;
import com.facishare.bpm.util.MetadataUtils;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.ObjectDescribeListSortUtil;
import com.facishare.bpm.utils.model.FlowType;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.paas.I18N;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.paas.metadata.api.condition.TermConditions;
import com.facishare.rest.core.annotation.SocketConfigParam;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_IN;
import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_OUT;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.MetadataKey.define_type_custom;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.REPLACE_WHEN_NOT_FOUND;
import static com.facishare.bpm.util.MetadataUtils.validateAndCorrectDataValue;
import static com.facishare.bpm.utils.MetadataUtils.*;

/**
 * Created by wangz on 17-1-4.
 */
@Service
@Slf4j
public class MetadataServiceImpl implements MetadataService {

    @Autowired
    private NewPaasMetadataProxy newMetadataProxy;

    @Autowired
    private SFABusinessProxy sfaBusinessProxy;

    @Autowired
    private MetaDataAuthProxy metaDataAuthProxy;

    @Autowired
    private NewPaasMetadataProxy newPaasMetadataProxy;


    //TODO 将来存在国际化的话,会存在问题
    private LoadingCache<String, GetCountryAreaOptions.GetCountryAreaOptionsResultDetail> countryCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .initialCapacity(100).build(new CacheLoader<String, GetCountryAreaOptions.GetCountryAreaOptionsResultDetail>() {
                                            @Override
                                            public GetCountryAreaOptions.GetCountryAreaOptionsResultDetail load(@NotNull String key) throws Exception {
                                                RemoteContext context = new RemoteContext("", "1", "CRM", "-10000");
                                                GetCountryAreaOptions.GetCountryAreaOptionsResultDetail areaOptions =
                                                        newMetadataProxy.getCountryAreaOptions(context);
                                                return areaOptions;
                                            }
                                        }
            );
    private LoadingCache<String, Map<String, Object>> objDescCache = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .initialCapacity(50)
            .maximumSize(100).build(new CacheLoader<String, Map<String, Object>>() {
                                            @Override
                                            public Map<String, Object> load(@NotNull String key) throws Exception {
                                                return null;
                                            }
                                        }
            );



    @Override
    public Map<String, Object> findDescribe(RemoteContext context, String descApiName, boolean containAllFields, boolean includeStatistics) {
        Map describe = newMetadataProxy.findDescribe(getNewContext(context), descApiName, containAllFields, includeStatistics,
                false, false);
        return JacksonUtil.fromJsonOfGeneric(JacksonUtil.toJson(describe), Map.class);

    }

    @Override
    public String getActionNameByActionCode(RemoteContext context, String descApiName, String actionCode) {
        Map<String, String> actionCodeAndNames = getActionNamesFromDesc(context, descApiName, actionCode);

        return actionCodeAndNames.get(actionCode);
    }


    @Override
    public Map<String, Object> createData(RemoteContext context, String descApiName, String jsonData) {
        return newMetadataProxy.createData(getNewContext(context), descApiName, jsonData).getObject_data();
    }

    private RemoteContext getNewContext(RemoteContext context) {
        if (context.isOuterPerson()) {
            RemoteContext temp = new RemoteContext(null, context.getTenantId(), context.getAppId(), context.getUserId());
            temp.setUserId(BPMConstants.CRM_SYSTEM_USER);
            return temp;
        }
        return context;
    }

    @Override
    public Map<String, Object> updateData(RemoteContext context, String descApiName, String dataId, String jsonData, boolean isPartUpdate, boolean isBatch) {
        return updateData(context, descApiName, dataId, jsonData, isPartUpdate, false, false, null, isBatch, "").getObject_data();
    }

    /**
     * @param applyValidationRule     添加验证规则
     * @param applyDataPrivilegeCheck 验证数据权限
     * @return
     */
    @Override
    public UpdateData.UpdateDataResultDetail updateData(RemoteContext context, String descApiName, String dataId, String jsonData, boolean isPartUpdate, boolean applyValidationRule, boolean applyDataPrivilegeCheck, Map<String, Object> describe, boolean isBatch, String modelName) {
        Map<String, Object> data = JacksonUtil.fromJsonOfGeneric(jsonData, Map.class);
        if (MapUtils.isEmpty(describe)) {
            describe = findDescribe(context, descApiName, true, false);
        }

        data = validateAndCorrectDataValue(data, describe);
        return newMetadataProxy.updateData(getNewContext(context), descApiName, dataId, JacksonUtil.toJson(data),
                false, isPartUpdate, applyValidationRule, applyDataPrivilegeCheck, isBatch, UUID.randomUUID().toString(), "", TraceContext.get().getSourceProcessId(), modelName);
    }

    @Override
    public UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String descApiName, String dataId, String jsonData, boolean isPartUpdate, boolean applyValidationRule, boolean applyDataPrivilegeCheck, Map<String, Object> describe, boolean isBatch, String modelName) {
        Map<String, Object> data = JacksonUtil.fromJsonOfGeneric(jsonData, Map.class);
        if (MapUtils.isEmpty(describe)) {
            describe = findDescribe(context, descApiName, true, false);
        }

        data = validateAndCorrectDataValue(data, describe);

        return newMetadataProxy.updateData(getNewContext(context), descApiName, dataId, JacksonUtil.toJson(data)
                , false, isPartUpdate, applyValidationRule, applyDataPrivilegeCheck
                , true, isBatch, UUID.randomUUID().toString(), "", "", TraceContext.get().getSourceProcessId() ,modelName);

    }

    /**
     * 自定义对象,需要将notValidate设置为true,跳过必填项,透传给玉乾
     * <p>
     * <p>
     * 跳过必填
     * 注意 这个方法如果数据冲突会返回冲突信息
     *
     * @param applyValidationRule     添加验证规则
     * @param applyDataPrivilegeCheck 验证数据权限
     */
    @Override
    public UpdateData.UpdateDataResultDetail updateDataSkipRequired(RemoteContext context, String descApiName, String dataId, String jsonData, boolean incrementalUpdate, boolean applyValidationRule, boolean applyDataPrivilegeCheck) {
        Map<String, Object> data = JacksonUtil.fromJsonOfGeneric(jsonData, Map.class);
        Map<String, Object> describe = findDescribe(context, descApiName, true, false);
        data = validateAndCorrectDataValue(data, describe);
        jsonData = JacksonUtil.toJson(data);
        return newMetadataProxy.updateData(
                getNewContext(context),
                descApiName,
                dataId,
                jsonData,
                UUID.randomUUID().toString(),//eventId
                "",
                "",
                TraceContext.get().getSourceProcessId(),
                "workflow_bpm",
                false,
                incrementalUpdate,
                applyValidationRule,
                applyDataPrivilegeCheck,
                false,
                true,
                false,
                true);
    }

    @Override
    public FindInternalDataById.FindInternalDataByIdResultDetail findDataById(RemoteContext context,
                                                                              String descApiName,
                                                                              String dataId,
                                                                              boolean includeLookupName,
                                                                              boolean includeDescribe,
                                                                              boolean includeStatistics,
                                                                              boolean isFillExtendField,
                                                                              boolean formatData, boolean skipRelevantTeam,
                                                                              SocketConfigParam.SocketConfig socketConfig) {
        if (Strings.isNullOrEmpty(dataId) || Strings.isNullOrEmpty(descApiName)) {
            log.warn("findDataById 必传字段未填写:entityId:{},objectId:{},includeDescribe:{}", descApiName, dataId, includeDescribe);
            throw new BPMRuntimeException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND);
        }
        if(skipRelevantTeam){
            //掩码 -10000 和系统管理员不返回,故此处修改为普通人员
            return newMetadataProxy.findInternalDataByIdAndSkipRelevantTeam(context, descApiName, dataId, includeDescribe, includeLookupName, true, includeStatistics, isFillExtendField, true, formatData, socketConfig);
        }else{
            return newMetadataProxy.findInternalDataById(context, descApiName, dataId, includeDescribe, includeLookupName, true, includeStatistics, isFillExtendField, true, formatData, socketConfig);
        }
    }

    @Override
    public FindInternalDataById.FindInternalDataByIdResultDetail findDataById(RemoteContext context,
                                                                              String descApiName,
                                                                              String dataId,
                                                                              boolean includeLookupName,
                                                                              boolean includeDescribe,
                                                                              boolean includeStatistics,
                                                                              boolean isFillExtendField,
                                                                              boolean formatData,boolean skipRelevantTeam,
                                                                              boolean includeDeleted,
                                                                              SocketConfigParam.SocketConfig socketConfig) {
        if (Strings.isNullOrEmpty(dataId) || Strings.isNullOrEmpty(descApiName)) {
            log.warn("findDataById 必传字段未填写:entityId:{},objectId:{},includeDescribe:{}", descApiName, dataId, includeDescribe);
            throw new BPMRuntimeException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND);
        }
        if(skipRelevantTeam){
            //掩码 -10000 和系统管理员不返回,故此处修改为普通人员
            return newMetadataProxy.findInternalDataByIdAndSkipRelevantTeam(context, descApiName, dataId, includeDescribe, includeLookupName, true, includeStatistics, isFillExtendField, true, formatData, includeDeleted, socketConfig);
        }else{
            return newMetadataProxy.findInternalDataById(context, descApiName, dataId, includeDescribe, includeLookupName, true, includeStatistics, isFillExtendField, true, formatData, includeDeleted, socketConfig);
        }
    }

    @Override
    public List<Map<String, Object>> findDataByQuery(RemoteContext context,
                                                     String descApiName,
                                                     List<IConditions> conditions) {
        RemoteContext systemContext = new RemoteContext(context.getEa(), context.getTenantId(), context.getAppId(), BPMConstants.CRM_SYSTEM_USER);
        FindDataByQuery.FindDataByQueryArg newArg = new FindDataByQuery.FindDataByQueryArg();
        newArg.setLimit(100);
        newArg.setConditions(conditions);
        FindDataByQuery.FindDataByQueryResultDetail dataByQueryResultDetail = newMetadataProxy.findDataByQuery(systemContext, descApiName, newArg);
        return dataByQueryResultDetail.getQueryResult().getData();
    }

    @Override
    public List<Map<String, Object>> findDataByQuery(String tenantId,
                                                     String ea,
                                                     String appId,
                                                     String apiName,
                                                     Map<String, String> conditionMap,
                                                     int pageSize,
                                                     int pageNumber) {
        RemoteContext systemContext = new RemoteContext(ea, tenantId, appId, BPMConstants.CRM_SYSTEM_USER);
        IConditions conditions = new TermConditions();
        if(MapUtils.isNotEmpty(conditionMap)){
            for(Map.Entry entry : conditionMap.entrySet()){
                conditions.addCondition((String) entry.getKey(), (String)entry.getValue());
            }
        }
        FindDataByQuery.FindDataByQueryArg newArg = new FindDataByQuery.FindDataByQueryArg();
        newArg.setOffset((pageNumber - 1) * pageSize);
        newArg.setLimit(pageSize);
        newArg.setConditions(Lists.newArrayList(conditions));
        FindDataByQuery.FindDataByQueryResultDetail dataByQueryResultDetail = newMetadataProxy.findDataByQuery(systemContext, apiName, newArg);
        return dataByQueryResultDetail.getQueryResult().getData();
    }

    @Override
    public PageResult<WorkflowInstanceVO> findDataByQuery(RemoteContext context,
                                                          String descApiName,
                                                          InstanceState state,
                                                          String sourceWorkflowId,
                                                          String workflowName,
                                                          String objectId,
                                                          int pageSize, int pageNumber) {
        IConditions termConditions = new TermConditions();
        if (Objects.nonNull(state)) {
            termConditions.addCondition(TransferDataConstants.MDField.state.getValue(), state.name());
        }
        if (Objects.nonNull(sourceWorkflowId)) {
            termConditions.addCondition(TransferDataConstants.MDField.sourceWorkflowId.getValue(), sourceWorkflowId);
        }
        if (Objects.nonNull(objectId)) {
            termConditions.addCondition(TransferDataConstants.MDField.objectId.getValue(), objectId);
        }
        if (Objects.nonNull(workflowName)) {
            termConditions.addCondition(TransferDataConstants.MDField.workflowName.getValue(), workflowName);
        }
        RemoteContext systemContext = new RemoteContext(context.getEa(), context.getTenantId(), context.getAppId(), BPMConstants.CRM_SYSTEM_USER);
        FindDataByQuery.FindDataByQueryArg newArg = new FindDataByQuery.FindDataByQueryArg();
        newArg.setOffset((pageNumber - 1) * pageSize);
        newArg.setLimit(pageSize);
        newArg.setConditions(Lists.newArrayList(termConditions));
        FindDataByQuery.FindDataByQueryResultData queryResult = newMetadataProxy.findDataByQuery(systemContext, descApiName, newArg).getQueryResult();
        PageResult<WorkflowInstanceVO> pageResult = new PageResult<>();
        List<Map<String, Object>> instances = queryResult.getData();
        if (CollectionUtils.isNotEmpty(instances)) {
            List<WorkflowInstanceVO> workflowInstanceVOList = instances.stream()
                    .map(WorkflowInstanceVO::create).collect(Collectors.toList());
            pageResult.setTotal(queryResult.getTotalNumber());
            pageResult.setDataList(workflowInstanceVOList);
        }
        return pageResult;
    }


    @Override
    public List<SimpleMetadataDesc> findDescsByTenantId(RemoteContext context,
                                                        List<String> apiNames, boolean includeFieldsDesc,boolean useGroupManager) {

        List<Map<String, Object>> descs = getAllDescs(getNewContext(context), includeFieldsDesc,useGroupManager);

        List<SimpleMetadataDesc> simpleMetadataDescs = Lists.newArrayList();
        if (descs != null) {
            simpleMetadataDescs = descs.stream()
                    .filter(desc -> {
                        if(StringUtils.isNotBlank((String) desc.get(MetadataKey.ORIGINAL_DESCRIBE_API_NAME))){
                            //变更单对象直接过滤掉
                            return false;
                        }
                        String apiName = (String) desc.get(MetadataKey.apiName);
                        if (CollectionUtils.isNotEmpty(apiNames) && !apiNames.contains(apiName)) {
                            return false;
                        }
                        if (Boolean.TRUE.equals(desc.getOrDefault(MetadataKey.isActive, true))) {
                            return define_type_custom.equals(desc.get(MetadataKey.defineType)) ||
                                    BPMObjectSupportConfig.objectSupports.isPreDefineObj(apiName) ||
                                    DefineConfigHelper.getObjectSupportByType(context.getTenantId(),FlowType.workflow_bpm).contains(apiName);
                        } else {
                            return false;
                        }

                    })
                    .map(desc -> fromDescMap(desc, includeFieldsDesc, null))
                    .collect(Collectors.toList());
        }
        return simpleMetadataDescs;
    }

    /**
     * @param context
     * @param apiNames
     * @param includeLookups
     * @param includeCountFieldLookupType 新增字段,获取关联关系
     * @return
     */
    @Override
    public List<Map<String, Object>> findDescsByApiNames(RemoteContext context, Collection<String> apiNames, boolean includeLookups, boolean includeCountFieldLookupType) {
        return newMetadataProxy.getDescribeByApiNames(context, apiNames, includeLookups, includeCountFieldLookupType);
    }

    @Override
    public List<Map<String, Object>> findDescsByApiNamesByCache(RemoteContext context, Collection<String> apiNames, boolean includeLookups, boolean includeCountFieldLookupType) {
        List<Map<String, Object>> result = Lists.newArrayList();
        List<String> noCacheApiNames = Lists.newArrayList();
        String tenantId = context.getTenantId();
        String lang = TraceContext.get().getLocale();
        for (String apiName : apiNames) {
            Map<String, Object> descMap = null;
            try {
                descMap = objDescCache.get(tenantId + "_" + apiName + "_" + includeLookups + "_" + includeCountFieldLookupType + "_" + lang);
            }catch (Exception e){

            }
            if (MapUtils.isNotEmpty(descMap)){
                result.add(descMap);
            }else {
                noCacheApiNames.add(apiName);
            }
        }
        if(CollectionUtils.isEmpty(noCacheApiNames)){
            return result;
        }
        List<Map<String, Object>> noCacheDesc = newMetadataProxy.getDescribeByApiNames(context, noCacheApiNames, includeLookups, includeCountFieldLookupType);
        if(CollectionUtils.isNotEmpty(noCacheDesc)){
            result.addAll(noCacheDesc);
            for (Map<String, Object> desc : noCacheDesc) {
                if (MapUtils.isNotEmpty(desc)){
                    objDescCache.put(tenantId + "_" + desc.get("api_name") + "_" + includeLookups + "_" + includeCountFieldLookupType + "_" + lang, desc);
                }
            }
        }

        return result;
    }


    @Override
    public List<SimpleMetadataDesc> findDescsByApiNames(RemoteContext context, List<String> apiNames, boolean
            includeLookups) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return Lists.newArrayList();
        }
        //1.查找指定的对象
        List<Map<String, Object>> descs = findDescsByApiNamesByCache(context, apiNames, true, false);

        if (includeLookups) {
            //2.过滤出对象中的lookup 对象的apiNames
            Set<String> lookupObjectApiNames = Sets.newHashSet();
            descs.forEach(desc -> {
                // 线上告警,desc为空,为什么为空,没找到原因,打印日志
                if (MapUtils.isEmpty(desc)) {
                    log.error("getObjectDesc context:{},error:{}", JacksonUtil.toJson(context), JacksonUtil.toJson(apiNames));
                } else {
                    lookupObjectApiNames.addAll(getLookupObjectApiNames(desc));
                }
            });

            //3.查找关联对象的描述
            lookupObjectApiNames.removeAll(apiNames);
            descs.addAll(findDescsByApiNamesByCache(context, lookupObjectApiNames, true, false));
        }

        try {
            /**
             * 找到统计字段并为其添加统计字段与当前对象的关系
             * lookup_field_type：master_detail|object_reference
             * 后面会成为对象的标准能力 此key是 2019.8.14 号与伟荣确认
             */
            Map<String, List<CountFieldBean>> statisticsFieldAndRefObjectApiNames = getStatisticsFieldAndRefObjectApiNames(descs);
            List<String> existsApiDetails = descs.stream().map(item -> (String) item.get(MetadataKey.apiName)).collect(Collectors.toList());
            Collection<String> needFetchDescribeApiNames = CollectionUtils.subtract(statisticsFieldAndRefObjectApiNames.keySet(), existsApiDetails);
            List<Map<String, Object>> tempDescribes = findDescsByApiNamesByCache(context, Lists.newArrayList(needFetchDescribeApiNames), true, false);
            tempDescribes.addAll(descs);
            setStatisticsTypeOfField(statisticsFieldAndRefObjectApiNames, tempDescribes);
        } catch (Throwable e) {
            log.warn("getDescribeByApiNames:获取统计字段关系类型时失败", e);
        }
        //4.转换结果
        return descs.stream().map(desc -> fromDescMap(desc, true, null)).collect(Collectors
                .toList());
    }

    @Override
    public List<SimpleMetadataAction> findActionsByDesc(RemoteContext context, String descApiName, String actionType) {
        List<SimpleMetadataAction> simpleActions;
        //判断下是不是自定义按钮获取
        if(BPMConstants.COMMON_BTN_ACTIONS_TYPE.equals(actionType)){
            simpleActions = getCommonActionAndNames(context, descApiName);
        }else {
            simpleActions = Lists.newArrayList();
            Map<String, String> actionCodeAndNames = getActionNamesFromDesc(context, descApiName);
            for (Map.Entry<String, String> actionCodeAndName : actionCodeAndNames.entrySet()) {
                simpleActions.add(new SimpleMetadataAction(actionCodeAndName.getKey(), actionCodeAndName.getValue()));
            }
            simpleActions.addAll(findActionsBySpecialCondition(context, descApiName));
        }
        return simpleActions;
    }

    @Override
    public List<SimpleMetadataDesc> findReferences(RemoteContext context, String apiName) {
        List<Map<String, Object>> descs = newMetadataProxy.findReferencesDesc(getNewContext(context), apiName);
        List<SimpleMetadataDesc> simpleDescs = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(descs)) {
            for (Map<String, Object> desc : descs) {
                if (isSupportedReference(context.getTenantId(),apiName, desc)) {
                    List<SimpleMetadataDesc.RelatedListProperty> relatedListProperties = getRelatedAndMasterDetailFields(desc,
                            apiName);
                    if (relatedListProperties.size() > 0) {
                        simpleDescs.add(fromDescMap(desc, false, relatedListProperties));
                    }
                }
            }
        }

        return simpleDescs;
    }

    @Override
    public String getPaaSObjectName(RemoteContext context, String apiName, String objectId) {
        log.debug("getPaaSObjectName : OBJECT_ID={}", objectId);
        try {
            return newMetadataProxy.getPaaSObjectName(getNewContext(context), apiName, objectId, REPLACE_WHEN_NOT_FOUND);
        } catch (RestProxyBusinessException e) {
            log.warn("getPaaSObjectName error : find data failed. CONTEXT={}, API_NAME={}, DATA_ID={}", context,
                    apiName, objectId, e);
            if (BPMBusinessExceptionCode.metadataNoPermission(e.getCode())) {
                return BPMConstants.REPLACE_WHEN_NO_PERMISSION;
            }
            return REPLACE_WHEN_NOT_FOUND;
        } catch (Exception e) {
            log.error("getPaaSObjectName error : find data failed. CONTEXT={}, API_NAME={}, DATA_ID={}", context,
                    apiName, objectId, e);
            return REPLACE_WHEN_NOT_FOUND;
        }
    }


    @Override
    public Map<String, String> getPaaSObjectNames(RemoteContext context, Collection<Pair<String, String>>
            entityIdAndObjectIdList) {
        return newMetadataProxy.getPaaSObjectNames(getNewContext(context), entityIdAndObjectIdList);
    }

    @Override
    public Map<String, String> getPaaSObjectNames(RemoteContext context, String entityId, List<String> objectIds) {
        FindObjectNameByIds.FindObjectNameByIdsQueryResult data = newMetadataProxy.findObjectNameByIds(
                context,
                entityId,
                new ArrayList<>(new HashSet<>(objectIds))).getQueryResult();
        if (Objects.nonNull(data) && Objects.nonNull(data.getData())) {
            return data.getData()
                    .stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getName()))
                    .collect(Collectors.toMap((FindObjectNameByIds.DataNameDetail::getId), (FindObjectNameByIds.DataNameDetail::getLanguageName)));
        }
        return Maps.newHashMap();
    }

    @Override
    public Map<String, String> getObjectNameAndDisplayName(RemoteContext context, String entityId, List<String> objectIds) {
        return  newMetadataProxy.getObjectNameAndDisplayName(context, entityId, objectIds);
    }

    @Override
    public boolean getDeliveryNoteEnable(RemoteContext context) {
        return newMetadataProxy.getDeliveryNoteEnable(context);
    }


    @Override
    public List<String> getDataOwner(RemoteContext context, String apiName, String id) {
        Map<String, Object> data = newMetadataProxy.findDataOwner(context, apiName, id);
        if (data == null) {
            return new ArrayList<>();
        }
        Object owner = data.get(MetadataKey.owner);
        if (owner instanceof String) {
            return Lists.newArrayList((String) owner);
        } else if (owner instanceof Number) {
            return Lists.newArrayList(((Number) owner).intValue() + "");
        } else if (owner instanceof List) {
            return ((List) owner);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, List<String>> getDataOwners(RemoteContext context, String entityId, Set<String> ids) {
        Map<String, Map<String, Object>> dataListOwner = newMetadataProxy.findDataListOwner(context, entityId, new ArrayList<>(ids));
        if (MapUtils.isEmpty(dataListOwner)) {
            return Maps.newHashMap();
        }
        return dataListOwner.keySet().stream().collect(Collectors.toMap(id -> id, id -> {
            Map<String, Object> data = dataListOwner.get(id);
            if (data == null) {
                return new ArrayList<>();
            }
            Object owner = data.get(MetadataKey.owner);
            if (owner instanceof String) {
                return Lists.newArrayList((String) owner);
            } else if (owner instanceof Number) {
                return Lists.newArrayList(String.valueOf(((Number) owner).intValue()));
            } else if (owner instanceof List) {
                return ((List) owner);
            } else {
                return new ArrayList<>();
            }

        }, (v1, v2) -> v1));
    }

    @Override
    public Map<String, Object> findDescWithReference(RemoteContext context, String apiName) {
        return newMetadataProxy.findInternalDescribe(getNewContext(context), apiName, true, true).getDescribe();
    }

    @Override
    public List<FindDisplayNames.FindDisplayNamesResultDetail> getEntityNames(RemoteContext context) {
        return newMetadataProxy.getEntityNames(context);
    }


    /**
     * 实例顶部看的对象 不需要处理
     */
    @Override
    public List<FindDisplayNames.FindDisplayNamesResultDetail> getEntityNames(RemoteContext context, int socketReadTimeout, int retryTimes) {
        try {
            return newMetadataProxy.getEntityNames(context, SocketConfigParam.SocketConfig.builder().retryTimes(retryTimes).socketReadTimeout(socketReadTimeout).build());
        } catch (Throwable e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                log.warn("获取企业下所有对象简要信息失败:{}", context);
            } else {
                log.error("获取企业下所有对象简要信息失败,默认返回空数据", e);
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public FindDataBySearchTemplate.Result findDataBySearchTemplate(RemoteContext context, String apiName, Object query) {
        return newMetadataProxy.findDataBySearchTemplate(context, apiName, query);
    }

    @Override
    public Map<String, Object> findDataDescribeLayout(RemoteContext context, String entityId, String objectId, String recordType, String layoutType) {
        Map<String, Object> rst = Maps.newHashMap();
        //获取数据
        FindDataById.FindDataByIdResultDetail dataByIdWithDescribe = newMetadataProxy.findDataByIdWithDescribe(context, entityId, objectId);
        rst.put(BPMConstants.MetadataKey.DATA, dataByIdWithDescribe.getObject_data());
        rst.put(BPMConstants.MetadataKey.DESCRIBE, dataByIdWithDescribe.getDescribe());
        //数据上有record_type,如果没有则使用default__c
        if (Strings.isNullOrEmpty(recordType)) {
            recordType = (String) dataByIdWithDescribe.getObject_data().get(BPMConstants.RECORD_TYPE);
            if (Strings.isNullOrEmpty(recordType)) {
                recordType = "default__c";
            }
        }
        // 移动端布局只有一套
        if ("list".equals(layoutType)) {
            Map layout = metaDataAuthProxy.findDefaultLayout(context, "list", entityId);
            rst.put(BPMConstants.MetadataKey.LAYOUT, layout);
        } else {
            // detail 需要根据当前人的角色获取
            GetDescribeLayout.Result result = sfaBusinessProxy.getDescribeLayout(context, entityId, recordType, true);
            if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                rst.put(BPMConstants.MetadataKey.LAYOUT, result.getData().getLayout());
            }
        }
        return rst;
    }

    @Override
    public List<SimpleMetadataAction> findActionsBySpecialCondition(RemoteContext context, String descApiName){
        List<SimpleMetadataAction> result = Lists.newArrayList();
        //签到/签退-自定义对象自动支持，预置对象需要配置
        //签到signin/签退signout(只对有签到组件的对象显示，若有签到组件，但未开启签退添加标志位为true)
        if(BPMObjectSupportConfig.objectSupports.isCustomObj(context.getTenantId(),descApiName) || BPMObjectSupportConfig.objectSupports.getApiNameBySpecialOperationType(BPMConstants.OperationCodeType.signin.name()).contains(descApiName)){
            Map<String, String> actions  = BPMObjectSupportConfig.objectSupports.getOperationByOperationType(BPMConstants.OperationCodeType.signin.name());
            if(!actions.isEmpty()){
                //查询对象描述
                Map entityDescribe = findDescribe(context,descApiName, true, false);
                //获取对象描述中签到组件描述
                Map signInDescribe = MetadataUtils.getAppointFieldDescribeByFieldCondition(entityDescribe, MetadataKey.group_type, MetadataKey.SIGN_IN);
                //是否存在签到组件描述或是否已禁用
                if(Objects.nonNull(signInDescribe) && Boolean.TRUE.equals(signInDescribe.get(MetadataKey.isActive))){
                    for (String key : actions.keySet()){
                        SimpleMetadataAction simpleMetadataAction = new SimpleMetadataAction(key, I18N.text(actions.get(key)));
                        if(BPMConstants.OperationCodeType.signout.name().equals(key)){
                            simpleMetadataAction.setIsOpenSignOut(Boolean.TRUE.equals(signInDescribe.get(MetadataKey.IS_ENABLE_SIGN_OUT)));
                        }
                        result.add(simpleMetadataAction);
                    }
                }
            }
        }
        return result;
    }

    private Set<String> getLookupObjectApiNames(Map<String, Object> desc) {
        Set<String> rets = Sets.newHashSet();
        Map<String, Map<String, Object>> fields = MapUtil.instance.getMapOfGeneric(desc, MetadataKey.fields);
        if (fields != null) {
            fields.forEach((field, filedDesc) -> {
                if (MetadataKey.objectReference.equals(filedDesc.get(MetadataKey.type))) {
                    String target_api_name = (String) filedDesc.get(MetadataKey.targetApiName);
                    if (Strings.isNullOrEmpty(target_api_name)) {
                        log.error("getLookupObjectApiNames:field:{},no target_api_name,full desc:{}",
                                JsonUtil.toJson(field), JsonUtil.toJson(desc));
                    } else {
                        rets.add(target_api_name);
                    }
                }
            });
        }

        return rets;
    }

    private static final List<String> salesOrderSpecialActionCode = Lists.newArrayList(
            BPMConstants.Button.confirmdelivery.name(),
            BPMConstants.Button.confirmreceive.name()
    );

    private Map<String, String> getActionNamesFromDesc(RemoteContext context, String apiName, String actionCode) {

        Map<String, String> ret = new HashMap<>(BPMObjectSupportConfig.objectSupports.getActions(context.getTenantId(),apiName));

        if (MetadataKey.APINAME_LEADSOBJ.equals(apiName)) {
            String combinedActionNames = combineLeadsActions(ret);
            ret.put(MetadataKey.leadsObjHandleActionCode, combinedActionNames);
        }
        // 如果发货单开启,action中去掉确认发货和确认收货按钮

        if (MetadataKey.SALES_ORDER_OBJ_API_NAME.equals(apiName) && salesOrderSpecialActionCode.contains(actionCode.toLowerCase())) {
            boolean isOpenDeliveryNote = getDeliveryNoteEnable(context);
            if (isOpenDeliveryNote) {
                ret.remove(BPMConstants.Button.confirmdelivery.name());
                ret.remove(BPMConstants.Button.confirmreceive.name());
            }
        }

        //TODO  需要处理一下 从配置中心获取 @高俊
        if (BPMConstants.OperationCodeType.signin.name().equals(actionCode)) {
            ret.put(actionCode, I18N.text(PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_IN.key));
        } else if (BPMConstants.OperationCodeType.signout.name().equals(actionCode)) {
            ret.put(actionCode, I18N.text(PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_OUT.key));
        }

        return ret;
    }

    private Map<String, String> getActionNamesFromDesc(RemoteContext context, String apiName) {

        Map<String, String> ret = new HashMap<>(BPMObjectSupportConfig.objectSupports.getActions(context.getTenantId(),apiName));

        if (MetadataKey.APINAME_LEADSOBJ.equals(apiName)) {
            String combinedActionNames = combineLeadsActions(ret);
            ret.put(MetadataKey.leadsObjHandleActionCode, combinedActionNames);
        }
        // 如果发货单开启,action中去掉确认发货和确认收货按钮
        if (MetadataKey.SALES_ORDER_OBJ_API_NAME.equals(apiName)) {
            boolean isOpenDeliveryNote = getDeliveryNoteEnable(context);
            if (isOpenDeliveryNote) {
                ret.remove(BPMConstants.Button.confirmdelivery.name());
                ret.remove(BPMConstants.Button.confirmreceive.name());
            }
        }

        return ret;
    }


    private List<SimpleMetadataAction> getCommonActionAndNames(RemoteContext context, String apiName){
        List<SimpleMetadataAction> result = Lists.newArrayList();
        //获取对象所有可用自定义按钮
        List<FindCustomButtonList.CustomButton> customButtonList = findCustomButtonList(context, apiName, Boolean.TRUE);
        if(CollectionUtils.isEmpty(customButtonList)){
            return result;
        }
        //获取当前对象支持的系统自定义按钮信息
        List<String> objCommonSystemBtn = SwitchConfigManager.getCommonSystemButtons(apiName);

        for (FindCustomButtonList.CustomButton customButton : customButtonList) {
            //是可用的
            Boolean isActive = Boolean.TRUE.equals(customButton.is_active()) && Boolean.FALSE.equals(customButton.is_deleted());
            //是业务按钮 common \ redirect(UI按钮) \ convert(对应映射规则)
            Boolean isSupportBtnType = BPMConstants.COMMON_BTN_ACTIONS_TYPE.equals(customButton.getButton_type()) || BPMConstants.REDIRECT_BTN_ACTIONS_TYPE.equals(customButton.getButton_type());
            //是详情页
            Boolean isDetail = Objects.nonNull(customButton.getUse_pages()) && customButton.getUse_pages() instanceof List &&((List) customButton.getUse_pages()).contains(MetadataKey.OBJECT_CUSTOM_BTN_DETAIL_TYPE);
            //是系统按钮 system(系统或业务预置) \ custom(自定义的)  \ null(映射规则生成)
            Boolean isSystemType = MetadataKey.OBJECT_CUSTOM_BTN_SYS_DEFINE_TYPE.equals(customButton.getDefine_type());
            //在白名单中
            Boolean isInSystemWhiteList = objCommonSystemBtn.contains(customButton.getApi_name());
            if(isActive && isSupportBtnType && isDetail && (!isSystemType || (isSystemType && isInSystemWhiteList))){
                result.add(new SimpleMetadataAction(customButton.getApi_name(), customButton.getLabel()));
            }
        }
        return result;
    }

    private boolean isSupportedReference(String tenantId,String apiName, Map<String, Object> targetObjectDesc) {
        String targetApiName = (String) targetObjectDesc.get(MetadataKey.apiName);
        //如果关联对象是预设对象,而且不在配置中,则不支持;其他情况支持
        if (!BPMObjectSupportConfig.objectSupports.isBPMSupport(tenantId,targetApiName)) {
            return false;
        }

        if (BPMObjectSupportConfig.objectSupports.isOldObj(tenantId,targetApiName) && BPMObjectSupportConfig.objectSupports.isOldObj(tenantId,apiName)) {  //预设对象,配置中限制支持的关联预设对象
            List<String> referencesSupport = BPMObjectSupportConfig.objectSupports.getOldObjectReferences(apiName);
            //如果对象是关联对象,而且不在支持配置中,则不支持;其他情况支持
            if (!referencesSupport.contains(targetApiName)) {
                return false;
            }
            return true;
        }
        return true;
    }

    private String combineLeadsActions(Map<String, String> actionCodeAndNames) {
        List<String> shouldCombineActionNames = MetadataKey.leadsObjThreeHandleActionCodes.stream()
                .map(actionCodeAndNames::get).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        String combinedActionName = String.join("/", shouldCombineActionNames);
        MetadataKey.leadsObjThreeHandleActionCodes.forEach(actionCodeAndNames::remove);
        return combinedActionName;
    }

    private List<SimpleMetadataDesc.RelatedListProperty> getRelatedAndMasterDetailFields(Map<String, Object> objDesc, String targetApiName) {

        //写在这里的好处
        //1. 不影响原有逻辑 2.后期如果对字段进行屏蔽,也同样在此处
        List<String> blacklistObjectReferences = BPMObjectSupportConfig.objectSupports.getBlacklistObjectReferences(targetApiName);

        List<SimpleMetadataDesc.RelatedListProperty> ret = Lists.newArrayList();
        if (objDesc != null && !Strings.isNullOrEmpty(targetApiName)) {
            //获取所有字段描述
            Map<String, Map<String, Object>> fields = MapUtil.instance.getMapOfGeneric(objDesc, MetadataKey.fields);
            if (fields != null) {
                for (Map.Entry<String, Map<String, Object>> field : fields.entrySet()) {
                    Map<String, Object> fieldDesc = field.getValue();
                    Object fieldType = fieldDesc.get(ExtensionKey.type);
                    if (!MetadataKey.objectReference.equals(fieldType) && !MetadataKey.masterDetail.equals(fieldType)) {
                        continue;
                    }
                    boolean isActive = MapUtil.instance.getBool(fieldDesc, UtilConstans.IS_ACTIVE);
                    if ((MetadataKey.objectReference.equals(fieldType) || MetadataKey.masterDetail.equals(fieldType))
                            && targetApiName.equals(field.getValue().get(MetadataKey.targetApiName))
                            && isActive
                            && !blacklistObjectReferences.contains(field.getValue().get(MetadataKey.DESCRIBE_API_NAME))) {
                        String relatedLabel = (String) fieldDesc.get(MetadataKey.label);
                        String relatedName = (String) fieldDesc.get(MetadataKey.relatedListName);
                        ret.add(new SimpleMetadataDesc.RelatedListProperty(relatedLabel, relatedName, String.valueOf(fieldType)));
                    }
                }
            }
        }

        return ret;
    }

    private List<Map<String, Object>> getAllDescs(RemoteContext context, boolean
            includeFieldsDesc,boolean useGroupManager) {
        List<Map<String, Object>> rst = newMetadataProxy.getAllDescribes(getNewContext(context), includeFieldsDesc,false,useGroupManager);
        return ObjectDescribeListSortUtil.sort(context.getTenantId(),BPMObjectSupportConfig.objectSupports, rst);
    }

    private SimpleMetadataDesc fromDescMap(Map describe, boolean includeFields, List<SimpleMetadataDesc
            .RelatedListProperty> relatedListProperties) {
        if (describe == null) {
            return null;
        }

        SimpleMetadataDesc simpleDesc = new SimpleMetadataDesc();
        simpleDesc.setDisplayName((String) describe.get(MetadataKey.displayName));
        simpleDesc.setObjApiName(((String) describe.get(MetadataKey.apiName)));
        simpleDesc.setDefineType(((String) describe.get(MetadataKey.defineType)));
        simpleDesc.setLastModifiedTime(describe.get(MetadataKey.lastModifiedTime));
        simpleDesc.setRelatedFields(relatedListProperties);
        simpleDesc.setGroupManagerHidden((Boolean) describe.getOrDefault(BPMConstants.GROUP_MANAGER_HIDDEN,true));
        if (includeFields) {
            simpleDesc.setFields((Map) describe.get(MetadataKey.fields));
        }
        if(describe.containsKey(MetadataKey.VISIBLE_SCOPE)
                && describe.containsKey(MetadataKey.UPSTREAM_TENANT_ID)
                && MetadataKey.PUBLIC_VISIBLE_SCOPE_VALUES.contains(describe.get(MetadataKey.VISIBLE_SCOPE))
                && Objects.nonNull(describe.get(MetadataKey.UPSTREAM_TENANT_ID))) {
            simpleDesc.setIsPublicObj(true);
        }

        return simpleDesc;
    }

    @Override
    public Map<String, Object> findInternalDescribe(RemoteContext context, String descApiName, boolean containAllFields) {
        return newMetadataProxy.findInternalDescribe(getNewContext(context), descApiName, containAllFields, true).getDescribe();
    }


    @Override
    public Map<String, Object> getAreaOption(String fieldType) {
        try {
            GetCountryAreaOptions.GetCountryAreaOptionsResultDetail areaOptions = countryCache.get("1");
            Map<String, Object> areaDesc;
            switch (fieldType) {
                case BPMConstants.MetadataKey.city:
                    areaDesc = areaOptions.getCity();
                    break;
                case BPMConstants.MetadataKey.country:
                    areaDesc = areaOptions.getCountry();
                    break;
                case BPMConstants.MetadataKey.district:
                    areaDesc = areaOptions.getDistrict();
                    break;
                case BPMConstants.MetadataKey.province:
                    areaDesc = areaOptions.getProvince();
                    break;
                default:
                    areaDesc = Maps.newHashMap();
            }
            return areaDesc;
        } catch (ExecutionException e) {
            throw new BPMRuntimeException(e);
        }
    }



    @Override
    public GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions(RemoteContext context) {
        try {
            return countryCache.get("1");
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Map<String, String> getAreaLabelByCodes(RemoteContext remoteContext, List<String> codes) {
        if (codes.isEmpty()) {
            return Maps.newHashMap();
        }
        return newMetadataProxy.batchQueryAreaLabels(remoteContext, codes).getCodeLabelMap();
    }

    @Override
    public Map<String, Object> findDescribeExtra(RemoteContext context, String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
        FindDescribeExtra.Result remoteResult = newMetadataProxy.findDescribeExtra(context, apiName, describeExtraType);
        if(MapUtils.isNotEmpty(remoteResult.getData().getDescribeExtra())){
            return JacksonUtil.fromJsonOfGeneric(JacksonUtil.toJson(remoteResult.getData().getDescribeExtra()), Map.class);
        }else {
            return Maps.newHashMap();
        }
    }

    @Override
    public List<FindCustomButtonList.CustomButton> findCustomButtonList(RemoteContext context, String apiName, Boolean includeUIAction){
        return newPaasMetadataProxy.getCustomButtonList(context, apiName, includeUIAction);
    }

    @Override
    public List<FindCustomButtonList.CustomButton> findDataExhibitButton(RemoteContext context,String apiName, String objectId,  String usePageType){
        return newPaasMetadataProxy.findDataExhibitButton(context, apiName, objectId, usePageType).getData().getButtons();
    }

    @Override
    public void invalidAndDeleteByDataId(RemoteContext context, String dataId, String entityId){
        if(StringUtils.isBlank(dataId) || StringUtils.isBlank(entityId)){
            return;
        }
        newMetadataProxy.invalidData(context, dataId, entityId);
        newMetadataProxy.deleteData(context, dataId, entityId);
    }

    @Override
    public void batchCreateData(RemoteContext context, String descApiName, String jsonData) {
        newMetadataProxy.batchCreate(getNewContext(context), descApiName, jsonData);
    }

    @Override
    public void batchUpdateData(RemoteContext context, String descApiName, List<Map<String, Object>> updateList) {
        newMetadataProxy.batchIncrementUpdate(getNewContext(context), descApiName, updateList);
    }

    @Override
    public void directlyBulkDelete(RemoteContext context, String descApiName, List<String> deleteDataIds) {
        if (CollectionUtils.isEmpty(deleteDataIds)){
            return;
        }
        newMetadataProxy.directlyBulkDelete(getNewContext(context), descApiName, deleteDataIds);
    }

    @Override
    public void deleteTaskDraft(RemoteContext context, String taskId){
        if(StringUtils.isBlank(taskId)){
            return;
        }
        newMetadataProxy.deleteDraftBatch(context, FlowType.workflow_bpm, Lists.newArrayList(taskId));
    }

}
