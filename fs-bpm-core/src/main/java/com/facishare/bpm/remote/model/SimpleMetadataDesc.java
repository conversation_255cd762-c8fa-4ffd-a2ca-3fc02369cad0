package com.facishare.bpm.remote.model;

import com.google.common.base.Strings;
import lombok.*;
import org.joda.time.format.ISODateTimeFormat;

import java.util.List;
import java.util.Map;

/**
 * Created by wangz on 17-1-10.
 */
@Data
public class SimpleMetadataDesc implements Comparable<SimpleMetadataDesc> {
    private String displayName;
    private String objApiName;
    private String defineType;
    private Map fields;
    private Long lastModifiedTime;
    private List<RelatedListProperty> relatedFields;
    private boolean groupManagerHidden;
    private Boolean isPublicObj;

    public void setLastModifiedTime(Object dateTime) {
        if (dateTime instanceof Double) {
            this.lastModifiedTime = ((Double) dateTime).longValue();
        } else if (dateTime instanceof Long) {
            this.lastModifiedTime = (Long) dateTime;
        } else if (dateTime instanceof Integer) {
            this.lastModifiedTime = Long.valueOf(dateTime + "");
        } else {
            String tempDateTime = dateTime == null ? null : dateTime + "";
            if (Strings.isNullOrEmpty(tempDateTime)) {
                this.lastModifiedTime = System.currentTimeMillis();
            } else {
                this.lastModifiedTime = ISODateTimeFormat.dateTime().parseDateTime(tempDateTime).getMillis();
            }
        }
    }

    @Override
    public int compareTo(SimpleMetadataDesc o) {
        if ("package".equals(this.defineType) && "custom".equals(o.defineType)) {
            return -1;
        } else if ("custom".equals(this.defineType) && "package".equals(o.defineType)) {
            return 1;
        } else {
            return this.lastModifiedTime.compareTo(o.lastModifiedTime);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RelatedListProperty {
        private String relatedListLabel;
        private String relatedListName;
        private String type;
    }
}
