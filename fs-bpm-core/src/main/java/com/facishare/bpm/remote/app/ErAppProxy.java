package com.facishare.bpm.remote.app;


import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.resource.app.GetAppActions;
import com.facishare.bpm.model.resource.app.HasPrivilege;
import com.facishare.bpm.resource.AppResource;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class ErAppProxy {

    @Autowired
    private AppResource appResource;

    private LoadingCache<String, GetAppActions.Result> appActionsCache = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .initialCapacity(50)
            .maximumSize(100).build(new CacheLoader<String, GetAppActions.Result>() {
                                            @Override
                                            public GetAppActions.Result load(@NotNull String key) {
                                                return null;
                                            }
                                        }
            );

    public GetAppActions.Result getAppActions(String tenantId, String userId, String entityId, Boolean externalFlow, String appId, Integer appType) {
        try {
            return appResource.getAppActions(
                    new GetAppActions.Arg(
                            tenantId,
                            entityId,
                            userId,
                            externalFlow,
                            appId,
                            appType));
        } catch (Exception e) {
            log.warn("getAppActions error:{},{},{},{}", tenantId, entityId, userId, externalFlow, e);
            return new GetAppActions.Result();
        }
    }

    public GetAppActions.Result getAppActionsByCache(String tenantId, String userId, String entityId, Boolean externalFlow, String appId, Integer appType) {
        String key = tenantId + "_" + entityId + "_" + externalFlow + "_" + appId + "_" + appType;
        GetAppActions.Result result;
        try {
            result = appActionsCache.get(key);
            if(Objects.nonNull(result)){
                return result;
            }
        }catch (Exception e){

        }
        try {
            result =  appResource.getAppActions(
                    new GetAppActions.Arg(
                            tenantId,
                            entityId,
                            userId,
                            externalFlow,
                            appId,
                            appType));
        } catch (Exception e) {
            log.warn("getAppActions error:{},{},{},{}", tenantId, entityId, userId, externalFlow, e);
            return new GetAppActions.Result();
        }
        if (Objects.nonNull(result)){
            appActionsCache.put(key, result);
        }
        return result;
    }

    /**
     * 是否支持外部流程的查询
     * @param serviceManager
     * @return
     */
    public boolean hasPrivilege(RefServiceManager serviceManager) {
        try {
            return appResource.hasPrivilege(new HasPrivilege.Arg(serviceManager.getTenantId(), serviceManager.getUserId())).isData();
        } catch (RuntimeException e) {
            log.warn("appResource.hasPrivilege:{},{}", serviceManager.getTenantId(), serviceManager.getUserId());
            return false;
        }

    }
}
