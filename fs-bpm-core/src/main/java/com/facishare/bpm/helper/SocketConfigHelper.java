package com.facishare.bpm.helper;

import com.facishare.rest.core.annotation.SocketConfigParam;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/5/30 1:21 PM
 */
@Slf4j
@Component
public class SocketConfigHelper {

    private static volatile Map<String, SocketConfigParam.SocketConfig> socketConfig;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-bpm-service-socket-config", (config) -> {
            socketConfig = JacksonUtil.fromJson(config.getString(), new TypeReference<Map<String, SocketConfigParam.SocketConfig>>() {
            });
            log.info("fs-bpm-service-socket-config :{} ", config.getString());
        });
    }

    public SocketConfigParam.SocketConfig getSocketConfig(String key) {
        return socketConfig.get(key);
    }

}
