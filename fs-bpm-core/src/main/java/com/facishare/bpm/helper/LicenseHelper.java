package com.facishare.bpm.helper;

import com.facishare.paas.I18N;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.exception.RestProxyExceptionCode;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.bpm.language.BPMRestProxyI18N.paas_flow_common_invalid_crm_version;


/**
 * desc: 新版本license通过jar方式引用,此处做兼容处理
 * version: 6.7
 * Created by cuiyongxu on 2019/11/26 11:23 AM
 */
@Component
public class LicenseHelper {

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    public long getBPMQuota(RemoteContext context) {
        String value = "0";
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(getLicenseContext(context));
        arg.setModuleCode("bpm");
        arg.setParaKeys(Sets.newHashSet("bpm_process_limit"));
        ParaInfoResult paraInfoResult = licenseClient.queryModulePara(arg);

        if (Objects.nonNull(paraInfoResult)) {
            List<ModuleParaPojo> result = paraInfoResult.getResult();
            if (CollectionUtils.isEmpty(result)) {
                return 0;
            }
            value = result.get(0).getParaValue();
        }

        return Strings.isEmpty(value) ? 0 : Long.parseLong(value);
    }

    public ProductVersionPojo getCRMVersion(RemoteContext context) {
        QueryProductArg arg = new QueryProductArg();
        arg.setLicenseContext(getLicenseContext(context));
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(arg);
        List<ProductVersionPojo> result = licenseVersionResult.getResult();

        if (CollectionUtils.isEmpty(result)) {
            throw new RestProxyBusinessException(RestProxyExceptionCode.REST_PROXY_INVALIDE_CRM_VERSION,
                    I18N.text(paas_flow_common_invalid_crm_version.getKey()));
        }
        result = result.stream().filter(item -> "0".equals(item.getProductType())).collect(Collectors.toList());
        return result.get(0);
    }

    private LicenseContext getLicenseContext(RemoteContext context) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setUserId(context.getUserId());
        licenseContext.setTenantId(context.getTenantId());
        licenseContext.setAppId(context.getAppId());
        return licenseContext;
    }
}
