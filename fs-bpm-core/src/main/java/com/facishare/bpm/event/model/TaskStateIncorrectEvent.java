package com.facishare.bpm.event.model;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.event.BaseEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * desc:
 * author: cuiyongxu
 * create_time: 2021/7/14-5:03 下午
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TaskStateIncorrectEvent extends BaseEvent {

    private String instanceId;
    private String reason;


    public static TaskStateIncorrectEvent create(RefServiceManager serviceManager, String instanceId, String reason) {
        TaskStateIncorrectEvent taskStateIncorrectEvent = new TaskStateIncorrectEvent();
        taskStateIncorrectEvent.setInstanceId(instanceId);
        taskStateIncorrectEvent.setReason(reason);
        taskStateIncorrectEvent.setServiceManager(serviceManager);
        return taskStateIncorrectEvent;
    }
}
