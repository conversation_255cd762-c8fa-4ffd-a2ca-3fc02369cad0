package com.facishare.bpm.event.listener;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.event.BPMEventBusListener;
import com.facishare.bpm.event.model.TaskStateIncorrectEvent;
import com.facishare.bpm.model.BpmConstant;
import com.facishare.bpm.model.CancelSourceEnum;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.google.common.collect.Maps;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * desc:如果实例状态是cancel  但是任务是in_progress 或 error的话  执行一次实例终止操作,解决线上出现的bug
 * author: cuiyongxu
 * create_time: 2021/7/14-5:03 下午
 **/
@Slf4j
@Component
public class TaskStateIncorrectListener implements BPMEventBusListener {

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @Subscribe
    public void validateTaskState(TaskStateIncorrectEvent event) {
        RefServiceManager serviceManager = event.getServiceManager();
        // 实例已终止&&任务状态是进行中
        Map<String, Object> opinionExtensions = Maps.newHashMap();
        opinionExtensions.put(BpmConstant.CANCEL_SOURCE, CancelSourceEnum.console.name());
        paasWorkflowServiceProxy.cancel(serviceManager.getContext(), opinionExtensions, event.getInstanceId(), event.getReason());
    }

}
