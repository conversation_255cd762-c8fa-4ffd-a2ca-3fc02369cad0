package com.facishare.bpm.event.model;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.event.BaseEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/12 5:24 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UpdateTaskReadEmployeeEvent extends BaseEvent {

    private String taskId;
    private List<String> candidateIds;


    public static UpdateTaskReadEmployeeEvent create(RefServiceManager serviceManager, String taskId, List<String> candidateIds) {
        UpdateTaskReadEmployeeEvent updateTaskReadEmployeeEvent = new UpdateTaskReadEmployeeEvent();
        updateTaskReadEmployeeEvent.setCandidateIds(candidateIds);
        updateTaskReadEmployeeEvent.setTaskId(taskId);
        updateTaskReadEmployeeEvent.setServiceManager(serviceManager);
        return updateTaskReadEmployeeEvent;
    }
}
