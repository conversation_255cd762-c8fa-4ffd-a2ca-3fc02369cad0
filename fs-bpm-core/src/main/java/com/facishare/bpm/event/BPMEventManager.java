package com.facishare.bpm.event;

import org.apache.commons.collections.MapUtils;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

@Component
public class BPMEventManager extends ApplicationObjectSupport {

    @PostConstruct
    public void init() {
        Map<String, BPMEventBusListener> eventBusListener = getApplicationContext().getBeansOfType(BPMEventBusListener.class);
        if (MapUtils.isNotEmpty(eventBusListener)) {
            BPMEventBus.init(eventBusListener.values());
        }
    }
}
