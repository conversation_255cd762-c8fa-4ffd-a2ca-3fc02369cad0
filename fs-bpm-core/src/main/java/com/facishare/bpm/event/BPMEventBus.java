package com.facishare.bpm.event;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.event.model.TaskStateIncorrectEvent;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.google.common.eventbus.AsyncEventBus;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/12 5:19 PM
 */
@Slf4j
public class BPMEventBus {


    private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors() * 2,
            Runtime.getRuntime().availableProcessors() * 2, 0,
            TimeUnit.SECONDS, new LinkedBlockingDeque<>(),
            r -> new Thread(r, "BPMEventBus-" + Thread.currentThread().getName()));


    private static AsyncEventBus engineEventBus = new AsyncEventBus("BPMEventBus", executorService);

    public static void init(Collection<BPMEventBusListener> listeners) {
        if (listeners != null && !listeners.isEmpty()) {
            for (BPMEventBusListener listener : listeners) {
                engineEventBus.register(listener);
            }
        }
    }

    public static void post(BaseEvent event) {
        engineEventBus.post(event);
    }

    public static void post(RefServiceManager serviceManager, Task task, WorkflowInstance workflowInstance) {

        if (Objects.isNull(task) || Objects.isNull(workflowInstance)) {
            log.info("skip validateTaskState");
            return;
        }
        InstanceState instanceState = workflowInstance.getState();
        if (Objects.equals(InstanceState.cancel, instanceState)) {
            TaskState taskState = task.getState();
            // 任务已完成
            if (task.getCompleted()) {
                return;
            }
            if (!Objects.equals(taskState, TaskState.cancel)) {
                // 实例已终止&&任务状态是进行中
                post(TaskStateIncorrectEvent.create(serviceManager, workflowInstance.getId(), workflowInstance.getReason()));
            }
        }
    }
}
