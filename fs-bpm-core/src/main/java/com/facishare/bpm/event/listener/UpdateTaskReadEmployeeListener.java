package com.facishare.bpm.event.listener;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.event.BPMEventBusListener;
import com.facishare.bpm.event.model.UpdateTaskReadEmployeeEvent;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.rest.core.util.JacksonUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/12 5:19 PM
 */
@Slf4j
@Component
public class UpdateTaskReadEmployeeListener implements BPMEventBusListener {


    @Subscribe
    public void updateTaskReadEmployee(UpdateTaskReadEmployeeEvent event) {
        RefServiceManager serviceManager = event.getServiceManager();
        List<String> candidateIds = event.getCandidateIds();
        String taskId = event.getTaskId();

        try {
            String userId = serviceManager.getUserIdWithOuterUserId();

            if (CollectionUtils.isEmpty(candidateIds) || !candidateIds.contains(userId)) {
                return;
            }
            //仅将在待办中的人,设置到已读人员中
            //查一下元数据中的bpmTask
            Map taskData = serviceManager.findDataById(BPMConstants.BPM_TASK, taskId);
            if (MapUtils.isNotEmpty(taskData)) {
                Object readEmployeeObject = taskData.get(BPMConstants.READ_EMPLOYEE);
                Set<String> readEmployee = Objects.isNull(readEmployeeObject) ?
                        Sets.newHashSet() : readEmployeeObject instanceof List ? Sets.newHashSet((List) readEmployeeObject) : (Set) readEmployeeObject;
                //如果不存在 ,需要设置人员信息
                if (!readEmployee.contains(userId)) {
                    readEmployee.add(userId);
                } else {
                    return;
                }

                Map<String, Object> updateData = Maps.newHashMap();
                updateData.put(BPMConstants.READ_EMPLOYEE, readEmployee);
                TraceContext.get().setSourceProcessId((String) taskData.getOrDefault(WorkflowKey.sourceWorkflowId, StringUtils.EMPTY));
                //增量更新
                serviceManager.updateData(serviceManager.getContext(), BPMConstants.BPM_TASK, taskId, JacksonUtil.toJson(updateData), false, false, "");
            } else {
                log.warn("更新已读人异常,当前数据不存在,出现同步任务比更新已读人员数据慢");
            }

        } catch (Exception e) {
            log.warn("更新已读人员异常,taskId:{},candidateIds:{}", taskId, JacksonUtil.toJson(candidateIds), e);
        }
    }


}
