#!/bin/bash

# Finally块return语句修复验证脚本
# 验证是否已移除finally块中的return语句

echo "=== Finally块Return语句修复验证 ==="
echo ""

# 检查修复的文件
TARGET_FILE="src/main/java/com/facishare/bpm/service/impl/BPMInstanceServiceImpl.java"

echo "🔍 检查文件: $TARGET_FILE"
echo ""

if [ ! -f "$TARGET_FILE" ]; then
    echo "❌ 目标文件不存在: $TARGET_FILE"
    exit 1
fi

# 检查是否还存在finally块中的return语句
echo "📊 检查finally块中的return语句..."

# 搜索finally块中的return语句模式
FINALLY_RETURN_ISSUES=$(grep -A 10 "finally[[:space:]]*{" "$TARGET_FILE" | grep -B 5 -A 5 "return" || true)

if [ -z "$FINALLY_RETURN_ISSUES" ]; then
    echo "✅ 未发现finally块中的return语句"
else
    echo "❌ 仍存在finally块中的return语句:"
    echo "$FINALLY_RETURN_ISSUES"
    exit 1
fi

echo ""
echo "🔧 检查重构后的代码结构..."

# 检查是否使用了while循环替代递归
WHILE_LOOP_COUNT=$(grep -c "while[[:space:]]*(" "$TARGET_FILE" || echo "0")
echo "   发现while循环: $WHILE_LOOP_COUNT 个"

# 检查是否有shouldContinue变量
SHOULD_CONTINUE_COUNT=$(grep -c "shouldContinue" "$TARGET_FILE" || echo "0")
echo "   发现shouldContinue变量使用: $SHOULD_CONTINUE_COUNT 次"

# 检查方法签名是否保持不变
METHOD_SIGNATURE=$(grep -n "public Integer fixedPGCancelReason" "$TARGET_FILE" || true)
if [ ! -z "$METHOD_SIGNATURE" ]; then
    echo "✅ 方法签名保持不变: $METHOD_SIGNATURE"
else
    echo "❌ 方法签名发生变化"
    exit 1
fi

echo ""
echo "🧪 验证代码编译..."

# 编译验证
cd "$(dirname "$0")"
mvn compile -q > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 代码编译成功"
else
    echo "❌ 代码编译失败"
    exit 1
fi

echo ""
echo "📝 重构总结:"
echo "   - 移除了finally块中的return语句"
echo "   - 将递归调用改为while循环"
echo "   - 使用shouldContinue变量控制循环"
echo "   - 保持了原有的方法签名和业务逻辑"
echo "   - 添加了异常处理的安全退出机制"

echo ""
echo "🎯 SonarQube问题修复:"
echo "   - 解决了'Remove this return statement from this finally block'"
echo "   - 避免了finally块中return语句覆盖try/catch返回值的问题"
echo "   - 提升了代码的可读性和可维护性"
echo "   - 符合Java最佳实践"

echo ""
echo "🎯 Finally块Return语句修复验证完成！"
