{"sourceWorkflowId": "633108a9d43fef000186c32f", "name": "服务请求流程", "enabled": true, "description": "", "entryType": "ServiceRequestObj", "entryTypeName": "服务请求", "rangeEmployeeIds": [], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": ["00000000000000000000000000000006", "consultServiceRequestCustomer"], "workflow": {"externalFlow": 0, "entityId": "ServiceRequestObj", "history": false, "type": "workflow_bpm", "singleInstanceFlow": 0, "appId": "BPM", "linkAppEnable": false, "name": "服务请求流程", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1663741401490"}, {"type": "userTask", "bpmExtension": {"executionType": "operation", "executionName": "操作对象", "entityId": "ServiceRequestObj", "entityName": "服务请求", "objectId": {"expression": "activity_0##ServiceRequestObj"}, "enableLayoutRules": false, "actionCode": "changeowner", "defaultButtons": {"changeowner": {"label": "变更负责人"}}}, "canSkip": false, "remind": true, "linkAppEnable": false, "name": "分配服务请求", "description": "", "id": "1663741401491", "assignee": {"role": ["consultServiceRequestCustomer"]}, "taskType": "anyone", "execution": {"pass": [{"taskType": "updates", "rowNo": 0, "modifyTime": 0, "updateFieldJson": "[{\"isCalculate\":false,\"key\":\"${ServiceRequestObj.service_request_status}\",\"value\":\"handling\",\"defaultValue\":\"\",\"entityId\":\"activity_1663741401491##ServiceRequestObj\"}]"}]}, "assignNextTask": 0, "externalApplyTask": 0}, {"type": "exclusiveGateway", "name": "是否分配服务请求", "description": "", "id": "1663741401497", "defaultTransitionId": "1663900527501", "gatewayType": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "服务请求", "entityId": "ServiceRequestObj", "executionType": "update", "defaultButtons": {"update": {"label": "编辑"}, "Save": {"label": "保存"}, "UpdateAndComplete": {"label": "保存并完成"}}, "objectId": {"expression": "activity_0##ServiceRequestObj"}, "form": [[{"name": "subject", "type": "text", "required": false, "label": "主题"}, {"name": "problem_desc", "type": "long_text", "required": false, "label": "问题描述"}, {"name": "account_id", "type": "object_reference", "required": false, "label": "客户"}, {"name": "contact_id", "type": "object_reference", "required": false, "label": "联系人"}, {"name": "product_id", "type": "object_reference", "required": false, "label": "产品"}, {"name": "priority", "type": "select_one", "required": false, "label": "优先级"}, {"name": "appointment_processing_time", "type": "date_time", "required": false, "label": "客户预约处理时间"}, {"name": "service_address", "type": "group", "required": false, "fields": {"area_country": "country", "area_location": "location", "area_detail_address": "address", "area_city": "city", "area_province": "province", "area_district": "district"}, "group_type": "area", "label": "服务地址"}, {"name": "remark", "type": "long_text", "required": false, "label": "备注"}]], "enableLayoutRules": false}, "linkAppEnable": false, "remind": true, "name": "处理服务请求", "description": "", "id": "*************", "assignee": {"ext_process": ["${activity_*************##ServiceRequestObj##owner}"]}, "taskType": "anyone", "execution": {"pass": [{"taskType": "updates", "rowNo": 0, "modifyTime": 0, "updateFieldJson": "[{\"isCalculate\":false,\"key\":\"${ServiceRequestObj.service_request_status}\",\"value\":\"finished\",\"defaultValue\":\"\",\"entityId\":\"activity_1663741401491##ServiceRequestObj\"}]"}]}, "assignNextTask": 0, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "1663741401504"}, {"type": "executionTask", "bpmExtension": {"executionType": "execution", "entityName": "服务请求", "entityId": "ServiceRequestObj", "objectId": {"expression": "activity_0##ServiceRequestObj"}}, "name": "服务请求状态变更为处理中", "description": "", "id": "1663900527500", "itemList": [{"taskType": "updates", "updateFieldJson": "[{\"isCalculate\":false,\"key\":\"${ServiceRequestObj.service_request_status}\",\"value\":\"handling\",\"defaultValue\":\"\",\"entityId\":\"activity_1663900527500##ServiceRequestObj\"}]"}]}], "transitions": [{"id": "1663741401500", "fromId": "1663741401490", "toId": "1663741401497", "serialNumber": 0}, {"description": "", "id": "1663741401502", "fromId": "1663741401497", "toId": "1663741401491", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##ServiceRequestObj##is_auto_commit"}, "right": {"value": "yes", "type": {"name": "text"}, "metadata": {"containSubDept": false}}}]}]}, "serialNumber": 1}, {"id": "1663741401503", "fromId": "1663741401491", "toId": "*************", "serialNumber": 2}, {"id": "1663741401505", "fromId": "*************", "toId": "1663741401504", "serialNumber": 3}, {"description": "", "id": "1663900527501", "fromId": "1663741401497", "toId": "1663900527500", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##ServiceRequestObj##is_auto_commit"}, "right": {"value": "no", "type": {"name": "text"}, "metadata": {"containSubDept": false}}}]}]}, "serialNumber": 4}, {"id": "1664157783968", "fromId": "1663900527500", "toId": "1663741401504", "serialNumber": 5}], "variables": [{"id": "activity_0##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_1663741401491##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_*************##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_0##ServiceRequestObj##is_auto_commit", "type": {"name": "text"}}], "id": "6331097d6f4272006a91fb8f", "sourceWorkflowId": "633108a9d43fef000186c32f", "createTime": 1664157865440, "tenantId": "82846"}, "extension": {"pools": [{"lanes": [{"id": "1663741401492", "name": "分配服务请求", "description": "", "activities": ["1663741401490", "1663741401491", "1663741401497"]}]}, {"lanes": [{"id": "1663741401496", "name": "编辑服务请求", "description": "", "activities": ["*************", "1663741401504", "1663900527500"]}]}], "diagram": [{"id": "1663741401496", "attr": {"width": 220, "height": 540, "x": 380, "y": 60}}, {"id": "1663741401492", "attr": {"width": 230, "height": 540, "x": 40, "y": 60}}, {"id": "1663741401490", "attr": {"width": 60, "height": 60, "x": 110, "y": 90}}, {"id": "1663741401491", "attr": {"width": 160, "height": 50, "x": 60, "y": 480}}, {"id": "1663741401497", "attr": {"width": 100, "height": 100, "x": 140, "y": 200}}, {"id": "*************", "attr": {"width": 160, "height": 50, "x": 420, "y": 480}}, {"id": "1663741401504", "attr": {"width": 60, "height": 60, "x": 470, "y": 320}}, {"id": "1663900527500", "attr": {"width": 160, "height": 50, "x": 420, "y": 180}}, {"id": "1664157783968", "attr": {"d": "M500,230 v46 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v46", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1663900527501", "attr": {"d": "M190,250 h96 a20,20 0 0 0 20,-20 v-5 a20,20 0 0 1 20,-20 h96", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1663741401505", "attr": {"d": "M500,530 v-104 a0,0 0 0 1 0,-0 h0 a0,0 0 0 0 0,-0 v-104", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1663741401503", "attr": {"d": "M220,505 h101 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h101", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1663741401502", "attr": {"d": "M140,300 v91 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v91", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1663741401500", "attr": {"d": "M140,150 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=650 width=650 tabindex=\"0\"><defs><marker id=\"end-arrow_1664250830319\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1664250830319\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1664250830319\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1664250830319\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n          background-color: #f3f3f5;\n        }\n\n        g[type=pool] {\n          font-size: 13px;\n        }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g data-id=\"1663741401496\" shape=\"rectangle\" type=\"pool\" x=\"380\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(380,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(30,15)\"><tspan>编辑服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">2</text></g><g data-id=\"1663741401492\" shape=\"rectangle\" type=\"pool\" x=\"40\" y=\"60\" width=\"230\" height=\"540\" transform=\"translate(40,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"230\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"230\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"110.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"225.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"110.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(30,15)\"><tspan>分配服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">1</text></g><g name=\"line-wrapper\"><g tabindex=\"0\" data-id=\"1664157783968\"><path d=\"M500,230 v46 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v46\" start-id=\"1663900527500\" end-id=\"1663741401504\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1664250830319)\"></path></g><g tabindex=\"0\" data-id=\"1663900527501\"><path d=\"M190,250 h96 a20,20 0 0 0 20,-20 v-5 a20,20 0 0 1 20,-20 h96\" start-id=\"1663741401497\" end-id=\"1663900527500\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1664250830319)\"></path></g><g tabindex=\"0\" data-id=\"1663741401505\"><path d=\"M500,530 v-104 a0,0 0 0 1 0,-0 h0 a0,0 0 0 0 0,-0 v-104\" start-id=\"*************\" end-id=\"1663741401504\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1664250830319)\"></path></g><g tabindex=\"0\" data-id=\"1663741401503\"><path d=\"M220,505 h101 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h101\" start-id=\"1663741401491\" end-id=\"*************\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1664250830319)\"></path></g><g tabindex=\"0\" data-id=\"1663741401502\"><path d=\"M140,300 v91 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v91\" start-id=\"1663741401497\" end-id=\"1663741401491\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1664250830319)\"></path></g><g tabindex=\"0\" data-id=\"1663741401500\"><path d=\"M140,150 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\" start-id=\"1663741401490\" end-id=\"1663741401497\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1664250830319)\"></path></g></g><g data-id=\"1663741401490\" shape=\"rectangle\" type=\"startEvent\" x=\"110\" y=\"90\" width=\"60\" height=\"60\" transform=\"translate(110,90)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#70757f\" transform=\"translate(30,35)\"><tspan>开始</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1663741401491\" shape=\"rectangle\" type=\"userTask\" x=\"60\" y=\"480\" width=\"160\" height=\"50\" transform=\"translate(60,480)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>分配服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1663741401497\" type=\"exclusiveGateway\" shape=\"diamond\" transform=\"translate(140,200)\" x=\"140\" y=\"200\" width=\"100\" height=\"100\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"70.71067811865474\" height=\"70.71067811865474\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(0,55)\" textTransformLineTwo=\"translate(0,43)\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" transform=\"translate(-50,0)\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"41.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"95.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"91.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"95.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"41.5\" y=\"91.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(0,55)\"><tspan>是否分配服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(-50, 110)\" trans-y=\"110\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"*************\" shape=\"rectangle\" type=\"userTask\" x=\"420\" y=\"480\" width=\"160\" height=\"50\" transform=\"translate(420,480)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>处理服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1663741401504\" shape=\"rectangle\" type=\"endEvent\" x=\"470\" y=\"320\" width=\"60\" height=\"60\" transform=\"translate(470,320)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#70757f\" transform=\"translate(30,35)\"><tspan>结束</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1663900527500\" shape=\"rectangle\" type=\"executionTask\" x=\"420\" y=\"180\" width=\"160\" height=\"50\" transform=\"translate(420,180)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,20)\"><tspan x=\"0\" y=\"0\">服务请求状态变更为处</tspan><tspan x=\"0\" y=\"20\">理中</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "externalFlow": 0, "singleInstanceFlow": 0, "isDeleted": false, "hasInstance": false, "linkAppEnable": false, "rule": {"conditionPattern": "", "conditions": []}, "supportExternalFlow": true, "scope": ["CRM管理员", "受理客服"]}