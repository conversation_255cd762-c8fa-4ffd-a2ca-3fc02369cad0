{"sourceWorkflowId": "6459b4a8fbd3d40001f06c1f", "name": "服务请求流程", "enabled": true, "description": "", "entryType": "ServiceRequestObj", "entryTypeName": "服务请求【预设】", "rangeEmployeeIds": [], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": ["consultServiceRequestCustomer"], "workflow": {"externalFlow": 0, "entityId": "ServiceRequestObj", "errorNotifyRecipients": {}, "history": false, "type": "workflow_bpm", "singleInstanceFlow": 0, "enable": true, "appId": "BPM", "linkAppEnable": false, "name": "服务请求流程", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1683598114146"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "externalApplyTask", "executionName": "", "entityName": "服务请求【预设】", "entityId": "ServiceRequestObj", "externalApply": {"appCode": "7", "appName": "服务请求", "actionCode": "serviceRequestAssign", "actionName": "指派服务请求"}, "objectId": {"expression": "activity_0##ServiceRequestObj"}}, "linkAppEnable": false, "remind": false, "name": "分配服务请求", "description": "", "id": "1683598114151", "assignee": {}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 1}, {"type": "exclusiveGateway", "name": "是否分配服务请求", "description": "", "id": "1683598114152", "defaultTransitionId": "1683598114154", "gatewayType": 0}, {"type": "executionTask", "bpmExtension": {"executionType": "execution", "entityName": "服务请求【预设】", "entityId": "ServiceRequestObj", "objectId": {"expression": "activity_0##ServiceRequestObj"}}, "name": "服务请求状态变更为处理中", "description": "", "id": "1683598114158", "itemList": [{"taskType": "updates", "updateFieldJson": "[{\"isCalculate\":false,\"key\":\"${ServiceRequestObj.service_request_status}\",\"value\":\"handling\",\"defaultValue\":\"\",\"entityId\":\"activity_1683598114158##ServiceRequestObj\"}]"}], "delay": false}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "服务请求【预设】", "entityId": "ServiceRequestObj", "executionType": "update", "objectId": {"expression": "activity_0##ServiceRequestObj"}, "form": [[{"name": "subject", "type": "text", "required": false, "label": "主题"}, {"name": "problem_desc", "type": "long_text", "required": false, "label": "问题描述"}, {"name": "account_id", "type": "object_reference", "required": false, "label": "客户"}, {"name": "contact_id", "type": "object_reference", "required": false, "label": "联系人"}, {"name": "product_id", "type": "object_reference", "required": false, "label": "产品"}, {"name": "priority", "type": "select_one", "required": false, "label": "优先级"}, {"name": "appointment_processing_time", "type": "date_time", "required": false, "label": "客户预约处理时间"}, {"name": "service_address", "type": "group", "required": false, "fields": {"area_country": "country", "area_location": "location", "area_detail_address": "address", "area_city": "city", "area_province": "province", "area_district": "district"}, "group_type": "area", "label": "服务地址"}, {"name": "remark", "type": "long_text", "required": false, "label": "备注"}]], "enableLayoutRules": false, "defaultButtons": {"update": {"label": "编辑"}, "Save": {"label": "保存"}, "UpdateAndComplete": {"label": "保存并完成"}}}, "remind": false, "linkAppEnable": false, "name": "处理服务请求", "description": "", "id": "1683598114172", "assignee": {"ext_process": ["${activity_1683598114172##ServiceRequestObj##owner}"]}, "assigneeType": "assignee", "taskType": "anyone", "execution": {"pass": [{"taskType": "updates", "rowNo": 0, "modifyTime": 0, "updateFieldJson": "[{\"isCalculate\":false,\"key\":\"${ServiceRequestObj.service_request_status}\",\"value\":\"finished\",\"defaultValue\":\"\",\"entityId\":\"activity_1683598114151##ServiceRequestObj\"}]"}]}, "assignNextTask": 0, "externalApplyTask": 0}], "transitions": [{"description": "", "id": "1683598114154", "fromId": "1683598114152", "toId": "1683598114151", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##ServiceRequestObj##is_auto_commit"}, "right": {"value": "yes", "type": {"name": "text"}, "metadata": {"containSubDept": false}}}]}]}, "serialNumber": 0}, {"id": "1683598114153", "fromId": "1683598114146", "toId": "1683598114152", "serialNumber": 1}, {"description": "", "id": "1683598114161", "fromId": "1683598114152", "toId": "1683598114158", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##ServiceRequestObj##is_auto_commit"}, "right": {"value": "no", "type": {"name": "text"}, "metadata": {"containSubDept": false}}}]}]}, "serialNumber": 2}, {"id": "1683598114170", "fromId": "1683598114158", "toId": "*************", "serialNumber": 3}, {"id": "1683598114175", "fromId": "1683598114151", "toId": "1683598114172", "serialNumber": 4}, {"id": "1683598114176", "fromId": "1683598114172", "toId": "*************", "serialNumber": 5}], "variables": [{"id": "activity_0##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_1683598114151##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_1683598114158##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_1683598114172##ServiceRequestObj", "type": {"name": "text"}}, {"id": "activity_0##ServiceRequestObj##is_auto_commit", "type": {"name": "text"}}], "id": "6475af40c2c2f303f78e767b", "sourceWorkflowId": "6459b4a8fbd3d40001f06c1f"}, "extension": {"pools": [{"lanes": [{"id": "1683598114148", "name": "分配服务请求", "description": "", "activities": ["1683598114146", "1683598114152", "1683598114151"]}]}, {"lanes": [{"id": "1683598114155", "name": "编辑服务请求", "description": "", "activities": ["*************", "1683598114158", "1683598114172"]}]}], "diagram": [{"id": "1683598114148", "attr": {"width": 220, "height": 540, "x": 80, "y": 60}}, {"id": "1683598114155", "attr": {"width": 220, "height": 540, "x": 400, "y": 60}}, {"id": "*************", "attr": {"width": 60, "height": 60, "x": 470, "y": 260}}, {"id": "1683598114146", "attr": {"width": 60, "height": 60, "x": 160, "y": 125}}, {"id": "1683598114152", "attr": {"width": 100, "height": 100, "x": 190, "y": 240}}, {"id": "1683598114151", "attr": {"width": 160, "height": 50, "x": 110, "y": 390}}, {"id": "1683598114158", "attr": {"width": 160, "height": 50, "x": 430, "y": 130}}, {"id": "1683598114172", "attr": {"width": 160, "height": 50, "x": 430, "y": 390}}, {"id": "1683598114176", "attr": {"d": "M510,391 v-30 a5,5 0 0 0 -5,-5 h0 a5,5 0 0 1 -5,-5 v-30", "toPosition": "bottom", "fromPosition": "top", "type": "polyline"}}, {"id": "1683598114175", "attr": {"d": "M270,415 h81 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h81", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1683598114170", "attr": {"d": "M510,180 v36 a5,5 0 0 1 -5,5 h0 a5,5 0 0 0 -5,5 v36", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1683598114161", "attr": {"d": "M240,290 h76 a20,20 0 0 0 20,-20 v-95 a20,20 0 0 1 20,-20 h76", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1683598114153", "attr": {"d": "M190,185 v28 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v28", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1683598114154", "attr": {"d": "M190,340 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=650 width=670 tabindex=\"0\"><defs><marker id=\"end-arrow_1685433993097\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1685433993097\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1685433993097\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1685433993097\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n          background-color: #f3f3f5;\n        }\n\n        g[type=pool] {\n          font-size: 13px;\n        }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g name=\"pool-wrapper\"><g data-id=\"1683598114148\" shape=\"rectangle\" type=\"pool\" x=\"80\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(80,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\"><rect fill=\"rgba(255,255,255,0.4)\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#5498FF\" rx=\"0\" ry=\"0\" resizable=\"true\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"32\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(10,15)\"><tspan>分配服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1683598114155\" shape=\"rectangle\" type=\"pool\" x=\"400\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(400,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\"><rect fill=\"rgba(255,255,255,0.4)\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#5498FF\" rx=\"0\" ry=\"0\" resizable=\"true\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"32\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(10,15)\"><tspan>编辑服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><g name=\"line-wrapper\"><g tabindex=\"0\" data-id=\"1683598114176\"><path d=\"M510,391 v-30 a5,5 0 0 0 -5,-5 h0 a5,5 0 0 1 -5,-5 v-30\" start-id=\"1683598114172\" end-id=\"*************\" fill=\"transparent\" stroke-width=\"2.5\" type=\"polyline\" to-position=\"bottom\" from-position=\"top\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1685433993097)\"></path></g><g tabindex=\"0\" data-id=\"1683598114175\"><path d=\"M270,415 h81 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h81\" start-id=\"1683598114151\" end-id=\"1683598114172\" fill=\"transparent\" stroke-width=\"2.5\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1685433993097)\"></path></g><g tabindex=\"0\" data-id=\"1683598114170\"><path d=\"M510,180 v36 a5,5 0 0 1 -5,5 h0 a5,5 0 0 0 -5,5 v36\" start-id=\"1683598114158\" end-id=\"*************\" fill=\"transparent\" stroke-width=\"2.5\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1685433993097)\"></path></g><g tabindex=\"0\" data-id=\"1683598114161\"><path d=\"M240,290 h76 a20,20 0 0 0 20,-20 v-95 a20,20 0 0 1 20,-20 h76\" start-id=\"1683598114152\" end-id=\"1683598114158\" fill=\"transparent\" stroke-width=\"2.5\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1685433993097)\"></path></g><g tabindex=\"0\" data-id=\"1683598114153\"><path d=\"M190,185 v28 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v28\" start-id=\"1683598114146\" end-id=\"1683598114152\" fill=\"transparent\" stroke-width=\"2.5\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1685433993097)\"></path></g><g tabindex=\"0\" data-id=\"1683598114154\"><path d=\"M190,340 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\" start-id=\"1683598114152\" end-id=\"1683598114151\" fill=\"transparent\" stroke-width=\"2.5\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1685433993097)\"></path></g></g><g data-id=\"*************\" shape=\"rectangle\" type=\"endEvent\" x=\"470\" y=\"260\" width=\"60\" height=\"60\" transform=\"translate(470,260)\" tabindex=\"0\" class=\"\"><rect fill=\"#737C8C\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#737C8C\" stroke-width=\"3\" color=\"#FFFFFF\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#FFFFFF&quot; stroke-width=&quot;2&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#FFFFFF\" stroke-width=\"2\" color=\"#e67373\" type=\"rect\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#FFFFFF\" transform=\"translate(30,35)\"><tspan>结束</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1683598114146\" shape=\"rectangle\" type=\"startEvent\" x=\"160\" y=\"125\" width=\"60\" height=\"60\" transform=\"translate(160,125)\" tabindex=\"0\" class=\"\"><rect fill=\"#16B4AB\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#16B4AB\" stroke-width=\"3\" color=\"#FFFFFF\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#FFFFFF\" transform=\"translate(30,35)\"><tspan>开始</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1683598114152\" type=\"exclusiveGateway\" shape=\"diamond\" transform=\"translate(190,240)\" x=\"190\" y=\"240\" width=\"100\" height=\"100\" tabindex=\"0\" class=\"bpm-draw-polyline-able\"><rect fill=\"#E6F4FF\" width=\"70.71067811865474\" height=\"70.71067811865474\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(0,55)\" textTransformLineTwo=\"translate(0,43)\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" transform=\"translate(-50,0)\" stroke=\"#49bffc\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"41.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"41.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"95.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"91.5\" y=\"41.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"95.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"41.5\" y=\"91.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(0,55)\"><tspan>是否分配服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(-50, 110)\" trans-y=\"110\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1683598114151\" shape=\"rectangle\" type=\"userTask\" x=\"110\" y=\"390\" width=\"160\" height=\"50\" transform=\"translate(110,390)\" tabindex=\"0\" class=\"bpm-draw-polyline-able\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>分配服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1683598114158\" shape=\"rectangle\" type=\"executionTask\" x=\"430\" y=\"130\" width=\"160\" height=\"50\" transform=\"translate(430,130)\" tabindex=\"0\" class=\"bpm-draw-polyline-able\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,20)\"><tspan x=\"0\" y=\"0\">服务请求状态变更为处<title>服务请求状态变更为处理中</title></tspan><tspan x=\"0\" y=\"20\">理中<title>服务请求状态变更为处理中</title></tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1683598114172\" shape=\"rectangle\" type=\"userTask\" x=\"430\" y=\"390\" width=\"160\" height=\"50\" transform=\"translate(430,390)\" tabindex=\"0\" class=\"bpm-draw-polyline-able bpm-shape-focus-node node-focused\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>处理服务请求</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "externalFlow": 0, "singleInstanceFlow": 0, "isDeleted": false, "hasInstance": true, "linkAppEnable": false}