{"sourceWorkflowId": "5ea6bc200d0fd00001815bf7", "name": "外勤上门安装服务流程（示例）", "enabled": true, "description": "", "entryType": "CasesObj", "entryTypeName": "工单", "rangeEmployeeIds": [], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": ["00000000000000000000000000000010"], "workflow": {"externalFlow": 0, "entityId": "CasesObj", "history": false, "type": "workflow_bpm", "singleInstanceFlow": 0, "appId": "BPM", "linkAppEnable": false, "name": "外勤上门安装服务流程（示例）", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1587980653910"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "externalApplyTask", "executionName": "", "entityName": "工单", "entityId": "CasesObj", "externalApply": {"appCode": "0", "appName": "服务通", "actionCode": "assign", "actionName": "可指派任意对象"}, "objectId": {"expression": "activity_0##CasesObj"}}, "remind": false, "name": "工单指派", "description": "", "id": "1587980653924", "assignee": {}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 1, "linkAppEnable": false}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "工单", "entityId": "CasesObj", "objectId": {"expression": "activity_0##CasesObj"}, "executionType": "update", "form": [[{"name": "field_gcjl__c", "label": "服务过程记录", "type": "long_text", "required": false}, {"name": "field_xcpz__c", "label": "服务现场拍照", "type": "image", "required": false}]], "enableLayoutRules": false}, "remind": false, "name": "工单处理", "description": "", "id": "1587980653926", "assignee": {"extUserType": ["${activity_1587980653926##CasesObj##field_xcfwry__c}"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0, "linkAppEnable": false}, {"type": "endEvent", "name": "结束", "description": "", "id": "1587980653929"}], "transitions": [{"id": "1587980653925", "fromId": "1587980653910", "toId": "1587980653924", "serialNumber": 0}, {"id": "1587980653930", "fromId": "1587980653924", "toId": "1587980653926", "serialNumber": 1}, {"id": "1587980653931", "fromId": "1587980653926", "toId": "1587980653929", "serialNumber": 2}], "variables": [{"id": "activity_0##CasesObj", "type": {"name": "text"}}, {"id": "activity_1587980653924##CasesObj", "type": {"name": "text"}}, {"id": "activity_1587980653926##CasesObj", "type": {"name": "text"}}], "sourceWorkflowId": "5ea6bc200d0fd00001815bf7"}, "extension": {"pools": [{"lanes": [{"id": "1587980653912", "name": "工单指派", "description": "", "activities": ["1587980653910", "1587980653924"]}]}, {"lanes": [{"id": "1587980653922", "name": "工单处理", "description": "", "activities": ["1587980653926"]}]}, {"lanes": [{"id": "1587980653923", "name": "结束", "description": "", "activities": ["1587980653929"]}]}], "diagram": [{"id": "1587980653923", "attr": {"width": 220, "height": 540, "x": 682, "y": 66}}, {"id": "1587980653922", "attr": {"width": 220, "height": 540, "x": 345, "y": 61}}, {"id": "1587980653912", "attr": {"width": 220, "height": 540, "x": 40, "y": 60}}, {"id": "1587980653910", "attr": {"width": 60, "height": 60, "x": 120, "y": 125}}, {"id": "1587980653924", "attr": {"width": 160, "height": 50, "x": 76, "y": 268}}, {"id": "1587980653926", "attr": {"width": 160, "height": 50, "x": 376, "y": 265}}, {"id": "1587980653929", "attr": {"width": 60, "height": 60, "x": 757, "y": 437}}, {"id": "1587980653931", "attr": {"d": "M536,290 h91 a20,20 0 0 1 20,20 v137 a20,20 0 0 0 20,20 h91", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1587980653930", "attr": {"d": "M236,293 h69 a1.5,1.5 0 0 0 1.5,-1.5 v0 a1.5,1.5 0 0 1 1.5,-1.5 h69", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1587980653925", "attr": {"d": "M150,185 v39 a3,3 0 0 0 3,3 h0 a3,3 0 0 1 3,3 v39", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=656 width=952 tabindex=\"0\"><defs><marker id=\"end-arrow_1588126905758\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1588126905758\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1588126905758\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1588126905758\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n          background-color: #f3f3f5;\n        }\n\n        g[type=pool] {\n          font-size: 13px;\n        }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g data-id=\"1587980653923\" shape=\"rectangle\" type=\"pool\" x=\"682\" y=\"66\" width=\"220\" height=\"540\" transform=\"translate(682,66)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(30,15)\"><tspan>结束</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">3</text></g><g data-id=\"1587980653922\" shape=\"rectangle\" type=\"pool\" x=\"345\" y=\"61\" width=\"220\" height=\"540\" transform=\"translate(345,61)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(30,15)\"><tspan>工单处理</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">2</text></g><g data-id=\"1587980653912\" shape=\"rectangle\" type=\"pool\" x=\"40\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(40,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(30,15)\"><tspan>工单指派</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">1</text></g><g name=\"line-wrapper\"><g tabindex=\"0\" data-id=\"1587980653931\"><path d=\"M536,290 h91 a20,20 0 0 1 20,20 v137 a20,20 0 0 0 20,20 h91\" start-id=\"1587980653926\" end-id=\"1587980653929\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1588126905758)\"></path></g><g tabindex=\"0\" data-id=\"1587980653930\"><path d=\"M236,293 h69 a1.5,1.5 0 0 0 1.5,-1.5 v0 a1.5,1.5 0 0 1 1.5,-1.5 h69\" start-id=\"1587980653924\" end-id=\"1587980653926\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1588126905758)\"></path></g><g tabindex=\"0\" data-id=\"1587980653925\"><path d=\"M150,185 v39 a3,3 0 0 0 3,3 h0 a3,3 0 0 1 3,3 v39\" start-id=\"1587980653910\" end-id=\"1587980653924\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1588126905758)\"></path></g></g><g data-id=\"1587980653910\" shape=\"rectangle\" type=\"startEvent\" x=\"120\" y=\"125\" width=\"60\" height=\"60\" transform=\"translate(120,125)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#70757f\" transform=\"translate(30,35)\"><tspan>开始</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1587980653924\" shape=\"rectangle\" type=\"userTask\" x=\"76\" y=\"268\" width=\"160\" height=\"50\" transform=\"translate(76,268)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>工单指派</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1587980653926\" shape=\"rectangle\" type=\"userTask\" x=\"376\" y=\"265\" width=\"160\" height=\"50\" transform=\"translate(376,265)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>工单处理</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1587980653929\" shape=\"rectangle\" type=\"endEvent\" x=\"757\" y=\"437\" width=\"60\" height=\"60\" transform=\"translate(757,437)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#70757f\" transform=\"translate(30,35)\"><tspan>结束</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "externalFlow": 0, "singleInstanceFlow": 0, "isDeleted": false, "hasInstance": false, "linkAppEnable": false, "rule": {"conditionPattern": "", "conditions": []}, "supportExternalFlow": true, "scope": ["服务人员"]}