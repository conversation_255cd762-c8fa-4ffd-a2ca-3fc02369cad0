##  前后端接口文档

[WEB接口](http://git.firstshare.cn/bpm/fs-bpm/wikis/home)

##  设计 

[5.5原型稿](http://172.31.101.2/prototype/5.5.0/CRM/BPM/)

[5.7](http://172.31.101.2/prototype/5.7.0/CRM/BPM/)

##  规 范

[异常码规范](http://wiki.firstshare.cn/pages/viewpage.action?pageId=33951018)

##  日志调整

[日志调整](http://wiki.firstshare.cn/pages/viewpage.action?pageId=19666838)

## 测试环境 

### 112环境：
```
BPM 5.6环境

57612/18210911798/a123456

BPM 5.5环境完毕：   53588/18210911798/a123456

```

### 113 环境
```
5.5后端

2/test/123qwe 北京欢迎你 管理员   
2/luoy/123qwe 罗瑛      普通用户

5.6 后端
54821/15110130731/123qwe

```
## 线上资源 

### 线上bpm rest 服务的全网路由和灰度路由

```

http://bpm.nsvc.foneshare.cn
http://bpm-gray.nsvc.foneshare.cn  

```
###  线上跟进入口 

1. 线上接口调用统计
2. [JVM](http://wiki.firstshare.cn/pages/viewpage.action?pageId=19666838)
###  服务器

vlnx021014 (8vCPU/8GB Memory/50GB OS Disk)(用途：BPM服务器 /负责人：万松;赵永刚) + 控制台
vlnx021015 (8vCPU/8GB Memory/50GB OS Disk)(用途：BPM服务器 /负责人：万松;赵永刚) + 灰度服务
vlnx021016 (8vCPU/8GB Memory/50GB OS Disk)(用途：BPM服务器 /负责人：万松;赵永刚) + 灰度服务

###  数据库

```
mongo.servers=********************************************************************************************************************************************/
mongo.dbName = BPM
mongo.mapPackage = com.facishare.bpm.dao.entity
mongo.connectTimeout = 5000
mongo.socketTimeout = 10000
encrypt.pwd = true
```

### git分支使用规则

1. master,hotfix-xxx,release-x.x,develop,feature-xxxx
2. 上线时从master上打tag

<img src="http://img.blog.csdn.net/20151229145702928" height="500px"/>


