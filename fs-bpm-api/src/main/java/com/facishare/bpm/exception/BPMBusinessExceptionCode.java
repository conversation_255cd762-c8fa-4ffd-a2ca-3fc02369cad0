package com.facishare.bpm.exception;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * //TODO  把国际码放到这里面
 * BPM 业务异常码 <p> Created by <PERSON> on 16/8/23.
 */
public enum BPMBusinessExceptionCode {

    /*
     * 提示
     */
    PAAS_FLOW_BPM_VERIFIY_RESET_BPM_FLOW_NAME(110_01_0300, "请重新设定流程名称"),

    /**
     * 警告
     */

    PAAS_FLOW_BPM_PARAMETER_ANOMALY(210_01_0400, "参数异常"),
    PAAS_FLOW_BPM_PLEASE_REFRESH_THE_PAGE(210_01_0401, "当前访问的服务接口已下线，请尝试刷新页面"),


    /***********************************************
     * 错误
     ***********************************************/
    PAAS_FLOW_BPM_SERVER_ERROR(310_01_0500, "服务异常"),
    PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION(310_01_1006, "{0}"),
    PAAS_FLOW_BPM_REMOTE_SERVICE_REQUEST_ERROR(310_01_0501, ""),


    /**
     * 业务异常
     */
    PAAS_FLOW_BPM_DEFINE_NOT_FOUND(210_01_1001, "流程定义不存在"),
    PAAS_FLOW_BPM_VERIFIY_CHOICE_SCOPE(210_01_1019, "请选择流程适用范围"),
    PAAS_FLOW_BPM_VERIFIY_INLET_OBJECT_DISABLE_OR_DELETE(210_01_1020, "入口对象 已被禁用或被删除 ,请确认"),
    PAAS_FLOW_BPM_TIPS_EXPRESSION_ERROR(210_01_1021, "表达式存在异常"),
    PAAS_FLOW_BPM_OBJECT_VARIABLE_ERROR(210_01_1022, "对象变量不正确,请确认"),
    PAAS_FLOW_BPM_FLOW_VARIABLE_NOT_SUPPORT(210_01_1023, "流程变量不支持{0}"),
    PAAS_FLOW_BPM_FLOW_HANDLE_ERROR(210_01_1024, "流程处理异常"),
    PAAS_FLOW_BPM_VERIFIY_FORMAT_ERROR(210_01_1025, "{0} 表单格式不正确"),
    PAAS_FLOW_BPM_VERIFIY_EXPRESSION_NOT_FOUND_VARIABLE(210_01_1026, "定义中表达式未添加到变量定义中"),
    PAAS_FLOW_BPM_TASK_NOT_FOUND_ASSIGNEED(210_01_1027, "{0} 没有设置处理人"),
    PAAS_FLOW_BPM_DEFINE_NOT_FOUND_START_NODE(210_01_1028, "没有开始节点"),
    PAAS_FLOW_BPM_VERIFIY_ABSENCE_BUSS_NODE(210_01_1029, "缺少业务节点"),
    PAAS_FLOW_BPM_VERIFIY_NOT_FOUND_CONNECT_START_NODE(210_01_1030, "流程配置错误 {0} 没有连接到开始节点,请确认"),
    PAAS_FLOW_BPM_OBJECT_FIELD_DISABLE(210_01_1031, "{0} 的{1} 已被禁用,请确认"),
    PAAS_FLOW_BPM_OBJECT_FIELD_DELETE(210_01_1032, "{0} 的{1} 已被删除,请确认"),
    PAAS_FLOW_BPM_NOT_SUPPORT_VARIABLE(210_01_1033, "业务流程不支变量 {0}"),
    PAAS_FLOW_BPM_VERIFIY_TRIGGER_CONDITION(210_01_1034, "流程发起条件中 {0}"),
    PAAS_FLOW_BPM_FILTER_CONFIG_DEPT_FIELD_ERROR(210_01_1035, "过滤器配置部门类型字段有误"),
    PAAS_FLOW_BPM_VERIFIY_NODE_COMPLETE_CONDITION(210_01_1036, "{0} 节点完成条件中{1}"),
    PAAS_FLOW_BPM_VERIFIY_NODE_NON_EXIST_STAGE(210_01_1037, "节点应该在阶段中"),
    PAAS_FLOW_BPM_VERIFIY_STAGE_GET_INFO_ERROR(210_01_1038, "阶段信息获取异常"),
    PAAS_FLOW_BPM_VERIFIY_STAGE_NOT_FOUND_NODE(210_01_1039, "{0} 阶段中没有节点"),
    PAAS_FLOW_BPM_VERIFIY_NOT_FOUND(210_01_1040, "{0} 不在阶段中"),
    PAAS_FLOW_BPM_DRAFT_NOT_FOUND(210_01_1041, "草稿已删除或不存在"),
    PAAS_FLOW_BPM_START_TO_NODE_NEED_APPROVAL_NODE(210_01_1042, "开始节点到 {0} 之间必须有审批、会签或业务节点"),
    PAAS_FLOW_BPM_VERIFIY_BASE_INLET_OBJECT_MANY(210_01_1043, "入口对象出现了 {0} 个,请确认复制流程时入口对象没有发生变化"),
    PAAS_FLOW_BPM_VERIFIY_LOOP_MONITOR_ERROR(210_01_1044, "回路监测异常,请检查流程图"),
    PAAS_FLOW_BPM_VERIFIY_DEFINE_LOOP_ERROR(210_01_1045, "存在闭环流程,请修改流程图"),
    PAAS_FLOW_BPM_VERIFIY_STAGE_NAME_ISNULL(210_01_1046, "阶段名称不能为空"),
    PAAS_FLOW_BPM_VALIDATE_POOL_IS_EMPTY(210_01_1047, "泳道不能为空"),
    PAAS_FLOW_BPM_VERIFIY_NODE_BRANCH_IN_LINE_ERROR(210_01_1048, "{0} 节点分支{1} 连入节点有误,请确认"),
    PAAS_FLOW_BPM_VERIFIY_NODE_BRANCH_OUT_LINE_ERROR(210_01_1049, "{0} 节点分支{1} 连出有误,请确认"),
    PAAS_FLOW_BPM_VERIFIY_CONDITION_NOT_FOUND_VARIABLE(210_01_1050, "{0} 分支的{1} 条件不在变量中"),
    PAAS_FLOW_BPM_VERIFIY_CONDITION_VARIABLE_CONTAINS(210_01_1051, "{0} 分支{1} 变量中 {2}"),
    PAAS_FLOW_BPM_VALIDATE_EXTERNAL_FLOW_FIRST_ACTIVITY_MUST_HANDLER_ACTIVITY(210_01_1052, "外部流程第一个节点必须是业务节点或审批节点"),
    PAAS_FLOW_BPM_VERIFIY_NODE_ASSIGNEEID_NOT_SET(210_01_1053, "节点未设置处理人"),
    PAAS_FLOW_BPM_VERIFIY_NOT_SUPPORTED(210_01_1054, "{0} 不支持  {1} 类型"),
    PAAS_FLOW_BPM_VALIDATE_DATA_RELATED_OR_VARIABLE_CONFIG_ERROR(210_01_1055, "{0} 数据相关或流程变量配置有误"),
    PAAS_FLOW_BPM_VERIFIY_PERSON_PROP_CONFIG_ERROR(210_01_1056, "{0} 人员对象属性变量配置不正确"),
    PAAS_FLOW_BPM_VALIDATE_RELATED_OBJECT_PERSON_CONFIG_ERROR(210_01_1057, "{0} 相关对象的人员字段配置中 {1}"),
    PAAS_FLOW_BPM_VERIFIY_OBJECT_EMAIL_FIELD_EXPRESSION_CONFIG_ERROR(210_01_1058, "{0} 对象属性邮件类型字段变量配置不正确"),
    PAAS_FLOW_BPM_VALIDATE_RELATED_OBJECT_EMAIL_CONFIG_ERROR(210_01_1059, "{0} 相关对象的邮件字段配置中 {1}"),
    PAAS_FLOW_BPM_VALIDATE_GATEWAY_NEED_APPEAR_IN_PAIRS(210_01_1060, "网关节点需要成对出现"),
    PAAS_FLOW_BPM_VALIDATE_NOT_SET_BELONG_OBJECT(210_01_1061, "{0} 未设置所属对象"),
    PAAS_FLOW_BPM_VALIDATE_NOT_SET_BELONG_OBJECT_NAME(210_01_1062, "{0} 未设置所属对象名称"),
    PAAS_FLOW_BPM_VALIDATE_NOT_CONFIG_EXECUTIONTYPE(210_01_1063, "{0} 未设置executionType"),
    PAAS_FLOW_BPM_VALIDATE_NOT_CONFIG_RELATEDENTITYID(210_01_1064, "{0} 未设置relatedEntityId"),
    PAAS_FLOW_BPM_VALIDATE_NOT_SET_BUSINESS_RECORD(210_01_1065, "{0} 未设置业务记录"),
    PAAS_FLOW_BPM_TASK_CHECK_OBJECT_INFO(210_01_1066, "{0} 请检查任务对象信息"),
    PAAS_FLOW_BPM_NODE_DATA_ERROR(210_01_1067, "{0} 节点数据选择错误"),
    PAAS_FLOW_BPM_VERIFIY_TASK_MISMATCH(210_01_1068, "当前节点对象和入口对象不一致"),
    PAAS_FLOW_BPM_VALIDATE_NO_DEFAULT_TRANSITION(210_01_1069, "{0} 需要配置默认分支"),
    PAAS_FLOW_BPM_VERIFIY_AT_MOST(210_01_1070, "{0} 节点只能有{1} 条{2} 的线"),
    PAAS_FLOW_BPM_VERIFIY_AT_LEAST(210_01_1071, "{0} 节点至少有条{1} 的{2} 线"),
    PAAS_FLOW_BPM_VALIDATE_TRANSITION_CONDITION_ERROR(210_01_1072, "线条表达式异常"),
    PAAS_FLOW_BPM_VALIDATE_NOT_SET_ALL_APPROVE_TYPE(210_01_1073, "{0} 未设置会签类型"),
    PAAS_FLOW_BPM_VALIDATE_NOT_SET_OPERATE_TYPE(210_01_1074, "{0} 未设置操作类型"),
    PAAS_FLOW_BPM_DEFINE_DELETE_ERROR_OF_STOP(210_01_1075, "删除失败,请确认该流程是否已经停用"),
    PAAS_FLOW_BPM_TASK_NOT_FOUND_TASKTYPE(210_01_1076, "流程定义异常,任务节点缺少,任务类型"),
    PAAS_FLOW_BPM_OBJECT_DELETE_DISABLE(210_01_1077, "{0} 对象已被删除或禁用,请确认"),
    PAAS_FLOW_BPM_ENTITY_FIELD_DELETE_DISABLE(210_01_1078, "{0} 的字段{1} 已经删除或停用,请确认"),
    PAAS_FLOW_BPM_WHO_HAS_DELETED_DEF(210_01_1079, "{0}由{1}在{2}将流程定义删除,请确认"),
    PAAS_FLOW_BPM_DEFINE_SERVICE_DELETE(210_01_1080, "{0} 流程定义已被删除,请确认"),
    PAAS_FLOW_BPM_WHO_HAS_STOP_DEF(210_01_1081, "{0}由{1}在{2}将流程定义停用,请确认"),
    PAAS_FLOW_BPM_DEFINE_SERVICE_STOP(210_01_1082, "{0} 流程定义已被停用,请确认"),
    PAAS_FLOW_BPM_DRAFT_QUOTA_ERROR(210_01_1083, "草稿配额数量已达上限:草稿配额为定义配额的两倍"),
    PAAS_FLOW_BPM_DRAFT_ALREADY_EXIST(210_01_1084, "当前定义草稿已存在,请编辑原草稿"),
    PAAS_FLOW_BPM_DRAFT_DATA_CONFIG_ERROR(210_01_1085, "定义草稿数据结构有误,请联系纷享客服"),
    PAAS_FLOW_BPM_NODE_DEFINE_NOT_FOUND(210_01_1086, "节点定义不存在，请确认参数的正确性"),
    PAAS_FLOW_BPM_VERIFIY_RELATION_OBJECT_NOT_FOUND(210_01_1087, "该流程关联的对象不存在,请确认"),
    PAAS_FLOW_BPM_REPEAT_TRIGGER(210_01_1088, "[{0} ]业务流程不允许重复发起"),
    PAAS_FLOW_BPM_INSTANCE_COMPLETE_OR_CANCEL(210_01_1089, "该流程已完成或已取消"),
    PAAS_FLOW_BPM_INSTANCE_NOT_EXISTS(210_01_1090, "未查询到流程实例"),
    PAAS_FLOW_BPM_INSTANCE_ID_NOT_FOUND(210_01_1091, "流程实例id不能为空"),
    PAAS_FLOW_BPM_OBJECT_DESCRIBE_NOT_FOUND(210_01_1092, "对象描述不存在"),
    PAAS_FLOW_BPM_NODE_LOOKUP_OBJECT_NOT_FOUND(210_01_1093, "节点对象的lookup对象不存在"),
    PAAS_FLOW_BPM_TENANT_NOT_QUOTA(210_01_1094, "当前模块可用配额数不足，请联系纷享客服购买更高版本或增购资源包。"),
    PAAS_FLOW_BPM_TASK_NOT_FOUND(210_01_1095, "任务不存在"),
    PAAS_FLOW_BPM_TASK_CHANGE_OWNER_EXPRESSION_ERROR(210_01_1096, "更换处理人人员变量存在异常"),
    PAAS_FLOW_BPM_VERIFIY_CHOICE_BPM_FLOW(210_01_1097, "请选择业务流程"),
    PAAS_FLOW_BPM_OBJECT_NULL(210_01_1098, "对象不能为空"),
    PAAS_FLOW_BPM_CANDIDATE_GET_ASSIGNEED_ERROR(210_01_1099, "解析人员失败,不支持{0}"),
    PAAS_FLOW_BPM_PARAMETER_INCORRECT(210_01_1100, "参数不正确,请联系纷享客服"),
    PAAS_FLOW_BPM_TASK_NOT_AUTH_COMPLETE(210_01_1101, "您无执行权限"),
    PAAS_FLOW_BPM_NODE_OBJECT_ERROR(210_01_1102, "节点对象异常{0}"),
    PAAS_FLOW_BPM_TASK_GET_TASK_ERROR(210_01_1103, "获取任务信息失败"),
    PAAS_FLOW_BPM_NO_SUPPORT_TYPE(210_01_1104, "暂时不支持此类型:{0}"),
    PAAS_FLOW_BPM_VALIDATE_HAS_STOP(210_01_1105, "[{0}]业务流程定义已停用"),
    PAAS_FLOW_BPM_CANDIDATE_PARAMS_ERROR(210_01_1106, "流程分支条件涉及到的参数值 {0} 不正确,目前是 {1} ,请确认"),
    PAAS_FLOW_BPM_TASK_STATUS_PAAS(210_01_1107, "任务已经被处理"),
    PAAS_FLOW_BPM_INSTANCE_STOP_CALL_CRM_SYSTEM(210_01_1108, "当前流程已被终止,请联系管理员确认"),
    PAAS_FLOW_BPM_TASK_STATUS_PAAS_BY_USER(210_01_1109, "您已经处理过此任务"),
    PAAS_FLOW_BPM_TASK_RELATION_OBJECT_DELETE(210_01_1110, "请检查任务相关对象是否被删除、作废或无权限,无法完成任务"),//任务相关对象已被删除或作废,无法完成任务(无权限也会提示@亚庆)
    PAAS_FLOW_BPM_VERIFIY_CHOICE_NEXT_ASSIGNEED(210_01_1111, "请指定下一节点处理人"),
    PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND(210_01_1112, "数据不存在"),
    PAAS_FLOW_BPM_TASK_NOT_SETTING_ACTION_CODE(210_01_1113, "没有设置ActionCode"),
    PAAS_FLOW_BPM_VERIFIY_ABSENCE_APPROVAL_RESULT(210_01_1114, "缺少审批结果"),
    PAAS_FLOW_BPM_TASK_PARAMS_ERROR(210_01_1115, "处理任务请求参数不正确,请联系技术人员"),
    PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA(210_01_1116, "没有选择关联数据,不能完成任务"),
    PAAS_FLOW_BPM_FORM_FORMAT_DATA_NULL(210_01_1117, "表单数据为空"),
    PAAS_FLOW_BPM_TASK_RELATION_DESCRIBE_DELETE(210_01_1118, "任务关联的对象描述已被删除,请确认"),
    PAAS_FLOW_BPM_TASK_CHOICE_OR_CREATE_OBJECT_NOT_FOUND(210_01_1119, "任务选择和创建的对象未找到"),
    PAAS_FLOW_BPM_VALIDATE_THE_WAITING_TIME_SET_ERROR(210_01_1120, " {0} 允许停留时长设置有误请检查"),
    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_ERROR(210_01_1121, " {0} 超时提醒提醒时间设置超出允许停留时间,请检查"),
    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_FIELD_STOP_OR_DELETE(210_01_1122, " {0} 超时提醒 提醒时间设置的对象或字段已停用或删除,请检查"),
    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_ISNULL(210_01_1123, "{0} 超时提醒内容不能为空"),
    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_EXPRESSION_ERROR(210_01_1124, "{0} 超时提醒内容中表达式存在异常"),
    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_TITILE_ISNULL(210_01_1125, "{0} 超时提醒标题不能为空"),
    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_TITILE_EXPRESSION_ERROR(210_01_1126, "{0} 超时提醒标题中表达式存在异常"),
    PAAS_FLOW_BPM_INSTANCE_NAME_REQUIRED(210_01_1127, "请填写流程名称"),
    PAAS_FLOW_BPM_ENTRY_NAME_REQUIRED(210_01_1128, "请选择入口对象名称"),
    PAAS_FLOW_BPM_ENTRY_TYPE_REQUIRED(210_01_1129, "请选择入口对象类型"),
    PAAS_FLOW_BPM_SUPPORT_NOT_FOUND(210_01_1130, "扩展没找到"),
    PAAS_FLOW_BPM_INSTANCE_NOT_FOUND(210_01_1131, "流程实例不存在"),
    PAAS_FLOW_BPM_GET_PATTERN_ERROR(210_01_1132, "获取模板异常"),
    PAAS_FLOW_BPM_TEMPLATE_DELETE_CHOOSE_OTHER(210_01_1133, "该流程模板已删除，请选择别的模板"),
    PAAS_FLOW_BPM_LOOKUP_OBJECT_ALREADY_DELETE(210_01_1134, "{0} 节点关联的对象已被禁用或删除，请确认"),
    PAAS_FLOW_BPM_GET_ENTERPRISE_INFO_ERROR(210_01_1135, "获取企业信息失败"),
    PAAS_FLOW_BPM_BRANCH_SUPPORTS_CHILD_DEPT_EXCEPTION(210_01_1136, "分支节点支持子部门配置异常"),
    PAAS_FLOW_BPM_ACTIVITY_REMIND_FORMAT_ERROR(210_01_1137, "允许停留时长格式存在异常"),//TODO 作废
    PAAS_FLOW_BPM_WORKFLOW_TEMPLATE_NO_DEFINITION_FOUND(210_01_1138, "模版不存在,请联系技术人员"),
    PAAS_FLOW_BPM_APPROVAL_NODE_CONFIG_ERROR(210_01_1139, "{0} 审批节点或会签节点配置有误，请确认"),
    PAAS_FLOW_BPM_REMIND_EMAIL_FORMAT_ERROR(210_01_1140, "{0} 超时邮件提醒信息不完整"),
    PAAS_FLOW_BPM_RESOLVE_STAFF_VARIABLE_EXCEPTION(210_01_1141, "解析人员变量异常"),
    PAAS_FLOW_BPM_DEPT_NOT_EXIST_EXCEPTION(210_01_1142, "{0} 部门不存在，请确认"),
    PAAS_FLOW_BPM_DEPT_IS_DELETE_EXCEPTION(210_01_1143, "{0} 部门已删除，请确认"),
    PAAS_FLOW_BPM_DEPT_IS_STOP_EXCEPTION(210_01_1144, "{0} 部门已停用，请确认"),
    PAAS_FLOW_BPM_ROLE_IS_EXIST_EXCEPTION(210_01_1145, "{0} 角色不存在或已删除，请确认"),
    PAAS_FLOW_BPM_GROUP_IS_EXIST_EXCEPTION(210_01_1146, "{0} 用户组不存在或已删除，请确认"),
    PAAS_FLOW_BPM_GROUP_IS_STOP_EXCEPTION(210_01_1147, "{0} 用户组已停用，请确认"),
    PAAS_FLOW_BPM_USER_IS_EXIST_EXCEPTION(210_01_1148, "{0} 用户不存在或已删除，请确认"),
    PAAS_FLOW_BPM_USER_IS_STOP_EXCEPTION(210_01_1149, "{0} 用户已停用，请确认"),
    PAAS_FLOW_BPM_ORG_EXCEPTION(210_01_1150, "{0} {1} {2}"),
    PAAS_FLOW_BPM_REMIND_CONTENT_EXCEPTION(210_01_1151, "{0} 超时提醒内容 {1}"),
    PAAS_FLOW_BPM_REMIND_TITLE_EXCEPTION(210_01_1152, "{0} 超时提醒标题 {1}"),
    PAAS_FLOW_BPM_ACTIVITY_REMIND_FORMAT_ERROR_BY_TASK(210_01_1153, "{0} 节点允许停留时长格式存在异常"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_PARAMS_NOT_SETTING(210_01_1154, "应用节点 {0} 参数配置不完整，请检查"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_NOT_SETTING(210_01_1155, "应用节点 {0} 未配置需要做的事情，请检查"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_OFFLINE(210_01_1156, "应用节点 {0} {1} 已下线，请检查"),
    PAAS_FLOW_BPM_DATA_INVALID_OR_DELETED_NOT_TRIGGER_FLOW(210_01_1157, "数据作废或已删除，不允许发起流程"),
    PAAS_FLOW_BPM_TIME_OUT_REMINDER_STRUCTURE_ERROR(210_01_1158, "{0} 超时提醒结构体设置异常，请删除重新添加"),
    PAAS_FLOW_BPM_EXTERNAL_APPLY_TASK_NONSUPPORT_LINK_APP(210_01_1159, "{0} {1} {2} 不支持互联应用"),
    PAAS_FLOW_BPM_NOT_TOGETHER_EXTERNAL_LINK(210_01_1160, "互联应用不能与外部流程一起使用"),
    PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_CAN_NOT_BE_EMPTY(210_01_1161,"任务或定义未选择互联应用。"),
    PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_ILLEGAL(210_01_1162,"定义未配置互联应用，{0} 节点不允许配置互联应用"),
    PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_EXTERNAL_ROLE(210_01_1163,"{0} 节点配置了外部角色，则必须启动互联应用"),
    PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_ASSIGN_NEXT_TASK(210_01_1163,"{0} 节点配置了互联应用，则不支持指定下一节点处理人"),
    PAAS_FLOW_BPM_FUNCTION_VALUE_IS_NULL(210_01_1164,"节点 {0} 配置的后动作函数 {1} 字段 {2} value值为空，请选择变量或填写常量值"),
    PAAS_FLOW_BPM_COMPLETE_CONDITION_FUNCTION_VALUE_IS_NULL(210_01_1165,"节点 {0} 配置的函数完成条件 {1} 字段 {2} value值为空，请选择变量或填写常量值"),
    PAAS_FLOW_BPM_NODE_COMPLETE_CONDITION(210_01_1166,"节点:{0} 完成条件 {1}"),
    PAAS_FLOW_BPM_AFTER_ACTION(210_01_1167,"节点:{0} 后动作 {1}"),
    PAAS_FLOW_BPM_NON_EXTERNAL_OR_INTERCONNECTED_PROCESSE_NOT_SUPPORT_EXTERNAL_APPLY(210_01_1168,"非外部流程或互联流程，不允许配置应用节点"),
    PAAS_FLOW_BPM_SYNCHRONIZATION_TASK_NOT_FOUND_INSTANCE_RETRY_QUEUE(210_01_1169,"同步任务时未查询到自定义对象实例数据,进入重试队列"),
    PAAS_FLOW_BPM_TASK_RELATED_DATA_NOT_FOUND(210_01_1170, "任务关联数据不存在，请检查流程配置"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_LAYOUT_ERROR(210_01_1171, "请选择正确的流程布局"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_LAYOUT_DELETED(210_01_1172, "{0}节点对象流程布局已删除，请重新选择"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_RELATION_FIELD(210_01_1173, "{0}节点不存在编辑关联的字段，请重新选择"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_RELATION_FIELD_DELETED(210_01_1174, "{0}节点编辑的关联对象字段已删除或禁用，请检查"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_TYPE_RELATION_FIELD(210_01_1175, "{0}节点编辑的字段非查找关联类型，请重新选择"),
    PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_NULL(210_01_1176, "关联对象的字段没有值或已禁用"),
    PAAS_FLOW_BPM_ASSIGNEE_FUNCTION_ERROR(210_01_1177, "函数的节点处理人解析错误code"),
    PAAS_FLOW_BPM_VERIFIY_STAGE_ORDER_ERROR(210_01_1178, "阶段顺序配置有误，请检查重试"),
    PAAS_FLOW_BPM_VERIFIY_ASSIGNEE_AND_GROUPHANDLER_COEXIST(210_01_1179, "处理人和分组处理人不能同时存在，请修改 节点名称：{0}"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ERROR(210_01_1180, "分组处理人配置异常，请重试 节点名称：{0}"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ORDER_ERROR(210_01_1181, "分组处理人排序配置异常，请重试 节点名称：{0}"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_ERROR(210_01_1182, "分组处理人第{0}组条件异常，{1}，请重试 节点名称：{2}"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ASSIGNEE_ERROR(210_01_1183, "分组处理人第{0}组处理人配置异常，请重试 节点名称：{1}"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_NOT_DEFAULT_RULE(210_01_1184, "分组处理人没有默认条件，请重试 节点名称：{0}"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_ERROR_EXCEPTION(210_01_1185, "分组处理人第{0}组条件异常，请重试 节点名称：{1}"),
    PAAS_FLOW_BPM_VERIFIY_AVAILABLE_NODE_ERROR(210_01_1186, "流程定义中至少包含一个操作节点，请检查重试"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_CONTENT_ERROR_EXCEPTION(210_01_1187, "{0} 的第{1}个分组的分组条件 {2}"),

    //TODO cvs需要添加
    PAAS_FLOW_BPM_CURRENT_TASK_FORM_HAS_MUST_FIELD(301_010_122, "当前记录有必填项${0}$未填写，请【编辑】补充完整后，再进行当前任务;"),
    //采用引擎原有异常code值,防止影响rest接口,以下两个code值相同
    PAAS_FLOW_BPM_COMPLETE_RULE_NOT_MEET(301_010_121, "不满足完成条件，请确认条件满足后完成任务！"),
    PAAS_FLOW_BPM_START_RULE_NOT_MEET(301_010_403, "发起业务流程条件不满足，请确认条件满足后再执行发起业务流！"),
    PAAS_FLOW_BPM_LOOK_UP_NONSUPPORT_COUNT(301_010_122, "{0} 字段暂不支持关联类型的统计字段"),
    PAAS_FLOW_BPM_SEND_SMS_RECEIVE_NOT_NULL(301_010_123, "{0} 节点后动作发送短信，短信通知人员或相关对象变量或接收手机号不为空"),
    PAAS_FLOW_BPM_REMIND_TARGET_NOT_NULL(301_010_124, "{0} 节点超时提醒配置的提醒人不能为空"),
    PAAS_FLOW_BPM_PRE_SYNCHRONIZED_TO_CUSTOM_OBJECT_QUERY_ENGINE_DB_RETURNS_NULL(301_010_125, "预同步到自定义对象,查询引擎DB返回结果为空"),
    PAAS_FLOW_BPM_CUSTOM_BUTTON_DATA_STRUCTURE_IS_INCORRECT(301_010_126, "请尝试修改 {0} 节点的自定义按钮"),
    PAAS_FLOW_BPM_CHANGE_APPROVER_INCLUDE_OUTSIDERS_ERROR(301_010_127, "要更换的处理人中包含外部人员,请检查"),
    PAAS_FLOW_BPM_PLEASE_ENTER_APPROVAL_COMMENTS(301_010_128, "处理结果必填: agree|reject"),
    PAAS_FLOW_BPM_CHANGE_APPROVER_TYPE_ERROR(301_010_129, "非业务流程任务，不允许执行更换处理人操作"),
    PAAS_FLOW_BPM_COMPLETE_TASK_ERROR(301_010_130, "非业务流程任务，不允许执行当前操作"),
    PAAS_FLOW_BPM_CANCEL_INSTANCE_TYPE_ERROR(301_010_131, "非业务流程实例，不允许执行终止操作"),
    PAAS_FLOW_BPM_TRIGGER_HAS_REACHED_UPPER_LIMIT(301_010_132, "检测触发存在循环,自动终止触发业务流"),
    PAAS_FLOW_BPM_TASK_REPLACE_OWNER_EXPRESSION_ERROR(301_010_133, "替换处理人人员变量存在异常"),
    PAAS_FLOW_BPM_ONLY_SUPPORT_SYSTEM_USER_ERROR(301_010_134, "该操作只允许预制系统用户执行"),
    PAAS_FLOW_BPM_BRANCH_PARAMS_TYPE_EXCEPTION(301_010_135, "{0} 分支变量类型设置有误，请检查"),
    PAAS_FLOW_BPM_TASK_NOT_FOUND_LINK_APP_ID(301_010_136, "任务无linkAppId,不允许执行替换处理人操作，请检查"),
    ENGINE_TASK_COMPLETE_PARALLEL_ERROR(301_010_137, "多个并行任务正在完成,请尝试重新完成任务"),
    PAAS_FLOW_BPM_TASK_CANDIDATEIDS_IS_NULL_REJECT_COMPLETE_TASK(301_010_138, "任务待处理人为空,不允许完成任务"),
    PAAS_FLOW_BPM_PLEASES_MODIFY_TIME_OUT_EXECUTION(301_010_139, "请尝试修改 {0} 节点的超时策略"),
    PAAS_FLOW_BPM_PLEASES_MODIFY_TIME_OUT_EXECUTION_PARAMS_IS_NULL(301_010_140, "超时策略必填参数未设置，请检查"),
    PAAS_FLOW_BPM_NOT_SET_TASK_TIME_OUT_NOT_SET_REMIND_TIME(301_010_141, " {0} 未设置超时时间，不能设置超时提醒，请检查"),
    PAAS_FLOW_BPM_TASK_COMPLETED_CANNOT_MODIFY_DATA(301_010_142, "任务已经完成，不能修改数据"),
    PAAS_FLOW_BPM_TASK_NOT_CANDIDATE_CANNOT_MODIFY_DATA(301_010_143, "非任务处理人，不能修改数据"),
    PAAS_FLOW_BPM_TASK_ID_IS_ILLEGAL(301_010_144, "任务id不合法"),

    PAAS_FLOW_BPM_INSTANCE_NOT_CANCEL(301_010_145, "实例终止中或实例任务完成中，请重试"),
    PAAS_FLOW_BPM_AFTER_ACTION_IS_WAITING(301_010_146, "任务节点后动作正在执行中，执行完成后，流程将自动进入下一个节点。"),
    PAAS_FLOW_BPM_NOT_SUPPORT_TRANSFER_DEFINITION(301_010_147, "当前流程定义类型不支持转换为标准业务流。"),
    PAAS_FLOW_APPROVAL_CUSTOM_VARIABLE_UNDEFINE(301_010_148, "条件中的流程变量没有在变量管理列表中定义"),
    PAAS_FLOW_APPROVAL_CUSTOM_IS_EXISTS(301_010_149, "变量管理中不能存在重复的ApiName"),
    PAAS_FLOW_APPROVAL_CUSTOM_VARIABLES_DEFAULT_VALUE_ERROR(301_010_150, "变量表 {0} 默认值配置错误"),
    PAAS_FLOW_BPM_SEND_EMAIL_SENDER_IS_INVALID(301_010_151, "{0} 的发送邮件发送人已失效"),
    ENGINE_TASK_OPERATE_PROCESSING(301_010_152, "任务正在处理，请稍后重试。"),
    PAAS_FLOW_BPM_TASK_OPERATE_NOT_SUSPEND(301_010_153, "当前任务不能暂停，请刷新页面"),
    PAAS_FLOW_BPM_TASK_OPERATE_NOT_RESUME(301_010_154, "当前任务非暂停中，请刷新页面"),
    PAAS_FLOW_BPM_TASK_NOT_SUPPORTED_OPERATE(301_010_155, "暂不支持当前任务操作类型"),
    PAAS_FLOW_BPM_TASK_SUSPEND_NOT_COMPLETE(301_010_156, "暂停中的任务不能直接完成，请重试"),
    /**
     * 880 自定义元素
     */
    PAAS_FLOW_BPM_TASK_ELEMENT_IS_NULL(301_010_157,"自定义元素节点 {0} 属性 {1} 不能为空"),
    PAAS_FLOW_BPM_TASK_ELEMENT_CUSTOM_FALSE(301_010_158,"自定义元素节点 {0} 属性 {1} 必须为 true"),
    PAAS_FLOW_BPM_TASK_ELEMENT_NOT_EXISTS(301_010_159,"自定义元素节点 {0} 对应的元素 {1} 不可用，请确认"),
    PAAS_FLOW_BPM_TASK_ELEMENT_EXTENSION_IS_EMPTY(301_010_160,"自定义元素节点 {0} 扩展信息不能为空,请确认"),
    PAAS_FLOW_BPM_TASK_ELEMENT_CANDIDATE_NOT_MATCH(301_010_161,"自定义元素节点 {0} 是否配置处理人的值不一致，请确认；理论上 元素需要配置处理人时，这里 customCandidateConfig 需要是 true "),

    PAAS_FLOW_BPM_COMPLETE_TASK_OPINION_TOO_LONG(301_010_162, "任务意见过长，字数应小于500字，请重试"),
    PAAS_FLOW_BPM_TASK_UNABLE_ADD_TAG(301_010_163, "当前任务无法加签，请重试"),
    PAAS_FLOW_BPM_TASK_ADD_TAG_ARG_ERROR(301_010_164, "加签参数异常，请重试"),
    PAAS_FLOW_BPM_TASK_TAG_WAITING_NOT_COMPLETE(301_010_165, "加签中的任务不能直接完成，请重试"),
    PAAS_FLOW_BPM_TASK_DATA_MODIFIED_RETRY_ERROR(301_010_166, "当前编辑的数据已被他人修改，请关闭页面重试"),
    PAAS_FLOW_BPM_TASK_ELEMENT_IMPORT_OBJECT_DEF_ERROR(301_010_167,"自定义元素节点 {0} 引入元素配置错误，请删除原节点重新配置"),
    /**
     * 元数据数据异常
     */
    PAAS_FLOW_BPM_CUSTOM_DATA_NOT_FOUND(201_11_2008, "自定义对象数据已作废或已删除"),
    PAAS_FLOW_BPM_PACKAGE_DATA_NOT_FOUND(400_00_019, "预设数据已作废或已删除"),
    PAAS_FLOW_BPM_DESC_NOT_FOUND(301_11_1018, "对象不存在或已禁用"),
    CONTROLLED_WORKFLOW_CANNOT_BE_CHANGED(301_11_1019, "受管控的流程定义无法创建、编辑、删除、停用"),
    PAAS_FLOW_BPM_TAG_WAITING_NOT_CHANGE_APPROVER(301_11_1020, "加签中的任务不能更换处理人，请重试"),
    PAAS_FLOW_BPM_UPDATE_HISTORY_DEFINITION_ERROR(301_11_1021, "修改历史定义的结构不符合修改规范，请重试"),
    PAAS_FLOW_BPM_ACTIVITY_NOT_CHOOSE_LINK_APP(301_11_1022, "{0} 节点未选择互联应用"),
    PAAS_FLOW_BPM_TASK_IN_PROGRESS_CAN_ONLY_REMIND(301_11_1023, "进行中的任务才能催办，请重试"),
    PAAS_FLOW_BPM_TASK_REMIND_PERSONS_IS_NULL(301_11_1024, "催办人员是空，请重试"),
    PAAS_FLOW_BPM_ACTION_CANNOT_BE_NULL_AFTER_NODE_FIELD_UPDATE(301_11_1025, "{0}节点字段更新后动作配置错误,{1}不能为空"),
    PAAS_FLOW_BPM_ACTION_ONLY_UP_TO_5_LEVELS_CAN_BE_SELECTED_WHEN_UPDATING_ASSOCIATED_OBJECTS(301_11_1026, "{0}节点字段更新后动作配置错误,更新关联对象时最多只能选择5级"),
    THE_RELATEDENTITYID_DOES_NOT_MATCH_THE_ENTITYID_OF_THE_UPDATED_DATA(301_11_1027, "{0}节点字段更新后动作配置错误,更新关联对象时同一组操作entityId必须相同"),
    THE_OBJECT_TO_BE_UPDATED_SHOULD_BE_CONSISTENT_WITH_THE_RELATED_ENTITY_ID(301_11_1028, "{0}节点字段更新后动作配置错误,要更新的对象要和relatedEntityId保持一致"),
    RELATED_ENTITY_ID_OR_RELATED_OBJECT_ID_CANNOT_HAVE_A_VALUE(301_11_1029, "{0}节点字段更新后动作配置错误,更新当前对象时,relatedEntityId或relatedObjectId不能有值"),
    THE_NUMBER_OF_KEY_LEVELS_CANNOT_EXCEED_3(301_11_1030, "{0}节点字段更新后动作配置错误,更新关联对象时,key的级数不能超过2"),
    APPLICANT_WHEN_SYSTEM_USER_NOT_SUPPORT(301_11_1031, "流程发起人当是系统时不支持指定为{0}"),
    ;

    private int code;
    private String message;

    BPMBusinessExceptionCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static BPMBusinessExceptionCode valueOf(int errorCode) {
        BPMBusinessExceptionCode[] codes = BPMBusinessExceptionCode.values();
        for (BPMBusinessExceptionCode code : codes) {
            if (code.getCode() == errorCode) {
                return code;
            }
        }

        return null;
    }

    /**
     * 数据作废或者已删除的判断
     * @param errorCode
     * @return
     */
    public static boolean metadataNotFound(Object errorCode) {
        int intCode = Integer.valueOf(errorCode + "");
        return intCode == PAAS_FLOW_BPM_CUSTOM_DATA_NOT_FOUND.getCode() || intCode == PAAS_FLOW_BPM_PACKAGE_DATA_NOT_FOUND.getCode();
    }

    /**
     * 无权限的时候提示
     *
     * 40000106 客户
     * 40000025 联系人
     * 40000082 合同
     * 40000053 订单
     * 40000238 退货单
     * 40000030 线索
     * 40000262 市场活动
     * 40000284 商机
     * 40000210 开票
     * 40000193 退款
     * @param errorCode
     * @return
     */

    private final static List<Integer> noPermission = Lists.newArrayList(40000106, 40000025, 40000082, 40000053, 40000238, 40000030, 40000262, 40000284, 40000210, 40000193);

    public static boolean metadataNoPermission(Object errorCode) {
        int intCode = Integer.valueOf(errorCode + "");
        return noPermission.contains(intCode);
    }

    public static boolean metadataDescNotFound(int errorCode) {
        return errorCode == PAAS_FLOW_BPM_DESC_NOT_FOUND.getCode();
    }

    public static void main(String[] args) {
        System.out.println(BPMBusinessExceptionCode.metadataNotFound(201112008));
        System.out.println(BPMBusinessExceptionCode.metadataNotFound(40000019));
        System.out.println(BPMBusinessExceptionCode.metadataNotFound(400000019));
        System.out.println(BPMBusinessExceptionCode.metadataNoPermission(40000106));
        System.out.println(BPMBusinessExceptionCode.metadataDescNotFound(301111018));
    }

}
