package com.facishare.bpm.exception;


import com.facishare.bpm.i18n.BPMI18N;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVER_ERROR;

/**
 * BPM异常根
 *
 * <AUTHOR>
 * @date 16/8/19
 */
public class BPMRuntimeException extends RuntimeException {


    private int errorCode = PAAS_FLOW_BPM_SERVER_ERROR.getCode();

    public BPMRuntimeException() {
    }

    public BPMRuntimeException(Throwable throwable) {
        super(throwable);
    }

    public BPMRuntimeException(BPMBusinessExceptionCode code, Object... arg) {
        this(code.getCode(), BPMI18N.getI18NByName(code,arg));
    }

    public BPMRuntimeException(int errorCode, String message) {
        this(message, false);
        this.errorCode = errorCode;
    }

    private BPMRuntimeException(String message, boolean stackTrace) {
        super(message, null, true, stackTrace);
    }

    public int getErrorCode() {
        return errorCode;
    }
}
