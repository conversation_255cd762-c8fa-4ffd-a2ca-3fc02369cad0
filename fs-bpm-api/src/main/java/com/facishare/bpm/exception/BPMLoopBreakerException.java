package com.facishare.bpm.exception;

/**
 * Created by wansong on 17/3/4.
 */
public class BPML<PERSON>BreakerException extends BPMBusinessException {

    public BPMLoopBreakerException(BPMBusinessExceptionCode code, Object... args) {
        super(code, args);
    }

    public BPMLoopBreakerException(String message) {
        super(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TRIGGER_HAS_REACHED_UPPER_LIMIT.getCode(),message);
    }

    public BPMLoopBreakerException() {
        super(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TRIGGER_HAS_REACHED_UPPER_LIMIT.getCode(),BPMBusinessExceptionCode.PAAS_FLOW_BPM_TRIGGER_HAS_REACHED_UPPER_LIMIT.getMessage());
    }
}
