package com.facishare.bpm.i18n;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.utils.i18n.I18NUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Objects;
@Slf4j
public enum BPMI18N {

    PAAS_FLOW_BPM_PLEASE_REFRESH_THE_PAGE("paas.flow.bpm.please.refresh.the.page","当前访问的服务接口已下线，请尝试刷新页面","",""),
    BPM_TIME_UNIT_DAY("bpm_time_unit_day","天","",""),
    BPM_TIME_UNIT_MINUTE("bpm_time_unit_minute","天","",""),
    BPM_TIME_UNIT_HOUR("bpm_time_unit_hour","小时","",""),

    PAAS_FLOW_BPM_CONFIG_BUTTON_SAVE("paas.flow.bpm.config.button.save", "保存", "保存", "6.7_2019年05月17日14:53:20"),

    PAAS_FLOW_BPM_GET_ENTERPRISE_INFO_ERROR("paas.flow.bpm.get.enterprise.info.error", "获取企业信息失败", "", "665_20190605095328"),
    PAAS_FLOW_BPM_REMIND_EMAIL_FORMAT_ERROR("paas.flow.bpm.remind.email.format.error", "{0} 超时邮件提醒信息不完整", "", "665_20190605095328"),


    PAAS_FLOW_BPM_CUSTOM_DATA_NOT_FOUND("paas.flow.bpm.custom.data.not.found", "自定义对象数据已作废或已删除", "", "665_20190605095328"),
    PAAS_FLOW_BPM_PACKAGE_DATA_NOT_FOUND("paas.flow.bpm.package.data.not.found", "预设数据已作废或已删除", "", "665_20190605095328"),
    PAAS_FLOW_BPM_DESC_NOT_FOUND("paas.flow.bpm.desc.not.found", "对象不存在或已禁用", "", "665_20190605095328"),

    //=========================================================================

    PAAS_FLOW_BPM_CONFIG_PLEASE_UPGRADE_APP_TO_LATEST_VERSION("paas.flow.bpm.config.please.upgrade.app.to.latest.version", "请升级App到最新版本", "fs-bpm-switch-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_BUTTON_SELECT_CREATE("paas.flow.bpm.config.button.select.create", "选择或新建 {0}", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_BATCH_BUTTON_SELECT_CREATE("paas.flow.bpm.batch.config.button.select.create", "批量新建 {0}", "fs-bpm-task-form-button-config", "2020年06月05日16:22:57"),
    PAAS_FLOW_BPM_CONFIG_BUTTON_AGREE("paas.flow.bpm.config.button.agree", "同意", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_BUTTON_REJECT("paas.flow.bpm.config.button.reject", "不同意", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_BUTTON_UPDATE("paas.flow.bpm.config.button.update", "更新", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_BUTTON_COMPLETE("paas.flow.bpm.config.button.complete", "完成", "fs-bpm-task-form-button-config", "6.5_201901101006"),


    PAAS_FLOW_BPM_CONFIG_ACTION_CHANGEOWNER("paas.flow.bpm.config.action.changeowner", "变更负责人", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_RETURN("paas.flow.bpm.config.action.return", "退回公海", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_ADD_TEAMMEMBER("paas.flow.bpm.config.action.add.teammember", "添加团队成员", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_IN("paas.flow.bpm.config.action.sign.in", "签到", "MetadataServiceImpl.java", "7.9.5_202202091030"),
    PAAS_FLOW_BPM_CONFIG_ACTION_SIGN_OUT("paas.flow.bpm.config.action.sign.out", "签退", "MetadataServiceImpl.java", "7.9.5_202202091030"),
    PAAS_FLOW_BPM_CONFIG_ACTION_CLOSE("paas.flow.bpm.config.action.close", "无效", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_FOLLOWUP("paas.flow.bpm.config.action.followup", "跟进中", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_TRANSFORM("paas.flow.bpm.config.action.transform", "转换", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_CONFIRMRECEIVE("paas.flow.bpm.config.action.confirmreceive", "确认收货", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_CONFIG_ACTION_CONFIRMDELIVERY("paas.flow.bpm.config.action.confirmdelivery", "确认发货", "fs-bpm-task-form-button-config", "6.5_201901101006"),
    PAAS_FLOW_BPM_QUOTA_STANDARD_EDITION("paas.flow.bpm.quota.standard.edition", "您所在企业购买的{0}，“业务流程管理”为赠送使用，如果您想新建更多业务流程，请购买更高版本。", "fs-bpm-quota-msgs", "6.5_201901101151"),
    PAAS_FLOW_BPM_QUOTA_ENTERPRISE_EDITION("paas.flow.bpm.quota.enterprise.edition", "您所在企业目前支持{0}个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。", "fs-bpm-quota-msgs", "6.5_201901101151"),
    PAAS_FLOW_BPM_QUOTA_OTHER("paas.flow.bpm.quota.other", "当前版本只支持定义{0}个业务流程，请升级版本或购买资源扩展包。", "fs-bpm-quota-msgs", "6.5_201901101151"),

    //******************* ⬆ 以上都是配置文件中的中文  ⬆  以上数据已导入国际化平台*****************

    PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION("paas.flow.bpm.service.execute.exception", "{0}", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_RELATION_OBJECT_NOT_FOUND("paas.flow.bpm.verifiy.relation.object.not.found", "该流程关联的对象不存在，请确认", "BasicHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SCHEDULE_CONTENT_NOT_EMPTY("paas.flow.bpm.send.schedule.content.not.empty", "{0} 发送日程 内容 不能为空", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_NAME("paas.flow.bpm.instance.name", "流程实例名称", "WorkflowEngineSupportVariablesUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_UPDATE_FIELD("paas.flow.bpm.node.update.field", "{0} 节点 更新字段中 {1} ", "UpdateValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SALE_TYPE_NOT_EMPTY("paas.flow.bpm.send.sale.type.not.empty", "{0} 发送销售记录 类型不 能为空", "FeedSalesRecordValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_REMINDER_NOT_NULL("paas.flow.bpm.reminder.not.null", "提醒人员不能为空", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ACTIVITY_INPUT("paas.flow.bpm.activity.input", "连出", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SERVICE_PASS("paas.flow.bpm.service.pass", "服务通系列服务等", "AppResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_WHO_HAS_STOP_DEF("paas.flow.bpm.who_has_stop_def", "{0}由{1}在{2}将流程定义停用，请确认", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_CHOICE_OR_CREATE_OBJECT_NOT_FOUND("paas.flow.bpm.task.choice.or.create.object.not.found", "任务选择和创建的对象未找到", "BPMTaskServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINE_STOP_OR_DELETE("paas.flow.bpm.define.stop.or.delete", "{0}  (已停用或删除)", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_APPROVAL_TRIGGER("paas.flow.bpm.approval.trigger", "审批流", "TriggerSource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_EXPRESSION_ERROR("paas.flow.bpm.verifiy.after.remind.content.expression.error", "{0} 超时提醒内容中表达式存在异常", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTION_FORWARD("paas.flow.bpm.buttion.forward", "转发", "MoreOperationManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTON_REFRESH_HANDLER("paas.flow.bpm.button.refresh.handler", "重新解析处理人", "MoreOperationManagerImpl.java", "8.0.5_20220506"),

    PAAS_FLOW_BPM_VERIFIY_BRANCH_PARAMS("paas.flow.bpm.verifiy.branch.params", "{0} 分支{1} 参数中{2}", "ConditionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_CHOICE_SCOPE("paas.flow.bpm.verifiy.choice.scope", "请选择流程适用范围", "Workflow.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_BASE_INLET_OBJECT_MANY("paas.flow.bpm.verifiy.base.inlet.object.many", "入口对象出现了 {0} 个，请确认复制流程时入口对象没有发生变化", "BasicHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CONFIRM_DELIVERY("paas.flow.bpm.confirm.delivery", "确认发货", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SALE_COPY_RANGE_NOT_EMPTY("paas.flow.bpm.send.sale.copy.range.not.empty", "{0} 发送销售记录 抄送范围 不能为空", "FeedSalesRecordValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DATA_NO_PERMISSION("paas.flow.bpm.data.no.permission", "没有权限查看", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    @Deprecated//2019年01月10日15:12:47
            PAAS_FLOW_BPM_OBJECT_VARIABLES("paas.flow.bpm.object.variables", "对象相关变量", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_EMAIL_TARGET_IS_EMPTY("paas.flow.bpm.send_email_target_is_empty", "{0} 的发送邮件没有指定 [收件员工] 或 [收件邮箱]", "SendEmailValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CREATE_SCHEDULE("paas.flow.bpm.create_schedule", "创建日程", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_CRM_TITLE_NOT_EMPTY("paas.flow.bpm.send.crm.title.not.empty", "{0} 发送CRM提醒 标题 不能为空", "SendCRMMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_GROUP_DEPT_LEADER("paas.flow.bpm.group.dept.leader", "记录相关团队成员所在主部门负责人", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_DESCRIBE_NOT_FOUND("paas.flow.bpm.object.describe.not.found", "对象描述不存在", "MetadataUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AFTER_EXECUTION_FUNCTION("paas.flow.bpm.after.execution.function", "执行函数", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NOT_FOUND("paas.flow.bpm.verifiy.not.found", "{0} 不在阶段中", "NodeInStageHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NODE_BRANCH_OUT_LINE_ERROR("paas.flow.bpm.verifiy.node.branch.out.line.error", "{0} 节点分支{1} 连出有误，请确认", "ConditionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BOOLEAN_TYPE("paas.flow.bpm.boolean_type", "布尔类型", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_EXCEPTION("paas.flow.bpm.instance.exception", "流程实例异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AT_MOST("paas.flow.bpm.verifiy.at.most", "{0} 节点只能有{1} 条{2} 的线", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BPM_TRIGGER("paas.flow.bpm.bpm.trigger", "业务流程", "TriggerSource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NOT_FOUND_CONNECT_START_NODE("paas.flow.bpm.verifiy.not.found.connect.start.node", "流程配置错误 {0} 没有连接到开始节点，请确认", "VerifyInitHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NODE_ASSIGNEEID_NOT_SET("paas.flow.bpm.verifiy.node.assigneeId.not.set", "{0} 节点未设置处理人", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_FOUND_TASKTYPE("paas.flow.bpm.task.not.found.tasktype", "流程定义异常，任务节点缺少任务类型", "BPMTask.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTERNAL_MESSAGE("paas.flow.bpm.external.message", "外部通知", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_CHOICE_NEXT_ASSIGNEED("paas.flow.bpm.verifiy.choice.next.assigneed", "请指定下一节点处理人", "BPMTaskServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NATIONAL_PROVINCIAL_CITY_AND_DISTRICT("paas.flow.bpm.national.provincial.city.and.district", "国家、省、市、区", "TaskUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_MESSAGE_ERROR("paas.flow.bpm.node.message.error", "节点信息错误", "ActivityValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_PARAMETER_ANOMALY("paas.flow.bpm.parameter.anomaly", "参数异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TIMEOUT_WARN_VALIDATE("paas.flow.bpm.timeout_warn_validate", "超时提醒验证", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CANDIDATE_PARAMS_ERROR("paas.flow.bpm.candidate.params.error", "流程分支条件涉及到的参数值 {0} 不正确，目前是 {1} ，请确认", "MetaDataToWorkflowVariableUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TENANT_NOT_QUOTA("paas.flow.bpm.tenant.not.quota", "当前模块可用配额数不足，请联系纷享客服购买更高版本或增购资源包。", "BPMTenantServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CURRENT_TASK_FORM_HAS_MUST_FIELD("paas.flow.bpm.current_task_form_has_must_field", "当前记录有必填项$-$未填写，请【编辑】补充完整后，再进行当前任务;", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AT_LEAST("paas.flow.bpm.verifiy.at.least", "{0} 节点至少有{1}条{2}的线", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OUTER_MATCHED_FIELD_IS_ONLY_OPTION("paas.flow.bpm.outer.matched.field.is.only.option", "下游企业匹配的字段只支持单选", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_SET_BELONG_OBJECT("paas.flow.bpm.validate.not_set_belong_object", " {0} 未设置所属对象", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_EXTERNAL_FLOW_FIRST_ACTIVITY_MUST_HANDLER_ACTIVITY("paas.flow.bpm.validate.external_flow_first_activity_must_handler_activity", "外部流程第一个节点必须是业务节点或审批节点", "WorkflowJsonUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_ABSENCE_APPROVAL_RESULT("paas.flow.bpm.verifiy.absence.approval.result", "缺少审批结果", "TaskHandlerManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FLOW_HANDLE_ERROR("paas.flow.bpm.flow.handle.error", "流程处理异常", "ExecutableWorkflowExt.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_COMPLETED_TIME("paas.flow.bpm.task.completed.time", "任务完成时间", "WorkflowEngineSupportVariablesUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_FUNCTION_VARIABLE_TYPE_ERROR("paas.flow.bpm.validate.function_variable_type_error", "{0} 的执行函数变量{1} 的类型不支持", "CustomFunctionValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_UPDATE_NODE("paas.flow.bpm.update.node", "更新节点", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AUTOMATIC_NODE_MISS_EXECUTION_ITEM("paas.flow.bpm.automatic.node.miss.execution.item", "自动节点缺少执行项，请确认", "ExecutionTaskValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FORM_FORMAT_DATA_NULL("paas.flow.bpm.form.format.data.null", "表单数据为空", "MUpdateDataAndCompleteTask.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SUPPORT_NOT_FOUND("paas.flow.bpm.support.not.found", "扩展没找到", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_DATA_ERROR("paas.flow.bpm.node.data.error", "{0} 节点数据选择错误", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_ABSENCE_BUSS_NODE("paas.flow.bpm.verifiy.absence.buss.node", "缺少业务节点", "VerifyInitHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_WORKFLOW_NAME_DUPLLICATE("paas.flow.bpm.workflow.name.dupllicate", "流程名称已存在", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_SETTING_ACTION_CODE("paas.flow.bpm.task.not.setting.action.code", "没有设置ActionCode", "TaskHandlerManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_SEND_REMIND_CONTENT("paas.flow.bpm.node.send.remind.content", "{0} 节点 发送CRM提醒 内容中 {1} ", "SendCRMMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_END_NODE_SEND_REMIND_CONTENT("paas.flow.bpm.end.node.send.remind.content", "{0} 发送CRM提醒 内容中 {1} ", "SendCRMMessageValidateActionHandler.java", "7.0_202002071006"),

    PAAS_FLOW_BPM_TEMPLATE_DELETE_CHOOSE_OTHER("paas.flow.bpm.template.delete.choose.other", "该流程模板已删除，请选择别的模板", "WorkflowTemplateDaoImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINE_SERVICE_STOP("paas.flow.bpm.define.service.stop", "{0} 流程定义已被停用，请确认", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_ASSIGNOR_NOT_EMPTY("paas.flow.bpm.send.task.assignor.not.empty", "{0} 发送任务 分配人 为空", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_START_TO_NODE_NEED_APPROVAL_NODE("paas.flow.bpm.start.to.node.need.approval.node", "开始节点到{0} 之间必须有审批、会签或业务节点", "ActivityHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_EXISTS_MULTI_TRANSITION("paas.flow.bpm.validate.exists_multi_transition", "存在多条相同连线，请检查", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NO_ENTITYID("paas.flow.bpm.no.entityId", "tenantId={0} 没有对象={1} ", "BpmMetadataResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AFTER_SETTING_ERROR("paas.flow.bpm.after.setting.error", "后动作配置错误，不支持 {0}", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_RESET_BPM_FLOW_NAME("paas.flow.bpm.verifiy.reset.bpm.flow.name", "请重新设定流程名称", "WorkflowOutlineDaoImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_WORKFLOW_TRIGGER("paas.flow.bpm.workflow.trigger", "工作流", "TriggerSource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_EMAIL_SENDER_IS_EMPTY("paas.flow.bpm.send_email_sender_is_empty", "{0} 的发送邮件发送人不能为空", "SendEmailValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_EMAIL_SENDER_IS_INVALID("paas.flow.bpm.send_email_sender_is_invalid", "{0} 的发送邮件发送人已失效", "SendEmailValidateActionHandler.java", "8.7_20230703"),

    PAAS_FLOW_BPM_SEND_SCHEDULE_REMIND_SETTING_NOT_EMPTY("paas.flow.bpm.send.schedule.remind.setting.not.empty", "{0} 发送日程 提醒配置 不能为空", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SALE_COPY_RANGE("paas.flow.bpm.send.sale.copy.range", "{0} 发送销售记录 抄送范围", "FeedSalesRecordValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_CHOICE_BPM_FLOW("paas.flow.bpm.verifiy.choice.bpm.flow", "请选择业务流程", "BPMTaskServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_LOOKUP_OBJECT_NOT_EXIST("paas.flow.bpm.lookup.object.not.exist", "对象关联关系已解除或已禁用，流程无法继续进行，请联系系统管理员", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTERNAL_CONTENT("paas.flow.bpm.external.content", "外部通知 内容中 {0}", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TIMEOUT_WARN_PERSON("paas.flow.bpm.timeout_warn_person", "{0} 超时提醒提醒人员", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_NOT_FOUND("paas.flow.bpm.instance.not.found", "流程实例不存在", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_STATUS_PAAS("paas.flow.bpm.task.status.paas", "任务已经被处理", "Task.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BPM_FLOW_CONDITION_NOT_SUPPORT("paas.flow.bpm.bpm.flow.condition.not.support", "业务流程分支类型: {0} 不支持操作符: {1}", "ConditionComparatorHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CANDIDATE_GET_ASSIGNEED_ERROR("paas.flow.bpm.candidate.get.assigneed.error", "解析人员失败，不支持{0}", "CandidateManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ACTIVITY_TO_ACTIVITY_CONDITION_ERROR("paas.flow.bpm.activity_to_activity_condition_error", "{0} 到 {1} 节点的 {2} 条件{3}，请联系流程配置人员", "ConditionComparatorHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_FUNCTION_APINAME_IS_BLANK("paas.flow.bpm.validate.function_apiname_is_blank", "{0} 的执行函数配置apiName不能为空", "CustomFunctionValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_PERSON_PROP_CONFIG_ERROR("paas.flow.bpm.verifiy.person.prop.config.error", "{0} 人员对象属性变量配置不正确", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_HANDLER("paas.flow.bpm.task.handler", "节点执行人", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NODE_BRANCH_IN_LINE_ERROR("paas.flow.bpm.verifiy.node.branch.in.line.error", "{0} 节点分支{1} 连入节点有误，请确认", "ConditionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SCHEDULE_COPY_RANGE_NOT_EMPTY("paas.flow.bpm.send.schedule.copy.range.not.empty", "{0} 发送日程 抄送范围 不能为空", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_HAS_STOP("paas.flow.bpm.validate.has_stop", "[{0}]业务流程定义已停用", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_APPLICATION_NODE("paas.flow.bpm.application.node", "应用节点", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_COPY_RANGE("paas.flow.bpm.send.task.copy.range", "{0} 发送任务 抄送范围 ", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_UPDATE_FIELD_VALUE_IS_BLANK("paas.flow.bpm.validate.update_field_value_is_blank", "{0} 的更新字段的值不能为空", "UpdateValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_SET_OPERATE_TYPE("paas.flow.bpm.validate.not_set_operate_type", " {0} 未设置操作类型", "ExecutionTypeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NO_DEFAULT_TRANSITION("paas.flow.bpm.validate.no_default_transition", " {0} 需要配置默认分支", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_COMPLETE_NOTICE("paas.flow.bpm.task.complete.notice", "任务完成通知", "UserTaskExt.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_WHO_HAS_DELETED_DEF("paas.flow.bpm.who_has_deleted_def", "{0}由{1}在{2}将流程定义删除，请确认", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINE_NOT_FOUND_START_NODE("paas.flow.bpm.define.not.found.start.node", "没有开始节点", "PreNodesUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_CHOICE_DATA("paas.flow.bpm.task.not.choice.data", "没有选择关联数据，不能完成任务", "TaskHandlerManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_CONFIG_RELATEDENTITYID("paas.flow.bpm.validate.not_config.relatedEntityId", " {0} 未设置relatedEntityId", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_FIELD_DISABLE("paas.flow.bpm.object.field.disable", "{0} 的{1} 已被禁用，请确认", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AGREE("paas.flow.bpm.agree", "同意", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VIEW_ENTIRE("paas.flow.bpm.view_entire", "查看完整流程", "MoreOperationManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_CRM_RECIPIENT("paas.flow.bpm.send.crm.recipient", "{0} 发送CRM提醒 接收人", "SendCRMMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_REMIND_NOT_EMPTY("paas.flow.bpm.send.task.remind.not.empty", "{0} 发送任务 提醒配置 不能为空", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_SEND_TASK_CONTENT("paas.flow.bpm.node.send.task.content", "{0} 节点 发送任务 内容中 {1} ", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VIEW_BPM_INSTANCE_LOG("paas.flow.bpm.view.bpm.instance.log", "流程日志", "MoreOperationManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NODE_COMPLETE_CONDITION("paas.flow.bpm.verifiy.node.complete.condition", "{0} 节点完成条件中{1}", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NOT_SUPPORT_VARIABLE("paas.flow.bpm.not_support_variable", "业务流程不支变量 {0} ", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_LOOKUP_OBJECT("paas.flow.bpm.lookup.object", "关联对象", "UserTaskAddRelatedObjectValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FIELD_LABEL_NOT_CORRECT("paas.flow.bpm.field_label_not_correct", "{0} 的对象{1} 的字段{2} 的label匹配失败", "BpmMetadataResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SYSTEM_ERROR("paas.flow.bpm.system.error", "系统异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_TRIGGER_CONDITION("paas.flow.bpm.verifiy.trigger.condition", "流程发起条件中 {0}", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_EXTERNAL_CONTENT_IS_BLANK("paas.flow.bpm.validate.external_content_is_blank", "{0} 的外部通知内容不能为空", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SALE_CONTENT_NOT_EMPTY("paas.flow.bpm.send.sale.content.not.empty", "{0} 发送销售记录内容不能为空", "FeedSalesRecordValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CREATE_SALES_RECORD("paas.flow.bpm.create.sales_record", "创建销售记录", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTERNAL_ROLE_NOT_NULL("paas.flow.bpm.external.role.not.null", "外部角色不能为空", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DRAFT_ALREADY_EXIST("paas.flow.bpm.draft.already.exist", "当前定义草稿已存在，请编辑原草稿", "BPMWorkflowDraftServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ENTITY_FIELD_DELETE_DISABLE("paas.flow.bpm.entity.field.delete.disable", "{0} 的字段{1} 已经删除或停用，请确认", "ValidateRefServiceManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_DATA_REMOVE_INSTANCE_CANCEL("paas.flow.bpm.object.data.remove.instance.cancel", "由于 {0} 数据作废，系统自动终止当前流程", "BPMConstants.java", "6.5_201901101006"),

    @Deprecated//2019年01月10日15:12:55
            PAAS_FLOW_BPM_WORKFLOW_ENGINE_VARIABLES("paas.flow.bpm.workflow.engine.variables", "流程引擎相关变量", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_GATEWAY_NEED_APPEAR_IN_PAIRS("paas.flow.bpm.validate.gateway_need_appear_in_pairs", "网关节点需要成对出现", "ParallelHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_RELATION_OBJECT_DELETE("paas.flow.bpm.task.relation.object.delete", "请检查任务相关对象是否被删除、作废或无权限，无法完成任务", "InstanceVariableManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TRIGGER_BPM("paas.flow.bpm.trigger_bpm", "触发业务流程", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_REFERENCE_DESC_NOT_FOUND("paas.flow.bpm.task.reference.desc.not.found", "任务涉及的业务对象描述已删除", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FLOW_VARIABLE_NOT_SUPPORT("paas.flow.bpm.flow.variable.not.support", "流程变量不支持{0}", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_STAGE_NOT_FOUND_NODE("paas.flow.bpm.verifiy.stage.not.found.node", "{0} 阶段中没有节点，请确认", "NodeInStageHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_RELATED_OBJECT_EMAIL_CONFIG_ERROR("paas.flow.bpm.validate.related_object_email_config_error", " {0} 相关对象的邮件字段配置中 {1} ", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TEMPLETE_NAME_NOT_NULL("paas.flow.bpm.templete.name.not.null", "{0} 模板名称不能为空", "SendEmailValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_DATA_RELATED_OR_VARIABLE_CONFIG_ERROR("paas.flow.bpm.validate.data_related_or_variable_config_error", " {0} 数据相关或流程变量配置有误", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_MULTIPLAYER_APPROVAL("paas.flow.bpm.multiplayer.approval", "多人审批", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_HAS_NO_HANDLER("paas.flow.bpm.has.no.handler", "未解析到处理人", "BPMTaskServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTON_IGNORE("paas.flow.bpm.button.ignore", "忽略", "AfterActionExecution.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_QUOTA_ERROR("paas.flow.bpm.quota.error", "配额异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ASSIGNEEID_DEPT_LEADER("paas.flow.bpm.assigneeId.dept.leader", "节点处理人所属主部门负责人", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SOME_DATA_DEAL_ERROR("paas.flow.bpm.some_data_deal_error", "部分数据处理失败", "BpmMetadataResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_REFER_OBJECT_NOT_FOUND("paas.flow.bpm.task.refer.object.not.found", "任务业务对象无法正常获取", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_OBJECT_NOT_SAME_AS_FUNCTION("paas.flow.bpm.node.object.not.same.as.function", "节点对象 与 执行函数配置的对象不致，请确认", "CustomFunctionValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_LOOKUP_OBJECT_ALREADY_DELETE("paas.flow.bpm.lookup.object.already.delete", "{0} 节点关联的对象已被禁用或删除，请确认", "UserTaskAddRelatedObjectValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_STOP_CALL_CRM_SYSTEM("paas.flow.bpm.instance.stop.call.crm.system", "当前流程已被终止，请联系管理员确认", "Task.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_NOT_EXIST("paas.flow.bpm.node.not.exist", "节点不存在", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTERNAL_REMINDER("paas.flow.bpm.external.reminder", "{0}外部通知提醒人员", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_DELETE_DISABLE("paas.flow.bpm.object.delete.disable", "{0} 对象已被删除或禁用，请确认", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CREATE_RELATED_OBJECT("paas.flow.bpm.create.related.object", "选择和新建从对象", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINE_SERVICE_DELETE("paas.flow.bpm.define.service.delete", "{0} 流程定义已被删除，请确认", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DISAGREE("paas.flow.bpm.disagree", "不同意", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTENSION_IS_NULL("paas.flow.bpm.extension.is.null", "扩展信息不能为空", "WorkflowService.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_FOUND_INSTANCE_ID("paas.flow.bpm.task.not.found.instance.id", "任务没找到instanceId: {0} activityId: {1}", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJEXT_NOT_EMPTY("paas.flow.bpm.objext.not.empty", "对象为空", "SpecialResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AFTER_SERVER("paas.flow.bpm.after.server", "后动作服务", "AfterResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_PARAM_NOT_NULL("paas.flow.bpm.instance.param.not.null", "流程实例参数不能为空", "WorkflowService.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINITION_CONVERT_SUCCESS("paas.flow.bpm.definition_convert_success", "定义转换成功", "TemplateResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_MAIL("paas.flow.bpm.send.mail", "发送邮件", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_SET_BELONG_OBJECT_NAME("paas.flow.bpm.validate.not_set_belong_object_name", " {0} 未设置所属对象名称", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_CONTENT_NOT_EMPTY("paas.flow.bpm.send.task.content.not.empty", "{0} 发送任务内容不能为空", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_REQUIRED_FILED_ERROR_MSG2("paas.flow.bpm.required.filed.error.msg2", "没有填写必填信息:-不能完成任务!如果看不到选项，请确认是否有相关权限*;", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_POOL_IS_EMPTY("paas.flow.bpm.validate.pool_is_empty", "泳道不能为空", "StageAndPoolHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_DEFINE_LOOP_ERROR("paas.flow.bpm.verifiy.define.loop.error", "存在闭环流程，请修改流程图", "CycleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_UPDATE_FIELD_NAME_IS_BLANK("paas.flow.bpm.validate.update_field_name_is_blank", "{0} 的更新字段name不能为空", "UpdateValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CREATE_DETAIL_TASK("paas.flow.bpm.create.detail.task", "新建从对象节点", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_OBJECT_EMAIL_FIELD_EXPRESSION_CONFIG_ERROR("paas.flow.bpm.verifiy.object.email.field.expression.config.error", "{0} 对象属性邮件类型字段变量配置不正确", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_START_EXCEPTION("paas.flow.bpm.start.exception", "流程启动异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINITION_ERROR("paas.flow.bpm.definition.error", "流程定义异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_COMPLETE_OR_CANCEL("paas.flow.bpm.instance.complete.or.cancel", "该流程已完成或已取消", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_INLET_OBJECT_DISABLE_OR_DELETE("paas.flow.bpm.verifiy.inlet.object.disable.or.delete", "入口对象已被禁用或被删除，请确认", "ProcessDraftActionImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SELECT_WHICH_BPM("paas.flow.bpm.select_which_bpm", "{0} 的触发业务流程，请选择触发哪个业务流程", "TriggerBpmValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_CRM_CONTENTNOT_EMPTY("paas.flow.bpm.send.crm.contentnot.empty", "{0} 发送CRM提醒 内容 不能为空", "SendCRMMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SYSTEM_ERROR_CONTACT_SERVICER("paas.flow.bpm.system.error.contact.servicer", "系统异常，请联系客服人员", "BPMExceptionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_SEND_REMIND_TITLE("paas.flow.bpm.node.send.remind.title", "{0} 节点 发送CRM提醒 标题中 {1} ", "SendCRMMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_END_NODE_SEND_REMIND_TITLE("paas.flow.bpm.end_node.send.remind.title", "{0} 发送CRM提醒 标题中 {1} ", "SendCRMMessageValidateActionHandler.java", "7.0_202002071006"),

    PAAS_FLOW_BPM_PAAS_EXECUTE_IGNORE_SUCCESS("paas.flow.bpm.paas.execute.ignore.success", "执行忽略成功", "PaasWorkflowServiceProxy.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_CHANGE_OWNER_EXPRESSION_ERROR("paas.flow.bpm.task.change.owner.expression.error", "更换处理人人员变量存在异常", "BPMTaskServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_REPLACE_OWNER_EXPRESSION_ERROR("paas.flow.bpm.task.replace.owner.expression.error", "替换处理人人员变量存在异常", "BPMTaskServiceImpl.java", "7.5.0_2021年01月11日10:21:25"),

    PAAS_FLOW_BPM_NODE_FIELD_IS_NOT_ACTIVE("paas.flow.bpm.node.field.is.not.active", "{0} 节点 {1} ({2} )涉及到的字段{3} 已被禁用，请开启后重试", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_PREPARATIVE_DATA_REMOVE_OR_DELETE("paas.flow.bpm.preparative.data.remove.or.delete", "预设数据已作废或已删除", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_NAME_REQUIRED("paas.flow.bpm.instance.name.required", "请填写流程名称", "WorkflowResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CHANGE_OWNER("paas.flow.bpm.change.owner", "更换处理人", "MoreOperationManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_AUTH_COMPLETE("paas.flow.bpm.task.not.auth.complete", "您无执行权限", "Task.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_TRANSITION_CONDITION_ERROR("paas.flow.bpm.validate.transition_condition_error", "线条表达式异常", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_METADATA_DATA_NOT_FOUND("paas.flow.bpm.metadata.data.not.found", "数据不存在", "TaskHandlerManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_ID_NOT_FOUND("paas.flow.bpm.instance.id.not.found", "流程实例id不能为空", "ProcessInstanceActionImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_STRING_TYPE("paas.flow.bpm.verifiy.string.type", "字符串类型", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_STATUS_PAAS_BY_USER("paas.flow.bpm.task.status.paas.by.user", "您已经处理过此任务", "Task.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_START_TIME("paas.flow.bpm.instance.start.time", "流程实例开始时间", "WorkflowEngineSupportVariablesUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TIPS_EXPRESSION_ERROR("paas.flow.bpm.tips.expression.error", "表达式存在异常", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_SET_BUSINESS_RECORD("paas.flow.bpm.validate.not_set_business_record", " {0} 未设置业务记录", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OUT_TENANT_MATCHED_FIELD_REQUIRED("paas.flow.bpm.out.tenant.matched.field.required", "请选择下游企业匹配的字段", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DRAFT_STOP_OR_DELETE("paas.flow.bpm.draft.stop.or.delete", "{0}  (已停用或删除)", "BPMWorkflowDraftServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ASSIGNEE_ID("paas.flow.bpm.assignee.id", "节点处理人", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINE_NOT_FOUND("paas.flow.bpm.define.not.found", "流程定义不存在", "ProcessDefinitionActionImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_GROUP_LEADER("paas.flow.bpm.group.leader", "记录相关团队成员上级", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_LOOKUP_OR_SLAVE_FIELD_NOT_EXIST("paas.flow.bpm.node.lookup.or.slave.field.not.exist", "{0} 节点 关联对象或从对象({1} )描述中已将({2} )的引用字段进行移除或修改，请确认并重新选择{3} 节点的配置信息", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_GET_TASK_ERROR("paas.flow.bpm.task.get.task.error", "获取任务信息失败", "BPMTask.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ASSIGNEE_LEADER("paas.flow.bpm.assignee.leader", "节点处理人上级", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_FIELD_NOT_CORRECT("paas.flow.bpm.object_field_not_correct", "{0} 的对象{1} 的字段{2} 匹配失败", "BpmMetadataResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_WORKFLOW_TASK_EXECUTE_EXCEPTION("paas.flow.bpm.workflow.task.execute.exception", "任务执行异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_TASK_MISMATCH("paas.flow.bpm.verifiy.task.mismatch", "当前节点对象和入口对象不一致", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CHOOSE_CREATE_DETAIL_OBJECT("paas.flow.bpm.choose.create.detail.object", "选择和新建从对象", "ActivityValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NOT_SUPPORT_OPERATE("paas.flow.bpm.not_support_operate", "{0}不支持操作符{1}", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_APPROVAL_CUSTOM_VARIABLE_UNDEFINE("paas.flow.approval.custom.variable_undefine", "条件中的流程变量没有在变量管理列表中定义","RuleHandler.java", "2023年07月18日11:24:18"),

    PAAS_FLOW_BPM_HAS_NO_TASK_AND_LAND_PAGE("paas.flow.bpm.has.no.task.and.land.page", "实例信息:当实例完成时，没有当前任务和落地页面", "WorkflowInstanceVO.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_PARALLEL_GATEWAY("paas.flow.bpm.parallel_gateway", "并行网关", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_REJECT_APPROVE("paas.flow.bpm.reject_approve", "驳回了审批", "MTaskLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SERVER_ERROR("paas.flow.bpm.server.error", "服务异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SCHEDULE_COPY_RANGE("paas.flow.bpm.send.schedule.copy.range", "{0} 发送日程 抄送范围 不能为空", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_STAGE_GET_INFO_ERROR("paas.flow.bpm.verifiy.stage.get.info.error", "阶段信息获取异常", "NodeInStageHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_ASSIGNEE_PARAM_ERROR("paas.flow.bpm.task.assignee.param.error", "任务指定人参数不正确", "WorkflowService.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_METADATA_OBJECT_DESCRIBE_NOT_FOUND_OR_DELETE("paas.flow.bpm.metadata.object.describe.not.found.or.delete", "自定义对象描述未找到或已删除", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_APPLICANT_ID("paas.flow.bpm.applicant.id", "流程发起人", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_FUNCTION_OBJECT_IS_BLANK("paas.flow.bpm.validate.function_object_is_blank", "{0} 的执行函数的对象类型不能为空", "CustomFunctionValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_PARAMS_ERROR("paas.flow.bpm.task.params.error", "处理任务请求参数不正确，请联系技术人员", "TaskHandlerManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NAME("paas.flow.bpm.task.name", "任务名称", "WorkflowEngineSupportVariablesUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_SEND_SCHEDULE_CONTENT("paas.flow.bpm.node.send.schedule.content", "{0} 节点 发送日程 内容中 {1} ", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_METADATA_EXCEPTION("paas.flow.bpm.metadata.exception", "数据处理异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_NOT_EXIST("paas.flow.bpm.object.not.exist", "对象不存在或已禁用", "paas.flow.bpm", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTON_RETRY("paas.flow.bpm.button.retry", "重试", "AfterActionExecution.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_VARIABLE_ERROR("paas.flow.bpm.object.variable.error", "对象变量不正确，请确认", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_FORMAT_ERROR("paas.flow.bpm.verifiy.format.error", "{0} 表单格式不正确", "FormHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TRIGGER_BY_PERSON("paas.flow.bpm.trigger.by.person", "手动触发", "TriggerSource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OPERATE_ACTIVITY("paas.flow.bpm.operate_activity", "操作节点", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SELECT_TRIGGER_OBJECT("paas.flow.bpm.select_trigger_object", "{0} 的触发业务流程请选择发起对象", "TriggerBpmValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_METADATA_OBJECT_DATA_NOT_FOUND_OR_DELETE("paas.flow.bpm.metadata.object.data.not.found.or.delete", "自定义对象数据已作废或已删除", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_APPROVAL_PASSED("paas.flow.bpm.approval.passed", "同意了审批", "MTaskLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AFTER_UPDATE("paas.flow.bpm.after.update", "后动作更新", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OWNER_LEADER("paas.flow.bpm.owner.leader", "记录负责人上级", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TRIGGER_FUNCTION_VARIABLE("paas.flow.bpm.trigger_function_variable", "{0}  触发函数 配置中参数{1} 的值 {2} ", "CustomFunctionValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TERMINAL_ELEVATION_HINT("paas.flow.bpm.terminal.elevation.hint", "终端强升提示", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_GET_PATTERN_ERROR("paas.flow.bpm.get.pattern.error", "获取模板异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSANCE_NOT_AUTH_CANCEL("paas.flow.bpm.insance.not.auth.cancel", "您无权取消该流程", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_CONDITION_NOT_FOUND_VARIABLE("paas.flow.bpm.verifiy.condition.not.found.variable", "{0} 分支的{1} 条件不在变量中", "ConditionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_CONDITION_VARIABLE_CONTAINS("paas.flow.bpm.verifiy.condition.variable.contains", "{0} 分支{1}变量中 {2}", "ConditionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_SEND_FEED_CONTENT("paas.flow.bpm.node.send.feed.content", "{0} 节点 发送销售 记录内容中 {1} ", "FeedSalesRecordValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_CRM_RECIPIENT_NOT_EMPTY("paas.flow.bpm.send.crm.recipient.not.empty", "{0} 发送CRM提醒 接收人 不能为空", "SendCRMMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_CONFIG_EXECUTIONTYPE("paas.flow.bpm.validate.not_config_executionType", " {0} 未设置executionType", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SUCCESSFUL_RE_IMPLEMENTATION("paas.flow.bpm.successful.re.implementation", "重新执行成功", "PaasWorkflowServiceProxy.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_NULL("paas.flow.bpm.object.null", "对象不能为空", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_LOOKUP_OBJECT_NOT_FOUND("paas.flow.bpm.node.lookup.object.not.found", "节点对象的lookup对象不存在", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_COMMON_FIELD("paas.flow.bpm.common.field", "普通字段", "TaskFormToMetaDataUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BASIC_INFORMATION("paas.flow.bpm.basic.information", "基本信息", "TaskFormToMetaDataUtil.java", "770_2020年01月17日18:05:26"),

    PAAS_FLOW_BPM_AFTER_ERROR("paas.flow.bpm.after.error", "后动作异常，请管理员查看任务详情进行处理", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INVOICE_HAS_OPEN("paas.flow.bpm.invoice.has.open", "发货单已开启， {0} 操作不可使用，请修改流程配置", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_FUNCTION_CONFIG_ERROR("paas.flow.bpm.validate.function_config_error", "{0} 的执行函数配置不正确", "CustomFunctionValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINITION_TRANSFER_ERROR("paas.flow.bpm.definition.transfer.error", "流程定义转换失败", "TemplateResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FINISHED_TASK("paas.flow.bpm.finished.task", "完成了任务", "MTaskLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NUMBER_TYPE("paas.flow.bpm.number.type", "数字类型", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CRM_REMIND_CLEANER_INTERFACE("paas.flow.bpm.crm.remind.cleaner.interface", "消息消数接口", "CrmRemindResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_AFTER_SETTING("paas.flow.bpm.node.after.setting", "{0} 节点 后动作配置的 {1} ", "TriggerBpmValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_RELATION_DESCRIBE_DELETE("paas.flow.bpm.task.relation.describe.delete", "任务关联的对象描述已被删除，请确认", "BPMTaskServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEPLOY_EXCEPTION("paas.flow.bpm.deploy.exception", "流程发布异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NOT_SET_ALL_APPROVE_TYPE("paas.flow.bpm.validate.not_set_all_approve_type", " {0} 未设置会签类型", "ExecutionTypeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_CHECK_OBJECT_INFO("paas.flow.bpm.task.check.object.info", "{0} 请检查任务对象信息", "ExtensionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_COUNTERSIGN("paas.flow.bpm.countersign", "会签", "MGetWorkflowInstanceLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DRAFT_ERROR("paas.flow.bpm.draft.error", "流程草稿异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DRAFT_QUOTA_ERROR("paas.flow.bpm.draft.quota.error", "草稿配额数量已达上限:草稿配额为定义配额的两倍", "BPMWorkflowDraftServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_CRM("paas.flow.bpm.send.crm", "发送企信提醒", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_REPEAT_TRIGGER("paas.flow.bpm.repeat_trigger", "[{0} ]业务流程不允许重复发起", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTON_SAVE("paas.flow.bpm.button.save", "保存", "TaskFormToMetaDataUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_ASSIGNOR("paas.flow.bpm.send.task.assignor", "{0} 发送任务 分配人", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_UPDATE_FORM_IS_BLANK("paas.flow.bpm.validate.update_form_is_blank", "{0} 的更新没有配置要更新的字段", "UpdateValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NOT_SUPPORTED("paas.flow.bpm.verifiy.not.supported", "{0} 不支持  {1} 类型", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DRAFT_NOT_FOUND("paas.flow.bpm.draft.not.found", "草稿已删除或不存在", "BPMWorkflowDraftServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_DEFINE_NOT_FOUND("paas.flow.bpm.node.define.not.found", "节点定义不存在，请确认参数的正确性", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_RIGHT_CONFIG_ERROR("paas.flow.bpm.verifiy.right.config.error", "右侧配置错误", "ConditionComparatorHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ACTIVITY_OUTPUT("paas.flow.bpm.activity.output", "连入", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_THE_WAITING_TIME_SET_ERROR("paas.flow.bpm.validate.the_waiting_time_set_error", " {0} 允许停留时长设置有误请检查", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_OBJECT_ERROR("paas.flow.bpm.node.object.error", "节点对象异常{0}", "InstanceVariableManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_STAGE_NAME_ISNULL("paas.flow.bpm.verifiy.stage.name.isnull", "阶段名称不能为空", "StageAndPoolHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_HANDLER_LEADER("paas.flow.bpm.task.handler.leader", "节点执行人上级", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ENTRY_TYPE_REQUIRED("paas.flow.bpm.entry.type.required", "请选择入口对象类型", "WorkflowResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ENTRY_NAME_REQUIRED("paas.flow.bpm.entry.name.required", "请选择入口对象名称", "WorkflowResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_TITLE_NOT_EMPTY("paas.flow.bpm.send.task.title.not.empty", "{0} 发送任务 标题 不能为空", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_TASK_TIME_NOT_EMPTY("paas.flow.bpm.send.task.time.not.empty", "{0} 发送任务 完成时间 不能为空", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_STOP_EMPLOYEE("paas.flow.bpm.stop.employee", "停用用户", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_PARAMETER_INCORRECT("paas.flow.bpm.parameter.incorrect", "参数不正确，请联系纷享客服", "WorkflowResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_NODE_NON_EXIST_STAGE("paas.flow.bpm.verifiy.node.non.exist.stage", "节点应该在阶段中", "NodeInStageHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TENANT_ID_OR_ENTITY_ID_NOT_NULL("paas.flow.bpm.tenant.id.or.entity.id.not.null", "企业Id或者对象Id为空", "SpecialResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CREATE_AND_CHOICE_RELATION_OBJECT("paas.flow.bpm.create.and.choice.relation.object", "选择和新建关联对象", "ActivityValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_TITILE_EXPRESSION_ERROR("paas.flow.bpm.verifiy.after.remind.titile.expression.error", "{0} 超时提醒标题中表达式存在异常", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_EMAIL_TEMPLATE_IS_EMPTY("paas.flow.bpm.send_email_template_is_empty", "{0} 的发送邮件模板不能为空", "SendEmailValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_ERROR("paas.flow.bpm.verifiy.after.remind.content.remind.time.error", "{0} 超时提醒提醒时间设置超出允许停留时间，请检查", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_FIELD_STOP_OR_DELETE("paas.flow.bpm.verifiy.after.remind.content.remind.time.field.stop.or.delete", "{0} 超时提醒 提醒时间设置的对象或字段已停用或删除，请检查", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CONFIRM_RECEIVE("paas.flow.bpm.confirm.receive", "确认收货", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AREA_LOCATION("paas.flow.bpm.area_location", "地区定位", "TaskFormToMetaDataUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_LOOP_MONITOR_ERROR("paas.flow.bpm.verifiy.loop.monitor.error", "回路监测异常，请检查流程图", "CycleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NODE_SEND_TASK_TITLE("paas.flow.bpm.node.send.task.title", "{0} 节点 发送任务 标题中 {1} ", "FeedTaskValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_ISNULL("paas.flow.bpm.verifiy.after.remind.content.isnull", "{0} 超时提醒内容不能为空", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTON_CANCEL_BPM_FLOW("paas.flow.bpm.button.cancel.bpm.flow", "终止", "MoreOperationManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OBJECT_FIELD_DELETE("paas.flow.bpm.object.field.delete", "{0} 的{1} 已被删除，请确认", "ValidateVariableAndContentUtils.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_FOUND("paas.flow.bpm.task.not.found", "任务不存在", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_STOP_CRM_GROUP("paas.flow.bpm.stop.crm.group", "停用组", "BPMConstants.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_BUTTON_SAVE_AND_COMPLETE("paas.flow.bpm.button.save_and_complete", "保存并完成", "TaskFormToMetaDataUtil.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTERNAL_TITLE("paas.flow.bpm.external.title", "外部通知 标题中 {0}", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_EXPRESSION_NOT_FOUND_VARIABLE("paas.flow.bpm.verifiy.expression.not.found.variable", "定义中表达式未添加到变量定义中", "FormHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_END("paas.flow.bpm.instance.end", "流程结束", "MTaskLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_PERMISSION_ERROR("paas.flow.bpm.permission.error", "权限异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_TASK_NOT_FOUND_ASSIGNEED("paas.flow.bpm.task.not.found.assigneed", "{0} 没有设置处理人", "UserTaskExt.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FIELD_NOT_IN_OBJECT_DESCRIBE("paas.flow.bpm.field_not_in_object_describe", "{0} 的对象{1} 的字段{2} 不存在", "BpmMetadataResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_NOT_EXISTS("paas.flow.bpm.instance_not_exists", "未查询到流程实例", "BPMInstanceServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FIELD_TYPE_NOT_CORRECT("paas.flow.bpm.field_type_not_correct", "{0} 的对象{1} 的字段{2} 的type匹配失败", "BpmMetadataResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CHILD_OBJECT("paas.flow.bpm.child_object", "从对象", "UserTaskAddRelatedObjectValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_EXTERNAL_REMIND_IS_BLANK("paas.flow.bpm.validate.external_remind_is_blank", "{0} 的外部通知标题不能为空", "ExternalMessageValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_NO_PROBLEM("paas.flow.bpm.validate.no.problem", "校验没有问题", "WorkflowResource.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_UPDATE_OBJECT_TYPE_IS_BLANK("paas.flow.bpm.validate.update_object_type_is_blank", "{0} 的更新对象类型不能为空", "UpdateValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EXTENSION_ERROR("paas.flow.bpm.extension.error", "流程扩展信息异常", "BPMBusinessExceptionCode.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_INSTANCE_STARTED("paas.flow.bpm.instance.started", "启动了流程", "MTaskLog.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CREATE_TASK("paas.flow.bpm.create_task", "创建任务", "AfterActionValidateManager.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_TITILE_ISNULL("paas.flow.bpm.verifiy.after.remind.titile.isnull", "{0} 超时提醒标题不能为空", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_VALIDATE_RELATED_OBJECT_PERSON_CONFIG_ERROR("paas.flow.bpm.validate.related_object_person_config_error", " {0} 相关对象的人员字段配置中 {1} ", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_SEND_SCHEDULE_START_TIME_NOT_EMPTY("paas.flow.bpm.send.schedule.start.time.not.empty", "{0} 发送日程 开始时间 不能为空", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DEFINE_DELETE_ERROR_OF_STOP("paas.flow.bpm.define.delete.error.of.stop", "删除失败，请确认该流程是否已经停用", "BPMDefinitionServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_EMAIL_TARGET("paas.flow.bpm.email_target", "{0} 的发送邮件收件人", "SendEmailValidateActionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_OWNER_DEPT_LEADER("paas.flow.bpm.owner.dept.leader", "记录负责人所在主部门负责人", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_FILTER_CONFIG_DEPT_FIELD_ERROR("paas.flow.bpm.filter.config.dept.field.error", "过滤器配置部门类型字段有误", "RuleHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_DRAFT_DATA_CONFIG_ERROR("paas.flow.bpm.draft.data.config.error", "定义草稿数据结构有误，请联系纷享客服", "BPMWorkflowDraftServiceImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_NO_SUPPORT_TYPE("paas.flow.bpm.no.support.type", "暂时不支持此类型:{0}", "BPMExtCandidate.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_CURRENT_INSTANCE_OR_TASK("paas.flow.bpm.current.instance.or.task", "当前实例或任务", "DataChangeProcessor.java", "6.5_201901101006"),

    //================== 2018年12月25日16:54:19 新增属性 开始
    PAAS_FLOW_BPM_BRANCH_SUPPORTS_CHILD_DEPT_EXCEPTION("paas.flow.bpm.branch.supports.child.dept.exception", "分支节点支持子部门配置异常", "ConditionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_WORKFLOW_TEMPLATE_NO_DEFINITION_FOUND("paas.flow.bpm.workflow.template.no.definition.found", "模版不存在,请联系技术人员", "DefinitionInitManagerImpl.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_ACTIVITY_REMIND_FORMAT_ERROR("paas.flow.bpm.activity.remind.format.error", "允许停留时长格式存在异常", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_APPROVAL_NODE_CONFIG_ERROR("paas.flow.bpm.approval.node.config.error", "{0} 审批节点或会签节点配置有误，请确认", "NodeTransitionHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_REMIND_TITLE_EXCEPTION("paas.flow.bpm.remind.titile.exception", "{0} 超时提醒标题 {1}", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_REMIND_CONTENT_EXCEPTION("paas.flow.bpm.remind.content.exception", "{0} 超时提醒内容 {1}", "UserTaskRemindValidateHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_RESOLVE_STAFF_VARIABLE_EXCEPTION("paas.flow.bpm.resolve.staff.variable.exception", "解析人员变量异常", "AssigneeHandler.java", "6.5_201901101006"),

    PAAS_FLOW_BPM_AFTER_WAITING_EXCEPTION("paas.flow.bpm.after.waiting.exception", "后动作正在执行,请稍后刷新重试", "AfterActionMoreOperationManagerImpl.java", "6.5.2_201902281445"),
    PAAS_FLOW_BPM_CONFIG_BUTTON_ONLY_CREATE("paas.flow.bpm.config.button.only.create", "新建 {0}", "", "6.5.1_201901241957"),

    PAAS_FLOW_BPM_EXECUTE_TIMEOUT_TIP("paas.flow.approval.execute_timeout", "执行任务时间较长,请稍后刷新该页面", "PaasWorkflowServiceProxy.java,标识码与审批流相同", "6.5.1_201901281844"),
    PAAS_FLOW_BPM_BUSS_CODE_NOT_FOUND("paas.flow.bpm.buss.code.not.found", "入参必须传递业务码", "营销流程调用业务流,必须要传递的code值", "6.6_201902281108"),
    PAAS_FLOW_BPM_START_RULE_NOT_MEET("paas.flow.bpm.start.rule.not.meet", "不满足完成条件，请确认条件满足后完成任务！", "TaskResource.java", "6.6.2_20190322110516"),
    PAAS_FLOW_AFTER_SYSTEM_FUNCTION_EXCEPTION("paas.flow.after.system.function.exception", "函数执行异常", "和后动作采用同样的key", "6.6_2019_03_14"),

    PAAS_FLOW_BPM_DEPT_NOT_EXIST_EXCEPTION("paas.flow.bpm.dept.not.exist.exception", "{0} 部门不存在，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_DEPT_IS_DELETE_EXCEPTION("paas.flow.bpm.dept.is.delete.exception", "{0} 部门已删除，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_DEPT_IS_STOP_EXCEPTION("paas.flow.bpm.dept.is.stop.exception", "{0} 部门已停用，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_ROLE_IS_EXIST_EXCEPTION("paas.flow.bpm.role.is.exist.exception", "{0} 角色不存在或已删除，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_GROUP_IS_EXIST_EXCEPTION("paas.flow.bpm.group.is.exist.exception", "{0} 用户组不存在或已删除，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_GROUP_IS_STOP_EXCEPTION("paas.flow.bpm.group.is.stop.exception", "{0} 用户组已停用，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_USER_IS_EXIST_EXCEPTION("paas.flow.bpm.user.is.exist.exception", "{0} 用户不存在或已删除，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_USER_IS_STOP_EXCEPTION("paas.flow.bpm.user.is.stop.exception", "{0} 用户已停用，请确认","BPMBaseService.java","6.5_201902271736"),

    PAAS_FLOW_BPM_ORG_EXCEPTION("paas.flow.bpm.org.exception", "{0} {1} {2}", "AfterActionValidateManager.java","6.5_201902271736"),

    PAAS_FLOW_BPM_DATA_LOCK("paas.flow.bpm.data.lock", "数据锁定", "AfterActionExecution", "6.6_2019年04月09日22:13:30"),
    PAAS_FLOW_BPM_DATA_UN_LOCK("paas.flow.bpm.data.un.lock", "数据解锁", "AfterActionExecution", "6.6_2019年04月09日22:13:30"),

    PAAS_FLOW_BPM_TO_DO("paas.flow.bpm.to.do", "去处理", "TaskButtonCommonManagerImpl", "6.7_2019年04月24日15:13:30"),
    PAAS_FLOW_BPM_ACTIVITY_REMIND_FORMAT_ERROR_BY_TASK("paas.flow.bpm.activity.remind.format.error.by.task", "{0} 节点允许停留时长格式存在异常", "UserTaskRemindValidateHandler", "6.6_2019年05月15日09:51:38"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_PARAMS_NOT_SETTING("paas.flow.bpm.externalapply.params.not.setting", "应用节点 {0} 参数配置不完整，请检查", "UserTaskExternalApplyValidateHandler", "6.6_2019年05月22日11:47:13"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_NOT_SETTING("paas.flow.bpm.externalapply.actioncode.not.setting", "应用节点 {0} 未配置需要做的事情，请检查", "UserTaskExternalApplyValidateHandler", "6.6_2019年05月22日11:47:13"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_OFFLINE("paas.flow.bpm.externalapply.actioncode.offline", "应用节点 {0} {1} 已下线，请检查", "UserTaskExternalApplyValidateHandler", "6.6_2019年05月22日11:47:13"),
    PAAS_FLOW_BPM_RANGE_ASSIGNEE("paas.flow.bpm.range.assignee", "发起人范围", "BasicHandler", "6.6_2019年05月29日14:47:13"),
    PAAS_FLOW_BPM_SEND_SCHEDULE_COPY_PARTICIPANT("paas.flow.bpm.send.schedule.copy.participant", "{0} 发送日程 参与人", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),
    PAAS_FLOW_BPM_LOOK_UP_NONSUPPORT_COUNT("paas_flow_bpm_look_up_nonsupport_count", "{0} 字段暂不支持关联类型的统计字段", "RuleHandler.java.java", "6.6.5_201908160017"),
    PAAS_FLOW_BPM_AFTER_ACTION_IS_ERROR("paas.flow.bpm.after.action.is.error", "后动作异常,请管理员查看任务详情进行处理", "FeedScheduleValidateActionHandler.java", "6.5_201901101006"),
    PAAS_FLOW_BPM_AFTER_ACTION_IS_WAITING("paas.flow.bpm.after.action.is.waiting", "任务节点后动作正在执行中，执行完成后，流程将自动进入下一个节点。", "TaskLog.java", "6.7_201906191006"),
    PAAS_FLOW_BPM_NOT_SUPPORT_TRANSFER_DEFINITION("paas.flow.bpm.not.support.transfer.definition", "当前流程定义类型不支持转换为标准业务流。", "TaskLog.java", "8.6_20230419"),
    PAAS_FLOW_BPM_WRITE("paas.flow.bpm.write", "填写", "StandardData.java", "6.7_2019年09月19日17:11:09"),
    PAAS_FLOW_BPM_DATA_INVALID_OR_DELETED_NOT_TRIGGER_FLOW("paas.flow.bpm.data.invalid.or.deleted.not.trigger.flow", "数据作废或已删除，不允许发起流程", "BPMInstanceImpl", "6.7_2019年09月27日18:26:48_bugfix"),
    PAAS_FLOW_BPM_TIME_OUT_REMINDER_STRUCTURE_ERROR("paas.flow.bpm.time.out.reminder.structure.error", "{0} 超时提醒结构体设置异常，请删除重新添加", "UserTaskRemindValidateHandler", "6.7_2019年09月29日10:39:16_bugfix"),
    PAAS_FLOW_BPM_TASK_RELATED_DATA_NOT_FOUND("paas.flow.bpm.task.related.data.not.found", "任务关联数据不存在，请检查流程配置", "UpdateGetTaskDetailHandler", "7.9_2022年02月17日18:39:16_bugfix"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_LAYOUT_ERROR("paas.flow.bpm.verifiy.flow.layout.error", "请选择正确的流程布局","ExtensionHandler", "20220725"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_LAYOUT_DELETED("paas.flow.bpm.verifiy.flow.layout.deleted", "{0}节点对象流程布局已删除，请重新选择", "ExtensionHandler", "20220725"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_RELATION_FIELD("paas.flow.bpm.verifiy.flow.not.relation.field", "{0}节点不存在编辑关联的字段，请重新选择","ExtensionHandler", "20220725"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_RELATION_FIELD_DELETED("paas.flow.bpm.verifiy.flow.relation.field.deleted", "{0}节点编辑的关联对象字段已删除或禁用，请检查","ExtensionHandler", "20220725"),
    PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_TYPE_RELATION_FIELD("paas.flow.bpm.verifiy.flow.not.type.relation.field", "{0}节点编辑的字段非查找关联类型，请重新选择", "ExtensionHandler", "20220725"),

    /**
     * todo 多语言配置
     */
    PAAS_FLOW_BPM_NOT_TOGETHER_EXTERNAL_LINK("paas.flow.bpm.not.together.external.link", "互联应用不能与外部流程一起使用", "BasicHandler", "6.8_2019年11月04日"),
    PAAS_FLOW_BPM_EXTERNAL_APPLY_TASK_NONSUPPORT_LINK_APP("paas.flow.bpm.external.apply.task.nonsupport.link.app", "{0} {1} {2}不支持互联应用", "ExecutionTypeHandler", "6.8_2019年11月04日"),
    PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_CAN_NOT_BE_EMPTY("paas.flow.bpm.validate.basic_link_app_can_not_be_empty","任务或定义未选择互联应用。", "BasicHandler", "6.8_2019年11月09日"),
    PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_ILLEGAL("paas_flow_bpm_validate_basic_link_app_illegal","定义未配置互联应用，{0} 节点不允许配置互联应用", "BasicHandler", "6.8_2019年11月09日"),
    PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_EXTERNAL_ROLE("paas_flow_bpm_validate_node_link_app_and_external_role","{0} 节点配置了外部角色，则必须启动互联应用", "BasicHandler", "6.8_2019年11月09日"),
    PAAS_FLOW_BPM_FRONT_NODE("paas_flow_bpm_front_node","，前置节点：", "BasicHandler", "6.8_2019年11月09日"),
    PAAS_FLOW_BPM_SEND_SMS_TEMPLATE_NOT_EMPTY("paas_flow_bpm_send_sms_template_not_empty", "{0} 短信模版不能为空", "SendSMSValidateActionHandler.java", "6.8_201911091606"),
    PAAS_FLOW_BPM_SEND_SMS_CONTENT_NOT_EMPTY("paas.flow.bpm.send.sms.content.not.empty", "{0} 短信内容不能为空", "SendSMSValidateActionHandler.java", "6.8_201911091606"),
    PAAS_FLOW_BPM_SEND_SMS_OBJECT_PHONE_PROPERTIES("paas_flow_bpm_send_sms_object_phone_properties", "{0} 节点 后动作发送短信 {1} ", "SendSMSValidateActionHandler.java", "6.8_201911091606"),
    PAAS_FLOW_BPM_SEND_SMS_RECEIVE_NOT_NULL("paas_flow_bpm_send_sms_receive_not_null", "{0} 节点后动作发送短信，短信通知人员或相关对象变量或接收手机号不为空", "SendSMSValidateActionHandler.java", "6.8_201911091606"),
    PAAS_FLOW_BPM_SEND_SMS_RECEIVE("paas_flow_bpm_send_sms_receive", "{0} 节点后动作发送短信，短信通知人员，", "SendSMSValidateActionHandler.java", "6.8_201911091606"),
    PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_ASSIGN_NEXT_TASK("paas_flow_bpm_validate_node_link_app_and_assign_next_task", "{0} 节点配置了互联应用，则不支持指定下一节点处理人", "ActivityHandler.java", "6.8_201911091606"),
    PAAS_FLOW_BPM_NODE_FORM_FIELD_DOES_NOT_EXIST("paas.flow.bpm.node.form.field.does.not.exist", "{0} 节点表单字段 {1} 不存在", "UserTaskUpdateValidateHandler.java", "6.9_2020年01月02日16:14:21"),
    PAAS_FLOW_BPM_NODE_FORM_FIELD_BEEN_DISABLED("paas.flow.bpm.node.form.field.been.disabled", "{0} 节点表单字段 {1} 已禁用", "UserTaskUpdateValidateHandler.java", "6.9_2020年01月02日16:14:21"),
    PAAS_FLOW_BPM_NODE_FORM_OBJECT_SIGN_IN_INVALID("paas.flow.bpm.node.form.object.sign.in.invalid", "{0} 节点表单对象不存在签到组件或已禁用", "UserTaskOperationValidateHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_NODE_FORM_OBJECT_SIGN_OUT_INVALID("paas.flow.bpm.node.form.object.sign.out.invalid", "{0} 节点表单对象已禁用签到组件的签退操作", "UserTaskOperationValidateHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_NODE_DELAY_NO_EXIST_TIME("paas.flow.bpm.node.delay.no.exist.time", "{0} 不存等待时常，请检查", "ExecutionTaskValidateHandler.java", "810_2022年05月18日"),
    PAAS_FLOW_BPM_NODE_DELAY_TIME_LESS_ZERO("paas.flow.bpm.node.delay.time.less.zero", "{0} 等待时常必须大于0", "ExecutionTaskValidateHandler.java", "810_2022年05月18日"),
    PAAS_FLOW_BPM_NODE_COMMON_BUTTON_NUMBER_ERROR("paas.flow.bpm.node.common.button.number.error", "{0}未选择自定义按钮", "", "20220808"),
    PAAS_FLOW_BPM_NODE_COMMON_BUTTON_NOT_EXIST("paas.flow.bpm.node.common.button.not.exist", "{0}节点的自定义按钮{1}不存在或已禁用/删除", "", "20220822"),
    PAAS_FLOW_BPM_REMIND_TARGET_NOT_NULL("paas_flow_bpm_remind_target_not_null", "{0} 节点超时提醒配置的提醒人不为空", "UserTaskExt.java", "6.9_2020年01月17日10:14:21"),
    PAAS_FLOW_BPM_PRE_SYNCHRONIZED_TO_CUSTOM_OBJECT_QUERY_ENGINE_DB_RETURNS_NULL("paas.flow.bpm.pre.synchronized.to.custom.object.query.engine.db.returns.null", "预同步到自定义对象,查询引擎DB返回结果为空", "WorkflowTransferServiceImpl.java", "7.0_2020年04月27日13:40:47"),
    PAAS_FLOW_BPM_TASK_BUTTON_SIGN_OUT_DISABLE("paas.flow.bpm.task.button.sign.out.disable", "签到组件未开启签退，请联系管理员", "OperationTaskButtonHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_TASK_BUTTON_SIGNED_IN("paas.flow.bpm.task.button.signed.in", "已签到，只需完成当前任务", "OperationTaskButtonHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_TASK_BUTTON_SIGNED_OUT("paas.flow.bpm.task.button.signed.out", "已签退，只需完成当前任务", "OperationTaskButtonHandler.java", "7.9.5_2022年02月21日11:13:21"),
    PAAS_FLOW_BPM_TASK_BUTTON_NOT_SIGN_OUT("paas.flow.bpm.task.button.not.sign.out", "请先进行签到，才能签退", "OperationTaskButtonHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_TASK_BUTTON_WEB_NOT_SIGN_OUT("paas.flow.bpm.task.button.web.not.sign.out", "请先到移动端进行签到，才能签退", "OperationTaskButtonHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_TASK_NOT_EXIST_SIGN_IN("paas.flow.bpm.task.not.exist.sign.in", "签到组件已被停用或删除", "OperationTaskButtonHandler.java", "7.9.5_2022年02月08日17:13:21"),
    PAAS_FLOW_BPM_TASK_PLEASE_TO_MOBILE_SIGN_IN("paas.flow.bpm.task.please.to.mobile.sign.in", "请到移动端进行签到", "OperationTaskButtonHandler.java", "7.9.5_2022年02月21日11:13:21"),
    PAAS_FLOW_BPM_TASK_PLEASE_TO_MOBILE_SIGN_OUT("paas.flow.bpm.task.please.to.mobile.sign.out", "请到移动端进行签退", "OperationTaskButtonHandler.java", "7.9.5_2022年02月21日11:13:21"),
    PAAS_FLOW_BPM_TASK_SIGNED_IN_GROUP_DELETED("paas.flow.bpm.task.signed.in.group.deleted", "签到时的组件字段已删除", "OperationTaskButtonHandler.java", "7.9.5_2022年02月21日11:13:21"),
    PAAS_FLOW_BPM_TASK_SALES_ORDER_OBJ_RECEIVED("paas.flow.bpm.task.sales.order.obj.received", "订单已收货，无法发货", "OperationTaskConfirmReceiveHandler.java", "8.0.0_2022年04月08日11:13:21"),
    PAAS_FLOW_BPM_TASK_SALES_ORDER_OBJ_REPEAT_RECEIVED("paas.flow.bpm.task.sales.order.obj.repeat.received", "订单已收货，无法再次收货", "OperationTaskConfirmReceiveHandler.java", "8.0.0_2022年04月20日11:13:21"),
    PAAS_FLOW_BPM_TASK_SALES_ORDER_OBJ_NOT_RECEIVED("paas.flow.bpm.task.sales.order.obj.not.received", "订单未发货，无法收货", "OperationTaskConfirmReceiveHandler.java", "8.0.0_2022年04月20日11:13:21"),



    PAAS_FLOW_BPM_BATCH_TASK_DATA_STRUCTURE_FAIL("paas.flow.bpm.batch.task.data.structure.fail", "批量创建节点数据结构异常", "UserTaskBatchAddRelatedObjectValidateHandler.java", "2020年06月05日15:31:32"),

    //================== 2018年12月25日16:54:19 新增属性 结束 以上数据已经导入到国际化平台

    PAAS_FLOW_BPM_CUSTOM_BUTTON_DATA_STRUCTURE_IS_INCORRECT("paas.flow.bpm.custom.button.data.structure.is.incorrect", "请尝试修改 {0} 节点的自定义按钮", "ActivityHandler.java", "2020年07月29日15:13:07"),
    PAAS_FLOW_BPM_CHANGE_APPROVER_INCLUDE_OUTSIDERS_ERROR("paas.flow.bpm.change.approver.include.outsiders.error", "要更换的处理人中包含外部人员,请检查", "BPMTaskServiceImpl.java", "2020年09月01日18:07:45"),
    PAAS_FLOW_BPM_PLEASE_ENTER_APPROVAL_COMMENTS("paas.flow.bpm.please.enter.approval.comments", "处理结果必填: agree|reject", "TaskResource.java", "2020年09月17日17:55:53"),
    PAAS_FLOW_BPM_FUNCTION_VALUE_IS_NULL("paas.flow.bpm.function.value.is.null", "节点 {0} 配置的后动作函数 {1} 参数 {2} value值为空，请选择变量或填写常量值", "CustomFunctionValidateActionHandler.java", "2020年11月25日11:02:32"),
    PAAS_FLOW_BPM_COMPLETE_CONDITION_FUNCTION_VALUE_IS_NULL("paas.flow.bpm.complete.condition.function.value.is.null", "节点 {0} 配置的函数完成条件 {1} 字段 {2} value值为空，请选择变量或填写常量值", "RuleHandler.java", "2020年12月01日19:44:10"),
    ///
    PAAS_FLOW_BPM_NOT_EMPTY("paas.flow.bpm.not.empty", "不能为空", "ValidateVariableAndContentUtils.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_AFTER_ACTION("paas.flow.bpm.after.action", "节点:{0} 后动作 {1}", "ValidateVariableAndContentUtils.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_FUNCTION_CONFIG_IS_EMPTY("paas.flow.bpm.function.config.is.empty", "函数配置为空，请确认", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_FUNCTION_API_NAME_IS_EMPTY("paas.flow.bpm.function.api.name.is.empty", "函数配置apiName为空，请确认", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_FUNCTION_ENTITY_IS_EMPTY("paas.flow.bpm.function.entity.is.empty", "函数的对象类型为空，请确认", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_FUNCTION_ENTITY_NOT_EQ_NODE_ENTITY_IS_EMPTY("paas.flow.bpm.function.entity.not.eq.node.entity.is.empty", "函数配置的对象与当前节点配置对象不一致，请确认", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_FUNCTION_PARAMS_TYPE_NOT_SUPPORT("paas.flow.bpm.function.params.type.not.support", "函数变量 {0} 的类型({1})不支持", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_FUNCTION_CONFIG_PARAMS_MESSAGE("paas.flow.bpm.function.config.params.message", "函数配置的参数 {0}:{1}", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_NODE_COMPLETE_CONDITION("paas.flow.bpm.node.complete.condition", "节点:{0} 完成条件 {1}", "CustomFunctionValidateActionHandler.java", "2020年12月02日14:43:17"),
    PAAS_FLOW_BPM_EXTERNALAPPLY_NOT_SUPPORT("paas.flow.bpm.external.apply.not.support", "请进入服务通应用处理。", "ExternalApplyTaskButtonHandler.java", "2020年11月17日10:59:27"),
    PAAS_FLOW_BPM_CHANGE_APPROVER_TYPE_ERROR("paas.flow.bpm.change.approver.type.error", "非业务流程任务，不允许执行更换处理人操作", "BPMTaskServiceImpl.java", "2020年12月31日17:39:15"),
    PAAS_FLOW_BPM_COMPLETE_TASK_ERROR("paas.flow.bpm.complete.task.error", "非业务流程任务，不允许执行当前操作", "BPMTaskServiceImpl.java", "2020年12月31日17:39:15"),
    PAAS_FLOW_BPM_CANCEL_INSTANCE_TYPE_ERROR("paas.flow.bpm.cancel.instance.type.error", "非业务流程实例，不允许执行终止操作", "BPMTaskServiceImpl.java", "2020年12月31日17:39:15"),
    PAAS_FLOW_BPM_ONLY_SUPPORT_SYSTEM_USER_ERROR("paas.flow.bpm.only.support.system.user.error", "该操作只允许预制系统用户执行", "BPMTaskServiceImpl.java", "745 2021年01月11日11:17:11"),
    PAAS_FLOW_BPM_BRANCH_PARAMS_TYPE_EXCEPTION("paas.flow.bpm.branch.params.type.exception", "{0} 分支变量类型设置有误，请检查", "ConditionHandler.java", "2021年01月18日15:43:12"),
    PAAS_FLOW_BPM_NON_EXTERNAL_OR_INTERCONNECTED_PROCESSE_NOT_SUPPORT_EXTERNAL_APPLY("paas.flow.bpm.non.external.or.interconnected.processe.not.support.external.apply", "非外部流程或互联流程，不允许配置应用节点", "UserTaskExternalApplyValidateHandler.java", "2021年02月02日10:44:58"),
    PAAS_FLOW_BPM_TASK_NOT_FOUND_LINK_APP_ID("paas.flow.bpm.task.not.found.link.app.id", "任务无linkAppId,不允许执行替换处理人操作，请检查", "BPMTaskServiceImpl.java", "2021年02月02日10:44:58"),
    PAAS_FLOW_BPM_COMPLETE_RULE_NOT_MEET("paas.flow.bpm.complete.rule.not.meet", "不满足完成条件，请确认条件满足后完成任务！", "", "2021年07月26日18:16:40"),
    ENGINE_TASK_COMPLETE_PARALLEL_ERROR("engin_task_complete_parallel_time_short", "多个并行任务正在完成,请尝试重新完成任务", "BPMTaskServiceImpl.java", "2021年02月03日20:20:26"),

    //================== 2020年07月29日15:13:27 新增属性 结束 以上数据未导入到国际化平台

    PAAS_FLOW_BPM_SYNCHRONIZATION_TASK_NOT_FOUND_INSTANCE_RETRY_QUEUE("paas.flow.bpm.synchronization.task.not.found.instance.retry.queue", "同步任务时未查询到自定义对象实例数据,进入重试队列", "BpmEventHandler.java", "2021年04月09日11:52:09"),
    PAAS_FLOW_BPM_TASK_CANDIDATEIDS_IS_NULL_REJECT_COMPLETE_TASK("paas.flow.bpm.task.candidateids.is.null.reject.complete.task", "任务待处理人为空,不允许完成任务", "Task.java", "2021-09-24 16:49:38"),
    PAAS_FLOW_BPM_PLEASES_MODIFY_TIME_OUT_EXECUTION("paas.flow.bpm.pleases.modify.time.out.execution", "请尝试修改 {0} 节点的超时策略", "ActivityHandler.java", "2021年10月09日15:01:32"),
    PAAS_FLOW_BPM_PLEASES_MODIFY_TIME_OUT_EXECUTION_PARAMS_IS_NULL("paas.flow.bpm.pleases.modify.time.out.execution.params.is.null", "超时策略必填参数未设置，请检查", "ActivityHandler.java", "2021年10月09日15:01:32"),
    PAAS_FLOW_BPM_NOT_SET_TASK_TIME_OUT_NOT_SET_REMIND_TIME("paas.flow.bpm.not.set.task.time.out.set.remind.time", " {0} 未设置超时时间，不能设置超时提醒，请检查", "","20220620_810"),
    PAAS_FLOW_BPM_LAYOUT_DELETED("paas.flow.bpm.layout.deleted", "流程布局已删除", "","20220620_815"),
    PAAS_FLOW_BPM_EDIT_FORM_IS_EMPTY("paas.flow.bpm.edit.form.is.empty", "请确保是否具有功能权限，或已配置编辑字段", "","20230629_870"),
    PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_NULL("paas.flow.bpm.related.field.object.null", "关联对象的字段没有值或已禁用", "","20220725"),
    PAAS_FLOW_BPM_RELATED_FIELD_OBJECT_INCONSISTENT("paas.flow.bpm.related.field.object.inconsistent", "编辑的关联字段对象类型已被修改", "","20220823"),
    PAAS_FLOW_BPM_TASK_COMMON_BTN_DELETED("paas.flow.bpm.task.common.btn.deleted", "对象自定义按钮已禁用或删除", "","20220804"),
    PAAS_FLOW_BPM_TASK_COMMON_BTN_NOT_VISIBLE("paas.flow.bpm.task.common.btn.not.visible", "不符合对象自定义按钮展示条件，只需完成任务", "","20220804"),
    PAAS_FLOW_BPM_TASK_COMPLETED_CANNOT_MODIFY_DATA("paas.flow.bpm.task.completed.cannot.modify.data", "任务已经完成，不能修改数据", "", "20220819"),
    PAAS_FLOW_BPM_TASK_NOT_CANDIDATE_CANNOT_MODIFY_DATA("paas.flow.bpm.task.not.candidate.cannot.modify.data", "非任务处理人，不能修改数据", "", "20220819"),
    PAAS_FLOW_BPM_TASK_ID_IS_ILLEGAL("paas.flow.bpm.task.id.is.illegal", "任务id不合法", "", "20220824"),
    PAAS_FLOW_BPM_INSTANCE_NOT_CANCEL("paas.flow.bpm.instance.not.cancel", "实例终止中或实例任务完成中，请重试", "","20220905"),
    PAAS_FLOW_BPM_START_INSTANCE_REST_LOOP("paas.flow.bpm.start.instance.rest.loop","rest接口调用存在死循环,自动终止发起流程","","20220915"),
    PAAS_FLOW_BPM_DELAY_TASK_AUTOMATIC_PROCESSING("paas.flow.bpm.delay.task.automatic.processing", "自动处理", "","20230711_870"),
    PAAS_FLOW_BPM_DELAY_TASK_PERSON_PROCESSING("paas.flow.bpm.delay.task.person.processing", "进行了手动执行", "","20230711_870"),


    PAAS_FLOW_BPM_LOG_NODE_ACTION_EXECUTE("paas.flow.bpm.log.node.action.execute", "立即执行", "BPMConstants.java", "810_20220518"),
    PAAS_FLOW_BPM_LOG_NODE_ACTION_REFRESH("paas.flow.bpm.log.node.action.refresh", "刷新", "BPMConstants.java", "810_20220518"),

    PAAS_FLOW_BPM_END_EVENT_TERMINATE_PROCESS("paas.flow.bpm.end.event.terminate.process", "终止流程","AfterActionValidateManager.java", "20220621"),
    /**
     * 8.6.0 后动作变更团队成员
     */
    PAAS_FLOW_BPM_EDIT_TEAM_MEMBER("paas.flow.bpmn.edit_team_member", "变更团队成员", "EditTeamMemberValidateActionHandler.java", "2023年5月17日15:10:52"),
    PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_CREATE_ERROR("paas.flow.bpmn.edit_team_member.create_after_acion_error","未设置变更团队成员","EditTeamMemberValidateActionHandler.java","2023年5月18日20:23:34"),
    PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_PERMISSION_TYPE_ERROR("paas.flow.bpmn.edit_team_member.permission_type_error","变更人员权限为空","EditTeamMemberValidateActionHandler.java","2023年5月18日20:39:53"),
    PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_TYPE_ERROR("paas.flow.bpmn.edit_team_member.edit_type_error","变更类型为空","","2023年5月18日20:39:01"),
    PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_ILLEGAL_OUT_TEAM_MEMBER("paas.flow.bpmn.edit_team_member.illegal_out_team_member","非法的外部人员参数","EditTeamMemberValidateActionHandler.java","2023年5月20日14:22:43"),
    PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_PERSON_NOT_EMPTY("paas.flow.bpmn.edit_team_member.person_not_empty","变更人员为空","EditTeamMemberValidateActionHandler.java","2023年5月18日20:40:44"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_PERSON("paas.flow.bpm.trigger.source.person","手动发起","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_BPM("paas.flow.bpm.trigger.source.bpm","业务流触发","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_WORKFLOW("paas.flow.bpm.trigger.source.workflow","工作流触发","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_APPROVAL("paas.flow.bpm.trigger.source.approval","审批流触发","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_OPENAPI("paas.flow.bpm.trigger.source.openapi","外部调用","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_MARKET("paas.flow.bpm.trigger.source.market","营销流程","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_BPM_TRIGGER_SOURCE_FUNCTION("paas.flow.bpm.trigger.source.function","APL函数触发","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_APPROVAL_CUSTOM_IS_EXISTS("paas.flow.approval.custom.is.exists","变量管理中不能存在重复的ApiName","TriggerSource.java","2023年7月20日15:20:00"),
    PAAS_FLOW_APPROVAL_CUSTOM_VARIABLES_DEFAULT_VALUE_ERROR("paas.flow.approval.custom.variables.default.value.error","变量表 {0} 默认值配置错误","","2023年08月09日"),
    PAAS_FLOW_BPM_WORKFLOW_VARIABLES_TYPE_ERROR("paas.flow.bpm.workflow.variables", "定义{0}变量类型异常，请重新设置", "DefineGenerateManagerImpl.java", "2023年8月2日"),
    PAAS_FLOW_BPM_VERIFIY_STAGE_ORDER_ERROR("paas.flow.bpm.verifiy.stage.order.error", "阶段顺序配置有误，请检查重试", "", "8.8_20230911"),
    PAAS_FLOW_BPM_VERIFIY_AVAILABLE_NODE_ERROR("paas.flow.bpm.verifiy.available.node.error", "流程定义中至少包含一个操作节点，请检查重试", "", ""),
    CONTROLLED_WORKFLOW_CANNOT_BE_CHANGED("paas.flow.workflow.controlled_workflow_cannot_be_changed", "受管控的流程定义无法创建、编辑、删除、停用", "", "2023年9月14日"),
    PAAS_FLOW_BPM_BUTTON_SUSPEND("paas.flow.bpm.button.suspend", "暂不处理", "", "8.8_2023年9月18日"),
    PAAS_FLOW_BPM_BUTTON_RESUME("paas.flow.bpm.button.resume", "继续任务", "", "8.8_2023年9月18日"),
    ENGINE_TASK_OPERATE_PROCESSING("paas.flow.bpm.engine.task.operate.processing", "任务正在处理，请稍后重试", "", "8.8_2023年9月19日"),
    PAAS_FLOW_BPM_TASK_OPERATE_NOT_SUSPEND("paas.flow.bpm_task.operate.not.suspend", "当前任务不能暂停，请刷新页面", "", "8.8_2023年9月19日"),
    PAAS_FLOW_BPM_TASK_OPERATE_NOT_RESUME("paas.flow.bpm.task.operate.not.resume", "当前任务非暂停中，请刷新页面", "", "8.8_2023年9月19日"),
    PAAS_FLOW_BPM_TASK_NOT_SUPPORTED_OPERATE("paas.flow.bpm.task.not.supported.operate", "暂不支持当前任务操作类型", "", "8.8_2023年9月19日"),
    PAAS_FLOW_BPM_TASK_SUSPEND_NOT_COMPLETE("paas.flow.bpm.task.suspend.not.complete", "暂停中的任务不能直接完成，请重试", "", "8.8_2023年9月19日"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_CREATE_ERROR("paas.flow.bpmn.execute_convert_rule.create_after_acion_error","{0} 节点未选择转换规则", "","2023年9月25日18:37:32"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_NAME_IS_NULL("paas.flow.bpmn.execute_convert_rule.rule_name_is_null","{0} 节点转换规则的名称为空", "","2023年9月25日18:56:35"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_API_NAME_IS_NULL("paas.flow.bpmn.execute_convert_rule.rule_api_name_is_null","{0} 节点转换规则的apiName为空", "","2023年9月25日18:54:21"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_SOURCE_API_NAME_IS_NULL("paas.flow.bpmn.execute_convert_rule.source_api_name_is_null","{0} 节点转换规则的源对象为空", "","2023年9月25日18:55:42"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_TARGET_API_NAME_IS_NULL("paas.flow.bpmn.execute_convert_rule.target_api_name_is_null","{0} 节点转换规则的目标对象为空", "","2023年9月25日18:56:30"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_IS_DISABLE_OR_IS_DELETED("paas.flow.bpm.execute_convert_rule.rule_is_disabled_or_deleted","{0} 节点的后动作执行转换规则已停用或删除", "","2023年11月2日11:15:20"),
    PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_IS_DIFF_FROM_ENTRY_TYPE("paas.flow.bpm.execute_convert_rule.source_api_name_is_diff_from_entry_type", "{0} 节点选择的后动作执行转换规则 {1} 的来源对象与业务流发起对象不一致", "", "2023年11月2日14:50:27"),
    PAAS_FLOW_BPM_COMPLETE_TASK_OPINION_TOO_LONG("paas.flow.bpm.complete.task.opinion.too.long","任务意见过长，字数应小于500字，请重试", "","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_UNABLE_ADD_TAG("paas.flow.bpm.task.unable.add.tag","当前任务无法加签，请重试", "","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_ADD_TAG_ARG_ERROR("paas.flow.bpm.task.add.tag.arg.error","加签参数异常，请重试", "","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_ELEMENT_IS_NULL("paas.flow.bpm.task.element_is_null","自定义元素节点 {0} 属性 {1} 不能为空","880","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_ELEMENT_CUSTOM_FALSE("paas.flow.bpm.task.element_custom_false","自定义元素节点 {0} 属性 {1} 必须为 true","880","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_ELEMENT_NOT_EXISTS("paas.flow.bpm.task.element_not_exists","自定义元素节点 {0} 对应的元素 {1} 不可用，请确认","880","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_ELEMENT_EXTENSION_IS_EMPTY("paas.flow.bpm.task.element_extension_is_empty","自定义元素节点 {0} 扩展信息不能为空,请确认","880","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_TASK_ELEMENT_CANDIDATE_NOT_MATCH("paas.flow.bpm.task_element_candidate_not_match","自定义元素节点 {0} 是否配置处理人的值不一致，请确认；理论上 元素需要配置处理人时，这里 customCandidateConfig 需要是 true ","880","2023年10月16日14:45:35"),
    PAAS_FLOW_BPM_BUTTON_ADD_TAG("paas.flow.bpm.button.add.tag", "前加签", "MoreOperationManagerImpl.java", "8.9.0_20231211"),
    PAAS_FLOW_BPM_TASK_TAG_WAITING_NOT_COMPLETE("paas.flow.bpm.task.tag.waiting.not.complete","加签中的任务不能直接完成，请重试", "","890"),
    PAAS_FLOW_BPM_VERIFIY_ASSIGNEE_AND_GROUPHANDLER_COEXIST("paas.flow.bpm.verifiy.assignee.and.grouphandler.coexist", "处理人和分组处理人不能同时存在，请修改 节点名称：{0}", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ERROR("paas.flow.bpm.verifiy.grouphandler.error", "分组处理人配置异常，请重试 节点名称：{0}", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ORDER_ERROR("paas.flow.bpm.verifiy.grouphandler.order.error", "分组处理人排序配置异常，请重试 节点名称：{0}", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_ERROR("paas.flow.bpm.verifiy.grouphandler.rule.error", "分组处理人第{0}组条件异常，{1}，请重试 节点名称：{2}", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ASSIGNEE_ERROR("paas.flow.bpm.verifiy.grouphandler.assignee.error", "分组处理人第{0}组处理人配置异常，请重试 节点名称：{1}", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_NOT_DEFAULT_RULE("paas.flow.bpm.verifiy.grouphandler.not.default.rule", "分组处理人没有默认条件，请重试 节点名称：{0}", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_ERROR_EXCEPTION("paas.flow.bpm.verifiy.grouphandler.rule.error.exception", "分组处理人第{0}组条件异常，请重试 节点名称：{1}", "", "900"),
    PAAS_FLOW_BPM_TASK_DATA_MODIFIED_RETRY_ERROR("paas.flow.bpm.task.data.modified.retry.error", "当前编辑的数据已被他人修改，请关闭页面重试", "", "900"),
    PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_CONTENT_ERROR_EXCEPTION("paas.flow.bpm.verifiy.grouphandler.rule.content.error.exception", "{0} 的第{1}个分组的分组条件 {2}", "","900"),
    PAAS_FLOW_BPM_TAG_WAITING_NOT_CHANGE_APPROVER("paas.flow.bpm.tag.waiting.not.change.approver", "加签中的任务不能更换处理人，请重试", "","900"),
    PAAS_FLOW_BPM_UPDATE_HISTORY_DEFINITION_ERROR("paas.flow.approval.update.history.definition.error", "修改历史定义的结构不符合修改规范，请重试", "","900"),
    PAAS_FLOW_BPM_TASK_ELEMENT_IMPORT_OBJECT_DEF_ERROR("paas.flow.bpm.task.element.import.object.def.error", "自定义元素节点 {0} 引入元素配置错误，请删除原节点重新配置", "","910"),
    SPECIFY_THE_INITIATOR_WHEN_THE_SYSTEM_TRIGGERS_A_PROCESS("paas.flow.bpm.specifyTheInitiatorWhenTheSystemTriggersAProcess", "系统触发流程时，指定发起人", "","910"),
    PAAS_FLOW_BPM_ACTIVITY_NOT_CHOOSE_LINK_APP("paas.flow.bpm.activity.not.choose.link.app","{0} 节点未选择互联应用", "", "910"),
    PAAS_FLOW_BPM_TASK_IN_PROGRESS_CAN_ONLY_REMIND("paas.flow.bpm.task.in.progress.can.only.remind","进行中的任务才能催办，请重试", "", "920"),
    PAAS_FLOW_BPM_TASK_REMIND_PERSONS_IS_NULL("paas.flow.bpm.task.remind.persons.is.null","催办人员是空，请重试", "", "920"),
    PAAS_FLOW_BPM_BUTTON_REMIND("paas.flow.bpm.button.remind", "催办", "MoreOperationManagerImpl.java", "920"),
    PAAS_FLOW_BPM_ACTION_CANNOT_BE_NULL_AFTER_NODE_FIELD_UPDATE("paas.flow.bpm.action.cannot.be.null.after.node.field.update", "{0}节点字段更新后动作{1}不能为空", "", "920"),
    PAAS_FLOW_BPM_ACTION_ONLY_UP_TO_5_LEVELS_CAN_BE_SELECTED_WHEN_UPDATING_ASSOCIATED_OBJECTS("paas.flow.bpm.action.only.up.to.5.levels.can.be.selected.when.updating.associated.objects", "{0}节点字段更新后动作配置错误,更新关联对象时最多只能选择5级", "", "920"),
    THE_RELATEDENTITYID_DOES_NOT_MATCH_THE_ENTITYID_OF_THE_UPDATED_DATA("paas.flow.bpm.the.relatedentityid.does.not.match.the.entityid.of.the.updated.data", "{0}节点字段更新后动作配置错误,更新关联对象时同一组操作entityId必须相同", "", "920"),
    THE_OBJECT_TO_BE_UPDATED_SHOULD_BE_CONSISTENT_WITH_THE_RELATED_ENTITY_ID("paas.flow.bpm.the.object.to.be.updated.should.be.consistent.with.the.related.entity.id", "{0}节点字段更新后动作配置错误,要更新的对象要和relatedEntityId保持一致", "", "920"),
    RELATED_ENTITY_ID_OR_RELATED_OBJECT_ID_CANNOT_HAVE_A_VALUE("paas.flow.bpm.related.entity.id.or.related.object.id.cannot.have.a.value", "{0}节点字段更新后动作配置错误,更新当前对象时,relatedEntityId或relatedObjectId不能有值", "", "920"),
    THE_NUMBER_OF_KEY_LEVELS_CANNOT_EXCEED_3("paas.flow.bpm.the.number.of.key.levels.cannot.exceed.3", "{0}节点字段更新后动作配置错误,更新关联对象时,key的级数不能超过2", "", "920"),
    PAAS_FLOW_BPM_CHANGE_OWNER_V2_ERROR("paas_flow_bpm_change_owner_v2_error", "{0}节点 更换负责人后动作配置异常", "OperationValidateActionHandler.java", "930.1727351976000"),
    APPLICANT_WHEN_SYSTEM_USER_NOT_SUPPORT("applicant_when_system_user_not_support", "流程发起人当是系统时不支持指定为{0}", "BasicHandler.java", "940.2025-02-20 14:34:18"),
    INCORRECT_TASK_HANDLER_FORMAT("incorrect_task_handler_format", "{0}任务处理人格式不正确", "BPMTaskServiceImpl.java", "960.2025-05-20 11:34:18"),
    ;



    public String key;
    public String zh_CN;
    public String since;
    public String version;

    BPMI18N(String key, String zh_CN, String since, String version) {
        this.key = key;
        this.zh_CN = zh_CN;
        this.since = since;
        this.version = version;
    }

    public String text(Object... param){
        return I18NUtils.text(this.key,this.zh_CN,param);
    }

    public static String getI18NByName(BPMBusinessExceptionCode code, Object... arg) {
        try{
            for (BPMI18N value : BPMI18N.values()) {
                if(value.name().equals(code.name())){
                    return BPMI18N.valueOf(code.name()).text(arg);
                }
            }
            String[] stringPlaceHolder = Arrays.stream(arg).map(Objects::toString).toArray(String[]::new);
            return MessageFormat.format(code.getMessage(), stringPlaceHolder);
        }catch (Exception e){
            log.warn("{}");
        }
        return null;
    }
}
