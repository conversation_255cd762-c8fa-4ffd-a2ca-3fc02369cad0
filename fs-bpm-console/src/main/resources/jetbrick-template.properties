jetx.input.encoding=UTF-8
jetx.output.encoding=UTF-8
jetx.import.classes=com.facishare.bpm.resource.*
jetx.template.loaders=$loader
$loader=jetbrick.template.loader.ServletResourceLoader
$loader.root=/WEB-INF/templates/
$loader.reloadable=true
jetx.compile.strategy=precompile
jetx.compile.path=/WEB-INF/jetx_classes
jetx.compile.debug=false
jetx.trim.leading.whitespaces=true
jetx.trim.directive.whitespaces=true
