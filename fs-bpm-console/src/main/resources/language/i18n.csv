key,zh-CN,zh-TW,en,tags,tenant_id
paas.flow.bpm.config.please.upgrade.app.to.latest.version,请升级App到最新版本,請升級App到最新版本,,server,0
paas.flow.bpm.config.button.select.create,选择或新建 {0},選擇或新建 {0},,server,0
paas.flow.bpm.config.button.agree,同意,同意,,server,0
paas.flow.bpm.config.button.reject,不同意,不同意,,server,0
paas.flow.bpm.config.button.update,更新,更新,,server,0
paas.flow.bpm.config.button.save,保存,保存,,server,0
paas.flow.bpm.config.button.complete,完成,完成,,server,0
paas.flow.bpm.config.action.changeowner,变更负责人,變更負責人,,server,0
paas.flow.bpm.config.action.return,退回公海,退回公海,,server,0
paas.flow.bpm.config.action.add.teammember,添加团队成员,添加團隊成員,,server,0
paas.flow.bpm.config.action.close,无效,無效,,server,0
paas.flow.bpm.config.action.followup,跟进中,跟進中,,server,0
paas.flow.bpm.config.action.transform,转换,轉換,,server,0
paas.flow.bpm.config.action.confirmreceive,确认收货,確認收貨,,server,0
paas.flow.bpm.config.action.confirmdelivery,确认发货,確認發貨,,server,0
paas.flow.bpm.quota.standard.edition,您所在企业目前支持{0}个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企業目前支持{0}個業務流程，如果想獲得更多支持，請聯繫紛享客服申請資源擴展包。,,server,0
paas.flow.bpm.quota.enterprise.edition,您所在企业目前支持{0}个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企業目前支持{0}個業務流程，如果想獲得更多支持，請聯繫紛享客服申請資源擴展包。,,server,0
paas.flow.bpm.quota.other,您所在企业目前支持{0}个业务流程，如果想获得更多支持，请联系纷享客服申请资源扩展包。,您所在企業目前支持{0}個業務流程，如果想獲得更多支持，請聯繫紛享客服申請資源擴展包。,,server,0
paas.flow.bpm.service.execute.exception,{0},{0},,server,0
paas.flow.bpm.verifiy.relation.object.not.found,该流程关联的对象不存在，请确认,該流程關聯的對象不存在，請確認,,server,0
paas.flow.bpm.send.schedule.content.not.empty,{0} 发送日程 内容 不能为空,{0} 發送日程 內容 不能爲空,,server,0
paas.flow.bpm.instance.name,流程实例名称,流程實例名稱,,server,0
paas.flow.bpm.node.update.field,{0} 节点 更新字段中 {1} ,{0} 節點 更新字段中 {1} ,,server,0
paas.flow.bpm.send.sale.type.not.empty,{0} 发送销售记录 类型不 能为空,{0} 發送銷售記錄 類型不 能爲空,,server,0
paas.flow.bpm.reminder.not.null,提醒人员不能为空,提醒人員不能爲空,,server,0
paas.flow.bpm.activity.input,连出,連出,,server,0
paas.flow.bpm.service.pass,服务通系列服务等,服務通系列服務等,,server,0
paas.flow.bpm.who_has_stop_def,{0}由{1}在{2}将流程定义停用，请确认,{0}由{1}在{2}將流程定義停用，請確認,,server,0
paas.flow.bpm.task.choice.or.create.object.not.found,任务选择和创建的对象未找到,任務選擇和創建的對象未找到,,server,0
paas.flow.bpm.define.stop.or.delete,{0}  (已停用或删除),{0}  (已停用或刪除),,server,0
paas.flow.bpm.approval.trigger,审批流,審批流,,server,0
paas.flow.bpm.verifiy.after.remind.content.expression.error,{0} 超时提醒内容中表达式存在异常,{0} 超時提醒內容中表達式存在異常,,server,0
paas.flow.bpm.buttion.forward,转发,轉發,,server,0
paas.flow.bpm.verifiy.branch.params,{0} 分支{1} 参数中{2},{0} 分支{1} 參數中{2},,server,0
paas.flow.bpm.verifiy.choice.scope,请选择流程适用范围,請選擇流程適用範圍,,server,0
paas.flow.bpm.verifiy.base.inlet.object.many,入口对象出现了 {0} 个，请确认复制流程时入口对象没有发生变化,入口對象出現了 {0} 個，請確認複製流程時入口對象沒有發生變化,,server,0
paas.flow.bpm.confirm.delivery,确认发货,確認發貨,,server,0
paas.flow.bpm.send.sale.copy.range.not.empty,{0} 发送销售记录 抄送范围 不能为空,{0} 發送銷售記錄 抄送範圍 不能爲空,,server,0
paas.flow.bpm.data.no.permission,没有权限查看,沒有權限查看,,server,0
paas.flow.bpm.object.variables,对象相关变量,對象相關變量,,server,0
paas.flow.bpm.send_email_target_is_empty,{0} 的发送邮件没有指定 [收件员工] 或 [收件邮箱],{0} 的發送郵件沒有指定 [收件員工] 或 [收件郵箱],,server,0
paas.flow.bpm.create_schedule,创建日程,創建日程,,server,0
paas.flow.bpm.send.crm.title.not.empty,{0} 发送CRM提醒 标题 不能为空,{0} 發送CRM提醒 標題 不能爲空,,server,0
paas.flow.bpm.group.dept.leader,记录相关团队成员所在主部门负责人,記錄相關團隊成員所在主部門負責人,,server,0
paas.flow.bpm.object.describe.not.found,对象描述不存在,對象描述不存在,,server,0
paas.flow.bpm.after.execution.function,执行函数,執行函數,,server,0
paas.flow.bpm.verifiy.not.found,{0} 不在阶段中,{0} 不在階段中,,server,0
paas.flow.bpm.verifiy.node.branch.out.line.error,{0} 节点分支{1} 连出有误，请确认,{0} 節點分支{1} 連出有誤，請確認,,server,0
paas.flow.bpm.boolean_type,布尔类型,布爾類型,,server,0
paas.flow.bpm.instance.exception,流程实例异常,流程實例異常,,server,0
paas.flow.bpm.verifiy.at.most,{0} 节点只能有{1} 条{2} 的线,{0} 節點只能有{1} 條{2} 的線,,server,0
paas.flow.bpm.bpm.trigger,业务流程,業務流程,,server,0
paas.flow.bpm.verifiy.not.found.connect.start.node,流程配置错误 {0} 没有连接到开始节点，请确认,流程配置錯誤 {0} 沒有連接到開始節點，請確認,,server,0
paas.flow.bpm.verifiy.node.assigneeId.not.set,{0} 节点未设置处理人,{0} 節點未設置處理人,,server,0
paas.flow.bpm.task.not.found.tasktype,流程定义异常，任务节点缺少任务类型,流程定義異常，任務節點缺少任務類型,,server,0
paas.flow.bpm.external.message,外部通知,外部通知,,server,0
paas.flow.bpm.verifiy.choice.next.assigneed,请指定下一节点处理人,請指定下一節點處理人,,server,0
paas.flow.bpm.national.provincial.city.and.district,国家、省、市、区,國家、省、市、區,,server,0
paas.flow.bpm.node.message.error,节点信息错误,節點信息錯誤,,server,0
paas.flow.bpm.parameter.anomaly,参数异常,參數異常,,server,0
paas.flow.bpm.timeout_warn_validate,超时提醒验证,超時提醒驗證,,server,0
paas.flow.bpm.candidate.params.error,流程分支条件涉及到的参数值 {0} 不正确，目前是 {1} ，请确认,流程分支條件涉及到的參數值 {0} 不正確，目前是 {1} ，請確認,,server,0
paas.flow.bpm.tenant.not.quota,当前模块可用配额数不足，请联系纷享客服购买更高版本或增购资源包。,當前模塊可用配額數不足，請聯系紛享客服購買更高版本或增購資源包。,,server,0
paas.flow.bpm.current_task_form_has_must_field,当前记录有必填项$-$未填写，请【编辑】补充完整后，再进行当前任务;,當前記錄有必填項$-$未填寫，請【編輯】補充完整後，再進行當前任務;,,server,0
paas.flow.bpm.verifiy.at.least,{0} 节点至少有{1}条{2}的线,{0} 節點至少有{1}條{2}的線,,server,0
paas.flow.bpm.outer.matched.field.is.only.option,下游企业匹配的字段只支持单选,下游企業匹配的字段只支持單選,,server,0
paas.flow.bpm.validate.not_set_belong_object, {0} 未设置所属对象, {0} 未設置所屬對象,,server,0
paas.flow.bpm.validate.external_flow_first_activity_must_handler_activity,外部流程第一个节点必须是业务节点或审批节点,外部流程第一個節點必須是業務節點或審批節點,,server,0
paas.flow.bpm.verifiy.absence.approval.result,缺少审批结果,缺少審批結果,,server,0
paas.flow.bpm.flow.handle.error,流程处理异常,流程處理異常,,server,0
paas.flow.bpm.task.completed.time,任务完成时间,任務完成時間,,server,0
paas.flow.bpm.validate.function_variable_type_error,{0} 的执行函数变量{1} 的类型不支持,{0} 的執行函數變量{1} 的類型不支持,,server,0
paas.flow.bpm.update.node,更新节点,更新節點,,server,0
paas.flow.bpm.automatic.node.miss.execution.item,自动节点缺少执行项，请确认,自動節點缺少執行項，請確認,,server,0
paas.flow.bpm.form.format.data.null,表单数据为空,表單數據爲空,,server,0
paas.flow.bpm.support.not.found,扩展没找到,擴展沒找到,,server,0
paas.flow.bpm.node.data.error,{0} 节点数据选择错误,{0} 節點數據選擇錯誤,,server,0
paas.flow.bpm.verifiy.absence.buss.node,缺少业务节点,缺少業務節點,,server,0
paas.flow.bpm.workflow.name.dupllicate,流程名称已存在,流程名稱已存在,,server,0
paas.flow.bpm.task.not.setting.action.code,没有设置ActionCode,沒有設置ActionCode,,server,0
paas.flow.bpm.node.send.remind.content,{0} 节点 发送CRM提醒 内容中 {1} ,{0} 節點 發送CRM提醒 內容中 {1} ,,server,0
paas.flow.bpm.end.node.send.remind.content,{0} 发送CRM提醒 内容中 {1} ,{0} 發送CRM提醒 內容中 {1} ,,server,0
paas.flow.bpm.template.delete.choose.other,该流程模板已删除，请选择别的模板,該流程模板已刪除，請選擇別的模板,,server,0
paas.flow.bpm.define.service.stop,{0} 流程定义已被停用，请确认,{0} 流程定義已被停用，請確認,,server,0
paas.flow.bpm.send.task.assignor.not.empty,{0} 发送任务 分配人 为空,{0} 發送任務 分配人 爲空,,server,0
paas.flow.bpm.start.to.node.need.approval.node,开始节点到{0} 之间必须有审批、会签或业务节点,開始節點到{0} 之間必須有審批、會籤或業務節點,,server,0
paas.flow.bpm.validate.exists_multi_transition,存在多条相同连线，请检查,存在多條相同連線，請檢查,,server,0
paas.flow.bpm.no.entityId,tenantId={0} 没有对象={1} ,tenantId={0} 沒有對象={1} ,,server,0
paas.flow.bpm.after.setting.error,后动作配置错误，不支持 {0},後動作配置錯誤，不支持 {0},,server,0
paas.flow.bpm.verifiy.reset.bpm.flow.name,请重新设定流程名称,請重新設定流程名稱,,server,0
paas.flow.bpm.workflow.trigger,工作流,工作流,,server,0
paas.flow.bpm.send_email_sender_is_empty,{0} 的发送邮件发送人不能为空,{0} 的發送郵件發送人不能爲空,,server,0
paas.flow.bpm.send.schedule.remind.setting.not.empty,{0} 发送日程 提醒配置 不能为空,{0} 發送日程 提醒配置 不能爲空,,server,0
paas.flow.bpm.send.sale.copy.range,{0} 发送销售记录 抄送范围,{0} 發送銷售記錄 抄送範圍,,server,0
paas.flow.bpm.verifiy.choice.bpm.flow,请选择业务流程,請選擇業務流程,,server,0
paas.flow.bpm.lookup.object.not.exist,对象关联关系已解除或已禁用，流程无法继续进行，请联系系统管理员,對象關聯關係已解除或已禁用，流程無法繼續進行，請聯繫系統管理員,,server,0
paas.flow.bpm.external.content,外部通知 内容中 {0},外部通知 內容中 {0},,server,0
paas.flow.bpm.timeout_warn_person,{0} 超时提醒提醒人员,{0} 超時提醒提醒人員,,server,0
paas.flow.bpm.instance.not.found,流程实例不存在,流程實例不存在,,server,0
paas.flow.bpm.task.status.paas,任务已经被处理,任務已經被處理,,server,0
paas.flow.bpm.bpm.flow.condition.not.support,业务流程分支类型: {0} 不支持操作符: {1},業務流程分支類型: {0} 不支持操作符: {1},,server,0
paas.flow.bpm.candidate.get.assigneed.error,解析人员失败，不支持{0},解析人員失敗，不支持{0},,server,0
paas.flow.bpm.activity_to_activity_condition_error,{0} 到 {1} 节点的 {2} 条件{3}，请联系流程配置人员,{0} 到 {1} 節點的 {2} 條件{3}，請聯繫流程配置人員,,server,0
paas.flow.bpm.validate.function_apiname_is_blank,{0} 的执行函数配置apiName不能为空,{0} 的執行函數配置apiName不能爲空,,server,0
paas.flow.bpm.verifiy.person.prop.config.error,{0} 人员对象属性变量配置不正确,{0} 人員對象屬性變量配置不正確,,server,0
paas.flow.bpm.task.handler,节点执行人,節點執行人,,server,0
paas.flow.bpm.verifiy.node.branch.in.line.error,{0} 节点分支{1} 连入节点有误，请确认,{0} 節點分支{1} 連入節點有誤，請確認,,server,0
paas.flow.bpm.send.schedule.copy.range.not.empty,{0} 发送日程 抄送范围 不能为空,{0} 發送日程 抄送範圍 不能爲空,,server,0
paas.flow.bpm.validate.has_stop,[{0}]业务流程定义已停用,[{0}]業務流程定義已停用,,server,0
paas.flow.bpm.application.node,应用节点,應用節點,,server,0
paas.flow.bpm.send.task.copy.range,{0} 发送任务 抄送范围 ,{0} 發送任務 抄送範圍 ,,server,0
paas.flow.bpm.validate.update_field_value_is_blank,{0} 的更新字段的值不能为空,{0} 的更新字段的值不能爲空,,server,0
paas.flow.bpm.validate.not_set_operate_type, {0} 未设置操作类型, {0} 未設置操作類型,,server,0
paas.flow.bpm.validate.no_default_transition, {0} 需要配置默认分支, {0} 需要配置默認分支,,server,0
paas.flow.bpm.task.complete.notice,任务完成通知,任務完成通知,,server,0
paas.flow.bpm.who_has_deleted_def,{0}由{1}在{2}将流程定义删除，请确认,{0}由{1}在{2}將流程定義刪除，請確認,,server,0
paas.flow.bpm.define.not.found.start.node,没有开始节点,沒有開始節點,,server,0
paas.flow.bpm.task.not.choice.data,没有选择关联数据，不能完成任务,沒有選擇關聯數據，不能完成任務,,server,0
paas.flow.bpm.validate.not_config.relatedEntityId, {0} 未设置relatedEntityId, {0} 未設置relatedEntityId,,server,0
paas.flow.bpm.object.field.disable,{0} 的{1} 已被禁用，请确认,{0} 的{1} 已被禁用，請確認,,server,0
paas.flow.bpm.agree,同意,同意,,server,0
paas.flow.bpm.view_entire,查看完整流程,查看完整流程,,server,0
paas.flow.bpm.send.crm.recipient,{0} 发送CRM提醒 接收人,{0} 發送CRM提醒 接收人,,server,0
paas.flow.bpm.send.task.remind.not.empty,{0} 发送任务 提醒配置 不能为空,{0} 發送任務 提醒配置 不能爲空,,server,0
paas.flow.bpm.node.send.task.content,{0} 节点 发送任务 内容中 {1} ,{0} 節點 發送任務 內容中 {1} ,,server,0
paas.flow.bpm.view.bpm.instance.log,流程日志,流程日誌,,server,0
paas.flow.bpm.verifiy.node.complete.condition,{0} 节点完成条件中{1},{0} 節點完成條件中{1},,server,0
paas.flow.bpm.not_support_variable,业务流程不支变量 {0} ,業務流程不支變量 {0} ,,server,0
paas.flow.bpm.lookup.object,关联对象,關聯對象,,server,0
paas.flow.bpm.field_label_not_correct,{0} 的对象{1} 的字段{2} 的label匹配失败,{0} 的對象{1} 的字段{2} 的label匹配失敗,,server,0
paas.flow.bpm.system.error,系统异常,系統異常,,server,0
paas.flow.bpm.verifiy.trigger.condition,流程发起条件中 {0},流程發起條件中 {0},,server,0
paas.flow.bpm.validate.external_content_is_blank,{0} 的外部通知内容不能为空,{0} 的外部通知內容不能爲空,,server,0
paas.flow.bpm.send.sale.content.not.empty,{0} 发送销售记录内容不能为空,{0} 發送銷售記錄內容不能爲空,,server,0
paas.flow.bpm.create.sales_record,创建销售记录,創建銷售記錄,,server,0
paas.flow.bpm.external.role.not.null,外部角色不能为空,外部角色不能爲空,,server,0
paas.flow.bpm.draft.already.exist,当前定义草稿已存在，请编辑原草稿,當前定義草稿已存在，請編輯原草稿,,server,0
paas.flow.bpm.entity.field.delete.disable,{0} 的字段{1} 已经删除或停用，请确认,{0} 的字段{1} 已經刪除或停用，請確認,,server,0
paas.flow.bpm.object.data.remove.instance.cancel,由于 {0} 数据作废，系统自动终止当前流程,由於 {0} 數據作廢，系統自動終止當前流程,,server,0
paas.flow.bpm.workflow.engine.variables,流程引擎相关变量,流程引擎相關變量,,server,0
paas.flow.bpm.validate.gateway_need_appear_in_pairs,网关节点需要成对出现,網關節點需要成對出現,,server,0
paas.flow.bpm.task.relation.object.delete,请检查任务相关对象是否被删除、作废或无权限，无法完成任务,請檢查任務相關對象是否被刪除、作廢或無權限，無法完成任務,,server,0
paas.flow.bpm.trigger_bpm,触发业务流程,觸發業務流程,,server,0
paas.flow.bpm.task.reference.desc.not.found,任务涉及的业务对象描述已删除,任務涉及的業務對象描述已刪除,,server,0
paas.flow.bpm.flow.variable.not.support,流程变量不支持{0},流程變量不支持{0},,server,0
paas.flow.bpm.verifiy.stage.not.found.node,{0} 阶段中没有节点，请确认,{0} 階段中沒有節點，請確認,,server,0
paas.flow.bpm.validate.related_object_email_config_error, {0} 相关对象的邮件字段配置中 {1} , {0} 相關對象的郵件字段配置中 {1} ,,server,0
paas.flow.bpm.templete.name.not.null,{0} 模板名称不能为空,{0} 模板名稱不能爲空,,server,0
paas.flow.bpm.validate.data_related_or_variable_config_error, {0} 数据相关或流程变量配置有误, {0} 數據相關或流程變量配置有誤,,server,0
paas.flow.bpm.multiplayer.approval,多人审批,多人審批,,server,0
paas.flow.bpm.has.no.handler,未解析到处理人,未解析到處理人,,server,0
paas.flow.bpm.button.ignore,忽略,忽略,,server,0
paas.flow.bpm.quota.error,配额异常,配額異常,,server,0
paas.flow.bpm.assigneeId.dept.leader,节点处理人所属主部门负责人,節點處理人所屬主部門負責人,,server,0
paas.flow.bpm.some_data_deal_error,部分数据处理失败,部分數據處理失敗,,server,0
paas.flow.bpm.task.refer.object.not.found,任务业务对象无法正常获取,任務業務對象無法正常獲取,,server,0
paas.flow.bpm.node.object.not.same.as.function,节点对象 与 执行函数配置的对象不致，请确认,節點對象 與 執行函數配置的對象不致，請確認,,server,0
paas.flow.bpm.lookup.object.already.delete,{0} 节点关联的对象已被禁用或删除，请确认,{0} 節點關聯的對象已被禁用或刪除，請確認,,server,0
paas.flow.bpm.instance.stop.call.crm.system,当前流程已被终止，请联系管理员确认,當前流程已被終止，請聯繫管理員確認,,server,0
paas.flow.bpm.node.not.exist,节点不存在,節點不存在,,server,0
paas.flow.bpm.external.reminder,{0}外部通知提醒人员,{0}外部通知提醒人員,,server,0
paas.flow.bpm.object.delete.disable,{0} 对象已被删除或禁用，请确认,{0} 對象已被刪除或禁用，請確認,,server,0
paas.flow.bpm.create.related.object,选择和新建从对象,選擇和新建從對象,,server,0
paas.flow.bpm.define.service.delete,{0} 流程定义已被删除，请确认,{0} 流程定義已被刪除，請確認,,server,0
paas.flow.bpm.disagree,不同意,不同意,,server,0
paas.flow.bpm.extension.is.null,扩展信息不能为空,擴展信息不能爲空,,server,0
paas.flow.bpm.task.not.found.instance.id,任务没找到instanceId: {0} activityId: {1},任務沒找到instanceId: {0} activityId: {1},,server,0
paas.flow.bpm.objext.not.empty,对象为空,對象爲空,,server,0
paas.flow.bpm.after.server,后动作服务,後動作服務,,server,0
paas.flow.bpm.instance.param.not.null,流程实例参数不能为空,流程實例參數不能爲空,,server,0
paas.flow.bpm.definition_convert_success,定义转换成功,定義轉換成功,,server,0
paas.flow.bpm.send.mail,发送邮件,發送郵件,,server,0
paas.flow.bpm.validate.not_set_belong_object_name, {0} 未设置所属对象名称, {0} 未設置所屬對象名稱,,server,0
paas.flow.bpm.send.task.content.not.empty,{0} 发送任务内容不能为空,{0} 發送任務內容不能爲空,,server,0
paas.flow.bpm.required.filed.error.msg2,没有填写必填信息:-不能完成任务!如果看不到选项，请确认是否有相关权限*;,沒有填寫必填信息:-不能完成任務!如果看不到選項，請確認是否有相關權限*;,,server,0
paas.flow.bpm.validate.pool_is_empty,泳道不能为空,泳道不能爲空,,server,0
paas.flow.bpm.verifiy.define.loop.error,存在闭环流程，请修改流程图,存在閉環流程，請修改流程圖,,server,0
paas.flow.bpm.validate.update_field_name_is_blank,{0} 的更新字段name不能为空,{0} 的更新字段name不能爲空,,server,0
paas.flow.bpm.create.detail.task,新建从对象节点,新建從對象節點,,server,0
paas.flow.bpm.verifiy.object.email.field.expression.config.error,{0} 对象属性邮件类型字段变量配置不正确,{0} 對象屬性郵件類型字段變量配置不正確,,server,0
paas.flow.bpm.start.exception,流程启动异常,流程啓動異常,,server,0
paas.flow.bpm.definition.error,流程定义异常,流程定義異常,,server,0
paas.flow.bpm.instance.complete.or.cancel,该流程已完成或已取消,該流程已完成或已取消,,server,0
paas.flow.bpm.verifiy.inlet.object.disable.or.delete,入口对象已被禁用或被删除，请确认,入口對象已被禁用或被刪除，請確認,,server,0
paas.flow.bpm.select_which_bpm,{0} 的触发业务流程，请选择触发哪个业务流程,{0} 的觸發業務流程，請選擇觸發哪個業務流程,,server,0
paas.flow.bpm.send.crm.contentnot.empty,{0} 发送CRM提醒 内容 不能为空,{0} 發送CRM提醒 內容 不能爲空,,server,0
paas.flow.bpm.system.error.contact.servicer,系统异常，请联系客服人员,系統異常，請聯繫客服人員,,server,0
paas.flow.bpm.node.send.remind.title,{0} 节点 发送CRM提醒 标题中 {1} ,{0} 節點 發送CRM提醒 標題中 {1} ,,server,0
paas.flow.bpm.end.node.send.remind.title,{0} 发送CRM提醒 标题中 {1} ,{0} 發送CRM提醒 標題中 {1} ,,server,0
paas.flow.bpm.paas.execute.ignore.success,执行忽略成功,執行忽略成功,,server,0
paas.flow.bpm.task.change.owner.expression.error,更换处理人人员变量存在异常,更換處理人人員變量存在異常,,server,0
paas.flow.bpm.task.replace.owner.expression.error,替换处理人人员变量存在异常,替換處理人人員變量存在異常,,server,0
paas.flow.bpm.node.field.is.not.active,{0} 节点 {1} ({2} )涉及到的字段{3} 已被禁用，请开启后重试,{0} 節點 {1} ({2} )涉及到的字段{3} 已被禁用，請開啓後重試,,server,0
paas.flow.bpm.preparative.data.remove.or.delete,预设数据已作废或已删除,預設數據已作廢或已刪除,,server,0
paas.flow.bpm.instance.name.required,请填写流程名称,請填寫流程名稱,,server,0
paas.flow.bpm.change.owner,更换处理人,更換處理人,,server,0
paas.flow.bpm.task.not.auth.complete,您无执行权限,您無執行權限,,server,0
paas.flow.bpm.validate.transition_condition_error,线条表达式异常,線條表達式異常,,server,0
paas.flow.bpm.metadata.data.not.found,数据不存在,數據不存在,,server,0
paas.flow.bpm.instance.id.not.found,流程实例id不能为空,流程實例id不能爲空,,server,0
paas.flow.bpm.verifiy.string.type,字符串类型,字符串類型,,server,0
paas.flow.bpm.task.status.paas.by.user,您已经处理过此任务,您已經處理過此任務,,server,0
paas.flow.bpm.instance.start.time,流程实例开始时间,流程實例開始時間,,server,0
paas.flow.bpm.tips.expression.error,表达式存在异常,表達式存在異常,,server,0
paas.flow.bpm.validate.not_set_business_record, {0} 未设置业务记录, {0} 未設置業務記錄,,server,0
paas.flow.bpm.out.tenant.matched.field.required,请选择下游企业匹配的字段,請選擇下游企業匹配的字段,,server,0
paas.flow.bpm.draft.stop.or.delete,{0}  (已停用或删除),{0}  (已停用或刪除),,server,0
paas.flow.bpm.assignee.id,节点处理人,節點處理人,,server,0
paas.flow.bpm.define.not.found,流程定义不存在,流程定義不存在,,server,0
paas.flow.bpm.group.leader,记录相关团队成员上级,記錄相關團隊成員上級,,server,0
paas.flow.bpm.node.lookup.or.slave.field.not.exist,{0} 节点 关联对象或从对象({1} )描述中已将({2} )的引用字段进行移除或修改，请确认并重新选择{3} 节点的配置信息,{0} 節點 關聯對象或從對象({1} )描述中已將({2} )的引用字段進行移除或修改，請確認並重新選擇{3} 節點的配置信息,,server,0
paas.flow.bpm.task.get.task.error,获取任务信息失败,獲取任務信息失敗,,server,0
paas.flow.bpm.assignee.leader,节点处理人上级,節點處理人上級,,server,0
paas.flow.bpm.object_field_not_correct,{0} 的对象{1} 的字段{2} 匹配失败,{0} 的對象{1} 的字段{2} 匹配失敗,,server,0
paas.flow.bpm.workflow.task.execute.exception,任务执行异常,任務執行異常,,server,0
paas.flow.bpm.verifiy.task.mismatch,当前节点对象和入口对象不一致,當前節點對象和入口對象不一致,,server,0
paas.flow.bpm.choose.create.detail.object,选择和新建从对象,選擇和新建從對象,,server,0
paas.flow.bpm.not_support_operate,{0}不支持操作符{1},{0}不支持操作符{1},,server,0
paas.flow.bpm.has.no.task.and.land.page,实例信息:当实例完成时，没有当前任务和落地页面,實例信息:當實例完成時，沒有當前任務和落地頁面,,server,0
paas.flow.bpm.parallel_gateway,并行网关,並行網關,,server,0
paas.flow.bpm.reject_approve,驳回了审批,駁回了審批,,server,0
paas.flow.bpm.server.error,服务异常,服務異常,,server,0
paas.flow.bpm.send.schedule.copy.range,{0} 发送日程 抄送范围 不能为空,{0} 發送日程 抄送範圍 不能爲空,,server,0
paas.flow.bpm.verifiy.stage.get.info.error,阶段信息获取异常,階段信息獲取異常,,server,0
paas.flow.bpm.task.assignee.param.error,任务指定人参数不正确,任務指定人參數不正確,,server,0
paas.flow.bpm.metadata.object.describe.not.found.or.delete,自定义对象描述未找到或已删除,自定義對象描述未找到或已刪除,,server,0
paas.flow.bpm.applicant.id,流程发起人,流程發起人,,server,0
paas.flow.bpm.validate.function_object_is_blank,{0} 的执行函数的对象类型不能为空,{0} 的執行函數的對象類型不能爲空,,server,0
paas.flow.bpm.task.params.error,处理任务请求参数不正确，请联系技术人员,處理任務請求參數不正確，請聯繫技術人員,,server,0
paas.flow.bpm.task.name,任务名称,任務名稱,,server,0
paas.flow.bpm.node.send.schedule.content,{0} 节点 发送日程 内容中 {1} ,{0} 節點 發送日程 內容中 {1} ,,server,0
paas.flow.bpm.metadata.exception,数据处理异常,數據處理異常,,server,0
paas.flow.bpm.object.not.exist,对象不存在或已禁用,對象不存在或已禁用,,server,0
paas.flow.bpm.button.retry,重试,重試,,server,0
paas.flow.bpm.object.variable.error,对象变量不正确，请确认,對象變量不正確，請確認,,server,0
paas.flow.bpm.verifiy.format.error,{0} 表单格式不正确,{0} 表單格式不正確,,server,0
paas.flow.bpm.trigger.by.person,手动触发,手動觸發,,server,0
paas.flow.bpm.operate_activity,操作节点,操作節點,,server,0
paas.flow.bpm.select_trigger_object,{0} 的触发业务流程请选择发起对象,{0} 的觸發業務流程請選擇發起對象,,server,0
paas.flow.bpm.metadata.object.data.not.found.or.delete,自定义对象数据已作废或已删除,自定義對象數據已作廢或已刪除,,server,0
paas.flow.bpm.approval.passed,同意了审批,同意了審批,,server,0
paas.flow.bpm.after.update,后动作更新,後動作更新,,server,0
paas.flow.bpm.owner.leader,记录负责人上级,記錄負責人上級,,server,0
paas.flow.bpm.trigger_function_variable,{0}  触发函数 配置中参数{1} 的值 {2} ,{0}  觸發函數 配置中參數{1} 的值 {2} ,,server,0
paas.flow.bpm.terminal.elevation.hint,终端强升提示,終端強升提示,,server,0
paas.flow.bpm.get.pattern.error,获取模板异常,獲取模板異常,,server,0
paas.flow.bpm.insance.not.auth.cancel,您无权取消该流程,您無權取消該流程,,server,0
paas.flow.bpm.verifiy.condition.not.found.variable,{0} 分支的{1} 条件不在变量中,{0} 分支的{1} 條件不在變量中,,server,0
paas.flow.bpm.verifiy.condition.variable.contains,{0} 分支{1}变量中 {2},{0} 分支{1}變量中 {2},,server,0
paas.flow.bpm.node.send.feed.content,{0} 节点 发送销售 记录内容中 {1} ,{0} 節點 發送銷售 記錄內容中 {1} ,,server,0
paas.flow.bpm.send.crm.recipient.not.empty,{0} 发送CRM提醒 接收人 不能为空,{0} 發送CRM提醒 接收人 不能爲空,,server,0
paas.flow.bpm.validate.not_config_executionType, {0} 未设置executionType, {0} 未設置executionType,,server,0
paas.flow.bpm.successful.re.implementation,重新执行成功,重新執行成功,,server,0
paas.flow.bpm.object.null,对象不能为空,對象不能爲空,,server,0
paas.flow.bpm.node.lookup.object.not.found,节点对象的lookup对象不存在,節點對象的lookup對象不存在,,server,0
paas.flow.bpm.common.field,普通字段,普通字段,,server,0
paas.flow.bpm.basic.information,基本信息,基本信息,,server,0
paas.flow.bpm.after.error,后动作异常，请管理员查看任务详情进行处理,後動作異常，請管理員查看任務詳情進行處理,,server,0
paas.flow.bpm.invoice.has.open,发货单已开启， {0} 操作不可使用，请修改流程配置,發貨單已開啓， {0} 操作不可使用，請修改流程配置,,server,0
paas.flow.bpm.validate.function_config_error,{0} 的执行函数配置不正确,{0} 的執行函數配置不正確,,server,0
paas.flow.bpm.definition.transfer.error,流程定义转换失败,流程定義轉換失敗,,server,0
paas.flow.bpm.finished.task,完成了任务,完成了任務,,server,0
paas.flow.bpm.number.type,数字类型,數字類型,,server,0
paas.flow.bpm.crm.remind.cleaner.interface,消息消数接口,消息消數接口,,server,0
paas.flow.bpm.node.after.setting,{0} 节点 后动作配置的 {1} ,{0} 節點 後動作配置的 {1} ,,server,0
paas.flow.bpm.task.relation.describe.delete,任务关联的对象描述已被删除，请确认,任務關聯的對象描述已被刪除，請確認,,server,0
paas.flow.bpm.deploy.exception,流程发布异常,流程發佈異常,,server,0
paas.flow.bpm.validate.not_set_all_approve_type, {0} 未设置会签类型, {0} 未設置會籤類型,,server,0
paas.flow.bpm.task.check.object.info,{0} 请检查任务对象信息,{0} 請檢查任務對象信息,,server,0
paas.flow.bpm.countersign,会签,會籤,,server,0
paas.flow.bpm.draft.error,流程草稿异常,流程草稿異常,,server,0
paas.flow.bpm.draft.quota.error,草稿配额数量已达上限:草稿配额为定义配额的两倍,草稿配額數量已達上限:草稿配額爲定義配額的兩倍,,server,0
paas.flow.bpm.send.crm,发送企信提醒,發送企信提醒,,server,0
paas.flow.bpm.repeat_trigger,[{0} ]业务流程不允许重复发起,[{0} ]業務流程不允許重複發起,,server,0
paas.flow.bpm.button.save,保存,保存,,server,0
paas.flow.bpm.send.task.assignor,{0} 发送任务 分配人,{0} 發送任務 分配人,,server,0
paas.flow.bpm.validate.update_form_is_blank,{0} 的更新没有配置要更新的字段,{0} 的更新沒有配置要更新的字段,,server,0
paas.flow.bpm.verifiy.not.supported,{0} 不支持  {1} 类型,{0} 不支持  {1} 類型,,server,0
paas.flow.bpm.draft.not.found,草稿已删除或不存在,草稿已刪除或不存在,,server,0
paas.flow.bpm.node.define.not.found,节点定义不存在，请确认参数的正确性,節點定義不存在，請確認參數的正確性,,server,0
paas.flow.bpm.verifiy.right.config.error,右侧配置错误,右側配置錯誤,,server,0
paas.flow.bpm.activity.output,连入,連入,,server,0
paas.flow.bpm.validate.the_waiting_time_set_error, {0} 允许停留时长设置有误请检查, {0} 允許停留時長設置有誤請檢查,,server,0
paas.flow.bpm.node.object.error,节点对象异常{0},節點對象異常{0},,server,0
paas.flow.bpm.verifiy.stage.name.isnull,阶段名称不能为空,階段名稱不能爲空,,server,0
paas.flow.bpm.task.handler.leader,节点执行人上级,節點執行人上級,,server,0
paas.flow.bpm.entry.type.required,请选择入口对象类型,請選擇入口對象類型,,server,0
paas.flow.bpm.entry.name.required,请选择入口对象名称,請選擇入口對象名稱,,server,0
paas.flow.bpm.send.task.title.not.empty,{0} 发送任务 标题 不能为空,{0} 發送任務 標題 不能爲空,,server,0
paas.flow.bpm.send.task.time.not.empty,{0} 发送任务 完成时间 不能为空,{0} 發送任務 完成時間 不能爲空,,server,0
paas.flow.bpm.stop.employee,停用用户,停用用戶,,server,0
paas.flow.bpm.parameter.incorrect,参数不正确，请联系纷享客服,參數不正確，請聯繫紛享客服,,server,0
paas.flow.bpm.verifiy.node.non.exist.stage,节点应该在阶段中,節點應該在階段中,,server,0
paas.flow.bpm.tenant.id.or.entity.id.not.null,企业Id或者对象Id为空,企業Id或者對象Id爲空,,server,0
paas.flow.bpm.create.and.choice.relation.object,选择和新建关联对象,選擇和新建關聯對象,,server,0
paas.flow.bpm.verifiy.after.remind.titile.expression.error,{0} 超时提醒标题中表达式存在异常,{0} 超時提醒標題中表達式存在異常,,server,0
paas.flow.bpm.send_email_template_is_empty,{0} 的发送邮件模板不能为空,{0} 的發送郵件模板不能爲空,,server,0
paas.flow.bpm.verifiy.after.remind.content.remind.time.error,{0} 超时提醒提醒时间设置超出允许停留时间，请检查,{0} 超時提醒提醒時間設置超出允許停留時間，請檢查,,server,0
paas.flow.bpm.verifiy.after.remind.content.remind.time.field.stop.or.delete,{0} 超时提醒 提醒时间设置的对象或字段已停用或删除，请检查,{0} 超時提醒 提醒時間設置的對象或字段已停用或刪除，請檢查,,server,0
paas.flow.bpm.confirm.receive,确认收货,確認收貨,,server,0
paas.flow.bpm.area_location,地区定位,地區定位,,server,0
paas.flow.bpm.verifiy.loop.monitor.error,回路监测异常，请检查流程图,迴路監測異常，請檢查流程圖,,server,0
paas.flow.bpm.node.send.task.title,{0} 节点 发送任务 标题中 {1} ,{0} 節點 發送任務 標題中 {1} ,,server,0
paas.flow.bpm.verifiy.after.remind.content.isnull,{0} 超时提醒内容不能为空,{0} 超時提醒內容不能爲空,,server,0
paas.flow.bpm.button.cancel.bpm.flow,终止,終止,,server,0
paas.flow.bpm.object.field.delete,{0} 的{1} 已被删除，请确认,{0} 的{1} 已被刪除，請確認,,server,0
paas.flow.bpm.task.not.found,任务不存在,任務不存在,,server,0
paas.flow.bpm.stop.crm.group,停用组,停用組,,server,0
paas.flow.bpm.button.save_and_complete,保存并完成,保存並完成,,server,0
paas.flow.bpm.external.title,外部通知 标题中 {0},外部通知 標題中 {0},,server,0
paas.flow.bpm.verifiy.expression.not.found.variable,定义中表达式未添加到变量定义中,定義中表達式未添加到變量定義中,,server,0
paas.flow.bpm.instance.end,流程结束,流程結束,,server,0
paas.flow.bpm.permission.error,权限异常,權限異常,,server,0
paas.flow.bpm.task.not.found.assigneed,{0} 没有设置处理人,{0} 沒有設置處理人,,server,0
paas.flow.bpm.field_not_in_object_describe,{0} 的对象{1} 的字段{2} 不存在,{0} 的對象{1} 的字段{2} 不存在,,server,0
paas.flow.bpm.instance_not_exists,未查询到流程实例,未查詢到流程實例,,server,0
paas.flow.bpm.field_type_not_correct,{0} 的对象{1} 的字段{2} 的type匹配失败,{0} 的對象{1} 的字段{2} 的type匹配失敗,,server,0
paas.flow.bpm.child_object,从对象,從對象,,server,0
paas.flow.bpm.validate.external_remind_is_blank,{0} 的外部通知标题不能为空,{0} 的外部通知標題不能爲空,,server,0
paas.flow.bpm.validate.no.problem,校验没有问题,校驗沒有問題,,server,0
paas.flow.bpm.validate.update_object_type_is_blank,{0} 的更新对象类型不能为空,{0} 的更新對象類型不能爲空,,server,0
paas.flow.bpm.extension.error,流程扩展信息异常,流程擴展信息異常,,server,0
paas.flow.bpm.instance.started,启动了流程,啓動了流程,,server,0
paas.flow.bpm.create_task,创建任务,創建任務,,server,0
paas.flow.bpm.verifiy.after.remind.titile.isnull,{0} 超时提醒标题不能为空,{0} 超時提醒標題不能爲空,,server,0
paas.flow.bpm.validate.related_object_person_config_error, {0} 相关对象的人员字段配置中 {1} , {0} 相關對象的人員字段配置中 {1} ,,server,0
paas.flow.bpm.send.schedule.start.time.not.empty,{0} 发送日程 开始时间 不能为空,{0} 發送日程 開始時間 不能爲空,,server,0
paas.flow.bpm.define.delete.error.of.stop,删除失败，请确认该流程是否已经停用,刪除失敗，請確認該流程是否已經停用,,server,0
paas.flow.bpm.email_target,{0} 的发送邮件收件人,{0} 的發送郵件收件人,,server,0
paas.flow.bpm.owner.dept.leader,记录负责人所在主部门负责人,記錄負責人所在主部門負責人,,server,0
paas.flow.bpm.filter.config.dept.field.error,过滤器配置部门类型字段有误,過濾器配置部門類型字段有誤,,server,0
paas.flow.bpm.draft.data.config.error,定义草稿数据结构有误，请联系纷享客服,定義草稿數據結構有誤，請聯繫紛享客服,,server,0
paas.flow.bpm.no.support.type,暂时不支持此类型:{0},暫時不支持此類型:{0},,server,0
paas.flow.bpm.current.instance.or.task,当前实例或任务,當前實例或任務,,server,0
paas.flow.bpm.branch.supports.child.dept.exception,分支节点支持子部门配置异常,分支節點支持子部門配置異常,,server,0
paas.flow.bpm.workflow.template.no.definition.found,模版不存在，请联系技术人员,模版不存在，請聯繫技術人員,,server,0
paas.flow.bpm.activity.remind.format.error,允许停留时长格式存在异常,允許停留時長格式存在異常,,server,0
paas.flow.bpm.approval.node.config.error,{0} 审批节点或会签节点配置有误，请确认,{0} 審批節點或會籤節點配置有誤，請確認,,server,0
paas.flow.bpm.dept.not.exist.exception,{0} 部门不存在，请确认,{0} 部門不存在，請確認,,server,0
paas.flow.bpm.dept.is.delete.exception,{0} 部门已删除，请确认,{0} 部門已刪除，請確認,,server,0
paas.flow.bpm.dept.is.stop.exception,{0} 部门已停用，请确认,{0} 部門已停用，請確認,,server,0
paas.flow.bpm.role.is.exist.exception,{0} 角色不存在或已删除，请确认,{0} 角色不存在或已刪除，請確認,,server,0
paas.flow.bpm.group.is.exist.exception,{0} 用户组不存在或已删除，请确认,{0} 用戶組不存在或已刪除，請確認,,server,0
paas.flow.bpm.group.is.stop.exception,{0} 用户组已停用，请确认，请确认,{0} 用戶組已停用，請確認，請確認,,server,0
paas.flow.bpm.user.is.exist.exception,{0} 用户不存在或已删除，请确认,{0} 用戶不存在或已刪除，請確認,,server,0
paas.flow.bpm.user.is.stop.exception,{0} 用户已停用，请确认,{0} 用戶已停用，請確認,,server,0
paas.flow.bpm.org.exception,{0} {1} {2},{0} {1} {2},,server,0
paas.flow.bpm.remind.titile.exception,{0} 超时提醒标题 {1},{0} 超時提醒標題 {1},,server,0
paas.flow.bpm.remind.content.exception,{0} 超时提醒内容 {1},{0} 超時提醒內容 {1},,server,0
paas.flow.bpm.resolve.staff.variable.exception,解析人员变量异常,解析人員變量異常,,server,0
paas.flow.approval.execute_timeout,执行任务时间较长，请稍后刷新该页面,執行任務時間較長，請稍後刷新該頁面,,server,0,
paas.flow.bpm.config.button.only.create,新建 {0},新建 {0},,server,0,
paas.flow.bpm.buss.code.not.found,入参必须传递业务码,入參必須傳遞業務碼,,server,0,
paas.flow.bpm.after.waiting.exception,后动作正在执行，请稍后刷新重试,後動作正在執行，請稍後刷新重試,,server,0
paas.flow.after.system.function.exception,函数执行异常,函數執行異常,,server,0
paas.flow.bpm.start.rule.not.meet,不满足完成条件，请确认条件满足后完成任务！,不滿足完成條件，請確認條件滿足後完成任務！,,server,0
paas.flow.bpm.data.lock,数据锁定,數據鎖定,,server,0
paas.flow.bpm.data.un.lock,数据解锁,數據解鎖,,server,0
paas.flow.bpm.to.do,去处理,去处理,,server,0
paas.flow.bpm.activity.remind.format.error.by.task,{0} 节点允许停留时长格式存在异常,{0} 節點允許停留時長格式存在異常,,server,0
paas.flow.bpm.externalapply.params.not.setting,应用节点 {0} 参数配置不完整，请检查,應用節點 {0} 參數配置不完整，請檢查,,server,0
paas.flow.bpm.externalapply.actioncode.not.setting,应用节点 {0} 未配置需要做的事情，请检查,應用節點 {0} 未配置需要做的事情，請檢查,,server,0
paas.flow.bpm.externalapply.actioncode.offline,应用节点 {0} {1} 已下线，请检查,應用節點 {0} {1} 已下線，請檢查,,server,0
paas.flow.bpm.range.assignee,发起人范围,發起人範圍,,server,0
paas.flow.bpm.get.enterprise.info.error,获取企业信息失败,獲取企業信息失敗,,server,0
paas.flow.bpm.remind.email.format.error,{0} 超时邮件提醒信息不完整,{0} 超時郵件提醒信息不完整,,server,0
paas.flow.bpm.custom.data.not.found,自定义对象数据已作废或已删除,自定義對象數據已作廢或已刪除,,server,0
paas.flow.bpm.package.data.not.found,预设数据已作废或已删除,預設數據已作廢或已刪除,,server,0
paas.flow.bpm.desc.not.found,对象不存在或已禁用,對象不存在或已禁用,,server,0
paas.flow.bpm.send.schedule.copy.participant,{0} 发送日程 参与人,{0} 發送日程 參與人,,server,0
paas_flow_bpm_look_up_nonsupport_count,{0} 字段暂不支持关联类型的统计字段,{0} 字段暫不支持關聯類型的統計字段,,server,0
paas.flow.bpm.after.action.is.waiting,任务节点后动作正在执行中，执行完成后，流程将自动进入下一个节点。,任務節點後動作正在執行中，執行完成後，流程將自動進入下一個節點。,,server,0
paas.flow.bpm.after.action.is.error,后动作异常，请管理员查看任务详情进行处理。,後動作異常，請管理員查看任務詳情進行處理。,,server,0
paas.flow.bpm.write,填写,填寫,,server,0
paas.flow.bpm.data.invalid.or.deleted.not.trigger.flow,数据作废或已删除，不允许发起流程,數據作廢或已刪除，不允許發起流程,,server,0
paas.flow.bpm.time.out.reminder.structure.error,{0} 超时提醒结构体设置异常，请删除重新添加,{0} 超時提醒結構體設置異常，請刪除重新添加,,server,0
paas.flow.bpm.external.apply.task.nonsupport.link.app,{0} {1} {2} 不支持互联应用,{0} {1} {2} 不支持互聯應用,,server,0
paas.flow.bpm.not.together.external.link,互联应用不能与外部流程一起使用,互聯應用不能與外部流程一起使用,,server,0
paas.flow.bpm.validate.basic_link_app_can_not_be_empty,任务或定义未选择互联应用。,任務或定義未選擇互聯應用。,,server,0
paas_flow_bpm_validate_basic_link_app_illegal,定义未配置互联应用，{0} 节点不允许配置互联应用,定義未配置互聯應用，{0} 節點不允許配置互聯應用,,server,0
paas_flow_bpm_validate_node_link_app_and_external_role,{0} 节点配置了外部角色，则必须启动互联应用,{0} 節點配置了外部角色，則必須啟動互聯應用,,server,0
paas_flow_bpm_front_node,，前置节点：,，前置節點：,,server,0
paas_flow_bpm_send_sms_template_not_empty,{0} 短信模版不能为空,{0} 短信模版不能為空,,server,0
paas_flow_bpm_send_sms_object_phone_properties,{0} 节点 后动作发送短信 {1} ,{0} 節點 後動作發送短信 {1} ,,server,0
paas_flow_bpm_validate_node_link_app_and_assign_next_task,{0} 节点配置了互联应用，则不支持指定下一节点处理人,{0} 節點配置了互聯應用，則不支持指定下一節點處理人,,server,0
paas.flow.bpm.please.refresh.the.page,当前访问的服务接口已下线，请尝试刷新页面,當前訪問的服務接口已下線，請嘗試刷新頁面,,server,0
paas_flow_bpm_send_sms_receive_not_null,{0} 节点后动作发送短信，短信通知人员或相关对象变量或接收手机号不为空,{0} 節點後動作發送短信，短信通知人員或相關對象變量或接收手機號不為空,,server,0
paas_flow_bpm_send_sms_receive,{0} 节点后动作发送短信，短信通知人员，,{0} 節點後動作發送短信，短信通知人員，,,server,0
paas.flow.bpm.send.sms.content.not.empty,{0} 短信内容不能为空,{0} 短信內容不能為空,,server,0
paas.flow.bpm.node.form.field.does.not.exist,{0} 节点表单字段 {1} 不存在,{0} 節點表單字段 {1} 不存在,,server,0
paas.flow.bpm.node.form.field.been.disabled,{0} 节点表单字段 {1} 已禁用,{0} 節點表單字段 {1} 已禁用,,server,0
paas_flow_bpm_remind_target_not_null,{0} 节点超时提醒配置的提醒人不为空,{0} 節點超時提醒配置的提醒人不為空,,server,0
paas.flow.bpm.config.button.to.do,去处理,去處理,,server,0
paas.flow.bpm.batch.config.button.select.create,批量新建 {0},批量新建 {0},,server,0
paas.flow.bpm.custom.button.data.structure.is.incorrect,请尝试修改 {0} 节点的自定义按钮,請嘗試修改 {0} 節點的自定義按鈕,,server,0
paas.flow.bpm.batch.config.button.master.detail.create,编辑从对象,編輯從對象,,server,0
paas.flow.bpm.change.approver.include.outsiders.error,要更换的处理人中包含外部人员，请检查,要更換的處理人中包含外部人員，請檢查,,server,0
paas.flow.bpm.external.apply.not.support,请进入服务通应用处理。,請進入服務通應用處理。,,server,0
paas.flow.bpm.function.value.is.null,节点 {0} 配置的后动作函数 {1} 字段 {2} value值为空，请选择变量或填写常量值。,節點{0}配寘的後動作函數{1}欄位{2} value值為空，請選擇變數或填寫常數值。,,server,0
paas.flow.bpm.complete.condition.function.value.is.null,节点 {0} 配置的函数完成条件 {1} 字段 {2} value值为空，请选择变量或填写常量值。,節點{0}配寘的函數完成條件{1}欄位{2} value值為空，請選擇變數或填寫常數值。,,server,0
paas.flow.bpm.not.empty,不能为空,不能為空,,server,0
paas.flow.bpm.after.action,节点:{0} 后动作 {1},節點：{0}後動作{1},,server,0
paas.flow.bpm.function.config.is.empty,函数配置为空，请确认,函數配寘為空，請確認,,server,0
paas.flow.bpm.function.api.name.is.empty,函数配置apiName为空，请确认,函數配寘apiName為空，請確認,,server,0
paas.flow.bpm.function.entity.is.empty,函数的对象类型为空，请确认,函數的對象類型為空，請確認,,server,0
paas.flow.bpm.function.entity.not.eq.node.entity.is.empty,函数配置的对象与当前节点配置对象不一致，请确认,函數配寘的對象與當前節點配寘對象不一致，請確認,,server,0
paas.flow.bpm.function.params.type.not.support,函数变量 {0} 的类型({1})不支持,函數變數{0}的類型（{1}）不支持,,server,0
paas.flow.bpm.function.config.params.message,函数配置的参数 {0}:{1},函數配寘的參數{0}：{1},,server,0
paas.flow.bpm.node.complete.condition,节点:{0} 完成条件 {1},節點：{0}完成條件{1},,server,0
paas.flow.bpm.change.approver.type.error,非业务流程任务，不允许执行更换处理人操作,非業務流程任務，不允許執行更換處理人操作,,server,0
paas.flow.bpm.complete.task.error,非业务流程任务，不允许执行当前操作,非業務流程任務，不允許執行當前操作,,server,0
paas.flow.bpm.cancel.instance.type.error,非业务流程实例，不允许执行终止操作,非業務流程實例，不允許執行終止操作,,server,0
paas.flow.bpm.only.support.system.user.error,该操作只允许预制系统用户执行,該操作只允許預製系統用戶執行	,,server,0
paas.flow.bpm.branch.params.type.exception,{0} 分支变量类型设置有误，请检查,{0}分支變數類型設定有誤，請檢查,,server,0
paas.flow.bpm.non.external.or.interconnected.processe.not.support.external.apply,非外部流程或互联流程，不允许配置应用节点,非外部流程或互聯流程，不允許配寘應用節點,,server,0
paas.flow.bpm.task.not.found.link.app.id,任务无linkAppId，不允许执行替换处理人操作,任務無linkAppId，不允許執行替換處理人操作,,server,0
engin_task_complete_parallel_time_short,多个并行任务正在完成，请尝试重新完成任务	,多個並行任務正在完成，請嘗試重新完成任務,,server,0
paas.flow.bpm.synchronization.task.not.found.instance.retry.queue,同步任务时未查询到自定义对象实例数据，进入重试队列,同步任務時未査詢到自定義對象實例數據，進入重試隊列,,server,0
paas.flow.bpm.complete.rule.not.meet,不满足完成条件，请确认条件满足后完成任务,不滿足完成條件，請確認條件滿足後完成任務,,server,0
paas.flow.bpm.task.candidateids.is.null.reject.complete.task,任务处理人为空，不允许完成任务,任務處理人為空，不允許完成任務,,server,0
paas.flow.bpm.pleases.modify.time.out.execution,请尝试修改 {0} 节点的超时策略,請嘗試修改{0}節點的超時策略,,server,0
paas.flow.bpm.pleases.modify.time.out.execution.params.is.null,超时策略必填参数未设置，请检查,超時策略必填參數未設定，請檢查,,server,0
