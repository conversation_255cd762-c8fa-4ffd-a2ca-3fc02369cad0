package com.facishare.bpm.resource.model;

import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.utils.MapUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;


/**
 * Created by penghj on 2016/8/3.
 */
@Data
@Entity(value = "tasks", noClassnameStored = true)
public class TaskEntity implements Serializable {

  private static final long serialVersionUID = -4007289483623262192L;

  @Id
  protected ObjectId id;
  @Property("entityId")
  protected String entityId;
  @Property("objectId")
  protected String objectId;
  @Embedded("bpmExtension")
  protected Map<String, Object> bpmExtension;
  @Property("externalFlow")
  protected Integer externalFlow;
  @Property("linkApp")
  protected String linkApp;
  @Property("linkAppType")
  protected Integer linkAppType;

  public ExecutionTypeEnum getExecutionType() {
    return ExecutionTypeEnum.getExecutionType(this.bpmExtension);
  }

  public boolean getExternalFlowType() {
    return !Objects.isNull(externalFlow) && externalFlow == 1;
  }

  public Map<String, Object> getExternalApply() {
    return MapUtil.instance.getMap(this.getBpmExtension(), WorkflowKey.ActivityKey.ExtensionKey.externalApply);
  }

  public String getExternalApplyAppCode() {
    Map<String, Object> externalApply = getExternalApply();
    if (MapUtils.isNotEmpty(externalApply)) {
      Object getAppCode = externalApply.get(WorkflowKey.ActivityKey.ExtensionKey.appCode);
      if (Objects.nonNull(getAppCode)) {
        return getAppCode + "";
      }
    }
    return "";
  }

  public String getExternalApplyActionCode() {
    Map<String, Object> externalApply = getExternalApply();
    if (MapUtils.isNotEmpty(externalApply)) {
      Object actionCode = externalApply.get(WorkflowKey.ActivityKey.ExtensionKey.actionCode);
      if (Objects.nonNull(actionCode)) {
        return actionCode + "";
      }
    }
    return "";
  }

}
