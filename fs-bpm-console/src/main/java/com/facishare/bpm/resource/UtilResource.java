package com.facishare.bpm.resource;

import com.alibaba.fastjson.JSON;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.manager.MongoShardManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.GetWorkflow;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.app.GetAppActions;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.resource.todo.AsyncUpdateCustomInfo;
import com.facishare.bpm.proxy.NewPaasMetadataProxy;
import com.facishare.bpm.remote.app.ErAppProxy;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.resource.model.Result;
import com.facishare.bpm.resource.model.TaskEntity;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.LaneEntity;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.query.WorkflowOutlineQuery;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.MongoClient;
import com.mongodb.client.DistinctIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 17/01/2017.
 * @IgnoreI18n
 */
@Path("/util")
@Service
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Slf4j
public class UtilResource {

    @Autowired
    private BpmSimpleDefinitionDao outlineDao;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    private DefinitionExtensionDao workflowExtensionDao;
    @Autowired
    private MongoShardManager mongoShardManager;
    @Autowired
    private TodoResource todoResource;
    @Autowired
    private ErAppProxy erAppProxy;
    @Autowired
    private NewPaasMetadataProxy newMetadataProxy;


    @POST
    @Path("/sleep5s/paas/bpm/task/id")
    public Result getTaskById() {
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return new Result();
    }




    @POST
    @Path("/sleep5s/paas/bpm/completeTask")
    public Result completeTask() {
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return new Result();
    }

    @Path("/getI18nKey/{tenantId}")
    @GET
    public Result getI18nKey(@PathParam("tenantId") String tenantId) {
        //查询企业流程列表
        Page page = Page.getPage(1, 20);
        PageResult<WorkflowOutlineEntity> outlinePageResult;
        List<KeyInfo> result =Lists.newArrayList();
        while (Boolean.TRUE){
            outlinePageResult = outlineDao.find(tenantId, new WorkflowOutlineQuery(), page, false, null, Boolean.TRUE);
            if(CollectionUtils.isEmpty(outlinePageResult.getDataList())){
                break;
            }
            for (WorkflowOutlineEntity workflowOutlineEntity : outlinePageResult.getDataList()) {
                result.addAll(getKey(workflowOutlineEntity));
            }
            page.setPageNumber(page.getPageNumber() + 1);
        }
        String str = "";
        for (KeyInfo keyInfo : result) {
            str += keyInfo.getName() + "," + keyInfo.getKey() + "," + keyInfo.getTag() + "," + keyInfo.getDesc() + "<br />";
        }
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("String", str);
        return new Result(result, ext);
    }

    private List<KeyInfo> getKey(WorkflowOutlineEntity outlineEntity){
        List<KeyInfo> result = Lists.newArrayList();
        //获取引擎定义
        GetWorkflow.Result workflowAndRule;
        String tenantId = outlineEntity.getTenantId();
        try {
            workflowAndRule = paasWorkflow.getWorkflowAndRule(initContext(tenantId), outlineEntity.getWorkflowId());
        }catch (Exception e){
            return result;
        }
        if(Objects.isNull(workflowAndRule) || MapUtils.isEmpty(workflowAndRule.getWorkflowMap())) {
            return result;
        }
        ExecutableWorkflowExt workflowEntity = ExecutableWorkflowExt.fromJson(workflowAndRule.getWorkflowJson());
        String flowId= (String) workflowEntity.get("id");
        String flowName= (String) workflowEntity.get("name");

        //流程名称
        if(StringUtils.isNotBlank((String)workflowEntity.get("name"))){
            result.add(new KeyInfo((String) workflowEntity.get("name"), "flow." + flowId + ".name", flowName + "流程的名称"));
        }
        //流程描述
        if(StringUtils.isNotBlank((String)workflowEntity.get("description"))){
            result.add(new KeyInfo((String) workflowEntity.get("description"), "flow." + flowId + ".description", flowName + "流程的描述"));
        }
        //阶段信息
        FlowExtensionEntity extensionEntity = workflowExtensionDao.findOneFlowExtension(tenantId, flowId);
        for (PoolEntity pool : extensionEntity.getPools()) {
            for (LaneEntity lane : pool.getLanes()) {
                if(StringUtils.isNotBlank(lane.getName())){
                    result.add(new KeyInfo(lane.getName(), "flow." + flowId + "." + lane.getId() + ".name", flowName + "流程的" + lane.getName() + "阶段的名称"));
                }
                if(StringUtils.isNotBlank(lane.getDescription())){
                    result.add(new KeyInfo(lane.getDescription(), "flow." + flowId + "." + lane.getId() + ".description", flowName + "流程的" + lane.getName() + "阶段的描述"));
                }
            }
        }
        //节点信息 + 分支网关
        ArrayList<Map<String, Object>> activities = (ArrayList)workflowEntity.get("activities");
        for (Map<String, Object> activity : activities) {
            if(StringUtils.isNotBlank((String)activity.get("name"))){
                result.add(new KeyInfo((String) activity.get("name"), "flow." + flowId + "." + activity.get("id") + ".name", flowName + "流程的" + activity.get("name") + "节点的名称"));
            }
            if (StringUtils.isNotBlank((String)activity.get("description"))){
                result.add(new KeyInfo((String) activity.get("description"), "flow." + flowId + "." + activity.get("id") + ".description", flowName + "流程的" + activity.get("name") + "节点的描述"));
            }
            //按钮信息
            Map<String, Object> customButtons = getTaskCustomButtons(activity);
            if(MapUtils.isNotEmpty(customButtons)){
                customButtons.forEach((k, btn) ->{
                    if(StringUtils.isNotBlank((String)(((Map)btn).get("label")))){
                        result.add(new KeyInfo((String)(((Map)btn).get("label")), "flow." + flowId + "." + activity.get("id") + "." + k, flowName + "流程的" + activity.get("name") + "节点的" + ((Map)btn).get("label") + "按钮的名称"));
                    }
                });
            }
        }
        return result;
    }

    private static Map<String, Object> getTaskCustomButtons(Map<String, Object> task){
        Map<String, Object>extension = (Map)task.get("bpmExtension");
        if (extension == null) {
            return null;
        }
        Map btn = (Map) extension.get("defaultButtons");
        return btn == null ? null : btn;
    }

    private RemoteContext initContext(String tenantId) {
        return new RemoteContext(null, tenantId, BPMConstants.CRM_APP_ID, BPMConstants.CRM_SYSTEM_USER);
    }

    @Data
    class KeyInfo{
        private String name;
        private String key;
        private String tag = "flow";
        private String desc;

        public KeyInfo() {
        }

        public KeyInfo(String name, String key, String desc) {
            this.name = name;
            this.key = key;
            this.desc = desc;
        }
    }


    @Path("/refreshHistoryTodoColumnFilter")
    @GET
    public void refreshHistoryTodoColumnFilter(@QueryParam("tenantId")String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            return ;
        }
        log.info("-----------------------开始更新待办信息，tenantId：" + tenantId +"-----------------------");
        List<String> workFlowsType = Lists.newArrayList(WorkflowConstants.WorkflowType.APPROVAL_FLOW, WorkflowConstants.WorkflowType.BPM, WorkflowConstants.WorkflowType.STAGE);
        CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClient.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(
                        PojoCodecProvider.builder().register(TaskEntity.class).build()
                )
        );
        RemoteContext context = new RemoteContext();
        context.setUserId("-10000");
        context.setTenantId(tenantId);
        Map<String, String> dataRecordTypeCache = Maps.newHashMap();
        Map<String, List<GetAppActions.App>> bizNodeCache = Maps.newHashMap();
        Set<String> deleteData = Sets.newHashSet();
        for (String type : workFlowsType) {
            if(!SwitchConfigManager.getOpenRefreshData()){
                break;
            }
            Set<String> entityIdSet = Sets.newHashSet();
            try {
                mongoShardManager.getMongoDatabases().forEach((database) -> {
                    MongoCollection<Document> workflowsCollection = database.getCollection("workflows");
                    Bson filter = Filters.and(
                            Filters.eq(BPMConstants.TENANT_ID, tenantId),
                            Filters.eq(BPMConstants.TYPE, type)
                    );
                    DistinctIterable<String> entityIds = workflowsCollection.distinct("entityId", filter, String.class);
                    for (String entityId : entityIds) {
                        entityIdSet.add(entityId);
                    }
                });
                log.info(type + "类型，获取对象ApiName成功！！！");
            } catch (Exception e) {
                log.info(type + "类型，获取对象ApiName失败！！！");
                continue;
            }
            if(CollectionUtils.isEmpty(entityIdSet)){
                continue;
            }
            for (String entityId : entityIdSet) {
                log.info("开始 类型：" + type + "，entityId:" + entityId + "，的待办修改");
                if(!SwitchConfigManager.getOpenRefreshData()){
                    break;
                }
                List<TaskEntity> taskEntity = Lists.newArrayList();
                try {
                    mongoShardManager.getMongoDatabases().forEach((database) -> {
                        MongoCollection<Document> workflowsCollection = database.getCollection("tasks").withCodecRegistry(codecRegistry);
                        Bson filter = Filters.and(
                                Filters.eq(BPMConstants.TENANT_ID, tenantId),
                                Filters.eq("entityId", entityId),
                                Filters.eq(BPMConstants.TYPE, type)
                        );
                        Bson projection = Projections.include(Lists.newArrayList("_id", "entityId", "objectId", "bpmExtension", "externalFlow", "linkApp", "linkAppType"));
                        FindIterable<TaskEntity> taskEntities = workflowsCollection.find( filter, TaskEntity.class).projection(projection);
                        for (TaskEntity task: taskEntities) {
                            taskEntity.add(task);
                        }
                    });
                }catch (Exception e){
                    log.info(type + "类型，获取对象"+ entityId +"任务失败！！！");
                    continue;
                }
                refreshTodoColumnFilter(taskEntity, tenantId, type, dataRecordTypeCache, bizNodeCache, context, deleteData);
                log.info("结束 类型：" + type + "，entityId:" + entityId + "，的待办修改");
            }

        }
        log.info("-----------------------结束更新待办信息，tenantId：" + tenantId +"-----------------------");


    }

    private void refreshTodoColumnFilter(List<TaskEntity> tasks, String tenantId, String type, Map<String, String> dataRecordTypeCache, Map<String, List<GetAppActions.App>> bizNodeCache, RemoteContext context, Set<String> deleteData){
        if(CollectionUtils.isEmpty(tasks)){
            return;
        }
        if(!SwitchConfigManager.getOpenRefreshData()){
            return;
        }
        List<UpdateCustomToDoInfo> toTodoList = Lists.newArrayList();
        for (TaskEntity task : tasks) {
            if(!SwitchConfigManager.getOpenRefreshData()){
                break;
            }
            if(StringUtils.isAnyBlank(task.getEntityId(), task.getObjectId())){
                log.info("任务没有对象ApiName或id，taskId：" + task.getId().toString());
                continue;
            }
            //获取对象业务类型
            String recordType = "";
            String key = task.getEntityId() + "+" + task.getObjectId();
            if(dataRecordTypeCache.containsKey(key)){
                recordType = dataRecordTypeCache.get(key);
            }else {
                if(deleteData.contains(key)){
                    continue;
                }
                try {
                    Map<String, Object> dataById = newMetadataProxy.findDataById(context, task.getEntityId(), task.getObjectId());
                    recordType = (String) dataById.get("record_type");
                }catch (Exception e){
                    log.info("获取对象业务类型失败，taskId：" + task.getId().toString());
                    if("数据已作废或已删除".equals(e.getMessage())){
                        deleteData.add(key);
                    }
                    continue;
                }
                if (StringUtils.isNotBlank(recordType)) {
                    dataRecordTypeCache.put(key, recordType);
                }
            }
            if (StringUtils.isBlank(recordType)){
                log.info("获取对象业务类型失败，taskId：" + task.getId().toString() + ",获取对象数据是成功的");
                continue;
            }
            //获取业务流节点类型
            String bizNode = WorkflowConstants.WorkflowType.BPM.equals(type) ? getBpmBizNode(tenantId, task, bizNodeCache) : null;
            //获取bizType
            String bizType = getTodoBizType(type, task.getId().toString(), context);
            toTodoList.add(new UpdateCustomToDoInfo(task.getId().toString(), bizType, bizType, recordType, bizNode));
        }
        updatePGTask(type, context, toTodoList);
        updateTodoInfo(toTodoList);
        try {
            Thread.sleep(1000L);
        }catch (Exception e){

        }
    }

    @Data
    @AllArgsConstructor
    class UpdateCustomToDoInfo{
        String sourceId;
        String bizType;
        String todoType;
        String objectBizType;
        String bizNode;
    }

    private void updateTodoInfo(List<UpdateCustomToDoInfo> list){
        if(!SwitchConfigManager.getOpenRefreshData()){
            return;
        }
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        List<AsyncUpdateCustomInfo.UpdateCustomInfoArg> remoteList = list.stream().map(item ->{
            AsyncUpdateCustomInfo.UpdateCustomInfoArg arg = new AsyncUpdateCustomInfo.UpdateCustomInfoArg();
            arg.setSourceId(item.getSourceId());
            arg.setBizType(item.getBizType());
            arg.setTodoType(item.getTodoType());
            arg.setObjectBizType(item.getObjectBizType());
            arg.setBizNode(item.getBizNode());
            return arg;
        }).collect(Collectors.toList());
        AsyncUpdateCustomInfo.Arg remoteArg = new AsyncUpdateCustomInfo.Arg();
        remoteArg.setArgList(remoteList);
        try {
            todoResource.asyncUpdateCustomInfo(remoteArg);
        }catch (Exception e){
            List<String> sourceIdList = remoteList.stream().map(AsyncUpdateCustomInfo.UpdateCustomInfoArg::getSourceId).collect(Collectors.toList());
            log.info("更新待办失败，taskIds：" + ArrayUtils.toString(sourceIdList));
        }
    }

    private String getBpmBizNode(String tenantId, TaskEntity task, Map<String, List<GetAppActions.App>> cache){
        //不是应用节点 类型为审批或业务节点
        if(!ExecutionTypeEnum.externalApplyTask.equals(task.getExecutionType())){
            if (ExecutionTypeEnum.approve.equals(task.getExecutionType())){
                return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.APPROVE_NODE;
            }else {
                return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.NORMAL_NODE;
            }
        }
        List<GetAppActions.App> data;
        String key = task.getEntityId() + "+" + task.getExternalFlowType() + "+" + task.getLinkApp() + "+" + task.getLinkAppType();
        if(cache.containsKey(key)){
            data = cache.get(key);
        }else {
            try {
                GetAppActions.Result appActions = erAppProxy.getAppActions(tenantId, BPMConstants.CRM_SYSTEM_USER, task.getEntityId(), task.getExternalFlowType(), task.getLinkApp(), task.getLinkAppType());
                cache.put(key, appActions.getData());
                data = appActions.getData();
            }catch (Exception e){
                log.info("调深研获取应用节点类型失败，taskId：" + task.getId().toString());
                return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.EXTERNAL_APPLY_NODE;
            }
        }
        //获取appCode actionCode
        String appCode = task.getExternalApplyAppCode();
        String actionCode = task.getExternalApplyActionCode();
        if(CollectionUtils.isEmpty(data) || StringUtils.isBlank(appCode) || StringUtils.isBlank(actionCode)){
            return BPMConstants.BPM_INSTANCE_BUSINESS_CODE.EXTERNAL_APPLY_NODE;
        }
        List<GetAppActions.AppAction> actions = Lists.newArrayList();
        for (GetAppActions.App app : data) {
            if(appCode.equals(app.getAppCode()) && CollectionUtils.isNotEmpty(app.getActions())){
                actions.addAll(app.getActions());
            }
        }
        //<子节点， 根节点>
        Map<String, String> map = Maps.newHashMap();
        for (GetAppActions.AppAction action : actions) {
            String  root = action.getActionCode();
            fillActionCodeMap(action, map, root);
        }

        return StringUtils.isBlank(map.get(actionCode)) || !SwitchConfigManager.getSupportExternalApplyBusinessCode(task.getEntityId()).contains(map.get(actionCode))
                ? BPMConstants.BPM_INSTANCE_BUSINESS_CODE.EXTERNAL_APPLY_NODE
                : map.get(actionCode);
    }

    private void fillActionCodeMap(GetAppActions.AppAction action, Map map, String root){
        if(Objects.isNull(action)){
            return;
        }
        if (CollectionUtils.isEmpty(action.getChildren())){
            if(StringUtils.isNotBlank(action.getActionCode())){
                map.put(action.getActionCode(), root);
            }
            return;
        }
        for (GetAppActions.AppAction child : action.getChildren()) {
            fillActionCodeMap(child, map, root);
        }
    }

    public String getTodoBizType(String type, String taskId, RemoteContext context){
        if(WorkflowConstants.WorkflowType.APPROVAL_FLOW.equals(type)){
            return "452";
        }
        if(WorkflowConstants.WorkflowType.STAGE.equals(type)){
            return "460";
        }
        String bpmBizType = null;
        try {
            Map<String, Object> taskData = newMetadataProxy.findDataById(context, "BpmTask", taskId);
            bpmBizType = (String) taskData.get("session_key");
        }catch (Exception e){
            log.info("获取业务流任务对象sessionKey数据失败，taskId：" + taskId);
        }
        return StringUtils.isBlank(bpmBizType) ? "457" : bpmBizType;
    }

    public void updatePGTask(String type, RemoteContext context, List<UpdateCustomToDoInfo> toTodoList){
        if(CollectionUtils.isEmpty(toTodoList)){
            return;
        }
        String taskType;
        if(WorkflowConstants.WorkflowType.APPROVAL_FLOW.equals(type)){
            taskType = "ApprovalTaskObj";
        }else if(WorkflowConstants.WorkflowType.STAGE.equals(type)){
            taskType = "StageTaskObj";
        }else {
            taskType = "BpmTask";
        }
        toTodoList.removeIf(task ->{
            try {
                TaskPGObj taskObj = new TaskPGObj();
                taskObj.setObject_record_type(task.getObjectBizType());
                if("BpmTask".equals(taskType)){
                    taskObj.setBusiness_code(task.getBizNode());
                }
                Map<String, Object> data = JacksonUtil.fromJsonOfGeneric(JSON.toJSONString(taskObj), Map.class);
                newMetadataProxy.updateData(context, taskType, task.getSourceId(), JacksonUtil.toJson(data),
                        false, true, false, false, false, UUID.randomUUID().toString());
            }catch (Exception e){
                log.info("更新PG中task的object_record_type失败， taskId：" + task.getSourceId());
                return true;
            }
            return false;
        });
    }

    @Data
    class TaskPGObj{
        private String object_record_type;
        private String business_code;
    }

    @Path("/refreshHistoryTodoColumnFilterBatch")
    @GET
    public void refreshHistoryTodoColumnFilterBatch(@QueryParam("tenantId")String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            return;
        }
        String[] tenantIdList = tenantId.split(",");
        if(Objects.isNull(tenantIdList) || tenantIdList.length == 0){
            return;
        }
        log.info("-----------------------******开始批量更新待办信息******-----------------------");
        for (String single: tenantIdList) {
            refreshHistoryTodoColumnFilter(single);
        }
        log.info("-----------------------******结束批量更新待办信息******-----------------------");
    }

}
