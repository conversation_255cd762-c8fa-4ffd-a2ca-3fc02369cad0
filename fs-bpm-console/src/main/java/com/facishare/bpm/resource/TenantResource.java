package com.facishare.bpm.resource;

import com.facishare.bpm.manager.ConsoleButtonSwitchManager;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.resource.model.Result;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.flow.mongo.bizdb.TenantDao;
import com.facishare.flow.mongo.bizdb.entity.TenantEntity;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * Created by wangz on 17-3-13.
 * @IgnoreI18n
 */
@Path("/tenant")
@Service
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class TenantResource {

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private ConsoleButtonSwitchManager consoleButtonSwitchManager;

    @GET
    @Path("all")
    public Result all(@QueryParam("pageNumber") int pageNumber, @QueryParam("pageSize") int pageSize,
                          @QueryParam("tenantId") String tenantId) {
        PageResult<TenantEntity> queryResult = tenantDao.findAllTenantInfos(pageNumber, pageSize, tenantId);
        PageResult<SimpleQuotaInfo> ret = new PageResult<>();
        if (queryResult != null && queryResult.getDataList() != null) {
            ret.setTotal(queryResult.getTotal());
            List<SimpleQuotaInfo> simpleQuotaInfos = Lists.newArrayList();
            queryResult.getDataList().forEach(tenantEntity -> simpleQuotaInfos.add(
                    BeanUtils.transfer(tenantEntity, SimpleQuotaInfo.class, (rsc, dec) ->
                            dec.setCheckQuotaSwitch(consoleButtonSwitchManager.isCheckQuotaSwitch()))));
            ret.setResult(simpleQuotaInfos);
        }

        return new Result(true, ret);
    }

    @GET
    @Path("validate")
    public Result disable(@QueryParam("tenantId") String tenantId, @QueryParam("validate") boolean validate) {
        TenantEntity tenantEntity = tenantDao.updateValidateFlag(tenantId, validate);
        return new Result(true, tenantEntity);
    }

    @Getter
    @Setter
    public static class SimpleQuotaInfo {

        private String tenantId;

        private String ea;

        private String name;

        private int sourceWorkflowCount;

        private int sourceWorkflowQuota;

        private int workflowCount;

        private int workflowInstanceCount;

        private boolean validate = false;

        /**
         * 是否展示 "是否验证配额信息"
         */
        private boolean checkQuotaSwitch;

    }
}
