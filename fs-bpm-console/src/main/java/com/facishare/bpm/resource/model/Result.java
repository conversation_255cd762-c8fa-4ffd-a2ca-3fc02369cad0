package com.facishare.bpm.resource.model;

import lombok.Data;

import javax.ws.rs.core.Response;
import java.io.Serializable;
import java.util.Map;

/**
 * Created by <PERSON> on 17/01/2017.
 */
@Data
public class Result implements Serializable {
    private boolean success;
    private Object result;
    private Map<String,Object> ext;

    public Result(Object result) {
        this.result = result;
        this.setSuccess(true);
    }

    public Result(Object result,Map<String,Object> ext) {
        this.result = result;
        this.setSuccess(true);
        this.ext = ext;
    }

    public Result(boolean success, Object result) {
        this.setResult(result);
        this.setSuccess(success);
    }

    public Result(boolean success) {
        this.setSuccess(success);
    }

    public Result() {
    }
    public Response build(){
        return Response.ok(this).build();
    }
}
