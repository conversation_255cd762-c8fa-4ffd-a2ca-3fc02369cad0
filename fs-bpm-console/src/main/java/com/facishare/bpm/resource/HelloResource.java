package com.facishare.bpm.resource;

import com.facishare.bpm.resource.model.Result;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;

/**
 * Created by <PERSON> on 17/01/2017.
 */
@Path("/function/hello")
@Service
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class HelloResource {

    private static volatile String content="{}";
    static {
        ConfigFactory.getConfig("fs-test-config",(config)-> content=new String(config.getContent()));
    }

    @GET
    @Path("/demo1")
    public Result hello(@QueryParam("ea") String ea) {
        return new Result(JsonUtil.fromJson(content, HashMap.class));
    }

}
