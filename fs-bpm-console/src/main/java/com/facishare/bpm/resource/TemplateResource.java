package com.facishare.bpm.resource;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.resource.model.Result;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTemplateEntity;
import com.facishare.flow.mongo.bizdb.impl.BpmDefinitionSimpleDaoImpl;
import com.facishare.flow.mongo.bizdb.impl.WorkflowTemplateDaoImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 09/01/2017.
 * @IgnoreI18n
 */
@Path("/template")
@Service
public class TemplateResource {
    @Autowired
    private WorkflowTemplateDaoImpl workflowTemplateDao;
    @Autowired
    private BpmDefinitionSimpleDaoImpl workflowOutlineDao;


    @GET
    @Path("disable/{id}/{disable}/{tenantId}")
    @Produces(MediaType.TEXT_PLAIN)
    public boolean disable(@PathParam("id") String id, @PathParam("disable") boolean disable, @PathParam("tenantId") String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            return false;
        }
        workflowTemplateDao.disable(tenantId, id, disable);
        return true;
    }


    @GET
    @Path("delete/{id}/{tenantId}")
    @Produces(MediaType.TEXT_PLAIN)
    public boolean delete(@PathParam("id") String id, @PathParam("tenantId") String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            return false;
        }
        workflowTemplateDao.delete(tenantId, id);
        return true;
    }
    @GET
    @Path("detail/{id}/{tenantId}")
    @Produces(MediaType.APPLICATION_JSON)
    public WorkflowOutline get(@PathParam("id") String id, @PathParam("tenantId") String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            return null;
        }
        WorkflowOutline outline = WorkflowOutline.fromEntity(workflowTemplateDao.find(tenantId, id, true));
        return outline;
    }

    @GET
    @Path("list/{tenantId}")
    @Produces(MediaType.APPLICATION_JSON)
    public PageResult<WorkflowOutline> getList(@PathParam("tenantId") String tenantId) {
        if(StringUtils.isBlank(tenantId)){
            return null;
        }
        Page page = new Page();
        page.setPageNumber(1);
        page.setPageSize(10000);
        page.setAsc(true);
        page.setOrderBy("lastModifiedTime");
        PageResult<WorkflowTemplateEntity> entityResult = workflowTemplateDao.getPageResult(tenantId, page, true);
        PageResult<WorkflowOutline> result = new PageResult<>();
        result.setTotal(entityResult.getTotal());
        result.setDataList(entityResult.getDataList().stream().map(entity -> WorkflowOutline.fromEntity(entity)).collect(Collectors.toList()));
        return result;
    }

    /**
     * 流程转模板
     *
     * @param tenantId
     * @param id
     */
    @GET
    @Path("/processToTemplate/{tenantId}/{id}")
    public Result processToTemplate(@PathParam("tenantId") String tenantId, @PathParam("id") String id) {
        WorkflowOutlineEntity outlineEntity = workflowOutlineDao.find(tenantId, id);
        Result result = new Result(false, "未查询到流程定义");
        if (outlineEntity == null) {
            return result;
        }
        WorkflowTemplateEntity templateEntity = BeanUtils.transfer(outlineEntity, WorkflowTemplateEntity.class);
        templateEntity.clearNoUseProperties();
        WorkflowTemplateEntity wte = workflowTemplateDao.createOrUpdate(tenantId, templateEntity);
        if (null == wte) {
            result.setResult("流程定义转换失败");
            return result;
        }
        return new Result(true, "定义转换成功");
    }

}
