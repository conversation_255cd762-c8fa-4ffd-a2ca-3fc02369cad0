package com.facishare.bpm.resource.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON> on 17/01/2017.
 */
@Data
public class PageResult extends Result implements Serializable {
    private int total;
    private int pageNum;
    private int totalPage;
    private int pageSize;
    private boolean definitionOperationSwitch;

    public PageResult(List result, int total, int pageNum, int pageSize) {
        super(result);
        this.setPageNum(pageNum);
        this.setPageSize(pageSize);
        this.setTotal(total);
        int temp = (total / pageSize);
        int real = total % pageSize;
        this.setTotalPage(real > 0 ? temp + 1 : temp);
    }

    public PageResult(List result, int total, int pageNum, int pageSize, boolean definitionOperationSwitch) {
        super(result);
        this.setPageNum(pageNum);
        this.setPageSize(pageSize);
        this.setTotal(total);
        int temp = (total / pageSize);
        int real = total % pageSize;
        this.setTotalPage(real > 0 ? temp + 1 : temp);
        this.definitionOperationSwitch = definitionOperationSwitch;
    }
}
