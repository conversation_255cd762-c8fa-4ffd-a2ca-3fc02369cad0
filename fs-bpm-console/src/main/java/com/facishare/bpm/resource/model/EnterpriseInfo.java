package com.facishare.bpm.resource.model;

import com.facishare.fsi.proxy.model.global.config.GetEnterpriseConfigByEI;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Map;
import java.util.Objects;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/14 5:25 PM
 * @IgnoreI18n
 */
@Data
public class EnterpriseInfo {

    private String tenantId;
    private String ea;
    private String name;
    private String licenseId;
    private String productId;
    private String productType;//0-主版本，1-资源包，2-行业套件，3-应用
    private String productName;
    private String currentVersion;
    private String versionName;
    private String orderNumber;//订单号
    private Integer maxHeadCount;//企业员工数
    private String trialFlag;//试用标识
    private String startTime;
    private String expiredTime;


    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static Map<String, String> productTypes = Maps.newHashMap();
    private static Map<String, String> currentVersionType = Maps.newHashMap();

    static {
        productTypes.put("0", "主版本");
        productTypes.put("1", "资源包");
        productTypes.put("2", "行业套件");
        productTypes.put("3", "应用");

        //==============
        currentVersionType.put("enterprise_edition", "集团版（无限版）");
        currentVersionType.put("wechat_standard_edition", "企业微信版（标准版）");
        currentVersionType.put("kdweibo_edition", "云之家版");
        currentVersionType.put("kis_edition", "KIS版");
        currentVersionType.put("jdy_edition", "精斗云");
        currentVersionType.put("dealer_edition", "经销商版");
        currentVersionType.put("strengthen_edition", "旗舰版");
        currentVersionType.put("standardpro_edition", "专业版");
        currentVersionType.put("wechat_standardpro_edition", "企业微信版（专业版）");
        currentVersionType.put("promotion_sales_edition", "访销版");
        currentVersionType.put("agent_edition", "代理商版");
        currentVersionType.put("manufacture_dealer_edition", "制造业经销商版 ");
    }


    public static EnterpriseInfo transfer(ProductVersionPojo crmVersion, GetEnterpriseConfigByEI.EnterpriseConfig enterpriseConfigByEI) {
        EnterpriseInfo info = new EnterpriseInfo();
        info.setTenantId(crmVersion.getTenantId());
        info.setLicenseId(crmVersion.getLicenseId());
        info.setProductId(crmVersion.getProductId());
        info.setProductName(crmVersion.getProductName());

        if (!Strings.isNullOrEmpty(productTypes.get(crmVersion.getProductType()))) {
            info.setProductType(productTypes.get(crmVersion.getProductType()));
        } else {
            info.setProductType(crmVersion.getProductType());
        }


        if (!Strings.isNullOrEmpty(currentVersionType.get(crmVersion.getCurrentVersion()))) {
            info.setCurrentVersion(currentVersionType.get(crmVersion.getCurrentVersion()));
        } else {
            info.setCurrentVersion(crmVersion.getCurrentVersion());
        }


        info.setVersionName(crmVersion.getVersionName());
        info.setOrderNumber(crmVersion.getOrderNumber());
        info.setMaxHeadCount(crmVersion.getMaxHeadCount());
        info.setTrialFlag(crmVersion.isTrialFlag() ? "试用版" : "正式版(非试用版)");
        info.setStartTime(Objects.isNull(crmVersion.getStartTime()) ? "" : sdf.format(crmVersion.getStartTime()));
        info.setExpiredTime(Objects.isNull(crmVersion.getExpiredTime()) ? "" : sdf.format(crmVersion.getExpiredTime()));

        //=============================
        info.setEa(enterpriseConfigByEI.EnterpriseAccount);
        info.setName(enterpriseConfigByEI.EnterpriseName);


        return info;
    }
}
