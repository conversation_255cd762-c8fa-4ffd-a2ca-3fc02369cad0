package com.facishare.bpm.resource;

import com.alibaba.fastjson.JSON;
import com.facishare.bpm.manager.BpmDataToPGManager;
import com.facishare.bpm.manager.MongoManager;
import com.facishare.bpm.manager.PGDBManager;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.resource.model.Result;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.ParallelUtils;
import com.facishare.paas.pod.client.PodClient;
import com.facishare.paas.pod.entity.TenantRoute;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.fxiaoke.i18n.client.I18nClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jetbrick.util.JSONUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * Created by wangz on 17-8-14.
 * @IgnoreI18n
 */
@Path("/metadata")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Service
@Slf4j
public class BpmMetadataResource {

    private static final String FIELD_ID = "_id";

    public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";

    @PostConstruct
    public void initI18N(){
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);
    }

    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;
    @Autowired
    private PaasWorkflowServiceProxy workflowServiceProxy;
    @Resource
    private PodClient podClient;
    @Autowired
    private PGDBManager pgdbManager;
    @Autowired
    private MongoManager mongoManager;

    @Autowired
    private WorkflowTransferService workflowTransferService;

    @Autowired
    private BpmDataToPGManager bpmDataToPGManager;


    @POST
    @Path("create/instance/{tenantId}")
    public Result createInstance(@PathParam("tenantId") String tenantId, String instanceJson) {
        try {
            verify(instanceJson);
            metadataService.createData(getContext(tenantId), BPMConstants.BPM_INSTANCE, instanceJson);
            return new Result(true);
        } catch (Exception e) {
            log.info("createInstance error : TENANT_ID={}, INSTANCE_JSON={}, ", tenantId, instanceJson, e);
            return new Result(false, e.getMessage());
        }
    }

    private void verify(String instanceJson) {
        Map<String, Object> data = JSON.parseObject(instanceJson, HashMap.class);
        String id = (String) data.get(FIELD_ID);
        if (Strings.isNullOrEmpty(id)) {
            throw new RuntimeException("id is null");
        }
    }

    @POST
    @Path("/update/instance/{tenantId}")
    public Result updateInstance(@PathParam("tenantId") String tenantId, String instanceJson) {
        try {
            verify(instanceJson);
            String id = (String) JSON.parseObject(instanceJson, HashMap.class).get(FIELD_ID);
            metadataService.updateData(getContext(tenantId), BPMConstants.BPM_INSTANCE, id, instanceJson, true, Boolean.FALSE);
            return new Result(true);
        } catch (Exception e) {
            log.info("updateInstance error : TENANT_ID={}, INSTANCE_JSON={}, ", tenantId, instanceJson, e);
            return new Result(false, e.getMessage());
        }
    }


    @POST
    @Path("/create/task/{tenantId}")
    public Result createTask(@PathParam("tenantId") String tenantId, String taskJson) {
        try {
            verify(taskJson);
            metadataService.createData(getContext(tenantId), BPMConstants.BPM_TASK, taskJson);
            return new Result(true);
        } catch (Exception e) {
            log.info("createTask error : TENANT_ID={}, INSTANCE_JSON={}, ", tenantId, taskJson, e);
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/update/task/{tenantId}")
    public Result updateTask(@PathParam("tenantId") String tenantId, String taskJson) {
        try {
            verify(taskJson);
            String id = (String) JSON.parseObject(taskJson, HashMap.class).get(FIELD_ID);
            metadataService.updateData(getContext(tenantId), BPMConstants.BPM_TASK, id, taskJson, true, Boolean.FALSE);
            return new Result(true);
        } catch (Exception e) {
            log.info("updateTask error : TENANT_ID={}, INSTANCE_JSON={}, ", tenantId, taskJson, e);
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/{tenantId}/transfer/instance")
    public Result fixWorkflowData(@PathParam("tenantId") String tenantId, String ids) {
        try {
            RemoteContext context = getContext(tenantId);
            String[] instanceArr = ids.split(",");
            List<String> instanceIds = Lists.newArrayList(instanceArr);
            if (CollectionUtils.isEmpty(instanceIds)) {
                return new Result(false, "ids is null");
            }
            log.info("transfer instance start : TENANT_ID={}, INSTANCE_IDS={}", tenantId, JSONUtils.toJSONString
                    (instanceIds));
            List<WorkflowInstance> instances = workflowServiceProxy.getWorkflowInstances(getContext(tenantId), null,
                    null, instanceIds, null, null, null, new Page(100, 1, null, true), null).getDataList();
            List<String> errorInstanceIds = Lists.newArrayList();
            if (instances != null) {
                instances.forEach(instance -> {
                    try {
                        workflowTransferService.transfer(context, instance.getId());
                    } catch (Exception e) {
                        errorInstanceIds.add(instance.getId());
                    }
                });
            }

            if (CollectionUtils.isEmpty(errorInstanceIds)) {
                return new Result(true);
            } else {
                log.error("transfer instance error. mq send failed！ TENANT_ID={}, INSTANCEs_ID={}, ",
                        tenantId, errorInstanceIds);
                return new Result(false, "部分数据处理失败");
            }

        } catch (Exception e) {
            log.info("transfer instance error : get workflowInstance faield. TENANT_ID={}, ",
                    tenantId, e);
            return new Result(false, e.getMessage());
        }
    }


    @POST
    @Path("/{tenantId}/instance/topg")
    public Result instance2pg(@PathParam("tenantId") String tenantId, String instanceIdStr) {
        try {
            RemoteContext context = getContext(tenantId);
            String[] instanceArr = instanceIdStr.split(",");
            List<String> instanceIds = Lists.newArrayList(instanceArr);
            if (CollectionUtils.isEmpty(instanceIds)) {
                return new Result(false, "ids is null");
            }
            for (String instanceId : instanceIds) {
                try {
                    bpmDataToPGManager.findAndModifyInstance(context, workflowServiceProxy.getWorkflowInstance(context, instanceId), bpmDataToPGManager.getCurrentCandidateIds(context, instanceId));
                }catch (Exception e){
                    log.error("",e);
                }
            }
            return new Result(true, "执行完毕");
        } catch (Exception e) {
            log.info("transfer instance error : get workflowInstance faield. TENANT_ID={}, ",
                    tenantId, e);
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/{tenantId}/task/topg")
    public Result task2PG(@PathParam("tenantId") String tenantId, String taskIdStr) {
        try {
            RemoteContext context = getContext(tenantId);
            String[] taskArr = taskIdStr.split(",");
            List<String> taskIds = Lists.newArrayList(taskArr);
            if (CollectionUtils.isEmpty(taskIds)) {
                return new Result(false, "ids is null");
            }
            Map<String,String> error = Maps.newHashMap();
            for (String taskId : taskIds) {
                if (Strings.isNullOrEmpty(taskId)||"\n".equals(taskId)) {
                    continue;
                }
                try {
                    bpmDataToPGManager.findAndModifyTask(context, workflowServiceProxy.getTask(context, taskId));
                }catch (Exception e){
                    log.error("",e);
                    error.put(taskId,e.getMessage());
                }
            }
            return new Result(true,error);
        } catch (Exception e) {
            log.info("transfer instance error : get workflowInstance faield. TENANT_ID={}, ",
                    tenantId, e);
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/{tenantId}/cancel/task")
    public Result cancelByTask(@PathParam("tenantId") String tenantId, String taskIdStr) {
        try {
            RemoteContext context = getContext(tenantId);
            String[] taskArr = taskIdStr.split(",");
            List<String> taskIds = Lists.newArrayList(taskArr);
            if (CollectionUtils.isEmpty(taskIds)) {
                return new Result(false, "ids is null");
            }
            Map<String,String> error = Maps.newHashMap();
            for (String taskId : taskIds) {
                if (Strings.isNullOrEmpty(taskId)) {
                    continue;
                }
                try {
                    Task task = workflowServiceProxy.getTask(context, taskId);
                    workflowServiceProxy.cancel(context,task.getWorkflowInstanceId(),"自动终止流程-system");
                }catch (Exception e){
                    log.error("",e);
                    error.put(taskId,e.getMessage());
                }
            }
            return new Result(true,error);
        } catch (Exception e) {
            log.info("transfer instance error : get workflowInstance faield. TENANT_ID={}, ",
                    tenantId, e);
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/{tenantId}/transfer/tasks")
    public Result transferTask(@PathParam("tenantId") String tenantId, String taskIdStrs) {
        RemoteContext context = getContext(tenantId);
        try {
            List<String> taskIds = Lists.newArrayList(taskIdStrs.split(","));
            if (CollectionUtils.isEmpty(taskIds)) {
                return new Result(false, "ids is null");
            }

            Set<String> instanceIds = taskIds.stream().map(id -> {
                Task task = workflowServiceProxy.getTask(getContext(tenantId), id);
                if (task != null) {
                    return task.getWorkflowInstanceId();
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toSet());
            List<String> errorInstanceIds = Lists.newArrayList();
            if (instanceIds != null) {
                List<WorkflowInstance> instances = workflowServiceProxy.getWorkflowInstances(getContext(tenantId), null,
                        null, instanceIds, null, null, null, new Page(100, 1, null, true), null).getDataList();
                instances.stream().filter(item -> item.getTriggerSource() != null).forEach(instance -> {
                    try {
                        workflowTransferService.transfer(context, instance.getId());
                    } catch (Exception e) {
                        errorInstanceIds.add(instance.getId()+","+e.getMessage());
                    }
                });
            }

            if (CollectionUtils.isEmpty(errorInstanceIds)) {
                return new Result(true);
            } else {
                log.error("transfer instance error. mq send failed！ TENANT_ID={}, INSTANCEs_ID={}, ",
                        tenantId, errorInstanceIds);
                return new Result(false, "部分数据处理失败\n"+ JacksonUtil.toJson(errorInstanceIds));
            }

        } catch (Exception e) {
            log.info("transfer instance error : get workflowInstance faield. TENANT_ID={}, ",
                    tenantId, e);
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/db/count/{apiName}")
    public Result compareCountFromPGAndWorkflow(Map<String, String> tenantIds, @PathParam("apiName") String apiName) {
        String[] tenantIdArr = tenantIds.get("tenantIds").split(",");
        if (tenantIdArr.length == 0) {
            return new Result(true);
        }

        log.info("compareCountFromPGAndWorkflow arg : {}", tenantIds);
        List<TenantRoute> tenantRoutes = podClient.getResourceByTenantIds(Lists.newArrayList(tenantIdArr));
        log.info("compareCountFromPGAndWorkflow : tenantRoutes = {}", tenantRoutes);
        Map<String, Integer> pgDataCount = Maps.newConcurrentMap();
        Map<String, Integer> mongoDataCount = Maps.newConcurrentMap();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        tenantRoutes.forEach(tenantRoute -> {
                    parallelTask.submit(Thread.currentThread().getName() + "", new PGTask(pgdbManager, tenantRoute.getTenantId(), tenantRoute.getIp(),
                            tenantRoute.getDataBaseName(), apiName, pgDataCount));
                    parallelTask.submit(Thread.currentThread().getName() + "", new MongoTask(mongoManager, tenantRoute.getTenantId(), apiName, mongoDataCount));
                }
        );

        try {
            parallelTask.await(10, TimeUnit.SECONDS);
            Map<String, String> ret = tenantRoutes.stream().collect(Collectors.toMap(TenantRoute::getTenantId, t ->
                    "pg=" + pgDataCount.get(t.getTenantId()) + ",mongo=" + mongoDataCount.get(t.getTenantId())));
            return new Result(true, ret);
        } catch (TimeoutException e) {
            log.error("compareCountFromPGAndWorkflow task time out!");
            return new Result(false, e.getMessage());
        }
    }

    @POST
    @Path("/db/route/{tenantId}")
    public Result getPGDBRoute(@PathParam("tenantId") String tenantId) {
        String resource = podClient.getResource(tenantId, "CRM", "CRM", "pg");
        List<TenantRoute> tenantRoutes = podClient.getResourceByTenantIds(Lists.newArrayList(tenantId));

        Map<String, Integer> ret = Maps.newHashMap();
        log.info("getPGDBRoute ret : route = {}, tenantRoutes={}", resource, tenantRoutes);
        new Thread(new PGTask(pgdbManager, tenantId, "172.17.56.10:5432", "fsdb010002", "BpmInstance", ret)).start();
        return new Result(ret);
    }

    @POST
    @Path("/bpm/verify/currentEntityDescribe")
    public Result verifyBpmPGDesc(String tenantIdsJson) {
        List<String> tenantIds;
        try {
            tenantIds = JSON.parseArray(tenantIdsJson, String.class);
        } catch (Exception e) {
            log.info("对象匹配参数错误, tenantIdsJson={}", tenantIdsJson, e);
            return new Result(false, "params format error");
        }

        try {
            Map<String, Object> standardInstanceDesc = getStandardBpmInstance();
            Map<String, Object> standardTaskDesc = getStandardBpmTask();

            List<String> errorMsg = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(tenantIds)) {
                for (String tenantId : tenantIds) {
                    String instanceError = verifyDesc(tenantId, "BpmInstance", standardInstanceDesc);
                    String taskError = verifyDesc(tenantId, "BpmTask", standardTaskDesc);

                    if (instanceError != null) {
                        errorMsg.add(instanceError);
                        errorMsg.add(taskError);
                    }
                }
            }

            if (errorMsg.size() > 0) {
                log.error("对象描述有误 : {}", errorMsg);
            }

            return new Result(true, errorMsg.toString());
        } catch (Exception e) {
            log.error("对象匹配程序错误 : ", e);
            return new Result(false, e.getMessage());
        }
    }

    private String verifyDesc(String tenantId, String descApiName, Map<String, Object> sourceDesc) {
        Map<String, Object> targetDesc = metadataService.findDescribe(getContext(tenantId), descApiName, true, true);
        if (targetDesc == null) {
            log.error("企业 {} 的 {} 对象 没有同步到元数据", tenantId, descApiName);
            return "tenantId=" + tenantId + "没有对象=" + descApiName;
        }
        return verifyDesc(tenantId, descApiName, sourceDesc, targetDesc);
    }

    private String verifyDesc(String tenantId, String descApiName, Map<String, Object> sourceDesc, Map<String, Object>
            targetDesc) {
        Map<String, Object> sourceFieldsDesc = MapUtil.instance.getMapOfGeneric(sourceDesc, BPMConstants.MetadataKey.fields); // (Map<String, Object>) sourceDesc.get("fields");
        Map<String, Object> targetFieldsDesc = MapUtil.instance.getMapOfGeneric(targetDesc, BPMConstants.MetadataKey.fields); //(Map<String, Object>) targetDesc.get("fields");

        for (Map.Entry<String, Object> field : sourceFieldsDesc.entrySet()) {
            String apiName = field.getKey();
            Map<String, Object> sourceField = (Map<String, Object>) field.getValue();
            Map<String, Object> targetField = MapUtil.instance.getMapOfGeneric(targetFieldsDesc, apiName);//  (Map<String, Object>) targetFieldsDesc.get(apiName);

            if (targetField == null) {
                return "tenantId=" + tenantId + "的对象" + descApiName + "的字段" + apiName + "不存在";
            }

            if (!sourceField.get("api_name").equals(targetField.get("api_name"))) {
                return "tenantId=" + tenantId + "的对象" + descApiName + "的字段" + apiName + "匹配失败";
            }

            if (!sourceField.get("type").equals(targetField.get("type"))) {
                return "tenantId=" + tenantId + "的对象" + descApiName + "的字段" + apiName + "的type匹配失败";
            }

            if (!sourceField.get("label").equals(targetField.get("label"))) {
                return "tenantId=" + tenantId + "的对象" + descApiName + "的字段" + apiName + "的label匹配失败";
            }
        }

        return null;
    }

    private Map<String, Object> getStandardBpmInstance() throws IOException {
        String jsonStr = getFromFile(getClass().getResource("/").getPath() + "doc/BpmInstance.json");
        return JSON.parseObject(jsonStr, HashMap.class);
    }

    private Map<String, Object> getStandardBpmTask() throws IOException {
        String jsonStr = getFromFile(getClass().getResource("/").getPath() + "doc/BpmTask.json");
        return JSON.parseObject(jsonStr, HashMap.class);
    }

    private String getFromFile(String path) throws IOException {
        return FileUtils.readFileToString(new File(path));
    }

    private RemoteContext getContext(String tenantId) {
        return new RemoteContext("", tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
    }

    @Getter
    @Setter
    static class PGTask implements Runnable {
        private String tenantId;
        private String dbIp;
        private String dbName;
        private PGDBManager pgdbManager;
        private String type;
        private Map<String, Integer> resultMap;

        PGTask(PGDBManager pgdbManager, String tenantId, String dbIp, String dbName, String apiName, Map<String,
                Integer> resultMap) {
            this.tenantId = tenantId;
            this.dbIp = dbIp;
            this.dbName = dbName;
            this.pgdbManager = pgdbManager;
            this.type = apiName;
            this.resultMap = resultMap;
        }

        @Override
        public void run() {
            String dbUrl = getPGUrl();
            Integer pgDataCount = pgdbManager.queryDataCount(dbUrl, tenantId, type);
            resultMap.put(tenantId, pgDataCount == null ? 0 : pgDataCount);
        }

        private String getPGUrl() {
            return "jdbc:postgresql://" + getDbIp() + "/" + getDbName();
        }
    }

    @Getter
    @Setter
    static class MongoTask implements Runnable {
        private MongoManager mongoManager;
        private Map<String, Integer> resultMap;
        private String tenantId;
        private String type;

        MongoTask(MongoManager mongoManager, String tenantId, String type, Map<String, Integer>
                resultMap) {
            this.tenantId = tenantId;
            this.mongoManager = mongoManager;
            this.type = type;
            this.resultMap = resultMap;
        }

        @Override
        public void run() {
            resultMap.put(tenantId, mongoManager.getDataCount(tenantId, type));
        }
    }
}
