package com.facishare.bpm.resource.model;

import com.facishare.bpm.utils.DateUtil;
import com.google.common.base.Splitter;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangzhx on 2019/7/9.
 */
@Data
public class InvalidObjectTask {
    /**
     * 写redis的状态
     * 1. loading 正在写
     * 2. over 已写完
     */
    private String status;
    /**
     * 企业下所有的task
     */
    private Integer allTasks;
    /**
     * 当前已查询的task数据信息计数
     */
//    private Integer currentTasks;
    /**
     * 已作废数据还存在进行中的task统计
     */
    private Long invalidTasks;

    /**
     * 无效实例
     */
    private List<String> invalidInstanceList;

    /**
     * 作废数据及作废时间
     * 已删除不显示时间
     */
    private List<ObjectInfo> invalidObjectInfoList;

    public void setInvalidObjectInfoList(List<String> invalidObjectInfoList) {
        if (CollectionUtils.isEmpty(invalidObjectInfoList)) {
            return;
        }
        this.invalidObjectInfoList = invalidObjectInfoList.stream().filter(StringUtils::isNotBlank).map(
                invalidObjectInfo -> {
                    ObjectInfo info = new ObjectInfo();
                    List<String> list = Splitter.on(":").splitToList(invalidObjectInfo);
                    switch (list.size()) {
                        case 1:
                            info.setEntityId(list.get(0));
                            break;
                        case 2:
                            info.setEntityId(list.get(0));
                            info.setObjectId(list.get(1));
                            break;
                        case 3:
                            info.setEntityId(list.get(0));
                            info.setObjectId(list.get(1));
                            info.setInstanceId(list.get(2));
                            break;
                        case 4:
                            info.setEntityId(list.get(0));
                            info.setObjectId(list.get(1));
                            info.setInstanceId(list.get(2));
                            Date date = new Date(Long.valueOf(list.get(3)));
                            info.setInvalidTime(DateUtil.instance.format(date));
                            break;
                    }
                    return info;
                }).collect(Collectors.toList());
    }

    @Data
    private class ObjectInfo {
        private String entityId;
        private String objectId;
        private String instanceId;
        private String invalidTime;
    }
}
