package com.facishare.bpm.resource;

import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.resource.paas.privilege.CreateBaseDataPrivilege;
import com.facishare.bpm.proxy.DataPrivilegeProxy;
import com.facishare.bpm.proxy.GDSProxy;
import com.facishare.bpm.resource.model.Result;
import com.facishare.flow.mongo.bizdb.TenantDao;
import com.facishare.flow.mongo.bizdb.entity.TenantEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * @IgnoreI18n
 */
@Path("/special")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Service
@Slf4j
public class SpecialResource {

    @Autowired
    private GDSProxy gdsProxy;

    @Autowired
    private DataPrivilegeProxy dataPrivilegeProxy;

    @Autowired
    private TenantDao tenantDao;

    @Path("/create/base/privilege/{entityId}/{tenantId}")
    @GET
    public Result createBasePrivilege(@QueryParam("ea") String ea, @PathParam("entityId") String entityId, @PathParam("tenantId") String tenantId) {
        if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(entityId)) {
            Map<String, String> map = Maps.newHashMap();
            map.put("message", "企业id或者对象Id为空");
            return new Result(map);
        }
        RemoteContext context = initContext(ea, tenantId);
        try {
            return new Result(dataPrivilegeProxy.createBaseDataPrivilege(context, entityId, null));
        } catch (BPMBusinessException e) {
            return new Result(Maps.newHashMap());
        }
    }


    @Path("/create/base/privilege/all/{entityId}")
    @GET
    public Result createBasePrivilege(@QueryParam("pageNumber") int pageNumber, @QueryParam("pageSize") int pageSize,
                                      @QueryParam("ea") String ea, @PathParam("entityId") String entityId) {
        Map<String, String> map = Maps.newHashMap();
        if (Strings.isNullOrEmpty(entityId)) {
            map.put("message", "对象Id为空");
            return new Result(map);
        }

        List<TenantEntity> errorTenant = Lists.newArrayList();

        PageResult<TenantEntity> queryResult = tenantDao.findAllTenantInfos(pageNumber, pageSize, null);

        if (queryResult != null && queryResult.getDataList() != null) {
            queryResult.getDataList().forEach(tenantEntity -> {
                RemoteContext context = initContext(ea, tenantEntity.getTenantId());
                try {
                    CreateBaseDataPrivilege.Result rst = (dataPrivilegeProxy.createBaseDataPrivilege(context, entityId, null));
                    if (rst.getErrCode() != 0) {
                        errorTenant.add(tenantEntity);
                    }
                } catch (Exception e) {
                    errorTenant.add(tenantEntity);
                }
            });
        } else {
            map.put("message", "为获取到企业信息");
            return new Result(map);
        }
        return new Result(errorTenant);
    }


    private RemoteContext initContext(String ea, String tenantId) {
        if (Strings.isNullOrEmpty(tenantId) && !Strings.isNullOrEmpty(ea)) {
            tenantId = gdsProxy.getEnterpriseConfigByEA(ea).EnterpriseID + "";
        }
        return new RemoteContext(null, tenantId, BPMConstants.CRM_APP_ID, BPMConstants.CRM_SYSTEM_USER);
    }

}
