package com.facishare.bpm.resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by <PERSON> on 17/01/2017.
 */
@Path("/common")
@Service
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Slf4j
public class CommonConsoleResource {


    @Path("/ping")
    @GET
    public String ping() {

        return "ok";
    }

}
