package com.facishare.bpm.resource;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.proxy.GDSProxy;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.RestConstant;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date on 2018/5/22
 * @since 6.3
 * @IgnoreI18n
 */
@Slf4j
@Service
public class BaseResource extends BPMBaseService {


    public Map<String, String> getMemberNames(RemoteContext context, List<Object> userIds) {
        Map<Integer, Employee> members = getServiceManager(context).getMembersByIds(userIds);
        Employee employee = new Employee();
        employee.setName("系统");
        members.put(Integer.parseInt(BPMConstants.CRM_SYSTEM_USER), employee);

        Map<String, String> rst = Maps.newHashMap();

        userIds.forEach(item -> {
            Integer id = Integer.valueOf(item.toString());
            Employee member = members.get(id);
            if (member == null) {
                rst.put(item.toString(), "--");
            } else {
                rst.put(item.toString(), member.getName());
            }
        });
        return rst;
    }

    @Autowired
    private GDSProxy gdsProxy;

    protected RemoteContext initContext(String ea, String tenantId, String userId) {
        if (Strings.isNullOrEmpty(tenantId) && !Strings.isNullOrEmpty(ea)) {
            tenantId = gdsProxy.getEnterpriseConfigByEA(ea).EnterpriseID + "";
        }
        return new RemoteContext(null, tenantId, BPMConstants.APP_ID, userId);
    }

    protected Map<String, String> getHeaders(RemoteContext context) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-userInfo", context.getUserId());
        headers.put("x-fs-ei", context.getTenantId());
        headers.put(RestConstant.GRAY_VALUE, context.getTenantId());
        return headers;
    }

    protected Map<String, String> getObjectNames(RemoteContext context, String entityId, Collection<String> objectIds) {
        List<Pair<String, String>> rst = objectIds.stream().map(item -> {
            Pair<String, String> pair = new Pair(entityId, item);
            return pair;
        }).collect(Collectors.toList());
        return getServiceManager(context).getPaaSObjectNames(rst);
    }

    protected Map<String, String> getObjectNames(RemoteContext context, Map<String, Set<String>> entityIdObjectIds) {
        Map<String, String> rst = Maps.newConcurrentMap();
        entityIdObjectIds.forEach((key, value) -> {
            Map<String, String> names = getObjectNames(context, key, value);
            log.info("getObjectNames:{}", names);
            rst.putAll(names);
        });
        return rst;
    }
}
