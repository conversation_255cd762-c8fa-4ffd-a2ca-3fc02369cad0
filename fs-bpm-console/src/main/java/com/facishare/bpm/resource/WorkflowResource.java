package com.facishare.bpm.resource;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.manage.DefineGenerateManager;
import com.facishare.bpm.manager.ConsoleButtonSwitchManager;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Page;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.resource.model.PageResult;
import com.facishare.bpm.resource.model.Result;
import com.facishare.bpm.service.WorkflowInitialService;
import com.facishare.bpm.service.impl.BPMDefinitionServiceImpl;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.util.WorkflowJsonUtil;
import com.facishare.bpm.util.verifiy.VerifyManager;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.util.PreNodesUtil;
import com.facishare.bpm.util.verifiy.util.PreNodesUtil2;
import com.facishare.bpm.utils.SpringUtils;
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.FlowExtensionEntity;
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseEnvironment;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 17/01/2017.
 * @IgnoreI18n
 */
@Path("/workflow")
@Service
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Slf4j
public class WorkflowResource extends BaseResource {
    @Autowired
    private BPMDefinitionServiceImpl service;
    @Autowired
    private BpmSimpleDefinitionDao outlineDao;
    @Autowired
    private DefinitionExtensionDao extensionDao;
    @Autowired
    private DefineGenerateManager defineGenerateManager;
    @Autowired
    private ConsoleButtonSwitchManager consoleButtonSwitchManager;

    @Autowired
    private WorkflowInitialService workflowInitialService;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;


    @Path("/{id}")
    @GET
    public Result get(@QueryParam("tenantId") String tenantId, @PathParam("id") String id) {
        WorkflowOutlineEntity workflow = outlineDao.find(tenantId, id);
        return new Result(WorkflowOutline.fromEntity(workflow));
    }

    @Path("/workflow/{id}")
    @GET
    public Result findWorkflow(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @PathParam("id") String id) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        return new Result(true, service.getWorkflowById(getServiceManager(context), id));
    }


    /**
     * id = workflowId
     *
     * @param ea
     * @param id
     * @param tenantId
     * @return
     */
    @Path("/svg/{id}")
    @GET
    public Result instanceSVG(@QueryParam("ea") String ea, @PathParam("id") String id, @QueryParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        try {
            WorkflowExtension extension = service.getWorkflowExtensionByWorkflowId(context, id);
            return new Result(true, extension);
        } catch (BPMBusinessException e) {
            return new Result(Maps.newHashMap());
        }
    }

    @Path("/define/gen")
    @GET
    public Result defineGen(@QueryParam("tenantId") String tenantId) {
        RemoteContext context = new RemoteContext(null, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        List<WorkflowOutlineEntity> outlineEntities = outlineDao.findAll(context.getTenantId());
        log.info("{} 企业下共计流程定义:{} 条", tenantId, outlineEntities.size());

        long time = 0;
        if (outlineEntities.size() > 10) {
            time = 500;
        }
        long finalTime = time;
        new Thread(() -> outlineEntities.forEach(item -> {
            try {
                WorkflowOutline rst = WorkflowOutline.fromEntity(item);
                defineGenerateManager.generateVariables(rst, getServiceManager(context));
                Thread.sleep(finalTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        })).start();
        return new Result(true, "执行完成,请到后台检查日志");
    }

    @Path("/validate/{sourceWorkflowId}")
    @GET
    public Result validate(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @PathParam("sourceWorkflowId") String id) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        WorkflowOutline outline = service.getWorkflowOutlineBySourceId(getServiceManager(context), id);
        Workflow workflow = new Workflow(
                getServiceManager(context),
                outline.getName(),
                outline.getEntryType(),
                outline.getEntryTypeName(),
                outline.getWorkflow(),
                outline.getRule(),
                JacksonUtil.toJson(outline.getExtension()));
        try {
            VerifyManager.instance.execute(workflow);
        } catch (BPMWorkflowDefVerifyException e) {
            return new Result(false, e.getMessage());
        }
        return new Result(true, "校验没有问题");
    }

    @Path("/preActivities/{id}")
    @GET
    public Result preActivities(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @PathParam("id") String id) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        ExecutableWorkflowExt workflow = service.getWorkflowById(getServiceManager(context), id);
        long t1 = System.currentTimeMillis();
        Map<String, List<ActivityExt>> old = Maps.newHashMap();
        if (workflow.getActivities().size() < 30) {
            old = PreNodesUtil.getPreActivityMap(workflow);
        }
        long t2 = System.currentTimeMillis();
        Map<String, List<ActivityExt>> new1 = PreNodesUtil2.getPreActivities(workflow);
        long t3 = System.currentTimeMillis();
        Map<String, Object> rst = Maps.newHashMap();
        Map<String, ActivityExt> activityMaps = workflow.getActivityMaps();
        if (MapUtils.isNotEmpty(old)) {
            old.forEach((activityId, activityExts) -> {
                Map<String, List<String>> temp = Maps.newHashMap();
                temp.put("old", activityExts.stream().map(item -> item.getName() + "(" + item.getId() + ")").sorted().collect(Collectors.toList()));
                temp.put("new", new1.get(activityId).stream().map(item -> item.getName() + "(" + item.getId() + ")").sorted().collect(Collectors.toList()));
                rst.put(activityMaps.get(activityId).getName() + (activityId), temp);
            });
        } else {
            new1.forEach((activityId, activityExts) -> {
                rst.put(activityId, new1.get(activityId).stream().map(item -> item.getName() + "(" + item.getId() + ")").sorted().collect(Collectors.toList()));
            });
        }


        rst.put("oldTime", MapUtils.isEmpty(old) ? "太多,不计算了" : (t2 - t1) + "ms");
        rst.put("newTime", (t3 - t2) + "ms");
        return new Result(true, rst);
    }

    @Path("/delete/{id}")
    @GET
    public Result delete(@QueryParam("tenantId") String tenantId, @PathParam("id") String id) {
        outlineDao.delete(tenantId, BPMConstants.CRM_SYSTEM_USER, id);

        return new Result(true);
    }

    @Path("/list/all")
    @GET
    public Result list(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @QueryParam("page") Integer pageNum, @QueryParam("size") Integer size) {
        if (StringUtils.isBlank(ea) && StringUtils.isBlank(tenantId)) {
            return new PageResult(Lists.newArrayList(), 0, pageNum, size);
        }
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        Page page = new Page();
        if (pageNum == null) {
            pageNum = 1;
        }
        if (size == null) {
            size = 10;
        }
        page.setPageNumber(pageNum);
        page.setPageSize(size);
        page.setOrderBy("lastModifiedTime");
        page.setAsc(false);
        List<Object> userIds = Lists.newArrayList();
        List<WorkflowOutline> outlines = outlineDao.findAll(context.getTenantId()).stream().map(item -> {
            WorkflowOutline rst = WorkflowOutline.fromEntity(item);
            userIds.add(rst.getCreatedBy());
            userIds.add(rst.getLastModifiedBy());
            return rst;
        }).collect(Collectors.toList());

        Map<String, String> members = getMemberNames(context, userIds);
        outlines.forEach(item -> {
            String createBy = item.getCreatedBy();
            if (StringUtils.isNotBlank(createBy)) {
                item.setCreatedBy(members.get(createBy));
            }
            String modify = item.getLastModifiedBy();
            if (StringUtils.isNotBlank(modify)) {
                item.setLastModifiedBy(members.get(modify));
            }
        });
        return new PageResult(outlines, outlines.size(), pageNum, size, consoleButtonSwitchManager.isDefinitionOperationSwitch());
    }

    @Path("/deploy")
    @POST
    public Result create(Map data) {

        WorkflowOutline workflowOutline = JsonUtil.fromJson(JsonUtil.toJson(data), WorkflowOutline.class);

        if (workflowOutline == null) {
            return null;
        }
        if (StringUtils.isBlank(workflowOutline.getId())) {
            workflowOutline.setCreatedBy(workflowOutline.getUserId());
            workflowOutline.setCreateTime(System.currentTimeMillis());
        }

        workflowOutline.setLastModifiedBy(workflowOutline.getUserId());//在创建时也填写上更新人和更新时间
        workflowOutline.setLastModifiedTime(System.currentTimeMillis());

        workflowOutline.setTenantId(workflowOutline.getTenantId());
        workflowOutline.setUserId(workflowOutline.getUserId());


        ExecutableWorkflowExt workflow = WorkflowJsonUtil.convertExecutableWorkflowFromJson(JsonUtil.toJson(workflowOutline.getWorkflow()));

        Workflow.isBlank(workflowOutline.getName(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NAME_REQUIRED);
        Workflow.isBlank(workflowOutline.getEntryType(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_ENTRY_TYPE_REQUIRED);
        Workflow.isBlank(workflowOutline.getEntryTypeName(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_ENTRY_NAME_REQUIRED);
        Workflow.isAnyBlankCount(workflowOutline.getRangeCircleIds(), workflowOutline.getRangeEmployeeIds());
        workflow.setSourceWorkflowId(workflowOutline.getSourceWorkflowId());

        RemoteContext context = new RemoteContext(null, workflowOutline.getTenantId(), BPMConstants.APP_ID, workflowOutline.getUserId());
        String workflowId = service.deployWorkflow(getServiceManager(context), workflowOutline, true, true);
        return new Result(true, workflowId);
    }


    @Path("/delete/{id}/{tenantId}/{isDelete}")
    @GET
    public Result delete(@PathParam("id") String id, @PathParam("tenantId") String tenantId, @QueryParam("ea") String ea,
                         @PathParam("isDelete") boolean isDelete) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        WorkflowOutlineEntity outlineEntity = outlineDao.delete(context.getTenantId(), BPMConstants.CRM_SYSTEM_USER, id, isDelete, null);

        return new Result(true, outlineEntity);

    }

    @Path("/extension/{workflowId}")
    @GET
    public Result findWorkflowExtension(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @PathParam("workflowId") String workflowId) {
        FlowExtensionEntity extension = extensionDao.findOneFlowExtension(initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER).getTenantId(), workflowId);
        return new Result(true, extension);

    }

    @Path("/getAvailableWorkflows")
    @GET
    public Result getAvailableWorkflows(@QueryParam("ea") String ea,
                                        @QueryParam("tenantId") String tenantId,
                                        @QueryParam("entityId") String entityId,
                                        @QueryParam("objectId") String objectId,
                                        @QueryParam("userId") String userId
    ) {
        if (entityId == null || objectId == null || userId == null) {
            return new Result(false, "参数不正确,请联系纷享客服");
        }
        RemoteContext context = initContext(ea, tenantId, userId);
        List<WorkflowOutline> outlines = service.getAvailableWorkflows(getServiceManager(context), entityId, objectId, true, 0, null, Boolean.TRUE);

        return new Result(true, outlines.stream().map(item -> {
            Map map = Maps.newHashMap();
            map.put("name", item.getName());
            map.put("single", item.getSingleInstanceFlow());
            map.put("enable", item.isEnabled());
            return map;
        }).collect(Collectors.toList()));
    }


    /**
     * 沙箱同步异常后,重新同步,单条
     */
    @Path("/init/define")
    @GET
    public Result initDefine(@QueryParam("fromTenantId") String fromTenantId,
                             @QueryParam("toTenantId") String toTenantId,
                             @QueryParam("syncOutlineId") String syncOutlineId) {
        if (Strings.isNullOrEmpty(fromTenantId)) {
            return new Result(false, "来源企业不能为空");
        }

        if (Strings.isNullOrEmpty(toTenantId)) {
            return new Result(false, "目标企业不能为空");
        }


        if (Strings.isNullOrEmpty(syncOutlineId)) {
            return new Result(false, "outlineId 不能为空");
        }

        if (fromTenantId.equals(toTenantId)) {
            return new Result(false, "沙箱赋值企业Id不能相同");
        }

        try {
            workflowInitialService.initTenant(fromTenantId, toTenantId, syncOutlineId);
            return new Result(true, "复制完成");
        } catch (Exception e) {
            return new Result(false, e.getMessage());
        }

    }

    @Path("/update/linkapp")
    @GET
    public Result updateLinkApp(@QueryParam("tenantId") String tenantId) {


        Set<String> workflowOutlineIds = Sets.newHashSet();

        DatastoreExt datastore = getDataStore().setTenantId(tenantId);
        Query<WorkflowOutlineEntity> query = datastore.createQuery(WorkflowOutlineEntity.class);
        query.field(WorkflowOutlineEntity.Fields.tenantId).equal(tenantId);
        query.field(WorkflowOutlineEntity.Fields.linkApp).equal("FSAID_127a3981");
        query.field(WorkflowOutlineEntity.Fields.externalFlow).notEqual(1);
        List<WorkflowOutlineEntity> workflowOutlineEntities = query.asList();

        if (CollectionUtils.isNotEmpty(workflowOutlineEntities)) {
            for (WorkflowOutlineEntity workflowOutlineEntity : workflowOutlineEntities) {
                String id = workflowOutlineEntity.getId();
                workflowOutlineIds.add(id);
                Query<WorkflowOutlineEntity> updateQuery = datastore.createQuery(WorkflowOutlineEntity.class);
                updateQuery.field(WorkflowOutlineEntity.Fields.tenantId).equal(tenantId);
                updateQuery.field(WorkflowOutlineEntity.Fields.linkApp).equal("FSAID_127a3981");
                updateQuery.field(WorkflowOutlineEntity.Fields.externalFlow).notEqual(1);
                updateQuery.field(WorkflowOutlineEntity.Fields.id).equal(id);

                UpdateOperations<WorkflowOutlineEntity> updateOperations = datastore.createUpdateOperations(WorkflowOutlineEntity.class);
                updateOperations.set(WorkflowOutlineEntity.Fields.linkApp, "FSAID_11490ebc");
                updateOperations.set(WorkflowOutlineEntity.Fields.linkAppType, 1);
                datastore.findAndModify(updateQuery, updateOperations);
            }
        }
        return new Result(true, workflowOutlineIds);
    }

    @Path("/fixDefEnableToEngine/{environmentType}/{flag}/{sleepTime}")
    @GET
    public Result fixDefEnableToEngine(@PathParam("environmentType") Integer environmentType, @PathParam("flag") Boolean flag, @PathParam("sleepTime") Integer sleepTime) throws InterruptedException {
        if(Objects.isNull(getEnterpriseEnvironment(environmentType)) || Objects.isNull(flag) || Objects.isNull(sleepTime)){
            log.info("业务流同步定义状态到引擎，环境参数错误");
            return null;
        }
        
        Map<String, Boolean> enableTenant = Maps.newHashMap();
        List<WorkflowOutlineEntity> outLineList  = Lists.newArrayList();
        try {
            Iterator<WorkflowOutlineEntity> srcIdAndEnable = outlineDao.findSrcIdAndEnable("71557");
            srcIdAndEnable.forEachRemaining(item ->{
                outLineList.add(item);
            });
        }catch (Exception e){
            log.info("业务流同步定义状态到引擎，查询outLineList错误");
            return null;
        }
        
        if (CollectionUtils.isEmpty(outLineList)){
            log.info("业务流同步定义状态到引擎，查询outLineList为空");
            return null;
        }
        
        int pageSize = 100;
        int outLineListSize = outLineList.size();
        int index = -1;
        while (true){
            if(!SwitchConfigManager.getOpenRefreshData()){
                break;
            }
            ++index;
            log.info("业务流同步定义状态到引擎，index：" + index);
            if((outLineListSize - index * pageSize) < pageSize) {
                defEnableToEngine(outLineList.subList(index*pageSize, outLineListSize), index, enableTenant, environmentType, flag);
                break;
            }
            defEnableToEngine(outLineList.subList(index*pageSize, index*pageSize+pageSize), index, enableTenant, environmentType, flag);
            if(index*pageSize+pageSize == outLineListSize){
                break;
            }
            Thread.sleep(sleepTime);
        }
        log.info("业务流同步定义状态到引擎，结束了！！！");
        return new Result(true, "完成");
    }

    public void defEnableToEngine(List<WorkflowOutlineEntity> outLineList, int pageNumber, Map<String, Boolean> enableTenant, int environmentType, boolean flag){
        Map<String, Map<String, Boolean>> tenantIdMap = Maps.newHashMap();
        for (WorkflowOutlineEntity outline : outLineList) {
            String tenantId = outline.getTenantId();
            if(StringUtils.isBlank(tenantId)){
                continue;
            }
            if(!tenantIdMap.containsKey(tenantId)){
                tenantIdMap.put(tenantId, Maps.newHashMap());
            }
            tenantIdMap.get(tenantId).put(StringUtils.isBlank(outline.getSourceWorkflowId()) ? outline.getId() : outline.getSourceWorkflowId(), outline.isEnabled());
        }

        Set<Integer> notInSet = Sets.newHashSet();
        for (String k : tenantIdMap.keySet()){
            if(!enableTenant.containsKey(k)){
                notInSet.add(Integer.valueOf(k));
            }
        }
        if(CollectionUtils.isNotEmpty(notInSet)){
            for (Integer integer : notInSet) {
                enableTenant.put(String.valueOf(integer), false);
            }
            try {
                BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
                arg.setEnterpriseIds(notInSet);
                BatchGetSimpleEnterpriseDataResult res = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
                if(Objects.nonNull(res) || CollectionUtils.isNotEmpty(res.getSimpleEnterpriseList())){
                    for (SimpleEnterpriseData simpleEnterpriseData : res.getSimpleEnterpriseList()) {
                        if(Objects.equals(RunStatus.RUN_STATUS_NORMAL, simpleEnterpriseData.getRunStatus()) && (flag || Objects.equals(environmentType, simpleEnterpriseData.getEnv()))){
                            enableTenant.put(String.valueOf(simpleEnterpriseData.getEnterpriseId()), true);
                        }
                    }
                }
            }catch (Exception e){
                log.info("查询企业启用/停用失败：" + ArrayUtils.toString(notInSet));
            }
        }

        for (String k : tenantIdMap.keySet()){
            try {
                if(enableTenant.get(k)){
                    paasWorkflow.updateDefinitionStatusMap(getContext(k), tenantIdMap.get(k));
                }
            }catch (Exception e){
                log.info("业务流同步定义状态到引擎错误，index：" + pageNumber + "，tenantId：" + k);
                continue;
            }

        }
        return;
    }

    private EnterpriseEnvironment getEnterpriseEnvironment(Integer arg){
        if(Objects.isNull(arg)){
            return null;
        }
        for (EnterpriseEnvironment value : EnterpriseEnvironment.values()) {
            if(Objects.equals(value.getEnv(), arg)){
                return value;
            }
        }
        return null;
    }

    private RemoteContext getContext(String tenantId) {
        return new RemoteContext("", tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
    }

    public DatastoreExt getDataStore(){
        return SpringUtils.getBean("flowBizDatastore");
    }
}
