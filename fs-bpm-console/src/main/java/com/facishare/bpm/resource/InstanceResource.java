package com.facishare.bpm.resource;

import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.remote.BPMPaasWorkflowResource;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.resource.model.PageResult;
import com.facishare.bpm.resource.model.Result;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.util.DateUtils;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Aaron on 17/01/2017.
 * @IgnoreI18n
 */
@Path("/instance")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Service
@Slf4j
public class InstanceResource extends BaseResource {

    @Autowired
    private BPMInstanceService bpmInstanceService;

    @Autowired
    private BPMDefinitionService bpmDefinitionService;

    @Autowired
    private BPMPaasWorkflowResource workflowResource;

    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @Autowired
    private WorkflowTransferService workflowTransferService;

    /**
     * 获取流程实例详情
     * TODO 修改下, 包括console,同步修改
     *
     * @param id
     * @param tenantId
     * @return
     */
    @Path("/get/{id}")
    @GET
    public Result get(@QueryParam("ea") String ea, @PathParam("id") String id, @QueryParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        try {
            WorkflowInstance workflowInstance = bpmInstanceService.getWorkflowInstance(getServiceManager(context), id);
            Map<String,Object> ext = Maps.newHashMap();
            ext.put("start", DateUtils.currentTimeMilliFormat(workflowInstance.getStart()));
            ext.put("end", DateUtils.currentTimeMilliFormat(workflowInstance.getEnd()));
            return new Result(workflowInstance,ext);
        } catch (BPMBusinessException e) {
            return new Result(Maps.newHashMap());
        }
    }

    @Path("/svg/{id}")
    @GET
    public Result instanceSVG(@QueryParam("ea") String ea, @PathParam("id") String id, @QueryParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        try {
            WorkflowInstance workflowInstance = bpmInstanceService.getWorkflowInstance(getServiceManager(context), id);
            if (Objects.isNull(workflowInstance)) {
                return new Result(false, "instance not exists");
            }
            WorkflowExtension extension = bpmDefinitionService.getWorkflowExtensionByWorkflowId(context, workflowInstance.getWorkflowId());
            return new Result(true,extension);
        } catch (BPMBusinessException e) {
            return new Result(Maps.newHashMap());
        }
    }

    /**
     * 获取流程实例详情
     *
     * @param instanceId
     * @param tenantId
     * @return
     */
    @Path("/recoveryCancelInstance")
    @GET
    public Result recoveryCancelInstance(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @QueryParam("instanceId") String instanceId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        try {
            return new Result(bpmInstanceService.recoveryCancelInstance(getServiceManager(context), instanceId));
        } catch (BPMBusinessException e) {
            return new Result(Maps.newHashMap());
        }
    }

    @Path("/list/{sourceWorkflowId}")
    @GET
    public Result findIntancesBySourceId(@QueryParam("ea") String ea,
                                         @PathParam("sourceWorkflowId") String sourceWorkflowId,
                                         @QueryParam("tenantId") String tenantId,
                                         @QueryParam("page") Integer pageNum,
                                         @QueryParam("objectId") String objectId,
                                         @QueryParam("workflowName") String workflowName) {
        try {
            RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
            GetWorkflowInstances.Arg arg = new GetWorkflowInstances.Arg();
            arg.setContext(context);
            arg.setSourceWorkflowId(sourceWorkflowId);
            arg.setObjectId(objectId);
            arg.setWorkflowName(workflowName);
            Page pageInfo = new Page();
            pageInfo.setPageSize(20);
            pageInfo.setPageNumber(pageNum);
            arg.setPageInfo(pageInfo);
            com.facishare.bpm.model.resource.paas.PageResult<WorkflowInstance> rst = workflowResource.getWorkflowInstances(getHeaders(context), arg).getResult();
            List<WorkflowInstance> instances = rst.getDataList();
            List<String> objects = Lists.newArrayList();
            List<Object> applicantIds = Lists.newArrayList();
            instances.forEach(item -> {
                objects.add(item.getObjectId());
                applicantIds.add(item.getApplicantId());
            });
            if (CollectionUtils.isNotEmpty(instances)) {
                String entityId = instances.get(0).getEntityId();
                Map<String, String> members = getMemberNames(context, applicantIds);
                Map<String, String> names = getObjectNames(context, entityId, objects);
                instances.forEach(item -> {
                    item.setApplicantId(members.get(item.getApplicantId()));
                    item.setObjectName(names.getOrDefault(item.getObjectId(), item.getObjectId()));
                });
                Collections.sort(instances, (i1, i2) -> i1.getStart() > i2.getStart() ? -1 : 1);
            }
            return new PageResult(instances, rst.getTotal(), pageInfo.getPageNumber(), pageInfo.getPageSize());
        } catch (Exception e) {
            log.error("findIntancesBySourceId", e);
            return new Result(Maps.newHashMap());
        }
    }

    @Path("/cancel/{tenantId}/{instanceId}")
    @GET
    public Result cancelInstance(@QueryParam("ea") String ea, @PathParam("instanceId") String instanceId, @PathParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        CancelWorkflowInstance.Arg arg = new CancelWorkflowInstance.Arg();
        arg.setContext(context);
        arg.setWorkflowInstanceId(instanceId);
        workflowResource.cancelWorkflowInstance(getHeaders(context), arg);


        return new Result(true, null);
    }


    @Path("/rule/ruleMatch/{tenantId}/{sourceWorkflowId}")
    @GET
    public Result ruleMatch(@PathParam("sourceWorkflowId") String sourceWorkflowId, @PathParam("tenantId") String tenantId, @QueryParam("entityId") String entityId, @QueryParam("objectId") String objectId) {
        Map<String, Object> rst = Maps.newHashMap();
        RemoteContext context = new RemoteContext("", tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        WorkflowOutline workflow = bpmDefinitionService.getWorkflowOutlineBySourceId(
                getServiceManager(new RemoteContext("", tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER)),
                sourceWorkflowId);
        rst.put("rule", workflow.getRule());
        Map<String, Object> desc = metadataService.findDescribe(context, entityId, true, false);
        if (desc != null) {
            Map<String, Object> fields = (Map<String, Object>) desc.get("fields");
            rst.put("fields", fields);
        }

        Map<String, Object> data = metadataService.findDataById(context,entityId,objectId,false,false,false,true,true,true,null).getObject_data();
        rst.put("data", data);


        return new Result(true, rst);
    }



    @Path("retry")
    @GET
    public Result instanceRetry(
            @QueryParam("tenantId") String tenantId,
            @QueryParam("instanceId") String instanceId,
            @QueryParam("userId") String userId,
            @QueryParam("executeType") Integer executeType,
            @QueryParam("executeIndex") Integer executeIndex
    ) {


        if(Strings.isEmpty(tenantId)||
                Strings.isEmpty(instanceId)||
                Objects.isNull(executeType)||
                Objects.isNull(executeIndex)
                ){
            return new Result(false,"please check tenantId,instanceId,executeType,executeIndex is not null");
        }

        String tip = executeType==0?"ignore":"retry";

        AfterRetry.RetryResult result;
        try {
            if (Strings.isEmpty(userId)) {
                userId = BPMConstants.CRM_SYSTEM_USER;
            }
            RemoteContext context = initContext("", tenantId, userId);
            result = paasWorkflowServiceProxy.instanceAfterActionRetry(context, instanceId, executeIndex, executeType);
            return new Result(tip+","+result);
        } catch (Exception e) {
            return new Result(false,tip+"fail,"+e.getMessage());
        }
    }


    @Path("sync")
    @GET
    public Result instanceRetry(
            @QueryParam("tenantId") String tenantId,
            @QueryParam("instanceId") String instanceId
    ) {


        if (Strings.isEmpty(tenantId) ||
                Strings.isEmpty(instanceId)) {
            return new Result(false, "please check tenantId,instanceId is not null");
        }

        try {
            RemoteContext context = initContext("", tenantId, BPMConstants.CRM_SYSTEM_USER);
            workflowTransferService.transfer(context, instanceId);
            return new Result("sync success");
        } catch (Exception e) {
            return new Result(false, "sync fail," + e.getMessage());
        }
    }

    @Path("/fixedPGCancelReason/{tenantId}")
    @GET
    public Result fixedPGCancelReason(@PathParam("tenantId") String tenantId) {
        RemoteContext context = initContext(null, tenantId, BPMConstants.CRM_SYSTEM_USER);
        Integer res = bpmInstanceService.fixedPGCancelReason(context, Page.getPage(1, 10), new BPMInstanceObj());
        log.error("fixedPGCancelReason is finish, count:{}",res);
        return new Result(true, res);
    }

}
