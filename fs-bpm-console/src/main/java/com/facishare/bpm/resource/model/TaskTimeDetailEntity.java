package com.facishare.bpm.resource.model;

import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * Created by penghj on 2016/8/3.
 */
@Data
@Entity(value = "tasks", noClassnameStored = true)
public class TaskTimeDetailEntity implements Serializable {

    private static final long serialVersionUID = -4007289483623262192L;

    @Id
    protected ObjectId id;
    @Property("state")
    protected String state;
    @Property("workflowInstanceId")
    protected String workflowInstanceId;
    @Property("taskType")
    protected String taskType;
    @Property("completed")
    protected Boolean completed;
    @Property("createTime")
    protected Long createTime;
    @Property("modifyTime")
    protected Long modifyTime;
    @Property("allPassType")
    protected Integer allPassType;
    @Property("candidateIds")
    protected List<String> candidateIds;
    @Property("approverModifyLog")
    protected List<Map<String, Object>> approverModifyLog;
    @Property("operateLogs")
    protected List<Map<String, Object>> operateLogs;
    @Property("opinions")
    protected List<Map<String, Object>> opinions;
    @Property("version")
    protected Integer version;

    public boolean getCompleted() {
        return completed != null && completed;
    }

}
