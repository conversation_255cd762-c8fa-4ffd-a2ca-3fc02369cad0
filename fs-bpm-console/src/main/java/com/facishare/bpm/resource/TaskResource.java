package com.facishare.bpm.resource;

import com.alibaba.fastjson.JSON;
import com.effektif.workflow.api.ext.WorkflowConstants;
import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.manager.CheckInvalidTaskRedisManager;
import com.facishare.bpm.manager.MongoManager;
import com.facishare.bpm.manager.MongoShardManager;
import com.facishare.bpm.model.WorkflowExtension;
import com.facishare.bpm.model.meta.BPMTaskHandleTimeDetailObj;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate;
import com.facishare.bpm.proxy.PaasMetadataProxy;
import com.facishare.bpm.remote.BPMPaasWorkflowResource;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.resource.model.*;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.bpm.util.DateUtils;
import com.facishare.bpm.util.SwitchConfigManager;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.CalculateTaskHandleTimeDetailUtil;
import com.facishare.bpm.utils.ListUtil;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.mongo.bizdb.BizTaskDataDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTaskDataEntity;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.MongoClient;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Aaron on 17/01/2017.
 * @IgnoreI18n
 */
@Slf4j
@Path("/task")
@Service
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class TaskResource extends BaseResource {

    @Autowired
    private BPMPaasWorkflowResource workflowResource;
    @Autowired
    private BizTaskDataDao taskDataDao;
    @Autowired
    private BPMTaskService taskService;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;
    @Autowired
    private CheckInvalidTaskRedisManager checkInvalidTaskRedisManager;
    @Autowired
    private PaasMetadataProxy paasMetadataProxy;
    @Autowired
    private BPMDefinitionService bpmDefinitionService;
    @Autowired
    private MongoManager mongoManager;
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;
    @Autowired
    private MongoShardManager mongoShardManager;

    @GET
    @Path("/getByInstanceId/{instanceId}")
    public Result tasksByInstanceId(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId, @PathParam("instanceId") String instanceId) {

        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        GetTaskByPage.Arg arg = new GetTaskByPage.Arg();
        arg.setContext(context);
        arg.setInstanceId(instanceId);
        Page page = new Page();
        page.setPageSize(10000);
        page.setPageNumber(1);

        arg.setPageInfo(page);
        return new Result(workflowResource.getTasks(getHeaders(context), arg).getResult());
    }

    @Path("/{id}")
    @GET
    public Result task(@QueryParam("ea") String ea, @PathParam("id") String id, @QueryParam("tenantId") String tenantId) {
        RemoteContext context = new RemoteContext(tenantId, tenantId, BPMConstants.APP_ID, BPMConstants.CRM_SYSTEM_USER);
        GetTask.Arg arg = new GetTask.Arg();
        arg.setContext(context);
        arg.setTaskId(id);
        Task task = workflowResource.getTask(getHeaders(context), arg).getResult();
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("createTime", DateUtils.currentTimeMilliFormat(task.getCreateTime()));
        ext.put("modifyTime", DateUtils.currentTimeMilliFormat(task.getModifyTime()));
        return new Result(task, ext);
    }


    @Path("/getByUserId")
    @GET
    public Result getByUserId(@QueryParam("ea") String ea, @QueryParam("tenantId") String tenantId,
                              @QueryParam("page") Integer pageNum,
                              @QueryParam("size") Integer size,
                              @QueryParam("instanceId") String instanceId,
                              @QueryParam("completed") Boolean completed) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        GetTaskByPage.Arg arg = new GetTaskByPage.Arg();
        arg.setContext(context);
        arg.setCompleted(completed);
        arg.setInstanceId(instanceId);
        Page page = new Page();
        if (pageNum == null) {
            pageNum = 1;
        }
        if (size == null) {
            size = 10;
        }
        page.setAsc(false);
        page.setOrderBy("createTime");
        page.setPageNumber(pageNum);
        page.setPageSize(size);
        arg.setPageInfo(page);
        GetTaskByPage.Result result = workflowResource.getTasks(getHeaders(context), arg);
        return new PageResult(result.getResult().getResult(), result.getResult().getTotal(), pageNum, size);
    }


    @Path("/historydata/{tenantId}/{taskId}")
    @GET
    public Result findHistoryData(@QueryParam("ea") String ea, @PathParam("tenantId") String tenantId, @PathParam("taskId") String taskId) {

        WorkflowTaskDataEntity entity = taskDataDao.find(tenantId, taskId);
        return new Result(true, entity);
    }

    @Path("/after/retry/{tenantId}/{taskId}/{executeIndex}/{executeType}")
    @GET
    public Result retry(
            @PathParam("tenantId") String tenantId,
            @PathParam("taskId") String taskId,
            @PathParam("executeIndex") String executeIndex,
            @PathParam("executeType") String executeType
    ) {
        RemoteContext context = initContext("", tenantId, BPMConstants.CRM_SYSTEM_USER);
        AfterRetry.RetryResult rst = taskService.afterActionRetry(getServiceManager(context), taskId, Integer.parseInt(executeIndex), Integer.parseInt(executeType));
        return new Result(rst.isSuccess(), rst.getMessage());
    }

    /**
     * 查询
     *
     * @return
     */
    @Path("/cancel/instance")
    @POST
    public Result cancelInstanceByTask(Map params) {

        FindTaskByUserId.Arg arg = JsonUtil.fromJson(JsonUtil.toJson(params), FindTaskByUserId.Arg.class);

        RemoteContext context = initContext("", arg.getTenantId(), arg.getUserId());

        Page page = new Page();

        if (!Strings.isNullOrEmpty(arg.getPageNumber())) {
            page.setPageNumber(Integer.parseInt(arg.getPageNumber()));
        }

        if (!Strings.isNullOrEmpty(arg.getPageSize())) {
            page.setPageSize(Integer.parseInt(arg.getPageSize()));
        } else {
            page.setPageSize(1000);
        }

        RefServiceManager serviceManager = getServiceManager(context);

        List<Task> tasks = paasWorkflowServiceProxy.getAllInprogressTasks(context, arg.getEntityId());
        Set<String> instanceIds = Sets.newHashSet();
        tasks.forEach(task -> {
            Map data = serviceManager.findDataById(task.getEntityId(), task.getObjectId(), false, false);
            if (MapUtils.isEmpty(data)) {
                log.info("数据已作废或删除,taskId:{},entityId:{},objectId:{},instanceId:{}", task.getId(), task.getEntityId(), task.getObjectId(), task.getWorkflowInstanceId());
                instanceIds.add(task.getWorkflowInstanceId());
            }
        });

        instanceIds.forEach(instanceId -> {
            try {
                paasWorkflowServiceProxy.cancel(initContext("", arg.getTenantId(), BPMConstants.CRM_SYSTEM_USER), instanceId, "由于数据被作废,系统自动终止该流程");
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });

        return new Result();
    }


    @Path("/object/task/{tenantId}/{objectId}")
    @GET
    public Result findObjectTask(@QueryParam("ea") String ea, @PathParam("tenantId") String tenantId, @PathParam("objectId") String objectId) {
        RemoteContext context = initContext(ea, tenantId, "1000");
        GetTaskByPage.Arg arg = new GetTaskByPage.Arg();
        arg.setContext(context);
        arg.setObjectId(objectId);
        arg.setState(TaskState.in_progress_or_error);

        GetTaskByPage.Result result = workflowResource.getTasks(getHeaders(context), arg);
        return new Result(true, result);
    }

    @Path("/byInstanceId/{instanceId}")
    @GET
    public Result findTasksInstanceId(@QueryParam("ea") String ea,
                                      @QueryParam("tenantId") String tenantId,
                                      @PathParam("instanceId") String id,
                                      @QueryParam("completed") Boolean completed) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        try {
            List<Task> tasks = taskService.getTasksByInstanceId(getServiceManager(context), id).stream().filter(item -> {
                if (completed != null) {
                    return item.getCompleted() == completed;
                } else {
                    return true;
                }
            }).collect(Collectors.toList());
            List<Object> employeeIds = Lists.newArrayList();
            Map<String, Set<String>> entityIdObjectIds = Maps.newConcurrentMap();
            tasks.stream().forEach(item -> {
                employeeIds.add(item.getApplicantId());
                if(!Strings.isNullOrEmpty(item.getObjectId())){
                    if (entityIdObjectIds.get(item.getEntityId()) == null) {
                        entityIdObjectIds.put(item.getEntityId(), Sets.newHashSet(item.getObjectId()));
                    } else {
                        entityIdObjectIds.get(item.getEntityId()).add(item.getObjectId());
                    }
                }

                employeeIds.addAll(item.getOpinions().stream().map(opinion -> opinion.getUserId()).collect(Collectors.toList()));
                List<Task.ApproverModifyLog> modifyLogs = item.getApproverModifyLog();
                if (CollectionUtils.isNotEmpty(modifyLogs)) {
                    modifyLogs.forEach(modifyLog -> {
                        employeeIds.add(modifyLog.getUserId());
                        if (CollectionUtils.isNotEmpty(modifyLog.getAfterModifyPersons())) {
                            employeeIds.addAll(modifyLog.getAfterModifyPersons());
                        }
                        if (CollectionUtils.isNotEmpty(modifyLog.getBeforeModifyPersons())) {
                            employeeIds.addAll(modifyLog.getBeforeModifyPersons());
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(item.getAssigneeIds())) {
                    employeeIds.addAll(item.getAssigneeIds());
                }
                if (CollectionUtils.isNotEmpty(item.getCandidateIds())) {
                    employeeIds.addAll(item.getCandidateIds());
                }
            });
            Map<String, String> members = getMemberNames(context, employeeIds);
            Map<String, String> objectNames = getObjectNames(context, entityIdObjectIds);
            log.info("objectNames:{}", objectNames);
            tasks.stream().forEach(item -> {
                item.setApplicantId(members.getOrDefault(item.getApplicantId(), item.getApplicantId()));
                item.setObjectId(objectNames.getOrDefault(item.getObjectId(), item.getObjectId()));
                employeeIds.addAll(item.getOpinions().stream().map(opinion -> opinion.getUserId()).collect(Collectors.toList()));
                for (Opinion opinion : item.getOpinions()) {
                    opinion.setUserId(members.getOrDefault(opinion.getUserId(), opinion.getUserId()));
                }
                List<Task.ApproverModifyLog> modifyLogs = item.getApproverModifyLog();
                if (CollectionUtils.isNotEmpty(modifyLogs)) {
                    modifyLogs.forEach(modifyLog -> {
                        modifyLog.setUserId(members.getOrDefault(modifyLog.getUserId(), modifyLog.getUserId()));
                        if (CollectionUtils.isNotEmpty(modifyLog.getAfterModifyPersons())) {
                            modifyLog.setAfterModifyPersons(modifyLog.getAfterModifyPersons().stream().map(temp -> members.getOrDefault(temp, temp)).collect(Collectors.toList()));
                        }
                        if (CollectionUtils.isNotEmpty(modifyLog.getBeforeModifyPersons())) {
                            modifyLog.setBeforeModifyPersons(modifyLog.getBeforeModifyPersons().stream().map(temp -> members.getOrDefault(temp, temp)).collect(Collectors.toList()));
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(item.getAssigneeIds())) {
                    item.setAssigneeIds(item.getAssigneeIds().stream().map(temp -> members.getOrDefault(temp, temp)).collect(Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(item.getCandidateIds())) {
                    item.setCandidateIds(item.getCandidateIds().stream().map(temp -> members.getOrDefault(temp, temp)).collect(Collectors.toList()));
                }
            });
            return new Result(true, tasks);
        } catch (Throwable e) {
            log.error("findTasksInstanceId:", e);
            throw e;
        }
    }

    @Path("/find/invalidObjectTasks")
    @GET
    public Result findInvalidObjectTasks(@QueryParam("ea") String ea,
                                         @QueryParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        String key = CheckInvalidTaskRedisManager.getInvalidTaskKey(tenantId);
        InvalidObjectTask invalidObjectTask = new InvalidObjectTask();
        String allTaskCount = checkInvalidTaskRedisManager.getKey(CheckInvalidTaskRedisManager.getAllCountKey(tenantId));

        invalidObjectTask.setAllTasks(NumberUtils.isNumber(allTaskCount) ? Integer.valueOf(allTaskCount) : 0);
        invalidObjectTask.setInvalidTasks(checkInvalidTaskRedisManager.size(key));
        invalidObjectTask.setInvalidInstanceList(checkInvalidTaskRedisManager.getList(key));
        invalidObjectTask.setInvalidObjectInfoList(checkInvalidTaskRedisManager.getList(CheckInvalidTaskRedisManager.getInvalidTaskObjectKey(tenantId)));

        if (checkInvalidTaskRedisManager.isLoading(tenantId)) {
            invalidObjectTask.setStatus(CheckInvalidTaskRedisManager.Constants.LOADING);
            return new Result(true, invalidObjectTask);
        } else if (checkInvalidTaskRedisManager.isOver(tenantId)) {
            invalidObjectTask.setStatus(CheckInvalidTaskRedisManager.Constants.OVER);
            return new Result(true, invalidObjectTask);
        }

        List<Map<String, Object>> tasks = mongoManager.getTasks(context.getTenantId());
        if (CollectionUtils.isEmpty(tasks)) {
            return new Result(false, "no tasks");
        }
        checkInvalidTaskRedisManager.setKey(CheckInvalidTaskRedisManager.getAllCountKey(tenantId), String.valueOf(tasks.size()));
        //异步执行
        checkInvalidTaskRedisManager.writeInvalidObjectTasks(context, () -> getInvalidInstanceIdInfo(context, getTasksByGroupByEntityId(tasks)));

        return new Result(false, "正在查询，请稍后重试");
    }

    @Path("/cancel/invalidObjectTasks")
    @GET
    public Result cancelInvalidObjectTasks(@QueryParam("ea") String ea,
                                           @QueryParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        String key = CheckInvalidTaskRedisManager.getInvalidTaskKey(tenantId);

        if (checkInvalidTaskRedisManager.isLoading(tenantId)) {
            return new Result(false, "正在查询，请稍后重试");
        }
        if (checkInvalidTaskRedisManager.exists(key)) {
            List<String> workflowInstanceIdList = checkInvalidTaskRedisManager.getList(key);
            workflowInstanceIdList.forEach(workflowInstanceId ->
                    paasWorkflowServiceProxy.cancel(context, workflowInstanceId, "关联数据已作废或已删除,系统自动终止当前流程"));
        }
        checkInvalidTaskRedisManager.delete(key);
        return new Result(true, "success");
    }


    @Path("/delete/checkInvalidTaskRedisKeys")
    @GET
    public Result deleteCheckInvalidTaskRedisKeys(@QueryParam("ea") String ea,
                                                  @QueryParam("tenantId") String tenantId) {
        checkInvalidTaskRedisManager.deleteAllKeys(tenantId);
        return new Result(true, "success");
    }

    private Map<String, List<Map<String, Object>>> getTasksByGroupByEntityId(List<Map<String, Object>> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return null;
        }
        return tasks.stream().collect(Maps::newHashMap, (m, task) -> {
            String entityId = MapUtil.instance.getString(task, "entityId");
            List<Map<String, Object>> tasksByEntityId = m.get(entityId);
            if (CollectionUtils.isEmpty(tasksByEntityId)) {
                tasksByEntityId = Lists.newArrayList();
                tasksByEntityId.add(task);
                m.put(entityId, tasksByEntityId);
            } else {
                tasksByEntityId.add(task);
            }
        }, Map::putAll);
    }

    /**
     * 获取作废数据还存在进行从的实例
     *
     * @param context
     * @param entityIdTasks
     * @return 作废数据实例, 作废数据信息
     */
    private Pair<List<String>, List<String>> getInvalidInstanceIdInfo(RemoteContext context, Map<String, List<Map<String, Object>>> entityIdTasks) {
        List<String> invalidInstanceIds = Lists.newArrayList();
        List<String> invalidInstanceObjectInfoList = Lists.newArrayList();
        Pair<List<String>, List<String>> pair = new Pair<>(invalidInstanceIds, invalidInstanceObjectInfoList);

        if (MapUtils.isEmpty(entityIdTasks)) {
            return pair;
        }
        //按对象分组，同一个对象按20为一组查数据
        entityIdTasks.forEach((entityId, tasks) -> {
            if (CollectionUtils.isNotEmpty(tasks)) {
                //objectId -1-n- instanceId
                Map<String, List<String>> objectIdMapInstanceId = tasks.stream().collect(Maps::newHashMap, (m, task) -> {
                    String objectId = MapUtil.instance.getString(task, "objectId");
                    String workflowInstanceId = MapUtil.instance.getString(task, "workflowInstanceId");
                    if (m.get(objectId) == null) {
                        m.put(objectId, Lists.newArrayList(workflowInstanceId));
                    } else {
                        m.get(objectId).add(workflowInstanceId);
                    }
                }, Map::putAll);

                Set<String> objectIds = objectIdMapInstanceId.keySet();
                List<List<String>> objectIdsGroup = getListGroupFormSet(objectIds);
                List<Map<String, Object>> dataByIds = Lists.newArrayList();
                for (int i = 0; i < objectIdsGroup.size(); i++) {
                    List<String> subObjectIs = objectIdsGroup.get(i);
                    if (CollectionUtils.isEmpty(subObjectIs)) {
                        continue;
                    }
                    try {
                        /**
                         * 批量(20条)查询数据
                         */
                        dataByIds.addAll(paasMetadataProxy.findDataByIds(context, entityId, subObjectIs));
                    } catch (Throwable e) {
                        /**
                         * 如果查数据异常了，则去除待校验的数据
                         */
                        subObjectIs.forEach(objectIdMapInstanceId::remove);
                        objectIdsGroup.remove(i);
                        i--;
                        log.warn("context:{},entityId:{},exception objectIds:{}", context, entityId, subObjectIs, e);
                    }
                }
                objectIds = ListUtil.combineSubList(objectIdsGroup);
                collectInvalidInstances(entityId, dataByIds, invalidInstanceIds, objectIdMapInstanceId, invalidInstanceObjectInfoList, objectIds);
            }
        });
        return pair;
    }


    private void collectInvalidInstances(String entityId, List<Map<String, Object>> dataByIds, List<String> invalidInstanceIds,
                                         Map<String, List<String>> objectIdMapInstanceId, List<String> invalidInstanceObjectInfoList,
                                         Set<String> objectIds) {
        if (MapUtils.isEmpty(objectIdMapInstanceId)) {
            return;
        }

        //该对象下的数据都删除了
        if (CollectionUtils.isEmpty(dataByIds)) {
            invalidInstanceIds.addAll(objectIdMapInstanceId.values().stream().collect(Lists::newArrayList, List::addAll, List::addAll));
            invalidInstanceObjectInfoList.addAll(objectIdMapInstanceId.keySet().stream().map(k ->
                    entityId + ":" + k + ":" + objectIdMapInstanceId.get(k)).collect(Collectors.toList()));
        } else if (objectIds.size() != dataByIds.size()) {
            List<String> ids = dataByIds.stream().map(k -> String.valueOf(k.get("_id"))).collect(Collectors.toList());
            //已删除的
            List<String> disjunction = (List<String>) CollectionUtils.disjunction(objectIds, ids);
            disjunction.forEach(k -> {
                List<String> instanceIds = objectIdMapInstanceId.get(k);
                invalidInstanceObjectInfoList.add(entityId + ":" + k + ":" + instanceIds);
                invalidInstanceIds.addAll(instanceIds);
            });

            //已作废数据
            addElement(entityId, dataByIds, invalidInstanceIds, objectIdMapInstanceId, invalidInstanceObjectInfoList);

        } else {

            //已作废数据
            addElement(entityId, dataByIds, invalidInstanceIds, objectIdMapInstanceId, invalidInstanceObjectInfoList);
        }
    }

    private void addElement(String entityId, List<Map<String, Object>> dataByIds, List<String> invalidInstanceIds, Map<String, List<String>> objectIdMapInstanceId, List<String> invalidInstanceObjectInfoList) {
        //已作废数据
        dataByIds.forEach(k -> {
            if ("invalid".equals(String.valueOf(k.get("life_status")))) {
                String id = String.valueOf(k.get("_id"));
                String invalidTime = String.valueOf(k.get("last_modified_time"));

                List<String> instanceIds = objectIdMapInstanceId.get(id);
                invalidInstanceIds.addAll(instanceIds);
                invalidInstanceObjectInfoList.add(entityId + ":" + id + ":" + instanceIds + ":" + invalidTime);
            }
        });
    }

    private List<List<String>> getListGroupFormSet(Set<String> objectIds) {
        if (CollectionUtils.isEmpty(objectIds)) {
            return Lists.newArrayList();
        }
        return ListUtil.getSubList(Lists.newArrayList(objectIds), 20);
    }

    @Path("/svg/{id}")
    @GET
    public Result instanceSVG(@QueryParam("ea") String ea, @PathParam("id") String id, @QueryParam("tenantId") String tenantId) {
        RemoteContext context = initContext(ea, tenantId, BPMConstants.CRM_SYSTEM_USER);
        try {

            Task task = paasWorkflowServiceProxy.getTask(context, id);

            if (Objects.isNull(task)) {
                return new Result(false, "任务不存在");
            }
            WorkflowExtension extension = bpmDefinitionService.getWorkflowExtensionByWorkflowId(context, task.getWorkflowId());
            return new Result(true, extension);
        } catch (BPMBusinessException e) {
            return new Result(Maps.newHashMap());
        }
    }


    /**
     * 任务重试
     *
     * @return
     */
    @Path("retry")
    @GET
    public Result taskRetry(
            @QueryParam("tenantId") String tenantId,
            @QueryParam("taskId") String taskId,
            @QueryParam("userId") String userId,
            @QueryParam("type") String type,
            @QueryParam("executeType") Integer executeType,
            @QueryParam("executeIndex") Integer executeIndex
            ) {

        if(Strings.isEmpty(tenantId)||
                Strings.isEmpty(taskId)||
                Objects.isNull(executeType)||
                Objects.isNull(executeIndex)
                ){
            return new Result(false,"请检查 tenantId,taskId,executeType,executeIndex是否已填写");
        }

        String tip = executeType==0?"忽略":"重试";

        AfterRetry.RetryResult result;
        try {
            if (Strings.isEmpty(userId)) {
                userId = BPMConstants.CRM_SYSTEM_USER;
            }
            RemoteContext context = initContext("", tenantId, userId);
            if ("auto".equals(type)) {
                result = paasWorkflowServiceProxy.autoTaskAfterRetry(context, taskId, executeIndex, executeType);
            } else {
                result = paasWorkflowServiceProxy.afterActionRetry(context, taskId, executeIndex, executeType);
            }
            return new Result(tip+","+result);
        } catch (Exception e) {
            return new Result(false,tip+"失败,"+e.getMessage());
        }
    }

    @Path("/updateApproveTaskResult/{tenantId}/{ea}")
    @GET
    public Result updateApproveTaskResult(@PathParam("tenantId") String tenantId,@PathParam("ea") String ea) {
        List<String> errorInfo = Lists.newArrayList();
        int count = 0;
        //查询企业pg中进行中的审批任务
        int pageNumber = 1;
        int pageSize = 20;
        Map<String, String> conditionMap = Maps.newHashMap();
        conditionMap.put(TransferDataConstants.MDField.state.getValue(), TaskState.pass.name());
        conditionMap.put(TransferDataConstants.MDTaskField.execution_type.getValue(), ExecutionTypeEnum.approve.name());
        while (Boolean.TRUE){
            List<Map<String, Object>> queryRes = metadataService.findDataByQuery(tenantId, ea, BPMConstants.APP_ID, TransferDataConstants.APINAME_TASK, conditionMap, pageSize, pageNumber);
            if (CollectionUtils.isEmpty(queryRes)){
                break;
            }
            //更新数据
            List<String> taskIds = queryRes.stream().filter(item -> item.containsKey("_id") && StringUtils.isBlank((String) item.get("action_type"))).map(mapObj -> (String)mapObj.get("_id")).collect(Collectors.toList());
            errorInfo.addAll(updatePGApproveTask(ea, tenantId, taskIds));
            pageNumber++;
            count += taskIds.size();
        }
        Map<String,Object> ext = Maps.newHashMap();
        ext.put("count", count);
        return new Result(errorInfo, ext);
    }

    public List<String> updatePGApproveTask(String ea, String tenantId, List<String> taskIds){
        List<String> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(taskIds)){
            return result;
        }
        RemoteContext context = initContext(ea, tenantId,  BPMConstants.CRM_SYSTEM_USER);
        for(String taskId: taskIds){
            //查询mongo数据
            Task task;
            try {
                GetTask.Arg arg = new GetTask.Arg();
                arg.setContext(context);
                arg.setTaskId(taskId);
                task = workflowResource.getTask(getHeaders(context), arg).getResult();
            }catch (Exception e){
                result.add("查询mongo中的task失败，企业id：" + tenantId + "，任务id：" + taskId + "。");
                continue;
            }

            if(Objects.isNull(task)){
                result.add("查询mongo中的task没找到，企业id：" + tenantId + "，任务id：" + taskId + "。");
                continue;
            }
            if(!task.getCompleted() || StringUtils.isBlank(task.getActionType())){
                result.add("查询mongo中的task没完成或没有审批结果，企业id：" + tenantId + "，任务id：" + taskId + "。");
                continue;
            }
            //修改pg数据
            Map<String, Object> data = Maps.newHashMap();
            data.put("action_type", task.getActionType());
            try {
                metadataService.updateData(context, TransferDataConstants.APINAME_TASK, task.getId(), JSON.toJSONString(data), true, false);
            }catch (Exception e){
                result.add("修改PG中的task失败，企业id：" + tenantId + "，任务id：" + taskId + "。");
                continue;
            }
        }
        return result;
    }

    @Path("/retryCountSynchronizeHandleTimeDetail/{tenantId}/{taskIds}")
    @GET
    public Result retryCountSynchronizeHandleTimeDetail(@PathParam("tenantId") String tenantId,@PathParam("taskIds") String taskIds) {
        if(StringUtils.isBlank(tenantId) || StringUtils.isBlank(taskIds)){
            return new Result(false, "参数错误");
        }
        String[] taskArr = taskIds.split(",");
        List<String> taskIdList = Lists.newArrayList(taskArr);
        if (CollectionUtils.isEmpty(taskIdList)) {
            return new Result(false, "参数错误");
        }
        RemoteContext context = initContext("", tenantId,  BPMConstants.CRM_SYSTEM_USER);
        GetTask.Arg arg = new GetTask.Arg();
        arg.setContext(context);
        String errorMsg = "";
        for (String taskId : taskIdList) {
            arg.setTaskId(taskId);
            Task task = workflowResource.getTask(getHeaders(context), arg).getResult();
            if(Objects.isNull(task)){
                errorMsg += "任务：" + taskId + "，任务不存在；";
                continue;
            }
            CalculateTaskHandleTimeDetailUtil.TaskInfo taskInfo = CalculateTaskHandleTimeDetailUtil.TaskInfo.transfer(task);

            List<BPMTaskHandleTimeDetailObj> insertData = CalculateTaskHandleTimeDetailUtil.getTaskHandleTimeDetailModifyInfo(taskInfo, "").getAll();

            //删除所有当前任务耗时信息
            SearchTemplateQuery query = new SearchTemplateQuery();
            List<IFilter> filterList = Lists.newArrayList();

            Filter taskIdFilter = new Filter();
            taskIdFilter.setFieldName(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_TASK_ID_FIELD);
            taskIdFilter.setOperator(Operator.EQ);
            taskIdFilter.setFieldValues(Lists.newArrayList(taskId));
            filterList.add(taskIdFilter);

            Filter apiNameFilter = new Filter();
            apiNameFilter.setFieldName(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_TASK_API_NAME_FIELD);
            apiNameFilter.setOperator(Operator.EQ);
            apiNameFilter.setFieldValues(Lists.newArrayList(TransferDataConstants.APINAME_TASK));
            filterList.add(apiNameFilter);

            query.setFilters(filterList);
            query.setLimit(1000);
            FindDataBySearchTemplate.Result result = metadataService.findDataBySearchTemplate(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, query);
            FindDataBySearchTemplate.QueryResultInfo queryResultInfo = result.getData().getQueryResult();
            List<Map<String, Object>> data = queryResultInfo.getData();
            List<BPMTaskHandleTimeDetailObj> originalList =  JacksonUtil.fromJson(JacksonUtil.toJson(data), new TypeReference<List<BPMTaskHandleTimeDetailObj>>() {});
            CalculateTaskHandleTimeDetailUtil.ModifyInfo modifyInfo = CalculateTaskHandleTimeDetailUtil.comparisonAcquisitionUpdateAndAdd(originalList, insertData);
            if(CollectionUtils.isNotEmpty(modifyInfo.getUpdate())){
                //批量修改
                List<Map<String, Object>> updateList = modifyInfo.getUpdate().stream().map(o -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("_id", o.get_id());
                    map.put(TransferDataConstants.TASK_HANDLE_TIME_DETAIL_OBJ_END_TIME_FIELD, o.getEnd_time());
                    map.put("start_time", o.getStart_time());
                    map.put("state", o.getState());
                    map.put("user_id", o.getUser_id());
                    return map;
                }).collect(Collectors.toList());
                metadataService.batchUpdateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, updateList);

            }
            if(CollectionUtils.isNotEmpty(modifyInfo.getAdd())){
                //批量添加
                metadataService.batchCreateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, JSON.toJSONString(modifyInfo.getAdd()));
            }
            if(CollectionUtils.isNotEmpty(modifyInfo.getRemoveIds())){
                for (String removeId : modifyInfo.getRemoveIds()) {
                    metadataService.invalidAndDeleteByDataId(context, removeId, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL);
                }
            }
        }
        return StringUtils.isNotBlank(errorMsg) ? new Result(false, errorMsg) :new Result("重新同步任务耗时信息成功");

    }

    public void addHandleTimeDetailEndTime(List<BPMTaskHandleTimeDetailObj> insertData, Long endTime, List<String>excludePerson){
        if (CollectionUtils.isEmpty(insertData)){
            return;
        }
        if(Objects.isNull(excludePerson)){
            excludePerson = Lists.newArrayList();
        }
        for (BPMTaskHandleTimeDetailObj date : insertData) {
            if(Objects.isNull(date.getEnd_time()) && !excludePerson.contains(date.getUser_id().get(0))){
                date.setEnd_time(endTime);
            }
        }
    }

    public List<String> getRecentlyCandidateIds(Long time, List<Task.ApproverModifyLog> approverModifyLogList, List<String>currentCandidateIds){
        if(CollectionUtils.isEmpty(approverModifyLogList)){
            return Lists.newArrayList(currentCandidateIds);
        }
        Task.ApproverModifyLog targetLog = null;
        boolean isBefore = true;
        for (Task.ApproverModifyLog log : approverModifyLogList) {
            if(log.getModifyTime() < time){
                targetLog = log;
            }else {
                if(Objects.isNull(targetLog)){
                    isBefore = false;
                    targetLog = log;
                }
                break;
            }
        }
        if(Objects.isNull(targetLog)){
            return Lists.newArrayList(currentCandidateIds);
        }
        return isBefore ? Lists.newArrayList(targetLog.getAfterModifyPersons()) : Lists.newArrayList(targetLog.getBeforeModifyPersons());
    }

    public List<String> getSpecifyTimeProcessed(Long time, List<Opinion> opinions){
        if(CollectionUtils.isEmpty(opinions)){
            return Lists.newArrayList();
        }
        return opinions.stream().filter(o -> o.getReplyTime() < time).map(opinion -> opinion.getUserId()).collect(Collectors.toList());

    }

    @Path("/synchronousHistoryTaskHandleTimeDetail/{tenantId}/{entityId}")
    @GET
    public Result synchronousHistoryTaskHandleTimeDetail(@PathParam("tenantId") String tenantId,@PathParam("entityId") String entityId) {
        if(StringUtils.isBlank(tenantId) || StringUtils.isBlank(entityId)){
            return new Result(false, "参数错误");
        }
        if(!SwitchConfigManager.getOpenRefreshData()){
            return new Result(false, "未打开刷数据开关");
        }
        List<TaskTimeDetailEntity> taskEntityList = Lists.newArrayList();
        CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClient.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(
                        PojoCodecProvider.builder().register(TaskTimeDetailEntity.class).build()
                )
        );
        try {
            mongoShardManager.getMongoDatabases().forEach((database) -> {
                MongoCollection<Document> workflowsCollection = database.getCollection("tasks").withCodecRegistry(codecRegistry);
                Bson filter = Filters.and(
                        Filters.eq(BPMConstants.TENANT_ID, tenantId),
                        Filters.eq("entityId", entityId),
                        Filters.eq(BPMConstants.TYPE, WorkflowConstants.WorkflowType.BPM)
                );
                Bson projection = Projections.include(Lists.newArrayList("_id", "state", "workflowInstanceId", "taskType", "completed", "createTime", "modifyTime", "allPassType", "candidateIds", "approverModifyLog", "operateLogs", "opinions", "version"));
                FindIterable<TaskTimeDetailEntity> taskEntities = workflowsCollection.find( filter, TaskTimeDetailEntity.class).projection(projection);
                for (TaskTimeDetailEntity task: taskEntities) {
                    taskEntityList.add(task);
                }
            });
        }catch (Exception e){
            log.info("获取任务信息失败，tenantId：" + tenantId + "，entityId：" + entityId + "，" + e.getMessage());
            return new Result(false, "获取任务信息失败");
        }
        RemoteContext context = initContext("", tenantId,  BPMConstants.CRM_SYSTEM_USER);
        for (TaskTimeDetailEntity taskTimeDetailEntity : taskEntityList) {
            try {
                if(!SwitchConfigManager.getOpenRefreshData()){
                    return new Result(false, "刷数据开关已关闭");
                }
                if(Objects.nonNull(taskTimeDetailEntity.getVersion()) && taskTimeDetailEntity.getVersion() >= 880){
                    continue;
                }
                CalculateTaskHandleTimeDetailUtil.TaskInfo taskInfo = transferHandleTimeDetailTaskInfo(taskTimeDetailEntity);

                if(Objects.isNull(taskInfo)){
                    log.info("转换任务信息失败，tenantId：" + tenantId + "，taskId：" + taskTimeDetailEntity.getId().toString());
                    continue;
                }

                List<BPMTaskHandleTimeDetailObj> allData = CalculateTaskHandleTimeDetailUtil.getTaskHandleTimeDetailModifyInfo(taskInfo, "").getAll();

                if(CollectionUtils.isNotEmpty(allData)){
                    //批量添加
                    metadataService.batchCreateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, JSON.toJSONString(allData));
                }
            }catch (Exception e){
                log.info("同步任务耗时失败，tenantId：" + tenantId + "，taskId：" + taskTimeDetailEntity.getId().toString());
                continue;
            }
        }
        log.info("!!!同步任务耗时成功!!!");
        return new Result(true, "同步任务耗时成功");

    }

    private static CalculateTaskHandleTimeDetailUtil.TaskInfo transferHandleTimeDetailTaskInfo(TaskTimeDetailEntity taskTimeDetailEntity){
        CalculateTaskHandleTimeDetailUtil.TaskInfo result = new CalculateTaskHandleTimeDetailUtil.TaskInfo();
        result.setId(taskTimeDetailEntity.getId().toString());
        result.setState(taskTimeDetailEntity.getState());
        result.setInstanceId(taskTimeDetailEntity.getWorkflowInstanceId());
        result.setType(taskTimeDetailEntity.getTaskType());
        result.setCompleted(taskTimeDetailEntity.getCompleted());
        result.setCreateTime(taskTimeDetailEntity.getCreateTime());
        result.setModifyTime(taskTimeDetailEntity.getModifyTime());
        result.setAllPassType(taskTimeDetailEntity.getAllPassType());
        result.setCandidateIds(taskTimeDetailEntity.getCandidateIds());
        if(CollectionUtils.isNotEmpty(taskTimeDetailEntity.getApproverModifyLog())){
            List<CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate> approverModifyLog = Lists.newArrayList();
            for (Map<String, Object> map : taskTimeDetailEntity.getApproverModifyLog()) {
                CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate operate = new CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate();
                String id = map.get("id") instanceof String ? (String) map.get("id") : null;
                if(StringUtils.isBlank(id)){
                    return null;
                }
                operate.setId(id);
                operate.setTime(map.get("modifyTime") instanceof Long ? (Long) map.get("modifyTime") : 0);
                operate.setBeforeModifyPersons(map.get("beforeModifyPersons") instanceof List ? (List) map.get("beforeModifyPersons") : Lists.newArrayList());
                operate.setAfterModifyPersons(map.get("afterModifyPersons") instanceof List ? (List) map.get("afterModifyPersons") : Lists.newArrayList());
                approverModifyLog.add(operate);
            }
            result.setApproverModifyLog(approverModifyLog);
        }
        if(CollectionUtils.isNotEmpty(taskTimeDetailEntity.getOperateLogs())){
            List<CalculateTaskHandleTimeDetailUtil.OperateOperate> operateLog = Lists.newArrayList();
            for (Map<String, Object> map : taskTimeDetailEntity.getOperateLogs()) {
                CalculateTaskHandleTimeDetailUtil.OperateOperate operate = new CalculateTaskHandleTimeDetailUtil.OperateOperate();
                String id = map.get("id") instanceof String ? (String) map.get("id") : null;
                if(StringUtils.isBlank(id)){
                    return null;
                }
                operate.setId(id);
                operate.setTime(map.get("createTime") instanceof Long ? (Long) map.get("createTime") : 0);
                operate.setType(map.get("type") instanceof String ? (String) map.get("type") : null);
                operateLog.add(operate);
            }
            result.setOperateLog(operateLog);
        }
        if(CollectionUtils.isNotEmpty(taskTimeDetailEntity.getOpinions())){
            List<CalculateTaskHandleTimeDetailUtil.OpinionOperate> opinionLog = Lists.newArrayList();
            for (Map<String, Object> map : taskTimeDetailEntity.getOpinions()) {
                CalculateTaskHandleTimeDetailUtil.OpinionOperate operate = new CalculateTaskHandleTimeDetailUtil.OpinionOperate();
                String id = map.get("id") instanceof String ? (String) map.get("id") : null;
                if(StringUtils.isBlank(id)){
                    return null;
                }
                operate.setId(id);
                operate.setTime(map.get("replyTime") instanceof Long ? (Long) map.get("replyTime") : 0);
                operate.setActionType(map.get("actionType") instanceof String ? (String) map.get("actionType") : null);
                operate.setUserId(map.get("userId") instanceof String ? (String) map.get("userId") : null);
                opinionLog.add(operate);
            }
            result.setOpinionLog(opinionLog);
        }
        return result;
    }


    @Path("/synchronousHistoryTaskHandleTimeDetailRepairException/{tenantId}/{entityId}")
    @GET
    public Result synchronousHistoryTaskHandleTimeDetailRepairException(@PathParam("tenantId") String tenantId,@PathParam("entityId") String entityId) {
        if(StringUtils.isBlank(tenantId) || StringUtils.isBlank(entityId)){
            return new Result(false, "参数错误");
        }
        if(!SwitchConfigManager.getOpenRefreshData()){
            return new Result(false, "未打开刷数据开关");
        }
        List<Pair<TaskTimeDetailEntity, MongoCollection<Document>>> taskEntityList = Lists.newArrayList();
        CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClient.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(
                        PojoCodecProvider.builder().register(TaskTimeDetailEntity.class).build()
                )
        );
        log.info("!!!开始同步!!!");
        try {
            mongoShardManager.getMongoDatabases().forEach((database) -> {
                MongoCollection<Document> workflowsCollection = database.getCollection("tasks").withCodecRegistry(codecRegistry);
                Bson filter = Filters.and(
                        Filters.eq(BPMConstants.TENANT_ID, tenantId),
                        Filters.eq("entityId", entityId),
                        Filters.eq(BPMConstants.TYPE, WorkflowConstants.WorkflowType.BPM)
                );
                Bson projection = Projections.include(Lists.newArrayList("_id", "state", "workflowInstanceId", "taskType", "completed", "createTime", "modifyTime", "allPassType", "candidateIds", "approverModifyLog", "operateLogs", "opinions", "version"));
                FindIterable<TaskTimeDetailEntity> taskEntities = workflowsCollection.find(filter, TaskTimeDetailEntity.class).projection(projection);
                for (TaskTimeDetailEntity task: taskEntities) {
                    taskEntityList.add(new Pair<>(task, workflowsCollection));
                }
            });
        }catch (Exception e){
            log.info("获取任务信息失败，tenantId：" + tenantId + "，entityId：" + entityId + "，" + e.getMessage());
            return new Result(false, "获取任务信息失败");
        }
        log.info("任务查询完成，条数：" + taskEntityList.size());
        RemoteContext context = initContext("", tenantId,  BPMConstants.CRM_SYSTEM_USER);
        for (Pair<TaskTimeDetailEntity, MongoCollection<Document>> pair : taskEntityList) {
            try {
                if(!SwitchConfigManager.getOpenRefreshData()){
                    return new Result(false, "刷数据开关已关闭");
                }
                if(Objects.nonNull(pair.getKey().getVersion()) && pair.getKey().getVersion() >= 880){
                    continue;
                }
                Pair<CalculateTaskHandleTimeDetailUtil.TaskInfo, List<Pair<String, String>>> taskInfoListPair = transferHandleTimeDetailTaskInfoPair(pair.getKey());
                if(CollectionUtils.isEmpty(taskInfoListPair.getValue())){
                    continue;
                }
                try {
                    for (Pair<String, String> updatePair : taskInfoListPair.getValue()) {
                        Document query = new Document("_id", pair.getKey().getId());
                        Document update = new Document("$set", new Document(updatePair.getKey(), updatePair.getValue()));
                        pair.getValue().updateOne(query, update);
                    }
                }catch (Exception e){
                    log.info("修改mongo中操作的id失败，tenantId：" + tenantId + "，taskId：" + pair.getKey().getId().toString() + "，" + e.getMessage());
                    continue;
                }
                log.info("更新任务操作id成功，taskId：" + taskInfoListPair.getKey().getId());
                List<BPMTaskHandleTimeDetailObj> allData = CalculateTaskHandleTimeDetailUtil.getTaskHandleTimeDetailModifyInfo(taskInfoListPair.getKey(), "").getAll();
                if(CollectionUtils.isNotEmpty(allData)){
                    //批量添加
                    metadataService.batchCreateData(context, TransferDataConstants.APINAME_TASK_HANDLE_TIME_DETAIL, JSON.toJSONString(allData));
                }
            }catch (Exception e){
                log.info("同步任务耗时失败，tenantId：" + tenantId + "，taskId：" + pair.getKey().getId().toString());
                continue;
            }
        }
        log.info("!!!同步任务耗时成功!!!");
        return new Result(true, "同步任务耗时成功");

    }

    private static Pair<CalculateTaskHandleTimeDetailUtil.TaskInfo, List<Pair<String, String>>> transferHandleTimeDetailTaskInfoPair(TaskTimeDetailEntity taskTimeDetailEntity){
        List<Pair<String, String>> fixList = Lists.newArrayList();
        CalculateTaskHandleTimeDetailUtil.TaskInfo taskInfo = new CalculateTaskHandleTimeDetailUtil.TaskInfo();
        taskInfo.setId(taskTimeDetailEntity.getId().toString());
        taskInfo.setState(taskTimeDetailEntity.getState());
        taskInfo.setInstanceId(taskTimeDetailEntity.getWorkflowInstanceId());
        taskInfo.setType(taskTimeDetailEntity.getTaskType());
        taskInfo.setCompleted(taskTimeDetailEntity.getCompleted());
        taskInfo.setCreateTime(taskTimeDetailEntity.getCreateTime());
        taskInfo.setModifyTime(taskTimeDetailEntity.getModifyTime());
        taskInfo.setAllPassType(taskTimeDetailEntity.getAllPassType());
        taskInfo.setCandidateIds(taskTimeDetailEntity.getCandidateIds());
        if(CollectionUtils.isNotEmpty(taskTimeDetailEntity.getApproverModifyLog())){
            List<CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate> approverModifyLog = Lists.newArrayList();
            for (int i = 0 ; i < taskTimeDetailEntity.getApproverModifyLog().size() ; i++) {
                Map<String, Object> map = taskTimeDetailEntity.getApproverModifyLog().get(i);
                CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate operate = new CalculateTaskHandleTimeDetailUtil.ApproverModifyOperate();
                String id = map.get("id") instanceof String ? (String) map.get("id") : null;
                if(StringUtils.isBlank(id)){
                    id = new ObjectId().toHexString();
                    fixList.add(new Pair<>("approverModifyLog." + i +".id",id));
                }
                operate.setId(id);
                operate.setTime(map.get("modifyTime") instanceof Long ? (Long) map.get("modifyTime") : 0);
                operate.setBeforeModifyPersons(map.get("beforeModifyPersons") instanceof List ? (List) map.get("beforeModifyPersons") : Lists.newArrayList());
                operate.setAfterModifyPersons(map.get("afterModifyPersons") instanceof List ? (List) map.get("afterModifyPersons") : Lists.newArrayList());
                approverModifyLog.add(operate);
            }
            taskInfo.setApproverModifyLog(approverModifyLog);
        }
        if(CollectionUtils.isNotEmpty(taskTimeDetailEntity.getOperateLogs())){
            List<CalculateTaskHandleTimeDetailUtil.OperateOperate> operateLog = Lists.newArrayList();
            for (int i = 0 ; i < taskTimeDetailEntity.getOperateLogs().size() ; i++) {
                Map<String, Object> map = taskTimeDetailEntity.getOperateLogs().get(i);
                CalculateTaskHandleTimeDetailUtil.OperateOperate operate = new CalculateTaskHandleTimeDetailUtil.OperateOperate();
                String id = map.get("id") instanceof String ? (String) map.get("id") : null;
                if(StringUtils.isBlank(id)){
                    id = new ObjectId().toHexString();
                    fixList.add(new Pair<>("operateLogs." + i +".id",id));
                }
                operate.setId(id);
                operate.setTime(map.get("createTime") instanceof Long ? (Long) map.get("createTime") : 0);
                operate.setType(map.get("type") instanceof String ? (String) map.get("type") : null);
                operateLog.add(operate);
            }
            taskInfo.setOperateLog(operateLog);
        }
        if(CollectionUtils.isNotEmpty(taskTimeDetailEntity.getOpinions())){
            List<CalculateTaskHandleTimeDetailUtil.OpinionOperate> opinionLog = Lists.newArrayList();
            for (int i = 0 ; i < taskTimeDetailEntity.getOpinions().size() ; i++) {
                Map<String, Object> map = taskTimeDetailEntity.getOpinions().get(i);
                CalculateTaskHandleTimeDetailUtil.OpinionOperate operate = new CalculateTaskHandleTimeDetailUtil.OpinionOperate();
                String id = map.get("id") instanceof String ? (String) map.get("id") : null;
                if(StringUtils.isBlank(id)){
                    id = new ObjectId().toHexString();
                    fixList.add(new Pair<>("opinions." + i +".id",id));
                }
                operate.setId(id);
                operate.setTime(map.get("replyTime") instanceof Long ? (Long) map.get("replyTime") : 0);
                operate.setActionType(map.get("actionType") instanceof String ? (String) map.get("actionType") : null);
                operate.setUserId(map.get("userId") instanceof String ? (String) map.get("userId") : null);
                opinionLog.add(operate);
            }
            taskInfo.setOpinionLog(opinionLog);
        }
        return new Pair<>(taskInfo, fixList);
    }




}
