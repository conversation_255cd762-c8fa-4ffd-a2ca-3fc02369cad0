package com.facishare.bpm.manager;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.MongoClient;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Projections;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;


/**
 * Created by wangz on 17-6-29.
 */
@Service
@Slf4j
public class MongoManager {

    @Autowired
    private DbRouterClient dbRouterClient;
    private static volatile MongoClient mongoClient;


    public Integer getDataCount(String tenantId, String type) {
        MongoCollection collection = null;
        try {
            if ("BpmInstance".equals(type)) {
                collection = getWorkflowDBConnection("workflowInstances", tenantId);
            }
            if ("BpmTask".equals(type)) {
                collection = getWorkflowDBConnection("tasks", tenantId);
            }

            if (collection != null) {
                Document query = new Document();
                query.put("appId", BPMConstants.APP_ID);
                query.put("tenantId", tenantId);
                return (int) collection.count(query);
            }
        } catch (Exception e) {
            log.error("get mongo data count error : tenantId={}, type={}, ", tenantId, type, e);
        } finally {
            if (collection != null) {
                collection = null;
            }
            close();
        }

        return 0;
    }

    public MongoCollection<Document> getWorkflowDBConnection(String tableName, String tenantId) {
        RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, "FLOW", "workflow-engine", "mongodb");
        mongoClient = new MongoClient(routerInfo.getJdbcUrl());
        return mongoClient.getDatabase("FS-PAAS-WORKFLOW-DB").getCollection(tableName);
    }

    public List<Map<String, Object>> getTasks(String tenantId) {
        MongoCollection<Document> collection = getWorkflowDBConnection("tasks",tenantId);
        Bson bson = Filters.and(Filters.eq("tenantId", tenantId),
                Filters.eq("appId", "BPM"),
                Filters.exists("completed", Boolean.FALSE),
                Filters.or(Filters.eq("state", "in_progress"), Filters.eq("state", "error")));
        FindIterable<Document> findIterable = collection.find(bson).projection(Projections.include("workflowInstanceId", "entityId", "objectId"));
        MongoCursor<Document> iterator = findIterable.iterator();
        List<Map<String, Object>> tasks = Lists.newArrayList();
        while (iterator.hasNext()) {
            tasks.add(Maps.newHashMap(iterator.next()));
        }
        close();
        return tasks;
    }

    /**
     * 查询in_progress和error的任务
     */
    public void getTasks(String tenantId, String entityId, Consumer<Map> consumer) {
        MongoCollection<Document> collection = getWorkflowDBConnection("tasks", tenantId);

        List<Bson> filters = Lists.newArrayList(
                Filters.eq("tenantId", tenantId),
                Filters.eq("appId", "BPM"),
                Filters.eq("type", "workflow_bpm"),
                Filters.exists("completed", Boolean.FALSE),
                Filters.or(
                        Filters.eq("state", "in_progress"),
                        Filters.eq("state", "error")
                ));
        if (!Strings.isNullOrEmpty(entityId)) {
            filters.add(Filters.eq("entityId", entityId));
        }
        Bson query = Filters.and(filters);
        FindIterable<Document> findIterable = collection.find(query).batchSize(100).projection(Projections.include("workflowInstanceId", "entityId", "objectId", "_id"));
        for (Document document : findIterable) {
            consumer.accept(Maps.newHashMap(document));
        }
        close();
    }

    public void close(){
        if(Objects.nonNull(mongoClient)){
            mongoClient.close();
        }
    }
}
