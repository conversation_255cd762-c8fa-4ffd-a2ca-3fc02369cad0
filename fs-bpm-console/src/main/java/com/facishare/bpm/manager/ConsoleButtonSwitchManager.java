package com.facishare.bpm.manager;

import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * Created by wangzhx on 2019/8/8.
 */
@Service
public class ConsoleButtonSwitchManager {
    private static final String configName = "fs-bpm-console-button-switch-config";
    @Delegate
    private volatile ConsoleButtonSwitchConfig config;

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig(configName, iConfig ->
                config = JsonUtil.fromJson(iConfig.getString(), ConsoleButtonSwitchConfig.class));
    }

    class ConsoleButtonSwitchConfig {
        @Getter
        private boolean definitionOperationSwitch;
        @Getter
        private boolean checkQuotaSwitch;
    }
}
