package com.facishare.bpm.manager;

import com.github.autoconf.ConfigFactory;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.sql.*;

/**
 * Created by wangz on 17-12-5.
 */
@Service
@Getter
@Setter
@Slf4j
public class PGDBManager {
    private final static String CONFIG_NAME = "fs-bpm-console-pg-property";
    private String userName;
    private String psWord;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig(CONFIG_NAME, config -> {
            try {
                Class.forName("org.postgresql.Driver");

                log.info("======load pg server config start=====");
                this.userName = config.get("userName");
                this.psWord = config.get("psWord");
                log.info("======load pg server config end=====");
            } catch (Exception e) {
                log.error("cannot load pg config. CONFIG={}", config.getName(), e);
            }
        });
    }

    public Integer queryDataCount(String dbUrl, String tenantId, String apiName) {

        Connection connection = null;
        PreparedStatement st = null;
        try {
            connection = DriverManager.getConnection(dbUrl, getUserName(), getPsWord());
            String query="select count(1) from mt_data md " +
                    "where md.object_describe_api_name = ? and md.tenant_id = ?";
            st = connection.prepareStatement(query);
            st.setString(1,apiName);
            st.setString(2,tenantId);
            try(ResultSet rs = st.executeQuery()){
                return rs.getInt(1);
            }
        } catch (SQLException e) {

            log.error("Connection pg db failed! " + e);
        } finally {
            try {
                if (st != null) {
                    st.close();
                }

                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                log.warn("close pg db resource failed! " + e);
            }
        }
        return null;
    }
}
