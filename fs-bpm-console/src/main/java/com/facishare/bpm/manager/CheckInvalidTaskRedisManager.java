package com.facishare.bpm.manager;

import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.model.RemoteContext;
import com.github.jedis.support.MergeJedisCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * Created by wangzhx on 2019/7/9.
 */
@Service
@Slf4j
public class CheckInvalidTaskRedisManager {
    @Qualifier("afterRedisClient")
    @Autowired
    private MergeJedisCmd redisClient;

    @Async
    public void writeInvalidObjectTasks(RemoteContext context, List<Task> tasks, Consumer<Task> consumer) {
        String tenantId = context.getTenantId();
        String key = getInvalidTaskKey(tenantId);

        setWriteStatus(context.getTenantId(), Constants.LOADING);
        AtomicInteger count = new AtomicInteger();
        tasks.forEach(task -> {
            count.incrementAndGet();
            try {
                consumer.accept(task);
            } catch (RestProxyRuntimeException e) {
                //作废或删除
                if (201112008 == e.getCode()) {
                    redisClient.lpush(key, task.getWorkflowInstanceId());
                }
            } catch (Throwable e) {
                log.warn("e:{}", e);
            }
            redisClient.set(getCountKey(tenantId), String.valueOf(count));
        });
        setWriteStatus(context.getTenantId(), Constants.OVER);
        expire(tenantId, 60 * 30);
    }

    @Async
    public void writeInvalidObjectTasks(RemoteContext context, Supplier<Pair<List<String>, List<String>>> supplier) {
        String tenantId = context.getTenantId();
        String invalidTaskKey = getInvalidTaskKey(tenantId);
        String invalidTaskObjectKey = getInvalidTaskObjectKey(tenantId);
        setWriteStatus(context.getTenantId(), Constants.LOADING);
        try {
            Pair<List<String>, List<String>> pair = supplier.get();
            List<String> invalidInstances = pair.getKey();
            List<String> invalidInstanceObjectInfo = pair.getValue();
            if (CollectionUtils.isEmpty(invalidInstances)) {
                return;
            }
            redisClient.lpush(invalidTaskKey, invalidInstances.toArray(new String[0]));
            redisClient.lpush(invalidTaskObjectKey, invalidInstanceObjectInfo.toArray(new String[0]));
            log.info("context:{},invalidInstances:{},invalidInstanceObjects:{}", context, invalidInstances, invalidInstanceObjectInfo);

        } catch (Throwable e) {
            log.warn("context:{}", context, e);
        } finally {
            setWriteStatus(context.getTenantId(), Constants.OVER);
        }
    }

    private void setWriteStatus(String tenantId, String status) {
        redisClient.set(getStatusKey(tenantId), status);
    }

    public boolean exists(String key) {
        Long len = redisClient.llen(key);
        return Objects.nonNull(len) && 0 != len;
    }

    public Long size(String key) {
        return redisClient.llen(key);
    }

    public List<String> getList(String key) {
        return redisClient.lrange(key, 0, -1);
    }

    public void delete(String... key) {
        redisClient.del(key);
    }

    public void deleteAllKeys(String tenantId) {
        delete(getStatusKey(tenantId), getAllCountKey(tenantId), getInvalidTaskKey(tenantId), getCountKey(tenantId), getInvalidTaskObjectKey(tenantId));
    }

    public String setKey(String key, String value) {
        return redisClient.set(key, value);
    }

    public String getKey(String key) {
        return redisClient.get(key);
    }

    public void expire(String tenantId, int expireTime) {
        redisClient.expire(getCountKey(tenantId), expireTime);
        redisClient.expire(getInvalidTaskKey(tenantId), expireTime);
        redisClient.expire(getStatusKey(tenantId), expireTime);
        redisClient.expire(getAllCountKey(tenantId), expireTime);
    }

    public static String getInvalidTaskKey(String tenantId) {
        return tenantId + Constants.INVALID_SUFFIX;
    }

    public static String getInvalidTaskObjectKey(String tenantId) {
        return tenantId + Constants.INVALID_INFO_SUFFIX;
    }

    public static String getStatusKey(String tenantId) {
        return tenantId + Constants.STATUS_SUFFIX;
    }

    public static String getCountKey(String tenantId) {
        return tenantId + Constants.COUNT_SUFFIX;
    }

    public static String getAllCountKey(String tenantId) {
        return tenantId + Constants.ALL_SUFFIX;
    }

    public boolean isLoading(String tenantId) {
        return Constants.LOADING.equals(getKey(CheckInvalidTaskRedisManager.getStatusKey(tenantId)));
    }

    public boolean isOver(String tenantId) {
        return Constants.OVER.equals(getKey(CheckInvalidTaskRedisManager.getStatusKey(tenantId)));
    }

    public interface Constants {
        String LOADING = "loading";
        String OVER = "over";

        /**
         * 所有待办数
         */
        String ALL_SUFFIX = "_bpm_invalid_object_tasks_all";
        /**
         * 作废数据存在进行中实例
         */
        String INVALID_SUFFIX = "_bpm_invalid_object_instances";
        /**
         * 校验的计数
         */
        String COUNT_SUFFIX = "_bpm_invalid_object_tasks_count";
        /**
         * 异步执行写redis状态
         */
        String STATUS_SUFFIX = "_bpm_invalid_object_tasks_write_status";
        /**
         * 作废数据的作废时间
         */
        String INVALID_INFO_SUFFIX = "_bpm_invalid_object_info";
    }
}
