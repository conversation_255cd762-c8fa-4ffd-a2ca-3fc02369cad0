package com.facishare.bpm.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMInstanceException;
import com.facishare.bpm.manage.TodoSessionKeyManager;
import com.facishare.bpm.model.RelevantTeam;
import com.facishare.bpm.model.meta.BPMInstanceObj;
import com.facishare.bpm.model.meta.BPMTaskObj;
import com.facishare.bpm.model.paas.engine.bpm.Task;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.facishare.bpm.proxy.ManageGroupProxy;
import com.facishare.bpm.proxy.OrganizationServiceProxy;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.model.org.Employee;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.WorkflowTransferService;
import com.facishare.bpm.util.TransferDataConstants;
import com.facishare.bpm.utils.DataTransferUtils;
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao;
import com.facishare.flow.mongo.bizdb.entity.PoolEntity;
import com.facishare.flow.repository.BPMTaskRepository;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc: 任务相关封装
 * version: 7.0.0
 * Created by cuiyongxu on 2020/2/17 3:24 PM
 */
@Slf4j
@Component
public class BpmDataToPGManager {


    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    @Autowired
    private WorkflowTransferService workflowTransferService;

    @Autowired
    private OrganizationServiceProxy organizationServiceProxy;

    @Autowired
    private DefinitionExtensionDao extensionDao;

    @Autowired
    private BPMTaskRepository bpmTaskRepository;

    @Autowired
    private TodoSessionKeyManager getTodoSessionKeyManager;

    @Autowired
    private PaasWorkflowServiceProxy paasWorkflowServiceProxy;

    @Autowired
    private ManageGroupProxy manageGroupProxy;
    /**
     * 创建流程实例
     * <p>
     * 已存在,返回map
     * 不存在,返回空
     *  @param context
     * @param instanceId
     * @return
     */
    public BPMInstanceObj createInstance(RemoteContext context, String instanceId) {
        BPMInstanceObj mateInstance = findMetaInstanceById(context,instanceId);
        //不需要重试,如果没有的话 更新任务的时候会创建
        if (Objects.isNull(mateInstance)) {
            return workflowTransferService.transfer(context, instanceId);
        }
        return mateInstance;
    }


    /**
     * 创建任务
     *
     * @param context
     * @param task
     * @param metadataInstance
     * @param getEmployeeFunction
     */
    protected void createTask(
            RemoteContext context,
            Task task,
            BPMInstanceObj metadataInstance,
            Function<List<String>, Map<Integer, Employee>> getEmployeeFunction) {
        //  创建任务
        //2020年05月22日16:56:40   由于终端 转发时,企信测会调用自定义对象查询数据,无人员数据不会同步到自定义对象,导致差不到,页面一直显示 正在加载中,
        // 未解析到人也同步到自定义对象,下发转发按钮的时候  也需要判断
        List<PoolEntity> pools = extensionDao.getPools(context.getTenantId(), task.getWorkflowId());
        BPMTaskObj mdTask = DataTransferUtils.transferTask(task, metadataInstance, pools, true, getEmployeeFunction,  getTodoSessionKeyManager.getSessionKey(context,task.getEntityId(),task.getExecutionType(),task.getExternalApplyActionCode(),task.getExternalFlow(),task.getId(), task.getElementApiName()), task.calculateActualDuration(context, manageGroupProxy));
        metadataService.createData(context, TransferDataConstants.APINAME_TASK, JSON.toJSONString(mdTask));
        log.info("BPMEventBus createTask : success! TENANT_ID={}, TASK_ID={}", context.getTenantId(), task.getId());
    }

    /**
     * 更新任务
     *
     * @param context
     * @param metadataTask
     * @param metadataInstance
     * @param task
     * @param getEmployeeFunction
     */
    protected void updateTask(
            RemoteContext context,
            BPMTaskObj metadataTask,
            BPMInstanceObj metadataInstance,
            Task task,
            Function<List<String>, Map<Integer, Employee>> getEmployeeFunction) {
//        sessoin key 只有新建的时候会获取一下,更新任务不会做处理,故传递为null即可
        String sessionKey = getTodoSessionKeyManager.getSessionKey(context, task.getEntityId(), task.getExecutionType(), task.getExternalApplyActionCode(), task.getExternalFlow(),task.getId(), task.getElementApiName());
        BPMTaskObj mdTask = DataTransferUtils.transferTask(task, metadataInstance, null, false, getEmployeeFunction, sessionKey, task.calculateActualDuration(context, manageGroupProxy));
        metadataService.updateData(context, TransferDataConstants.APINAME_TASK, task.getId(), JSON.toJSONString(mdTask), false, Boolean.FALSE);
        log.info("BPMEventBus updateTask Update Task Success! TENANT_ID={}, TASK_ID={}", context.getTenantId(), task.getId());
    }

    public void findAndModifyTask(RemoteContext context, Task task) {
        String instanceId = task.getWorkflowInstanceId();
        String taskId = task.getId();
        /**
         * 已存在,返回map
         * 不存在,创建并返回空
         */
        BPMInstanceObj mateInstance = createInstance(context, instanceId);
        //查找实例
        if (Objects.nonNull(mateInstance)) {
            //是否需要更新实例
            if (shouldUpdateInstance(mateInstance, task)) {
                Map<Integer, Employee> employees = organizationServiceProxy.getOutMembersByIds(context, new ArrayList<>(task.getCandidateIds()));

                Set<String> referenceObjectIds = bpmTaskRepository.getTaskObjectIdByInstanceId(context.getTenantId(), instanceId);

                DataTransferUtils.setParticipantsAndObjectIds(mateInstance, Lists.newArrayList(task), employees, referenceObjectIds);
                metadataService.updateData(context, TransferDataConstants.APINAME_INSTANCE, instanceId, JSON.toJSONString(mateInstance), false, Boolean.FALSE);
                log.info("BPMEventBus CreateTaskHandler Update Instance Success! TENANT_ID={}, INSTANCE_ID={}, TASK_ID={}", context.getTenantId(), instanceId, taskId);
            }
            //查询任务是否在自定义对象中已经存在
            BPMTaskObj metadataTask = findMetaTaskById(context, taskId);
            Function<List<String>, Map<Integer, Employee>> getEmployeeFunction = userIds -> organizationServiceProxy.getOutMembersByIds(context, Lists.newArrayList(userIds));
            if (Objects.isNull(metadataTask)) {
                createTask(context, task, mateInstance, getEmployeeFunction);
            } else {
                updateTask(context, metadataTask, mateInstance, task, getEmployeeFunction);
            }
        } else {
            log.warn("当前同步任务:{},实例:{}未被同步成功到元数据,执行重试操作", task.getId(), instanceId);
            throw new BPMInstanceException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SYNCHRONIZATION_TASK_NOT_FOUND_INSTANCE_RETRY_QUEUE);
        }
    }


    private BPMInstanceObj findMetaInstanceById(RemoteContext context, String objectId) {
        return JacksonUtil.fromJson(JacksonUtil.toJson(findDataById(context,TransferDataConstants.APINAME_INSTANCE,objectId)),BPMInstanceObj.class);
    }

    private BPMTaskObj findMetaTaskById(RemoteContext context, String objectId) {
        return JacksonUtil.fromJson(JacksonUtil.toJson(findDataById(context,TransferDataConstants.APINAME_TASK,objectId)),BPMTaskObj.class);
    }

    private Map<String, Object> findDataById(RemoteContext context, String entityId, String objectId) {
        try {
            return metadataService.findDataById(context, entityId, objectId, false, false, false, true, true,false, null).getObject_data();
        } catch (Throwable e) {
            log.warn("调用自定义对象查询实例或任务,未查询到:{},{},{}", context, entityId, objectId);
            return null;
        }
    }


    /**
     * 判断当前 任务 中的团队成员是否没有全包含在 实例的团队成员中
     * 或者
     * 任务的落地对象没有全部在实例的所涉及的对象列表中
     *
     * @param mateInstance
     * @param task
     * @return
     */
    private boolean shouldUpdateInstance(BPMInstanceObj mateInstance, Task task) {
        Set<RelevantTeam> instanceParticipants = mateInstance.getRelevant_team();
        Set<String> instanceParticipantIds = Sets.newHashSet();

        instanceParticipants.forEach(participant -> {
            if (CollectionUtils.isNotEmpty(participant.getTeamMemberEmployee())) {
                instanceParticipantIds.addAll(participant.getTeamMemberEmployee());
            }
        });

        Collection<String> taskParticipantIds = task.getCandidateIds();

        Set<String> instanceObjectIds = mateInstance.getObjectIds();
        String taskObjectId = task.getObjectId();
        if (CollectionUtils.isEmpty(taskParticipantIds) || Strings.isNullOrEmpty(taskObjectId)) {
            return false;
        }
        return !instanceParticipantIds.containsAll(taskParticipantIds) || !instanceObjectIds.contains(taskObjectId);
    }

    public Set<String> getCurrentCandidateIds(RemoteContext context, String instanceId) {
        List<Task> sourceTasks = paasWorkflowServiceProxy.getTasksByInstanceId(context, instanceId);
        return sourceTasks.stream()
                .filter(item -> TaskState.in_progress.equals(item.getState()) || TaskState.error.equals(item.getState()))
                .peek(item -> item.getCandidateIds().removeAll(item.getProcessIds()))
                .flatMap(item -> item.getCandidateIds().stream())
                .collect(Collectors.toSet());
    }



    public void findAndModifyInstance(RemoteContext context, WorkflowInstance workflowInstance, Set<String> currentCandidateIds) {
        List<PoolEntity> pools = extensionDao.getPools(context.getTenantId(), workflowInstance.getWorkflowId());
        if (Objects.isNull(findMetaInstanceById(context,workflowInstance.getId()))) {
            BPMInstanceObj mdInstance = getMDInstance(context, workflowInstance, pools, true);
            mdInstance.setCurrent_candidate_ids(currentCandidateIds);
            metadataService.createData(context, TransferDataConstants.APINAME_INSTANCE, JSON.toJSONString(mdInstance));
            log.info("BPMEventBus findAndModifyInstance instance to createInstance : success! TENANT_ID={}, INSTANCE_ID={}", context.getTenantId(), workflowInstance.getId());
        } else {
            BPMInstanceObj mdInstance = getMDInstance(context, workflowInstance, pools, false);
            mdInstance.setCurrent_candidate_ids(currentCandidateIds);
            metadataService.updateData(context, TransferDataConstants.APINAME_INSTANCE, mdInstance.get_id(), JSON.toJSONString(mdInstance), false, Boolean.FALSE);
            log.info("BPMEventBus findAndModifyInstance updateInstance : success! TENANT_ID={}, INSTANCE_ID={}", context.getTenantId(), mdInstance.get_id());
        }
    }


    private BPMInstanceObj getMDInstance(RemoteContext context, WorkflowInstance workflowInstance,
                                              List<PoolEntity> pools, boolean isNewData) {


        Set<String> referenceObjectIds = bpmTaskRepository.getTaskObjectIdByInstanceId(context.getTenantId(), workflowInstance.getId());

        BPMInstanceObj mdInstance = DataTransferUtils.transferInstance(workflowInstance, pools, isNewData, referenceObjectIds, workflowInstance.calculateActualDuration(context, manageGroupProxy));
        if (isNewData) {
            workflowTransferService.setMetadataOwner(context, mdInstance,
                    mdInstance.getApplicantId().stream().findFirst().get(),
                    mdInstance.getObjectApiName(),
                    mdInstance.getObjectDataId());
        }

        return mdInstance;
    }


}
