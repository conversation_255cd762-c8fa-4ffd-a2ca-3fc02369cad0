package com.facishare.bpm.manager;

import com.facishare.bpm.proxy.PaasMetadataProxy;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.rest.core.model.RemoteContext;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/5/26 2:34 下午
 */
@Slf4j
@Component
public class InvalidDataClearManager {

    @Autowired
    private CheckInvalidTaskRedisManager checkInvalidTaskRedisManager;

    @Autowired
    private MongoManager mongoManager;

    @Autowired
    private PaasMetadataProxy paasMetadataProxy;


    public Map<String, Set<CheckData>> execute(RemoteContext context, String entId) {

        //所有业务流待办数
        AtomicInteger count = new AtomicInteger();
        Map<String, Set<CheckData>> entityObject = Maps.newHashMap();
        //最终结果
        Map<String, Set<CheckData>> returnData = Maps.newHashMap();

        mongoManager.getTasks(context.getTenantId(), entId, (mapdata) -> {
            count.getAndIncrement();
            String entityId = MapUtil.instance.getString(mapdata, "entityId");
            String objectId = MapUtil.instance.getString(mapdata, "objectId");
            String taskId = mapdata.get("_id").toString();
            String workflowInstanceId = MapUtil.instance.getString(mapdata, "workflowInstanceId");
            Set<CheckData> checkDatas = entityObject.get(entityId);
            if (CollectionUtils.isEmpty(checkDatas)) {
                checkDatas = Sets.newHashSet();
                checkDatas.add(CheckData.create(workflowInstanceId, entityId, objectId, taskId));
            } else {
                checkDatas.add(CheckData.create(workflowInstanceId, entityId, objectId, taskId));
            }
            entityObject.put(entityId, checkDatas);

            //每20条进行查询
            if (checkDatas.size() >= 20) {
                Set<String> engineObjectIds = checkDatas.stream().filter(k -> {
                    boolean flag = Strings.isNullOrEmpty(k.getObjectId());
                    if (flag) {
                        log.info("taskId: {} objectId 不存在", k.getTaskId());
                    }
                    return !flag;
                }).map(CheckData::getObjectId).collect(Collectors.toSet());
                List<Map<String, Object>> metadata = paasMetadataProxy.findDataByIds(context, entityId, engineObjectIds);
                Set<CheckData> checkData = checkInvalid(metadata, engineObjectIds, checkDatas);
                Set<CheckData> returnCheckData = returnData.get(entityId);
                if (CollectionUtils.isEmpty(returnCheckData)) {
                    returnCheckData = Sets.newHashSet();
                }
                if (CollectionUtils.isNotEmpty(checkData)) {
                    returnCheckData.addAll(checkData);
                    returnData.put(entityId, returnCheckData);
                }
                entityObject.remove(entityId);
            }
        });

        //不满20 的则最后执行一次
        Set<String> entityIds = Sets.newHashSet(entityObject.keySet());
        entityIds.forEach(entityId -> {
            Set<CheckData> checkDatas = entityObject.get(entityId);
            Set<String> engineObjectIds = checkDatas.stream().map(CheckData::getObjectId).collect(Collectors.toSet());
            List<Map<String, Object>> metadata = paasMetadataProxy.findDataByIds(context, entityId, engineObjectIds);
            Set<CheckData> checkData = checkInvalid(metadata, engineObjectIds, checkDatas);
            Set<CheckData> returnCheckData = returnData.get(entityId);
            if (CollectionUtils.isEmpty(returnCheckData)) {
                returnCheckData = Sets.newHashSet();
            }
            if (CollectionUtils.isNotEmpty(checkData)) {
                returnCheckData.addAll(checkData);
                returnData.put(entityId, returnCheckData);
            }
            entityObject.remove(entityId);
        });


        //以下需要重构

        if (count.get() == 0) {
            return Maps.newHashMap();
        }

        return returnData;
    }

    private Set<CheckData> checkInvalid(List<Map<String, Object>> metadata, Set<String> engineObjectIds, Set<CheckData> checkDatas) {
        Set<CheckData> result = Sets.newHashSet();

        Multimap<String, CheckData> myMultimap = ArrayListMultimap.create();
        checkDatas.forEach(data -> {
            myMultimap.put(data.getObjectId(), data);
        });


        if (CollectionUtils.isEmpty(metadata)) {
            return checkDatas;
        } else if (engineObjectIds.size() != metadata.size()) {
            List<String> metadataObjectId = metadata.stream().map(k -> String.valueOf(k.get("_id"))).collect(Collectors.toList());
            //已删除的
            List<String> disjunction = Lists.newArrayList(engineObjectIds);
            disjunction.removeAll(metadataObjectId);// (List<String>) CollectionUtils.disjunction(engineObjectIds, metadataObjectId);
            disjunction.forEach(k -> result.addAll(myMultimap.get(k)));
        } else {
            //已作废数据
            metadata.forEach(k -> {
                if ("invalid".equals(String.valueOf(k.get("life_status")))) {
                    String id = String.valueOf(k.get("_id"));
                    String invalidTime = String.valueOf(k.get("last_modified_time"));
                    Collection<CheckData> checkData = myMultimap.get(id);
                    for (CheckData checkDatum : checkData) {
                        checkDatum.setLastModifiedTime(invalidTime);
                    }
                    result.addAll(checkData);
                }
            });
        }
        return result;
    }

    @Data
    static class CheckData {
        private String instanceId;
        private String entityId;
        private String objectId;
        private String lastModifiedTime;
        private String taskId;

        public static CheckData create(String instanceId, String entityId, String objectId, String taskId) {
            CheckData checkData = new CheckData();
            checkData.setEntityId(entityId);
            checkData.setObjectId(objectId);
            checkData.setInstanceId(instanceId);
            checkData.setTaskId(taskId);
            return checkData;
        }
    }
}
