package com.facishare.bpm.manager;

import com.facishare.paas.pod.client.DbRouterClient;
import com.facishare.paas.pod.dto.RouterInfo;
import com.facishare.paas.pod.exception.DbRouterException;
import com.facishare.paas.pod.exception.ErrorCode;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.mongodb.ReadPreference;
import com.mongodb.client.MongoDatabase;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/5/27 6:38 下午
 */
@Slf4j
@Component
public class MongoShardManager {


    private final String application = "workflow-engine";
    private final String biz = "FLOW";
    private String dialect = "mongodb";
    private List<ClientAndDb> clientAndDbs;

    @Autowired
    private DbRouterClient dbRouterClient;

    public List<MongoDatabase> getMongoDatabases() {
        List<String> jdbcUrl = getJdbcUrl();
        if (CollectionUtils.isEmpty(clientAndDbs) || jdbcUrl.size() > clientAndDbs.size()) {
            clientAndDbs = Lists.newArrayList();
            jdbcUrl.stream().filter(url -> !Strings.isNullOrEmpty(url)).forEach(url -> clientAndDbs.add(getClient(url)));
        }
        return clientAndDbs.stream().map(item -> item.mongoClient.getDatabase(item.databaseName)).collect(Collectors.toList());
    }

    public MongoDatabase getDbClientByTenantId(String tenantId) {
        RouterInfo routerInfo = dbRouterClient.queryRouterInfo(tenantId, biz, application, dialect);
        String jdbcUrl = routerInfo.getJdbcUrl();
        ClientAndDb client = getClient(jdbcUrl);
        return client.mongoClient.getDatabase(client.databaseName);
    }

    private List<String> getJdbcUrl() {
        List<RouterInfo> routerInfos = dbRouterClient.matchJdbcUrl(biz, dialect, application, "");
        if (CollectionUtils.isNotEmpty(routerInfos)) {
            return routerInfos.stream().map(RouterInfo::getJdbcUrl).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private ClientAndDb getClient(String mongoServer) {
        log.info("load config.mongo server ...");
        MongoClientOptions.Builder builder = new MongoClientOptions.Builder();
        builder.readPreference(ReadPreference.valueOf("secondary")).maxWaitTime(120000).connectionsPerHost(100).connectTimeout(5000).socketTimeout(60000);

        MongoClientURI clientURI = new MongoClientURI(mongoServer, builder);
        MongoClient mongoClient = new MongoClient(clientURI);
        log.info("load config.create mongo client success ...");
        return new ClientAndDb(mongoServer, mongoClient, getDBName(mongoServer));
    }

    private String getDBName(String url) {
        List<String> temps = Splitter.on("/").splitToList(url).stream().filter(item -> !Strings.isNullOrEmpty(item)).collect(Collectors.toList());
        return temps.get(temps.size() - 1);
    }

    /**
     * 企业是否存在路由
     */
    public boolean routeExists(String tenantId){
        try{
            dbRouterClient.queryRouterInfo(tenantId, biz, application, dialect);
        }catch (DbRouterException e){
            if(Objects.equals(e.getPodErrorCode(), ErrorCode.ROUTE_NOT_FOUND)||Objects.equals(e.getPodErrorCode(), ErrorCode.TENANT_INVALID)){
                return false;
            }
        }
        return true;
    }

    public boolean routeNotExists(String tenantId){
        return !routeExists(tenantId);
    }

    @Data
    class ClientAndDb {
        private String mongoUri;
        private MongoClient mongoClient;
        private String databaseName;

        public ClientAndDb(String mongoUri, MongoClient mongoClient, String databaseName) {
            this.mongoUri = mongoUri;
            this.mongoClient = mongoClient;
            this.databaseName = databaseName;
        }
    }

}
