package com.facishare.bpm.codec;

import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 15/02/2017.
 */
@Slf4j
public class FsiResourceCodeC implements IRestCodeC {
    private static final String FSI_STATUS = "fsi-status";
    private static final String FSI_CONTENT = "fsi-content";
    private static final String FSI_CODE = "fsi-failcode";

    @Override
    public <T> byte[] encodeArg(T obj) {

        Schema schema = RuntimeSchema.getSchema(obj.getClass());
        byte[] bytes = ProtobufIOUtil.toByteArray(obj, schema, allocLinkedBuffer());

        return bytes;
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        String fsiStatus = headers.get(FSI_STATUS) != null ? headers.get(FSI_STATUS).get(0) : null;

        /**
         * process fsi status
         */
        if (fsiStatus == null) {
            throw new RestProxyInvokeException("FsiStatusNotFound!");
        }
        FsiStatus status;
        try {
            status = FsiStatus.valueOf(fsiStatus.toLowerCase());
        } catch (IllegalArgumentException e) {
            throw new RestProxyInvokeException("Unknown FsiStatus:" + fsiStatus);
        }

        switch (status) {
            case success:
                if (bytes == null || bytes.length == 0) {
                    log.warn("Console FsiResourceCodeC invoke result is null");
                    return null;
                }
                Schema<T> schema = RuntimeSchema.getSchema(clazz);
                T ret = schema.newMessage();
                ProtobufIOUtil.mergeFrom(bytes, ret, schema);
                return ret;
            case failure:
                int code = Integer.parseInt(headers.get(FSI_CODE).get(0));
                String message = new String(bytes);
                throw new RestProxyBusinessException(code, "FsiFailure,Code:" + code + ",Message:" + message);
            case error:
                String error = headers.get(FSI_CONTENT).get(0);
                throw new RestProxyInvokeException("FsiError:" + error + ",response:" + new String(bytes));
            default:
                throw new RestProxyInvokeException("FsiStatus:" + status);
        }


    }

    private static LinkedBuffer allocLinkedBuffer() {
        return LinkedBuffer.allocate(2048);
    }

    public enum FsiStatus {
        success,
        error,
        failure,
        notimplemented,
    }
}
