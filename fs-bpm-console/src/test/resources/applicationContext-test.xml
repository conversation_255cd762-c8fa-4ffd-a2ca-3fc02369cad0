<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">


    <import resource="classpath:spring/fs-bpm-fsi-proxy.xml"/>
    <import resource="classpath:spring/fs-bpm-rest-proxy.xml"/>
    <import resource="classpath:spring/fs-bpm-mq.xml"/>
    <import resource="classpath*:spring/spring-pg.xml"/>
    <import resource="classpath:spring/license-client.xml"/>
    <import resource="classpath:spring/flow-public-db-config.xml"/>




    <context:component-scan base-package="com.facishare.bpm">
            <context:exclude-filter type="regex" expression="com\.facishare\.bpm\.processor\..*"/>
    </context:component-scan>
    <context:annotation-config/>
    <context:property-placeholder location="classpath:application.properties"/>

    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="dubbo-common"/>

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>

    <bean id="podClient" class="com.facishare.paas.pod.client.PodClient" p:configName="fs-pod-client-config"/>
    <task:annotation-driven/>

</beans>
