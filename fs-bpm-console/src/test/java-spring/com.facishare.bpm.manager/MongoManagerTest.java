package com.facishare.bpm.manager;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.mongodb.client.MongoCollection;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/6/24 10:50 上午
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext-test.xml"})
public class MongoManagerTest {

    @Autowired
    private MongoManager mongoManager;

    @Test
    public void clentTest() {
        String tenantId = "71557";
        MongoCollection<Document> workflows = mongoManager.getWorkflowDBConnection("workflows", tenantId);
        Document query = new Document();
        query.put("appId", BPMConstants.APP_ID);
        query.put("tenantId", tenantId);
        System.out.println(workflows.count(query));
    }


}
