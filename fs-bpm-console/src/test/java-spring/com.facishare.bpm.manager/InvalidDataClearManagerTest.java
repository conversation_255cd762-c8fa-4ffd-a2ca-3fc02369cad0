package com.facishare.bpm.manager;

import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/5/26 2:49 下午
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:applicationContext.xml"})
public class InvalidDataClearManagerTest {

    @Autowired
    private InvalidDataClearManager invalidDataClearManager;

    @Test
    public void getData() {
        String entityId = "";
        RemoteContext context = new RemoteContext("", "71554", "BPM", "-1000");
        Map<String, Set<InvalidDataClearManager.CheckData>> result = invalidDataClearManager.execute(context, entityId);
        log.info("涉及对象:{}个", result.size());
        result.forEach((key, value) -> {
            List<String> instanceIds = value.stream().map(InvalidDataClearManager.CheckData::getInstanceId).collect(Collectors.toList());
            log.info("对象:{},实例:{}", key, JsonUtil.toJson(instanceIds));
        });
    }
}
