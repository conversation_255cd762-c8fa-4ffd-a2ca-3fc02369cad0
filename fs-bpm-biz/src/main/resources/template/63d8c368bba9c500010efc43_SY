{"sourceWorkflowId": "63d8c368bba9c500010efc43", "name": "对账单预设流程模板", "enabled": true, "description": "该业务流是系统预设的，管理员可根据需要调整", "entryType": "TransactionStatementObj", "entryTypeName": "交易对账单", "rangeEmployeeIds": [], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": ["00000000000000000000000000000016", "00000000000000000000000000000002", "00000000000000000000000000000003"], "workflow": {"externalFlow": 0, "linkAppType": 1, "modifier": "1000", "linkApp": "FSAID_11490c84", "entityId": "TransactionStatementObj", "history": false, "type": "workflow_bpm", "linkAppName": "订货通", "singleInstanceFlow": 1, "appId": "BPM", "linkAppEnable": true, "name": "对账单预设流程模板", "description": "该业务流是系统预设的，管理员可根据需要调整", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1675149745635"}, {"type": "userTask", "bpmExtension": {"executionType": "update", "executionName": "编辑对象", "entityId": "TransactionStatementObj", "entityName": "交易对账单", "objectId": {"expression": "activity_0##TransactionStatementObj"}, "commonButtonApiNames": "", "defaultButtons": {"update": {"label": "业务确认"}, "Save": {"label": "保存"}, "UpdateAndComplete": {"label": "保存并完成"}}}, "canSkip": false, "remind": true, "linkAppEnable": false, "name": "财务确认", "description": "", "id": "1675149745636", "assignee": {"role": ["00000000000000000000000000000002", "00000000000000000000000000000003"]}, "assigneeType": "assignee", "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "1675149745645"}, {"type": "userTask", "bpmExtension": {"executionName": "操作对象(多选)", "entityName": "交易对账单", "entityId": "TransactionStatementObj", "executionType": "operationMulti", "objectId": {"expression": "activity_1675149745636##TransactionStatementObj"}, "commonButtonApiNames": ["ConfirmReconciliation_button_default"]}, "linkAppType": 1, "linkApp": "FSAID_11490c84", "remind": true, "linkAppName": "订货通", "linkAppEnable": true, "name": "确认对账", "description": "", "id": "1675166842883", "assignee": {"extUserType": ["${activity_1675166842883##TransactionStatementObj##confirm_person}"]}, "assigneeType": "assignee", "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "操作对象(多选)", "entityName": "交易对账单", "entityId": "TransactionStatementObj", "executionType": "operationMulti", "objectId": {"expression": "activity_1675149745636##TransactionStatementObj"}, "commonButtonApiNames": ["InitiateReconciliation_button_default"]}, "linkAppEnable": false, "remind": true, "name": "发起对账", "description": "", "id": "1675166842896", "assignee": {"role": ["00000000000000000000000000000002"]}, "assigneeType": "assignee", "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}], "transitions": [{"id": "1675149745638", "fromId": "1675149745635", "toId": "1675149745636", "serialNumber": 0}, {"id": "1675166842884", "fromId": "1675166842883", "toId": "1675149745645", "serialNumber": 1}, {"id": "1675166842897", "fromId": "1675149745636", "toId": "1675166842896", "serialNumber": 2}, {"id": "1675166842898", "fromId": "1675166842896", "toId": "1675166842883", "serialNumber": 3}], "variables": [{"id": "activity_0##TransactionStatementObj", "type": {"name": "text"}}, {"id": "activity_1675149745636##TransactionStatementObj", "type": {"name": "text"}}, {"id": "activity_1675166842883##TransactionStatementObj", "type": {"name": "text"}}, {"id": "activity_1675166842896##TransactionStatementObj", "type": {"name": "text"}}], "id": "63e30dcffb9f983264f960d4", "sourceWorkflowId": "63d8c368bba9c500010efc43", "createTime": 1675150185567, "tenantId": "68867"}, "rule": {"ruleId": "63e30dcffb9f983264f960d5", "deleted": false, "entityId": "TransactionStatementObj", "conditionPattern": "(0 and 1)", "conditions": [{"leftSide": {"fieldName": "activity_0##TransactionStatementObj##status", "fieldSrc": "activity_0##TransactionStatementObj", "fieldType": "string"}, "operator": "equals", "rightSide": {"value": "1", "metadata": {"containSubDept": false}}, "rowNo": 0}, {"leftSide": {"fieldName": "activity_0##TransactionStatementObj##completion_mark", "fieldSrc": "activity_0##TransactionStatementObj", "fieldType": "string"}, "operator": "equals", "rightSide": {"value": "2", "metadata": {"containSubDept": false}}, "rowNo": 1}], "workflowSrcId": "63d8c368bba9c500010efc43"}, "extension": {"pools": [{"lanes": [{"id": "1675149745637", "name": "1.内部审核", "description": "", "activities": ["1675149745635", "1675149745636"]}]}, {"lanes": [{"id": "1675166842882", "name": "2.发起对账", "description": "", "activities": ["1675166842896"]}]}, {"lanes": [{"id": "1675166842895", "name": "3.确认对账", "description": "", "activities": ["1675149745645", "1675166842883"]}]}], "diagram": [{"id": "1675166842895", "attr": {"width": 220, "height": 540, "x": 520, "y": 60}}, {"id": "1675166842882", "attr": {"width": 220, "height": 540, "x": 300, "y": 60}}, {"id": "1675149745637", "attr": {"width": 220, "height": 540, "x": 80, "y": 60}}, {"id": "1675149745635", "attr": {"width": 60, "height": 60, "x": 160, "y": 125}}, {"id": "1675149745636", "attr": {"width": 160, "height": 50, "x": 110, "y": 240}}, {"id": "1675149745645", "attr": {"width": 60, "height": 60, "x": 610, "y": 430}}, {"id": "1675166842883", "attr": {"width": 160, "height": 50, "x": 560, "y": 330}}, {"id": "1675166842896", "attr": {"width": 160, "height": 50, "x": 330, "y": 240}}, {"id": "1675166842898", "attr": {"d": "M490,265 h16 a20,20 0 0 1 20,20 v50 a20,20 0 0 0 20,20 h16", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1675166842897", "attr": {"d": "M270,265 h31 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h31", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1675166842884", "attr": {"d": "M640,380 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1675149745638", "attr": {"d": "M190,185 v28 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v28", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=650 width=790 tabindex=\"0\"><defs><marker id=\"end-arrow_1675924029557\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1675924029557\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1675924029557\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1675924029557\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n          background-color: #f3f3f5;\n        }\n\n        g[type=pool] {\n          font-size: 13px;\n        }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g name=\"pool-wrapper\"><g data-id=\"1675166842895\" shape=\"rectangle\" type=\"pool\" x=\"520\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(520,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"rgba(255,255,255,0.4)\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#5498FF\" rx=\"0\" ry=\"0\" resizable=\"true\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"32\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(10,15)\"><tspan>3.确认对账</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1675166842882\" shape=\"rectangle\" type=\"pool\" x=\"300\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(300,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"rgba(255,255,255,0.4)\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#5498FF\" rx=\"0\" ry=\"0\" resizable=\"true\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"32\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(10,15)\"><tspan>2.发起对账</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1675149745637\" shape=\"rectangle\" type=\"pool\" x=\"80\" y=\"60\" width=\"220\" height=\"540\" transform=\"translate(80,60)\" tabindex=\"0\" class=\"paas-bpm-resizable\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"rgba(255,255,255,0.4)\" width=\"220\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#5498FF\" rx=\"0\" ry=\"0\" resizable=\"true\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"220\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"215.5\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"105.5\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"32\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(10,15)\"><tspan>1.内部审核</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><g name=\"line-wrapper\"><g tabindex=\"0\" data-id=\"1675166842898\"><path d=\"M490,265 h16 a20,20 0 0 1 20,20 v50 a20,20 0 0 0 20,20 h16\" start-id=\"1675166842896\" end-id=\"1675166842883\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1675924029557)\"></path></g><g tabindex=\"0\" data-id=\"1675166842897\"><path d=\"M270,265 h31 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h31\" start-id=\"1675149745636\" end-id=\"1675166842896\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1675924029557)\"></path></g><g tabindex=\"0\" data-id=\"1675166842884\"><path d=\"M640,380 v26 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v26\" start-id=\"1675166842883\" end-id=\"1675149745645\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1675924029557)\"></path></g><g tabindex=\"0\" data-id=\"1675149745638\"><path d=\"M190,185 v28 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v28\" start-id=\"1675149745635\" end-id=\"1675149745636\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1675924029557)\"></path></g></g><g data-id=\"1675149745635\" shape=\"rectangle\" type=\"startEvent\" x=\"160\" y=\"125\" width=\"60\" height=\"60\" transform=\"translate(160,125)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#16B4AB\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#16B4AB\" stroke-width=\"3\" color=\"#FFFFFF\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#FFFFFF\" transform=\"translate(30,35)\"><tspan>开始</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1675149745636\" shape=\"rectangle\" type=\"userTask\" x=\"110\" y=\"240\" width=\"160\" height=\"50\" transform=\"translate(110,240)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>财务确认</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1675149745645\" shape=\"rectangle\" type=\"endEvent\" x=\"610\" y=\"430\" width=\"60\" height=\"60\" transform=\"translate(610,430)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#737C8C\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#737C8C\" stroke-width=\"3\" color=\"#FFFFFF\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#FFFFFF&quot; stroke-width=&quot;2&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#FFFFFF\" stroke-width=\"2\" color=\"#e67373\" type=\"rect\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#FFFFFF\" transform=\"translate(30,35)\"><tspan>结束</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1675166842883\" shape=\"rectangle\" type=\"userTask\" x=\"560\" y=\"330\" width=\"160\" height=\"50\" transform=\"translate(560,330)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>确认对账</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1675166842896\" shape=\"rectangle\" type=\"userTask\" x=\"330\" y=\"240\" width=\"160\" height=\"50\" transform=\"translate(330,240)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>发起对账</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "externalFlow": 0, "singleInstanceFlow": 1, "isDeleted": false, "hasInstance": true, "linkAppEnable": true, "linkApp": "FSAID_11490c84", "linkAppName": "订货通", "linkAppType": 1, "errorNotifyRecipients": {}, "supportExternalFlow": true, "scope": ["订单管理员", "回款财务", "订单财务"]}