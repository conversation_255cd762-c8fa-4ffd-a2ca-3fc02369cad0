{"id": null, "tenantId": "2", "userId": "-10000", "sourceWorkflowId": null, "workflowId": null, "name": "复杂的分支流程234f1cae-0", "count": 0, "enabled": true, "description": "", "entryType": "AccountObj", "entryTypeName": "客户", "rangeEmployeeIds": [1000], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": [], "createdBy": "-10000", "createTime": *************, "lastModifiedBy": "-10000", "lastModifiedTime": *************, "workflow": {"activities": [{"description": "", "id": "*************", "name": "开始", "type": "startEvent"}, {"assignNextTask": 0.0, "assignee": {"ext_bpm": ["${instance##owner}"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionName": "编辑对象", "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "canSkip": false, "description": "", "id": "*************", "latencyTime": "3", "latencyUnit": "2", "name": "定时等待节点", "taskType": "anyone", "timeType": "1", "type": "latencyTask"}, {"defaultTransitionId": "*************", "description": "", "id": "*************", "name": "网关", "type": "exclusiveGateway"}, {"assignNextTask": 0.0, "assignee": {"ext_bpm": ["${instance##owner}"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionName": "编辑对象", "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "description": "", "id": "*************", "name": "分支1", "taskType": "anyone", "type": "userTask"}, {"assignNextTask": 0.0, "assignee": {"ext_bpm": ["${instance##owner}"]}, "bpmExtension": {"entityId": "AccountObj", "entityName": "客户", "executionName": "编辑对象", "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "description": "", "id": "*************", "name": "分支2", "taskType": "anyone", "type": "userTask"}, {"description": "", "id": "*************", "name": "结束", "type": "endEvent"}], "transitions": [{"fromId": "*************", "id": "*************", "serialNumber": 0.0, "toId": "*************"}, {"fromId": "*************", "id": "*************", "serialNumber": 1.0, "toId": "*************"}, {"condition": {"conditions": [{"conditions": [{"left": {"expression": "activity_*************##executionType"}, "right": {"metadata": {"containSubDept": false}, "type": {"name": "text"}, "value": "person"}, "type": "equals"}], "type": "and"}], "type": "or"}, "description": "", "fromId": "*************", "id": "*************", "serialNumber": 2.0, "toId": "*************"}, {"condition": {"conditions": [{"conditions": [{"left": {"expression": "activity_*************##executionType"}, "right": {"metadata": {"containSubDept": false}, "type": {"name": "text"}, "value": "overrun"}, "type": "equals"}], "type": "and"}], "type": "or"}, "description": "", "fromId": "*************", "id": "*************", "serialNumber": 3.0, "toId": "*************"}, {"fromId": "*************", "id": "*************", "serialNumber": 4.0, "toId": "*************"}, {"fromId": "*************", "id": "*************", "serialNumber": 5.0, "toId": "*************"}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##executionType", "type": {"name": "text"}}]}, "rule": {"ruleId": null, "deleted": false, "tenantId": null, "appId": null, "entityId": null, "ruleType": null, "triggerTypes": null, "conditionPattern": "", "conditions": [], "workflowSrcId": null, "createTime": null, "creator": null, "modifyTime": null, "modifier": null, "allFields": []}, "svg": null, "extension": {"id": null, "pools": [{"id": null, "name": null, "lanes": [{"id": "*************", "name": "阶段", "description": "", "activities": ["*************", "*************", "*************", "*************", "*************", "*************"]}]}], "diagram": [], "svg": "<svg></svg>", "workflowId": null}, "templateId": null, "externalFlow": 0, "singleInstanceFlow": 0, "hasInstance": false, "draftId": null, "supportFlow": "market", "deleted": false, "ruleJson": null, "new": true}