{"id": "64b60071fbef3700015351cf", "tenantId": "71557", "userId": "1007", "sourceWorkflowId": "64b60071fbef3700015351ce", "workflowId": "64b60bb4f5297162f0c972d4", "name": "流程变量测试1", "count": 0, "enabled": true, "description": "", "entryType": "object_UPfNh__c", "entryTypeName": "自行车(INAG)", "rangeEmployeeIds": ["1007"], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": [], "createdBy": "1007", "createTime": 1689649265263, "lastModifiedBy": "1002", "lastModifiedTime": 1689652147761, "workflow": {"creator": "1007", "externalFlow": 0, "modifier": "1002", "entityId": "object_UPfNh__c", "errorNotifyRecipients": {}, "history": false, "type": "workflow_bpm", "singleInstanceFlow": 0, "modifyTime": 1689652148885, "enable": true, "appId": "BPM", "linkAppEnable": false, "customVariableTable": [{"default_value": 0, "label": "产品总和", "id": "custom_variable##count__c", "type": "number"}, {"default_value": false, "label": "产品明细是否存在已下架", "id": "custom_variable##aaa__c", "type": "boolean"}, {"default_value": "", "label": "产品品牌", "id": "custom_variable##cuiyongxu__c", "type": "text"}, {"label": "产品明细名称", "id": "custom_variable##sss__c", "type": "list"}], "name": "流程变量测试1", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1689649199514"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "update", "executionName": "编辑对象", "entityId": "object_UPfNh__c", "entityName": "自行车(INAG)", "objectId": {"expression": "activity_0##object_UPfNh__c"}}, "canSkip": false, "remind": true, "linkAppEnable": false, "name": "业务活动", "description": "", "id": "1689649199515", "assignee": {"ext_bpm": ["${instance##owner}"]}, "assigneeType": "assignee", "taskType": "anyone", "rule": {"conditionPattern": "(0)", "conditions": [{"leftSide": {"fieldName": "custom_variable##cuiyongxu__c", "fieldSrc": "custom_variable", "fieldType": "string"}, "operator": "equals", "rightSide": {"value": "VywulqfMn", "metadata": {"containSubDept": false}}, "rowNo": 0}]}, "assignNextTask": 0, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "1689649199519"}, {"type": "executionTask", "bpmExtension": {"executionType": "execution", "entityName": "自行车(INAG)", "entityId": "object_UPfNh__c", "objectId": {"expression": "activity_1689649199515##object_UPfNh__c"}}, "remind": true, "name": "等待节点", "description": "", "id": "1689649199524", "itemList": [], "delay": true, "delayStrategy": "condition", "latencyUnit": 2, "remindLatency": 1, "rule": {"conditionPattern": "(0)", "conditions": [{"leftSide": {"fieldName": "custom_variable##cuiyongxu__c", "fieldSrc": "custom_variable", "fieldType": "string"}, "operator": "equals", "rightSide": {"value": "VywulqfMn", "metadata": {"containSubDept": false}}, "rowNo": 0}]}}, {"id": "1689649199528", "name": "分支节点", "description": "", "type": "exclusiveGateway", "defaultTransitionId": "1689649199530"}], "transitions": [{"id": "1689649199517", "fromId": "1689649199514", "toId": "1689649199515", "serialNumber": 0}, {"id": "1689649199525", "fromId": "1689649199515", "toId": "1689649199524", "serialNumber": 1}, {"id": "1689649199529", "fromId": "1689649199524", "toId": "1689649199528", "serialNumber": 2}, {"id": "1689649199530", "fromId": "1689649199528", "toId": "1689649199519", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"left": {"expression": "custom_variable##cuiyongxu__c"}, "type": "equals", "right": {"value": "rvgu3fymC", "type": {"name": "text"}, "metadata": {"containSubDept": false}}}]}]}, "description": "", "serialNumber": 3}, {"id": "1689649199533", "fromId": "1689649199528", "toId": "1689649199515", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"left": {"expression": "activity_0##object_UPfNh__c##field_7teqa__c"}, "type": "equals", "right": {"value": "i4oham411", "type": {"name": "text"}, "metadata": {"containSubDept": false}}}]}]}, "description": "", "serialNumber": 4}], "variables": [{"id": "activity_0##object_UPfNh__c", "type": {"name": "text"}}, {"id": "activity_1689649199515##object_UPfNh__c", "type": {"name": "text"}}, {"id": "custom_variable##cuiyongxu__c", "type": {"name": "text"}}, {"id": "activity_1689649199524##object_UPfNh__c", "type": {"name": "text"}}, {"id": "activity_0##object_UPfNh__c##field_7teqa__c", "type": {"name": "text"}}], "id": "64b60bb4f5297162f0c972d4", "sourceWorkflowId": "64b60071fbef3700015351ce", "createTime": 1689649265506, "tenantId": "71557"}, "extension": {"pools": [{"lanes": [{"id": "1689649199516", "name": "阶段", "description": "", "activities": ["1689649199514", "1689649199519", "1689649199515", "1689649199524", "1689649199528"]}]}], "diagram": [{"id": "1689649199516", "attr": {"width": 940, "height": 540, "x": 80, "y": 60}}, {"id": "1689649199514", "attr": {"width": 60, "height": 60, "x": 160, "y": 125}}, {"id": "1689649199519", "attr": {"width": 60, "height": 60, "x": 162, "y": 463}}, {"id": "1689649199515", "attr": {"width": 160, "height": 50, "x": 120, "y": 260}}, {"id": "1689649199524", "attr": {"width": 160, "height": 50, "x": 380, "y": 270}}, {"id": "1689649199528", "attr": {"width": 100, "height": 100, "x": 680, "y": 260}}, {"id": "1689649199533", "attr": {"d": "M680,261 v-20 a20,20 0 0 0 -20,-20 h-440 a20,20 0 0 0 -20,20 v20", "toPosition": "top", "fromPosition": "top", "type": "polyline"}}, {"id": "1689649199532", "attr": {}}, {"id": "1689649199531", "attr": {}}, {"id": "1689649199530", "attr": {"d": "M680,360 v113 a20,20 0 0 1 -20,20 h-438", "toPosition": "right", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1689649199529", "attr": {"d": "M540,295 h38 a7.5,7.5 0 0 1 7.5,7.5 v0 a7.5,7.5 0 0 0 7.5,7.5 h38", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1689649199526", "attr": {}}, {"id": "1689649199525", "attr": {"d": "M280,285 h46 a5,5 0 0 1 5,5 v0 a5,5 0 0 0 5,5 h46", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1689649199517", "attr": {"d": "M190,185 v33 a5,5 0 0 0 5,5 h0 a5,5 0 0 1 5,5 v33", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=650 width=1070 tabindex=\"0\"><defs><marker id=\"end-arrow_1689649199527\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1689649199527\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1689649199527\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1689649199527\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n          background-color: #f3f3f5;\n        }\n\n        g[type=pool] {\n          font-size: 13px;\n        }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g name=\"pool-wrapper\"><g data-id=\"1689649199516\" shape=\"rectangle\" type=\"pool\" x=\"80\" y=\"60\" width=\"940\" height=\"540\" transform=\"translate(80,60)\" tabindex=\"0\" class=\"paas-bpm-resizable bpm-draw-polyline-able\"><rect fill=\"rgba(255,255,255,0.4)\" width=\"940\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#5498FF\" rx=\"0\" ry=\"0\" resizable=\"true\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;32&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot;  class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"940\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"465.5\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"935.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"465.5\" y=\"535.5\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"220\" height=\"32\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><text node-name=\"node-name\" transform=\"translate(10,15)\"><tspan>阶段</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\" trans-y=\"550\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g></g><g name=\"line-wrapper\"><g fill=\"transparent\" tabindex=\"0\" data-id=\"1689649199533\"><path type=\"polyline\" d=\"M680,261 v-20 a20,20 0 0 0 -20,-20 h-440 a20,20 0 0 0 -20,20 v20\" from-position=\"top\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1689649199527)\" to-position=\"top\" start-id=\"1689649199528\" end-id=\"1689649199515\"></path></g><g fill=\"transparent\" tabindex=\"0\" data-id=\"1689649199532\"></g><g fill=\"transparent\" tabindex=\"0\" data-id=\"1689649199531\"></g><g fill=\"transparent\" tabindex=\"0\" data-id=\"1689649199530\"><path type=\"polyline\" d=\"M680,360 v113 a20,20 0 0 1 -20,20 h-438\" from-position=\"bottom\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1689649199527)\" to-position=\"right\" start-id=\"1689649199528\" end-id=\"1689649199519\"></path></g><g fill=\"transparent\" tabindex=\"0\" data-id=\"1689649199529\"><path type=\"polyline\" d=\"M540,295 h38 a7.5,7.5 0 0 1 7.5,7.5 v0 a7.5,7.5 0 0 0 7.5,7.5 h38\" from-position=\"right\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1689649199527)\" to-position=\"left\" start-id=\"1689649199524\" end-id=\"1689649199528\"></path></g><g tabindex=\"0\" data-id=\"1689649199526\"></g><g tabindex=\"0\" data-id=\"1689649199525\"><path d=\"M280,285 h46 a5,5 0 0 1 5,5 v0 a5,5 0 0 0 5,5 h46\" start-id=\"1689649199515\" end-id=\"1689649199524\" fill=\"transparent\" stroke-width=\"2\" type=\"polyline\" to-position=\"left\" from-position=\"right\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1689649199527)\"></path></g><g tabindex=\"0\" data-id=\"1689649199517\"><path d=\"M190,185 v33 a5,5 0 0 0 5,5 h0 a5,5 0 0 1 5,5 v33\" start-id=\"1689649199514\" end-id=\"1689649199515\" fill=\"transparent\" stroke-width=\"2\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1689649199527)\"></path></g></g><g data-id=\"1689649199514\" shape=\"rectangle\" type=\"startEvent\" x=\"160\" y=\"125\" width=\"60\" height=\"60\" transform=\"translate(160,125)\" tabindex=\"0\" class=\"\"><rect fill=\"#16B4AB\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#16B4AB\" stroke-width=\"3\" color=\"#FFFFFF\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#FFFFFF\" transform=\"translate(30,35)\"><tspan>开始</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1689649199519\" shape=\"rectangle\" type=\"endEvent\" x=\"162\" y=\"463\" width=\"60\" height=\"60\" transform=\"translate(162,463)\" tabindex=\"0\" class=\"\"><rect fill=\"#737C8C\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#737C8C\" stroke-width=\"3\" color=\"#FFFFFF\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#FFFFFF&quot; stroke-width=&quot;2&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#FFFFFF\" stroke-width=\"2\" color=\"#e67373\" type=\"rect\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#FFFFFF\" transform=\"translate(30,35)\"><tspan>结束</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\" trans-y=\"70\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1689649199515\" shape=\"rectangle\" type=\"userTask\" x=\"120\" y=\"260\" width=\"160\" height=\"50\" transform=\"translate(120,260)\" tabindex=\"0\" class=\"\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>业务活动</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1689649199524\" shape=\"rectangle\" type=\"executionTask\" x=\"380\" y=\"270\" width=\"160\" height=\"50\" transform=\"translate(380,270)\" tabindex=\"0\" class=\"\" status=\"normal\"><rect fill=\"#E6F4FF\" width=\"160\" height=\"50\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(80,30)\" textTransformLineTwo=\"translate(80,20)\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(80,30)\"><tspan>等待节点</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(80,60)\" trans-y=\"60\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\" width=\"100\" height=\"20\">该节点有且只能有一根连出的线</tspan></text></g></g><g data-id=\"1689649199528\" type=\"exclusiveGateway\" shape=\"diamond\" transform=\"translate(680,260)\" x=\"680\" y=\"260\" width=\"100\" height=\"100\" tabindex=\"0\" status=\"normal\" class=\"bpm-shape-focus-node bpm-draw-polyline-able node-focused\"><rect fill=\"#E6F4FF\" width=\"70.71067811865474\" height=\"70.71067811865474\" stroke=\"#368DFF\" rx=\"3\" ry=\"3\" textTransformLineOne=\"translate(0,55)\" textTransformLineTwo=\"translate(0,43)\" type=\"rect\" transform=\"rotate(45)\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" transform=\"translate(-50,0)\" stroke=\"#49bffc\"><rect width=\"100\" height=\"100\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"41.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"41.5\" y=\"-8.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"95.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"91.5\" y=\"41.5\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"45.5\" y=\"95.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"41.5\" y=\"91.5\"></rect></g><text node-name=\"node-name\" text-anchor=\"middle\" alignment-baseline=\"middle\" transform=\"translate(0,55)\"><tspan>分支节点</tspan></text><g name=\"error-node\" class=\"hide\" transform=\"translate(0,110)\" trans-y=\"110\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\" width=\"100\" height=\"20\">该节点配置错误</tspan></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "externalFlow": 0, "singleInstanceFlow": 0, "isDeleted": false, "hasInstance": false, "linkAppEnable": false, "rule": {"conditionPattern": "", "conditions": []}, "supportExternalFlow": true, "scope": ["崔永旭"]}