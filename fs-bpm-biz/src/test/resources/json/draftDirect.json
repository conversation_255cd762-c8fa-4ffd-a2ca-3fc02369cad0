{"appId": "BPM", "workflow": {"activities": [{"id": "1532157365931", "type": "startEvent", "name": "开始", "description": ""}, {"id": "1532157365932", "type": "userTask", "name": "A", "description": "", "canSkip": false, "taskType": "anyone", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "bpmExtension": {"actionCode": "", "executionType": "update", "executionName": "编辑对象", "entityId": "object_UPfNh__c", "entityName": "自行车(INAG)", "objectId": {"expression": "activity_0##object_UPfNh__c"}}, "assignNextTask": 0}, {"id": "1532157365936", "name": "B", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "自行车(INAG)", "entityId": "object_UPfNh__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1532157365936##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_UPfNh__c"}}, "type": "userTask", "assignNextTask": 0}, {"id": "1532157365937", "name": "C", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "自行车(INAG)", "entityId": "object_UPfNh__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1532157365937##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_UPfNh__c"}}, "type": "userTask", "assignNextTask": 0}, {"id": "1532157365938", "name": "结束", "description": "", "type": "endEvent"}], "variables": [{"id": "activity_0##object_UPfNh__c", "type": {"name": "text"}}, {"id": "activity_1532157365932##object_UPfNh__c", "type": {"name": "text"}}, {"id": "activity_1532157365936##object_UPfNh__c", "type": {"name": "text"}}, {"id": "activity_1532157365937##object_UPfNh__c", "type": {"name": "text"}}], "transitions": [{"id": "1532157365934", "fromId": "1532157365931", "toId": "1532157365932", "serialNumber": 0}, {"id": "1532157365939", "fromId": "1532157365932", "toId": "1532157365936", "serialNumber": 1}, {"id": "1532157365940", "fromId": "1532157365936", "toId": "1532157365937", "serialNumber": 2}, {"id": "1532157365941", "fromId": "1532157365937", "toId": "1532157365938", "serialNumber": 3}]}, "extension": {"diagram": [{"id": "1532157365933", "attr": {"width": 700, "height": 540, "x": 40, "y": 60}}, {"id": "1532157365931", "attr": {"width": 60, "height": 60, "x": 120, "y": 125}}, {"id": "1532157365932", "attr": {"width": 160, "height": 50, "x": 70, "y": 260}}, {"id": "1532157365936", "attr": {"width": 160, "height": 50, "x": 360, "y": 260}}, {"id": "1532157365937", "attr": {"width": 160, "height": 50, "x": 375, "y": 428}}, {"id": "1532157365938", "attr": {"width": 60, "height": 60, "x": 110, "y": 430}}, {"id": "1532157365941", "attr": {"d": "M376,453 h-99 a3.5,3.5 0 0 0 -3.5,3.5 v0 a3.5,3.5 0 0 1 -3.5,3.5 h-99", "toPosition": "right", "fromPosition": "left", "type": "polyline"}}, {"id": "1532157365940", "attr": {"d": "M440,310 v52 a7.5,7.5 0 0 0 7.5,7.5 h0 a7.5,7.5 0 0 1 7.5,7.5 v52", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}, {"id": "1532157365939", "attr": {"d": "M230,285 h66 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h66", "toPosition": "left", "fromPosition": "right", "type": "polyline"}}, {"id": "1532157365934", "attr": {"d": "M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38", "toPosition": "top", "fromPosition": "bottom", "type": "polyline"}}], "pools": [{"lanes": [{"id": "1532157365933", "name": "阶段", "description": "", "activities": ["1532157365931", "1532157365932", "1532157365936", "1532157365937", "1532157365938"]}]}], "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"paas-bpm-canvas\" height=650 width=790 tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><defs><marker id=\"end-arrow_1532157397246\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#666666\"></path></marker><marker id=\"end-arrow-colored_1532157397246\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"5\" refY=\"0\" viewBox=\"-5 -5 10 10\"><path d=\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\" fill=\"#49bffc\"></path></marker><marker id=\"approval-yes_1532157397246\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#7FC25D\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><marker id=\"approval-no_1532157397246\" markerHeight=\"10\" markerWidth=\"10\" markerUnits=\"userSpaceOnUse\" orient=\"auto\" refX=\"-10\" refY=\"5\"><g><circle fill=\"#DC9688\" cx=\"5\" cy=\"5\" r=\"5\"></circle></g></marker><filter id=\"box-shadow\"><feGaussianBlur in=\"SourceAlpha\" stdDeviation=\"2\"></feGaussianBlur><feOffset dx=\"0\" dy=\"1\" result=\"offsetblur\"></feOffset><feFlood flood-color=\"black\" flood-opacity=\"0.06\"></feFlood><feComposite in2=\"offsetblur\" operator=\"in\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\"SourceGraphic\"></feMergeNode></feMerge></filter><style type=\"text/css\">svg {\n        background-color: #f3f3f5;\n      }\n\n      g[type=pool] {\n        font-size: 13px;\n      }</style></defs><g class=\"paas-bpm-canvas-wrapper\" height=\"100%\" width=\"100%\" transform=\"translate(0,0) scale(1)\"><g data-id=\"1532157365933\" shape=\"rectangle\" type=\"pool\" x=\"40\" y=\"60\" width=\"700\" height=\"540\" transform=\"translate(40,60)\" tabindex=\"0\" class=\"paas-bpm-resizable bpm-draw-polyline-able\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"><rect fill=\"transparent\" width=\"700\" height=\"540\" namePos=\"top\" highlight=\"false\" stroke=\"#cccccc\" rx=\"0\" ry=\"0\" resizable=\"true\" placeAt=\"first\" external=\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"700\" height=\"540\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"-4.5\" y=\"265.5\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"0\" ry=\"0\" x=\"345\" y=\"-4.5\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"695\" y=\"265.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"0\" ry=\"0\" x=\"345\" y=\"535.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect fill=\"transparent\" width=\"700\" height=\"60\" stroke=\"transparent\" rx=\"0\" ry=\"0\" y=\"0\" class=\"paas-bpm-pool-drag-area\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"700\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">阶段</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 550)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g><rect width=\"18\" height=\"18\" class=\"paas-bpm-badge\" x=\"0.5\" y=\"0.5\" fill=\"#ccc\"></rect><text class=\"paas-bpm-badge-text\" text-anchor=\"middle\" fill=\"#70757f\" x=\"9\" y=\"13\">1</text></g><g name=\"line-wrapper\"><g fill=\"transparent\" tabindex=\"0\" data-id=\"1532157365941\"><path type=\"polyline\" d=\"M376,453 h-99 a3.5,3.5 0 0 0 -3.5,3.5 v0 a3.5,3.5 0 0 1 -3.5,3.5 h-99\" from-position=\"left\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1532157397246)\" to-position=\"right\" start-id=\"1532157365937\" end-id=\"1532157365938\"></path></g><g fill=\"transparent\" tabindex=\"0\" data-id=\"1532157365940\"><path type=\"polyline\" d=\"M440,310 v52 a7.5,7.5 0 0 0 7.5,7.5 h0 a7.5,7.5 0 0 1 7.5,7.5 v52\" from-position=\"bottom\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1532157397246)\" to-position=\"top\" start-id=\"1532157365936\" end-id=\"1532157365937\"></path></g><g fill=\"transparent\" tabindex=\"0\" data-id=\"1532157365939\"><path type=\"polyline\" d=\"M230,285 h66 a0,0 0 0 1 0,0 v0 a0,0 0 0 0 0,0 h66\" from-position=\"right\" stroke-width=\"1\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1532157397246)\" to-position=\"left\" start-id=\"1532157365932\" end-id=\"1532157365936\"></path></g><g tabindex=\"0\" data-id=\"1532157365934\"><path d=\"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\" start-id=\"1532157365931\" end-id=\"1532157365932\" fill=\"transparent\" stroke-width=\"1\" type=\"polyline\" to-position=\"top\" from-position=\"bottom\" stroke=\"#aaaaaa\" marker-end=\"url(#end-arrow_1532157397246)\"></path></g></g><g data-id=\"1532157365931\" shape=\"rectangle\" type=\"startEvent\" x=\"120\" y=\"125\" width=\"60\" height=\"60\" transform=\"translate(120,125)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" class=\"\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"60\" width=\"60\" fill=\"#70757f\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">开始</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(0, 70)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"></text></g></g><g data-id=\"1532157365932\" shape=\"rectangle\" type=\"userTask\" x=\"70\" y=\"260\" width=\"160\" height=\"50\" transform=\"translate(70,260)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" class=\"bpm-draw-polyline-able\" status=\"normal\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"160\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">A</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(80, 60)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\" width=\"100\" height=\"20\">该节点配置错误</tspan></text></g></g><g data-id=\"1532157365936\" shape=\"rectangle\" type=\"userTask\" x=\"360\" y=\"260\" width=\"160\" height=\"50\" transform=\"translate(360,260)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" status=\"normal\" class=\"\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"160\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">B</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(80, 60)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\" width=\"100\" height=\"20\">该节点配置错误</tspan></text></g></g><g data-id=\"1532157365937\" shape=\"rectangle\" type=\"userTask\" x=\"375\" y=\"428\" width=\"160\" height=\"50\" transform=\"translate(375,428)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" status=\"normal\" class=\"bpm-draw-polyline-able bpm-shape-focus-node node-focused\"><rect fill=\"white\" width=\"160\" height=\"50\" rx=\"3\" ry=\"3\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"160\" height=\"50\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"155.5\" y=\"20.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"151.5\" y=\"16.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"75.5\" y=\"45.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"71.5\" y=\"41.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"50\" width=\"160\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">C</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(80, 60)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\" width=\"100\" height=\"20\">该节点配置错误</tspan></text></g></g><g data-id=\"1532157365938\" shape=\"rectangle\" type=\"endEvent\" x=\"110\" y=\"430\" width=\"60\" height=\"60\" transform=\"translate(110,430)\" tabindex=\"0\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\" status=\"normal\" class=\"bpm-draw-polyline-able\"><rect fill=\"#f3f3f5\" rx=\"60\" ry=\"60\" width=\"60\" height=\"60\" stroke=\"#70757f\" stroke-width=\"3\" color=\"#70757f\" external=\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\" type=\"rect\"></rect><g fill=\"transparent\" name=\"bpm-shape-focus-node\" class=\"hide\" stroke=\"#49bffc\"><rect width=\"60\" height=\"60\" rx=\"0\" ry=\"0\" fill=\"transparent\"></rect><rect width=\"9\" height=\"9\" name=\"left\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"-4.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"left\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"-8.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"top\" fill=\"white\" class=\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"-4.5\"></rect><rect width=\"17\" height=\"17\" name=\"top\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"-8.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"right\" fill=\"white\" class=\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"55.5\" y=\"25.5\"></rect><rect width=\"17\" height=\"17\" name=\"right\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"51.5\" y=\"21.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect><rect width=\"9\" height=\"9\" name=\"bottom\" fill=\"white\" class=\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\" rx=\"9\" ry=\"9\" x=\"25.5\" y=\"55.5\"></rect><rect width=\"17\" height=\"17\" name=\"bottom\" fill=\"transparent\" stroke=\"transparent\" class=\"paas-bpm-outer-connect-point\" rx=\"17\" ry=\"17\" x=\"21.5\" y=\"51.5\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\"></rect></g><g name=\"external\"><rect x=\"4\" y=\"4\" fill=\"transparent\" rx=\"52\" ry=\"52\" width=\"52\" height=\"52\" stroke=\"#70757f\" stroke-width=\"1\" color=\"#e67373\" type=\"rect\"></rect></g><foreignObject name=\"name\" style=\"overflow:hidden;\" height=\"60\" width=\"60\" fill=\"#70757f\"><div style=\"display:table;height: 100%;width: 100%;\"><p style=\"line-height: 20px;display: table-cell;height: 100%;text-align: center;vertical-align: middle;padding: 0 12px;\">结束</p></div></foreignObject><g name=\"error-node\" class=\"hide\" transform=\"translate(30, 70)\"><text text-anchor=\"middle\" alignment-baseline=\"middle\" fill=\"#f57a62\" x=\"0\" y=\"15\"><tspan font-size=\"20\"></tspan><tspan dy=\"-5\" width=\"100\" height=\"20\">该节点至少有一根连入的线</tspan></text></g></g></g><rect class=\"paas-bpm-canvas-drag-area hide\" height=\"100%\" width=\"100%\" fill=\"transparent\"></rect></svg>"}, "name": "草稿模板", "entryType": "object_UPfNh__c", "entryTypeName": "自行车(INAG)", "description": "", "singleInstanceFlow": 0, "rangeEmployeeIds": ["1007"], "rangeCircleIds": [], "rangeGroupIds": [], "rangeRoleIds": [], "supportExternalFlow": true, "scope": ["崔永旭"], "enabled": true, "externalFlow": 0}