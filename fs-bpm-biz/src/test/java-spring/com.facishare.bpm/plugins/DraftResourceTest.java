package com.facishare.bpm.plugins;

import com.facishare.bpm.plugins.model.*;
import com.facishare.bpm.resource.BaseRestTest;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;
import com.facishare.rest.core.util.JsonUtil;
import org.apache.commons.io.IOUtils;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/9 1:28 PM
 */
public class DraftResourceTest extends BaseRestTest {

    @Autowired
    private DraftResource draftResource;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Test
    public void createDraft() throws IOException {
        CreateDraft.Arg arg = JsonUtil.fromJson(IOUtils.toString(getClass().getResourceAsStream("/json/draft_deploy_01.json")), CreateDraft.Arg.class);
        arg.setSupportFlow(SupportFlow.market);
        CreateDraft.Result result = draftResource.createDraft(arg);
        assertNotNull(result);
    }

    @Test
    public void updateDraft() throws IOException {
        UpdateDraft.Arg arg = JsonUtil.fromJson(IOUtils.toString(getClass().getResourceAsStream("/json/update_draft_01.json")), UpdateDraft.Arg.class);
        arg.setSupportFlow(SupportFlow.market);
        draftResource.updateDraft(arg);
    }

    @Test
    public void deleteDraft() {
        DeleteDefinition.Arg arg = new DeleteDefinition.Arg();
        arg.setId("431788150152101888");
        draftResource.deleteDraft(arg);
    }

    @Test
    public void getDraftList() {
        GetDraftList.Arg arg = new GetDraftList.Arg();
        arg.setPageNumber(1);
        arg.setPageSize(20);
        arg.setSupportFlow(SupportFlow.market);
        GetDraftList.Result result = draftResource.getDraftList(arg);
        assertTrue(result.getTotalCount()>0);
    }

    @Test
    public void getDraftById() {
        GetDraft.Arg arg = new GetDraft.Arg();
        arg.setId("376052722577309696");
        assertNotNull(draftResource.getDraftById(arg));
    }


}
