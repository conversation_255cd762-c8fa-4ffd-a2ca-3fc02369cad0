package com.facishare.bpm.plugins;

import com.facishare.bpm.model.paas.engine.bpm.AutoTask;
import com.facishare.bpm.plugins.model.GetDelayTask;
import com.facishare.bpm.plugins.model.PluginCompleteTask;
import com.facishare.bpm.plugins.model.RefreshHandlerByTaskId;
import com.facishare.bpm.resource.BaseRestTest;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/9 1:52 PM
 */
public class TaskResourceTest extends BaseRestTest {


    @Autowired
    private TaskResource taskResource;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();


    @Test
    public void completeLatencyTask() {
        String id = "5caef0833db71dfea6326c60";
        PluginCompleteTask.Arg arg = new PluginCompleteTask.Arg();
        arg.setTaskId("latency_" + id);
        taskResource.complete(arg);
    }


    @Test
    public void completeTask() {
        String id = "5caef0833db71dfea6326c60";
        PluginCompleteTask.Arg arg = new PluginCompleteTask.Arg();
        arg.setTaskId(id);
        taskResource.complete(arg);
    }

    @Test
    public void refreshHandlerByTaskIdTest() {
        RefreshHandlerByTaskId.Arg arg = new RefreshHandlerByTaskId.Arg();
        arg.setTaskId("6333b8c41abf82062c279e42");
        RefreshHandlerByTaskId.Result result = taskResource.refreshHandlerByTaskId(arg);
        System.out.println(result);
    }

    @Test
    public void findDelayTask() {
        GetDelayTask.Arg arg = new GetDelayTask.Arg();
        arg.setObjectId("63736b0ebaaec40001eaba57");
        arg.setPageNumber(2);
        arg.setPageSize(3);
        List<AutoTask> result = taskResource.findDelayTask(arg);
        System.out.println(result);
    }


}
