package com.facishare.bpm.plugins;

import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.plugins.model.*;
import com.facishare.bpm.resource.BaseRestTest;
import com.facishare.bpm.utils.ObjectId;
import com.facishare.flow.mongo.bizdb.entity.SupportFlow;
import com.facishare.rest.core.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/9 1:27 PM
 */
public class DefinitionResourceTest extends BaseRestTest {

    @Autowired
    private DefinitionResource definitionResource;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Before
    public void before() throws Exception {
        getRestContext().setTenantId(71557);
        getRestContext().setUserId(1000);
    }

    @Test
    public void createDefinition() throws IOException {
//        expectedException.expect(BPMWorkflowNameDuplicateException.class);
//        expectedException.expectMessage("请重新设定流程名称");

        CreateDefinition.Arg arg = JsonUtil.fromJson(IOUtils.toString(getClass().getResourceAsStream("/json/deploy_01.json")), CreateDefinition.Arg.class);

        arg.setSupportFlow(SupportFlow.market);
        arg.setName(arg.getName()+ ObjectId.getId());
        definitionResource.createDefinition(arg);
    }

    @Test
    public void updateDefinition() throws IOException {
        UpdateDefinition.Arg arg = JsonUtil.fromJson(IOUtils.toString(getClass().getResourceAsStream("/json/update_01.json")), UpdateDefinition.Arg.class);
        arg.setSupportFlow(SupportFlow.market);

        definitionResource.updateDefinition(arg);
    }

    @Test
    public void deleteDefinition() {
        expectedException.expect(BPMWorkflowDefVerifyException.class);
        expectedException.expectMessage("删除失败，请确认该流程是否已经停用");

        DeleteDefinition.Arg arg = new DeleteDefinition.Arg();
        arg.setId("431793386388324352");
        definitionResource.deleteDefinition(arg);
    }

    @Test
    public void getDefinitionList() {
        GetDefinitionList.Arg arg = new GetDefinitionList.Arg();
        arg.setPageNumber(1);
        arg.setPageSize(20);
        arg.setSupportFlow(SupportFlow.market);
        GetDefinitionList.Result definitionList = definitionResource.getDefinitionList(arg);
        assertTrue(CollectionUtils.isNotEmpty(definitionList.getOutlines()));
    }

    @Test
    public void getDefinitionByWorkflowId() {
        GetDefinitionByWorkflowId.Arg arg = new GetDefinitionByWorkflowId.Arg();
        arg.setWorkflowId("5c50322f6886295c16e8632f");
        assertNotNull(definitionResource.getDefinitionByWorkflowId(arg).getOutline());
    }

    @Test
    public void updateDefinitionStatus() {
        UpdateDefinitionStatus.Arg arg = new UpdateDefinitionStatus.Arg();
        arg.setIds("431796333977403392");
        arg.setEnabled(true);
        definitionResource.updateDefinitionStatus(arg);
    }
}
