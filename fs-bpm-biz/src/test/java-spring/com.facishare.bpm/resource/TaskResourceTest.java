package com.facishare.bpm.resource;

import com.facishare.bpm.plugins.TaskResource;
import com.facishare.bpm.plugins.model.GetPluginTask;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * DefinitionResourceImpl Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @since <pre>Dec 14, 2018</pre>
 */
@Slf4j
public class TaskResourceTest extends BaseRestTest {
    @Autowired
    private TaskResource taskResource;

    @Before
    public void before() throws Exception {
        getRestContext().setTenantId(74164);
        getRestContext().setUserId(100018597);
    }

    @Test
    public void testGetTask() {
        getRestContext().setUserId(100018597);
        GetPluginTask.Arg arg=new GetPluginTask.Arg();
        arg.setInstanceId("5c41985068862932ef097d6b");
        arg.setActivityInstanceId("2");
        GetPluginTask.Result rst = taskResource.getTask(arg);

    }

    @Test
    public void testGetTasks() {
        getRestContext().setUserId(100018597);
        GetPluginTask.GetTaskByIdsArg arg=new GetPluginTask.GetTaskByIdsArg();
        arg.setTaskIds(Sets.newHashSet("5b6cfc1c688629ffa0aa474f","5b6d0a00688629ffa0aa48b5","5d22bc72688629d0e9c776ae"));
        List<GetPluginTask.TaskByIdsResult> rst = taskResource.getByIds(arg);
        assert Objects.isNull(rst);
    }
} 
