package com.facishare.bpm.resource;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.rest.ext.RestContext;
import com.facishare.rest.ext.RestRequest;
import org.jboss.resteasy.core.interception.PostMatchContainerRequestContext;
import org.mockito.Mockito;

import javax.ws.rs.container.ContainerRequestContext;

/**
 * <AUTHOR>
 * @creat_date: 2018/12/14
 * @creat_time: 14:29
 * @since 6.4
 */
public class BaseRestTest extends BaseTest {
    @Override
    public void setUp() throws Exception {
        super.setUp();
        ContainerRequestContext restContext= Mockito.mock(PostMatchContainerRequestContext.class);
        RestRequest request = RestRequest.init(restContext);
        request.setContext(new RestContext());
        RestContext context=RestRequest.getLocal().getRestContext();
        context.setUserId(1002);
        context.setTenantId(71557);
    }


    protected RestContext getRestContext(){
        return RestRequest.getLocal().getRestContext();
    }
}
