package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.mobile.model.MGetAllDefinitionList;
import com.facishare.bpm.controller.mobile.model.MGetAvailableWorkflowOfObject;
import com.facishare.bpm.controller.mobile.model.MGetAvailableWorkflows;
import com.facishare.bpm.controller.mobile.model.MGetBPMUseApiNames;
import com.facishare.rest.ext.common.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON> on 12/04/2017.
 */
public class MDefinitionActionTest extends BaseTest{
    @Autowired
    private MDefinitionAction definitionAction;
    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) definitionAction).setContextManager(contextManager);
    }
    @Test
    public void getAvailableWorkflowOfObject()  {
        MGetAvailableWorkflowOfObject.Arg arg=new MGetAvailableWorkflowOfObject.Arg();
        arg.setEntryType("object_7prLH__c");
        arg.setObjectId("632bcd6219eadc000125d616");
        MGetAvailableWorkflowOfObject.Result result = definitionAction.getAvailableWorkflowOfObject(arg);
        Assert.assertTrue(result.getDataList().size()>0);
    }

    @Test
    public void getAllAvailableWorkflows() throws Exception {
        MGetAvailableWorkflows.Arg arg=new MGetAvailableWorkflows.Arg();
        MGetAvailableWorkflows.Result result = definitionAction.getAllAvailableWorkflows(arg);
        Assert.assertTrue(result.getDataList().size()>0);
    }


    @Test
    public void getBPMUseApiNames() {
        MGetBPMUseApiNames.Result result = definitionAction.getBPMUseApiNames();
        System.out.println(JacksonUtil.toJson(result.getApiNames()));
    }
    @Test
    public void getAllDefinitionList() {
        MGetAllDefinitionList.Result allDefinitionList = definitionAction.getAllDefinitionList();
        System.out.println(allDefinitionList);
    }


}
