package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.mobile.model.MGetWorkflowInstanceLog;
import com.facishare.rest.ext.common.JacksonUtil;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON> on 12/04/2017.
 */
public class MInstanceActionTest extends BaseTest {
    @Autowired
    private MInstanceAction instanceAction;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) instanceAction).setContextManager(contextManager);
    }

    @Test
    public void getWorkflowInstanceLog() {
        MGetWorkflowInstanceLog.Arg arg = new MGetWorkflowInstanceLog.Arg();
        arg.setWorkflowInstanceId("66ebe2e1e268c669f62e2718");
        MGetWorkflowInstanceLog.Result result = instanceAction.getWorkflowInstanceLog(arg);
        System.out.println(JacksonUtil.toJson(result));
    }


}
