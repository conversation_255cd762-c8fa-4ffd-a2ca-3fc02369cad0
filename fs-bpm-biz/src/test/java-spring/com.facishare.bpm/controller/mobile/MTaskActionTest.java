package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.mobile.model.*;
import com.facishare.bpm.controller.web.model.Edit;
import com.facishare.rest.ext.common.JacksonUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created by cuiyongxu on 03/09/2020.
 */
@Slf4j
public class MTaskActionTest extends BaseTest {
    @Autowired
    private MTaskAction mTaskAction;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) mTaskAction).setContextManager(contextManager);
    }




    @Test
    public void getTask() {
        MGetTask.Arg arg = new MGetTask.Arg();
        arg.setActivityInstanceId("2");
        arg.setInstanceId("61c19b68da54471fd845bda0");
        arg.setSource("h5");
        arg.setApplyButtons(true);
        MGetTask.Result task = mTaskAction.getTask(arg);
        log.info("data:{}",JacksonUtil.toJson(task) );
    }


    @Test
    public void getTaskDetail() {
        MGetTaskDetail.Arg arg = new MGetTaskDetail.Arg();
        arg.setId("616e2f66b3371079a19f6bf5");
        arg.setApplyButtons(true);
        MGetTaskInfo.Result task = mTaskAction.getTaskDetail(arg);
        log.info("data:{}",JacksonUtil.toJson(task) );
    }


    @Test
    public void getTaskInfo() {
        MGetTaskInfo.Arg arg = new MGetTaskInfo.Arg();
        //arg.setActivityId("1689044722321");
        //arg.setActivityInstanceId("5");
       // arg.setInstanceId("64b639eaee95f530f8afdc05");
        arg.setApplyButtons(true);
        arg.setId("64c098a3f8e5777bcad67943");
        arg.setIncludeTaskFeedDetailConfig(true);
        MGetTaskInfo.Result task = mTaskAction.getTaskInfo(arg);
        log.info("data:{}",JacksonUtil.toJson(task) );
    }

    @Test
    public void completeTask() {
        MCompleteTask.Arg arg = new MCompleteTask.Arg();
        arg.setTaskId("61712a3de3e3a14b5faf640e");
        arg.setObjectId("616f89f4b6c58400018daa03");
        MCompleteTask.Result task = mTaskAction.completeTask(arg);
        log.info("data:{}",JacksonUtil.toJson(task) );
    }


    @Test
    public void completeAndCreateTaskData() {
        MCompleteAndCreateTaskData.Arg arg = new MCompleteAndCreateTaskData.Arg();
        arg.setActivityId("1637034288581");
        arg.setActivityInstanceId(4);
        arg.setExecutionType("batchAddRelatedObject");
        arg.setTaskId("54");
        Map<String,Object> data = Maps.newHashMap();
        data.put("entityId","333");
        data.put("objectId","333");
        arg.setData(data);
        MCompleteTask.Result task = mTaskAction.completeAndCreateTaskData(arg);
        log.info("data:{}",JacksonUtil.toJson(task) );
    }



    @Test
    public void getButtonByTaskIds() {
        MGetButtonByTaskIds.Arg arg = new MGetButtonByTaskIds.Arg();
//        arg.setTaskIds(Sets.newHashSet(    "6152b649d30745379fe6ff15","614bebe80b6f822fd9a46ba2","6141dd092b8eaa0d0e2f529b","6141acb72b8eaa0d0e2f3b8a","61419f332b8eaa0d0e2f3ad9","614199142b8eaa0d0e2f3ab9","614071b82b8eaa0d0e2f38c1","614071b72b8eaa0d0e2f38c0","613f11d82b8eaa0d0e2f3726","613ec5fa2b8eaa0d0e2f367a"));
        arg.setTaskIds(Sets.newHashSet(    "615fee17f335b65209003fbd"));
        MGetButtonByTaskIds.Result task = mTaskAction.getButtonByTaskIds(arg);
        log.info("data:{}",JacksonUtil.toJson(task) );
    }

    @Test
    public void edit(){
        Edit.Arg arg = new Edit.Arg();
        arg.setTaskId("6305899f6a8e045f39d5073e");
        arg.setObjectData("{\"object_describe_api_name\":\"object_7prLH__c\",\"object_describe_id\":\"6212f6c3da55d60001f3ca72\",\"name\":\"test_A\",\"field_b5h20__c\":\"\",\"field_8M521__c\":null,\"field_0XmA0__c\":1659456000000,\"_id\":\"62f9e53c9e2e6000017e1cb3\"}");
        Edit.Result res = mTaskAction.edit(arg);
        System.out.println();
    }

    @Test
    public void getHandleTaskList(){
        MGetHandleTaskList.Arg arg = new MGetHandleTaskList.Arg();
        MGetHandleTaskList.Result handleTaskList = mTaskAction.getHandleTaskList(arg);
        System.out.println();
    }

    @Test
    public void getUncompletedTasksByObject(){
        MGetUncompletedTasksByObject.Arg arg = new MGetUncompletedTasksByObject.Arg();
        arg.setApiName("object_7prLH__c");
        arg.setObjectId("632bcd6219eadc000125d616");
        MGetUncompletedTasksByObject.Result uncompletedTasksByObject = mTaskAction.getUncompletedTasksByObject(arg);
        System.out.println();
    }


}
