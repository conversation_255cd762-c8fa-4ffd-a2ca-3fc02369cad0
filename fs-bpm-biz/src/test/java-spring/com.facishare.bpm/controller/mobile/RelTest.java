package com.facishare.bpm.controller.mobile;

import com.facishare.bpm.model.ActionButton;
import com.facishare.bpm.util.I18NParser;
import com.facishare.bpm.utils.i18n.I18NExpression;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * @Description :
 * <AUTHOR> cuiyongxu
 * @Date : 2021/12/21-9:43 下午
 **/
public class RelTest {

    public static void main(String[] args) {

        ActionButton actionButton = new ActionButton();
        actionButton.setAction("UpdateAndComplete");
        actionButton.setCode("update");
        actionButton.setLabel("保存并完成22");
        Map<String, ActionButton> buttons = Maps.newHashMap();
        buttons.put("UpdateAndComplete", actionButton);
        A a = new A();
        a.setData(buttons);
        I18NParser.parse("71557", a);

        System.out.println(1);
    }
}

@Data
class A {

    @I18NExpression(drill = true)
    private Map<String, ActionButton> data;
}