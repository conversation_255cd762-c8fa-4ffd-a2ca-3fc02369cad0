package com.facishare.bpm.controller;

import com.facishare.bpm.controller.web.ProcessDefinitionAction;
import com.facishare.bpm.controller.web.ProcessInstanceAction;
import com.facishare.bpm.controller.web.ProcessTaskAction;
import com.facishare.bpm.service.BPMBaseService;
import com.facishare.rest.ext.CepContext;
import com.facishare.rest.ext.RestRequest;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.*;


/**
 * Created by liyiguang on 16/9/8.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:application-test.xml"})
public class BaseTest extends BPMBaseService {

    @Autowired
    public ProcessDefinitionAction processDefinitionAction;
    @Autowired
    public ProcessInstanceAction processInstanceAction;
    @Autowired
    public ProcessTaskAction processTaskAction;

    public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";


    @BeforeClass
    public static void setConfig() {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Before
    public void setUp() throws Exception {
        RestRequest restRequest = RestRequest.init(null);
        CepContext context = new CepContext();
        context.setTenantId(71557);
        context.setAppId("BPM");
        context.setUserId(1002);
        restRequest.setContext(context);

    }


    public String getOutlineJsonFromFile(String jsonFileName) {
        String path = BaseTest.class.getResource("/").getPath() + File.separator + "json" + File.separator + jsonFileName;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }
}
