package com.facishare.bpm.controller;

import com.facishare.stage.doc.tools.MarkDownGenerator;
import org.junit.Test;

/**
 * Created by liyi<PERSON><PERSON> on 16/8/12.
 */
public class DocGen {
    @Test
    public void createFcpDoc() {
        MarkDownGenerator markDownGenerator = new MarkDownGenerator();
        markDownGenerator.generateDoc("BPM", "com.facishare.bpm.controller.web", com.facishare.stage.doc.tools.DocGenerator.Type
                        .FHH, "EM1H",
                "../document/2.接口文档/WEB");
        markDownGenerator.generateDoc("BPM", "com.facishare.bpm.controller.mobile", com.facishare.stage.doc.tools.DocGenerator.Type
                        .FHH, "EM1H",
                "../document/2.接口文档/移动端");
    }

    @Test
    public void createRestProxyDoc() {
        com.facishare.stage.doc.tools.MarkDownGenerator generator = new com.facishare.stage.doc.tools.MarkDownGenerator();
        generator.generateRestDoc("com.facishare.bpm.plugins", "plugins", "../document/2.接口文档/REST");
    }

}
