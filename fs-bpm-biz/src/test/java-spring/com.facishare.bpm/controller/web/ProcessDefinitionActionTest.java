package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.tenant.SkipPageFormToDo;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Created by cuiyongxu on 16/12/26.
 */
@Slf4j
public class ProcessDefinitionActionTest extends BaseTest {

    @Autowired
    private ProcessDefinitionAction processDefinitionAction;

    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) processDefinitionAction).setContextManager(contextManager);
    }


    /**
     * 部署流程定义
     */
    @Test
    public void update() {
        String json = getOutlineJsonFromFile("deploy.json");
        UpdateDefinition.Arg arg = JsonUtil.fromJson(json, UpdateDefinition.Arg.class);
        arg.setEnabled(true);
        UpdateDefinition.Result result = processDefinitionAction.updateDefinition(arg);
    }


    @Test
    public void deploy() {
        String json = getOutlineJsonFromFile("deploy.json");
        CreateDefinition.Arg arg = JsonUtil.fromJson(json, CreateDefinition.Arg.class);
        arg.setEnabled(true);
        CreateDefinition.Result result = processDefinitionAction.createDefinition(arg);
        try {
            arg.setId(null);
            result = processDefinitionAction.createDefinition(arg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.debug("流程定义id为 :{}", result.getId());
    }


    /**
     * 更新流程定义状态
     */
    @Test
    public void updateWorkflowOutlineStatus() {
        String deployId = "228778468413440000";
        UpdateDefinitionStatus.Arg arg = new UpdateDefinitionStatus.Arg();
        arg.setEnabled(true);
        arg.setIds(deployId);
        UpdateDefinitionStatus.Result result = processDefinitionAction.updateDefinitionStatus(arg);
        if (null != result) {
            Assert.assertTrue(result.isResult());
        }

    }


    /**
     * 查询流程定义详情
     */
    @Test
    public void getWorkflowOutline() {
        String id = "6501648994a9c64ef7f20751";
        GetDefinition.Arg getDefinition = new GetDefinition.Arg();
        getDefinition.setId(id);
        GetDefinition.Result getDefinitionResult = processDefinitionAction.getDefinition(getDefinition);
        WorkflowOutline workflowOutline = getDefinitionResult.getOutline();
        processDefinitionAction.updateDefinition(UpdateDefinition.Arg.getInstance(workflowOutline));
        log.info("result = {}", workflowOutline);
    }

    /**
     * 查询流程定义详情并更新流程定义
     */
    @Test
    public void updateWorkflowOutline() throws Exception {
        String id = "228786122582032384";
        GetDefinition.Arg getDefinition = new GetDefinition.Arg();
        getDefinition.setId(id);
        GetDefinition.Result getDefinitionResult = processDefinitionAction.getDefinition(getDefinition);
        WorkflowOutline workflowOutline = getDefinitionResult.getOutline();

        CreateDefinition.Arg arg = new CreateDefinition.Arg();
        arg.setId(workflowOutline.getId());
        arg.setTenantId(workflowOutline.getTenantId());
        arg.setUserId(workflowOutline.getUserId());
        arg.setSourceWorkflowId(workflowOutline.getSourceWorkflowId());
        arg.setWorkflowId(workflowOutline.getWorkflowId());
        arg.setName(workflowOutline.getName());
        arg.setEnabled(workflowOutline.isEnabled());
        arg.setDescription(workflowOutline.getDescription());
        arg.setEntryType(workflowOutline.getEntryType());
        arg.setEntryTypeName(workflowOutline.getEntryTypeName());
        arg.setRangeEmployeeIds(workflowOutline.getRangeEmployeeIds());
        arg.setRangeCircleIds(workflowOutline.getRangeCircleIds());
        arg.setCreatedBy(workflowOutline.getCreatedBy());
        arg.setCreateTime(workflowOutline.getCreateTime());
        arg.setLastModifiedBy(workflowOutline.getLastModifiedBy());
        arg.setLastModifiedTime(workflowOutline.getLastModifiedTime());
        arg.setWorkflow(workflowOutline.getWorkflow());
        arg.setExtension(workflowOutline.getExtension());
        arg.setDeleted(workflowOutline.isDeleted());
        arg.setCount(workflowOutline.getCount());
        arg.setHasInstance(workflowOutline.isHasInstance());
        arg.setSvg(workflowOutline.getSvg());
        arg.setTemplateId(workflowOutline.getTemplateId());

        arg.getWorkflow().put("test", "123456");
        CreateDefinition.Result result = processDefinitionAction.createDefinition(arg);
        log.info("result = {}", result);
    }

    @Test
    public void updateDefinition() {
        String definitionJson = "{\n" +
                "    \"appId\": \"BPM\",\n" +
                "    \"workflow\": {\n" +
                "        \"activities\": [\n" +
                "            {\n" +
                "                \"id\": \"1510907084555\",\n" +
                "                \"type\": \"startEvent\",\n" +
                "                \"name\": \"开始\",\n" +
                "                \"description\": \"\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084556\",\n" +
                "                \"type\": \"userTask\",\n" +
                "                \"name\": \"业务活动\",\n" +
                "                \"description\": \"\",\n" +
                "                \"canSkip\": false,\n" +
                "                \"taskType\": \"anyone\",\n" +
                "                \"assignee\": {\n" +
                "                    \"ext_bpm\": [\n" +
                "                        \"instance##owner$$流程发起人\"\n" +
                "                    ]\n" +
                "                },\n" +
                "                \"bpmExtension\": {\n" +
                "                    \"actionCode\": \"\",\n" +
                "                    \"executionType\": \"update\",\n" +
                "                    \"executionName\": \"编辑对象\",\n" +
                "                    \"entityId\": \"object_t11NT__c\",\n" +
                "                    \"entityName\": \"自行车\",\n" +
                "                    \"objectId\": {\n" +
                "                        \"expression\": \"activity_0##object_t11NT__c\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"execution\": {\n" +
                "                    \"pass\": [\n" +
                "                        {\n" +
                "                            \"taskType\": \"updates\",\n" +
                "                            \"updateFieldJson\": \"[{\\\"isCalculate\\\":false,\\\"key\\\":\\\"${object_t11NT__c.field_2Mbna__c}\\\",\\\"value\\\":\\\"<EMAIL>\\\",\\\"defaultValue\\\":\\\"\\\",\\\"entityId\\\":\\\"activity_1510907084556##object_t11NT__c\\\"}]\"\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084568\",\n" +
                "                \"name\": \"结束\",\n" +
                "                \"description\": \"\",\n" +
                "                \"type\": \"endEvent\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"variables\": [\n" +
                "            {\n" +
                "                \"id\": \"activity_0##object_t11NT__c\",\n" +
                "                \"type\": {\n" +
                "                    \"name\": \"text\"\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"activity_1510907084556##object_t11NT__c\",\n" +
                "                \"type\": {\n" +
                "                    \"name\": \"text\"\n" +
                "                }\n" +
                "            }\n" +
                "        ],\n" +
                "        \"transitions\": [\n" +
                "            {\n" +
                "                \"id\": \"1510907084558\",\n" +
                "                \"fromId\": \"1510907084555\",\n" +
                "                \"toId\": \"1510907084556\",\n" +
                "                \"serialNumber\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084569\",\n" +
                "                \"fromId\": \"1510907084556\",\n" +
                "                \"toId\": \"1510907084568\",\n" +
                "                \"serialNumber\": 1\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"extension\": {\n" +
                "        \"diagram\": [\n" +
                "            {\n" +
                "                \"id\": \"1510907084557\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 220,\n" +
                "                    \"height\": 540,\n" +
                "                    \"x\": 40,\n" +
                "                    \"y\": 60\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084555\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 60,\n" +
                "                    \"height\": 60,\n" +
                "                    \"x\": 120,\n" +
                "                    \"y\": 125\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084556\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 160,\n" +
                "                    \"height\": 50,\n" +
                "                    \"x\": 70,\n" +
                "                    \"y\": 260\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084568\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 60,\n" +
                "                    \"height\": 60,\n" +
                "                    \"x\": 109,\n" +
                "                    \"y\": 479\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084569\",\n" +
                "                \"attr\": {\n" +
                "                    \"d\": \"M150,310 v80 a5.5,5.5 0 0 1 -5.5,5.5 h0 a5.5,5.5 0 0 0 -5.5,5.5 v80\",\n" +
                "                    \"toPosition\": \"top\",\n" +
                "                    \"fromPosition\": \"bottom\",\n" +
                "                    \"type\": \"polyline\"\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"1510907084558\",\n" +
                "                \"attr\": {\n" +
                "                    \"d\": \"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\",\n" +
                "                    \"toPosition\": \"top\",\n" +
                "                    \"fromPosition\": \"bottom\",\n" +
                "                    \"type\": \"polyline\"\n" +
                "                }\n" +
                "            }\n" +
                "        ],\n" +
                "        \"pools\": [\n" +
                "            {\n" +
                "                \"lanes\": [\n" +
                "                    {\n" +
                "                        \"id\": \"1510907084557\",\n" +
                "                        \"name\": \"阶段\",\n" +
                "                        \"description\": \"\",\n" +
                "                        \"activities\": [\n" +
                "                            \"1510907084555\",\n" +
                "                            \"1510907084556\",\n" +
                "                            \"1510907084568\"\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"svg\": \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" class=\\\"paas-bpm-canvas\\\" height=650 width=310 tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><defs><marker id=\\\"end-arrow_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#666666\\\"></path></marker><marker id=\\\"end-arrow-colored_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#49bffc\\\"></path></marker><marker id=\\\"approval-yes_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#7FC25D\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><marker id=\\\"approval-no_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#DC9688\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><filter id=\\\"box-shadow\\\"><feGaussianBlur in=\\\"SourceAlpha\\\" stdDeviation=\\\"2\\\"></feGaussianBlur><feOffset dx=\\\"0\\\" dy=\\\"1\\\" result=\\\"offsetblur\\\"></feOffset><feFlood flood-color=\\\"black\\\" flood-opacity=\\\"0.06\\\"></feFlood><feComposite in2=\\\"offsetblur\\\" operator=\\\"in\\\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\\\"SourceGraphic\\\"></feMergeNode></feMerge></filter><style type=\\\"text/css\\\">svg {\\n        background-color: #f3f3f5;\\n      }\\n\\n      g[type=pool] {\\n        font-size: 13px;\\n      }</style></defs><g class=\\\"paas-bpm-canvas-wrapper\\\" height=\\\"100%\\\" width=\\\"100%\\\" transform=\\\"translate(0,0) scale(1)\\\" k=\\\"100\\\"><g data-id=\\\"1510907084557\\\" shape=\\\"rectangle\\\" type=\\\"pool\\\" x=\\\"40\\\" y=\\\"60\\\" width=\\\"220\\\" height=\\\"540\\\" transform=\\\"translate(40,60)\\\" tabindex=\\\"0\\\" class=\\\"paas-bpm-resizable\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"540\\\" namePos=\\\"top\\\" highlight=\\\"false\\\" stroke=\\\"#cccccc\\\" rx=\\\"0\\\" ry=\\\"0\\\" resizable=\\\"true\\\" placeAt=\\\"first\\\" external=\\\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"220\\\" height=\\\"540\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"-4.5\\\" y=\\\"265.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"105.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"215.5\\\" y=\\\"265.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"105.5\\\" y=\\\"535.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><g name=\\\"external\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"60\\\" stroke=\\\"transparent\\\" rx=\\\"0\\\" ry=\\\"0\\\" y=\\\"0\\\" class=\\\"paas-bpm-pool-drag-area\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"30\\\" x=\\\"110\\\">阶段</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 545)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g><rect width=\\\"18\\\" height=\\\"18\\\" class=\\\"paas-bpm-badge\\\" x=\\\"0.5\\\" y=\\\"0.5\\\" fill=\\\"#ccc\\\"></rect><text class=\\\"paas-bpm-badge-text\\\" text-anchor=\\\"middle\\\" fill=\\\"#70757f\\\" x=\\\"9\\\" y=\\\"13\\\">1</text></g><g name=\\\"line-wrapper\\\"><g fill=\\\"transparent\\\" tabindex=\\\"0\\\" data-id=\\\"1510907084569\\\"><path type=\\\"polyline\\\" d=\\\"M150,310 v80 a5.5,5.5 0 0 1 -5.5,5.5 h0 a5.5,5.5 0 0 0 -5.5,5.5 v80\\\" from-position=\\\"bottom\\\" stroke-width=\\\"1\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1510908093970)\\\" to-position=\\\"top\\\" start-id=\\\"1510907084556\\\" end-id=\\\"1510907084568\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"1510907084558\\\"><path d=\\\"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\\\" start-id=\\\"1510907084555\\\" end-id=\\\"1510907084556\\\" fill=\\\"transparent\\\" stroke-width=\\\"1\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1510908093970)\\\"></path></g></g><g data-id=\\\"1510907084555\\\" shape=\\\"rectangle\\\" type=\\\"startEvent\\\" x=\\\"120\\\" y=\\\"125\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(120,125)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"\\\"><rect fill=\\\"#f3f3f5\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"3\\\" color=\\\"#70757f\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"52.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"52.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"35\\\" x=\\\"30\\\" fill=\\\"#70757f\\\">开始</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 65)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"1510907084556\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"70\\\" y=\\\"260\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(70,260)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"bpm-draw-polyline-able bpm-shape-focus-node node-focused\\\" status=\\\"normal\\\"><rect fill=\\\"white\\\" width=\\\"160\\\" height=\\\"50\\\" rx=\\\"3\\\" ry=\\\"3\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"#49bffc\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"17.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"72.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"152.5\\\" y=\\\"17.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"72.5\\\" y=\\\"42.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"30\\\" x=\\\"80\\\">业务活动</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(80, 55)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"><tspan font-size=\\\"20\\\">\uE906</tspan><tspan dy=\\\"-5\\\">该节点配置错误</tspan></text></g></g><g data-id=\\\"1510907084568\\\" shape=\\\"rectangle\\\" type=\\\"endEvent\\\" x=\\\"109\\\" y=\\\"479\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(109,479)\\\" tabindex=\\\"0\\\" status=\\\"normal\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"\\\"><rect fill=\\\"#f3f3f5\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"3\\\" color=\\\"#70757f\\\" external=\\\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"52.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"52.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><g name=\\\"external\\\"><rect x=\\\"4\\\" y=\\\"4\\\" fill=\\\"transparent\\\" rx=\\\"52\\\" ry=\\\"52\\\" width=\\\"52\\\" height=\\\"52\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"1\\\" color=\\\"#e67373\\\" type=\\\"rect\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"35\\\" x=\\\"30\\\" fill=\\\"#70757f\\\">结束</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(30, 65)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"><tspan font-size=\\\"20\\\">\uE906</tspan><tspan dy=\\\"-5\\\">该节点至少有一根连入的线</tspan></text></g></g></g><rect class=\\\"paas-bpm-canvas-drag-area hide\\\" height=\\\"100%\\\" width=\\\"100%\\\" fill=\\\"transparent\\\"></rect></svg>\"\n" +
                "    },\n" +
                "    \"name\": \"f's'da'f\",\n" +
                "    \"description\": \" 发发发\",\n" +
                "    \"entryType\": \"object_t11NT__c\",\n" +
                "    \"entryTypeName\": \"自行车\",\n" +
                "    \"scope\": [\n" +
                "        \"zhangman\"\n" +
                "    ],\n" +
                "    \"rangeEmployeeIds\": [\n" +
                "        \"1000\"\n" +
                "    ],\n" +
                "    \"rangeCircleIds\": [],\n" +
                "    \"rangeGroupIds\": [],\n" +
                "    \"rangeRoleIds\": [],\n" +
                "    \"enabled\": true\n" +
                "}";
        CreateDefinition.Arg arg = JsonUtil.fromJson(definitionJson, CreateDefinition.Arg.class);
        processDefinitionAction.createDefinition(arg);
    }

    /**
     * 获取流程定义列表
     */
    @Test
    public void getWorkflowOutlineList() {
        GetDefinitionList.Arg arg = new GetDefinitionList.Arg();
        arg.setPageSize(10);
        arg.setPageNumber(1);
//        arg.setKeyWord("划分客户类别");
        GetDefinitionList.Result result = processDefinitionAction.getDefinitionList(arg);
        List<WorkflowOutline> list = result.getOutlines();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        list.forEach(entity -> {
            log.info("===>{} , {} , {} , {}", sdf.format(new Date(entity.getLastModifiedTime())), entity.getName(), entity.getId(), entity.isEnabled());
        });
    }


    /**
     * 删除流程定义
     */
    @Test
    public void deleteWorkflowOutline() {
        String json = getOutlineJsonFromFile("deploy.json");
        CreateDefinition.Arg arg2 = JsonUtil.fromJson(json, CreateDefinition.Arg.class);
        arg2.setEnabled(true);
        CreateDefinition.Result result2 = processDefinitionAction.createDefinition(arg2);
        String deployId = result2.getId();
        DeleteDefinition.Arg arg = new DeleteDefinition.Arg();
        arg.setId(deployId);
        DeleteDefinition.Result result = processDefinitionAction.deleteDefinition(arg);
        Assert.assertTrue(result.isResult());
    }


    /**
     * 查询当前用户下可用的流程
     */

    @Test
    public void getAllAvailableWorkflows() {
        GetAllAvailableWorkflows.Arg arg = new GetAllAvailableWorkflows.Arg();
        GetAllAvailableWorkflows.Result list = processDefinitionAction.getAllAvailableWorkflows(arg);
        list.forEach(e -> {
            GetAllAvailableWorkflows.AvailableWorkflow availableWorkflow = (GetAllAvailableWorkflows.AvailableWorkflow) e;
            System.out.println(availableWorkflow.getName());
        });
    }

    /**
     * 查询当前对象（入口）下可用的流程（同时 对该用户可用）
     */
    @Test
    public void getAvailableWorkflows() {
        GetAvailableWorkflows.Arg arg = new GetAvailableWorkflows.Arg();
        arg.setEntryType("object_7prLH__c");
        arg.setObjectId("632bcd6219eadc000125d616");
        GetAvailableWorkflows.Result result = processDefinitionAction.getAvailableWorkflows(arg);
        //log.info("{}",result);
        log.info("result size = {}", result.getOutlines().size());

    }

    /**
     * 获取最新的流程定义的阶段信息
     */
    @Test
    public void getLanesOfWorkflowId() {
        GetLanesOfWorkflowSourceId.Arg arg = new GetLanesOfWorkflowSourceId.Arg();
        //arg.setSourceWorkflowIdAndTenantId("232093935366471680");
        GetLanesOfWorkflowSourceId.Result result = processDefinitionAction.getLanesOfWorkflowSourceId(arg);
        Assert.assertTrue(result.size() > 0);
    }

    /**
     * 统计流程使用量
     */
    @Test
    public void getWorkflowUseCount() {
        GetDefinitionList.Arg arg = new GetDefinitionList.Arg();
        arg.setPageSize(500);
        arg.setPageNumber(1);
        GetDefinitionList.Result result = processDefinitionAction.getDefinitionList(arg);
        List<WorkflowOutline> outlines = result.getOutlines();
        int count = 0;

        for (WorkflowOutline outline : outlines) {
            log.info("{} count:{}", outline.getName(), outline.getCount());
            count += outline.getCount();
        }
        log.info("总数:{}", count);

    }


    /**
     * 查询App activity 上相关的应用和事件
     */
    @Test
    public void getAppActions() {
        GetActivityAppActions.Arg arg = new GetActivityAppActions.Arg();
        arg.setExternalFlow(true);
        GetActivityAppActions.Result result = processDefinitionAction.getAppActions(arg);
        log.info("App activity: {}", JsonUtil.toJson(result));
        Assert.assertNotNull(result);
    }

    /**
     * 查询App activity 上相关的应用和事件
     */
    @Test
    public void getHistory() {
        GetDefinitionHistoryList.Arg arg = new GetDefinitionHistoryList.Arg();
        arg.setSourceWorkflowId("405678154806329344");
        GetDefinitionHistoryList.Result result = processDefinitionAction.getHistoryList(arg);
        log.info("App activity: {}", JsonUtil.toJson(result));
        Assert.assertNotNull(result);
    }


    @Test
    public void getDefinitionByWorkflowId() {
        GetDefinitionByWorkflowId.Arg arg = new GetDefinitionByWorkflowId.Arg();
        arg.setWorkflowId("67088d8536c5e4405fc81049");
        GetDefinitionByWorkflowId.Result result = processDefinitionAction.getDefinitionByWorkflowId(arg);
        log.info("json: {}", JsonUtil.toJson(result), "UTF-8");
        Assert.assertNotNull(result);
    }


    @Test
    public void getActivityDefByActivityId() {
        GetActivityDefByActivityId.Arg arg = new GetActivityDefByActivityId.Arg();
        arg.setWorkflowId("5bdfc08168862989ca153d17");
        arg.setActivityId("1541484794221");
        GetActivityDefByActivityId.Result result = processDefinitionAction.getActivityDefByActivityId(arg);
        log.info("json: {}", JsonUtil.toJson(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void getSkipPageFromTodo() {
        GetSkipPageFromTodo.Arg arg = new GetSkipPageFromTodo.Arg();
        GetSkipPageFromTodo.Result skipPageFromTodo = processDefinitionAction.getSkipPageFromTodo(arg);
        log.info("skipPageFromTodo:{}", skipPageFromTodo);
    }

    @Test
    public void updateSkipPageFromTodo(){
        UpdateSkipPageFromTodo.Arg arg = new UpdateSkipPageFromTodo.Arg();
        arg.setSkipPageFormToDo(SkipPageFormToDo.dataPage);
        processDefinitionAction.updateSkipPageFromTodo(arg);
    }

    @Test
    public void getWorkflowLogs(){
        GetWorkflowLogs.Arg arg = new GetWorkflowLogs.Arg();
        arg.setSourceWorkflowId("6334f7207f65690001ef756c");
        processDefinitionAction.getWorkflowLogs(arg);
    }


    public static byte[] inputStream2Byte(InputStream inStream)
            throws Exception {
        int count = inStream.available();
        byte[] b = new byte[count];
        inStream.read(b);
        return b;
    }
}
