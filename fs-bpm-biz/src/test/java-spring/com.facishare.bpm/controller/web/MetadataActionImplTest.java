package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.metadata.*;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;


/**
 * Created by wangz on 17-1-10.
 */
public class MetadataActionImplTest extends BaseTest {


    @Autowired
    MetadataAction metadataAction;

    String descApiName = "AccountObj";
    String id = "6e6c7ae19d9f4fc48bc09adbd004fa06";

    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) metadataAction).setContextManager(contextManager);
    }

    /**
     * 查询描述文件
     *
     * @throws Exception
     */
    @Test
    public void findDescribe() throws Exception {
        FindDescribe.Arg arg = new FindDescribe.Arg();
        arg.setApiName("object_cosr8__c");
        FindDescribe.Result result = metadataAction.findDescribe(arg);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getDescribe());
    }

    @Test
    public void findDescWithReference() throws Exception {
        FindDescWithReference.Arg arg = new FindDescWithReference.Arg();
        arg.setApiName("ContractObj");

        FindDescWithReference.Result result = metadataAction.findDescWithReference(arg);
    }

    @Test
    public void getBpmTaskBusinessCodeFilterCriteria() throws Exception {
        GetBpmTaskBusinessCodeFilterCriteria.Result result= metadataAction.getBpmTaskBusinessCodeFilterCriteria();
        System.out.println();
    }

    /**
     * 查询数据
     *
     * @throws Exception
     */
    @Test
    public void findDataById() throws Exception {
        FindDataById.Arg arg = new FindDataById.Arg();
        arg.setApiName(descApiName);
        arg.setDataId(id);
        FindDataById.Result result = metadataAction.findDataById(arg);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void findCustomObjs() throws Exception {
        FindCustomObjs.Arg arg = new FindCustomObjs.Arg();
        arg.setPackageName("CRM");
        arg.setIncludeFieldsDesc(false);
        FindCustomObjs.Result result = metadataAction.findCustomObjs(arg);
        result.getCustomObjects().forEach(r -> {
            System.out.println(r.getObjApiName());
        });
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getCustomObjects());
    }

    @Test
    public void findDescsByApiNames() throws Exception {
        FindDescsByApiNames.Arg arg = new FindDescsByApiNames.Arg();
        arg.setApiNames(Lists.newArrayList("object_UPfNh__c", "object_UPfNh__c"));
        arg.setIncludeLookups(true);
        FindDescsByApiNames.Result result = metadataAction.findDescsByApiNames(arg);
        System.out.println(result);
    }

    @Test
    public void findObjActions() throws Exception {
        FindObjActions.Arg arg = new FindObjActions.Arg();
//        arg.setApiName(descApiName);
        arg.setApiName("LeadsObj");
        arg.setActionType("common");
        FindObjActions.Result result = metadataAction.findObjActions(arg);
        Assert.assertNotNull(result);
        System.out.println(JsonUtil.toJson(result));
        Assert.assertNotNull(result.getActions());
    }


    @Test
    public void findReferences() throws Exception {
        FindReferences.Arg arg = new FindReferences.Arg();
        arg.setApiName("CasesObj");

        FindReferences.Result ret = metadataAction.findReferences(arg);
        Assert.assertNotNull(ret);
        Assert.assertTrue(ret.getCustomObjects().size() > 0);
    }

    @Test
    public void testUpdateData() {
        String json = "{\"taskId\":\"5da1f0f6688629c66d55dd9a\",\"data\":{\"object_describe_api_name\":\"AccountObj\",\"object_describe_id\":\"\",\"field_cRg0x__c\":\"41\",\"field_mjRxs__c\":\"12\",\"field_4S6iA__c\":\"29.00\",\"_id\":\"5d8c9d4f396ea10001f529b3\"},\"apiName\":\"AccountObj\",\"dataId\":\"5d8c9d4f396ea10001f529b3\",\"jsonData\":\"{\\\"object_describe_api_name\\\":\\\"AccountObj\\\",\\\"object_describe_id\\\":\\\"\\\",\\\"field_cRg0x__c\\\":\\\"41\\\",\\\"field_mjRxs__c\\\":\\\"12\\\",\\\"field_4S6iA__c\\\":\\\"29.00\\\",\\\"_id\\\":\\\"5d8c9d4f396ea10001f529b3\\\"}\",\"ignoreNonBlocking\":true}\n";
        UpdateData.Arg arg = JacksonUtil.fromJson(json, UpdateData.Arg.class);
        metadataAction.updateData(arg);
    }

    @Test
    public void update() {
        Map<String, Object> data = Maps.newHashMap();
        data.put("taskId", "5e9e916c319d1989355a100a");
        data.put("apiName", "object_UPfNh__c");
        data.put("dataId", "5e9e914fe14a1a0001c0799a");
        data.put("jsonData", "{\"object_describe_api_name\":\"object_UPfNh__c\"," +
                "\"field_aLX96__c\":\"222222\"," +
               // "\"field_EH2zk__c\":\"444\"," +
                //"\"field_0rE4v__c\":null," +
                "\"field_1e7tL__c\":\"55555\"," +
                "\"version\":13," +
                "\"_id\":\"5e9e914fe14a1a0001c0799a\"}");
        data.put("ignoreNonBlocking", true);
        UpdateData.Arg arg = JacksonUtil.fromJson(JacksonUtil.toJson(data), UpdateData.Arg.class);
        metadataAction.updateData(arg);
    }

}
