package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.GetTenantQuota;
import com.facishare.bpm.controller.web.model.HasQuota;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

/**
 * Created by cuiyongxu on 17/5/9.
 */
@Slf4j
public class TenantActionTest extends BaseTest {

    @Autowired
    private TenantAction tenantAction;

    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) tenantAction).setContextManager(contextManager);
    }

    @Test
    public void hasQuota(){
        HasQuota.Arg arg = new HasQuota.Arg();
        HasQuota.Result result =  tenantAction.hasQuota(arg);
        Assert.isTrue(result.isHasQuota());
    }



    @Test
    public void getTenantQuota(){
        GetTenantQuota.Result result =  tenantAction.getTenantQuota();
        System.out.println(result);
    }
}
