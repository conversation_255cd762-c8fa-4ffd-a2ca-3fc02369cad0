package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.GetOrganization;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * Created by cuiyongxu on 17/5/4.
 */
@Slf4j
public class OrganizationActionTest extends BaseTest {

    @Autowired
    private OrganizationAction bpmOrganizationAction;


    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) bpmOrganizationAction).setContextManager(contextManager);
    }

    @Test
    public void getOrganization(){
        GetOrganization.Arg arg = new GetOrganization.Arg();
        arg.setCRMGroup(Lists.newArrayList( "58cfbc563db71d6164dee6c0","58c2bc563db71d6164dee6c0"));
        arg.setDept(Lists.newArrayList(1000, 1020, 1062, 1326,null));
        arg.setPerson(Lists.newArrayList(1000, null,null,1000));
        arg.setRole(Lists.newArrayList("00000000000000000000000000000009",null,"00000000000000000000000000000020", "00000000000000000000000000000014"));
        GetOrganization.Result result =  bpmOrganizationAction.getOrganization(arg);



        log.info("人====={}", result.getPerson());//--
        log.info("角色====={}", result.getRole());
        log.info("部门====={}", result.getDept());//--
        log.info("组====={}", result.getCRMGroup());
    }
}
