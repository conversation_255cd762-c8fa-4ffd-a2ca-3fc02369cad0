package com.facishare.bpm.controller.web;


import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.GetTemplateDetail;
import com.facishare.bpm.controller.web.model.GetTemplateList;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.flow.mongo.bizdb.WorkflowTemplateDao;
import com.facishare.flow.mongo.bizdb.entity.WorkflowTemplateEntity;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by cuiyongxu on 16/12/27.
 */
@Slf4j
public class ProcessTemplateActionTest extends BaseTest {




    @Autowired
    private ProcessTemplateAction processTemplateAction;


    @Autowired
    private WorkflowTemplateDao workflowTemplateDao;




    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) processTemplateAction).setContextManager(contextManager);
    }


    /**
     * 创建流程模板
     */
    @Test
    public void createWorkflowTemplate(){
        String json = getOutlineJsonFromFile("deploy.json");
        WorkflowOutline workflowOutline = JsonUtil.fromJson(json, WorkflowOutline.class);
        WorkflowTemplateEntity workflowTemplateEntity = WorkflowOutline.toTemplateEntity(workflowOutline);
        WorkflowTemplateEntity entity = workflowTemplateDao.createOrUpdate("71557", workflowTemplateEntity);
        Assert.assertNotNull(entity);
    }


    /**
     * 获取模板列表
     *
     * @throws Exception
     */
    @Test
    public void getTemplateList() throws Exception {
        GetTemplateList.Arg arg = new GetTemplateList.Arg();
        GetTemplateList.Result result = processTemplateAction.getTemplateList(arg);
        Assert.assertTrue(result.getTotal() > 0);
    }


    /**
     * 获取模板详情
     * @throws Exception
     */
    @Test
    public void getTemplate() throws Exception {
        GetTemplateDetail.Arg arg = new GetTemplateDetail.Arg();
        arg.setId("232877022392254464");
        WorkflowOutline result = processTemplateAction.getTemplate(arg);
        Assert.assertNotNull(result);
    }
}
