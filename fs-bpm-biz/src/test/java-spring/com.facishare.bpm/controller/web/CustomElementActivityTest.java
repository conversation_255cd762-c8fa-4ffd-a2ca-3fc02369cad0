package com.facishare.bpm.controller.web;

import com.facishare.bpm.BaseTest;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2023/10/11
 * @apiNote 自定义业务元素的业务测试
 **/
@Slf4j
public class CustomElementActivityTest extends BaseTest {
    /**
     * 客户
     * tijiaoren
     */
    private String objectId = "60f68d684bd1dc000131fbb2";
    private String entityId = "AccountObj";

    public String testTriggerElementInstance() {
        StartInstance.Arg arg = new StartInstance.Arg();
        arg.setId("65266b756dad632421150b65");
        arg.setObjectId(objectId);
        StartInstance.Result startInstanceResult = processInstanceAction.startInstance(arg);
        log.info("返回值 = {} ", startInstanceResult);
        return startInstanceResult.getResult();
    }

    public void testCompleteTask() {

    }

    @Test
    public void testGetTask() {
        GetInstancesByObject.Arg arg = new GetInstancesByObject.Arg();
        arg.setEntityId(entityId);
        arg.setObjectId(objectId);

        GetInstancesByObject.Result result = processInstanceAction.getInstancesByObject(arg);
        result.getResult().forEach(instance -> {
            String workflowName = instance.getWorkflowName();
            String instanceId = instance.getId();
            CancelInstance.Arg cancelInstanceArg = new CancelInstance.Arg();
            cancelInstanceArg.setId(instanceId);
            cancelInstanceArg.setReason("批量取消");
//            processInstanceAction.cancelInstance(cancelInstanceArg);
        });

//        testTriggerElementInstance();

        result = processInstanceAction.getInstancesByObject(arg);
        result.getResult().forEach(instance -> {
            String workflowName = instance.getWorkflowName();
            String instanceId = instance.getId();
            log.info(instanceId + "---" + workflowName);
            GetTaskInfoByLaneId.Result tasks = getLaneTasks(instanceId);
            tasks.getResult().forEach(task -> log.info("{}"+task.getTaskName()));
        });
    }

    private GetTaskInfoByLaneId.Result getLaneTasks(String instanceId) {
        GetTaskInfoByLaneId.Arg laneArg=new GetTaskInfoByLaneId.Arg();
        laneArg.setApplyButtons(true);
        laneArg.setEntityId(entityId);
        laneArg.setInstanceId(instanceId);
        laneArg.setObjectId(objectId);
        laneArg.setLaneId("1510907084557");
        laneArg.setNotGetDatas(true);
        laneArg.setWorkflowId("65266b75017b820629a4a358");
        return processTaskAction.getTaskInfoByLaneId(laneArg);
    }

    /**
     * 定义部署
     */
    @Test
    public void deployDef() {
        String definitionJson = "{\n" +
                "    \"appId\": \"BPM\",\n" +
                "    \"workflow\": {\n" +
                "        \"activities\": [\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"type\": \"startEvent\",\n" +
                "                \"name\": \"开始\",\n" +
                "                \"description\": \"\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"type\": \"userTask\",\n" +
                "                \"name\": \"自定义元素\",\n" +
                "                \"description\": \"\",\n" +
                "                \"canSkip\": false,\n" +
                "                \"taskType\": \"anyone\",\n" +
                "                \"custom\": true,\n" +
                "                \"elementApiName\": \"first_test_element__c\",\n" +
                "                \"customCandidateConfig\": true,\n" +
                "                \"customExtension\": {\"test\":1},\n" +
                "                \"bpmExtension\": {\n" +
                "                    \"actionCode\": \"\",\n" +
                "                    \"executionType\": \"custom\",\n" +
                "                    \"executionName\": \"自定义元素\",\n" +
                "                    \"entityId\": \"AccountObj\",\n" +
                "                    \"entityName\": \"客户\",\n" +
                "                    \"objectId\": {\n" +
                "                        \"expression\": \"activity_0##AccountObj\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"execution\": {\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"name\": \"结束\",\n" +
                "                \"description\": \"\",\n" +
                "                \"type\": \"endEvent\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"variables\": [\n" +
                "            {\n" +
                "                \"id\": \"activity_0##AccountObj\",\n" +
                "                \"type\": {\n" +
                "                    \"name\": \"text\"\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"activity_*************##AccountObj\",\n" +
                "                \"type\": {\n" +
                "                    \"name\": \"text\"\n" +
                "                }\n" +
                "            }\n" +
                "        ],\n" +
                "        \"transitions\": [\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"fromId\": \"*************\",\n" +
                "                \"toId\": \"*************\",\n" +
                "                \"serialNumber\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"fromId\": \"*************\",\n" +
                "                \"toId\": \"*************\",\n" +
                "                \"serialNumber\": 1\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"extension\": {\n" +
                "        \"diagram\": [\n" +
                "            {\n" +
                "                \"id\": \"1510907084557\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 220,\n" +
                "                    \"height\": 540,\n" +
                "                    \"x\": 40,\n" +
                "                    \"y\": 60\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 60,\n" +
                "                    \"height\": 60,\n" +
                "                    \"x\": 120,\n" +
                "                    \"y\": 125\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 160,\n" +
                "                    \"height\": 50,\n" +
                "                    \"x\": 70,\n" +
                "                    \"y\": 260\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"attr\": {\n" +
                "                    \"width\": 60,\n" +
                "                    \"height\": 60,\n" +
                "                    \"x\": 109,\n" +
                "                    \"y\": 479\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"attr\": {\n" +
                "                    \"d\": \"M150,310 v80 a5.5,5.5 0 0 1 -5.5,5.5 h0 a5.5,5.5 0 0 0 -5.5,5.5 v80\",\n" +
                "                    \"toPosition\": \"top\",\n" +
                "                    \"fromPosition\": \"bottom\",\n" +
                "                    \"type\": \"polyline\"\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\": \"*************\",\n" +
                "                \"attr\": {\n" +
                "                    \"d\": \"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\",\n" +
                "                    \"toPosition\": \"top\",\n" +
                "                    \"fromPosition\": \"bottom\",\n" +
                "                    \"type\": \"polyline\"\n" +
                "                }\n" +
                "            }\n" +
                "        ],\n" +
                "        \"pools\": [\n" +
                "            {\n" +
                "                \"lanes\": [\n" +
                "                    {\n" +
                "                        \"id\": \"1510907084557\",\n" +
                "                        \"name\": \"阶段\",\n" +
                "                        \"description\": \"\",\n" +
                "                        \"activities\": [\n" +
                "                            \"*************\",\n" +
                "                            \"*************\",\n" +
                "                            \"*************\"\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"svg\": \"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" class=\\\"paas-bpm-canvas\\\" height=650 width=310 tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><defs><marker id=\\\"end-arrow_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#666666\\\"></path></marker><marker id=\\\"end-arrow-colored_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"5\\\" refY=\\\"0\\\" viewBox=\\\"-5 -5 10 10\\\"><path d=\\\"M 0,0 m -5,-5 L 5,0 L -5,5 Z\\\" fill=\\\"#49bffc\\\"></path></marker><marker id=\\\"approval-yes_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#7FC25D\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><marker id=\\\"approval-no_1510908093970\\\" markerHeight=\\\"10\\\" markerWidth=\\\"10\\\" markerUnits=\\\"userSpaceOnUse\\\" orient=\\\"auto\\\" refX=\\\"-10\\\" refY=\\\"5\\\"><g><circle fill=\\\"#DC9688\\\" cx=\\\"5\\\" cy=\\\"5\\\" r=\\\"5\\\"></circle></g></marker><filter id=\\\"box-shadow\\\"><feGaussianBlur in=\\\"SourceAlpha\\\" stdDeviation=\\\"2\\\"></feGaussianBlur><feOffset dx=\\\"0\\\" dy=\\\"1\\\" result=\\\"offsetblur\\\"></feOffset><feFlood flood-color=\\\"black\\\" flood-opacity=\\\"0.06\\\"></feFlood><feComposite in2=\\\"offsetblur\\\" operator=\\\"in\\\"></feComposite><feMerge><feMergeNode></feMergeNode><feMergeNode in=\\\"SourceGraphic\\\"></feMergeNode></feMerge></filter><style type=\\\"text/css\\\">svg {\\n        background-color: #f3f3f5;\\n      }\\n\\n      g[type=pool] {\\n        font-size: 13px;\\n      }</style></defs><g class=\\\"paas-bpm-canvas-wrapper\\\" height=\\\"100%\\\" width=\\\"100%\\\" transform=\\\"translate(0,0) scale(1)\\\" k=\\\"100\\\"><g data-id=\\\"1510907084557\\\" shape=\\\"rectangle\\\" type=\\\"pool\\\" x=\\\"40\\\" y=\\\"60\\\" width=\\\"220\\\" height=\\\"540\\\" transform=\\\"translate(40,60)\\\" tabindex=\\\"0\\\" class=\\\"paas-bpm-resizable\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"540\\\" namePos=\\\"top\\\" highlight=\\\"false\\\" stroke=\\\"#cccccc\\\" rx=\\\"0\\\" ry=\\\"0\\\" resizable=\\\"true\\\" placeAt=\\\"first\\\" external=\\\"<rect fill=&quot;transparent&quot; width=&quot;220&quot; height=&quot;60&quot; stroke=&quot;transparent&quot; rx=&quot;0&quot; ry=&quot;0&quot; y=&quot;0&quot; class=&quot;paas-bpm-pool-drag-area&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\"><rect width=\\\"220\\\" height=\\\"540\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"-4.5\\\" y=\\\"265.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"105.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"215.5\\\" y=\\\"265.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"0\\\" ry=\\\"0\\\" x=\\\"105.5\\\" y=\\\"535.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><g name=\\\"external\\\"><rect fill=\\\"transparent\\\" width=\\\"220\\\" height=\\\"60\\\" stroke=\\\"transparent\\\" rx=\\\"0\\\" ry=\\\"0\\\" y=\\\"0\\\" class=\\\"paas-bpm-pool-drag-area\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"30\\\" x=\\\"110\\\">阶段</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 545)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g><rect width=\\\"18\\\" height=\\\"18\\\" class=\\\"paas-bpm-badge\\\" x=\\\"0.5\\\" y=\\\"0.5\\\" fill=\\\"#ccc\\\"></rect><text class=\\\"paas-bpm-badge-text\\\" text-anchor=\\\"middle\\\" fill=\\\"#70757f\\\" x=\\\"9\\\" y=\\\"13\\\">1</text></g><g name=\\\"line-wrapper\\\"><g fill=\\\"transparent\\\" tabindex=\\\"0\\\" data-id=\\\"*************\\\"><path type=\\\"polyline\\\" d=\\\"M150,310 v80 a5.5,5.5 0 0 1 -5.5,5.5 h0 a5.5,5.5 0 0 0 -5.5,5.5 v80\\\" from-position=\\\"bottom\\\" stroke-width=\\\"1\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1510908093970)\\\" to-position=\\\"top\\\" start-id=\\\"*************\\\" end-id=\\\"*************\\\"></path></g><g tabindex=\\\"0\\\" data-id=\\\"*************\\\"><path d=\\\"M150,185 v38 a0,0 0 0 0 0,0 h0 a0,0 0 0 1 0,0 v38\\\" start-id=\\\"*************\\\" end-id=\\\"*************\\\" fill=\\\"transparent\\\" stroke-width=\\\"1\\\" type=\\\"polyline\\\" to-position=\\\"top\\\" from-position=\\\"bottom\\\" stroke=\\\"#aaaaaa\\\" marker-end=\\\"url(#end-arrow_1510908093970)\\\"></path></g></g><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"startEvent\\\" x=\\\"120\\\" y=\\\"125\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(120,125)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"\\\"><rect fill=\\\"#f3f3f5\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"3\\\" color=\\\"#70757f\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"52.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"52.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"35\\\" x=\\\"30\\\" fill=\\\"#70757f\\\">开始</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(0, 65)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"></text></g></g><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"userTask\\\" x=\\\"70\\\" y=\\\"260\\\" width=\\\"160\\\" height=\\\"50\\\" transform=\\\"translate(70,260)\\\" tabindex=\\\"0\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"bpm-draw-polyline-able bpm-shape-focus-node node-focused\\\" status=\\\"normal\\\"><rect fill=\\\"white\\\" width=\\\"160\\\" height=\\\"50\\\" rx=\\\"3\\\" ry=\\\"3\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"#49bffc\\\"><rect width=\\\"160\\\" height=\\\"50\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"17.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"72.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"155.5\\\" y=\\\"20.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"152.5\\\" y=\\\"17.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"75.5\\\" y=\\\"45.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"72.5\\\" y=\\\"42.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"30\\\" x=\\\"80\\\">业务活动</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(80, 55)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"><tspan font-size=\\\"20\\\">\uE906</tspan><tspan dy=\\\"-5\\\">该节点配置错误</tspan></text></g></g><g data-id=\\\"*************\\\" shape=\\\"rectangle\\\" type=\\\"endEvent\\\" x=\\\"109\\\" y=\\\"479\\\" width=\\\"60\\\" height=\\\"60\\\" transform=\\\"translate(109,479)\\\" tabindex=\\\"0\\\" status=\\\"normal\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\" class=\\\"\\\"><rect fill=\\\"#f3f3f5\\\" rx=\\\"60\\\" ry=\\\"60\\\" width=\\\"60\\\" height=\\\"60\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"3\\\" color=\\\"#70757f\\\" external=\\\"<rect x=&quot;4&quot; y=&quot;4&quot; fill=&quot;transparent&quot; rx=&quot;52&quot; ry=&quot;52&quot; width=&quot;52&quot; height=&quot;52&quot; stroke=&quot;#70757f&quot; stroke-width=&quot;1&quot; color=&quot;#e67373&quot; type=&quot;rect&quot;></rect>\\\" type=\\\"rect\\\"></rect><g fill=\\\"transparent\\\" name=\\\"bpm-shape-focus-node\\\" class=\\\"hide\\\" stroke=\\\"\\\"><rect width=\\\"60\\\" height=\\\"60\\\" rx=\\\"0\\\" ry=\\\"0\\\" fill=\\\"transparent\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"left\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"-4.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"left\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"-7.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"top\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point paas-bpm-connect-point resize\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"-4.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"top\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"-7.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"right\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ew-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"55.5\\\" y=\\\"25.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"right\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"52.5\\\" y=\\\"22.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect><rect width=\\\"9\\\" height=\\\"9\\\" name=\\\"bottom\\\" fill=\\\"white\\\" class=\\\"paas-bpm-inner-connect-point ns-resize paas-bpm-connect-point\\\" rx=\\\"9\\\" ry=\\\"9\\\" x=\\\"25.5\\\" y=\\\"55.5\\\"></rect><rect width=\\\"15\\\" height=\\\"15\\\" name=\\\"bottom\\\" fill=\\\"transparent\\\" stroke=\\\"transparent\\\" class=\\\"paas-bpm-outer-connect-point\\\" rx=\\\"15\\\" ry=\\\"15\\\" x=\\\"22.5\\\" y=\\\"52.5\\\" style=\\\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\\\"></rect></g><g name=\\\"external\\\"><rect x=\\\"4\\\" y=\\\"4\\\" fill=\\\"transparent\\\" rx=\\\"52\\\" ry=\\\"52\\\" width=\\\"52\\\" height=\\\"52\\\" stroke=\\\"#70757f\\\" stroke-width=\\\"1\\\" color=\\\"#e67373\\\" type=\\\"rect\\\"></rect></g><text text-anchor=\\\"middle\\\" name=\\\"name\\\" y=\\\"35\\\" x=\\\"30\\\" fill=\\\"#70757f\\\">结束</text><g name=\\\"error-node\\\" class=\\\"hide\\\" transform=\\\"translate(30, 65)\\\"><text text-anchor=\\\"middle\\\" alignment-baseline=\\\"middle\\\" fill=\\\"#f57a62\\\" x=\\\"0\\\" y=\\\"15\\\"><tspan font-size=\\\"20\\\">\uE906</tspan><tspan dy=\\\"-5\\\">该节点至少有一根连入的线</tspan></text></g></g></g><rect class=\\\"paas-bpm-canvas-drag-area hide\\\" height=\\\"100%\\\" width=\\\"100%\\\" fill=\\\"transparent\\\"></rect></svg>\"\n" +
                "    },\n" +
                "    \"name\": \"测试自定义元素1\",\n" +
                "    \"description\": \"自定义元素基本测试\",\n" +
                "    \"entryType\": \"AccountObj\",\n" +
                "    \"entryTypeName\": \"客户\",\n" +
                "    \"sourceWorkflowId\": \"element_test\",\n" +
                "    \"scope\": [\n" +
                "    ],\n" +
                "    \"rangeEmployeeIds\": [\n" +
                "    ],\n" +
                "    \"rangeCircleIds\": [],\n" +
                "    \"rangeGroupIds\": [],\n" +
                "    \"rangeRoleIds\": [],\n" +
                "    \"enabled\": true\n" +
                "}";
        CreateDefinition.Arg arg = JsonUtil.fromJson(definitionJson, CreateDefinition.Arg.class);
        processDefinitionAction.createDefinition(arg);
    }
}
