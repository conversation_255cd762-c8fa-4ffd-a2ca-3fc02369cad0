package com.facishare.bpm.controller.web;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.exception.BPMDeployException;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.paas.engine.bpm.*;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.model.task.BPMTask;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy;
import com.facishare.bpm.service.BPMDefinitionService;
import com.facishare.bpm.service.BPMInstanceService;
import com.facishare.bpm.service.BPMTaskService;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.internal.LinkedTreeMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 模拟测试定义到任务
 * 执行run方法一键式执行
 * Created by cuiyongxu on 17/3/10.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
public class SimulateTaskCompleteTest extends BaseTest {

    private static long seed = 0;//1 true,-1:false
    @Autowired
    private BPMInstanceService bpmInstanceService;
    @Autowired
    private BPMDefinitionService bpmDefinitionService;
    @Autowired
    private BPMTaskService bpmTaskService;
    @Autowired
    private PaasWorkflowServiceProxy paasWorkflow;
    @Autowired
    @Qualifier("bpmMetadataServiceImpl")
    private MetadataService metadataService;

    private RemoteContext context;
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private String entityId = "AccountObj";
    private String objectId = "4136fb7d05c1466b9a9b567e89974e93";//请您别删除 //"4136fb7d05c1466b9a9b567e89974e93"; c1-bpm测试账户
    private static String relatedObjectId = "58ec8b47eb8caaafc8251f2a"; // 关联节点的Id,这地方的关联对象是写死的，客户AccountObj的关联对象bpm0410的一条数据
    private static String relatedEntityId = "bpm0410__c"; // 客户的关联对象，在测试关联对象时，请和定义中的关联对象类型一致


    private String outlineId = "233763919113125888";
    private String instanceId = "";//为空则启动新的实例//58cb59fd23627b21089b9283
    private boolean autoExecuteListFlag = false;//自动执行流程定义列表
    private String profiles = "ceshi113"; //fstest


    @Before
    public void setUp() throws Exception {
        System.setProperty("-Dspring.profiles.active", profiles);
        context = new RemoteContext();
        context.setAppId(BPMConstants.APP_ID);
        context.setEa("2");
        context.setTenantId("2");
        context.setUserId("1000");//1317

        TaskHandlerType.bpmTaskService = bpmTaskService;
        TaskHandlerType.metadataService = metadataService;
    }

    /**
     * 根据objectId,entityId获取正在运行的流程实例
     */
    @Test
    public void getRunProcess() {
        //233689950515265536
        PageResult<WorkflowInstance> workflowInstancePageResult = paasWorkflow.getAllWorkflowInstances(context, null, objectId, InstanceState.in_progress_or_error, "", null);
        List<String> list = Lists.newArrayList();
        workflowInstancePageResult.getDataList().forEach(e -> list.add(e.getSourceWorkflowId()));
        list.forEach(a -> {
            List<Task> tasks = paasWorkflow.getAllInprogressTasksBySourceWorkflowId(context, a);
            tasks.forEach(b -> {
                log.info("流程实例Id: {}", b.getWorkflowInstanceId());
            });
        });
    }


    /**
     * 取消流程实例
     */
    @Test
    public void cancelInstance() {
        //启动流程实例后,如果任务上存在error状态,则查询流程实例的时候,查询不到,由于存在一个错误的流程实例,
        String instance = "58cbb85d0144008fd872d853";
        bpmInstanceService.cancelWorkflowInstance(getServiceManager(context), instance, "测试代码中止当前实例");
    }

    /**
     * 启动流程实例
     */
    @Test
    public void startInstance() {
        //启动流程实例后,如果任务上存在error状态,则查询流程实例的时候,查询不到,由于存在一个错误的流程实例,
        outlineId = "";
        outlineId = "241056090681933824";
        objectId = "5ff568b581a846a4b0488f0b903482eb";
        context.setTenantId("2");
        context.setUserId("1000");
        bpmInstanceService.startWorkflow(getServiceManager(context), TriggerSource.approval, outlineId, objectId);
    }

    /**
     * 获取流程实例详情
     */
    @Test
    public void getInstance() {
        String instanceId = "58cb7cf03db71d22e8a354b7";
        WorkflowInstance workflowInstance = bpmInstanceService.getWorkflowInstance(getServiceManager(context), instanceId);
        Assert.assertNotNull(workflowInstance);
    }


    /**
     * 自动执行方法
     */
    @Test
    public void run() {
        RefServiceManager serviceManage = getServiceManager(context);
        //发布流程定义
        try {
            System.out.println(">>>>>>>>>>" + System.getProperty("-Dspring.profiles.active"));
            WorkflowOutline outline = bpmDefinitionService.getWorkflowOutlineById(serviceManage, outlineId);
            if (null == outline) {
                log.error("未查询到流程定义");
            }
        } catch (BPMDeployException e) {
            instanceId = "";
            autoExecuteListFlag = false;
            log.error("未查询到流程定义");
            String json = getOutlineJsonFromFile("deploy.json");
            WorkflowOutline workflowOutline = JsonUtil.fromJson(json, WorkflowOutline.class);
            outlineId = bpmDefinitionService.deployWorkflow(serviceManage, workflowOutline, true, true);
        }

        if (!autoExecuteListFlag && StringUtils.isAnyBlank(outlineId, entityId, objectId)) {
            throw new RuntimeException("请检查:outlineId,entityId,objectId");
        }


        log.info("必要信息检查 ->流程定义Id: {} ,流程实例Id: {}, entityId: {} ,objectId : {}", outlineId, instanceId, entityId, objectId);


        if (autoExecuteListFlag) {
            List<WorkflowOutline> outlines = bpmDefinitionService.getAvailableWorkflows(serviceManage, entityId, null, false, null, null, Boolean.TRUE);
            outlines.forEach(e -> {
                try {
                    instanceId = bpmInstanceService.startWorkflow(serviceManage, TriggerSource.approval, e.getId(), objectId);//可能存在已经启用的不允许再次启用
                    catchException(instanceId);
                } catch (Exception e1) {
                    log.error(e1.getMessage() + " 实例跳过");
                    return;
                }

            });
            return;
        } else {
            //instanceId为空
            if (StringUtils.isEmpty(instanceId)) {
                //不自动执行流程定义列表的时候,启动一个
                instanceId = bpmInstanceService.startWorkflow(serviceManage, TriggerSource.approval, outlineId, objectId);//可能存在已经启用的不允许再次启用
            }
            catchException(instanceId);
        }

       /* boolean flag = bpmDefinitionService.deleteWorkflowById(context, outlineId);
        if (flag) {
            log.info(" {} 流程定义删除成功", outlineId);
        } else {
            log.error(" {} 流程定义删除失败", outlineId);
        }*/

    }

    public void catchException(String instanceId) {
        try {
            ready(instanceId);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            log.info("流程定义Id: {} ", outlineId);
            log.info("流程实例Id: {} ", instanceId);
        }
    }


    public void ready(String instanceId) throws InterruptedException {
        RefServiceManager serviceManage = getServiceManager(context);
        log.info("实例Id:{}", instanceId);
        WorkflowInstance workflowInstance = bpmInstanceService.getWorkflowInstance(serviceManage, instanceId);
        if (null == workflowInstance) {
            log.info("流程实例不存在: {} ", instanceId);
            return;
        }
        if (workflowInstance.getEnd() != null) {
            log.info("当前流程实例已结束: {} , 结束时间: {}", instanceId, sdf.format(workflowInstance.getEnd()));
            return;
        }
        List<String> activityInstances = workflowInstance.getActivityInstances().parallelStream().map(e -> e.getId() + "").collect(Collectors.toList());
        PageResult<Task> pageResult = paasWorkflow.getTasksByInstanceIds(context, instanceId, activityInstances);
        //获取任务
        List<BPMTask> bpmTask = bpmTaskService.getTasksByInstanceIds(serviceManage, instanceId, activityInstances);
        //通过paas获取任务,但我们只需要里面的人
        List<Task> pageTask = pageResult.getDataList();
        List<BPMTasks> bpmTasks = getBPMTasks(bpmTask, pageTask);


        if (bpmTasks == null || bpmTasks.size() == 0) {
            log.info("当前流程的任务存在异常,流程实例自动停止,请检查该实例的任务: {}", instanceId);
            bpmInstanceService.cancelWorkflowInstance(serviceManage, instanceId, "测试代码中止当前实例");
            log.info("未查询到可执行任务");
            Thread.sleep(3000);
            return;
        }
        log.info("当前实例存在任务: {} ", bpmTasks);
        executeTask(bpmTasks, instanceId);
    }


    public void executeTask(List<BPMTasks> bpmTasks, String instanceId) throws InterruptedException {
        log.warn("需要审批人,调用了引擎接口");
        RefServiceManager serviceManage = getServiceManager(context);
        log.info("任务个数: {} ", bpmTasks.size());
        bpmTasks.forEach(task -> {

            String taskId = task.getId();
            log.info("任务描述: {} , {} ", task.getName(), taskId);
            if (TaskState.pass.equals(task.getState())) {
                log.info(" {} 任务已完成,taskId : {}", task.getName(), taskId);
                return;
            }

            Map<String, Object> variables = bpmInstanceService.getWorkflowInstance(serviceManage, task.getWorkflowInstanceId()).getVariables();
            TaskHandler handler = TaskHandlerType.valueOf((String) task.getExtension().get(BPMConstants.EXECUTIONTYPE));
            handler.execute(getServiceManager(context), task, objectId, variables);

        });
        Thread.sleep(1000);
        ready(instanceId);
    }


    public enum TaskHandlerType implements TaskHandler {
        create {
            @Override
            public void execute(RefServiceManager serviceManager, BPMTasks task, String objectId, Map<String, Object> variables) {
                log.info("TaskHandlerType.create");
            }
        },
        update {
            @Override
            public void execute(RefServiceManager serviceManager, BPMTasks task, String objectId, Map<String, Object> variables) {
                log.info("TaskHandlerType.update");
                String taskId = task.getId();
                List<String> candidateIds = task.getCandidateIds();
                Map map = Maps.newHashMap();
                String opinion = "";
                String userId = candidateIds.get(getRandomInteger(candidateIds.size()));
                log.info("当前审批用户: {} ", userId);
                RemoteContext context = serviceManager.getContext();
                context.setUserId(userId);
                //赋值formValue
                updateFormToValue(context, task, objectId, map);

                boolean flag = true;
                try {
                    bpmTaskService.completeTask(serviceManager, taskId, opinion, map, null, null, true,false, null);
                } catch (BPMTaskExecuteException e) {
                    if (e.getErrorCode() == 210_01_1004 & e.getMessage().equals("您已经审批过此任务")) {
                        log.info("您已经审批过此任务");
                        return;
                    }
                }
                if (flag) {
                    log.info("更新成功:{}", taskId);
                } else {
                    log.info("更新失败:{}", taskId);
                }
            }

        },
        operation {
            public void execute(RefServiceManager serviceManager, BPMTasks task, String objectId, Map<String, Object> variables) {
                log.info("TaskHandlerType.opetation");
                String taskId = task.getId();
                List<String> candidateIds = task.getCandidateIds();
                Map map = Maps.newHashMap();
                String opinion = "";
                String userId = candidateIds.get(getRandomInteger(candidateIds.size()));
                log.info("当前审批用户: {} ", userId);
                RemoteContext context = serviceManager.getContext();
                context.setUserId(userId);

                boolean flag = false;
                try {
                    bpmTaskService.completeTask(serviceManager, taskId, opinion, map, null, null, true,false, null);
                } catch (BPMTaskExecuteException e) {
                    if (e.getErrorCode() == 210_01_1004 & e.getMessage().equals("您已经审批过此任务")) {
                        return;
                    }
                }
                if (flag) {
                    log.info("操作成功:{}", taskId);
                } else {
                    log.info("操作失败:{}", taskId);
                }
            }
        },
        approve {
            public void execute(RefServiceManager serviceManager, BPMTasks task, String objectId, Map<String, Object> variables) {
                log.info("TaskHandlerType.approve");
                List<String> candidateIds = task.getCandidateIds();
                Map map = Maps.newHashMap();
                String taskId = task.getId();
                String opinion = "审批意见:" + getRandomString(5);
                //会签操作
                if (TaskType.ALL.value.equals(task.getTaskType())) {
                    log.info("当前节点为会签节点: {} , {}", task.getName(), task.getTaskType());
                    //所有人执行
                    candidateIds.forEach(userId -> {
                        String result = getRandomResult();
                        map.put(BPMConstants.ApproveResult.RESULT, result);
                        log.info("当前审批用户: {}, 审批结果: {} ", userId, result);
                        RemoteContext context = serviceManager.getContext();
                        context.setUserId(userId);
                        boolean flag = false;
                        try {
                            bpmTaskService.completeTask(serviceManager, taskId, opinion, map, null, null, true,false, null);
                        } catch (BPMTaskExecuteException e) {
                            if (e.getErrorCode() == 210_01_1004 & e.getMessage().equals("您已经审批过此任务")) {
                                return;
                            }
                        }
                        if (flag) {
                            log.info("会签审批成功:{}", taskId);
                        } else {
                            log.info("会签审批失败:{}", taskId);
                        }
                    });
                } else {
                    String result = getRandomResult();
                    log.info("当前节点为普通任务节点: {} , {}", task.getName(), task.getTaskType());
                    //非会签随便一个人审批
                    String userId = candidateIds.get(getRandomInteger(candidateIds.size()));
                    log.info("当前审批用户: {}, 审批结果: {} ", userId, result);
                    RemoteContext context = serviceManager.getContext();
                    context.setUserId(userId);
                    map.put(BPMConstants.ApproveResult.RESULT, result);
                    boolean flag = true;
                    bpmTaskService.completeTask(serviceManager, taskId, opinion, map, null, null, true,false, null);
                    if (flag) {
                        log.info("普通任务审批成功:{}", taskId);
                    } else {
                        log.info("普通任务审批失败:{}", taskId);
                    }
                }
            }
        }, addRelatedObject {
            @Override
            public void execute(RefServiceManager serviceManager, BPMTasks task, String objectId, Map<String, Object> variables) {
                log.info("TaskHandlerType.addRelatedObject");
                String taskId = task.getId();
                List<String> candidateIds = task.getCandidateIds();
                Map data = Maps.newHashMap();
                data.put(ExtensionKey.relatedObjectId, relatedObjectId);

                String opinion = "";
                String userId = candidateIds.get(getRandomInteger(candidateIds.size()));
                log.info("当前审批用户: {} ", userId);
                RemoteContext context = serviceManager.getContext();
                context.setUserId(userId);


                boolean flag = false;
                try {
                    bpmTaskService.completeTask(serviceManager, taskId, opinion, data, null, null, true,false, null);
                } catch (BPMTaskExecuteException e) {
                    if (e.getErrorCode() == 210_01_1004 & e.getMessage().equals("您已经审批过此任务")) {
                        return;
                    }
                }
                if (flag) {
                    log.info("操作成功:{}", taskId);
                } else {
                    log.info("操作失败:{}", taskId);
                }
            }
        };

        private static void updateFormToValue(RemoteContext context, BPMTasks task, String objectId, Map maps) {
            Map<String, Object> bpmExtension = task.getExtension();
            ArrayList<ArrayList<Map>> map = (ArrayList) bpmExtension.get("form");
            map.forEach(t -> {
                t.forEach(e -> {
                    String type = (String) e.get("type");
                    String name = (String) e.get("name");
                    String value = (String) e.get("value");
                    switch (type) {
                        case "text":
                            if (!"_id".equals(name)) {
                                if (StringUtils.isEmpty((value))) {
                                    maps.put(name, "输入内容" + getRandomString(10));
                                } else {
                                    maps.put(name, value);
                                }
                            }
                            break;
                        case "select_one":
                            ArrayList<LinkedTreeMap> options = (ArrayList) e.get("options");
                            LinkedTreeMap m = options.get(getRandomInteger(options.size()));
                            //m = options.get(0); //会签的时候需要自行修改一下级别,比如:重要客户
                            String val = (String) m.get("value");
                            maps.put(name, val);
                            break;
                        case "email":
                            maps.put(name, getRandomString(6) + "@faceshare.com");
                            break;
                        case "long_text":
                            maps.put(name, "输入内容LongText" + getRandomString(4));
                            break;
                    }
                });
            });

            Map<String, Object> objectMap = metadataService.updateData(context, task.getEntityId(), objectId, JsonUtil.toJson(maps), true, Boolean.FALSE);
            if (null != objectMap) {
                log.info("调用元数据 {} 成功", task.getExtension().get(BPMConstants.EXECUTIONTYPE));
            }
        }

        private static BPMTaskService bpmTaskService;
        private static MetadataService metadataService;
        public static Logger log = LoggerFactory.getLogger(TaskHandlerType.class.getName());
    }


    /**
     * 数据转换
     *
     * @param bpmTask
     * @param pageTask
     * @return
     */
    private List<BPMTasks> getBPMTasks(List<BPMTask> bpmTask, List<Task> pageTask) {
        List<BPMTasks> bpmTasks = Lists.newArrayList();
        //缓存taskId,candidateIds
        Map<String, List<String>> tmpTask = Maps.newHashMap();
        //缓存taskId,WorkflowInstanceId
        Map<String, String> workInstances = Maps.newHashMap();

        pageTask.forEach(e -> {
            tmpTask.put(e.getId(), e.getCandidateIds());
        });

        pageTask.forEach(e -> {
            workInstances.put(e.getId(), e.getWorkflowInstanceId());
        });

        bpmTask.forEach(e -> {
            if (e != null) {
                BPMTasks bpmTasks1 = new BPMTasks();
                bpmTasks1.setId(e.getId());
                bpmTasks1.setName(e.getName());
                bpmTasks1.setDescription(e.getDescription());
                bpmTasks1.setCreateTime(e.getCreateTime());
                bpmTasks1.setModifyTime(e.getModifyTime());
                bpmTasks1.setAssigneeIds(e.getAssigneeIds());
                bpmTasks1.setCompleted(e.getCompleted());
                bpmTasks1.setCanceled(e.getCanceled());
                bpmTasks1.setTaskType(e.getTaskType());
                bpmTasks1.setActivityId(e.getActivityId());
                bpmTasks1.setActivityInstanceId(e.getActivityInstanceId());
                bpmTasks1.setWorkflowId(e.getWorkflowId());
                bpmTasks1.setEntityId(e.getEntityId());
                bpmTasks1.setEntityName(e.getEntityName());
                bpmTasks1.setObjectId(e.getObjectId());
                bpmTasks1.setRemindLatency(e.getRemindLatency());
                bpmTasks1.setLatencyUnit(e.getLatencyUnit());
                bpmTasks1.setCandidateCount(e.getCandidateCount());
                bpmTasks1.setOpinions(e.getOpinions());
                bpmTasks1.setAssignee(e.getAssignee());
                bpmTasks1.setState(e.getState());
                bpmTasks1.setIsTaskOwner(e.getIsTaskOwner());
                bpmTasks1.setExtension(e.getExtension());
                bpmTasks1.setCandidateIds(tmpTask.get(e.getId()));
                bpmTasks1.setWorkflowInstanceId(workInstances.get(e.getId()));
                bpmTasks.add(bpmTasks1);
            }
        });
        return bpmTasks;
    }


    /**
     * 获取范围内的数字
     *
     * @param size
     * @return
     */
    public static int getRandomInteger(int size) {
        return new Random().nextInt(size);
    }


    /**
     * 随机返回字符串
     *
     * @param length
     * @return
     */
    public static String getRandomString(int length) {
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 随机返回同意或者不同意
     *
     * @return
     */
    public static boolean getRandomBoolean() {
        Random random = new Random();
        if (seed == 1 || seed == -1) {
            random.setSeed(seed);
        }

        return random.nextBoolean();
    }


    @Test
    public void test() {
        for (int i = 0; i < 50; i++) {
            System.out.println(getRandomBoolean());
        }
    }

    /**
     * 随机返回统一或者不同意
     *
     * @return
     */
    public static String getRandomResult() {
        if (getRandomBoolean()) {
            return "agree";
        } else {
            return "reject";
        }
    }


    enum TaskType {
        ALL("all");
        String value;

        TaskType(String value) {
            this.value = value;
        }
    }


    interface TaskHandler {
        void execute(RefServiceManager serviceManager, BPMTasks task, String objectId, Map<String, Object> variables);
    }

    @Data
    class BPMTasks extends BPMTask {
        private List<String> candidateIds;
        private String workflowInstanceId;
    }


    public String getOutlineJsonFromFile(String jsonFileName) {
        String path = BaseTest.class.getResource("/").getPath() + File.separator + "json" + File.separator + jsonFileName;
        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }

}

