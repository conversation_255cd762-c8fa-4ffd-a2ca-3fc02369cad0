package com.facishare.bpm.controller.web;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.model.WorkflowOutlineDraft;
import com.facishare.bpm.utils.bean.BeanUtils;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 1. 直接新建草稿,保存并提交,如何删除草稿?
 * 2. 有定义,然后定义转草稿,sourceId为原来的workflowId,sourceName为原来的name,保存并提交,如何删除草稿
 */
@Slf4j
public class ProcessDraftActionTest extends BaseTest {


    @Autowired
    private ProcessDraftAction processDraftAction;

    @Autowired
    private ProcessDefinitionAction processDefinitionAction;


    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) processDraftAction).setContextManager(contextManager);
        //((BPMBaseAction) processDefinitionAction).setContextManager(contextManager);
    }


    /**
     * 直接保存草稿
     */
    @Test
    public void createDirectDraft() {
        createC("draftDirect.json");
    }

    /**
     * 有定义保存草稿
     */
    @Test
    public void createDraft() {
        createC("draft.json");
    }


    /**
     * 修改草稿
     */
    @Test
    public void updateDraft() {
        String id = createC("draftDirect.json");
        WorkflowOutlineDraft workflowOutline = getDraftByIdC(id);
        workflowOutline.setName("cuiyongxu-000010101010");
        processDraftAction.updateDraft(BeanUtils.transfer(workflowOutline, UpdateDraft.Arg.class));
        WorkflowOutline newOutline = getDraftByIdC(id);
        log.info("name:{}", newOutline.getName());
    }

    /**
     * 根据id查询草稿
     */
    @Test
    public void getDraftById() {
        String id = createC("draftDirect.json");
        WorkflowOutline workflowOutline = getDraftByIdC(id);
        log.info("workflowOutline : {}", JacksonUtil.toJson(workflowOutline));
    }

    /**
     * 查询所有草稿,关键字筛选
     */
    @Test
    public void GetDraftList() {
        GetDraftList.Arg arg = new GetDraftList.Arg();
        //arg.setKeyWord("cuiyongxu");
        GetDraftList.Result result = processDraftAction.getDraftList(arg);
        for (WorkflowOutline outline : result.getOutlines()) {
            log.info("outlineId:{},name:{},draftId:{}", outline.getId(), outline.getName(),outline.getDraftId());
        }
    }

    /**
     * 删除草稿
     */
    @Test
    public void deleteDraft() {
        String id = createC("draftDirect.json");
        WorkflowOutline workflowOutline = getDraftByIdC(id);
        log.info("{} 当前状态为:{}", id, !workflowOutline.isDeleted() ? "未删除" : "已删除");
        DeleteDefinition.Arg arg = new DeleteDefinition.Arg();
        arg.setId(id);
        processDraftAction.deleteDraft(arg);
        workflowOutline = getDraftByIdC(id);
        log.info("{} 当前状态为:{}", id, workflowOutline == null ? "已删除" : "未删除");
    }

    @Test
    public void GetDefinitionList() {
        GetDefinitionList.Arg arg = new GetDefinitionList.Arg();
        arg.setKeyWord("bpm");
        GetDefinitionList.Result result = processDefinitionAction.getDefinitionList(arg);
        result.getOutlines().forEach(workflowOutlines -> {
            System.out.println(workflowOutlines.getDraftId());
        });
    }


    /**
     * 创建定义并删除定义,校验草稿是否正常删除
     * 创建定义->保存草稿->删除定义->删除草稿
     */
    @Test
    public void deployAndDelete() {
        //读取定义文件
        String json = getOutlineJsonFromFile("draftDirect.json");
        CreateDefinition.Arg createDefArg = JsonUtil.fromJson(json, CreateDefinition.Arg.class);
        //直接设置停用 方便删除
        createDefArg.setEnabled(false);
        createDefArg.setName(createDefArg.getName() + ObjectId.get().toString());
        //保存定义
        CreateDefinition.Result createDefRst = processDefinitionAction.createDefinition(createDefArg);
        String id = createDefRst.getId();
        GetDefinition.Arg getDefArg = new GetDefinition.Arg();
        getDefArg.setId(id);
        //查询定义
        GetDefinition.Result getDefRst = processDefinitionAction.getDefinition(getDefArg);
        WorkflowOutline getDefOutline = getDefRst.getOutline();
        CreateDraft.Arg draftArg = CreateDraft.Arg.getInstance(BeanUtils.transfer(getDefOutline, CreateDraft.Arg.class));
        //创建草稿
        CreateDraft.Result crDraft = processDraftAction.createDraft(draftArg);
        log.info("rst:{}", crDraft.getId());
        //删除定义
        DeleteDefinition.Arg delArg = new DeleteDefinition.Arg();
        delArg.setId(id);
        DeleteDefinition.Result deldefRst = processDefinitionAction.deleteDefinition(delArg);
        if (!deldefRst.isResult()) {
            log.error("删除失败:{}", id);
        }
        //再次用草稿id去查询一次
        WorkflowOutline workflowOutline = getDraftByIdC(crDraft.getId());
        if (workflowOutline == null) {
            log.info("草稿删除成功:{}", crDraft.getId());
        }
    }

    /**
     * 直接新建的草稿,没有原始定义,然后编辑一下草稿,然后保存并提交,保存并提交后,草稿要删除
     * 创建草稿->保存定义->删除草稿
     */
    @Test
    public void getDraftCreateDef() {
        String draftId = createC("draftDirect.json");
        log.info("草稿Id:{}", draftId);
        // 查询出草稿
        WorkflowOutline draftOutline = getDraftByIdC(draftId);
        draftOutline.setName("崔永旭二次编辑" + ObjectId.get().toString());
        //将草稿转定义
        CreateDefinition.Arg createDefArg = BeanUtils.transfer(draftOutline, CreateDefinition.Arg.class);
        //直接设置停用 方便删除
        createDefArg.setEnabled(true);
        //保存定义
        CreateDefinition.Result createDefRst = processDefinitionAction.createDefinition(createDefArg);

        log.info("定义id:{}", createDefRst.getId());
    }


    public String createC(String fname) {
        String json = getOutlineJsonFromFile(fname);
        WorkflowOutlineDraft workflowOutline = JacksonUtil.fromJson(json, WorkflowOutlineDraft.class);
        CreateDraft.Arg arg = CreateDraft.Arg.getInstance(workflowOutline);
        CreateDraft.Result result = processDraftAction.createDraft(arg);
        log.info("result = {}", result);
        return result.getId();
    }

    private WorkflowOutlineDraft getDraftByIdC(String id) {
        GetDraft.Arg arg = new GetDraft.Arg();
        arg.setId(id);
        GetDraft.Result result = processDraftAction.getDraftById(arg);
        return result.getWorkflowOutline();
    }
    @Test
    public void testSaveExternalMessageV2(){
        GetDefinition.Arg  arg=new GetDefinition.Arg();
        arg.setId("6350f779f2d7e30001cb8d29");
        GetDefinition.Result definition = processDefinitionAction.getDefinition(arg);
        List<Map> activities = (List<Map>)definition.getOutline().getWorkflow().get("activities");
        Map map = activities.get(1);
        Map execution = (Map) map.get("execution");
        List<Map> pass = (List<Map> )execution.get("pass");
        pass.clear();
        pass.add(getActionMapping());
        System.out.println(definition);
        UpdateDefinition.Arg arg2= BeanUtils.transfer(definition.getOutline(),UpdateDefinition.Arg.class);
        UpdateDefinition.Result result = processDefinitionAction.updateDefinition(arg2);


    }

    Map<String, Object> getActionMapping() {
        return JacksonUtil.fromJson("{\n" +
                "    \"taskType\": \"external_message_v2\",\n" +
                "    \"actionMapping\":\n" +
                "    {\n" +
                "        \"appType\": \"FSAID_11491084-1\",\n" +
                "        \"linkAppType\":\"\",\n" +
                "        \"downObjects\":\n" +
                "        [\n" +
                "            {\n" +
                "                \"objectId\": \"${activity_1666250364683##SalesOrderObj##account_id}\",\n" +
                "                \"objectApiName\": \"AccountObj\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"roleIds\":\n" +
                "        [\n" +
                "            \"er_enterprise\"\n" +
                "        ],\n" +
                "        \"messageBody\":\n" +
                "        {\n" +
                "            \"title\": \"标题${activity_1666250364683##SalesOrderObj##name}\",\n" +
                "            \"fullContent\": \"内容信息${activity_1666250364683##SalesOrderObj##name}\",\n" +
                "            \"urlType\": 1,\n" +
                "            \"appId\": \"FSAID_11490c84\",\n" +
                "            \"bodyForm\":\n" +
                "            [\n" +
                "                {\n" +
                "                    \"keyElement\":\n" +
                "                    {\n" +
                "                        \"text\": \"wx3ce8441bd6cddce9_业务名称\"\n" +
                "                    },\n" +
                "                    \"valueElement\":\n" +
                "                    {\n" +
                "                        \"text\": \"${activity_1666250364683##SalesOrderObj##name}\"\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"keyElement\":\n" +
                "                    {\n" +
                "                        \"text\": \"wx3ce8441bd6cddce9_当前阶段\"\n" +
                "                    },\n" +
                "                    \"valueElement\":\n" +
                "                    {\n" +
                "                        \"text\": \"${activity_1666250364683##SalesOrderObj##name}\"\n" +
                "                    }\n" +
                "                }\n" +
                "            ],\n" +
                "            \"extraChannelList\":\n" +
                "            [\n" +
                "                \"{\\\"outChannelType\\\":4,\\\"appId\\\":\\\"FSAID_11490c84\\\",\\\"receiverChannelType\\\":6}\",\n" +
                "                \"{\\\"outChannelType\\\":2,\\\"appId\\\":\\\"FSAID_11490c84\\\",\\\"receiverChannelType\\\":6}\",\n" +
                "                \"{\\\"outChannelType\\\":5,\\\"appId\\\":\\\"FSAID_11490c84\\\",\\\"receiverChannelType\\\":6}\"\n" +
                "            ],\n" +
                "            \"templateIdKeyListMap\":\n" +
                "            {\n" +
                "                \"j-Z3Wh409PwD2AflMe2bfEcidy1qvPmaSNd1InjsYKY\":\n" +
                "                {\n" +
                "                    \"summary\": \"summarysummarysummary\",\n" +
                "                    \"业务名称\": \"wx3ce8441bd6cddce9_业务名称\",\n" +
                "                    \"当前阶段\": \"wx3ce8441bd6cddce9_当前阶段\",\n" +
                "                    \"remark\": \"remarkremarkremark\"\n" +
                "                },\n" +
                "                \"B2TSjodn874_W6l8NytALvtisUP7IiJULykBG7I_uFo\":\n" +
                "                {\n" +
                "                    \"业务名称\": \"wx3ce8441bd6cddce9_业务名称\",\n" +
                "                    \"当前阶段\": \"wx3ce8441bd6cddce9_当前阶段\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"extraDataMap\":\n" +
                "            {\n" +
                "                \"wechatTemplateId\": \"j-Z3Wh409PwD2AflMe2bfEcidy1qvPmaSNd1InjsYKY\",\n" +
                "                \"forwardType\": \"0\",\n" +
                "                \"miniTemplateId\": \"B2TSjodn874_W6l8NytALvtisUP7IiJULykBG7I_uFo\",\n" +
                "                \"targetMiniAppId\": \"wx70ff4bde6edfed15\",\n" +
                "                \"targetWxAppId\": \"wx3ce8441bd6cddce9\"\n" +
                "\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}",Map.class);
    }


}
