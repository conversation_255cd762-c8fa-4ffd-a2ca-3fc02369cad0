package com.facishare.bpm.controller.web;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.*;
import com.facishare.bpm.model.instance.EntireWorkflowInstance;
import com.facishare.bpm.model.instance.GetInstanceByObject;
import com.facishare.bpm.model.instance.WorkflowInstanceVO;
import com.facishare.bpm.model.paas.engine.bpm.TaskState;
import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.paas.I18N;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

/**
 * Created by cuiyongxu on 17/1/10.
 */
@Slf4j
public class ProcessInstanceActionTest extends BaseTest {


    @Autowired
    private ProcessInstanceAction processInstanceAction;

    @Autowired
    private ProcessTaskAction processTaskAction;

    @Before
    public void setUp() throws Exception {
        super.setUp();
    }


    /**
     * 启动流程实例
     */
    @Test
    public void startInstance() {
        String id = "367685678232403968";//流程定义id
        StartInstance.Arg arg = new StartInstance.Arg();
        arg.setId(id);
        arg.setObjectId("5b2cf708bab09c1eb4289aeb");
        StartInstance.Result result = processInstanceAction.startInstance(arg);
        log.info("返回值 = {} ", result);
    }

    /**
     * 取消流程实例
     *
     * @throws Exception
     */
    @Test
    public void cancelInstance() throws Exception {
        CancelInstance.Arg arg = new CancelInstance.Arg();
        arg.setId("58aea3513db71d978901b881");
        CancelInstance.Result result = processInstanceAction.cancelInstance(arg);
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isResult());
    }


    /**
     * 全部 ✔️
     * 进行中(error,in_progress) 暂不支持
     * 已完成(pass) 暂不支持
     * 取消的(cancel) 暂不支持
     * 我发起的流程实例 我自己发起的流程不可用,查询的是全部的 ALL,SLEF
     * 我下属发起的流程实例 CHILD
     * <p>
     * 排序可以不加
     * <p>
     * 1.查询自己发起的流程实例(分页) 已完成
     * 2.查询下属发起的流程 1000->1475 (分页) 已完成
     * 3.根据workflowName查询流程实例+SELF 已完成
     * 4.根据workflowName查询流程实例+All 已完成
     * 5.根据workflowName查询流程实例+CHILD 已完成
     * 6.根据error,in_progress,pass,cancel
     * error+in_progress 进行中
     * pass 已结束
     */
    @Test
    public void getInstanceList() {
        GetInstanceList.Arg arg = new GetInstanceList.Arg();
        arg.setPageSize(10);
        arg.setPageNumber(1);
        //arg.setWorkflowName("晚");
        //arg.setCircleType(CircleType.ALL);

        //arg.setState(InstanceState.pass);


        PageResult<WorkflowInstanceVO> page = processInstanceAction.getInstanceList(arg);
        System.out.println(">>>>>>>" + page.getTotal());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss sss");
        if (page.getTotal() > 0) {
            for (int i = 0; i < page.getDataList().size(); i++) {
                WorkflowInstanceVO vo = page.getDataList().get(i);
                log.info("===> vo :{} , {}", vo.getWorkflowName(), format.format(vo.getStart()));
            }
        } else {
            System.out.println("无有效数据");
        }

    }


    /**
     * 获取流程实例总数
     */
    @Test
    public void getInstanceListCount() {
        GetInstanceList.Arg arg = new GetInstanceList.Arg();
        arg.setPageSize(800);
        arg.setPageNumber(1);
        arg.setSourceWorkflowId("6334f7207f65690001ef756c");
        PageResult<WorkflowInstanceVO> page = processInstanceAction.getInstanceList(arg);
        log.info("page总数:{}", page.getTotal());
       /* List<WorkflowInstanceVO> workflowInstanceVOs = page.getDataList();
        int count = 0;
        for (WorkflowInstanceVO vo : workflowInstanceVOs) {
            count++;
        }
        log.info("总数:{}", count);*/
    }

    /**
     * 获取某个流程的实例列表0
     */
    @Test
    public void getInstanceListByObject() {
        GetInstanceListByObject.Arg arg = new GetInstanceListByObject.Arg();
        arg.setObjectId("64accac718b97600012b46fc");
        arg.setPageNumber(1);
        arg.setPageSize(100);
        GetInstanceListByObject.Result instanceListByObject = processInstanceAction.getInstanceListByObject(arg);
        log.info("getInstanceListByObject:{}",JacksonUtil.toJson(instanceListByObject));
        Assert.assertNotNull(instanceListByObject);
    }

    /**
     * 获取完成流程数据
     */
    @Test
    public void getNewEntireWorkflowInstance() {
        GetEntireWorkflowInstance.Arg arg = new GetEntireWorkflowInstance.Arg();
        arg.setInstanceId("5e560c78319d19a243a936c4");
        GetEntireWorkflowInstance.Result result = processInstanceAction.getNewEntireWorkflowInstance(arg);
        Assert.assertNotNull(result.getSvg());
        Assert.assertNotNull(result.getWorkflow());
        Assert.assertNotNull(result.getWorkflowInstance());
    }

    @Test
    public void getWorkflowStatsData() throws Exception {
        GetWorkflowStatsData.Arg arg = new GetWorkflowStatsData.Arg();
        arg.setSourceWorkflowId("");
    }

    @Test
    public void getInstanceLogs() throws Exception {
        GetWorkflowInstanceLog.Arg arg = new GetWorkflowInstanceLog.Arg();
//        arg.setWorkflowInstanceId("6566ec2d322f6c0e61e9d131");
        arg.setWorkflowInstanceId("66ebc00f7bd58f1774e6a22b");
        GetWorkflowInstanceLog.Result result = processInstanceAction.getWorkflowInstanceLog(arg);//实例5e3c158d319d198f40034a22 任务后动作异常实例5e3b7647688629efb0f9b520
        System.out.println("------->" + JacksonUtil.toJson(result));

    }

    @Test
    public void getInstancesByObject() throws Exception {
        I18N.setContext("71557", Locale.CHINESE.getLanguage());
        GetInstancesByObject.Arg arg = new GetInstancesByObject.Arg();
        arg.setEntityId("object_ph0GN__c");
        arg.setObjectId("671b08571080f00006c296c8");
        GetInstancesByObject.Result result = processInstanceAction.getInstancesByObject(arg);
        System.out.println("------->" + JacksonUtil.toJson(result));
    }

    @Test
    public void afterActionRetry() throws Exception {
        InstanceAfterActionRetry.Arg arg = new InstanceAfterActionRetry.Arg();
        arg.setInstanceId("5e37d83c688629846a33f59d");
        arg.setRowNum(1);
        arg.setExecuteType(0);
        InstanceAfterActionRetry.Result result = processInstanceAction.afterActionRetry(arg);
        System.out.println("------->" + JacksonUtil.toJson(result));
    }


    @Test
    public void getEntireWorkflowInstance() {
        GetEntireWorkflowInstance.Arg arg = new GetEntireWorkflowInstance.Arg();
        arg.setInstanceId("66f669c34327da31f4140e7e");
        EntireWorkflowInstance result = processInstanceAction.getEntireWorkflowInstance(arg);
        System.out.println("------->" + JacksonUtil.toJson(result));
    }


    @Test
    public void triggerInstance(){
        TriggerInstance.Arg triggerArg = new TriggerInstance.Arg();
        triggerArg.setId("6698fb35fc1dc40001f3028e");
        triggerArg.setObjectId("669ce568e4b7fe0007667cab");
        TriggerInstance.Result triggerResult = processInstanceAction.triggerInstance(triggerArg);
        System.out.println(triggerResult);
    }


    @Test
    public void textExecutionNode() throws InterruptedException {
        for (int count=0 ; count<300 ; count++){
            //触发业务流
            TriggerInstance.Arg triggerArg = new TriggerInstance.Arg();
            triggerArg.setId("62cfede0b1d8e60001fe96a8");
            triggerArg.setObjectId("62cfee162382f200014936e3");
            TriggerInstance.Result triggerResult = processInstanceAction.triggerInstance(triggerArg);
            Thread.sleep(1200);
            //获取Lane信息
            GetInstancesByObject.Arg laneArg = new GetInstancesByObject.Arg();
            laneArg.setObjectId("62cfee162382f200014936e3");
            laneArg.setEntityId("object_7prLH__c");
            GetInstancesByObject.Result laneResult = processInstanceAction.getInstancesByObject(laneArg);
            String laneId = laneResult.getResult().get(0).getLanes().get(0).getId();
            for(int i=0 ; i<2 ; i++){
                Thread.sleep(150l);
                //通过laneId查任务
                GetTaskInfoByLaneId.Arg taskLaneArg = new GetTaskInfoByLaneId.Arg();
                taskLaneArg.setApplyButtons(true);
                taskLaneArg.setEntityId("object_7prLH__c");
                taskLaneArg.setInstanceId(triggerResult.getResult());
                taskLaneArg.setLaneId(laneId);
                taskLaneArg.setObjectId("62cfee162382f200014936e3");
                taskLaneArg.setWorkflowId("62cff191d791472d6cc53ab4");
                GetTaskInfoByLaneId.Result taskLaneResult = processTaskAction.getTaskInfoByLaneId(taskLaneArg);
                //取taskIds
                List<String> taskIds = Lists.newArrayList();
                for (GetTaskInfoByLaneId.LaneTaskInfo laneTaskInfo : taskLaneResult.getResult()) {
                    if(TaskState.in_progress.equals(laneTaskInfo.getState())){
                        taskIds.add(laneTaskInfo.getId());
                    }
                }
                if(i == 1 && CollectionUtils.isEmpty(taskIds)){
                    System.out.println("出问题了，instanceId：" + triggerResult.getResult());
                    return;
                }
                if (i == 1) break;
                //完成任务
                for (String taskId : taskIds) {
                    CompleteTask.Arg completeTaskArg = new CompleteTask.Arg();
                    completeTaskArg.setTaskId(taskId);
                    completeTaskArg.setObjectId("62cfee162382f200014936e3");
                    processTaskAction.completeTask(completeTaskArg);
                }
            }
        }
    }



    @Test
    public void terminationFlow() {
        //获取Lane信息
        GetInstancesByObject.Arg laneArg = new GetInstancesByObject.Arg();
        laneArg.setObjectId("62971eb812b95e0001622da3");
        laneArg.setEntityId("AccountObj");
        GetInstancesByObject.Result laneResult = processInstanceAction.getInstancesByObject(laneArg);
        if(CollectionUtils.isEmpty(laneResult.getResult())) return;
        for (GetInstanceByObject getInstanceByObject : laneResult.getResult()) {
            CancelInstance.Arg arg = new CancelInstance.Arg();
            arg.setId(getInstanceByObject.getId());
            arg.setReason("");
            processInstanceAction.cancelInstance(arg);
        }
    }

    @Test
    public void getWorkflowStatsData1() throws Exception {
        GetWorkflowStatsData.Arg arg = new GetWorkflowStatsData.Arg();
        arg.setSourceWorkflowId("6334f7207f65690001ef756c");
        GetWorkflowStatsData.Result workflowStatsData = processInstanceAction.getWorkflowStatsData(arg);
        System.out.println("------->" + JacksonUtil.toJson(workflowStatsData));
    }



}
