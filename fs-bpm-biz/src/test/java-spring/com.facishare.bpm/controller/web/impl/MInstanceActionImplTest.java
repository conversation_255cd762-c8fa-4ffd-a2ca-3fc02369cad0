package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.mobile.MInstanceAction;
import com.facishare.bpm.controller.mobile.model.MGetEntireWorkflow;
import com.facishare.bpm.controller.mobile.model.MGetInstanceListByObject;
import com.facishare.bpm.controller.mobile.model.MGetLog;
import com.facishare.bpm.controller.mobile.model.MStartInstance;
import com.facishare.bpm.model.paas.engine.bpm.InstanceState;
import com.facishare.paas.I18N;
import com.fxiaoke.i18n.client.I18nClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by <PERSON> on 19/04/2017.
 */
public class MInstanceActionImplTest extends BaseTest{
    @Autowired
    private MInstanceAction instanceAction;
    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) instanceAction).setContextManager(contextManager);
    }

    @Test
    public void startInstance() {
        MStartInstance.Arg arg=new MStartInstance.Arg();
        instanceAction.startInstance(arg);
    }

    @Test
    public void cancelInstance() {

    }

    @Test
    public void getInstanceList() {

    }
    @Test
    public void getLogs() {
        MGetLog.Arg arg=new MGetLog.Arg();
        arg.setWorkflowInstanceId("63478d90ce9f383dac19eae1");
        MGetLog.Result result = instanceAction.getLog(arg);
        Assert.assertTrue(result.getDatas().size()>0);
    }
    @Test
    public void getInstanceListByObject() {
        MGetInstanceListByObject.Arg arg=new MGetInstanceListByObject.Arg();
        arg.setObjectId("632bcd6219eadc000125d616");
        arg.setState(InstanceState.in_progress);
        MGetInstanceListByObject.Result result = instanceAction.getInstanceListByObject(arg);
        Assert.assertNotNull(result.getDataList());
        Assert.assertTrue(result.getTotal()>0);

    }

    @Test
    public void getEntireWorkflowInstance() {

        I18N.setContext("71557", "en");
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);


        MGetEntireWorkflow.Arg arg=new MGetEntireWorkflow.Arg();
        arg.setInstanceId("63478d90ce9f383dac19eae1");
        MGetEntireWorkflow.Result result = instanceAction.getEntireWorkflowInstance(arg);
        Assert.assertNotNull(result.getSvg());
        Assert.assertNotNull(result.getWorkflow());
        Assert.assertNotNull(result.getWorkflowInstance());
    }

}
