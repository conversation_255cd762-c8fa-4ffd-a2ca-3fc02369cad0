package com.facishare.bpm.controller.web.impl;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.mobile.MTaskAction;
import com.facishare.bpm.controller.mobile.model.MGetTask;
import com.facishare.bpm.controller.mobile.model.MGetUncompletedTaskInfosByObject;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * Created by <PERSON> on 14/04/2017.
 */
public class MTaskActionImplTest extends BaseTest{
    @Autowired
    private MTaskAction taskAction;
    @Before
    public void setUp() throws Exception {
        super.setUp();
        //((BPMBaseAction) taskAction).setContextManager(contextManager);
    }

    @Test
    public void updateDataAndCompleteTask() throws Exception {

    }

    @Test
    public void getTask() throws Exception {
        MGetTask.Arg arg=new MGetTask.Arg();
        arg.setActivityInstanceId("2");
        arg.setInstanceId("63478d90ce9f383dac19eae1");
        MGetTask.Result result = taskAction.getTask(arg);
        Assert.assertNotNull(result.getButtons());
        Assert.assertNotNull(result.getMoreOperations());
    }

    @Test
    public void getTasksByObjectId(){
        MGetUncompletedTaskInfosByObject.Arg arg = new MGetUncompletedTaskInfosByObject.Arg();
        arg.setApiName("object_7prLH__c");
        arg.setObjectId("64a78038cc82630001b6cb3a");
        arg.setApplyButtons(true);
        arg.setIsMobile(true);
        MGetUncompletedTaskInfosByObject.Result result = taskAction.getUncompletedTaskInfoByObject(arg);
        Assert.assertTrue(result.getDataList().size()>0);
    }
}
