package com.facishare.bpm.controller;

import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.paas.I18N;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.httpclient.methods.GetMethod;
import org.junit.Test;

import java.io.*;
import java.util.*;

@Slf4j
public class I18NTest {
    @Test
    public void test() {
        I18N.setContext(null, "zh-TW");
        System.out.println(">>>>>>>>" + BPMI18N.PAAS_FLOW_BPM_TIPS_EXPRESSION_ERROR.text("这是真的"));
        System.out.println(">>>>>>>>" + I18N.text("paas.flow.bpm.verifiy.format.error", "任务节点", "嗯嗯"));
    }


    @Test
    public void test3() {
        System.out.println(BPMI18N.PAAS_FLOW_BPM_VERIFIY_CONDITION_VARIABLE_CONTAINS.text("a", "b", "c"));
        ;
    }

    @Test
    public void test2() {
        Locale locale = Locale.getDefault();
        System.out.println(locale.toLanguageTag());
        System.out.println(locale.getLanguage());
    }


    private Map<String, CSVRecord> i18nMap = Maps.newHashMap();


    @Test
    public void genJavaCode() {

        init("key", new String[0], "/language/i18n.csv");

        System.out.println("public enum BPMI18N { ");
        i18nMap.forEach((key, value) -> {
            String ikey = value.get("key");
            //System.out.println("//" + value.get("zh_CN") + "    " + value.get("code"));
            //System.out.println("String " + ikey.replace(".", "_").toUpperCase() + " = \"" + ikey + "\";");

            System.out.println(" " + ikey.replace(".", "_").toUpperCase() + "(\"" + ikey + "\", \"" + value.get("zh_CN") + "\"" + ",\"" + value.get("code") + "\"),");

        });

        System.out.println("}");

    }

    private void init(String i18key, String[] headers, String resourceName) {
        InputStream in = this.getClass().getResourceAsStream(resourceName);
        CSVFormat format = CSVFormat.DEFAULT.withHeader(headers).withSkipHeaderRecord().withIgnoreHeaderCase().withTrim();

        try {
            CSVParser csvParser = CSVParser.parse(in, Charsets.UTF_8, format);
            List<CSVRecord> records = csvParser.getRecords();
            records.forEach((record) -> {
                CSVRecord var10000 = (CSVRecord) this.i18nMap.put(record.get(i18key), record);
            });
        } catch (IOException var8) {
            log.warn("loading csv i18n resources error!", var8);
        }

    }

    private final static List<Integer> noPermission = Lists.newArrayList(********, ********, ********, ********, ********, ********, ********, ********, ********, ********);

    @Test
    public void tessss() {
        int intCode = Integer.valueOf("********");
        System.out.println(noPermission.contains(intCode));
        ;
    }


    @Test
    public void testArrayIndexOut() {
        Map<String, Object> dataCache = Maps.newHashMap();
        dataCache.put("AccountObj", Maps.newHashMap());

        if (MapUtils.isNotEmpty(dataCache) && CollectionUtils.isNotEmpty(dataCache.values())) {
            Map<String, Object> taskSnapshotData = (Map<String, Object>) dataCache.values().toArray()[0];
            System.out.println("--" + JacksonUtil.toJson(taskSnapshotData));
        }
    }


    @Test
    public void testForm() {
        List<List<Map<String, Object>>> form = Lists.newArrayList();
        form.add(Lists.newArrayList());//普通字段

        Map group = Maps.newHashMap();
        group.put("dd", "");
        form.add(Lists.newArrayList(group));//组件字段
        if (form.size() >= 2) {
            //从第二个开始判断
            for (int i = 1; i < form.size(); i++) {
                List<Map<String, Object>> formTwo = form.get(i);
                boolean flag = formTwo.stream().anyMatch(k -> {
                    return "group".equals(k.get("type")) && (Strings.isNullOrEmpty((String) k.get("group_type")) && "sign_in".equals(k.get("group_type")));
                });
                if (flag) {
                    System.out.println("存在签到组件");
                }
            }
        }
    }

    @Test
    public void tttt() {
        Map<String, Object> variables = Maps.newHashMap();
        String dataId = (String) variables.get("dsf");
        if (dataId == null) {
            System.out.println(111);
        }

        if ("".equals(dataId)) {
            System.out.println(222);
        }
    }

    @Test
    public void readErrorLog() {
        String errorData = getOutlineJsonFromFile("error_data.json");
        List<Map> datas = JacksonUtil.fromJsonOfGeneric(errorData, List.class);
        Map<String, Integer> count = Maps.newHashMap();
        datas.forEach(data -> {
            String tenantId = (String) data.get("tenantId");
            List<List> innerData = (List<List>) data.get("data");
            count.put(tenantId, innerData.size());
        });

        // 升序比较器
        Comparator<Map.Entry<String, Integer>> valueComparator = new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> o1, Map.Entry<String, Integer> o2) {
                return o2.getValue() - o1.getValue();
            }
        };

        // map转换成list进行排序
        List<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(count.entrySet());
        // 排序
        Collections.sort(list, valueComparator);

        // 默认情况下，TreeMap对key进行升序排序
        System.out.println("------------map按照value升序排序--------------------");
        for (Map.Entry<String, Integer> entry : list) {
            List<Map> ll = (List<Map>) getNameByEI(entry.getKey()).get("data");
            Map<String, Object> dddd = (Map<String, Object>) ll.get(0);
            int runStatus = (int) dddd.get("runStatus");
            String status = "";
            if (runStatus == 2) {
                status = "正常";
            }
            System.out.println(entry.getKey() + "," + dddd.get("enterpriseAccount") + "," + dddd.get("enterpriseName") + "," + entry.getValue() + "," + status);
        }


        log.info("共计查询{}家企业", count.size());
        long v = count.values().stream().mapToLong(ee -> ee).sum();

        log.info("共计查询出异常数据有:{}", v);

    }


    public String getOutlineJsonFromFile(String jsonFileName) {
        String path = BaseTest.class.getResource("/").getPath() + File.separator + "json" + File.separator + jsonFileName;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }


    public Map getNameByEI(String tenantId) {
        GetMethod getMethod = new GetMethod("http://devtools.foneshare.cn/enterpriseDBQueryApi/selectEnterpriseInfo?enterpriseAccounts=&enterpriseIds=" + tenantId + "&enterpriseName=");
        org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
        getMethod.addRequestHeader("Pragma", "no-cache");
        getMethod.addRequestHeader("Accept-Encoding", "gzip, deflate");
        getMethod.addRequestHeader("Accept-Language", "zh-CN,zh;q=0.9,ja;q=0.8,en-GB;q=0.7,en;q=0.6,zh-TW;q=0.5");
        getMethod.addRequestHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36");
        getMethod.addRequestHeader("Accept", "*/*");
        getMethod.addRequestHeader("Referer", "http://devtools.foneshare.cn/enterpriseDBQuery/enterpriseInfoQuery");
        getMethod.addRequestHeader("X-Requested-With", "XMLHttpRequest");
        getMethod.addRequestHeader("Cookie", "JSESSIONID=78AC18B3D87DF0E86AEFDB466A55E9D6");
        getMethod.addRequestHeader("Connection", "keep-alive");
        getMethod.addRequestHeader("Cache-Control", "no-cache");
        try {
            int code = httpClient.executeMethod(getMethod);
            if (code == 200) {

                InputStream inputStream = getMethod.getResponseBodyAsStream();
                BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
                StringBuilder stringBuffer = new StringBuilder();
                String str= "";
                while((str = br.readLine()) != null){
                    stringBuffer .append(str );
                }

                return JacksonUtil.fromJsonOfGeneric(stringBuffer.toString(), Map.class);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


}
