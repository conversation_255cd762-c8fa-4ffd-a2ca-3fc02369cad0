package com.facishare.bpm.util;

import com.facishare.bpm.controller.BaseTest;
import com.facishare.bpm.controller.web.model.UpdateDefinition;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.Test;

import java.io.*;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/4/8 4:39 PM
 */
public class DefineTest {


    @Test
    public void test() {
        String json = getOutlineJsonFromFile("deploy.json");
        Gson GSON = (new GsonBuilder()).disableHtmlEscaping().create();
        long start = System.currentTimeMillis();
        UpdateDefinition.Arg arg = GSON.fromJson(json, UpdateDefinition.Arg.class);
        long end = System.currentTimeMillis();

        System.out.println(end - start);
    }


    public String getOutlineJsonFromFile(String jsonFileName) {
        String path = BaseTest.class.getResource("/").getPath() + File.separator + "json" + File.separator + jsonFileName;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }
}
