package com.facishare.bpm

import com.facishare.bpm.utils.JsonUtil
import com.google.common.base.Strings
import spock.lang.Specification

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/5/10 5:04 PM
 */
class FormSingInTest extends Specification {

    def form = [
            [
                    [
                            "name"    : "field_Ev89c__c",
                            "label"   : "人员",
                            "type"    : "employee",
                            "required": false
                    ],
                    [
                            "name"    : "field_612Gc__c",
                            "label"   : "图片",
                            "type"    : "image",
                            "required": false
                    ],
                    [
                            "name"    : "field_1l7Qg__c",
                            "label"   : "单人审批1",
                            "type"    : "long_text",
                            "required": false
                    ]
            ],
            [
                    [
                            "name"    : "field_An75T__c",
                            "label"   : "收款时间",
                            "type"    : "date_time",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_3gu3r__c",
                            "label"   : "收款状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_Clsx2__c",
                            "label"   : "收款方式",
                            "type"    : "text",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_JWoG9__c",
                            "label"   : "收款金额",
                            "type"    : "currency",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"      : "field_vWrZg__c",
                            "label"     : "支付组件",
                            "type"      : "group",
                            "required"  : false,
                            "fields"    : [
                                    "pay_time_field"  : "field_An75T__c",
                                    "pay_status_field": "field_3gu3r__c",
                                    "pay_type_field"  : "field_Clsx2__c",
                                    "pay_amount_field": "field_JWoG9__c"
                            ],
                            "group_type": "payment"
                    ]
            ],
            [
                    [
                            "name"    : "field_2k3pm__c",
                            "label"   : "签到地址",
                            "type"    : "location",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_zmdZO__c",
                            "label"   : "拜访状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_lxk1A__c",
                            "label"   : "签到状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_pb081__c",
                            "label"   : "签到时间",
                            "type"    : "date_time",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_X3rbd__c",
                            "label"   : "签退状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_sIN72__c",
                            "label"   : "签退地址",
                            "type"    : "location",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_3o2Wu__c",
                            "label"   : "间隔时长",
                            "type"    : "number",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "sign_in_info__c",
                            "label"   : "签到信息",
                            "type"    : "embedded_object_list",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_9ufU8__c",
                            "label"   : "签退时间",
                            "type"    : "date_time",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"      : "field_Y4zQh__c",
                            "label"     : "签到组件",
                            "type"      : "group",
                            "required"  : false,
                            "signin"    : true,
                            "fields"    : [
                                    "sign_in_location_field" : "field_2k3pm__c",
                                    "visit_status_field"     : "field_zmdZO__c",
                                    "sign_in_status_field"   : "field_lxk1A__c",
                                    "sign_in_time_field"     : "field_pb081__c",
                                    "sign_out_status_field"  : "field_X3rbd__c",
                                    "sign_out_location_field": "field_sIN72__c",
                                    "interval_field"         : "field_3o2Wu__c",
                                    "sign_in_info_list_field": "sign_in_info__c",
                                    "sign_out_time_field"    : "field_9ufU8__c"
                            ],
                            "group_type": "sign_in"
                    ]
            ]
    ]


    def form2 = [
            [

            ],
            [
                    [
                            "name"    : "field_2k3pm__c",
                            "label"   : "签到地址",
                            "type"    : "location",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_zmdZO__c",
                            "label"   : "拜访状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_lxk1A__c",
                            "label"   : "签到状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_pb081__c",
                            "label"   : "签到时间",
                            "type"    : "date_time",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_X3rbd__c",
                            "label"   : "签退状态",
                            "type"    : "select_one",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_sIN72__c",
                            "label"   : "签退地址",
                            "type"    : "location",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_3o2Wu__c",
                            "label"   : "间隔时长",
                            "type"    : "number",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "sign_in_info__c",
                            "label"   : "签到信息",
                            "type"    : "embedded_object_list",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"    : "field_9ufU8__c",
                            "label"   : "签退时间",
                            "type"    : "date_time",
                            "required": false,
                            "used_in" : "component"
                    ],
                    [
                            "name"      : "field_Y4zQh__c",
                            "label"     : "签到组件",
                            "type"      : "group",
                            "required"  : false,
                            "signin"    : true,
                            "fields"    : [
                                    "sign_in_location_field" : "field_2k3pm__c",
                                    "visit_status_field"     : "field_zmdZO__c",
                                    "sign_in_status_field"   : "field_lxk1A__c",
                                    "sign_in_time_field"     : "field_pb081__c",
                                    "sign_out_status_field"  : "field_X3rbd__c",
                                    "sign_out_location_field": "field_sIN72__c",
                                    "interval_field"         : "field_3o2Wu__c",
                                    "sign_in_info_list_field": "sign_in_info__c",
                                    "sign_out_time_field"    : "field_9ufU8__c"
                            ],
                            "group_type": "sign_in"
                    ]
            ]
    ]

    def "测试form中只存在签到组件"() {

        given:

        if (form2.size() >= 2) {
            //从第二个开始判断
            for (int i = 1; i < form2.size(); i++) {
                List<Map<String, Object>> formTwo = form2.get(i);
                boolean flag = formTwo.stream().anyMatch({
                    k ->
                        k
                        println k
                        return "group".equals(k.get("type")) && (!Strings.isNullOrEmpty((String) k.get("group_type")) && "sign_in".equals(k.get("group_type")));
                });
                if (flag) {
                    System.out.println("存在签到组件");
                }
            }
        }

    }


    def "按钮修改"() {
        given:
        def defaultButtons = [
                "changeowner": "更换负责人",
                "update"     : [
                        Update           : [label: "填写"],
                        Save             : [label: "保存1"],
                        UpdateAndComplete: [label: "保存并完成"]]
        ]

        defaultButtons.forEach({ k, v ->
            if (v instanceof String) {
                defaultButtons.put("changeowner", [lable: v])
            }
        })

        println JsonUtil.toJson(defaultButtons)

    }


}
