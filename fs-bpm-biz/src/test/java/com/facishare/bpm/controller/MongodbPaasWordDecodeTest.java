package com.facishare.bpm.controller;

import com.fxiaoke.common.PasswordUtil;
import com.google.common.net.UrlEscapers;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MongodbPaasWordDecodeTest {
    private static final Pattern MONGO_URI = Pattern.compile("mongodb://((.+):(.*)@)");

    public static void main(String[] args) {
        String pwd = decodePassword("********************************************************************************************************************************************/");
        System.out.println(pwd);
    }


    private static String decodePassword(String servers) {
        String uri = servers;
        Matcher m = MONGO_URI.matcher(servers);
        if (m.find()) {
            try {
                String pwd = UrlEscapers.urlFormParameterEscaper().escape(PasswordUtil.decode(m.group(3)));
                uri = servers.substring(0, m.end(2) + 1) + pwd + servers.substring(m.end(1) - 1);
            } catch (Exception var5) {
                log.error("cannot decode " + m.group(3), var5);
            }
        }

        return uri;
    }
}
