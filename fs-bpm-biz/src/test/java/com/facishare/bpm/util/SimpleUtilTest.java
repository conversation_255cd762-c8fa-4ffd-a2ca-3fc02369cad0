package com.facishare.bpm.util;

import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/11/21 6:39 PM
 */
public class SimpleUtilTest {

    @Test
    public void longToInt() {

        /*long longFcpHeader = 1324234234234033333L;
        int n = Math.toIntExact(longFcpHeader);//java.lang.ArithmeticException: integer overflow
        int m = new Long(longFcpHeader).intValue();
        System.out.println(m);//:451826869*/
    }

    //灰度企业池
    Set<Integer> grayTenantIds = Sets.newHashSet(665383, 665385, 665386, 665387, 665471, 665472, 665473, 665476, 665477, 665478, 665479, 665480, 665482, 665483, 665484, 665485, 665486, 665492, 663515, 663516, 663517, 663518, 663519, 663520, 663521, 663522, 663523, 663524, 663525, 663526, 663527, 663528, 663529, 663530, 663531, 663532, 663533, 663534, 663495, 663496, 663497, 663498, 663499, 663500, 663501, 663502, 663503, 663505, 663506, 663507, 663508, 663509, 663510, 663511, 663512, 663513, 663514, 663476, 663477, 663478, 663479, 663480, 663481, 663482, 663483, 663484, 663485, 663486, 663487, 663488, 663489, 663490, 663491, 663492, 663493, 663494, 663455, 663457, 663458, 663459, 663460, 663461, 663462, 663463, 663464, 663465, 663466, 663467, 663468, 663469, 663470, 663471, 663472, 663473, 663474, 663475, 662502, 662503, 662505, 662506, 662507, 662508, 662509, 662510, 662511, 662512, 662514, 662515, 662516, 662518, 662519, 662520, 662521, 662522, 662523, 662524, 662945, 662946, 662947, 662948, 662949, 662950, 662951, 662952, 662953, 662954, 662955, 662956, 662957, 662958, 662959, 662960, 662961, 662962, 662963, 662964, 662965, 662966, 662967, 662968, 662969, 662971, 662972, 662973, 662974, 662975, 662976, 662977, 662978, 662979, 662980, 662981, 662982, 662983, 662984, 662985, 662986, 662987, 662988, 662989, 662990, 662991, 662992, 662993, 662994, 662995, 662996, 662997, 662998, 662999, 663000, 663001, 663002, 663003, 663004, 663005, 663006, 663007, 663008, 663009, 663010, 663011, 663012, 663013, 663014, 663015, 663016, 663017, 663018, 663019, 663020, 663021, 663022, 663023, 663024, 663025, 663026, 663027, 663028, 663029, 663030, 663031, 663032, 663033, 663034, 663035, 663036, 663037, 663038, 663039, 663040, 663041, 663042, 663043, 663044, 663045, 663046, 663047, 663048, 663049, 663050, 663051, 663052, 663053, 663054, 663055, 663056, 663057, 663058, 663059, 663060, 663061, 663062, 663063, 663064, 663065, 663066, 663067, 663068, 663069, 663070, 663071, 663072, 663073, 663074, 663075, 663076, 663077, 663078, 663079, 663080, 663081, 663082, 663083, 663084, 663085, 663086, 663087, 663088, 663089, 663090, 663091, 663092, 663093, 663094, 663095, 663096, 663097, 663098, 663099, 663100, 663101, 663102, 663103, 663104, 663105, 663106, 663107, 663108, 663109, 663110, 663111, 663112, 663113, 663114, 663115, 663116, 663117, 663118, 663119, 663120, 663121, 663122, 663123, 663124, 663125);

    /**
     * 业务流过滤测试企业池
     */
    @Test
    public void testFilterGrayTenantIds() {
        Set<Integer> bpmMasterDetails = Sets.newHashSet(489068, 65927, 353127, 508254, 106348, 46599, 354479, 282489, 343100, 101034, 81732, 322299, 542661, 543130, 387683, 475005, 48745, 26640, 83653, 243647, 6671, 7089, 488857, 553842, 481430, 74394, 53395, 529955, 562974, 565678, 527399, 569133, 78005, 570838, 307235, 554822, 575302, 564761, 57393, 589984, 75489, 593286, 594014, 594192, 594443, 601659, 597902, 603575, 491400, 590065, 590064, 609571, 607451, 590062, 615315, 590086, 66299, 620438, 621636, 387095, 621205, 620819, 619318, 627526, 624756, 629129, 622836, 333774, 627626, 590125, 390305, 633074, 633867, 631525, 632829, 590126, 631171, 632845, 555525, 635758, 235910, 640428, 638089, 637016, 269141, 626246, 641158, 596317, 590177, 640242, 641062, 557156, 590225, 590172, 637042, 590179, 642973, 641559, 641468, 642840, 606013, 642860, 645753, 645650, 633072, 41796, 385728, 649522, 590220, 590221, 590140, 650355, 590196, 651486, 639323, 649328, 654352, 590262, 656563, 590261, 656652, 655754, 658303, 590257, 652043, 653250, 653008, 590239, 421712, 654667, 663746, 665290, 662957, 662964, 666764, 667197, 662985, 669073, 667974, 662987, 654774, 663010, 590097, 663019, 670513, 667842, 664226, 671396, 663499, 667835, 672256, 672394, 673508, 673816, 673241, 675547, 670552, 674495, 676823, 673842, 663055, 678727, 673411, 678805, 679234, 678845, 679333, 663121, 679406, 678848, 680562, 442289, 667582);
        System.out.println("业务流存在主从关系数据共计:" + bpmMasterDetails.size());
        List<String> bpmRst = Lists.newArrayList();
        bpmMasterDetails.forEach(k -> {
            if (!grayTenantIds.contains(k)) {
                bpmRst.add(k + "");
            }
        });
        System.out.println("业务流过滤掉灰度企业后总数:" + bpmRst.size());
        System.out.println("业务流过滤掉灰度企业数据:" + JsonUtil.toJson(bpmRst));

        Set<Integer> approvalMasterDetails = Sets.newHashSet(511346, 590055, 610432, 610432, 68359, 589990, 556034, 556034, 663843, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 405431, 611954, 491400, 624756, 586528, 70833, 590177, 590177, 590177, 590177, 590177, 590177, 590177, 590177, 590177, 590177, 590177, 590177, 504940, 590227, 78014, 589982, 77561, 590187, 85993, 85993, 554822, 590179, 588682, 578953, 578953, 578953, 578953, 578953, 578953, 627626, 637662, 626113, 565788, 590224, 590225, 590221, 590221, 590221, 503675, 503675, 508254, 593286, 593286, 635579, 662993, 54216, 385728, 385728, 385728, 385728, 538199, 538199, 538199, 538199, 582317, 663005, 675818, 158833, 158833, 158833, 158833, 590158, 632768, 276669, 580959, 671982, 567536, 574286, 574286, 574286, 574286, 575255, 612885, 566984, 666930, 590174, 570991, 155535, 155535, 581059, 592040, 614686, 601029, 125297, 125297, 659812, 300843, 300843, 663575, 662518, 601974, 586009, 578200, 669270, 669270, 481430, 633729, 609000, 651789, 651789, 651789, 651789, 651789, 651789, 651789, 588881, 471734, 639126, 609965, 545707, 580850, 632829, 574849, 534338, 655587, 655587, 655587, 533816, 565244, 565245, 259335, 572329, 533573, 645251, 619318, 62577, 62577, 62577, 62577, 651486, 651486, 102648, 102648, 17939, 573522, 573522, 635581, 590135, 489068, 551489, 514724, 42713, 618254, 546659, 587703, 590065, 590065, 590065, 91907, 91907, 590191, 590191, 253937, 253937, 613122, 590015, 590126, 590126, 590126, 590125, 406711, 65927, 514208, 387683, 387683, 610473, 663071, 49015, 49015, 662523, 662523, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 558400, 353127, 353127, 575302, 575302, 575302, 575300, 48745, 83653, 590062, 590061, 590064, 590064, 590064, 590064, 617168, 617168, 82103, 488995, 488995, 488995, 592135, 592135, 543130, 543130, 642865, 642865, 576373, 629991, 627067, 627067, 662964, 631077, 566766, 587576, 587576, 587752, 587752, 137137, 655038, 673816, 673816, 540053, 540053, 529955, 529955, 529955, 529955, 529955, 529955, 529955, 529955, 525448, 590262, 504622, 505113, 505113, 622395, 622395, 675547, 7089, 7089, 7089, 7089, 322246, 564061, 607727, 610537, 642794);
        System.out.println("审批流存在主从关系数据共计:" + approvalMasterDetails.size());
        List<String> approvalRst = Lists.newArrayList();
        approvalMasterDetails.forEach(k -> {
            if (!grayTenantIds.contains(k)) {
                approvalRst.add(k + "");
            }
        });
        System.out.println("审批流过滤掉灰度企业后总数:" + approvalRst.size());
        System.out.println("审批流过滤掉灰度企业数据:" + JsonUtil.toJson(approvalRst));

        Collection bpmSubTract = CollectionUtils.subtract(bpmRst, approvalRst);
        Collection approvalSubTract = CollectionUtils.subtract(approvalRst, bpmRst);
        Collection union = CollectionUtils.union(approvalRst, bpmRst);
        System.out.println("业务流比审批流多:" + bpmSubTract.size() + "家,分别为:" + JsonUtil.toJson(bpmSubTract));
        System.out.println("审批流比业务流多:" + approvalSubTract.size() + "家,分别为:" + JsonUtil.toJson(approvalSubTract));


        System.out.println("业务流和审批流合计:" + new HashSet<>(union).size() + "家,分别为:" + JsonUtil.toJson(new HashSet<>(union)));

    }
}
