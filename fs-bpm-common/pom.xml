<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-bpm</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.5.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-bpm-common</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
        </dependency>
        <!--流程公共jar包-->
        <dependency>
            <groupId>com.facishare.flow.public</groupId>
            <artifactId>fs-flow-remote-proxy</artifactId>
            <version>${fs-flow-public.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.flow.public</groupId>
            <artifactId>fs-bpmn-definition-validate</artifactId>
            <version>${fs-flow-public.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.flow.public</groupId>
            <artifactId>fs-flow-pod-tools</artifactId>
            <version>${fs-flow-public.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-transport-native-unix-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 流程公共模块结束 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>i18n-util</artifactId>
            <version>1.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bpm-api</artifactId>
            <version>${reversion}</version>
        </dependency>
        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>opencc4j</artifactId>
            <version>1.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-expression</artifactId>
            <version>1.6.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-api</artifactId>
            <version>1.4.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.21</version>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>cglib-nodep</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-idempotent-util</artifactId>
            <version>1.2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>rpc-trace</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Spring and Spring Test -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-tools</artifactId>
            <version>2.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>velocity</artifactId>
                    <groupId>org.apache.velocity</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.10</version>
        </dependency>
        <!-- End Spring -->
        <!-- Mandatory dependencies for using Spock -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.1-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <!-- Optional dependencies for using Spock -->
        <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <version>3.2.4</version>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- enables mocking of classes without default constructor (together with CGLIB) -->
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <version>2.5.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.flow.public</groupId>
            <artifactId>fs-flow-api-doc</artifactId>
            <version>${fs-flow-public.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-bpm-rest-ext</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency> <!-- only required if Hamcrest matchers are used -->
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>1.3</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.api</groupId>
            <artifactId>fs-id-generator</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-flow-element-plugin-api</artifactId>
            <version>9.2.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare.flow.public</groupId>
            <artifactId>fs-flow-test-utils</artifactId>
            <version>9.5.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>
