package com.facishare.bpm.bpmn;

import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 6.2
 */
public class ActivityExt extends BaseModel {
    public ActivityExt(){}
    public ActivityExt(Map map) {
        super(map);
    }

    public boolean instanceOf(Class<? extends ActivityExt> activityClazz) {
        TypeName activityType = activityClazz.getAnnotation(TypeName.class);
        return activityType.value().equals(getType());
    }
    public String getType() {
        return String.valueOf(getProperty("type"));
    }
    public String getDefaultTransitionId() {
        return String.valueOf(getProperty("defaultTransitionId"));
    }

    public String getTaskType() {
        return String.valueOf(getProperty("taskType"));
    }

    public String getEntityId(){
        return (String)getExtension().get("entityId");
    }
    @Override
    public ActivityExt id(String id){
        setId(id);
        return this;
    }
    public static String getActivityName(ActivityExt activity){
        return (activity==null?"":activity.getName());
    }


    public ExecutionTypeEnum getExtensionType() {
        return ExecutionTypeEnum.getExecutionType(getExtension());
    }

    public Map<String, Object> getExtension() {
        return getMap(WorkflowKey.ActivityKey.bpmExtension);
    }
    /**
     * 业务扩展信息存放
     */
    public Map<String, Object> getMap(String key) {
        Map<String, Object> mapData = (Map<String, Object>) get(key);
        if(mapData==null){
            mapData = Maps.newHashMap();
        }
        return mapData ;
    }

    public Object getTimeoutExecution() {
        return get(WorkflowKey.ActivityKey.timeoutExecution);
    }
    /**
     * @return 获取选择和新建关联对象的 类型
     */
    public String getRelatedEntityId() {
        return (String) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityId);
    }

    public List getFrom() {
        return (List) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.form);
    }

    public Object getDefaultButtons() {
        return getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.DEFAULT_BUTTONS);
    }
    /**
     * 是否开启了互联应用
     */
    public Boolean isLinkAppEnable() {
        Object linkAppEnable = get(WorkflowKey.linkAppEnable);
        return Objects.isNull(linkAppEnable)? Boolean.FALSE : (Boolean) linkAppEnable;
    }

    /**
     * 互联应用id
     */
    public String getLinkApp() {
        Object linkApp = this.get(WorkflowKey.linkApp);
        return Objects.isNull(linkApp) ? null : String.valueOf(linkApp);
    }

    public List<String> getCommonButtonApiNames(){
        return (List<String>) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.COMMON_BUTTON_API_NAMES);
    }

    public Object getImportObject() {
        return get(WorkflowKey.importObject);
    }

    public Integer getLinkAppType() {
        Object linkAppType = this.get(WorkflowKey.linkAppType);
        return Objects.nonNull(linkAppType) ? new BigDecimal(String.valueOf(linkAppType)).intValue() : null;
    }

}
