package com.facishare.bpm.bpmn;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.Reminder;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.verifiy.handler.bean.CustomFunction;
import com.facishare.bpm.utils.NumberFormatUtil;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 6.2
 */
@TypeName("userTask")
public class UserTaskExt extends ActivityExt {

    private transient Map<String, Set<Object>> assignee;
    private transient List<Map<String, Object>> groupHandler;

    public UserTaskExt() {

    }

    public UserTaskExt(Map map) {
        super(map);
    }

    public static UserTaskExt of(ExecutableWorkflowExt workflow, String activityId) {
        for (ActivityExt item : workflow.getActivities()) {
            if (activityId.equals(item.getId())) {
                return new UserTaskExt(item);
            }
        }
        return null;
    }

    public Map<String, Set<Object>> getAssignee() {
        Object temp = getProperty(WorkflowKey.ActivityKey.assignee);
        if (this.assignee == null) {
            if (temp != null) {
                try {
                    this.assignee = JsonUtil.fromJson(JsonUtil.toJson(temp), new TypeToken<Map<String, Set<Object>>>() {
                    }.getType());
                } catch (Throwable e) {
                    //                    throw new BPMWorkflowDefVerifyException(getName() + " 没有设置处理人");
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_NOT_FOUND_ASSIGNEED, getName());
                }
            } else {
                this.assignee = Maps.newHashMap();
            }
        }
        put(WorkflowKey.ActivityKey.assignee, this.assignee);
        return this.assignee;
    }

    public List<Map<String, Object>> getGroupHandler() {
        if(Objects.nonNull(this.groupHandler)){
            return this.groupHandler;
        }
        Object temp = getProperty(WorkflowKey.ActivityKey.groupHandler);
        if (temp != null) {
            try {
                this.groupHandler = JsonUtil.fromJson(JsonUtil.toJson(temp), new TypeToken<List<Map<String, Object>>>() {
                }.getType());
            } catch (Throwable e) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ERROR, getName());
            }
        } else {
            this.groupHandler = Lists.newArrayList();
        }
        return this.groupHandler;
    }

    public CustomFunction getAssigneeFunction() {
        Object temp = getProperty(WorkflowKey.ActivityKey.assigneeFunction);
        if(Objects.isNull(temp)){
            return null;
        }
        CustomFunction customFunction;
        try {
            customFunction = JsonUtil.fromJson(JsonUtil.toJson(temp), CustomFunction.class);
        }catch (Exception e){
            return null;
        }
        return customFunction;
    }

    /*
     "reminders":[
        {
            "remindStrategy":2,
            "remindContent":"${workflowName} 的任务 ${taskName} 已完成，完成时间 ${taskCompletedTime}。${当前节点审批意见}  ${当前节点审批意见} ",
            "remindTitle":"任务完成通知",
            "remindTargets":{
                "ext_bpm":[
                    "activity_1534495594193##assigneeId$$节点处理人"
                ]
            }
        }
    ]
     */
    public List<Reminder> getReminders() {
        Object temp = getProperty(WorkflowKey.ActivityKey.reminders);
        if (temp == null) {
            return null;
        }
        if(!(temp instanceof List)){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TIME_OUT_REMINDER_STRUCTURE_ERROR, getName());
        }
        List<Reminder> reminders = JacksonUtil.fromJson(JacksonUtil.toJson(temp), new TypeReference<List<Reminder>>() {
        });
        setProperty(WorkflowKey.ActivityKey.reminders, reminders);
        return reminders;
    }

    //获取超时提醒上的提醒人员ext_bpm
    public Set<Object> getExtBpmFromReminders() {
        List<Reminder> reminders = getReminders();
        if (CollectionUtils.isEmpty(reminders)) {
            return Sets.newHashSet();
        }
        return reminders.stream().collect(Sets::newHashSet, (set, reminder) -> {
            Map<String, Set<Object>> remindTargets = reminder.getRemindTargets();
            if (!(BPMConstants.SEND_EMAIL.equals(reminder.getChannel()) || BPMConstants.SEND_SMS.equals(reminder.getChannel()))) {
                /**
                 * 超时提醒 提醒人不能为空 校验
                 */
                if (MapUtils.isEmpty(remindTargets)) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REMIND_TARGET_NOT_NULL, getActivityName(this));
                }
                Set<Object> extBpmFromRemindTarget = remindTargets.get(BPMConstants.Assignee.EXT_BPM);
                if (CollectionUtils.isNotEmpty(extBpmFromRemindTarget)) {
                    set.addAll(extBpmFromRemindTarget);
                }
            }
        }, Set::addAll);
    }

    public Boolean getRemind() {
        Object temp = getProperty(WorkflowKey.ActivityKey.remind);
        if (temp != null && temp instanceof Boolean) {
            return (Boolean) temp;
        }
        return false;
    }

    public Object getRemindLatency() {
        return getProperty(WorkflowKey.ActivityKey.remindLatency);
    }


    public static boolean hasAssignee(Map<String, Set<Object>> assignees) {
        if (MapUtils.isEmpty(assignees)) {
            return false;
        }
        return !assignees.values().stream().allMatch(item -> CollectionUtils.isEmpty(item));
    }

    public Map<String, List<ExecutionItem>> getExecution() {
        return fromExecution((Map<String, List<Map>>) get(WorkflowKey.ActivityKey.execution));
    }


    public boolean isAddRelatedObject() {
        return getExtensionType().equals(ExecutionTypeEnum.addRelatedObject);
    }

    public static Map<String, List<ExecutionItem>> fromExecution(Map<String, List<Map>> execution) {
        Map<String, List<ExecutionItem>> rst = Maps.newHashMap();
        if (MapUtils.isNotEmpty(execution)) {
            execution.forEach((key, value) -> rst.put(key, ExecutionItem.from(value)));
        }
        return rst;
    }

    /**
     * @return 获取选择和新建关联对象的 哪个字段
     */
    public String getRelatedListName() {
        return (String) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.relatedListName);
    }

    public String getRelatedEntityName() {
        return (String) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.relatedEntityName);
    }

    public String getEntityName() {
        return (String) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.entityName);
    }
    public String getFieldApiName() {
        return (String) getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.fieldApiName);
    }

    //指定下一节点处理人
    public boolean isAssignNextTask(){
        Object assignNextTask = get("assignNextTask");
        //1:指定 0:不指定
        return Objects.nonNull(assignNextTask) && 1 == new BigDecimal(String.valueOf(assignNextTask)).intValue();
    }

    /**
     * latencyUnit:1-天；2-小时；3-分钟,前端传递,不信任前端 latencyUnitStr 可能为 ""
     *
     * @return 允许停留时长 的数据类型
     */
    public int getLatencyUnit() {
        Object latencyUnitStr = get(BPMConstants.latencyUnit);
        if (latencyUnitStr == null) {
            return 2;
        }
        // 可能前端传递了一个 ""
        Integer latencyUnit = NumberFormatUtil.instance.getIntegerByString(latencyUnitStr + "");
        // 如果为空,则默认是小时,适配线上数据
        if (latencyUnit == null) {
            return 2;
        } else {
            return latencyUnit;
        }
    }

    public WorkflowRule getPassCompleteRule() {
        Object ruleMap = get("rule");
        if (ruleMap != null) {
            return JsonUtil.fromJson(JsonUtil.toJson(ruleMap), WorkflowRule.class);
        }
        return null;
    }


    public WorkflowRule getRejectCompleteRule() {
        Object ruleMap = get("rejectRule");
        if (ruleMap != null) {
            return JsonUtil.fromJson(JsonUtil.toJson(ruleMap), WorkflowRule.class);
        }
        return null;
    }

    public CustomFunction getPassFunctionRule() {
        Object ruleMap = get("functionRule");
        if (ruleMap != null) {
            return JsonUtil.fromJson(JsonUtil.toJson(ruleMap), CustomFunction.class);
        }
        return null;
    }


    public CustomFunction getRejectFunctionRule() {
        Object ruleMap = get("rejectFunctionRule");
        if (ruleMap != null) {
            return JsonUtil.fromJson(JsonUtil.toJson(ruleMap), CustomFunction.class);
        }
        return null;
    }


    /**
     * 应用节点变成 自定义元素 后，
     * 其以下信息会存储到customExtension中
     * executionType: "externalApplyTask"
     * externalApply: {
     *      appCode: "0",
     *      appName: "服务通-上游厂家",
     *      actionCode: "assign",
     *      actionName: "指派任意对象"
     * }
     *
     * 应用节点同步待办分类, 是否可以弱化到通用中， 将映射的apiName与分类对标
     * 这样 应用节点，从此成为历史 （可以做到某个版本后 应用节点类型 彻底下线 ）
     */
    public boolean customIsExternalApplyTask(){
        return ExecutionTypeEnum.externalApplyTask.name().equals(getCustomExtension().get(BPMConstants.EXECUTIONTYPE));
    }

    /**
     * 是否自定义元素 的节点
     */
    public Boolean isCustom(){
        return Boolean.TRUE.equals(get(WorkflowKey.ActivityKey.custom));
    }

    /**
     * 是否自定义处理人逻辑
     */
    public Boolean isCustomCandidateConfig(){
        return Boolean.TRUE.equals(get(WorkflowKey.ActivityKey.customCandidateConfig));
    }
    /**
     * 获取 自定义元素的 apiName
     */
    public String getElementApiName(){
        return (String)get(WorkflowKey.ActivityKey.elementApiName);
    }

    /**
     * 业务扩展信息存放
     */
    public Map<String, Object> getCustomExtension() {
        return getMap(WorkflowKey.ActivityKey.customExtension);
    }

}

