package com.facishare.bpm.bpmn;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.handler.ConditionHandler;
import com.facishare.bpm.utils.MapUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date on 2018/6/1
 * @since 6.3
 */
@Data
public class WorkflowRule {
    private String ruleId;
    private boolean deleted;
    private String tenantId;
    private String appId;
    private String entityId;
    private String ruleType;
    private List<Integer> triggerTypes;
    private String conditionPattern;
    private List<RuleCondition> conditions;
    private String tip;
    private String workflowSrcId;
    private Long createTime;
    private String creator;
    private Long modifyTime;
    private String modifier;

    public void setWorkflowProperties(String tenantId, String workflowSrcId, String entityId) {
        setTriggerTypes(new ArrayList());
        setAppId(BPMConstants.APP_ID);
        setWorkflowSrcId(workflowSrcId);
        setTenantId(tenantId);
        setEntityId(entityId);
    }

    public List<String> getAllFields() {
        List<String> fields = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(conditions)) {
            for (RuleCondition condition : conditions) {
                fields.addAll(condition.getFields());
            }
        }
        return fields;
    }

    public void clearWorkflowRule() {
        this.setRuleId(null);
        this.setTenantId(null);
//        this.setWorkflowSrcId(null);
        this.setCreator(null);
        this.setModifier(null);
    }

    public static WorkflowRule convertFromJson(String workflowJson) {
        WorkflowRule rule = JsonUtil.fromJson(workflowJson, WorkflowRule.class);
        if (Objects.nonNull(rule) && CollectionUtils.isNotEmpty(rule.getConditions())) {
            rule.getConditions().forEach(condition -> {
                if (Objects.isNull(condition.getRightSide())) {
                    condition.setRightSide(new ConditionField());
                }
            });
        }
        return rule;
    }

    @Data
    public static class RuleCondition {
        private ConditionField leftSide;
        private String operator;
        private ConditionField rightSide;
        private int rowNo;

        public List<String> getFields() {
            List<String> result = Lists.newArrayList();
            result.add("${" + leftSide.getFieldName() + "}");
            if (rightSide != null) {
                result.add(rightSide.getValue() + "");
            }
            return result;
        }
    }

    @Data
    public static class ConditionField {
        private String fieldName;
        private String fieldSrc;
        private String fieldType;
        private Object value;
        private Map<String, Object> metadata;
    }

    /**
     * 验证包含子部门的逻辑
     *
     * @param serviceManager
     */
    public void validateContainSubDept(RefServiceManager serviceManager) {
        this.getConditions().forEach(condition -> {
            WorkflowRule.ConditionField rightSide = condition.getRightSide();
            if (rightSide == null) {
                return;
            }
            Map<String, Object> ext = condition.getRightSide().getMetadata();
            boolean containSubDept = MapUtil.instance.getBool(ext, "containSubDept");
            if (containSubDept) {
                WorkflowRule.ConditionField left = condition.getLeftSide();
                //activity_0##SalesOrderObj
                //activity_1##58932891821##SalesOrderObj
                //activity_0##SalesOrderObj##@OWNER_MAIN_DEPT_PATH
                //activity_158932891821##SalesOrderObj##@OWNER_MAIN_DEPT_PATH
                //activity_158932891821##SalesOrderObj##@OWNER_MAIN_DEPT_PATH
                //activity_0##object_4i9O5__c##field_5QnA3__c##field_habeJ__c
                //activity_158932891821##object_4i9O5__c##field_5QnA3__c##field_habeJ__c
                String nameType = ConditionHandler.getType(left.getFieldName(), serviceManager);
                if (!BPMConstants.DEPARTMENT.equals(nameType) && !BPMConstants.DEPARTMENT_MANY.equals(nameType)) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_FILTER_CONFIG_DEPT_FIELD_ERROR);
                }
            }
        });
    }


}
