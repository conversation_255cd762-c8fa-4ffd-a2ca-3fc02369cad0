package com.facishare.bpm.bpmn.condition;

import com.facishare.bpm.bpmn.BaseModel;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 6.2
 */
public class ConditionExt extends BaseModel {

    public static final String TYPE = "type";
    public static final String LEFT = "left";
    public static final String RIGHT = "right";
    public static final String CONDITIONS = "conditions";
    public static final String OR = "or";
    public static final String AND = "and";
    public static final String VALUE = "value";
    public static final String EXPRESSION = "expression";
    public static final String NAME = "name";


    public ConditionExt(Map map) {
        super(map);
    }
    public String getType(){
        return String.valueOf(getProperty(TYPE));
    }
    public ValueExt getLeft(){
        Map left = (Map) getProperty(LEFT);
        if(left==null){
            return null;
        }
        return new ValueExt(left);
    }
    public ValueExt getRight(){
        Map right = (Map) getProperty(RIGHT);
        if(right==null){
            return null;
        }
        return new ValueExt(right);
    }
    public List<ConditionExt> getConditions(){
        List<Map> conditions= (List<Map>) get(CONDITIONS);
        if(CollectionUtils.isNotEmpty(conditions)){
            return conditions.stream().map(item->new ConditionExt(item)).collect(Collectors.toList());
        }
        return null;

    }

    public boolean isComparator() {
        if(getType().equals(OR)||getType().equals(AND)){
            return false;
        }
        return true;
    }

    public static class ValueExt extends BaseModel {

        public ValueExt(Map map) {
            super(map);
        }
        public ValueType getType(){
            Map type=(Map) getProperty(TYPE);
            if(type==null){
                return null;
            }
            return new ValueType(type);
        }
        public Object getValue(){
            return get(VALUE);
        }
        public String getExpression(){
            return String.valueOf(get(EXPRESSION));
        }
    }

    public static class ValueType extends BaseModel{
        public ValueType(Map map) {
            super(map);
        }

        @Override
        public String getName(){
            return String.valueOf(get(NAME));
        }
    }

}
