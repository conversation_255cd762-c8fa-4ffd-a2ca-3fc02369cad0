package com.facishare.bpm.bpmn;

import com.facishare.bpm.bpmn.condition.ConditionExt;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 6.2
 */
public class TransitionExt  extends BaseModel {

    public static final String CONDITION = "condition";
    public static final String FROM_ID = "fromId";
    public static final String TO_ID = "toId";
    public static final String SERIAL_NUMBER = "serialNumber";

    public TransitionExt() {
    }

    public TransitionExt(Map map) {
        super(map);
    }

    public String getFromId() {
        return String.valueOf(get(FROM_ID));
    }
    public String getToId() {
        return String.valueOf(get(TO_ID));
    }

    public ConditionExt getCondition() {
        Object condition=getProperty(CONDITION);
        if(Objects.isNull(condition)){
            return null;
        }
        return new ConditionExt((Map)condition);
    }

    public Integer getSerialNumber() {
        Object seriablNumber=get(SERIAL_NUMBER);
        if(seriablNumber==null){
            return null;
        }
        return Integer.valueOf(String.valueOf(seriablNumber));
    }

    public void setFromId(String from) {
        setProperty(FROM_ID,from);
    }

    public void setToId(String to) {
        setProperty(TO_ID,to);
    }
}
