package com.facishare.bpm.bpmn;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.WorkflowJsonUtil;
import com.facishare.bpm.utils.NumberFormatUtil;
import com.facishare.bpmn.definition.model.CustomVariable;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 6.2
 */
@Slf4j
@EqualsAndHashCode
public class ExecutableWorkflowExt extends BaseModel {
    @Transient
    private List<ActivityExt> activities;
    @Transient
    private List<TransitionExt> transitions;
    @Transient
    private List<VariableExt> variables;

    public ExecutableWorkflowExt() {
    }

    public ExecutableWorkflowExt(Map map) {
        super(map);
    }

    public String toJson() {
        return JsonUtil.toJson(this);
    }

    public static ExecutableWorkflowExt of(String workflowJson){
            ExecutableWorkflowExt executableWorkflowExt = WorkflowJsonUtil.convertExecutableWorkflowFromJson(workflowJson);
            //如果是外部流程
            if (executableWorkflowExt.isExternalFlow()) {
                executableWorkflowExt.updateExternalFlowLinkAppEnable();
            }

            return executableWorkflowExt;
    }

    public static ExecutableWorkflowExt fromJson(String workflowJson) {
        try {
            return new ExecutableWorkflowExt(JacksonUtil.fromJson(workflowJson, Map.class));
        } catch (RuntimeException e) {
            log.error("fromJson:{}", workflowJson, e);
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_FLOW_HANDLE_ERROR);
        }
    }

    public Map<String, Set<Object>> getApplicantWhenSystemUser() {
        Object applicantWhenSystemUser = this.get(WorkflowKey.applicantWhenSystemUser);
        if (Objects.isNull(applicantWhenSystemUser)) {
            return null;
        }
        return JacksonUtil.fromJson(JsonUtil.toJson(applicantWhenSystemUser), new TypeReference<Map<String, Set<Object>>>() {});
    }

    public void setVariables(List<VariableExt> variables) {
        removeProperty(WorkflowKey.variables);
        put(WorkflowKey.variables,variables);
    }

    public List<ActivityExt> getActivities() {
        if (activities == null) {
            List<Map> temp = (List<Map>) get(WorkflowKey.activities);
            if (CollectionUtils.isNotEmpty(temp)) {
                this.activities = temp.stream().map(activity -> new ActivityExt(activity)).collect(Collectors.toList());
            } else {
                this.activities = Lists.newArrayList();
            }
            put(WorkflowKey.activities,this.activities);
        }
        return this.activities;
    }

    public List<TransitionExt> getTransitions() {
        if (this.transitions == null) {
            List<Map> temp = (List<Map>) get(WorkflowKey.transitions);
            if (CollectionUtils.isNotEmpty(temp)) {
                this.transitions = temp.stream().map(activity -> new TransitionExt(activity)).collect(Collectors.toList());
            } else {
                this.transitions = Lists.newArrayList();
            }
        }
        return this.transitions;
    }

    public List<VariableExt> getVariables() {
        if (this.variables == null) {
            List<Map> temp = (List<Map>) get(WorkflowKey.variables);
            if (CollectionUtils.isNotEmpty(temp)) {
                this.variables = temp.stream().map(variable -> new VariableExt(variable)).collect(Collectors.toList());
            } else {
                this.variables = Lists.newArrayList();
            }
        }
        return this.variables;
    }

    public void setSourceWorkflowId(String id) {
        setProperty(WorkflowKey.sourceWorkflowId, id);
    }

    public String getSourceWorkflowId() {
        return String.valueOf(getProperty(WorkflowKey.sourceWorkflowId));
    }

    public void setActivities(List<ActivityExt> activityList) {
        setProperty(WorkflowKey.activities, activityList);
    }

    public Map<String, ActivityExt> getActivityMaps() {
        return getActivities().stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
    }

    public boolean isExternalFlow() {
        Object externalFlow = this.get(WorkflowKey.externalFlow);
        return (externalFlow != null && (externalFlow.equals(1)||externalFlow.equals(1.0)));
    }

    public List<String> getCustomVariablesKeys() {
        List<CustomVariable> customVariables = getCustomVariables();
        return customVariables.stream().map(CustomVariable::getId).collect(Collectors.toList());
    }

    public List<CustomVariable> getCustomVariables() {
        Object customVariableTable = this.get(WorkflowKey.customVariableTable);
        if (customVariableTable instanceof List) {
            return JacksonUtil.fromJson(JacksonUtil.toJson(customVariableTable), new TypeReference<List<CustomVariable>>() {
            });
        }
        return Lists.newArrayList();
    }

    public List<Map<String,Object>> getCustomVariableTable() {
        Object customVariableTable = this.get(WorkflowKey.customVariableTable);
        if (customVariableTable instanceof List) {
            return JacksonUtil.fromJson(JacksonUtil.toJson(customVariableTable), new TypeReference<List<Map<String,Object>>>() {
            });
        }
        return Lists.newArrayList();
    }

    public boolean isLinkAppEnable() {
        Object linkAppEnable = this.get(WorkflowKey.linkAppEnable);
        return Objects.isNull(linkAppEnable) ? Boolean.FALSE : (boolean) linkAppEnable;
    }

    //互联应用id
    public String getLinkApp() {
        Object linkApp = this.get(WorkflowKey.linkApp);
        return Objects.isNull(linkApp) ? null : String.valueOf(linkApp);
    }

    public String getLinkAppName() {
        Object linkAppName = this.get(WorkflowKey.linkAppName);
        return Objects.isNull(linkAppName) ? null : String.valueOf(linkAppName);
    }


    //互联应用type
    public int getLinkAppType() {
        Object linkAppType = this.get(WorkflowKey.linkAppType);
        return Objects.isNull(linkAppType) ? 0 : new BigDecimal(String.valueOf(linkAppType)).intValue();
    }

    public ExecutableWorkflowExt setTransitions(List<TransitionExt> transitions) {
        this.setProperty(WorkflowKey.transitions,transitions);
        return this;
    }

    public String getCreator(){
        return (String)get("creator");
    }
    public String getModifier(){
        return(String)get("modifier");
    }
    public Long getCreateTime(){
        return NumberFormatUtil.instance.getLongByString(get("createTime"));
    }
    public Long getModifyTime(){
        return NumberFormatUtil.instance.getLongByString(get("modifyTime"));
    }

    public void updateExternalFlowLinkAppEnable() {
        getActivities().stream().filter(task -> task instanceof UserTaskExt)
                .forEach(task -> task.setProperty(WorkflowKey.linkAppEnable, false));
    }

    public Map getErrorNotifyRecipients(){
        return (Map) get("errorNotifyRecipients");
    }

    public void setErrorNotifyRecipients(Map map){
        put("errorNotifyRecipients", map);
    }
}
