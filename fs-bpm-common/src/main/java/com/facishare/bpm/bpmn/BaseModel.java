package com.facishare.bpm.bpmn;

import com.google.common.base.Objects;
import org.apache.commons.lang.NotImplementedException;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 6.2
 */
public class BaseModel extends LinkedHashMap {
    private transient Map source;

    public BaseModel() {
    }

    public BaseModel(Map map) {
        super.putAll(map);
        this.source = map;
    }

    @Override
    public Object put(Object key, Object value) {
        if (source != null) {
            this.source.put(key, value);
        }
        return super.put(key, value);
    }

    protected void setSource(Map source) {
        this.source = source;
    }

    public String getName() {
        return String.valueOf(get("name"));
    }

    public String getId() {
        return String.valueOf(this.get("id"));
    }

    public BaseModel id(String id) {
        this.setProperty("id", id);
        return this;
    }

    public String getDescription() {
        return String.valueOf(getProperty("description"));
    }

    public Object getProperty(String key) {
        return get(key);
    }

    public void setProperty(String key, Object value) {
        put(key, value);
    }

    public void setName(String name) {
        setProperty("name", name);
    }

    public void setDescription(String description) {
        setProperty("description", description);
    }

    public void removeProperty(String name) {
        super.remove(name);
        //remove(name);
    }

    public void setId(Object id) {
        setProperty("id", id);
    }


    @Override
    public Object remove(Object key) {
        throw new NotImplementedException();
    }

    @Override
    public Object putIfAbsent(Object key, Object value) {
        throw new NotImplementedException();
    }

    @Override
    public boolean remove(Object key, Object value) {
        throw new NotImplementedException();
    }

    @Override
    public boolean replace(Object key, Object oldValue, Object newValue) {
        throw new NotImplementedException();
    }

    @Override
    public Object replace(Object key, Object value) {
        throw new NotImplementedException();
    }

    @Override
    public Object computeIfAbsent(Object key, Function mappingFunction) {
        throw new NotImplementedException();
    }

    @Override
    public Object computeIfPresent(Object key, BiFunction remappingFunction) {
        throw new NotImplementedException();
    }

    @Override
    public Object compute(Object key, BiFunction remappingFunction) {
        throw new NotImplementedException();
    }

    @Override
    public Object merge(Object key, Object value, BiFunction remappingFunction) {
        throw new NotImplementedException();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof BaseModel)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        BaseModel baseModel = (BaseModel) o;
        return Objects.equal(source, baseModel.source);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(super.hashCode(), source);
    }
}
