package com.facishare.bpm.bpmn;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 6.2
 */
public class VariableExt extends BaseModel {
    public VariableExt(Map map) {
        super(map);
    }

    public VariableExt(String id, String typeName) {
        Map<String,Object> data= Maps.newHashMap();
        data.put("id",id);
        Map<String,Object> type=Maps.newHashMap();
        type.put("name",typeName);
        data.put("type",type);
        this.putAll(data);
    }

    public String getTypeName() {
        Map<String,Object> dataType= (Map<String, Object>) getProperty("type");
        return String.valueOf(dataType.get("name"));

    }

}
