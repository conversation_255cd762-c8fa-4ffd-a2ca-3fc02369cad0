package com.facishare.bpm.bpmn;


import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.util.verifiy.handler.bean.CustomFunction;
import com.facishare.bpm.utils.JsonUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey;

/**
 * <AUTHOR>
 * @since 6.3
 */
@TypeName("executionTask")
public class ExecutionTaskExt extends ActivityExt {

    public static final String ITEM_LIST = "itemList";
    private transient List<ExecutionItem> itemList;

    public ExecutionTaskExt(Map map) {
        super(map);
        setExtensionType();
    }

    public List<ExecutionItem> getItemList() {
        if(itemList==null){
            itemList=ExecutionItem.from((List<Map>)get(ITEM_LIST));
        }
        return  itemList;
    }

    public Boolean getDelay() {
        if (Objects.isNull(this.get("delay"))) {
            return Boolean.FALSE;
        }
        return (Boolean) this.get("delay");
    }

    @Override
    public Map<String, Object> getExtension() {
        return (Map<String, Object>) get(ActivityKey.bpmExtension);
    }

    public void setExtensionType(){
        getExtension().put(ActivityKey.ExtensionKey.executionType, ExecutionTypeEnum.execution.name());
    }

    public WorkflowRule getRule() {
        Object ruleMap = get("rule");
        if (ruleMap != null) {
            return JsonUtil.fromJson(JsonUtil.toJson(ruleMap), WorkflowRule.class);
        }
        return null;
    }

    public CustomFunction getFunctionRule() {
        Object ruleMap = get("functionRule");
        if (ruleMap != null) {
            return JsonUtil.fromJson(JsonUtil.toJson(ruleMap), CustomFunction.class);
        }
        return null;
    }


}
