package com.facishare.bpm.bpmn;

import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date on 2018/4/24
 * @since 6.3
 *
 * 定义相关后动作配置
 */
@Data
public class ExecutionItem{
    private String taskType;
    private String sender;
    private Map<String, Set<Object>> recipients;
    private Set<String> emailAddress;
    private String title;
    private String content;
    private String template;
    private LinkedHashMap<String, LinkedHashMap<String, String>> fieldMapping;
    private String updateFieldJson;
    private Object updateFieldObject;
    private Map<String, Object> triggerParam;
    private String afterActionDefinitionId;
    private String afterActionMappingId;
    private Map<String, Object> actionMapping;
    private List<ActionParam> actionParams;
    private List customVariables;
    private Boolean useRelated;
    private String relatedObjectId;
    private String relatedEntityId;

    private ExecutionItem() {
    }

    public static ExecutionItem setType(String type){
        ExecutionItem item = new ExecutionItem();
        item.setTaskType(type);
        return item;
    }

    public static List<ExecutionItem> from(List<Map> itemList) {
        return itemList.stream().map(item -> {
            ExecutionItem temp = fromMap(item);

            return temp;
        }).collect(Collectors.toList());
    }

    public static ExecutionItem fromMap(Map item) {
        ExecutionItem temp = JsonUtil.fromJson(JsonUtil.toJson(item), ExecutionItem.class);
        return temp;
    }

}
