package com.facishare.bpm;

import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList;
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId;
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo;
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate;
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra;
import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions;
import com.facishare.bpm.model.resource.newmetadata.UpdateData;
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch;
import com.facishare.bpm.remote.model.org.*;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.element.plugin.api.FlowElement;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.model.ClientInfo;
import com.facishare.rest.core.model.RemoteContext;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

public interface RefServiceManager {

    Map findDataById(String entityId, String objectId);

    Object findDataById(String entityId, String objectId, String field, boolean includeDescribe);

    Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe);

    Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData,boolean skipRelevantTeam);

    Map findDataById(RemoteContext cnt, String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe);

    Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe);

    Map findDescribe(String entityId, boolean containAllFields, boolean includeStatistics);

    Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType);

    Map<String, Object> getFields(String entityId);

    FindDataBySearchTemplate.Result findDataBySearchTemplate(String apiName, SearchTemplateQuery query);

    Map<String, String> getSimpleEntityNames();

    Map<String, String> getSimpleEntityNamesBySocketConfig();

    Map<String, Object> getFieldDesc(String entityId, String field);

    String getFieldType(String entityId, String field);

    String getKey(String entityId, String objectId);

    String getDescDisplayName(String entityId);

    Map<String, String> getPaaSObjectNames(Collection<Pair<String, String>>
                                                   entityIdAndObjectIdList);

    String getPaaSObjectName(String entityId, String objectId);

    String getActionNameByActionCode(String entityId, String actionCode);

    Map<String, String> getRefEntityIdByEntityId(String entityId);

    Map<String, Object> findDataByIdWithEmptyMap(String entityId, String dataId);

    List<String> getDataOwner(String entityId, String id);

    boolean isDataOwnerByQueryMetadata(String apiName, String objectId);

    Map<String,List<String>> getDataOwners(String entityId, Set<String> ids);

    RemoteContext getContext();

    String getTenantId();

    String getUserId();

    RemoteContext getNotExistEAContext();

    List<String> getValue(boolean convert, String entityId, String objectId, List<String> extUserType, String instanceId, Map<String, Object> variables);

    List<Integer> getDeptIdsByUserId();

    List<String> getGroupByUserId();

    List getMembersByGroupIds(List groups);

    List<String> getMembersByDeptIds(List<Object> deptIds);

    List<String> getDeptLeaders(List<Object> deptIds);

    List<String> getDeptLeadersByUserIds(List<String> userIds);

    List<String> getLeaders(List<String> userIds);

    List<String> getCRMUserOfRoles(List<Object> roles);

    RemoteContext getAdminContext();

    boolean isAdmin();


    //  抛出异常 ,暂且保留原始业务逻辑,不做修改,后期再做优化
    Map<String, Object> getDescribe(String entityId);

    //  暂且保留原始业务逻辑,不做修改,后期再做优化
    Map<String, Map<String, Object>> getDescribes(Collection<String> entityIds);

    //  暂且保留原始业务逻辑,不做修改,后期再做优化
    boolean workflowIsExists(String tenantId, String outLineId,String sourceWorkflowId);

    boolean isFieldInactive(String entityId, String field);

    List<String> getRoleByUserId();

    Map<String, Object> getAreaOption(String fieldType);

    void validateAssignee(Set<Object> deptIds, Set<Object> groupIdList, Set<Object> userIds, Set<Object> roleCodes, Set<Object> deptLeader, String tipName);

    List<String> getEmployeesByReportingObjectId(String userId);

    Map<String, Role> getRoleByCode(List<String> roleList);

    Map<Integer, Employee> getMembersByIds(List personList);

    Map<String, CRMGroup> getGroupByIds(List crmGroup);

    Map<Integer, Department> getDeptByIDs(List deptIds);

    Set<String> getPersons(Map<String, Object> nextTaskAssigneeScope);


    Map<String, Dept> getDeptByDeptIds(List<String> deptIds);

    boolean getDeliveryNoteEnable();

    GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions();

    Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes);

    GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId);

    Map<String, Boolean> hasObjectFunctionPrivilege(String entityId);

    boolean dataPrivilege(String entityId, String objectId);

    String getAppId();

    ClientInfo getClientInfo();

    String getFsPeerDisplayName();

    Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName);

    Map<String, Object> findDataHandlePermissionsById(String entityId, String objectId);

    Map<String,String> getAppActions(String entityId, Boolean isExternalFlow,String appId,Integer appType);

    Map<String, Object> getFieldDesc(String entityId, String field, boolean includeCountFieldLookupType);

    Map<Integer, Employee> getExternalUserIdInfo(List externalRole);

    Map<String, Integer> getFieldPermissions(String entityId);

    boolean isOuterUserId();

    List<Long> getOuterMainOwner();

    Map<String, Employee> getEmployeeInfo(List<Object> userIds);

    /**
     * @return 如果是外部请求返回外部身份
     */
    String getUserIdWithOuterUserId();

    boolean isOuterMainOwner();

    List<String> getExternalRole(List externalRole, String lowerTenantId, String linkAppId, Integer linkAppType);

    List<GetOutRolesByTenantId.SimpleRoleResult> getRolesByAppId(String appId, int appType);

    GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId);

    Map<String, String> getPaaSObjectNames(String entityId, List<String> ids);

    Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids);

    /**
     * 通用的请求缓存池
     */
    <E> E getObjectFromCache(String key, Function<String,E> function);

    Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet);

    List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction);

    Map<String, String> findDataExhibitButton(String apiName, String objectId,  String usePageType);

    boolean checkEmailEnable(String sender);

    FlowElementWrapper getFlowElementWrapper(String elementApiName);

    FlowElement getFlowElement(String elementApiName);

    String getI18nLinkAppName(String linkApp, String linkAppName);

    boolean convertRuleIsEnable(String ruleName, String ruleApiName);

    Map<String, Object> getFlowConfig(String terminal, List<String> types);

    /**
     * 是否需要展示转发安妮
     */
    boolean isNeedDiscussButton();

    UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName);

    Map<String, Object> findRecordFieldMapping(String entityId, String recordType);

    Map<String, String> getDimensionObjDataList(RemoteContext context, Object query);

    boolean instanceListSkipDataPrivilege();
}
