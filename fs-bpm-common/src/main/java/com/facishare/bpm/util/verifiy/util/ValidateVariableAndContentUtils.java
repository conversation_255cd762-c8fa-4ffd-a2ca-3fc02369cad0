package com.facishare.bpm.util.verifiy.util;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMMetaDataException;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.bean.BPMFieldBean;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.bpmn.definition.model.CustomVariable;
import com.facishare.paas.I18N;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.common.StopWatch;
import com.google.common.base.Joiner;
import com.google.common.base.Objects;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.facishare.bpm.i18n.BPMI18N.PAAS_FLOW_BPM_NOT_EMPTY;
import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.*;

/**
 * <AUTHOR>
 * @Date on 2018/5/24
 * @since 6.3
 *@IgnoreI18n
 */
@Slf4j
public class ValidateVariableAndContentUtils {
    // 修改原因:原正则无法获取到${中文} 内部的中文  只能获取到 字母+数字的 ,防止前端传递表达式存在中文的情况,修改此表达式
    //private static final Pattern pattern = Pattern.compile("\\$\\{([#@a-zA-Z_\\d]+)\\}");
    private static String GETKEY_REGEX = "\\$\\{(.+?)\\}";
    private static final Pattern pattern = Pattern.compile(GETKEY_REGEX);

    /**
     * 校验消息内容中的引擎变量和数据变量是否是可用的
     * @param workflow
     * @param content
     * @param isPreActivity
     * @return
     */
    public static ValidateResult validateContent(Workflow workflow, String content, PreActivityFunction isPreActivity) {
        return validateContentWithOutShell(workflow, getVariables(content), isPreActivity);
    }

    public static ValidateResult validateContentOfNotNull(Workflow workflow, String content, PreActivityFunction isPreActivity) {
        if(Strings.isNullOrEmpty(content)){
            return ValidateResult.fail(I18N.text(PAAS_FLOW_BPM_NOT_EMPTY.key));
        }
        return validateContentWithOutShell(workflow, getVariables(content), isPreActivity);
    }

    public static ValidateResult validateContent(Workflow workflow, List<String> content, PreActivityFunction isPreActivity) {
        return validateContent(workflow, Joiner.on(",").join(content), isPreActivity);
    }

    public static ValidateResult validateContentWithOutShell(Workflow workflow, Collection<String> expressions, PreActivityFunction isPreActivity) {
        log.info("validateContentWithOutShell:expressions:{}", JsonUtil.toJson(expressions));

        try {
            RefServiceManager serviceManager = workflow.getServiceManager();
            List<BPMDefVariable> expressionBeans = getExpressionBeans(expressions);
            validateWorkflowEngineVariables(expressionBeans);
            validateCustomValiable(workflow,expressionBeans);
            validateFields(serviceManager, expressionBeans, isPreActivity);
        } catch (BPMWorkflowDefVerifyException e) {
            return ValidateResult.fail(e.getMessage());
        }

        return ValidateResult.ok();
    }


    public static List<BPMDefVariable> getExpressionBeans(Collection<String> expressions) {
        if(CollectionUtils.isEmpty(expressions)){
            return Lists.newArrayList();
        }
        List<BPMDefVariable> expressionBeans = Lists.newArrayList();
        for (String expression : expressions) {
            //这里的表达式都是适配 pattern正则的表达式 不需要校验中文
            if (isChinese(expression)) {
                log.error("表达式存在中文:{}", expression);
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TIPS_EXPRESSION_ERROR);
            }
            if (expression.contains(ENTITY_FIELD_SPLIT) && !CustomVariableUtil.isCustomVariable(expression)) {
                expressionBeans.add(new BPMDefVariable(DefVariableType.EntityObject, expression));
            } else if(CustomVariableUtil.isCustomVariable(expression)){
                expressionBeans.add(new BPMDefVariable(DefVariableType.CustomVariable, expression));
            }else{
                expressionBeans.add(new BPMDefVariable(DefVariableType.WorkflowEngine, expression));
            }
        }
        return expressionBeans;
    }

    /**
     * 大文本字符串,符合 ${}  的表达式依次判断是否是中文
     */
    public static boolean checkChinese(String content) {
        Pattern pattern = Pattern.compile(GETKEY_REGEX);
        Matcher matcher = pattern.matcher(content);
        String name;
        while (matcher.find()) {
            name = matcher.group(1);
            if (isChinese(name)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证字符是否是中文
     */
    private static boolean isChinese(String str) {
        String regEx = "[\u4e00-\u9fa5]";
        Pattern pat = Pattern.compile(regEx);
        Matcher matcher = pat.matcher(str);
        boolean flag = false;
        if (matcher.find()) {
            flag = true;
        }
        return flag;
    }

    private static void validateFields(RefServiceManager serviceManager, List<BPMDefVariable> expressions, PreActivityFunction isPreActivity) {
        log.info("validateFields:expressions:{}", JsonUtil.toJson(expressions));
        StopWatch stopWatch = StopWatch.createUnStarted("calculate field");
        List<String> tempExpressions = expressions.stream().filter(item -> item.getType() == DefVariableType.EntityObject).map(item -> item.expression).collect(Collectors.toList());
        Map<String, EntityBean> entityBeans = EntityBean.fromExpressions(tempExpressions);
        entityBeans.values().stream().filter(java.util.Objects::nonNull).forEach(item -> {
            item.getObjectIds().forEach(objectId -> {
                if (!isPreActivity.apply(objectId.activityId, item.getEntityId().split(BPMConstants.ENTITY_FIELD_SPLIT)[0], objectId.isSelf)) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_VARIABLE_ERROR);
                }
            });
        });
        stopWatch.lap("fromExpressions");
        log.info("validateContent{}", JsonUtil.toPrettyJson(entityBeans));
        Map<String, Map<String, Object>> descs = serviceManager.getDescribes(entityBeans.keySet());
        stopWatch.lap("getDescribes");
        entityBeans.values().forEach(entityBean -> {
            log.info("containAllFields:entityId:{} entityBean:{}", entityBean.getEntityId(), entityBean);
            if (CollectionUtils.isNotEmpty(entityBean.getFields())) {
                entityBean.containAllFields(descs.get(entityBean.getEntityId()));
            }
        });
        stopWatch.lap("containAllFields");
        stopWatch.logSlow(100);
    }

    private static void validateWorkflowEngineVariables(Collection<BPMDefVariable> expressions) {
        List<String> tempExpressions = expressions.stream().filter(expression -> expression.getType() == DefVariableType.WorkflowEngine).map(item -> item.expression).collect(Collectors.toList());
        for (String tempExpression : tempExpressions) {
            if (WorkflowEngineSupportVariablesUtils.isNotSupport(tempExpression)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_FLOW_VARIABLE_NOT_SUPPORT, tempExpression);
            }
        }

    }

    private static void validateCustomValiable(Workflow workflow,Collection<BPMDefVariable> expressions) {
        List<String> customVariablesKeys = workflow.getExecutableWorkflow().getCustomVariablesKeys();
        List<String> tempExpressions = expressions.stream().filter(expression -> expression.getType() == DefVariableType.CustomVariable).map(item -> item.expression).collect(Collectors.toList());
        for (String tempExpression : tempExpressions) {
            if (!customVariablesKeys.contains(tempExpression)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_APPROVAL_CUSTOM_VARIABLE_UNDEFINE, tempExpression);
            }
        }
        if(customVariablesKeys!=null && customVariablesKeys.size()!=Sets.newHashSet(customVariablesKeys).size()){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_APPROVAL_CUSTOM_IS_EXISTS);
        }
        List<String> errorLabels = getCustomVariableDefaultValueErrorLabels(workflow.getExecutableWorkflow().getCustomVariables());
        if(CollectionUtils.isNotEmpty(errorLabels)){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_APPROVAL_CUSTOM_VARIABLES_DEFAULT_VALUE_ERROR,Joiner.on(",").join(errorLabels));
        }
    }
    private static List<String> getCustomVariableDefaultValueErrorLabels(List<CustomVariable> customVariables) {
        return customVariables.stream().filter(item -> {
            return !item.validateDefaultValue();
        }).map(item -> {
            return item.getLabel();
        }).collect(Collectors.toList());
    }

    public static ValidateResult validateEntities(RefServiceManager serviceManager, List<String> entityIds, PreActivityFunction isPreActivity) {
        log.info("validateEntities:{}", entityIds);

        List<String> rst = entityIds.stream().map(item -> {
            ActivityObjectId entityId = getEntityId(item);
            isPreActivity.apply(entityId.getActivityId(), entityId.getObjectId().split(BPMConstants.ENTITY_FIELD_SPLIT)[0], entityId.isSelf);
            return entityId.getObjectId();
        }).collect(Collectors.toList());
        if (!rst.contains("")) {
            try {
                serviceManager.getDescribes(rst);
            } catch (BPMMetaDataException e) {
                return ValidateResult.fail(e.getMessage());
            }
        }
        return ValidateResult.ok();
    }

    /**
     * activity_id##AccountObj
     * activity_id##AccountObj##lookupId
     * activity_id##RefSelf##AccountObj
     * activity_id##RefSelf##AccountObj##lookupId
     */
    private static ActivityObjectId getEntityId(String item) {
        String[] temps = item.split(ENTITY_FIELD_SPLIT);

        switch (temps.length) {
            case 2:
                return new ActivityObjectId(temps[0], temps[1]);
            case 3:
                if (temps[1].equals(SELF_REF_KEY)) {
                    return new ActivityObjectId(temps[0], temps[2], true);
                } else {
                    return new ActivityObjectId(temps[0], temps[1] + ENTITY_FIELD_SPLIT + temps[2]);
                }
            case 4:
                if (temps[1].equals(SELF_REF_KEY)) {
                    return new ActivityObjectId(temps[0], temps[2] + ENTITY_FIELD_SPLIT + temps[3]);
                } else {
                    return new ActivityObjectId(temps[0], temps[1] + ENTITY_FIELD_SPLIT + temps[2]);
                }
            default:
                return null;
        }
    }

    /**
     * activity_id##AccountObj##field
     * activity_id##AccountObj##lookupId##field
     * activity_id##RefSelf##AccountObj##field
     * activity_id##RefSelf##AccountObj##lookupId##field
     *
     * @param content ${activity_id##AccountObj##field} ${activity_id##AccountObj##field2}
     * @return activity_id##AccountObj##field
     */
    public static List<String> getVariables(String content) {
        ArrayList<String> rst = Lists.newArrayList();
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            String expression = matcher.group(1);
            rst.add(expression);
        }
        return rst;
    }

    /**
     * 获取内部的key,如果包含$则获取内部的,如果不包含$则将值原样返回
     */
    public static String getInnerKeyByFirst(String content) {
        if (content.contains(BPMConstants.DOLLAR_SEPARATOR) &&
                (content.indexOf(BPMConstants.DOLLAR_SEPARATOR) == content.lastIndexOf(BPMConstants.DOLLAR_SEPARATOR))
                ) {
            String rst = "";
            List<String> ats = getVariables(content);
            if (ats.size() > 0) {
                rst = ats.get(0);
            }
            return rst;
        }
        return content;
    }

    /**
     * 校验关联类型的统计类型字段
     *
     * @param serviceManager
     */
    public static void validateFieldType(String expression, RefServiceManager serviceManager) {
        BPMFieldBean bpmFieldBean = BPMFieldBean.analysisFieldExpression(
                Splitter.on(UtilConstans.WELL)
                        .splitToList(ValidateVariableAndContentUtils.getInnerKeyByFirst(expression)));

        String entityId = bpmFieldBean.getEntityId();
        String field = bpmFieldBean.getMainField();
        //activity_1571280135972##result
        if (StringUtils.isBlank(entityId) || StringUtils.isBlank(field)) {
            return;
        }
        Map<String, Object> fieldDesc = serviceManager.getFieldDesc(entityId, field, Boolean.FALSE);
        if (StringUtils.isNotBlank(bpmFieldBean.getLookupField())) {
            entityId = (String) fieldDesc.get(BPMConstants.MetadataKey.targetApiName);
            field = bpmFieldBean.getLookupField();
            fieldDesc = serviceManager.getFieldDesc(entityId, field, Boolean.FALSE);
        }
        String type = (String) fieldDesc.get(BPMConstants.MetadataKey.type);
        if (BPMConstants.MetadataKey.COUNT.equals(type)) {
            if (BPMConstants.MetadataKey.objectReference.equals(fieldDesc.get(BPMConstants.MetadataKey.LOOKUP_FIELD_TYPE))) {
                String fieldLabel = (String) fieldDesc.get(BPMConstants.MetadataKey.label);
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_LOOK_UP_NONSUPPORT_COUNT, fieldLabel);
            }
        }
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class BPMDefVariable {
        private DefVariableType type;
        private String expression;
    }

    public enum DefVariableType {
        EntityObject("对象相关变量"), WorkflowEngine("流程引擎相关变量"),CustomVariable("流程变量");
        private String name;

        DefVariableType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    @Data
    public static class EntityBean {
        private String entityId;
        private Set<ActivityObjectId> objectIds = Sets.newHashSet();
        private Set<String> fields = Sets.newHashSet();

        public void containAllFields(Map<String, Object> desc) {
            Map<String, Object> fieldDescs = (Map<String, Object>) desc.get(BPMConstants.MetadataKey.fields);
            fields.forEach(item -> {
                if (OWNER_MAIN_DEPT_PATH.equals(item)) {
                    return;
                }
                Map<String, Object> fieldDesc = (Map<String, Object>) fieldDescs.get(item);
                if (fieldDesc != null) {
                    /**
                     * 元数据规定,若isActive为空,默认字段是isActive=true
                     */
                    Boolean isActive = fieldDesc.get(BPMConstants.MetadataKey.isActive) == null ? true : Boolean.parseBoolean(fieldDesc.get(BPMConstants.MetadataKey.isActive) + "");

                    if (!isActive) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_FIELD_DISABLE, desc.get(BPMConstants.MetadataKey.displayName), fieldDesc.get(BPMConstants.MetadataKey.label));
                    }

                } else {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_FIELD_DELETE, desc.get(BPMConstants.MetadataKey.displayName), item);
                }
            });
        }

        /**
         * activity_id##AccountObj##field
         * activity_id##AccountObj##lookupId##field
         * activity_id##RefSelf##AccountObj##field
         * activity_id##RefSelf##AccountObj##lookupId##field
         */
        static Map<String, EntityBean> fromExpressions(List<String> expressions) {
            Map<String, EntityBean> map = Maps.newConcurrentMap();
            for (String expression : expressions) {
                if (!expression.contains(ENTITY_FIELD_SPLIT)) {
                    return null;
                }
                String[] temps = expression.split(ENTITY_FIELD_SPLIT);

                String entityId, field;
                ActivityObjectId activityObjectId;
                switch (temps.length) {
                    case 2:
                        entityId = temps[1];
                        activityObjectId = new ActivityObjectId(temps[0].split("_")[1], null);
                        field = null;
                        break;
                    case 3:
                        entityId = temps[1];
                        activityObjectId = new ActivityObjectId(temps[0].split("_")[1], temps[0] + ENTITY_FIELD_SPLIT + temps[1]);
                        field = temps[2];
                        break;
                    case 4:
                        if (expression.contains(SELF_REF_KEY)) {
                            entityId = temps[2];
                            activityObjectId = new ActivityObjectId(temps[0].split("_")[1], temps[0] + ENTITY_FIELD_SPLIT + SELF_REF_KEY + ENTITY_FIELD_SPLIT + temps[2]);
                            field = temps[3];
                        } else {
//                            0             1            2        3
//                            activity_id##AccountObj##lookupId##field
                            entityId = temps[1] + ENTITY_FIELD_SPLIT + temps[2];
                            activityObjectId = new ActivityObjectId(temps[0].split("_")[1], temps[0] + ENTITY_FIELD_SPLIT + temps[1] + ENTITY_FIELD_SPLIT + temps[2]);
                            field = temps[3];
                        }
                        break;
                    case 5:
//                           0           1       2             3        4
//                        activity_id##RefSelf##AccountObj##lookupId##field
                        entityId = temps[2] + ENTITY_FIELD_SPLIT + temps[3];
                        activityObjectId = new ActivityObjectId(temps[0].split("_")[1], temps[0] + ENTITY_FIELD_SPLIT + temps[1] + ENTITY_FIELD_SPLIT + temps[2] + ENTITY_FIELD_SPLIT + temps[3]);
                        field = temps[4];
                        break;
                    default:
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NOT_SUPPORT_VARIABLE,expression);
                }
                EntityBean entityBean = map.getOrDefault(entityId, new EntityBean());
                entityBean.setEntityId(entityId);
                entityBean.getObjectIds().add(activityObjectId);
                entityBean.addField(field);
                map.put(entityBean.getEntityId(), entityBean);
            }
            return map;
        }

        private void addField(String field) {
            if (StringUtils.isNotBlank(field)) {
                this.getFields().add(field);
            }
        }
    }

    @Data
    public static class ActivityObjectId {
        String activityId;
        String objectId;
        boolean isSelf = false;

        public ActivityObjectId(String activityId, String objectId) {
            this.activityId = activityId;
            this.objectId = objectId;
        }

        public ActivityObjectId(String activityId, String objectId, boolean isSelf) {
            this.activityId = activityId;
            this.objectId = objectId;
            this.isSelf = isSelf;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof ActivityObjectId)) {
                return false;
            }
            ActivityObjectId that = (ActivityObjectId) o;
            return Objects.equal(activityId, that.activityId) &&
                    Objects.equal(objectId, that.objectId) && Objects.equal(isSelf, that.isSelf);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(activityId, objectId, isSelf);
        }

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("[");
            sb.append("activityId:'").append(activityId);
            sb.append(", objectId:'").append(objectId);
            sb.append(']');
            return sb.toString();
        }
    }
}
