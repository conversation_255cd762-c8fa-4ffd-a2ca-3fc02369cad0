package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.EndEventExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.TypeName;
import com.facishare.bpm.exception.BPMOrgException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.AfterActionType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @since 6.2
 */
@Slf4j
public enum AfterActionValidateManager {
    send_email(new SendEmailValidateActionHandler()),
    trigger_bpm(new TriggerBpmValidateActionHandler()),
    external_message( new ExternalMessageValidateActionHandler()),
    external_message_v2(new ExternalMessageV2ValidateActionHandler()),
    custom_function( new CustomFunctionValidateActionHandler()),
    feed_task( new FeedTaskValidateActionHandler()),
    feed_schedule( new FeedScheduleValidateActionHandler()),
    feed_sales_record( new FeedSalesRecordValidateActionHandler()),
    send_qixin( new SendCRMMessageValidateActionHandler()),
    updates(new UpdateValidateActionHandler()),
    remind(new UserTaskRemindValidateHandler()),
    timeoutAction( new TimeoutExecutionValidateHandler()),
    trigger_operation( new OperationValidateActionHandler()),
    send_sms( new SendSMSValidateActionHandler()),
    edit_team_member( new EditTeamMemberValidateActionHandler()),
    custom_variable_evaluation( new CustomVariablesValidateActionHandler()),
    execute_convert_rule(new ExecuteConvertRuleValidateActionHandler()),
    ;

    private String name;
    private ValidateActionHandler validateActionHandler;

    AfterActionValidateManager(ValidateActionHandler validateActionHandler) {
        this.name = AfterActionType.valueOf(this.name()).getDesc();
        this.validateActionHandler = validateActionHandler;
    }

    public static ValidateResult validate(Workflow workflow) {
        StringBuilder sb = new StringBuilder();

        workflow.getUserTasks().forEach(item -> {
            //后动作
            item.getExecution().forEach((key, value) -> value.forEach(action -> {
                ValidateResult rst = validate(item, action, workflow);
                if (!rst.isValid()) {
                    sb.append(rst.getMessage()).append("\n");
                }
            }));
            // 如果节点上的后动作没有异常
            if (sb.length() == 0) {
                //则校验下超时提醒
                ValidateResult rst = validate(item, ExecutionItem.setType(AfterActionValidateManager.remind.name()), workflow);
                if (!rst.isValid()) {
                    sb.append(rst.getMessage());
                }

                //校验下超时执行后动作
                rst = validate(item, ExecutionItem.setType(AfterActionValidateManager.timeoutAction.name()), workflow);
                if (!rst.isValid()) {
                    sb.append(rst.getMessage());
                }
            }
            return;
        });

        //自动节点
        workflow.getExecutionTasks().forEach(item -> {
            item.getItemList().forEach(action -> {
                ValidateResult rst = validate(item, action, workflow);
                if (!rst.isValid()) {
                    sb.append(rst.getMessage());
                }
            });
            return;
        });

        //终止实例后动作校验
        workflow.getExecution().forEach((key, value) ->
                value.forEach(action -> {
                    EndEventExt endEventExt = new EndEventExt();
                    endEventExt.setName(BPMI18N.PAAS_FLOW_BPM_END_EVENT_TERMINATE_PROCESS.text());
                    ValidateResult rst = validate(endEventExt, action, workflow);
                    if (!rst.isValid()) {
                        sb.append(rst.getMessage());
                    }
                }));

        if (sb.length() > 0) {
            return ValidateResult.fail(sb.toString());
        }
        return ValidateResult.ok();
    }

    static List<String> ignoreAfterAction = Lists.newArrayList("feedback_customer_update", "feedback_requirement_feed", "feedback_issue_feed", "feedback_add_bug");

    public static ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        if (activity != null) {
            log.info("validate:activityType:{},activityName:{},actionType:{}", activity.getClass().getAnnotation(TypeName.class).value(), ActivityExt.getActivityName(activity), executionItem.getTaskType());
        }
        try {
            String executionItemType = executionItem.getTaskType();
            if (ignoreAfterAction.contains(executionItemType)) {
                return ValidateResult.ok();
            }
            return AfterActionValidateManager.valueOf(executionItemType).validateActionHandler.validate(activity, executionItem, workflow);
        } catch (BPMOrgException e) {
            log.error("", e);
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_ORG_EXCEPTION.text(ActivityExt.getActivityName(activity), executionItem.getTaskType(), e.getMessage()));
        } catch (IllegalArgumentException e) {
            log.error("", e);
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_AFTER_SETTING_ERROR.text(executionItem.getTaskType()));
        }
    }

    public String getName() {
        return name;
    }
}
