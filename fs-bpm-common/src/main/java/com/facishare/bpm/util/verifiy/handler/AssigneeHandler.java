package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.bpmn.WorkflowRule;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.after.CustomFunctionValidateActionHandler;
import com.facishare.bpm.util.verifiy.handler.bean.CustomFunction;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.bpm.utils.JsonUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.facishare.bpm.util.verifiy.handler.RuleHandler.validateConditionOperator;

/**
 * 2 校验节点处理人
 *
 * <AUTHOR>
 * @date 17/3/15
 */
@Slf4j
public class AssigneeHandler implements ValidateHandler {

    @Override
    public void validate(Workflow workflow) {
        if (!workflow.isExternalFlow()) {
            List<UserTaskExt> userTasks = workflow.getUserTasks();
            userTasks.stream().filter(userTask-> {
                // 880 自定义的元素可能会出现自定义节点处理人逻辑的情况， 这里下面只校验，非自定义处理人节点
                return !userTask.isCustomCandidateConfig();
            }).forEach(userTask -> {
                ExecutionTypeEnum extensionType = userTask.getExtensionType();
                if (!extensionType.equals(ExecutionTypeEnum.externalApplyTask)) {

                    String taskName = userTask.getName();
                    boolean hasAssignee = Objects.nonNull(userTask.get(WorkflowKey.ActivityKey.assignee))
                            && userTask.get(WorkflowKey.ActivityKey.assignee) instanceof Map
                            && MapUtils.isNotEmpty(((Map)userTask.get(WorkflowKey.ActivityKey.assignee)));
                    boolean hasGroupHandler = Objects.nonNull(userTask.get(WorkflowKey.ActivityKey.groupHandler))
                            && userTask.get(WorkflowKey.ActivityKey.groupHandler) instanceof List
                            && CollectionUtils.isNotEmpty(((List)userTask.get(WorkflowKey.ActivityKey.groupHandler)));
                    if(hasAssignee && hasGroupHandler){
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_ASSIGNEE_AND_GROUPHANDLER_COEXIST, userTask.getName());
                    }
                    if(Objects.isNull(userTask.get(WorkflowKey.ActivityKey.assigneeType)) || userTask.get(WorkflowKey.ActivityKey.assigneeType).equals(WorkflowKey.ActivityKey.assignee)){
                        if(hasAssignee){
                            if (!UserTaskExt.hasAssignee(userTask.getAssignee())) {
                                log.error("{} 未设置审批人", taskName);
                                //throw new BPMWorkflowDefVerifyException(taskName + " 节点未设置处理人");
                                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_ASSIGNEEID_NOT_SET);
                            }
                            validateAssignee(userTask.getName() + BPMI18N.PAAS_FLOW_BPM_ASSIGNEE_ID.text(), userTask, userTask.getAssignee(), workflow);
                        }else {
                            if(Objects.isNull(userTask.get(WorkflowKey.ActivityKey.groupHandler))){
                                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_ASSIGNEEID_NOT_SET);
                            }
                            validateGroupHandler(workflow, userTask.getGroupHandler(), userTask);
                        }
                    }else if (userTask.get(WorkflowKey.ActivityKey.assigneeType).equals(WorkflowKey.ActivityKey.assigneeFunction)){
                        CustomFunction assigneeFunction = userTask.getAssigneeFunction();
                        if(Objects.isNull(assigneeFunction)){
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_ASSIGNEEID_NOT_SET);
                        }
                        ValidateResult functionValidate = CustomFunctionValidateActionHandler.validateFunction(userTask, workflow, assigneeFunction);
                        if(!functionValidate.isValid()){
                            throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ASSIGNEE_FUNCTION_ERROR.getCode(), functionValidate.getMessage());
                        }
                    }else {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_ASSIGNEEID_NOT_SET);
                    }
                }
            });
        }
    }

    private static final Set<String> assigneeKeys = Sets.newHashSet("role", "person", "group", "dept", "dept_leader", "emails", "ext_bpm", "extUserType", "ext_process", "external_role","approval_role");
    private static final Pattern pattern = Pattern.compile("^\\$\\{([#@a-zA-Z_\\d]+)\\}$");
    private static final List<String> dataRelatedWhite = Lists.newArrayList("owner", "leader", "dept_leader", "group_leader", "group_dept_leader", "group","outer_data_group", "group_dept", "group_role", "group_group", "out_owner_dept_leader");

    private static final String DATA_OWNER_MAIN_DEPT_FIELD_PREFIX = "dept_field";
    public static void validateAssignee(String tipName, ActivityExt activityExt, Map<String, Set<Object>> assignees, Workflow workflow) {
        if (MapUtils.isEmpty(assignees)) {
            return;
        }
        Set<String> keys = assignees.keySet();
        for (String key : keys) {
            if (!assigneeKeys.contains(key)) {
                // throw new BPMWorkflowDefVerifyException(tipName + " 不支持 " + key + "类型");
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NOT_SUPPORTED,tipName,key);
            }
            switch (key) {
                case "ext_process":
                case "ext_bpm":
                    assignees.get(key).forEach(value -> {
                        if (!validateExtBPMSupport(value + "", activityExt, workflow)) {
                            //throw new BPMWorkflowDefVerifyException(tipName + " 数据相关 或 流程变量 配置有误");
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_DATA_RELATED_OR_VARIABLE_CONFIG_ERROR,tipName);
                        }
                    });
                    break;
                case "external_role":
                    List<GetOutRolesByTenantId.SimpleRoleResult> externalRoles = Lists.newArrayList();
                    try {
                        externalRoles = workflow.getServiceManager().getRolesByAppId(StringUtils.isNotBlank(activityExt.getLinkApp()) ? activityExt.getLinkApp() : workflow.getLinKApp()
                                , Objects.nonNull(activityExt.getLinkAppType()) ? activityExt.getLinkAppType() : workflow.getLinKAppType());
                    } catch (Throwable e) {
                        log.warn("getRolesByAppId error", e);
                    }
                    if (Collections.disjoint(assignees.get(key), externalRoles.stream().map(GetOutRolesByTenantId.SimpleRoleResult::getRoleId).collect(Collectors.toList()))) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_PERSON_PROP_CONFIG_ERROR, tipName);
                    }
                    break;
                case "extUserType":
                    ValidateResult rst = ValidateVariableAndContentUtils.validateContent(workflow,
                            assignees.get(key).stream().map(item -> {
                                String expression = StringUtils.trim(item + "");
                                if (!pattern.matcher(expression).find()) {
                                    //throw new BPMWorkflowDefVerifyException(tipName + " 人员对象属性变量配置不正确");
                                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_PERSON_PROP_CONFIG_ERROR,tipName);
                                }
                                return expression;
                            }).collect(Collectors.toList())
                            , workflow.getPreActivityFun(activityExt));
                    if (rst.isNotValid()) {
                        //throw new BPMWorkflowDefVerifyException(tipName + " 相关对象的人员字段配置中 " + rst.getMessage());
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_RELATED_OBJECT_PERSON_CONFIG_ERROR,tipName,rst.getMessage());
                    }
                    break;
                case "emails":
                    rst = ValidateVariableAndContentUtils.validateContent(workflow,
                            assignees.get(key).stream().map(item -> {
                                String expression = StringUtils.trim(item + "");
                                if (!pattern.matcher(expression).find()) {
                                    //throw new BPMWorkflowDefVerifyException(tipName + " 对象属性邮件类型字段变量配置不正确");
                                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_OBJECT_EMAIL_FIELD_EXPRESSION_CONFIG_ERROR,tipName);
                                }
                                return expression;
                            }).collect(Collectors.toList())
                            , workflow.getPreActivityFun(activityExt));
                    if (rst.isNotValid()) {
                       // throw new BPMWorkflowDefVerifyException(tipName + " 相关对象的 邮件字段 配置中 " + rst.getMessage());
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_RELATED_OBJECT_EMAIL_CONFIG_ERROR,tipName,rst.getMessage());
                    }
                    break;
                case "dept":
                case "dept_leader":
                case "group":
                case "person":
                case "role":
                    validateAssignee(assignees, workflow.getServiceManager(), tipName);
                    break;
                default:
                    break;
            }
        }
    }


    public static void validateAssignee(Map<String, Set<Object>> assignees, RefServiceManager serviceManager, String tipName) {


        //部门和部门负责人存的都是部门的id
        Set<Object> dept = assignees.get("dept");
        Set<Object> deptLeader = assignees.get("dept_leader");
        Set<Object> groups =  assignees.get("group");
        Set<Object> persons =  assignees.get("person");
        Set<Object> role =  assignees.get("role");

        //将部门进行汇总
        Set<Object> deptAll = Sets.newHashSet();
        if(CollectionUtils.isNotEmpty(dept)){
            deptAll.addAll(dept);
        }
        if(CollectionUtils.isNotEmpty(deptLeader)){
            deptAll.addAll(deptLeader);
        }

        serviceManager.validateAssignee(deptAll, groups, persons, role, deptLeader, tipName);
    }

    /**
     * ${instance##owner}
     * "activity_1526626713317##object_jw9Lf__c##field_dids__c##owner$$数据相关人员-数据负责人"
     * "activity_1526626713317##object_jw9Lf__c##field_dids__c##leader$$数据相关人员-****"
     * "activity_1526626713317##object_jw9Lf__cfield_dids__c##dept_leader$$数据相关人员-****"
     * "activity_1526626713317##object_jw9Lf__c##field_dids__c##group_leader$$数据相关人员-****"
     * "activity_1526626713317##object_jw9Lf__c##field_dids__c##group_dept_leader$$数据相关人员-****"
     * "activity_1526626713317##object_jw9Lf__c##field_dids__c##group$$数据相关人员-****"
     * "activity_1526626713317##object_jw9Lf__c##owner"
     * activity_1526626713317##object_jw9Lf__c##outer_data_group
     * <p>
     * 以上为正确格式
     */
    private static boolean validateExtBPMSupport(String at, ActivityExt currentActivity, Workflow workflow) {

        if (at.contains("instance##owner")||at.contains("applicant##dept_leader") || at.contains("applicant##leader")) {
            return true;
        } else if (at.contains("##assigneeId##leader") || at.contains("##assigneeId##dept_leader") || at.contains("##assigneeId") || at.contains("##processorId##leader") || at.contains("##processorId")) {//某节点处理人leader
            //节点处理人上级,节点处理人,节点执行人,节点执行人上级,节点处理人部门负责人
            at = ValidateVariableAndContentUtils.getInnerKeyByFirst(at);
            String[] atTemp = at.split(BPMConstants.ENTITY_FIELD_SPLIT);
            String activityId = atTemp[0].split("_")[1];
            return workflow.getPreActivityFun(currentActivity).apply(activityId, null, false);
        } else if (at.contains("##owner") || at.contains("##leader") || at.contains("##dept_leader") || at.contains("##dept_field") || at.contains("##group_leader")
                || at.contains("##group_dept_leader") || at.contains("##group") || at.contains("##outer_data_group")
                || at.contains("##group_dept") || at.contains("##group_role") || at.contains("##group_group") || at.contains("##out_owner_dept_leader")) {//数据组成员
            ExpressionModel expressionModel = ExpressionModel.getExpressionModel(at);

            // 验证assignee中对象相关变量的异常
            if (!checkAssigneeItSelfField(expressionModel, workflow)) {
                return false;
            }

            return workflow.getPreActivityFun(currentActivity).apply(expressionModel.getActivityId(), expressionModel.getEntityId(), false);
        }
        return false;
    }

    private static boolean checkAssigneeItSelfField(ExpressionModel atTemp, Workflow workflow) {
        Map<String, Object> describeMaps = workflow.getServiceManager().getDescribe(atTemp.getEntityId());
        if (MapUtils.isEmpty(describeMaps)) {
            log.error("{} describe not found", atTemp.getEntityId());
            return false;
        }

        //activity_1526626713317##object_jw9Lf__c##field_dids__c##group$$数据相关人员
        //activity_1526626713317##object_jw9Lf__c##group$$数据相关人员
        // 判断字段是否在白名单中
        if (dataRelatedWhite.contains(atTemp.getField())) {
            return true;
        }

        if(atTemp.getField().contains(DATA_OWNER_MAIN_DEPT_FIELD_PREFIX)) {
            return true;
        }

        Map<String, Object> fieldsDesc = (Map<String, Object>) describeMaps.get(BPMConstants.MetadataKey.fields);

        Map<String, Object> ifieldDesc = (Map<String, Object>) fieldsDesc.get(atTemp.getField());
        if (MapUtils.isEmpty(ifieldDesc)) {
            log.error("{} field not found", atTemp.getField());
            return false;
        }

        return true;
    }

    @Data
    public static class ExpressionModel {
        private String activityId;
        private String entityId;
        private String field;

        public static ExpressionModel getExpressionModel(String at) {
            //String[] atTemp=at.split(BPMConstants.ENTITY_FIELD_SPLIT);
            //String activityId=atTemp[0].split("_")[1];
            //String entityId=atTemp[1];

            //activity_1526626713317##object_jw9Lf__c##field_dids__c##group$$数据相关人员-**** => {activity_1526626713317,object_jw9Lf__c,field_dids__c,group$$数据相关人员-****}
            //activity_1526626713317##object_jw9Lf__c##owner => {activity_1526626713317,object_jw9Lf__c,owner$$数据相关人员-****}
            //activity_1526626713317##object_jw9Lf__c##dept=> {activity_1526626713317,object_jw9Lf__c,dept$$数据相关人员-****}
            //activity_1543313205151##SelfRef##object_Xp51d__c##owner
            //activity_1543313205151##SelfRef##object_Xp51d__c##owner$$数据负责人
            List<String> atTemp = Splitter.on(BPMConstants.ENTITY_FIELD_SPLIT).splitToList(ValidateVariableAndContentUtils.getInnerKeyByFirst(at));
            if (atTemp.size() <= 2) {
                log.error("解析:{}人员变量异常", at);
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_RESOLVE_STAFF_VARIABLE_EXCEPTION);
            }
            //activity_1526626713317 => {activity,1526626713317}
            List<String> atActivityId = Splitter.on(BPMConstants.UNDERLINE_FIELD_SPLIT).splitToList(atTemp.get(0));
            String activityId = atActivityId.get(1);

            ExpressionModel model = new ExpressionModel();
            model.setActivityId(activityId);

            // 存在自关联的情况的时候,需要特殊处理下
            String entityId = atTemp.get(1);
            String field = Splitter.on("$$").splitToList(atTemp.get(2)).get(0);
            if (BPMConstants.SELF_REF_KEY.equals(entityId)) {
                entityId = atTemp.get(2);
                field = Splitter.on("$$").splitToList(atTemp.get(3)).get(0);
            }
            model.setEntityId(entityId);
            model.setField(field);

            return model;
        }
    }

    public static void validateGroupHandler(Workflow workflow, List<Map<String, Object>> groupHandler, UserTaskExt userTask){
        if(CollectionUtils.isEmpty(groupHandler)){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ERROR, userTask.getName());
        }
        boolean hasDefault = false;
        for (Map<String, Object> handler : groupHandler) {
            if(MapUtils.isEmpty(handler)){
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ERROR, userTask.getName());
            }
            if(!handler.containsKey("order") || !(handler.get("order") instanceof Double)){
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ORDER_ERROR, userTask.getName());
            }
            hasDefault = hasDefault || Boolean.TRUE.equals(handler.get("defaultConfig"));
            if(groupHandler.size() > 1){
                if(!(handler.get("rule") instanceof Map)){
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_ERROR_EXCEPTION, handler.get("order"), userTask.getName());
                }
                WorkflowRule rule = WorkflowRule.convertFromJson(JsonUtil.toJson(handler.get("rule")));
                ValidateResult operatorRes = validateConditionOperator(workflow,rule.getConditions());
                if (!operatorRes.isValid()) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_ERROR, handler.get("order"), operatorRes.getMessage(), userTask.getName());
                }
                ValidateResult contentRes = ValidateVariableAndContentUtils.validateContent(workflow, rule.getAllFields(), workflow.getPreActivityFun(userTask));
                if(!contentRes.isValid()){
                    //xx 的第x个分组的分组条件 xxxxxx
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_RULE_CONTENT_ERROR_EXCEPTION, userTask.getName(), (((Double) handler.get("order")).intValue()+1), contentRes.getMessage());
                }
            }
            if(!(handler.get("assignee") instanceof Map)){
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ASSIGNEE_ERROR,  handler.get("order"), userTask.getName());
            }
            validateAssignee(BPMI18N.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_ASSIGNEE_ERROR.text(handler.get("order"), userTask.getName()), userTask, userTask.getAssignee(), workflow);
        }
        if(!hasDefault){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_GROUPHANDLER_NOT_DEFAULT_RULE, userTask.getName());
        }
    }
}
