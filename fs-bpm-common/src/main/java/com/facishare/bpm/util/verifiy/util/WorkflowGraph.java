package com.facishare.bpm.util.verifiy.util;

import com.facishare.bpm.bpmn.*;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.Bag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.bag.HashBag;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class WorkflowGraph {
    private ExecutableWorkflowExt workflowExt;
    private Graph graph;
    private Map<Integer, Set<Integer>> preNodes;
    private Map<String, List<ActivityExt>> preActivities;

    public WorkflowGraph(ExecutableWorkflowExt workflowExt) {
        this.workflowExt = workflowExt;
        graph = new Graph(workflowExt);
        preNodes = Maps.newHashMap();
        init();
    }

    private void init() {
        long start = System.currentTimeMillis();
        for (int i : graph.getV()) {
            preNodes.put(i, Sets.newHashSet());
        }
        /**
         * 初始化前置节点
         */
        initPreNodes(graph.getStart());
        /**
         * 此处不关心是否必走,只关心其节点一定在前面
         * updatePreNodes是为了解决下面场景
         *                     ---userTask---
         *                    |              |
         * start--userTask--x |---userTask------userTask(此节点的计算)--userTask(若算此节点时就用到了下面这个方法)--end
         *                    |              |
         *                     ---userTask---
         */
        updatePreNodes();
        log.info("init:{}ms,count:{}", (System.currentTimeMillis() - start), count.get());
    }

    /**
     * 补充多个入线的情况
     */
    private void updatePreNodes() {
        preNodes.forEach((key, value) -> {
            Set<Integer> newPre = Sets.newHashSet();
            value.forEach(item -> {
                newPre.add(item);
                newPre.addAll(preNodes.get(item));
                count.incrementAndGet();
            });
            preNodes.put(key, newPre);
        });
    }

    public final AtomicLong count = new AtomicLong();

    private void initPreNodes(Integer from) {
        Bag ids = graph.getTo(from);
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(item -> {
                if (!preNodes.get(from).contains(item)) {
                    Set<Integer> temp = preNodes.get(item);
                    boolean isHasValue = temp.size() > 0;
                    count.incrementAndGet();
                    temp.add(from);
                    temp.addAll(preNodes.get(from));
                    if (!isHasValue) {
                        initPreNodes((Integer) item);
                    }
                }
            });
        }
    }

    /**
     * 获取前置节点
     * @return
     */
    public Map<String, List<ActivityExt>> getPreActivityMap() {
        if (MapUtils.isEmpty(preActivities)) {
            preActivities = preNodes.keySet().parallelStream().collect(Collectors.toMap(
                    item -> graph.getActivityId(item),
                    item -> {
                        preNodes.get(item).add(item);
                        return preNodes.get(item).parallelStream().map(
                                temp -> workflowExt.getActivityMaps().get(graph.getActivityId(temp))
                        ).collect(Collectors.toList());
                    }));
        }
        return preActivities;
    }

    public List<String> notContainStartActivities() {
        return Arrays.stream(graph.getV()).filter(v -> !preNodes.keySet().contains(v))
                .boxed().map(item -> workflowExt.getActivityMaps()
                        .get(graph.getActivityId(item)).getName())
                .collect(Collectors.toList());
    }

    private List<String> hasPassActivityIds= Lists.newArrayList();

    public boolean toExecutionTaskNoUserTask(String executionActivityId){
        hasPassActivityIds.clear();
        return toExecutionTaskNoUserTask(graph.getStartId(),executionActivityId);
    }

    private boolean toExecutionTaskNoUserTask(String preActivity,String executionActivityId) {
        if(count.get()>10000){
            return false;
        }
        List<ActivityExt> activities = getTo(preActivity);
        boolean rst=false;
        for (ActivityExt activity : activities) {
            count.incrementAndGet();
            if(activity.getId().equals(executionActivityId)){
                return true;
            }
            if (!activity.instanceOf(UserTaskExt.class)&&!hasPassActivityIds.contains(activity.getId())) {
                hasPassActivityIds.add(activity.getId());
                rst= toExecutionTaskNoUserTask(activity.getId(),executionActivityId);
            }
            if(rst){
                return rst;
            }
        }
        return false;
    }

    private List<ActivityExt> getTo(String activityId) {
        if(getActivityMap().get(activityId).instanceOf(EndEventExt.class)){
            return Lists.newArrayList();
        }
        return Arrays.stream(graph.getTo(graph.getActivities2v().get(activityId)).toArray())
                .map(item -> workflowExt.getActivityMaps().get(graph.v2Activities[(int) item]))
                .collect(Collectors.toList());
    }

    private void print() {
        getPreActivityMap().forEach((key, value) -> {
            log.info("{}:{}", workflowExt.getActivityMaps().get(key).getName(), value.stream().map(item -> item.getName()).collect(Collectors.toList()));
        });
    }

    public Map<String, ActivityExt> getActivityMap() {
        return workflowExt.getActivityMaps();
    }

    @Data
    public static class Graph {

        private int[] v;
        private int e;
        private Bag[] adj;
        private String[] v2Activities;
        private Map<String, Integer> activities2v;
        private ExecutableWorkflowExt workflowExt;
        private int start;

        public Graph(ExecutableWorkflowExt workflow) {
            log.info("graph worklowId:{}", workflow.getId());
            long startTime = System.currentTimeMillis();
            this.workflowExt = workflow;
            List<ActivityExt> activities = workflow.getActivities();
            v = new int[activities.size()];
            v2Activities = new String[v.length];
            activities2v = Maps.newHashMap();
            adj = new HashBag[activities.size()];
            for (int i = 0; i < activities.size(); i++) {
                v2Activities[i] = activities.get(i).getId();
                activities2v.put(v2Activities[i], i);
                v[i] = i;
                if ("startEvent".equals(activities.get(i).getType())) {
                    start = i;
                }
            }
            for (TransitionExt transition : workflow.getTransitions()) {
                String from = transition.getFromId();
                String to = transition.getToId();
                Integer fromActivity2V = activities2v.get(from);
                if (fromActivity2V == null) {
                    log.error("tenantId:{},sourceWorkflowId:{},fromId:{} 在 activities 中不存在", workflow.get("tenantId"), workflow.getSourceWorkflowId(), from);
                }
                Integer toActivity2V = activities2v.get(to);
                if (toActivity2V == null) {
                    log.error("tenantId:{},sourceWorkflowId:{},toId:{} 在 activities 中不存在", workflow.get("tenantId"), workflow.getSourceWorkflowId(), to);
                }
                //都有可能出现空指针,但是不能做为业务异常来处理 出现此问题需要彻查
                addEdge(fromActivity2V, toActivity2V);
            }
            log.info("Graph.init:workflowName:{},activityIds:{},transitions:{},time:{}ms", workflow.getName(), activities.size(), workflow.getTransitions().size(), (System.currentTimeMillis() - startTime));
        }

        private void addEdge(int v, int w) {
            if (adj[v] == null) {
                adj[v] = new HashBag();
            }
            adj[v].add(w);
            e++;
        }

        public Bag getTo(Integer v) {
            return adj[v];
        }


        private List<String> getStartTo() {
            return Arrays.stream(getTo(start).toArray()).map(item -> v2Activities[(int) item]).collect(Collectors.toList());
        }

        private int[] getV() {
            return v;
        }

        private int getE() {
            return e;
        }

        private Bag[] getAdj() {
            return adj;
        }

        public Integer getStart() {
            return start;
        }
        public String getStartId(){
            return v2Activities[start];
        }
        public void print() {
            for (int i = 0; i < adj.length; i++) {
                log.info("from {} --> {} -->type:{}", i, JsonUtil.toJson(adj[i]), workflowExt.getActivityMaps().get(v2Activities[i]).getType());
            }
        }

        public String getActivityId(Integer item) {
            return v2Activities[item];
        }


    }
}