package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;

/**
 * <AUTHOR>
 * @Date on 2018/5/3
 * @since 6.3
 */
public class UserTaskApproveValidateHandler implements ValidateActivityHandler {
    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        return  ValidateResult.ok();
    }
}
