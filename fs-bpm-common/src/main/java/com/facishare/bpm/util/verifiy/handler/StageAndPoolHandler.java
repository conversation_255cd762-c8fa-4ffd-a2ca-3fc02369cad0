package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date on 2018/5/17
 * @since 6.3
 */
public class StageAndPoolHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        Workflow.WorkflowExtension extension = workflow.getExtension();
        if(extension==null){
            //throw new BPMWorkflowDefVerifyException("阶段名称不能为空");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_STAGE_NAME_ISNULL);
        }
        List<Workflow.PoolEntity> pools = extension.getPools();
        if(CollectionUtils.isNotEmpty(pools)){
            Boolean lanesHasOrder = null;
            for (Workflow.PoolEntity pool : pools) {
                List<Workflow.LaneEntity> lanes = pool.getLanes();
                if(CollectionUtils.isNotEmpty(lanes)){
                    for (Workflow.LaneEntity lane : lanes) {
                        if(StringUtils.isBlank(lane.getName())){
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_STAGE_NAME_ISNULL);
                        }
                        if(Objects.isNull(lanesHasOrder)){
                            lanesHasOrder = Objects.nonNull(lane.getOrder());
                            continue;
                        }
                        if(Objects.nonNull(lane.getOrder()) != lanesHasOrder){
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_STAGE_ORDER_ERROR);
                        }
                    }
                }
            }
        }else{
            //throw new BPMWorkflowDefVerifyException("泳道不能为空");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_POOL_IS_EMPTY);
        }
    }
}
