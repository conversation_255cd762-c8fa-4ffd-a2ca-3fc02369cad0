package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SendSMSValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        SendSMS sendSMS = JsonUtil.fromJson(JsonUtil.toJson(executionItem.getActionMapping()), SendSMS.class);
        String activityName = getActivityName(activity);
        // 短信模板为空 && content 为空
        if (Strings.isNullOrEmpty(sendSMS.getTemplate()) && Strings.isNullOrEmpty(sendSMS.getContent())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SMS_CONTENT_NOT_EMPTY.text(activityName));
        }
        List<String> variables = Lists.newArrayList();
        //手机号相关变量
        List<String> objectPhoneProperties = sendSMS.getObjectPhoneProperties();
        //插入变量
        List<Param> params = sendSMS.getParams();
        List<String> phoneNumbers = sendSMS.getPhoneNumbers();
        Map<String, Set<Object>> recipients = sendSMS.getRecipients();

        //接收的手机号不为空
        if (CollectionUtils.isEmpty(objectPhoneProperties) && CollectionUtils.isEmpty(phoneNumbers) && MapUtils.isEmpty(recipients)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_ASSIGN_NEXT_TASK.text(activityName));
        }
        //短信通知人员 校验
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_SEND_SMS_RECEIVE.text(activityName), activity, recipients, workflow);
        List<String> values = null;
        if (CollectionUtils.isNotEmpty(params)) {
            values = params.stream().map(Param::getValue).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(objectPhoneProperties)) {
            variables.addAll(objectPhoneProperties);
        }
        if (CollectionUtils.isNotEmpty(values)) {
            variables.addAll(values);
        }
        //校验变量
        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                variables,
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_SEND_SMS_OBJECT_PHONE_PROPERTIES.text(activityName, rst.getMessage()));
            return rst;
        }

        if(!Strings.isNullOrEmpty(sendSMS.getContent())){
            rst = ValidateVariableAndContentUtils.validateContent(workflow, sendSMS.getContent(), workflow.getPreActivityFun(activity));
            if (!rst.isValid()) {
                rst.setMessage(BPMI18N.PAAS_FLOW_BPM_SEND_SMS_OBJECT_PHONE_PROPERTIES.text(activityName, rst.getMessage()));
                return rst;
            }
        }
        return ValidateResult.ok();
    }

    @Data
    class SendSMS {
        private String template;
        private String content;
        private List<String> phoneNumbers;
        private List<String> objectPhoneProperties;
        private Map<String, Set<Object>> recipients;
        private List<Param> params;
    }

    @Data
    class Param {
        private String type;
        private String name;
        private String value;
        private String remark;
    }
}
