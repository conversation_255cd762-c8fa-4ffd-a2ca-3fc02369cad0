package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.bpmn.ExclusiveGatewayExt;
import com.facishare.bpm.bpmn.TransitionExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 环路监测
 * todo 所有路径上的环，分支--〉分支  直接死掉的  校验
 *
 * Created by cuiyongxu on 17/3/29.
 */
@Slf4j
public class CycleHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        Map<String, Node> nodeMap = Maps.newHashMap();

        //获取ExclusiveGateway节点
        workflow.getActivities().forEach(e -> {
            if (e.instanceOf(ExclusiveGatewayExt.class)) {
                nodeMap.put(e.getId(), Node.create(e.getId(), 0, 0));
            }
        });
        List<TransitionExt> transitionList = workflow.getExecutableWorkflow().getTransitions();
        //计算出度和入度

        for (TransitionExt transition : transitionList) {
            String formId = transition.getFromId();
            Node formNode = nodeMap.get(formId);
            if (null != formNode) {
                int out = formNode.getPathOut();
                out++;
                formNode.setPathOut(out);
            }
            String toId = transition.getToId();
            Node toNode = nodeMap.get(toId);
            if (null != toNode) {
                int in = toNode.getPathIn();
                in++;
                toNode.setPathIn(in);
            }
        }
        LinkedHashMap<String, Graph> graph = Maps.newLinkedHashMap();//绘制有向图
        //k 为需要校验的节点的id
        //绘制有向图--begin
        for (TransitionExt transition : transitionList) {
            String formId = transition.getFromId();
            Graph graphObj = graph.get(formId);

            String[] toId = new String[]{transition.getToId()};
            //存在重复,计数不进行覆盖
            if (null != graphObj) {
                graphObj.setToId(concat(graphObj.getToId(), toId));
            } else {
                graphObj = new Graph(formId, toId);
            }
            graph.put(formId, graphObj);
        }
        //绘制有向图--end


        nodeMap.forEach((k, v) -> {
            //入度大于等于2的ExclusiveGateway节点需要校验是否存在 死循环
            if (v.getPathIn() >= 2) {
                Graph g = graph.get(k);
                hashCycle(k, g.getToId(), graph, 0);
            }
        });
    }

    private void hashCycle(String key, String[] toIds, LinkedHashMap<String, Graph> graph, int num) {
        if (num > 100) {
            //throw new BPMWorkflowDefVerifyException("回路监测异常,请检查流程图");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_LOOP_MONITOR_ERROR);
        }
        for (String tId : toIds) {
            Graph child = graph.get(tId);
            if (child.getId().equals(key)) {
                log.info("{} 存在闭环流程,请修改流程图", key);
                //throw new BPMWorkflowDefVerifyException("存在闭环流程,请修改流程图");
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_DEFINE_LOOP_ERROR);
            } else {
                num++;
                hashCycle(key, child.getToId(), graph, num);
            }
        }
    }


    private String[] concat(String[] a, String[] b) {
        String[] c = new String[a.length + b.length];
        System.arraycopy(a, 0, c, 0, a.length);
        System.arraycopy(b, 0, c, a.length, b.length);
        return c;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Node {
        public String id;
        public int pathIn = 0;//入度数
        public int pathOut = 0;//出度数

        static Node create(String id, int pathIn, int pathOut) {
            return new Node(id, pathIn, pathOut);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private class Graph {
        private String id;
        private String[] toId;
    }
}
