package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.flow.element.plugin.api.FlowElement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * wansong 2023年10月11日
 * 方案： https://wiki.firstshare.cn/pages/viewpage.action?pageId=286097933
 */
@Slf4j
public class UserTaskCustomElementValidateHandler implements ValidateActivityHandler {

    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        RefServiceManager serviceManager = workflow.getServiceManager();
        UserTaskExt userTaskExt = (UserTaskExt) activity;
        String taskName = userTaskExt.getName();

        boolean custom = userTaskExt.isCustom();

        if (!custom) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_CUSTOM_FALSE, taskName, WorkflowKey.ActivityKey.custom);
        }

        String elementApiName = userTaskExt.getElementApiName();

        if (StringUtils.isEmpty(elementApiName)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_IS_NULL, taskName, WorkflowKey.ActivityKey.elementApiName);
        }

        FlowElement element = serviceManager.getFlowElement(elementApiName);

        if (Objects.isNull(element) || !element.isEnable()) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_NOT_EXISTS, taskName, elementApiName);
        }

        if (MapUtils.isEmpty(userTaskExt.getCustomExtension())) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_EXTENSION_IS_EMPTY, taskName);
        }

        if(!Objects.equals(element.isCustomCandidateConfig(),userTaskExt.isCustomCandidateConfig())){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_CANDIDATE_NOT_MATCH, taskName);
        }

        boolean hasImportObject = Boolean.TRUE.equals(userTaskExt.getImportObject());
        boolean hasRelatedEntityId = StringUtils.isNotBlank(userTaskExt.getRelatedEntityId());
        if(element.isImportObject()){
            if(!hasImportObject || !hasRelatedEntityId){
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_IMPORT_OBJECT_DEF_ERROR, taskName);
            }
        }else {
            if(hasImportObject){
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_ELEMENT_IMPORT_OBJECT_DEF_ERROR, taskName);
            }
        }

        return ValidateResult.ok();
    }
}
