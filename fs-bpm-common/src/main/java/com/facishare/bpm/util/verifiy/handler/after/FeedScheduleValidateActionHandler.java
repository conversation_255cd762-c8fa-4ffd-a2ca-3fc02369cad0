package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 6.3
 * 1. 抄送范围不能为空且合法
 * 2. 内容不能为空
 * 3. 开始和结束时间不能为空
 * 4. 提醒配置不能为空
 */
public class FeedScheduleValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        FeedSchedule feedSchedule = JsonUtil.fromJson(JsonUtil.toJson(executionItem.getActionMapping()), FeedSchedule.class);
        String activityName = getActivityName(activity);
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_SEND_SCHEDULE_COPY_PARTICIPANT.text(activityName), activity, feedSchedule.getAttenderEmployeeIds(), workflow);
        if (!UserTaskExt.hasAssignee(feedSchedule.getAttenderEmployeeIds())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SCHEDULE_COPY_RANGE_NOT_EMPTY.text(activityName));
        }
        if (Strings.isNullOrEmpty(feedSchedule.getContent())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SCHEDULE_CONTENT_NOT_EMPTY.text(activityName));
        }
        if (Strings.isNullOrEmpty(feedSchedule.getBeginTime() + "")) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SCHEDULE_START_TIME_NOT_EMPTY.text(activityName));
        }
        if (CollectionUtils.isEmpty(feedSchedule.getRemindTypes())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SCHEDULE_REMIND_SETTING_NOT_EMPTY.text(activityName));
        }
        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                feedSchedule.getContent(),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_SCHEDULE_CONTENT.text(activityName, rst.getMessage()));
            return rst;
        }
        return rst;
    }

    @Data
    class FeedSchedule {
        Boolean allDay;
        Map<String, Set<Object>> attenderEmployeeIds;
        Object beginTime;
        String content;
        Object endTime;
        boolean receipt;
        List remindTypes;
    }
}
