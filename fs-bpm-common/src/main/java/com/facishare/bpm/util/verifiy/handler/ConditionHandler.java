package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.TransitionExt;
import com.facishare.bpm.bpmn.condition.ConditionExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.bean.BPMFieldBean;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.bpm.utils.ExpressionUtil;
import com.facishare.bpm.utils.model.UtilConstans;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 审批节点或者 分支节点,transitions上面必需有表达式
 * 注意 分支节点上有默认 transition  此线可以没有条件
 *
 * <AUTHOR>
 * @date 17/3/15
 */
@Slf4j
public class ConditionHandler implements ValidateHandler {
    private final static ConditionComparatorHandler comparatorHandler = new ConditionComparatorHandler();

    private static Pattern exp = Pattern.compile("([^\"]*[0-9a-zA-Z]{1,}##[^\"]*)");

    @Override
    public void validate(Workflow workflow) {
        Map<String, ActivityExt> activities = workflow.getActivityMap();

        List<TransitionExt> conditionTransitions = workflow.getTransitions().stream().filter
                (transition -> transition.getCondition() != null).collect(Collectors.toList());

        for (TransitionExt t : conditionTransitions) {
            String fromActivityExtId = t.getFromId();
            String toId = t.getToId();
            String activityNodeName = workflow.getActivityMap().get(fromActivityExtId).getName();
            String description = t.getDescription();
            verifyFromToInWorkflow(activityNodeName, description, activities, fromActivityExtId, toId);
            Set<String> conditions = getTransitionConditions(t.getCondition());
            verifyExistVariable(activityNodeName, description, conditions, workflow.getActivityMap().get(fromActivityExtId), workflow);
            verifyContainSubDept(t.getCondition(), workflow.getServiceManager());
            verifyRightValueAndType(activityNodeName, t.getCondition());
        }

        comparatorHandler.validate(workflow);
    }

    /**
     * 校验右侧的值  看类型和value是否匹配
     *
     * @param condition
     */
    private void verifyRightValueAndType(String activityNodeName, ConditionExt condition) {
        if (condition != null) {
            List<ConditionExt> conditionExts = condition.getConditions();
            if (CollectionUtils.isNotEmpty(conditionExts)) {
                conditionExts.forEach(conditionExt -> verifyRightValueAndType(activityNodeName, conditionExt));
            } else {
                ConditionExt.ValueExt right = condition.getRight();
                if (right != null) {
                    /*
                    {
                        "type": "equals",
                        "left": {
                            "expression": "activity_0##object_UPfNh__c##field_aKWce__c"
                        },
                        "right": {
                            "value": ["option1"],
                            "type": {
                                "name": "text"
                            },
                            "metadata": {
                                "containSubDept": false
                            }
                        }
                    }

                     */
                    Map<String, Object> rightType = (Map<String, Object>) right.get(BPMConstants.TYPE);
                    if (MapUtils.isNotEmpty(rightType)) {
                        String conditionType = (String) condition.get(BPMConstants.TYPE);
                        String name = (String) rightType.get(BPMConstants.NAME);
                        Object value = right.get(BPMConstants.VALUE);
                        if ("equals".equals(conditionType) && "text".equals(name) && value instanceof List) {
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_BRANCH_PARAMS_TYPE_EXCEPTION, activityNodeName);
                        }
                    }
                }
            }
        }
    }


    private void verifyContainSubDept(ConditionExt condition, RefServiceManager serviceManager) {
        if (condition != null) {
            List<ConditionExt> conditionExts = condition.getConditions();
            if (CollectionUtils.isNotEmpty(conditionExts)) {
                conditionExts.forEach(conditionExt -> verifyContainSubDept(conditionExt, serviceManager));
            } else {
                getInnerExpression(condition, serviceManager);
            }
        }
    }


    //校验是否包含子部门
    private void getInnerExpression(ConditionExt conditionExt, RefServiceManager serviceManager) {
        //先获取右侧的metadata->containSubDept,判断是否为true,如果是true 则需要判断左侧的字段是否是部门类型的
        ConditionExt.ValueExt right = conditionExt.getRight();
        if (right != null) {
            Map<String, Object> metadata = (Map<String, Object>) right.get(BPMConstants.METADATA);
            if (MapUtils.isNotEmpty(metadata)) {
                Boolean containSubDept = (Boolean) metadata.get(BPMConstants.CONTAIN_SUB_DEPT);
                if (containSubDept) {
                    // 通过字段获取类型
                    ConditionExt.ValueExt valueExt = conditionExt.getLeft();
                    String nameType = getType(valueExt.getExpression(), serviceManager);
                    if (!BPMConstants.DEPARTMENT.equals(nameType)&& !BPMConstants.DEPARTMENT_MANY.equals(nameType)) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_BRANCH_SUPPORTS_CHILD_DEPT_EXCEPTION);
                    }
                }
            }
        }
    }

    private static List<String> otherExpression = Lists.newArrayList(BPMConstants.OWNER_MAIN_DEPT_PATH);

    public static String getType(String expression, RefServiceManager serviceManager) {
        expression = ExpressionUtil.getInternalExpression(expression);
        //负责人所属主部门,直接是list

        BPMFieldBean bpmFieldBean = BPMFieldBean.analysisFieldExpression(
                Splitter.on(UtilConstans.WELL)
                        .splitToList(ValidateVariableAndContentUtils.getInnerKeyByFirst(expression)));

        if (BPMConstants.ApproveResult.RESULT.equals(bpmFieldBean.getEntityId())
                || BPMConstants.EXECUTIONTYPE.equals(bpmFieldBean.getEntityId())
            //|| BPMConstants.LATENCY_RESULT.equals(bpmFieldBean.getEntityId())
        ) {
            return BPMConstants.EngineVariableType.TEXT;
        }

        if (otherExpression.contains(bpmFieldBean.getMainField())) {
            return BPMConstants.DEPARTMENT;
        }

        BPMFieldBean.MainObjOrField mainObjOrField = BPMFieldBean.getMainObjOrField(bpmFieldBean, serviceManager);
        return serviceManager.getFieldType(mainObjOrField.getEntityId(), mainObjOrField.getField());
    }


    private void verifyFromToInWorkflow(String activityNodeName, String conditionDesc, Map<String, ActivityExt> activities, String fromId, String toId) {
        if (!activities.containsKey(fromId)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_BRANCH_IN_LINE_ERROR, activityNodeName, conditionDesc);
        }
        if (!activities.containsKey(toId)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_BRANCH_OUT_LINE_ERROR, activityNodeName, conditionDesc);
        }
    }


    private void verifyExistVariable(String activityName, String description, Set<String> conditions, ActivityExt current, Workflow workflow) {
        ArrayList<String> variables = workflow.getVariablesList();
        for (String condition : conditions) {
            if (!variables.contains(condition)) {
                log.error("workflow verify error : transition condition does not exsit in varible. DESCRIPTION={}, " +
                        "CONDITION={}", description, condition);
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_CONDITION_NOT_FOUND_VARIABLE, activityName, description);
            }
        }
        ValidateResult rst = ValidateVariableAndContentUtils.validateContentWithOutShell(workflow, conditions, workflow.getPreActivityFun(current));
        // 校验分支关联类型的统计字段
        try {
            validateContentFieldType(ValidateVariableAndContentUtils.getExpressionBeans(conditions), workflow.getServiceManager());
        } catch (BPMWorkflowDefVerifyException e) {
            rst = ValidateResult.fail(e.getMessage());
        }
        if (rst.isNotValid()) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_CONDITION_VARIABLE_CONTAINS, activityName, description, rst.getMessage());
        }
    }


    private Set<String> getTransitionConditions(Object condition) {
        Set<String> conditions = Sets.newHashSet();

        Matcher matcher = exp.matcher(JsonUtil.toJson(condition));
        while (matcher.find()) {
            conditions.add(matcher.group());
        }

        return conditions;
    }

    /**
     * 不对流程的变量进行处理
     *
     * @param expressionBeans
     * @param serviceManager
     */
    private static void validateContentFieldType(List<ValidateVariableAndContentUtils.BPMDefVariable> expressionBeans, RefServiceManager serviceManager) {
        if (CollectionUtils.isEmpty(expressionBeans)) {
            return;
        }
        expressionBeans.forEach(expression -> {
            if (expression.getType() == ValidateVariableAndContentUtils.DefVariableType.EntityObject) {
                ValidateVariableAndContentUtils.validateFieldType(expression.getExpression(), serviceManager);
            }
        });
    }

}
