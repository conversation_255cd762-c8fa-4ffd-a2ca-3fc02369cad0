package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.bpmn.condition.ConditionExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.model.Pair;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 2. 开始节点有且只能有一根连出的线， 不允许有连入的线
 * 3. 业务活动节点有且只能有一根连出的线， 至少有一根连入的线
 * 4. 审批/会签节点至少有一根连入的线， 有且只有两个连出的线
 * 5. 分支节点， 至少有一根连入的线， 至少有一个连出的线
 * 6. 结束节点， 有至少有一根连入的线， 不允许有连出的线
 * 7. 默认分支是否存在校验
 * Created by cuiyongxu on 17/3/20.
 */
@Slf4j
public class NodeTransitionHandler implements ValidateHandler {

    @Override
    public void validate(Workflow workflow) {

        verifyDuplicateTransitions(workflow);
        verifyActivityTransitionOutAndInNum(workflow);
        verifyDefaultTransition(workflow.getExecutableWorkflow());
    }

    private void verifyDuplicateTransitions(Workflow workflow) {
        Set<Pair<String, String>> from2toPairs = Sets.newHashSet();
        Set<Pair<String, String>> duplicatePairs = Sets.newHashSet();

        List<TransitionExt> transitionList = workflow.getExecutableWorkflow().getTransitions();
        transitionList.forEach(transition -> {
            String fromId = transition.getFromId();
            String toId = transition.getToId();
            Pair<String, String> from2toPair = new Pair<>(fromId, toId);

            if (!from2toPairs.add(from2toPair)) {
                duplicatePairs.add(from2toPair);
            }
        });

        if (duplicatePairs.size() > 0) {
            Map<String, ActivityExt> activityMap = workflow.getExecutableWorkflow().getActivities().stream().collect(Collectors.toMap
                    (ActivityExt::getId, activity -> activity));
            StringBuilder errorMsg = new StringBuilder();

            duplicatePairs.forEach(from2to -> {
                ActivityExt from = activityMap.get(from2to.getKey());
                ActivityExt to = activityMap.get(from2to.getValue());
                String name = BPMI18N.PAAS_FLOW_BPM_PARALLEL_GATEWAY.text();
                String fromName = from.instanceOf(ParallelGatewayExt.class) ? name : from.getName();
                String toName = to.instanceOf(ParallelGatewayExt.class) ? name : to.getName();
                errorMsg.append("[").append(fromName).append("-").append(toName).append("]").append(",");
            });
            errorMsg.append(BPMI18N.PAAS_FLOW_BPM_VALIDATE_EXISTS_MULTI_TRANSITION.text());

            //throw new BPMWorkflowDefVerifyException(errorMsg.toString());
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, errorMsg.toString());
        }


    }

    public static void verifyDefaultTransition(ExecutableWorkflowExt workflow) {
        List<TransitionExt> transitionList = workflow.getTransitions();
        workflow.getActivities().stream().filter(activity -> activity.instanceOf(ExclusiveGatewayExt.class)).forEach(activity -> {
            String defaultTransitionId = activity.getDefaultTransitionId();
            log.info("{} 默认网关校验", activity.getName());
            if (!transitionList.stream().map(BaseModel::getId).collect(Collectors.toList()).contains(defaultTransitionId)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NO_DEFAULT_TRANSITION, activity.getName());
            }
        });

    }

    private void verifyActivityTransitionOutAndInNum(Workflow workflow) {
        //startEvent                           out=1 in=0 开始节点有且只能有一根连出的线， 不允许有连入的线
        //userTask                             out=1 in>=1 业务活动节点有且只能有一根连出的线， 至少有一根连入的线
        //userTask.approve                     out=2 in>=1 审批/会签节点至少有一根连入的线， 有且只有两个连出的线
        //exclusiveGateway                     out>=1 in>=1 分支节点， 至少有一根连入的线， 至少有一个连出的线
        //endEvent                             out=0 in>=1 结束节点， 有至少有一根连入的线， 不允许有连出的线
        List<String> formIdList = Lists.newArrayList();
        List<String> toIdList = Lists.newArrayList();

        // 保存节点 连出了的线
        Map<String, List<ConditionExt>> transitionCondition = Maps.newHashMap();
        List<TransitionExt> transitionList = workflow.getExecutableWorkflow().getTransitions();
        transitionList.forEach(transition -> {
            String fromId = transition.getFromId();
            String toId = transition.getToId();
            formIdList.add(fromId);
            toIdList.add(toId);
            //key 是formId  value 是condition

            ConditionExt conditionExt = transition.getCondition();

            if (conditionExt != null) {
                List<ConditionExt> conditions = transitionCondition.get(fromId);

                if (CollectionUtils.isEmpty(conditions)) {
                    conditions = Lists.newArrayList();
                    conditions.add(conditionExt);
                } else {
                    conditions.add(conditionExt);
                }
                transitionCondition.put(fromId, conditions);
            }

        });

        //保存会签和审批节点
        Map<String, ActivityExt> approvaTask = Maps.newHashMap();

        List<ActivityExt> activities = workflow.getExecutableWorkflow().getActivities();
        activities.forEach(activity -> {
            Map map = (Map) activity.getProperty("bpmExtension");
            String type = activity.getType();
            if (activity.instanceOf(UserTaskExt.class) && "approve".equals(String.valueOf(map.get(BPMConstants.EXECUTIONTYPE)))) {
                type = "approve"; //会签,审批
                approvaTask.put(activity.getId(), activity);
            }
            if (Strings.isNullOrEmpty(type)) {
                log.error("{}未知类型", activity.getName());
                return;
            }

            NodeType nodeType = NodeType.valueOf(type);
            conditionVerify(nodeType.getInCondition(), nodeType.getIn(), Collections.frequency(toIdList, activity.getId()), activity.getName(), 'i');//连入的线计算
            conditionVerify(nodeType.getOutCondition(), nodeType.getOut(), Collections.frequency(formIdList, activity.getId()), activity.getName(), 'o');//连出的线计算
        });
        // 校验审批节点和会签节点的 同意不同意 只能有一个
        conditionVerifyApproveLine(transitionCondition, approvaTask);
    }

    /**
     * 审批节点连出的线只能有一个同意和一个不同意
     *
     * @param transitionCondition
     * @param approvaTask
     */
    private void conditionVerifyApproveLine(Map<String, List<ConditionExt>> transitionCondition, Map<String, ActivityExt> approvaTask) {
        approvaTask.forEach((key, value) -> {
            List<ConditionExt> conditionExts = transitionCondition.get(key);
            if (CollectionUtils.isEmpty(conditionExts)) {
                //抛出异常,节点是审批或者会签,但是没有从线上获取到对应的condition
                log.error("{},{}节点为审批或会签节点,transition缺少condition", value.getName(), value.getId());
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_APPROVAL_NODE_CONFIG_ERROR, value.getName());
            }

            Set<String> result = Sets.newHashSet();
            conditionExts.forEach(conditionExt -> {
                getCondition(conditionExt, result);
            });

            if (result.size() != 2) {
                // 抛出异常.格式不正确
                log.error("{},{}节点为审批或会签节点,节点上 有两个同意或者不同意", value.getName(), value.getId());
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_APPROVAL_NODE_CONFIG_ERROR, value.getName());
            }

        });
    }

    private void getCondition(ConditionExt condition, Set<String> result) {
        if (condition != null) {
            List<ConditionExt> conditionExts = condition.getConditions();
            if (CollectionUtils.isNotEmpty(conditionExts)) {
                conditionExts.forEach(conditionExt -> getCondition(conditionExt, result));
            } else {
                setApproveNodeInnerExpression(condition, result);
            }
        }
    }

    private void setApproveNodeInnerExpression(ConditionExt condition, Set<String> result) {
        String leftValue = (String) condition.getLeft().get("expression");
        //activity_1540973813284##result
        //|| leftValue.contains(BPMConstants.LATENCY_RESULT)
        if (leftValue.contains(BPMConstants.ApproveResult.RESULT)|| leftValue.contains(BPMConstants.EXECUTIONTYPE)) {
            String rightValue = (String) condition.getRight().getValue();
            result.add(rightValue);
        }
    }

    /**
     * 表达式,数值
     *
     * @param condition
     * @param finalCount 只能出现的次数
     * @param count      出现次数
     */
    public void conditionVerify(Condition condition, int finalCount, int count, String name, char c) {
        String message = "";
        if (c == 'i') {
            message = BPMI18N.PAAS_FLOW_BPM_ACTIVITY_OUTPUT.text();
        }
        if (c == 'o') {
            message = BPMI18N.PAAS_FLOW_BPM_ACTIVITY_INPUT.text();
        }
        switch (condition) {
            case equals:
                if (finalCount != count) {
                    //throw new BPMWorkflowDefVerifyException(name + " 节点只能有" + finalCount + "条" + message + "的线");
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AT_MOST, name, finalCount, message);
                }
                break;
            case greaterThanOrEqual:
                if (count < finalCount) {
                    //throw new BPMWorkflowDefVerifyException(name + " 节点至少有" + finalCount + "条" + message + "的线");
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AT_LEAST, name, finalCount, message);
                }
                break;
            default:
                //throw new BPMWorkflowDefVerifyException("线条表达式异常");
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_TRANSITION_CONDITION_ERROR);
        }
    }


    enum NodeType {
        //in ,out, in,out
        startEvent(0, 1, Condition.equals, Condition.equals),
        userTask(1, 1, Condition.greaterThanOrEqual, Condition.equals),
        latencyTask(1, 1, Condition.equals, Condition.equals),
        approve(1, 2, Condition.greaterThanOrEqual, Condition.equals),
        exclusiveGateway(1, 2, Condition.greaterThanOrEqual, Condition.greaterThanOrEqual),
        parallelGateway(1, 1, Condition.greaterThanOrEqual, Condition.greaterThanOrEqual),
        executionTask(1, 1, Condition.greaterThanOrEqual, Condition.equals),
        endEvent(1, 0, Condition.greaterThanOrEqual, Condition.equals);

        @Getter
        private int in;
        @Getter
        private int out;
        @Getter
        private Condition inCondition;
        @Getter
        private Condition outCondition;

        NodeType(int in, int out, Condition inCondition, Condition outCondition) {
            this.in = in;
            this.out = out;
            this.inCondition = inCondition;
            this.outCondition = outCondition;
        }
    }

    enum Condition {
        //大于等于
        greaterThanOrEqual(">="),
        //等于
        equals("==");

        @Getter
        String character;

        Condition(String character) {
            this.character = character;
        }
    }
}





