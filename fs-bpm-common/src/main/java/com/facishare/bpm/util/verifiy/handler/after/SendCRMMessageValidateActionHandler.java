package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.EndEventExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.google.common.base.Strings;

/**
 * <AUTHOR>
 * @since 6.3
 * 1. 接收人 不为空 且 合法
 * 2. 内容 不为空
 * 3. 标题 不为空
 */
public class SendCRMMessageValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        String activityName = getActivityName(activity);
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_SEND_CRM_RECIPIENT.text(activityName), activity, executionItem.getRecipients(), workflow);
        if (!UserTaskExt.hasAssignee(executionItem.getRecipients())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_CRM_RECIPIENT_NOT_EMPTY.text(activityName));
        }
        if (Strings.isNullOrEmpty(executionItem.getContent())) {
            //终止流程异常显示名称替换，其他后动作后期支持也需处理
            if (activity instanceof EndEventExt) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_END_NODE_SEND_REMIND_CONTENT.text(activityName));
            } else {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_REMIND_CONTENT.text(activityName));
            }
        }
        if (Strings.isNullOrEmpty(executionItem.getTitle())) {
            if(activity instanceof EndEventExt){
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_END_NODE_SEND_REMIND_TITLE.text(activityName));
            }else {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_REMIND_TITLE.text(activityName));
            }
        }

        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                executionItem.getTitle(),
                workflow.getPreActivityFun(activity)
        );
        if (!rst.isValid()) {
            if(activity instanceof EndEventExt){
                rst.setMessage(BPMI18N.PAAS_FLOW_BPM_END_NODE_SEND_REMIND_TITLE.text(activityName, rst.getMessage()));
            }else {
                rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_REMIND_TITLE.text(activityName, rst.getMessage()));
            }
            return rst;
        }
        rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                executionItem.getContent(),
                workflow.getPreActivityFun(activity)
        );
        if (!rst.isValid()) {
            if(activity instanceof EndEventExt){
                rst.setMessage(BPMI18N.PAAS_FLOW_BPM_END_NODE_SEND_REMIND_CONTENT.text(activityName, rst.getMessage()));
            }else {
                rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_REMIND_CONTENT.text(activityName, rst.getMessage()));
            }
            return rst;
        }

        return rst;
    }

}
