package com.facishare.bpm.util.verifiy.handler.bean;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.model.UtilConstans;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;

@Data
public class BPMFieldBean {
    private String entityId;
    private String mainField;
    private String lookupField;
    private String activityIdFull;
    private String activityId;
    private boolean selfRef;

    public static BPMFieldBean create(String entityId, String mainField, String lookupField, String activityIdFull, String activityId, boolean selfRef) {
        BPMFieldBean fieldBean = new BPMFieldBean();
        fieldBean.setEntityId(entityId);
        fieldBean.setMainField(mainField);
        fieldBean.setLookupField(lookupField);
        fieldBean.setActivityIdFull(activityIdFull);
        fieldBean.setActivityId(activityId);
        fieldBean.setSelfRef(selfRef);
        return fieldBean;
    }

    @Data
    public static class MainObjOrField {
        private String entityId;
        private String field;
    }

    public static MainObjOrField getMainObjOrField(BPMFieldBean bpmFieldBean, RefServiceManager serviceManager) {
        MainObjOrField mainObjOrField = new MainObjOrField();
        //如果不是关联的,则只获取当前对象的字段进行校验
        if (Strings.isNullOrEmpty(bpmFieldBean.getLookupField())) {
            mainObjOrField.setEntityId(bpmFieldBean.getEntityId());
            mainObjOrField.setField(bpmFieldBean.getMainField());
        } else {
            // 存在关联关系
            Map<String, Object> fieldDesc = serviceManager.getFieldDesc(bpmFieldBean.getEntityId(), bpmFieldBean.getMainField());
            if (MapUtils.isEmpty(fieldDesc)) {
                throw new BPMBusinessException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_FILTER_CONFIG_DEPT_FIELD_ERROR);
            }
            //人员特殊处理
            if (BPMConstants.MetadataKey.EMPLOYEE.equals(fieldDesc.get(BPMConstants.MetadataKey.type)) || BPMConstants.MetadataKey.EMPLOYEE_MANY.equals(fieldDesc.get(BPMConstants.MetadataKey.type))) {
                mainObjOrField.setEntityId(BPMConstants.MetadataKey.PERSONNELOBJ);
            } else {
                Map<String, String> entityIdRefMap = serviceManager.getRefEntityIdByEntityId(bpmFieldBean.getEntityId());
                mainObjOrField.setEntityId(entityIdRefMap.get(bpmFieldBean.getMainField()));
            }
            mainObjOrField.setField(bpmFieldBean.getLookupField());
        }
        return mainObjOrField;
    }

    public static BPMFieldBean analysisFieldExpression(List<String> expressionList) {

        String entityId;
        String mainField = null;
        String lookupField = null;
        String activityIdFull = expressionList.get(0);
        String activityId = Splitter.on(UtilConstans.UNDERLINE).splitToList(activityIdFull).get(1);

        boolean selfRef = false;

        if (UtilConstans.REF.equals(expressionList.get(1))) {
            selfRef = true;
            //1.  actityId_0,SelfRef,AccountObj,mainfield
            //自关联
            entityId = expressionList.get(2);
            mainField = expressionList.get(3);
            if (expressionList.size() == 5) {
                //2.  actityId_0,SelfRef,AccountObj,mainfield,lookupField
                lookupField = expressionList.get(4);
            }
        } else {
            //非自关联
            //actityId_271287312837,AccountObj,mainField
            //actityId_423423432420,AccountObj,mainField,lookupField
            entityId = expressionList.get(1);
            //actityId_271287312837,result
            //&& !BPMConstants.LATENCY_RESULT.equals(entityId)
            if (!BPMConstants.ApproveResult.RESULT.equals(entityId) && !BPMConstants.EXECUTIONTYPE.equals(entityId)) {
                mainField = expressionList.get(2);
                if (expressionList.size() == 4) {
                    lookupField = expressionList.get(3);
                }
            }

        }

        return BPMFieldBean.create(entityId, mainField, lookupField, activityIdFull, activityId, selfRef);
    }
}