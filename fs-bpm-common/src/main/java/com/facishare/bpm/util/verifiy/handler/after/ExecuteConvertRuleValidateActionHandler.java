package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;

import java.util.Map;
import java.util.Objects;


public class ExecuteConvertRuleValidateActionHandler implements ValidateActionHandler{
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        Map<String, Object> actionMapping = executionItem.getActionMapping();
        Object ruleName = actionMapping.get(BPMConstants.ConvertRule.ruleName);
        if (Objects.isNull(ruleName)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_NAME_IS_NULL.text(activity.getName()));
        }
        Object ruleApiName = actionMapping.get(BPMConstants.ConvertRule.ruleApiName);
        if (Objects.isNull(ruleApiName)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_API_NAME_IS_NULL.text(activity.getName()));
        }
        Object sourceApiName = actionMapping.get(BPMConstants.ConvertRule.sourceApiName);
        if (Objects.isNull(sourceApiName)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_SOURCE_API_NAME_IS_NULL.text(activity.getName()));
        }
        if (Objects.isNull(actionMapping.get(BPMConstants.ConvertRule.targetApiName))) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_TARGET_API_NAME_IS_NULL.text(activity.getName()));
        }
        if (!workflow.getEntryType().equals(sourceApiName)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_IS_DIFF_FROM_ENTRY_TYPE.text(activity.getName(), ruleName));
        }
        if (!workflow.getServiceManager().convertRuleIsEnable(ruleName.toString(), ruleApiName.toString())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXECUTE_CONVERT_RULE_IS_DISABLE_OR_IS_DELETED.text(activity.getName()));
        }

        return ValidateResult.ok();
    }
}
