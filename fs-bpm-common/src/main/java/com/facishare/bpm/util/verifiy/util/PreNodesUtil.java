package com.facishare.bpm.util.verifiy.util;

import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 31/05/2017
 */
@Slf4j
@UtilityClass
@Deprecated
public class PreNodesUtil {

    public Map<String, List<ActivityExt>> getPreActivities(String tenantId, ExecutableWorkflowExt workflow) {
            Map<String, List<ActivityExt>> activityMap = getPreActivityMap(workflow);
            return activityMap;
    }

    public static Map<String, List<ActivityExt>> getPreActivityMap(ExecutableWorkflowExt workflow) {
        StopWatch stopWatch = StopWatch.createUnStarted("getPreActivities");
        Map<String, ActivityExt> activities = workflow.getActivityMaps();
        Map<String, List<ActivityExt>> activityMap = Maps.newHashMap();
        stopWatch.lap("activities");
        List<TransitionExt> transitions = workflow.getTransitions();
        ActivityExt startActivity = getStartActivity(activities.values());
        activities.values().forEach(activity -> {
            if (activity.instanceOf(UserTaskExt.class) || activity.instanceOf(ExclusiveGatewayExt.class)) {
                activityMap.put(activity.getId(), getPreActivities(startActivity, activity, activities, transitions).stream().filter(item -> !isApprovalOrExclusiveGateway(item) && !(item.instanceOf(ParallelGatewayExt.class))).collect(Collectors.toList()));
                stopWatch.lap("getPreActivity:" + activity.getId());
            }
        });

        if (MapUtils.isNotEmpty(activityMap)) {
            log.info("workflow:id:{}({})", workflow.getId(), workflow.getName());
            activityMap.forEach((key, value) -> log.info("workflowName:{},activity:{},preNode:{}", workflow.getName(), key + "(" + activities.get(key).getName() + ")", value.stream().map(item -> item.getId() + "(" + item.getName() + ")").toArray()));
        }
        stopWatch.logSlow(100);
        return activityMap;
    }

    public static boolean isApprovalOrExclusiveGateway(ActivityExt activity) {
        if (activity instanceof ExclusiveGatewayExt) {
            return true;
        }
        return false;
    }

    public ActivityExt getStartActivity(Collection<ActivityExt> activities) {
        for (ActivityExt activity : activities) {
            if (activity.instanceOf(StartEventExt.class)) {
                return activity;
            }
        }
        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_DEFINE_NOT_FOUND_START_NODE);
    }

    private List<ActivityExt> getPreActivities(ActivityExt startActivity, ActivityExt activity, Map<String, ActivityExt> activities, List<TransitionExt> transitions) {
        //去环，获取所有路径
        List<List<ActivityExt>> allPathNodes = Lists.newArrayList();

        //去环
        rejectCircles(startActivity, activity, activities, transitions, Lists.newArrayList(startActivity), allPathNodes);

        //遍历所有去环路径中的每条路径，找出配对并行节点、和配对并行节点内的前置节点
        List<ActivityExt> startPGs = Lists.newArrayList();
        List<ActivityExt> endPGs = Lists.newArrayList();
        calculateStartPGs(allPathNodes, transitions, startPGs);
        Collections.reverse(startPGs);
        calculateEndPGs(allPathNodes, transitions, startPGs, endPGs);
        //并行路径 开始路径相同的 路径 取交集  来处理分支节点的
        //将上面交集后的路径和别的并行路径 取并集
        List<ActivityExt> innerPreActivities = calculatePGsInnerPreActivity(allPathNodes, startPGs, endPGs);
        //并行节点外的路径取交集
        List<ActivityExt> outterPreActivities = calculatePGsOuterPreActivity(startActivity, activity, allPathNodes, startPGs, endPGs);
        //将所有路径取节点取并集
        List<List<ActivityExt>> allPreActivities = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(innerPreActivities)) {
            allPreActivities.add(innerPreActivities);
        }
        if (CollectionUtils.isNotEmpty(outterPreActivities)) {
            allPreActivities.add(outterPreActivities);
        }

        List<ActivityExt> rst = union(allPreActivities).stream()
                .collect(Collectors.toList());

        return rst;
    }

    public static List<ActivityExt> calculatePGsOuterPreActivity(ActivityExt start, ActivityExt activity, List<List<ActivityExt>> allPathNodes, List<ActivityExt> startPGs, List<ActivityExt> endPGs) {
        List<List<ActivityExt>> afterOutterActivites = Lists.newArrayList();
        List<List<ActivityExt>> beforeOutterActivites = Lists.newArrayList();
        if (startPGs.size() > 0) {
            startPGs.forEach(startPG -> {
                ActivityExt endPG = endPGs.get(startPGs.indexOf(startPG));
                allPathNodes.stream().filter(path -> path.contains(startPG) && path.contains(endPG) && path.contains(activity)).forEach(path -> {
                    beforeOutterActivites.add(path.subList(path.indexOf(start), path.indexOf(startPG)));
                    afterOutterActivites.add(path.subList(path.indexOf(endPG), path.indexOf(activity)));
                });
            });
        } else {
            afterOutterActivites.addAll(allPathNodes);
        }

        return union(Lists.newArrayList(intersection(beforeOutterActivites), intersection(afterOutterActivites)));
    }

    public static List<ActivityExt> calculatePGsInnerPreActivity(List<List<ActivityExt>> allPathNodes, List<ActivityExt> startPGs, List<ActivityExt> endPGs) {

        //1.并行路径 开始节点相同且有分支节点的的 路径 取交集
        //3.开始节点不同的 所有并行线 进行取并

        List<List<ActivityExt>> needUnionPath = Lists.newArrayList();

        startPGs.forEach(startPG -> {
            ActivityExt end = endPGs.get(startPGs.indexOf(startPG));
            Map<ActivityExt, List<List<ActivityExt>>> needInter = Maps.newHashMap();
            allPathNodes.stream()
                    .filter(path -> path.contains(startPG) && path.contains(end))
                    .map(path -> path.subList(path.indexOf(startPG) + 1, path.indexOf(end)))
                    .forEach(path -> {
                        if (path.size() > 0) {
                            List<List<ActivityExt>> item = needInter.get(path.get(0));
                            if (item == null) {
                                item = Lists.newArrayList();
                                needInter.put(path.get(0), item);
                            }
                            item.add(path);
                        }
                    });
            needInter.values().forEach(item -> needUnionPath.add(intersection(item)));
        });

        return union(needUnionPath);
    }


    /**
     * 1. 取出配对并行网关节点
     *
     * @param allPathNodes
     * @param transitions
     * @param startPGs
     * @param endPGs       Aaron
     */
    private static void calculateEndPGs(List<List<ActivityExt>> allPathNodes, List<TransitionExt> transitions, List<ActivityExt> startPGs, List<ActivityExt> endPGs) {
        Set<ActivityExt> hasPair = Sets.newHashSet();
        Set<ActivityExt> noUseStartPGs = Sets.newHashSet();
        startPGs.forEach(startPG -> {
            for (int i = 0; i < allPathNodes.size(); i++) {
                List<ActivityExt> path = allPathNodes.get(i);
                if (path.contains(startPG) && !hasPair.contains(startPG)) {
                    List<ActivityExt> childPath = path.subList(path.indexOf(startPG) + 1, path.size());
                    List<ActivityExt> tempPGs = childPath.stream().filter(activity -> (activity.instanceOf(ParallelGatewayExt.class)) && getOutGoings(transitions, activity).size() == 1).collect(Collectors.toList());
                    if (tempPGs.size() > 0) {
                        for (ActivityExt tempEnd : tempPGs) {
                            if (!endPGs.contains(tempEnd)) {
                                endPGs.add(tempEnd);
                                hasPair.add(startPG);
                                break;
                            }
                        }

                    }
                }
            }
            if (!hasPair.contains(startPG)) {
                noUseStartPGs.add(startPG);
            }
        });
        startPGs.removeAll(noUseStartPGs);
    }

    private static void calculateStartPGs(List<List<ActivityExt>> allPathNodes, List<TransitionExt> transitions, List<ActivityExt> startPGs) {
        List<ActivityExt> allRemoveActivity = Lists.newArrayList();
        allPathNodes.forEach(path -> path.stream().filter(activity -> activity.instanceOf(ParallelGatewayExt.class)).forEach(activity -> {
            List<TransitionExt> outGoings = getOutGoings(transitions, activity);
            List<TransitionExt> inGoings = getInGoings(transitions, activity);
            if (outGoings.size() > 1 && !startPGs.contains(activity)) {
                startPGs.add(activity);
            } else if (outGoings.size() <= 1 && inGoings.size() == 1) {
                allRemoveActivity.add(activity);
            }
        }));
//        将出现或入线为1的网关移除
        allPathNodes.forEach(path -> path.removeAll(allRemoveActivity));
    }

    public static List<ActivityExt> union(List<List<ActivityExt>> preNodesOnePath) {
        Collection<ActivityExt> rst = Lists.newArrayList();
        for (int i = 0; i < preNodesOnePath.size(); i++) {
            rst = CollectionUtils.union(rst, preNodesOnePath.get(i));
        }
        List<ActivityExt> temp = Lists.newArrayList();
        temp.addAll(rst);
        return temp;
    }

    public static List<ActivityExt> intersection(List<List<ActivityExt>> sameStartPaths) {
        Collection<ActivityExt> rst = null;
        for (int i = 0; i < sameStartPaths.size(); i++) {
            if (i == 0) {
                rst = sameStartPaths.get(0);
            } else {
                rst = CollectionUtils.intersection(rst, sameStartPaths.get(i));
            }
        }
        List<ActivityExt> temp = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(rst)) {
            temp.addAll(rst);
        }

        return temp;
    }

    public void rejectCircles(ActivityExt startActivity, ActivityExt currentActivity, Map<String, ActivityExt> activities, List<TransitionExt> transitions, List<ActivityExt> currentPath, List<List<ActivityExt>> allPathNodes) {
        if (startActivity.getId().equals(currentActivity.getId())) {
            allPathNodes.add(currentPath);
            return;
        }

        List<TransitionExt> outGoings = transitions.stream().filter(transition -> transition.getFromId().equals(startActivity.getId())).collect(Collectors.toList());
        for (TransitionExt transition : outGoings) {
            ActivityExt tempStartActivity = activities.get(transition.getToId());
            //去环动作
            if (!currentPath.contains(tempStartActivity)) {
                List<ActivityExt> tempPath = Lists.newArrayList();
                tempPath.addAll(currentPath);
                tempPath.add(tempStartActivity);
                rejectCircles(tempStartActivity, currentActivity, activities, transitions, tempPath, allPathNodes);
            }
        }
    }

    public static List<TransitionExt> getOutGoings(List<TransitionExt> transitions, ActivityExt tempActivity) {
        return transitions.stream().filter(transition -> transition.getFromId().equals(tempActivity.getId())).collect(Collectors.toList());
    }

    public static List<TransitionExt> getInGoings(List<TransitionExt> transitions, ActivityExt tempActivity) {
        return transitions.stream().filter(transition -> transition.getFromId().equals(tempActivity.getId())).collect(Collectors.toList());
    }

}
