package com.facishare.bpm.util;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.SpringUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 6.2
 * @IgnoreI18n
 */
@Slf4j
@Data
public class SwitchConfigManager {
    private static volatile SwitchConfig switchConfig;
    private static volatile Map<String, CustomElementConfig> customElementConfig;
    private static final String COMMON = "common";

    static {
        ConfigFactory.getConfig("fs-bpm-switch-config", SwitchConfigManager::loadSwitchConfig);
        ConfigFactory.getConfig("fs-bpm-custom-element-session-key-config", config -> {
            customElementConfig = JsonUtil.fromJson(new String(config.getContent()), new TypeToken<Map<String, CustomElementConfig>>() {}.getType());
            log.info("=============={}===========", config.getName());
            log.info(JsonUtil.toPrettyJson(customElementConfig));
            log.info("===========================");
        });
    }

    private static void loadSwitchConfig(IConfig config) {
        SwitchConfig oldSwitchConfig = switchConfig;
        log.info("old fs-bpm-switch-config:{}", JsonUtil.toJson(oldSwitchConfig));
        switchConfig = JsonUtil.fromJson(new String(config.getContent()), SwitchConfig.class);
        log.info("new fs-bpm-switch-config:{}", JsonUtil.toJson(switchConfig));

        // 如果oldSwitchConfig为空，则直接返回【系统启动时】
        if (Objects.isNull(oldSwitchConfig)) {
            return;
        }
        List<String> increaseSessionKeyCombineTenantIds = getIncreaseSessionKeyCombineTenantIds(oldSwitchConfig, switchConfig);
        if (CollectionUtils.isNotEmpty(increaseSessionKeyCombineTenantIds)) {
            for (String tenantId : increaseSessionKeyCombineTenantIds) {
                updateSessionKey(tenantId);
            }
            log.info("increaseSessionKeyCombine:{}", JsonUtil.toJson(increaseSessionKeyCombineTenantIds));
        }
    }

    private static void updateSessionKey(String tenantId) {
        try {
            TraceContext.get().setTraceId("update_session_key_" + tenantId + "_" + System.currentTimeMillis());
            // 从Spring上下文获取TodoSessionKeyManager实例
            Object todoSessionKeyManager = SpringUtils.getBean("todoSessionKeyManager");
            if (Objects.nonNull(todoSessionKeyManager)) {
                Class<?> todoSessionKeyManagerClass = todoSessionKeyManager.getClass();
                Method updateSessionKeyMethod = todoSessionKeyManagerClass.getMethod("updateSessionKey", String.class);
                updateSessionKeyMethod.invoke(todoSessionKeyManager, tenantId);
            } else {
                log.error("获取todoSessionKeyManager实例失败，无法更新企业{}的sessionKey", tenantId);
            }
        } catch (Exception e) {
            log.error("更新企业{}的sessionKey失败", tenantId, e);
        } 
        
    }

    private static List<String> getIncreaseSessionKeyCombineTenantIds(SwitchConfig oldSwitchConfig, SwitchConfig newSwitchConfig) {
        List<String> oldSessionKeyCombine = oldSwitchConfig.getSessionKeyCombine();
        List<String> newSessionKeyCombine = newSwitchConfig.getSessionKeyCombine();

        // 如果新配置为空,直接返回空列表
        if (CollectionUtils.isEmpty(newSessionKeyCombine)) {
            return Lists.newArrayList();
        }

        // 如果旧配置为空,返回全部新配置
        if (CollectionUtils.isEmpty(oldSessionKeyCombine)) {
            return newSessionKeyCombine;
        }

        // 返回新增的配置项(新配置减去旧配置的差集)
        return newSessionKeyCombine.stream()
                .filter(key -> !oldSessionKeyCombine.contains(key))
                .collect(Collectors.toList());
    }

    public static String getCustomElementSessionKey(String elementApiName){
        if(StringUtils.isBlank(elementApiName) || MapUtils.isEmpty(customElementConfig)){
            return "";
        }
        CustomElementConfig customElement = customElementConfig.get(elementApiName);
        String sessionKey = Objects.isNull(customElement) ? "" : customElement.getSessionKey();
        return BPMConstants.BPM_EXCLUSIVE_SESSION_KEY.contains(sessionKey) ? sessionKey : "";
    }

    public static String getCustomElementCategory(String entityId, String elementApiName){
        if(StringUtils.isBlank(elementApiName) || MapUtils.isEmpty(customElementConfig)){
            return "";
        }
        CustomElementConfig customElement = customElementConfig.get(elementApiName);
        String category = Objects.isNull(customElement) ? "" : customElement.getCategory();
        return SwitchConfigManager.getSupportExternalApplyBusinessCode(entityId).contains(category) ? category : "";
    }

    public static  boolean isSupportAllFormDescribe(String tenantId){
        SwitchConfig.GrayConfig supportAllFormDescribe = switchConfig.getSupportAllFormDescribe();
        if(Objects.isNull(supportAllFormDescribe)){
            return false;
        }
        return supportAllFormDescribe.supportTenantId(tenantId);
    }

    public static SwitchConfig getSwitchConfig() {
        return switchConfig;
    }

    public static List<Integer> syncDataFailRemindEmpleyee() {
        List<Integer> empleyee = switchConfig.getSyncDataFailRemindEmpleyee();
        if (CollectionUtils.isEmpty(empleyee)) {
            return Lists.newArrayList();
        }
        return empleyee;
    }

    //TODO 改个名字  重试失败次数达到 该值时,发送提醒
    public static Integer syncDataFailRetryCount() {
        Integer syncDataFailRetryCount = switchConfig.getSyncDataFailRetryCount();
        if (Objects.isNull(syncDataFailRetryCount)) {
            return 10;
        }
        return syncDataFailRetryCount;
    }

    public static boolean isUpgradeTaskInfo(String tenantId) {
        if(Objects.nonNull(switchConfig.getUpgradeTaskInfo())){
            return switchConfig.getUpgradeTaskInfo().needFilterMethod(tenantId).gray();
        }
        return false;
    }

    public static GrayType isMergeTasksByLaneDataAndDesc(String tenantId) {
        KidConfig mergeTasksByLaneDataAndDesc = switchConfig.getMergeTasksByLaneDataAndDesc();
        if(Objects.isNull(mergeTasksByLaneDataAndDesc)){
            return GrayType.history;
        }
        return mergeTasksByLaneDataAndDesc.needFilterMethod(tenantId);
    }

    /**
     * @param tenantId 企业id
     * @param entityId 对象
     * {
     *   "ei":["AccountObj","*"]
     * }
     * @return 是否忽略该企业该对象的增量更新的版本冲突
     */
    public static boolean ignoreDataVersion(String tenantId, String entityId) {
        return switchConfig.ignoreDataVersion(tenantId,entityId);
    }

    public static List<String> getCommonSystemButtons(String entityId){
        return switchConfig.getCommonSystemButtons(entityId);
    }
    public static boolean externalApplyTaskChangeCandidates(String tenantId, String actionCode){
        return switchConfig.externalApplyTaskChangeCandidates(tenantId, actionCode);
    }
    public static boolean getTaskNotGetTask(String tenantId){
        return switchConfig.getTaskNotGetTask(tenantId);
    }

    public static boolean getOpenRefreshData() {
        return switchConfig.getOpenRefreshData();
    }

    public static List<String> getSupportExternalApplyBusinessCode(String entityId){
        if(MapUtils.isNotEmpty(switchConfig.getSupportExternalApplyBusinessCodeFilter())){
            if(StringUtils.isBlank(entityId)){
                return Lists.newArrayList();
            }
            List<String> result = switchConfig.getSupportExternalApplyBusinessCodeFilter().get(entityId);
            return Objects.isNull(result) ? Lists.newArrayList() : result;
        }
        return switchConfig.getSupportExternalApplyBusinessCode();
    }

    public static Map<String, List<String>> getSupportExternalApplyBusinessCodeFilter(){
        return switchConfig.getSupportExternalApplyBusinessCodeFilter();
    }

    public static List<Integer> operateFailNoticeSessionUserId() {
        List<Integer> userId = switchConfig.getOperateFailNoticeSessionUserId();
        if (CollectionUtils.isEmpty(userId)) {
            return Lists.newArrayList();
        }
        return userId;
    }

    public static boolean getSessionKeyCombine(String tenantId){
        List<String> sessionKeyCombine = switchConfig.getSessionKeyCombine();
        if(CollectionUtils.isEmpty(sessionKeyCombine) || StringUtils.isBlank(tenantId)){
            return false;
        }
        return sessionKeyCombine.contains(tenantId) || sessionKeyCombine.contains("*");
    }

    @Data
    public static class SwitchConfig {
        //允许时间内
        private Integer durationSeconds = 20;
        //发起次数
        private Integer limitCount = 2;
        private List<Integer> syncDataFailRemindEmpleyee;
        private Integer syncDataFailRetryCount;

        //支持所有表单描述
        private GrayConfig supportAllFormDescribe;
        private KidConfig mergeTasksByLaneDataAndDesc;

        //忽略数据更新时版本号配置 ["1":["AccountObj","SalesOrderObj"]]
        private Map<String,List<Object>> ignoreDataVersion;
        //["AccountObj":["1","2"],"*":["3","4"]]
        private Map<String,List<String>> ignoreDataVersionEntityIdAndTenantIds;
        //与引擎的branchOldVariableSwitch 配合使用, 不可移除
        private KidConfig upgradeTaskInfo;

        //对象支持的系统自定义对象按钮类型   对象entityId，系统自定义按钮类型's
        private Map<String,List<String>> objectCustomSystemBtns;

        private Map<String,List<String>> externalApplyTaskChangeCandidate;

        private List<String> getTaskNotGetTask;

        private Boolean openRefreshData = false;

        private List<String> supportExternalApplyBusinessCode = Lists.newArrayList();
        private Map<String, List<String>> supportExternalApplyBusinessCodeFilter;

        private List<Integer> operateFailNoticeSessionUserId;

        private List<String> sessionKeyCombine;


        /**
         * @param tenantId 企业id
         * @param entityId 对象
         * {
         *   "ei":["AccountObj","*"]
         * }
         * @return 是否忽略该企业该对象的增量更新的版本冲突
         */
        public boolean ignoreDataVersion(String tenantId,String entityId){
            if(MapUtils.isNotEmpty(ignoreDataVersionEntityIdAndTenantIds)){
                if(ignoreDataVersionEntityIdAndTenantIds.containsKey("*")
                        &&ignoreDataVersionEntityIdAndTenantIds.getOrDefault("*", ListUtils.EMPTY_LIST).contains(tenantId)){
                    return true;
                }
                if(ignoreDataVersionEntityIdAndTenantIds.containsKey(entityId)
                        &&ignoreDataVersionEntityIdAndTenantIds.getOrDefault(entityId, ListUtils.EMPTY_LIST).contains(tenantId)){
                    return true;
                }
            }
            return Objects.nonNull(ignoreDataVersion)&&CollectionUtils.isNotEmpty(ignoreDataVersion.get(tenantId))&&(ignoreDataVersion.get(tenantId).contains(entityId)||ignoreDataVersion.get(tenantId).contains(BPMConstants.ALL));
        }

        public List<String> getCommonSystemButtons(String entityId){
            List<String> result = Lists.newArrayList();
            if(Objects.isNull(objectCustomSystemBtns)) {
                return result;
            }
            List<String> common = objectCustomSystemBtns.get(COMMON);
            if(CollectionUtils.isNotEmpty(common)){
                result.addAll(common);
            }

            List<String> list = objectCustomSystemBtns.get(entityId);
            if(CollectionUtils.isNotEmpty(list)){
                result.addAll(list);
            }
            return result;
        }

        public boolean externalApplyTaskChangeCandidates(String tenantId, String actionCode){
            if(StringUtils.isBlank(actionCode)){
                return Boolean.FALSE;
            }
            String masterActionCode;
            if(actionCode.contains("_")){
                masterActionCode = actionCode.substring(0, actionCode.indexOf("_"));
            }else {
                masterActionCode = actionCode;
            }
            return MapUtils.isNotEmpty(externalApplyTaskChangeCandidate)
                    && externalApplyTaskChangeCandidate.get(masterActionCode) instanceof List
                    && (externalApplyTaskChangeCandidate.get(masterActionCode).contains("*") || externalApplyTaskChangeCandidate.get(masterActionCode).contains(tenantId));

        }

        public boolean getTaskNotGetTask(String tenantId){
            return CollectionUtils.isNotEmpty(getTaskNotGetTask) && (getTaskNotGetTask.contains(tenantId)||getTaskNotGetTask.contains("*"));
        }

        public boolean getOpenRefreshData(){
            return Boolean.TRUE.equals(openRefreshData);
        }


        @Data
        public static class GrayConfig {
            private boolean gray = false;
            private Set<String> tenantIds = Sets.newHashSet();

            public boolean supportTenantId(String tenantId) {
                if (gray) {
                    return tenantIds.contains(tenantId);
                }
                return true;
            }
        }
    }


    @Data
    public static class KidConfig {
        /**
         * 主要是控制 灰度/全网
         */
        private boolean grayEnable;

        /**
         * 灰度详情
         */
        private Detail detail;

        GrayType needFilterMethod(String tenantId) {
            //如果企业id为空,则走原有逻辑,防止出现为空 而导致新功能错上加错
            if (Strings.isNullOrEmpty(tenantId)) {
                return GrayType.history;
            }
            //总开关,true的话走灰度;false,则直接全网
            if (grayEnable) {
                //灰度开关,开启了走灰度校验;
                if (detail.enable && !CollectionUtils.isEmpty(detail.tenantIds) && detail.tenantIds.contains(tenantId)) {
                    //企业ids不为空,并且当前企业在灰度名单中,则返回true
                    return GrayType.gray;
                } else {
                    return GrayType.history;
                }
            }
            return GrayType.full;
        }
    }

    @Data
    public static class Detail {
        /**
         * 控制灰度和历史老接口
         * grayEnable&&true:则走灰度接口
         * grayEnable&&false:则走目前线上老接口
         */
        private boolean enable;
        /**
         * 所要灰度的企业
         */
        private List<String> tenantIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomElementConfig{
        private String sessionKey;
        private String category;
    }

    public enum GrayType {
        gray("灰度"),
        full("全网"),
        history("历史接口");

        GrayType(String desc) {

        }


        public boolean gray() {
            return this == GrayType.full || this == GrayType.gray;
        }
    }


}
