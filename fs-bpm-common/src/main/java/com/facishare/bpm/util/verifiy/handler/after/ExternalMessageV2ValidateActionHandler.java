package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/10/20 11:00
 * @Description
 */
public class ExternalMessageV2ValidateActionHandler implements ValidateActionHandler{
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        String actionMappingJson = JsonUtil.toJson(executionItem.getActionMapping());
        ExternalMessageV2 actionMapping = JsonUtil.fromJson(actionMappingJson,ExternalMessageV2.class);
        String activityName = getActivityName(activity);
        Map<String, Set<Object>> receiverIds = actionMapping.getReceiverIds();
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_REMINDER.text(activityName), activity, receiverIds, workflow);
        ExternalMessageV2Body messageBody = actionMapping.getMessageBody();
        if(Objects.isNull(messageBody)){
            return new ValidateResult(false, null);
        }
        String appId = messageBody.getAppId();
        if (Strings.isNullOrEmpty(appId)
                || Objects.isNull(actionMapping.getMessageBody().getTitle())
                || Objects.isNull(actionMapping.getMessageBody().getFullContent())) {
            return new ValidateResult(false, BPMI18N.PAAS_FLOW_BPM_VALIDATE_EXTERNAL_CONTENT_IS_BLANK.text(activityName));
        }

        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                actionMapping.getMessageBody().getTitle(),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_TITLE.text(activityName, rst.getMessage()));
            return rst;
        }
        rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                actionMapping.getMessageBody().getFullContent(),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_CONTENT.text(activityName, rst.getMessage()));
            return rst;
        }

        List<ExternalMessageValidateActionHandler.DownObject> downObjects = actionMapping.getDownObjects();
        List<String> roleIds = actionMapping.getRoleIds();

        if (MapUtils.isEmpty(receiverIds)) {
            return  ExternalMessageValidateActionHandler.verifyFieldsMatchedByDownstreamEnterprise(downObjects, roleIds);
        } else {
            if (!UserTaskExt.hasAssignee(receiverIds)) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_REMINDER_NOT_NULL.text());
            }

            if (Objects.nonNull(downObjects)) {
                return ExternalMessageValidateActionHandler.verifyFieldsMatchedByDownstreamEnterprise(downObjects, roleIds);
            }
        }

        return rst;

    }
    @Data
    static class ExternalMessageV2 {
        private String appType;
        //"${AccountObj.UDLookUp1__c}"
        private List<ExternalMessageValidateActionHandler.DownObject> downObjects;//下游对象， 存在变量    String objectApiName; String objectId (是一个变量);

        private Map<String, Set<Object>> receiverIds;
        private List<String> roleIds;
        public ExternalMessageV2Body messageBody;//存在变量
    }
    @Data
    public static class ExternalMessageV2Body {
        public int type;
        public String appId;
        public List<String> extraChannelList;

        public String title;
        public String fullContent;
        public List<Map> bodyForm;
        public Map templateIdKeyListMap;
        public Map extraDataMap;

        public int getType() {
            return type==0?202:type;
        }
    }
}
