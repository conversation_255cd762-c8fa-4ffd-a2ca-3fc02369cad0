package com.facishare.bpm.util;

import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON> on 22/03/2017.
 */
@Slf4j
public class MetaDataToWorkflowVariableUtil {
    interface VariableType {
        String textType = "text", numberType = "number", booleanType = "boolean", listType="list";
    }

    public static Map<String, Object> convertValueType(String apiName, Map<String, Object> data, List<VariableExt> variableList) {
        //获取变量的类型
        Map<String, String> fields = Maps.newHashMap();
        variableList.forEach(variable -> {
            String[] temps = variable.getId().split("##");
            if (temps.length == 3 && temps[1].equals(apiName)) {
                String type = variable.getTypeName();
                fields.put(temps[2], type);
            }
        });

        Map<String, Object> target = Maps.newHashMap();

        data.forEach((k, v) -> {
            Object value=v;
            if (fields.get(k) != null) {
                String type = fields.get(k);
                try {
                    value = parseValue(v, type);
                } catch (Throwable e) {
                    log.error("parseValue failure : value convert error! VALUE={}, TYPE={}", v, type);
                    throw new BPMTaskExecuteException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_CANDIDATE_PARAMS_ERROR,k,value);
                }
            }
            target.put(k, value);
        });
        return target;
    }

    public static Object parseValue(Object v, String type) {

        if (Objects.isNull(v) || v instanceof List) {
            return v;
        }
        String value = v + "";
            if (Strings.isNullOrEmpty(value)) {
                if (!type.equals(VariableType.textType)) {
                    return null;
                }
                return value;
            }

            if (type.equals(VariableType.booleanType)) {
                return Boolean.parseBoolean(value);
            }

            if (type.equals(VariableType.numberType)) {
                boolean isDouble = value.contains(".");
                if (isDouble) {
                    return NumberUtils.createDouble(value);
                } else {
                    return NumberUtils.createLong(value);
                }
            }

            if (type.equals(VariableType.textType)) {
                if (v instanceof Double) {
                    Double temp = Double.valueOf(v + "");
                    if (temp.intValue() == temp) {
                        value = temp.intValue() + "";
                    }
                }
                return value;
            }

        return value;
    }


}
