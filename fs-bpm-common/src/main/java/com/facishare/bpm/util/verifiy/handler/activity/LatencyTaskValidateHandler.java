package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import lombok.extern.slf4j.Slf4j;



/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/4/10 5:08 PM
 */
@Slf4j
public class LatencyTaskValidateHandler implements ValidateActivityHandler {
    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        return ValidateResult.ok();
    }

}
