package com.facishare.bpm.util.layout.rules;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/6/5 11:37 AM
 */
public enum Rules {

    required("必填"), readOnly("只读"), hidden("隐藏"), display("显示");


    public static List<Rules> getRequiredRule(List<Rules> rules) {
        //如果没有规则,默认显示,不必填
        if (CollectionUtils.isEmpty(rules)) {
            return Lists.newArrayList(Rules.display);
        }
        if (rules.size() == 1) {
            return Lists.newArrayList(rules.get(0));
        }
        return null;
    }


    Rules(String desc) {

    }
}
