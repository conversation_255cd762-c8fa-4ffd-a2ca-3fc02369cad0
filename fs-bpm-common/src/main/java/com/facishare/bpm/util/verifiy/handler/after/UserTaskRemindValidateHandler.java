package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMUserTaskRemindException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.Reminder;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.handler.bean.BPMFieldBean;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.bpm.utils.ExpressionUtil;
import com.facishare.bpm.utils.MapUtil;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 校验超时提醒规则
 */
@Slf4j
public class UserTaskRemindValidateHandler implements ValidateActionHandler {

    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        UserTaskExt userTask = (UserTaskExt) activity;
        try {
            validateRemind(userTask, workflow);
        } catch (BPMUserTaskRemindException e) {
            return ValidateResult.fail(e.getMessage());
        }
        return ValidateResult.ok();
    }


    private void validateRemind(UserTaskExt userTask, Workflow workflow) {
        int latencyUnit = userTask.getLatencyUnit();
        //节点上允许停留时长
        Object remindLatency = userTask.getRemindLatency();//119
        // 允许停留时长设置了,超时提醒可以不设置
        List<Reminder> reminders = userTask.getReminders();
        Boolean remindLatencyIsNumber = remindLatency instanceof Integer || remindLatency instanceof Double;
        if(!remindLatencyIsNumber && Objects.nonNull(remindLatency)){
            remindLatencyExpressionValidate(remindLatency, userTask, workflow.getServiceManager());
        }
        // 如果超时提醒不为空
        if (CollectionUtils.isNotEmpty(reminders)) {
            reminders.forEach(reminder -> {
                if (reminder.isValidateRemind()) {
                    if (Objects.isNull(remindLatency)) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NOT_SET_TASK_TIME_OUT_NOT_SET_REMIND_TIME, userTask.getName());
                    }
                    if(remindLatencyIsNumber){
                        int remindUnit = Objects.isNull(reminder.getRemindUnit()) ? 2 : reminder.getRemindUnit();
                        remindLatencyNumberValidate(remindLatency, userTask, reminder.getRemindTime(), latencyUnit, remindUnit);
                    }
                }

                if (BPMConstants.SEND_EMAIL.equals(reminder.getChannel())) {
                    Map<String, Object> extension = reminder.getExtension();
                    Map<String, Object> emailExt = MapUtil.instance.getMap(extension, BPMConstants.SEND_EMAIL);
                    if (MapUtils.isEmpty(emailExt)) {
                        log.error("邮件提醒缺少扩展信息");
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REMIND_EMAIL_FORMAT_ERROR, userTask.getName());
                    }
                    String templeteId = MapUtil.instance.getString(emailExt, BPMConstants.TEMPLATE);
                    if (Strings.isNullOrEmpty(templeteId)) {
                        log.error("邮件提醒模板Id为空");
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REMIND_EMAIL_FORMAT_ERROR, userTask.getName());
                    }
                    String sender = MapUtil.instance.getString(emailExt, BPMConstants.SENDER);
                    if (!Strings.isNullOrEmpty(sender)){
                        if(!workflow.getServiceManager().checkEmailEnable(sender)){
                            throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SEND_EMAIL_SENDER_IS_INVALID, userTask.getName());
                        }
                    }
                }else if (BPMConstants.SEND_SMS.equals(reminder.getChannel())){
                    String content = reminder.getRemindContent();
                    String remindTemplate = reminder.getRemindTemplate();
                    if (Strings.isNullOrEmpty(content) && Strings.isNullOrEmpty(remindTemplate)) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_ISNULL, userTask.getName());
                    }
                    ValidateResult contentRst = ValidateResult.ok();
                    if (!Strings.isNullOrEmpty(content)){
                        contentRst = ValidateVariableAndContentUtils.validateContent(workflow, content, workflow.getPreActivityFun(userTask));
                    }
                    if (!contentRst.isValid()) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REMIND_CONTENT_EXCEPTION, userTask.getName(), contentRst.getMessage());
                    }
                }else {
                    String content = reminder.getRemindContent();
                    if (Strings.isNullOrEmpty(content)) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_ISNULL, userTask.getName());
                    }

                    String title = reminder.getRemindTitle();
                    if (Strings.isNullOrEmpty(title)) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_TITILE_ISNULL, userTask.getName());
                    }

                    ValidateResult titleRst = ValidateVariableAndContentUtils.validateContent(workflow, title, workflow.getPreActivityFun(userTask));
                    if (!titleRst.isValid()) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REMIND_TITLE_EXCEPTION, userTask.getName(), titleRst.getMessage());
                    }

                    ValidateResult contentRst = ValidateVariableAndContentUtils.validateContent(workflow, content, workflow.getPreActivityFun(userTask));
                    if (!contentRst.isValid()) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_REMIND_CONTENT_EXCEPTION, userTask.getName(), contentRst.getMessage());
                    }

                    AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_TIMEOUT_WARN_PERSON.text(userTask.getName()), userTask, reminder.getRemindTargets(), workflow);
                }

            });
        }
    }


    /**
     * @param remindLatency  ${activityid_32423423423423##object_fdsfsd__c##field__c}
     * @param activityExt
     * @param serviceManager
     */
    public static void remindLatencyExpressionValidate(Object remindLatency, ActivityExt activityExt, RefServiceManager serviceManager) {
        List<String> expressions = ExpressionUtil.getInternalExpressionByWell("" + remindLatency);
        if (CollectionUtils.isEmpty(expressions)) {
            throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ACTIVITY_REMIND_FORMAT_ERROR_BY_TASK, activityExt.getName());
        }
        expressions.forEach(expression -> {
            BPMFieldBean bpmFieldBean = BPMFieldBean.analysisFieldExpression(Splitter.on(BPMConstants.ENTITY_FIELD_SPLIT).splitToList(expression));
            if (bpmFieldBean != null) {
                boolean fieldInactive;
                //如果是关联对象的
                if (!Strings.isNullOrEmpty(bpmFieldBean.getLookupField())) {
                    Map<String, String> refEntityId = serviceManager.getRefEntityIdByEntityId(bpmFieldBean.getEntityId());
                    String lookupEntityId = refEntityId.get(bpmFieldBean.getMainField());
                    //如果lookup对象不存在了
                    if (Strings.isNullOrEmpty(lookupEntityId)) {
                        throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_FIELD_STOP_OR_DELETE, activityExt.getName());
                    }
                    fieldInactive = serviceManager.isFieldInactive(lookupEntityId, bpmFieldBean.getLookupField());
                } else {
                    //如果是本对象
                    fieldInactive = serviceManager.isFieldInactive(bpmFieldBean.getEntityId(), bpmFieldBean.getMainField());
                }

                if (fieldInactive) {
                    log.error("{} 节点设置允许停留时长{}字段已被禁用或者删除", activityExt.getName(), expression);
                    throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_FIELD_STOP_OR_DELETE, activityExt.getName());
                }
            } else {
                log.error("超时提醒表达式存在异常:" + expression);
            }
        });
    }


    /**
     * 如果是节点上 的允许停留时长是int类型,不需要解析字段
     *
     * @param remindLatency 节点上允许停留时长
     * @param userTask      节点信息
     * @param remindTime    超时前提醒设置的时间
     * @param latencyUnit   1-天；2-小时；3-分钟
     */
    private void remindLatencyNumberValidate(Object remindLatency, UserTaskExt userTask,  int remindTime, int latencyUnit, int remindTimeUnit) {
        //节点上允许停留时长
        Double remindLatencyNumber = Double.parseDouble(remindLatency + "");

        remindLatencyNumberValidate(remindLatency,userTask);
        //弹出框内设置的,只能是小时,整型
        Double remindTimeMinute = convertMinuteByUnit(Double.valueOf(Math.abs(remindTime)), remindTimeUnit);
        Double taskTimeMinute = convertMinuteByUnit(remindLatencyNumber, latencyUnit);

        //如果弹出框里的时间 大于  `等于` 允许停留时长,则抛出异常
        if (remindTimeMinute >= taskTimeMinute) {
            throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AFTER_REMIND_CONTENT_REMIND_TIME_ERROR, userTask.getName());
        }
    }

    /**
     * 根据时间单位将时间转换为分钟 1-天；2-小时；3-分钟  未知的时间类型默认用分钟
     * @param value
     * @param unit
     * @return
     */
    private Double convertMinuteByUnit(Double value, Integer unit){
        Double result = 0D;
        if(Objects.isNull(value) || Objects.isNull(unit)){
            return result;
        }
        switch (unit){
            case 1:
                return value * 24 * 60;
            case 2:
                return value * 60;
            default:
                return value;
        }
    }

    /**
     * 如果是节点上 的允许停留时长是int类型,不需要解析字段
     *
     * @param remindLatency 节点上允许停留时长
     * @param userTask      节点信息
     */
    protected void remindLatencyNumberValidate(Object remindLatency, UserTaskExt userTask) {
        //节点上允许停留时长
        Double remindLatencyNumber = Double.parseDouble(remindLatency + "");
        // 允许停留时长不能小于0
        if (remindLatencyNumber <= 0) {
            log.info("{} 节点允许停留时长未设置", userTask.getName());
            throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_THE_WAITING_TIME_SET_ERROR, userTask.getName());
        }
    }
}
