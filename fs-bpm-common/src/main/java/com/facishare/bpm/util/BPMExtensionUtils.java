package com.facishare.bpm.util;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Created by wangz on 17-1-17.
 */
@Slf4j
public class BPMExtensionUtils {
//    private static Pattern p = Pattern.compile("(activity_[0-9a-z]{1,})##.*");
    private static final String KEY_EXPRESSION = "expression";

    //expression:activity_99##AccountObj[##filedname]
    public static Expression transferExpression(Object objectIdExp) {
        if (objectIdExp == null || !Map.class.isInstance(objectIdExp) || ((Map) objectIdExp).isEmpty()) {
            return null;
        }

        String expressionStr = (String) ((Map) objectIdExp).get(KEY_EXPRESSION);
        return getExpression(expressionStr);

    }

    //activity_99##AccountObj[##filedname]
    public static Expression getExpression(String expStr) {
        Expression expression = new Expression();

        String[] keys = expStr.split("##");
        expression.setActivityId(keys[0].split("_")[1]);
        if (BPMConstants.SELF_REF_KEY.equals(keys[1])) {
            expression.setDescApiName(keys[2]);
            expression.setInstanceVariableKey(keys[0] + "##" + keys[1] + "##" + keys[2]);
            if (keys.length > 3) {
                expression.setFieldName(keys[3]);
            }
        } else {
            expression.setDescApiName(keys[1]);
            expression.setInstanceVariableKey(keys[0] + "##" + keys[1]);
            if (keys.length > 2) {
                expression.setFieldName(keys[2]);
            }
        }

        return expression;
    }

    @Getter
    @Setter
    public static class Expression {
        String instanceVariableKey; //对应 流程实例中自定义对象Id 的key activityId_3278423742##object_di9d__c
        String descApiName; //自定义对象的descApiName.元数据查找的时候用 object_di9d__c
        String fieldName; //当前表达式的字段名
        String activityId;
    }

    public static String getActivityRealId(String expression) {
        String[] keys = expression.split("##");
        return keys[0].split("_")[1];
    }

}
