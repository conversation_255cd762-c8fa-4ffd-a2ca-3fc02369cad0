package com.facishare.bpm.util.verifiy.handler.bean;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * desc:
 * author: cuiyongxu
 * create_time: 2021/10/9-3:28 PM
 **/
@Data
public class FunctionParam {
    String name;
    String type;
    String value;
    final static List<String> functionSupportParamTypes = Lists.newArrayList("String", "BigDecimal", "DateTime", "Date", "Time", "Boolean", "List", "Map");

    public boolean isValidType() {
        return (functionSupportParamTypes.contains(type));
    }
}
