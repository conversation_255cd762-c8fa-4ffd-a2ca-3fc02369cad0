package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.exception.BPMWorkflowNotFoundException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 6.3
 * 1. 数据id不能为空
 * 2. 业务流程id不能为空
 */
public class TriggerBpmValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        TriggerBpm triggerParam = JsonUtil.fromJson(JsonUtil.toJson(executionItem.getTriggerParam()), TriggerBpm.class);
        String activityName = getActivityName(activity);
        if (Strings.isNullOrEmpty(triggerParam.objectId)) {
            return new ValidateResult(false, BPMI18N.PAAS_FLOW_BPM_SELECT_TRIGGER_OBJECT.text(activityName));
        }
        if (Strings.isNullOrEmpty(triggerParam.id) && Strings.isNullOrEmpty(triggerParam.sourceWorkflowId)) {
            return new ValidateResult(false, BPMI18N.PAAS_FLOW_BPM_SELECT_WHICH_BPM.text(activityName));
        }
        try {
            workflow.getServiceManager().workflowIsExists(workflow.getTenantId(), triggerParam.id,triggerParam.sourceWorkflowId);
        } catch (BPMWorkflowDefVerifyException | BPMWorkflowNotFoundException e) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_AFTER_SETTING.text(activityName, e.getMessage()));
        }
        ValidateVariableAndContentUtils.validateEntities(
                workflow.getServiceManager(),
                Lists.newArrayList(triggerParam.getObjectId()),
                workflow.getPreActivityFun(activity));
        return ValidateResult.ok();
    }

    @Data
    class TriggerBpm {
        String objectId;
        String id;
        String sourceWorkflowId;
    }
}
