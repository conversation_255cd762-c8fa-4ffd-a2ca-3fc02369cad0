package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 6.3
 * 1. 分配人不为空 且 合法
 * 2. 抄送范围 需要合法
 * 3. 标题 不能为空
 * 4. 内容 不能为空
 * 5. 完成时间 不能为空
 */
public class FeedTaskValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        FeedTask feedTask = JsonUtil.fromJson(JsonUtil.toJson(executionItem.getActionMapping()), FeedTask.class);
        String activityName = getActivityName(activity);
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_SEND_TASK_ASSIGNOR.text(activityName), activity, feedTask.getAttenderEmployeeIds(), workflow);
        if (!UserTaskExt.hasAssignee(feedTask.getAttenderEmployeeIds())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_TASK_ASSIGNOR_NOT_EMPTY.text(activityName));
        }

        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_SEND_TASK_COPY_RANGE.text(activityName), activity, feedTask.getCarbonCopyEmployeeIds(), workflow);
        if (Strings.isNullOrEmpty(feedTask.getTitle())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_TASK_TITLE_NOT_EMPTY.text(activityName));
        }
        if (Strings.isNullOrEmpty(feedTask.getContent())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_TASK_CONTENT_NOT_EMPTY.text(activityName));
        }

        String deadLine = feedTask.getDeadLine() + "";
        if (Strings.isNullOrEmpty(deadLine)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_TASK_TIME_NOT_EMPTY.text(activityName));
        } else if (deadLine.contains("$")) {
            ValidateVariableAndContentUtils.validateContent(
                    workflow,
                    feedTask.getDeadLine() + "",
                    workflow.getPreActivityFun(activity));
        }

        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                feedTask.getTitle(),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_TASK_TITLE.text(activityName, rst.getMessage()));
            return rst;
        }
        rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                feedTask.getContent(),
                workflow.getPreActivityFun(activity)
        );
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_TASK_CONTENT.text(activityName, rst.getMessage()));
            return rst;
        }
        return rst;
    }

    @Data
    class FeedTask {
        //抄送范围
        Map<String, Set<Object>> attenderEmployeeIds;
        //分配给谁
        Map<String, Set<Object>> carbonCopyEmployeeIds;
        String content;
        Object deadLine;
        String title;
        List remindTimes;
    }
}
