package com.facishare.bpm.util.verifiy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValidateResult {
    private boolean valid;
    private String message;

    public static ValidateResult ok() {
        return new ValidateResult(true, "");
    }
    public static ValidateResult fail(String msg) {
        return new ValidateResult(false, msg);
    }
    public boolean isValid() {
        return this.valid;
    }
    public boolean isNotValid() {
        return !this.isValid();
    }

    public String getMessage() {
        return this.message;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}