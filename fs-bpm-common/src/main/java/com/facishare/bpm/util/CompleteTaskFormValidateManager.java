package com.facishare.bpm.util;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.utils.MapUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * desc:提交from的时候,需要检验下任务上的form和前端提交的form是否一致,且进行过滤
 * <p>
 * version: 6.6
 * Created by cuiyongxu on 2019/6/17 4:11 PM
 */
@Slf4j
public class CompleteTaskFormValidateManager {

    /**
     * 过滤前端传递的非法字段,返回需要更新的字段
     * __o,__r,字段有默认值,非只读字段,version不做过滤
     *
     * @return 需要更新的字段
     */
    public static Map<String, Object> getLegalUpdateFields(RefServiceManager serviceManager, String entityId,
                                                           Map<String, Object> updateFieldsOnForm,
                                                           List<List<Map<String, Object>>> forms,
                                                           Map<String, Object> fieldsDescribe, Boolean removeVersion) {

        if (MapUtils.isEmpty(updateFieldsOnForm) || CollectionUtils.isEmpty(forms)) {
            return updateFieldsOnForm;
        }
        //保存form上所有的字段,默认存在version
        Set<String> formFields = Sets.newHashSet(BPMConstants.MetadataKey.VERSION);
        //保存只读的form字段
        Map<String, Boolean> readOnlyFields = Maps.newHashMap();
        addEndTimeToDateTimeRangeGroup(forms);
        forms.forEach(form ->
                form.forEach(fieldFormInfo -> {
                    String name = MapUtil.instance.getString(fieldFormInfo, BPMConstants.MetadataKey.name);
                    Boolean readonly = MapUtil.instance.getBool(fieldFormInfo, BPMConstants.READONLY);
                    //若字段存在默认值时，不需要过滤
                    if (hasFieldDefaultValue(fieldsDescribe, name)) {
                        formFields.add(name);
                        return;
                    }
                    //非只读
                    readOnlyFields.put(name, readonly);
                    formFields.add(name);
                }));
        Map<String, Object> rst = updateFieldsOnForm.keySet().stream().filter(field -> {
            //1. 更新数据时,前端传递的data中,(key在定义的form中||存在__r|| 存在__o) && 非只读
            if ((formFields.contains(field) || field.contains(BPMConstants.MetadataKey.lookupFieldNameValuePostfix) ||
                    field.contains(BPMConstants.MetadataKey.otherFieldNameValuePostfix)|| field.contains(BPMConstants.MetadataKey.employeeListFieldNameValuePostfix)) && !isReadOnly(readOnlyFields, field)) {
                return true;
            }
            log.warn("remove form field:{}", field);
            return false;
        }).collect(Maps::newHashMap, (m, k) -> m.put(k, updateFieldsOnForm.get(k)), Map::putAll);
        if(Boolean.TRUE.equals(removeVersion) || (Objects.nonNull(serviceManager)&&SwitchConfigManager.ignoreDataVersion(serviceManager.getTenantId(),entityId))){
            rst.remove(BPMConstants.MetadataKey.VERSION);
        }
        return rst;
    }

    public static boolean hasFieldDefaultValue(Map<String, Object> fieldsDescribe, String field) {
        if (MapUtils.isEmpty(fieldsDescribe)) {
            return false;
        }
        Map<String, Object> fieldDesc = (Map<String, Object>) fieldsDescribe.get(field);
        if (MapUtils.isNotEmpty(fieldDesc)) {
            Object defaultValue = fieldDesc.get(BPMConstants.MetadataKey.DEFAULT_VALUE);
            if (Objects.nonNull(defaultValue)) {
                if (defaultValue instanceof String) {
                    if (StringUtils.isNotBlank((String) defaultValue)) {
                        return true;
                    }
                }
                if (defaultValue instanceof Collection) {
                    if (CollectionUtils.isNotEmpty((Collection) defaultValue)) {
                        return true;
                    }
                }
                if (defaultValue instanceof Map) {
                    if (MapUtils.isNotEmpty((Map) defaultValue)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 优化 不查描述创建修改的数据
     * @param updateFieldsOnForm
     * @param forms
     * @param serviceManager
     * @param entityId
     * @return
     */
    public static Map<String, Object> getLegalUpdateFields(Map<String, Object> updateFieldsOnForm,
                                                           List<List<Map<String, Object>>> forms, RefServiceManager serviceManager, String entityId, Boolean removeVersion) {

        if (MapUtils.isEmpty(updateFieldsOnForm) || CollectionUtils.isEmpty(forms)) {
            return updateFieldsOnForm;
        }
        //保存form上所有的字段,默认存在version
        Set<String> formFields = Sets.newHashSet(BPMConstants.MetadataKey.VERSION);

        addEndTimeToDateTimeRangeGroup(forms);
        forms.forEach(form ->
                form.forEach(fieldFormInfo -> {
                    formFields.add(MapUtil.instance.getString(fieldFormInfo, BPMConstants.MetadataKey.name));
                }));

        Map<String, Object> rst = updateFieldsOnForm.keySet().stream().filter(field -> {
            //1. 更新数据时,前端传递的data中,(key在定义的form中||存在__r|| 存在__o)
            if ((formFields.contains(field) || field.contains(BPMConstants.MetadataKey.lookupFieldNameValuePostfix) ||
                    field.contains(BPMConstants.MetadataKey.otherFieldNameValuePostfix) || field.contains(BPMConstants.MetadataKey.employeeListFieldNameValuePostfix))) {
                return true;
            }
            log.debug("remove form field:{}", field);
            return false;
        }).collect(Maps::newHashMap, (m, k) -> m.put(k, updateFieldsOnForm.get(k)), Map::putAll);

        if(Boolean.TRUE.equals(removeVersion) || (Objects.nonNull(serviceManager)&&SwitchConfigManager.ignoreDataVersion(serviceManager.getTenantId(),entityId))){
            rst.remove(BPMConstants.MetadataKey.VERSION);
        }
        return rst;
    }

    /**
     * 只读  return true
     * 非只读  return false
     * 单独提取个方法 是因为遍历updateData 时,有的key 没有在form中存在 获取到的值是null
     *
     * @param readOnlyFields
     * @param field
     * @return
     */
    public static boolean isReadOnly(Map<String, Boolean> readOnlyFields, String field) {
        //若以__r、__o结尾的字段，需要判断是否只读
        if (field.endsWith(BPMConstants.MetadataKey.lookupFieldNameValuePostfix) || field.endsWith(BPMConstants.MetadataKey.otherFieldNameValuePostfix)) {
            Boolean aBoolean = readOnlyFields.get(field.substring(0, field.length() - 3));
            return Objects.nonNull(aBoolean) && aBoolean;
        }
        Boolean aBoolean = readOnlyFields.get(field);
        //不为空 && true 表示只读
        return Objects.nonNull(aBoolean) && aBoolean;
    }


    @Data
    public static class ValidateFormButton{
        boolean showButton;
        boolean onlySignIn;
    }
    /**
     * 判断form button是否需要显示,比如只有签到组件的时候,不下发更新按钮,目前只针对签到组件
     * <p>
     * 业务流同一个form只支持一个签到组件和定位  type= location
     *
     * @param bpmExtension
     * @return
     */
    public static ValidateFormButton validateFormButtonIsShow(RefServiceManager serviceManager,
                                                              String entityId,
                                                              Map<String, Object> bpmExtension,
                                                              TaskParams taskParams) {

        if (MapUtils.isEmpty(bpmExtension) || CollectionUtils.isEmpty((Collection) bpmExtension.get(BPMConstants.FORM))) {
            return new ValidateFormButton();
        }

        boolean existsSignIn = false;

        //存储组字段(目前只针对签到)
        List<Field> groupFields = Lists.newArrayList();
        //存储普通字段
        List<String> nomalField = Lists.newArrayList();

        Map<String, Object> fields = serviceManager.getFields(entityId);

        List<List<Map>> forms = (List<List<Map>>) bpmExtension.get(BPMConstants.FORM);

        for (List<Map> form : forms) {
            for (Map map : form) {
                String key = (String) map.get(BPMConstants.MetadataKey.name);

                Map<String, Object> field = (Map<String, Object>) fields.get(key);
                String type = (String) field.get(BPMConstants.MetadataKey.type);
                String group_type = (String) field.get(BPMConstants.MetadataKey.group_type);
                //只检索签到组件和定位
                if (BPMConstants.MetadataKey.group.equals(type) && BPMConstants.MetadataKey.SIGN_IN.equals(group_type)) {
                    existsSignIn = true;
                    Map<String, String> signFields = (Map<String, String>) map.get(BPMConstants.MetadataKey.fields);
                    groupFields.add(Field.create(key, group_type, type, signFields));
                } else {
                    nomalField.add(key);
                }
            }
        }

        groupFields.forEach(groupField -> {
            //如果是签到组件
            if (BPMConstants.MetadataKey.SIGN_IN.equals(groupField.getGroupType())) {
                Map<String, String> signFields = groupField.getFields();
                if (MapUtils.isNotEmpty(signFields)) {
                    signFields.values().forEach(nomalField::remove);
                }
            }
        });
        ValidateFormButton validateFormButton = new ValidateFormButton();
        validateFormButton.setShowButton(!nomalField.isEmpty());

        //只有签到组件
        if (existsSignIn && nomalField.isEmpty()) {
            validateFormButton.setOnlySignIn(true);
        }
        return validateFormButton;
    }

    public static void addEndTimeToDateTimeRangeGroup(List<List<Map<String, Object>>> forms){
        if(CollectionUtils.isEmpty(forms)){
            return;
        }
        for (List<Map<String, Object>> form : forms) {
            if(CollectionUtils.isEmpty(form)){
                continue;
            }
            List<Pair<String, String>> startAndEndTimeField = Lists.newArrayList();
            for (Map<String, Object> item : form) {
                if(MapUtils.isEmpty(item)){
                    continue;
                }
                if(BPMConstants.MetadataKey.group.equals(item.get(BPMConstants.MetadataKey.type))
                        && "date_time_range".equals(item.get(BPMConstants.MetadataKey.group_type))
                        && item.get(BPMConstants.MetadataKey.fields) instanceof Map){
                    String startTimeField = (String) (((Map) item.get(BPMConstants.MetadataKey.fields)).get("start_time_field"));
                    String endTimeField = (String) (((Map) item.get(BPMConstants.MetadataKey.fields)).get("end_time_field"));
                    startAndEndTimeField.add(new Pair<>(startTimeField, endTimeField));
                }
            }
            if(CollectionUtils.isNotEmpty(startAndEndTimeField)){
                for (Pair<String, String> pair : startAndEndTimeField) {
                    String startTimeField = pair.getKey();
                    String endTimeField = pair.getValue();
                    if(StringUtils.isNotBlank(startTimeField) && StringUtils.isNotBlank(endTimeField)){
                        Optional<Map<String, Object>> startForm = form.stream().filter(f -> MapUtils.isNotEmpty(f) && startTimeField.equals(f.get(BPMConstants.MetadataKey.name))).findFirst();
                        if(startForm.isPresent()){
                            Map<String, Object> endForm = Maps.newHashMap(startForm.get());
                            endForm.put(BPMConstants.MetadataKey.name, endTimeField);
                            form.add(endForm);
                        }
                    }
                }
            }
        }
    }


    @Data
    static class Field {
        String apiName;
        String groupType;
        String type;
        Map<String, String> fields;

        public static Field create(String apiName, String groupType, String type, Map<String, String> fields) {
            Field field = new Field();
            field.setApiName(apiName);
            field.setGroupType(groupType);
            field.setFields(fields);
            field.setType(type);
            return field;
        }

    }
}
