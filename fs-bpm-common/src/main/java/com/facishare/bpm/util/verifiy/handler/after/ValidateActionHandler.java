package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;

/**
 * <AUTHOR>
 * @since 6.2
 */
public interface ValidateActionHandler {
    ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow);
    default String getActivityName(ActivityExt activity){
        return ActivityExt.getActivityName(activity);
    }

}
