package com.facishare.bpm.util.verifiy.handler;


import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 节点必需都处于阶段中
 * Created by cuiyongxu on 17/3/20.
 */
@Slf4j
public class NodeInStageHandler implements ValidateHandler {

    @Override
    public void validate(Workflow workflow) {
        //获取activityid:activity
        Map<String, ActivityExt> activityMap = workflow.getActivities().stream().collect(Collectors.toMap(ActivityExt::getId, activity -> activity));
        List<String> lanesActivitesAll = Lists.newArrayList();
        Workflow.WorkflowExtension extension = workflow.getExtension();
        if (extension == null) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_NON_EXIST_STAGE);
        }
        List<Workflow.PoolEntity> poolEntityList = extension.getPools();
        if (null == poolEntityList) {
            log.error("阶段信息不存在;可能存在节点数据,但无阶段数据,请查看提交JSON");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_STAGE_GET_INFO_ERROR);
        }
        poolEntityList.forEach(e -> {
            e.getLanes().forEach(p -> {
                if (CollectionUtils.isEmpty(p.getActivities())) {
                    //2019年01月11日 前端传递泳池name为空, 抛出异常为:null 阶段中没有节点，请确认
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_STAGE_NOT_FOUND_NODE, p.getName());
                }
                lanesActivitesAll.addAll(p.getActivities());
            });
        });
        activityMap.forEach((k, v) -> {
            if (!lanesActivitesAll.contains(k)) {
                log.error("{} 不在阶段中", v.getName());
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NOT_FOUND, v.getName());
            }
        });
    }


}
