package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.after.UserTaskRemindValidateHandler;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date on 2018/5/3
 * @since 6.3
 */
public class ExecutionTaskValidateHandler implements ValidateActivityHandler {
    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        //自动节点如果是延迟执行或是说条件执行，需要校验时间信息
        ExecutionTaskExt activityExecution = (ExecutionTaskExt) activity;
        if(activityExecution.getDelay()){
           Object remindLatency = activityExecution.get(WorkflowKey.ActivityKey.remindLatency);
            //可能是字符串或是数字   数字时间需要大于0
            if(Objects.isNull(remindLatency) || "".equals(remindLatency.toString())){
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_DELAY_NO_EXIST_TIME.text(activityExecution.getName()));
            }else if(remindLatency instanceof Number && Double.valueOf(remindLatency.toString()) <= 0){
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_DELAY_TIME_LESS_ZERO.text(activityExecution.getName()));
            }else if(remindLatency instanceof String){
                //校验表达式
                UserTaskRemindValidateHandler.remindLatencyExpressionValidate(remindLatency, activity, workflow.getServiceManager());
            }
        }
        return ValidateResult.ok();
    }
}
