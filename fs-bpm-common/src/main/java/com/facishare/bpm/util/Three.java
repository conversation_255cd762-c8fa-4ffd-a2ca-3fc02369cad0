package com.facishare.bpm.util;

/**
 * Created by <PERSON> on 08/03/2017.
 */
public class Three<K,V,T> {
    private K one;
    private V two;
    private T three;

    public Three(K one, V two,T three) {
        this.one = one;
        this.two=two;
        this.three=three;
    }

    public Three() {
    }

    public K getOne() {
        return one;
    }

    public void setOne(K one) {
        this.one = one;
    }

    public V getTwo() {
        return two;
    }

    public void setTwo(V two) {
        this.two = two;
    }

    public T getThree() {
        return three;
    }

    public void setThree(T three) {
        this.three = three;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Three)) {
            return false;
        }

        Three<?, ?, ?> three1 = (Three<?, ?, ?>) o;

        if (getOne() != null ? !getOne().equals(three1.getOne()) : three1.getOne() != null) {
            return false;
        }
        if (getTwo() != null ? !getTwo().equals(three1.getTwo()) : three1.getTwo() != null) {
            return false;
        }
        return getThree() != null ? getThree().equals(three1.getThree()) : three1.getThree() == null;
    }

    @Override
    public int hashCode() {
        int result = getOne() != null ? getOne().hashCode() : 0;
        result = 31 * result + (getTwo() != null ? getTwo().hashCode() : 0);
        result = 31 * result + (getThree() != null ? getThree().hashCode() : 0);
        return result;
    }
}
