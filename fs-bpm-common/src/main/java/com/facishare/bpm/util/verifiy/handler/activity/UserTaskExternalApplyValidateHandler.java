package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMUserTaskExternalApplyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * cuiyx 2019年05月22日11:58:21
 */
@Slf4j
public class UserTaskExternalApplyValidateHandler implements ValidateActivityHandler {

    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        RefServiceManager serviceManager = workflow.getServiceManager();
        UserTaskExt userTaskExt = (UserTaskExt) activity;
        String taskName = userTaskExt.getName();

        Boolean isExternalFlow = workflow.isExternalFlow();
        String entityId = activity.getEntityId();


        Map<String, Object> bpmExtension = userTaskExt.getExtension();


        Map<String, Object> externalApplyMap = (Map<String, Object>) bpmExtension.get(BPMConstants.EXTERNAL_APPLY);
        if (MapUtils.isEmpty(externalApplyMap)) {
            log.warn("externalApply 未设置,tenantId:{},taskName:{}", serviceManager.getTenantId(), taskName);
            throw new BPMUserTaskExternalApplyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_EXTERNALAPPLY_PARAMS_NOT_SETTING, taskName); //应用节点 taskName  参数配置不完整，请检查

        }

        //745 添加逻辑校验  选择了应用节点 ,业务流配置定义首页必须选择具体隶属于哪个应用
        //http://wiki.firstshare.cn/pages/viewpage.action?pageId=141944136  由 郭明创建, 最终由 瞿瑞斌修改于 2021-01-28
        //1. 外部流程  默认设置appId=FSAID_11490ebc
        //2. 互联流程  定义配置首页选择的appId
        //3. 内部流程  不允许出现应用节点
        if (workflow.isLinkAppEnable()) {
            if (Strings.isNullOrEmpty(workflow.getLinKApp())) {
                throw new BPMUserTaskExternalApplyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NON_EXTERNAL_OR_INTERCONNECTED_PROCESSE_NOT_SUPPORT_EXTERNAL_APPLY);
            }
            activity.put("linkApp", workflow.getLinKApp());
            activity.put("linkAppName", workflow.getLinkAppName());
            activity.put("linkAppEnable", workflow.getLinkAppEnable());
            activity.put("linkAppType", workflow.getLinKAppType());
        }
        ExternalApply externalApply = JacksonUtil.fromJson(JacksonUtil.toJson(externalApplyMap), ExternalApply.class);

        String actionCode = externalApply.getActionCode();
        String childrenActionCode = externalApply.getChildrenActionCode();


        if (Strings.isNullOrEmpty(actionCode) && Strings.isNullOrEmpty(childrenActionCode)) {
            log.warn("actionCode及childrenActionCode两者均未设置,tenantId:{},taskName:{}", serviceManager.getTenantId(), taskName);
            throw new BPMUserTaskExternalApplyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_NOT_SETTING,taskName); //应用节点 taskName  未配置具体执行的事情，请检查
        }

        Map<String, String> actionCodes = serviceManager.getAppActions(entityId, isExternalFlow,workflow.getLinKApp(),workflow.getLinKAppType());
        if (!Strings.isNullOrEmpty(actionCode) && !actionCodes.containsKey(actionCode)) {
            log.warn("actionCode不存在或已删除,tenantId:{},taskName:{},actionCode:{}", serviceManager.getTenantId(), taskName, actionCode);
            throw new BPMUserTaskExternalApplyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_OFFLINE,taskName,externalApply.getActionName());
        }

        if (!Strings.isNullOrEmpty(childrenActionCode) && !actionCodes.containsKey(childrenActionCode)) {
            log.warn("childrenActionName不存在或已删除,tenantId:{},taskName:{},actionCode:{}", serviceManager.getTenantId(), taskName, childrenActionCode);
            throw new BPMUserTaskExternalApplyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_EXTERNALAPPLY_ACTIONCODE_OFFLINE,taskName,externalApply.getChildrenActionName());
        }
        return ValidateResult.ok();
    }


    @Data
    private static class ExternalApply {
        private String actionCode;
        private String actionName;
        private String appCode;
        private String appName;
        private String childrenActionCode;
        private String childrenActionName;
    }
}
