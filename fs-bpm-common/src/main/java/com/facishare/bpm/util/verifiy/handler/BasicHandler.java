package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessException;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMDeployException;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.bpm.exception.BPMBusinessExceptionCode.APPLICANT_WHEN_SYSTEM_USER_NOT_SUPPORT;

/**
 * <AUTHOR>
 * @since 5.7
 */
public class BasicHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        Workflow.isBlank(workflow.getName(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_INSTANCE_NAME_REQUIRED);
        Workflow.isBlank(workflow.getEntryType(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_ENTRY_TYPE_REQUIRED);
        Workflow.isBlank(workflow.getEntryTypeName(), BPMBusinessExceptionCode.PAAS_FLOW_BPM_ENTRY_NAME_REQUIRED);
        validateApplicantWhenSystemUser(workflow.getApplicantWhenSystemUser());
        validateEntryType(workflow);
        validateAssignee(workflow);
        validateExternalFlowAndLinkApp(workflow);
    }
    private static final List<String> supportApplicantWhenSystemUser= Lists.newArrayList("extUserType","person");
    public void validateApplicantWhenSystemUser(Map<String, List<String>> applicantWhenSystemUser) {
        if (applicantWhenSystemUser != null) {
            applicantWhenSystemUser.keySet().forEach(item->{
                if (!supportApplicantWhenSystemUser.contains(item)) {
                    throw new BPMBusinessException(APPLICANT_WHEN_SYSTEM_USER_NOT_SUPPORT,item);
                }
            });
        }
    }

    private void validateAssignee(Workflow workflow) {
        AssigneeHandler.validateAssignee(workflow.getRangeAssignee(), workflow.getServiceManager(), BPMI18N.PAAS_FLOW_BPM_RANGE_ASSIGNEE.text());
    }

    private void validateEntryType(Workflow workflow) {
        validateEntryTypeExist(workflow);
        validateEntryTypeIsSingle(workflow);
    }

    /**
     * 验证入口对象的唯一性
     * 处理 在变量中出现 两个activity_0## 入口对象的问题
     */
    private void validateEntryTypeIsSingle(Workflow workflow) {
        String start = "activity_0##";
        List<String> variables = workflow.getVariablesList();
        List<String> entryTypes = variables.stream().filter(item -> {
            if (item.startsWith(start)) {
                String[] temp = item.split("##");
                if (temp.length == 2) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        if (entryTypes.size() > 1) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_BASE_INLET_OBJECT_MANY, entryTypes.size());
        }
    }

    /**
     * 验证入口对象是否还存在
     */
    private void validateEntryTypeExist(Workflow workflow) {


        try {
            Map<String, Object> desc = workflow.getServiceManager().getDescribe(workflow.getEntryType());
            if (desc == null) {
                throw new BPMDeployException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_RELATION_OBJECT_NOT_FOUND);
            }
        } catch (RestProxyRuntimeException e) {
            throw new BPMDeployException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_RELATION_OBJECT_NOT_FOUND);
        }
    }

    /**
     * 当前流程配置了互联:
     * 1. 外部流程不能一起使用
     * 2. linkApp不能为空
     * 3. 定义未启用互联应用，则节点也不允许有互联应用
     */
    private void validateExternalFlowAndLinkApp(Workflow workflow) {
        if (workflow.isLinkAppEnable()) {
            //互联应用与外部流程不能一起使用
            if (workflow.isExternalFlow()) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NOT_TOGETHER_EXTERNAL_LINK);
            }
            //linkApp不能为空
            if (StringUtils.isBlank(workflow.getLinKApp())) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_CAN_NOT_BE_EMPTY);
            }
        } else {
            //定义未启用互联应用，则节点也不允许有互联应用
            List<UserTaskExt> userTasks = workflow.getUserTasks();
            userTasks.forEach(userTask -> {
                if (userTask.isLinkAppEnable()) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_ILLEGAL, userTask.getName());
                }
            });
        }
    }
}
