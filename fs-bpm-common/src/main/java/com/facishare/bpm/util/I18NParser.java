package com.facishare.bpm.util;

import com.facishare.bpm.utils.ExpressionUtil;
import com.facishare.bpm.utils.i18n.*;
import com.facishare.paas.I18N;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description : todo 返回其本身
 * <AUTHOR> cuiyongxu
 * @Date : 2021/12/16-5:49 下午
 **/
@Slf4j
public class I18NParser {

    private static final String DEF_REGEX = "\\$\\{(.+?)}";


    public static void parse(String tenantId, Object object) {
        parse(tenantId, object, I18NParseContext.of());
    }

    /**
     * 此种方式适用于当前对象包含workflowId|| activityId||transitionId
     * ${workflow_id}.name
     * ${workflow_id.activityId}.name
     * ${workflow_id.transitionId}.name
     * ${workflow_id.activitie_id}.UpdateAndComplete
     * <p>
     * context 的目的是缓存当前数据上下文信息
     * <p>
     * tenantId  企业id
     * object 需要做转换的数据,可能是对象;可能是列表;可能是map;可能是对象中的下钻列表;可能是对象上的下钻map
     *
     * @return
     */
    public static void parse(String tenantId, Object object, I18NParseContext context) {
        StopWatch stopWatch = StopWatch.createUnStarted("i18n parser");

        if (object == null || Strings.isEmpty(tenantId)) {
            return;
        }
        //  对象下的list
        if (object instanceof Collection) {
            ((Collection<?>) object).forEach(item -> {
                parse(tenantId, item, context);
            });
            stopWatch.lap("each");
        } else if (object instanceof Map) {
            ((Map<?, ?>) object).forEach((key, value) -> {
                parse(tenantId, value, context);
            });
            stopWatch.lap("map");
        } else {
            try {
                //统计执行次数
                context.next(object);
                Class<?> objectClass = object.getClass();
                Field[] declaredFields = (Field[]) ArrayUtils.addAll(objectClass.getDeclaredFields(), objectClass.getFields());
                for (Field field : declaredFields) {
                    field.setAccessible(true);
                    I18NExpression annotation = field.getAnnotation(I18NExpression.class);
                    if (annotation == null) {
                        continue;
                    }

                    /**
                     * 表达式内容
                     * ${workflow_id}.name
                     * ${workflow_id.activityId}.name
                     * ${workflow_id.transitionId}.name
                     * ${workflow_id.activitie_id}.UpdateAndComplete
                     */
                    String[] relations = annotation.relation();

                    //是否需要下钻遍历
                    if (annotation.drill()) {
                        Object o = field.get(object);
                        if (Objects.nonNull(o)) {
                            if (context.depthNext()) {
                                return;
                            }
                            parse(tenantId, o, context);
                        }
                    } else if (CustomI18NHandler.none != annotation.custom()) {
                        CustomI18nProcessor.custom(annotation.custom(),tenantId,object,field);
                    } else if (ArrayUtils.isNotEmpty(relations)) {
                        log.debug("relations:{}", relations);

                        //获取内部表达式
                        //workflow_id
                        //workflow_id.activityId
                        //workflow_id.transitionId
                        //${workflow_id.activityId}.name  --->  workflow_id.activityId
                        List<String> relationNameList = Lists.newArrayList();
                        for (String relation : relations) {
                            String relationName = ExpressionUtil.getInternalExpression(relation);
                            if(StringUtils.isNotBlank(relationName)){
                                relationNameList.add(relationName);
                            }
                        }
                        if (CollectionUtils.isEmpty(relationNameList)) {
                            continue;
                        }
                        Map<String, Object> data = getValue(object, context, objectClass, relationNameList);

                        //渲染数据内容 例如: workflowid.name -> flow.5ecc7b395b03870001ce6ada.name
                        //渲染数据内容 例如: workflowid.activityId.name -> flow.5ecc7b395b03870001ce6ada.1590459216432.name
                        List<String> renderList = render(relations, data);

                        String text = parse(tenantId, renderList);
                        log.debug("language:{},key:{},render:{},value:{}", I18N.getContext().getLanguage(), relations, renderList, text);
                        if (!Strings.isEmpty(text)) {
                            field.set(object, text);
                        }
                    }
                    stopWatch.lap("set field");
                }
            } catch (Exception e) {
                log.warn("", e);
            }
        }
        stopWatch.logSlow(1000);
    }

    /**
     * 初始化变量
     *
     * @param object      数据信息
     * @param context     上下文公用信息
     * @param objectClass 当前类
     * @param relationList    表达式内容  ${a.b}.c  => a.b  或  ${a}.b => a
     * @return data
     */
    private static Map<String, Object> getValue(Object object, I18NParseContext context, Class<?> objectClass, List<String> relationList) throws IllegalAccessException {
        //存储 workflow_id || activityId ||transitionId 的值
        Map<String, Object> data = Maps.newHashMap();
        for (String relation : relationList) {
            //[workflowId,activitId]或 [a,b,c,d...n]  此处只有变量
            //按照顺序拆分 即 workflowId.acitivtyId ...n; 则 index 0;index 1;...n
            List<String> fields = Splitter.on(".").omitEmptyStrings().trimResults().splitToList(relation);
            //[workflowId]
            //[workflowId,activityId]
            //[workflowId,activityId,code]
            for (String fieldName : fields) {
                Field field = null;
                Object value = null;
                Map<Object, Object> aliases = Maps.newHashMap();
                try {

                    //fieldName = action  (ActionButton)
                    Field declaredField = null;
                    try {
                        declaredField = objectClass.getDeclaredField(fieldName);
                    }catch (NoSuchFieldException e){
                        declaredField = objectClass.getField(fieldName);
                    }
                    if (Objects.isNull(declaredField)) {
                        throw new NoSuchFieldException();
                    }
                    declaredField.setAccessible(true);
                    field = declaredField;

                    Annotation[] annotation = field.getAnnotations();
                    for (Annotation annt : annotation) {
                        if (annt instanceof I18NAliases) {
                            I18NAliases ans = (I18NAliases) annt;
                            for (I18NAlias an : ans.value()) {
                                aliases.put(an.value(), an.alias());
                            }
                        }
                    }
                } catch (NoSuchFieldException e) {
                    //如果字段不存在,则从源数据中查询,下钻数据 只会从第一层中获取数据,不会存在两个下钻,相互之间引用的情况
                    Object source = context.getSource();
                    if (source != null) {
                        try {
                            Class<?> sourceClass = source.getClass();
                            Field sourceField = null;
                            try {
                                sourceField = sourceClass.getDeclaredField(fieldName);
                            }catch (NoSuchFieldException noSuchFieldException){
                                sourceField = sourceClass.getField(fieldName);
                            }
                            if(Objects.isNull(sourceField)){
                                throw new NoSuchFieldException();
                            }
                            sourceField.setAccessible(true);
                            value = sourceField.get(source);
                        } catch (NoSuchFieldException iteme) {
                            log.warn("depth field not found");
                        }
                    }
                }


                //如果当前数据上存在该字段,则从数据上获取字段值
                //如果存在下钻,field可能为空
                if (Objects.nonNull(field)) {
                    value = field.get(object);
                }
                //如果值不为空,则判断下是否需要走别名逻辑
                if (Objects.nonNull(value)) {
                    //从aliases 通过字段值获取别名
                    value = aliases.getOrDefault(value, value);
                    data.put(fieldName, value);
                }
            }
            if (fields.size() > 1) {
                data.put(relation, Joiner.on(".").useForNull("").join(fields.stream().map(data::get).collect(Collectors.toList())));
            }
        }
        return data;
    }


    private static List<String> render(String[] parseTexts, Map<String, Object> data) {
        if (ArrayUtils.isEmpty(parseTexts)) {
            return Lists.newArrayList(parseTexts);
        }

        data.forEach((name, value) -> {

            //value = null 需要和产品沟通,如果获取到的值为空,原样返回还是设置为""
            if (value == null) {
                value = "";
            }

            String itemString = "";
            if (value instanceof List) {
                List<?> itemList = (List<?>) value;
                if (CollectionUtils.isNotEmpty(itemList)) {
                    itemString = Joiner.on(",").join(itemList);
                }
            } else {
                itemString = String.valueOf(value);
            }
            //list 转成 a,b,c
            //long 转成 string
            //int 转成 string
            //double 转 string
            //.....
            // 如果key中没有 . 并且值为空 则原样返回.存在的变量可能是 ${Name}
            if (!name.contains(".") && com.google.common.base.Strings.isNullOrEmpty(itemString) && !name.contains("##")) {
                itemString = "\\$\\{" + name + "}";
            }


            data.put(name, itemString);
        });

        List<String> result = Lists.newArrayList();
        for (String parseText : parseTexts) {
            try {
                StringBuffer sb = new StringBuffer();
                Pattern pattern = Pattern.compile(DEF_REGEX);
                Matcher matcher = pattern.matcher(parseText);
                while (matcher.find()) {
                    // 键名
                    String name = matcher.group(1);
                    // 键值
                    String value = String.valueOf(data.get(name));
                    value = Matcher.quoteReplacement(value);
                    matcher.appendReplacement(sb, value);
                }
                matcher.appendTail(sb);
                result.add("flow." + sb.toString());
                continue;
            } catch (Exception e) {
                log.warn("解析字段异常:", e);
            }
            result.add(parseText);
        }
        return result;

    }


    public static String parse(String tenantId, List<String> keyList) {
        if(CollectionUtils.isEmpty(keyList)){
            return null;
        }
        String language = I18N.getContext().getLanguage();
        for (String key : keyList) {
            //数据渲染
            Localization localization = I18nClient.getInstance().get(key, Long.parseLong(tenantId));
            if (localization == null) {
                continue;
            }
            String result = localization.get(language, false);
            if(StringUtils.isNotBlank(result)){
                return result;
            }
        }
        return null;

    }

}
