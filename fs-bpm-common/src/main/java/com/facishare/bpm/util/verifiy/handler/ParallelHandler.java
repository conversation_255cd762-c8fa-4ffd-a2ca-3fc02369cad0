package com.facishare.bpm.util.verifiy.handler;


import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ParallelGatewayExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 并行网关校验 A ->  Aょ
 * Created by cuiyongxu on 17/3/29.
 */
@Slf4j
public class ParallelHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        List<ActivityExt> parallels = workflow.getExecutableWorkflow().getActivities().stream().filter(activity -> activity.instanceOf(ParallelGatewayExt.class)).collect(Collectors.toList());
        if(parallels.size()%2!=0){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_GATEWAY_NEED_APPEAR_IN_PAIRS);
        }
    }


}
