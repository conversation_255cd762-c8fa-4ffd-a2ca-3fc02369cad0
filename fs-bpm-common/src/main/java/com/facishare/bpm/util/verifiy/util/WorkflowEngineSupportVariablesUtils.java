package com.facishare.bpm.util.verifiy.util;


import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @Date on 2018/5/26
 * @since 6.3
 * @IgnoreI18n
 */
@Slf4j
public class WorkflowEngineSupportVariablesUtils {
    enum WorkflowSupportVariable {
        taskCompletedTime("任务完成时间"),
        taskName("任务名称"),
        remindTime("超时前后**分钟/小时"),
        remindUnit("超时时间单位分钟(3)/小时(2)"),
        latencyTime("任务超时时限(小时)"),
        workflowName("流程实例名称"),
        workflowInstanceStartTime("流程实例开始时间"),
        applicantName("流程发起人"),
        currentUserId("当前操作人"),
        cancelReason("终止原因"),
        taskId("任务Id"),
        workflowInstanceId("实例Id");
        private String desc;

        WorkflowSupportVariable(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("");
            sb.append(this.name()).append(" : ").append(desc);
            return sb.toString();
        }
    }

    public static boolean isSupport(String variable) {
        try {
            WorkflowSupportVariable.valueOf(variable);
        } catch (IllegalArgumentException e) {
            log.info("目前流程实例只支持这些变量:{},not support :{}", WorkflowSupportVariable.values(),variable);
            return false;
        }
        return true;
    }
    public static boolean isNotSupport(String variable) {
        return !isSupport(variable);
    }

    public static void main(String[] args) {
        isSupport("taskNames");
    }
}
