package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**`
 * <AUTHOR>
 * @Date on 2018/5/3
 * @since 6.3
 */
public class UserTaskUpdateValidateHandler implements ValidateActivityHandler {
    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        List<List<Map<String, Object>>> forms = activity.getFrom();
        if (CollectionUtils.isNotEmpty(forms)) {
            String entityId = activity.getEntityId();
            ///如果是编辑关联字段的对象entityId是从字段上取的
            if(ExecutionTypeEnum.updateLookup.equals(activity.getExtensionType())){
                String fieldApiName = (String) activity.getExtension().get(WorkflowKey.ActivityKey.ExtensionKey.fieldApiName);
                if (StringUtils.isBlank(fieldApiName)){
                    return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_RELATION_FIELD.text(activity.getName()));
                }
                Map<String, Object> entityFieldMap = (Map<String, Object>) workflow.getServiceManager().getFields(entityId).get(fieldApiName);
                if(Objects.isNull(entityFieldMap) || Boolean.FALSE.equals(entityFieldMap.get(BPMConstants.MetadataKey.isActive)) || StringUtils.isBlank((String)entityFieldMap.get(BPMConstants.MetadataKey.type))){
                    return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VERIFIY_FLOW_RELATION_FIELD_DELETED.text(activity.getName()));
                }
                entityId = (String)entityFieldMap.get(BPMConstants.MetadataKey.targetApiName);
            }
            Map<String, Object> describe = workflow.getServiceManager().findDescribe(entityId, true, false);
            if (MapUtils.isNotEmpty(describe)) {
                Map<String, Object> fields = (Map<String, Object>) describe.get(BPMConstants.MetadataKey.fields);
                Map<String, Map<String, Object>> formFieldDescribe = new HashMap<>();
                Map<String, String> formFieldLabel = new HashMap<>();

                forms.forEach(formSet -> formSet.stream().filter(Objects::nonNull).forEach(temp -> {
                    String apiName = String.valueOf(temp.get(WorkflowKey.ActivityKey.ExtensionKey.name));
                    Map<String, Object> fieldDescribe = (Map<String, Object>) fields.get(apiName);
                    formFieldDescribe.put(apiName, fieldDescribe);
                    formFieldLabel.put(apiName, String.valueOf(temp.get(WorkflowKey.ActivityKey.ExtensionKey.label)));
                }));

                for (String field : formFieldDescribe.keySet()) {
                    Map<String, Object> fieldDesc = formFieldDescribe.get(field);
                    if (MapUtils.isEmpty(fieldDesc)) {
                        return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_FORM_FIELD_DOES_NOT_EXIST.text(activity.getName(),formFieldLabel.get(field)));
                    }
                    Boolean isActive = (Boolean) fieldDesc.get(BPMConstants.MetadataKey.isActive);
                    if (Objects.nonNull(isActive) && !isActive) {
                        return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_FORM_FIELD_BEEN_DISABLED.text(activity.getName(),formFieldLabel.get(field)));
                    }
                }
            }
        }
        return ValidateResult.ok();
    }
}
