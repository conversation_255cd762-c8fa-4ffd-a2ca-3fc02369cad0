package com.facishare.bpm.util.verifiy;

import com.facishare.bpm.util.verifiy.handler.*;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 1. 节点必须在阶段中
 * 2. 开始节点有且只能有一根连出的线， 不允许有连入的线
 * 3. 业务活动节点有且只能有一根连出的线， 至少有一根连入的线
 * 4. 审批/会签节点至少有一根连入的线， 有且只有两个连出的线
 * 5. 分支节点， 至少有一根连入的线， 至少有一个连出的线
 * 6. 结束节点， 有至少有一根连入的线， 不允许有连出的线
 * 7. 所有节点属性， 校验必填、长度、业务规则的，遵循表单规范和PRD文档
 */

@Slf4j
public enum VerifyManager {
    instance;
    private String config = "fs-bpm-verify";
    private List<ValidateHandler> handlers = Lists.newArrayList();

    VerifyManager() {
        init();
    }

    public void execute(Workflow workflow) {
        log.info("-------------------start-----------------------");
        handlers.forEach(handler -> {
            log.info("start -----{}---- validate ", handler.getClass().getSimpleName());
            handler.validate(workflow);
            log.info("end -----{}---- validate ", handler.getClass().getSimpleName());
        });
        log.info("---------------------end-----------------------");
    }


    public void init() {
        ConfigFactory.getConfig(config, (IConfig config) ->
                reloadHandlers(config.get("verify", ""))
        );
    }

    private void reloadHandlers(String verifyStr) {

        String[] verifies = verifyStr.split(",");

        handlers.clear();

        addHanlder(new VerifyInitHandler());
        addHanlder(new RuleHandler());

        if (verifies.length > 0) {
            for (String verify : verifies) {
                if (Strings.isNullOrEmpty(verify)) {
                    break;
                }
                try {
                    addHanlder(ValidateCommand.valueOf(verify).command);
                } catch (IllegalArgumentException e) {
                    if (null != log) {
                        log.warn("请检查 validate command 的正确性 : {},此异常不干扰校验", e.getMessage());
                    } else {
                        Logger log = LoggerFactory.getLogger(this.getClass());
                        log.warn("请检查 validate command 的正确性 :{}, {},此异常不干扰校验", verify, e.getMessage());
                    }
                }
            }
        }

        if (handlers.size() == 2) {
            Enum[] ena = ValidateCommand.class.getEnumConstants();
            for (int i = 0; i < ena.length; i++) {
                addHanlder(ValidateCommand.valueOf(ena[i].name()).command);
            }
        }
    }


    //{
    //将所有的userTask提出,将所有activityId提出,将所有的variablesId提出
    //addHanlder(new VerifyInitHandler());
    //校验节点审批人
    //addHanlder(new AssigneeHandler());
    //校验extension常量字段,entityId,entityName,executionType,objectId
    //addHanlder(new ExtensionHandler());
    //校验taskType和actionCode
    //addHanlder(new ExecutionTypeHandler());
    //校验form内容,将form中result的表达式提取出去
    //addHanlder(new FormHandler());
    //审批节点或者 分支节点,transitions上面必需有表达式
    //addHanlder(new ConditionHandler());

    //}

    private void addHanlder(ValidateHandler hanlder) {
        handlers.add(hanlder);
    }

    enum ValidateCommand {
        pool(new StageAndPoolHandler()),
        rule(new StageAndPoolHandler()),
        activity(new ActivityHandler()),
        executionItem(new AfterActionHandler()),
        assignee(new AssigneeHandler()),
        extension(new ExtensionHandler()),
        executionType(new ExecutionTypeHandler()),
        form(new FormHandler()),
        condition(new ConditionHandler()),
        nodeInStage(new NodeInStageHandler()),
        nodeTransition(new NodeTransitionHandler()),
        cycle(new CycleHandler()),
        parallel(new ParallelHandler()),
        basic(new BasicHandler());


        ValidateCommand(ValidateHandler command) {
            this.command = command;
        }

        ValidateHandler command;
    }

}
