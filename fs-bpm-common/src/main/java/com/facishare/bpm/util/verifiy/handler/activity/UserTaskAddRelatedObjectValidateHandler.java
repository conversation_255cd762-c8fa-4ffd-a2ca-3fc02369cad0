package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.MapUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date on 2018/5/3
 * @since 6.3
 */
@Slf4j
public class UserTaskAddRelatedObjectValidateHandler implements ValidateActivityHandler {

    private ExecutionTypeEnum executionType;

    public UserTaskAddRelatedObjectValidateHandler(ExecutionTypeEnum executionType) {
        this.executionType = executionType;
    }

    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        RefServiceManager serviceManager = workflow.getServiceManager();
        UserTaskExt userTask = (UserTaskExt) activity;
        return validateRelatedObject(workflow, serviceManager, userTask);
    }

    private ValidateResult validateRelatedObject(Workflow workflow, RefServiceManager serviceManager, UserTaskExt userTask) {
        String entityId = userTask.getEntityId();
        String entityName = userTask.getEntityName();
        String relatedOrMasterDetailEntityId = userTask.getRelatedEntityId();
        String relatedOrMasterDetailListName = userTask.getRelatedListName();
        String name = ActivityExt.getActivityName(userTask);


        //原是直接抛出异常,现在为空的话抛出的异常添加节点名称
        //Map<String, Object> relatedEntityDesc = serviceManager.getDescribe(relatedOrMasterDetailEntityId);
        Map<String, Object> relatedEntityDesc = serviceManager.findDescribe(relatedOrMasterDetailEntityId, true, false);
        if (relatedEntityDesc == null || !MapUtil.instance.getBool(relatedEntityDesc, BPMConstants.MetadataKey.isActive)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT_ALREADY_DELETE.text(userTask.getName()));
        }

        String relatedEntityName = relatedEntityDesc.get(BPMConstants.MetadataKey.displayName).toString();

        List<Map<String, Object>> referenceFields = getObjectRefAndMasterDetail(relatedEntityDesc, entityId, BPMConstants.MetadataKey.objectReference);
        log.info("validateRelatedObject:entityId:{},relatedEntityId:{},relatedListName:{},referenceFields:{}"
                , entityId, relatedOrMasterDetailEntityId, relatedOrMasterDetailListName, referenceFields);

        for (Map<String, Object> referenceField : referenceFields) {
            if (relatedOrMasterDetailListName.equals(referenceField.get(BPMConstants.MetadataKey.relatedListName))) {
                if (referenceField.get(BPMConstants.MetadataKey.isActive).equals(false)) {
                    return ValidateResult.fail(
                            BPMI18N.PAAS_FLOW_BPM_NODE_FIELD_IS_NOT_ACTIVE.text(
                                    name,
                                    BPMI18N.PAAS_FLOW_BPM_LOOKUP_OBJECT.text(),
                                    relatedEntityName,
                                    referenceField.get(BPMConstants.MetadataKey.label))
                    );
                }
                return ValidateResult.ok();
            }
        }

        if (ExecutionTypeEnum.batchAddRelatedObject.equals(executionType)) {
            // 批量新建不能包含  relatedObjectId
            if (userTask.getExtension().containsKey("relatedObjectId")) {
                log.warn("批量新建关联对象不能包含relatedObjectId,{}", userTask.getExtension().get("relatedObjectId"));
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_BATCH_TASK_DATA_STRUCTURE_FAIL.text(userTask.getName()));
            }
            //activity_1591270199434##object_UPfNh__c
            //variables 中不能存在选择新建对应的activity key
            String variablesKey = "activity_" + relatedOrMasterDetailEntityId;
            if (workflow.getVariablesList().contains(variablesKey)) {
                log.warn("批量新建关联对象的variables中不能包含当前关联对象的key:{}", variablesKey);
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_BATCH_TASK_DATA_STRUCTURE_FAIL.text(userTask.getName()));
            }
        }


        // 从对象的校验
        if (ExecutionTypeEnum.addMDObject.equals(executionType)) {
            List<Map<String, Object>> masterDetailFields = getObjectRefAndMasterDetail(relatedEntityDesc, entityId, BPMConstants.MetadataKey.masterDetail);
            for (Map<String, Object> masterDetail : masterDetailFields) {
                if (relatedOrMasterDetailListName.equals(masterDetail.get(BPMConstants.MetadataKey.relatedListName))) {
                    if (masterDetail.get(BPMConstants.MetadataKey.isActive).equals(false)) {
                        return ValidateResult.fail(
                                BPMI18N.PAAS_FLOW_BPM_NODE_FIELD_IS_NOT_ACTIVE.text(name,
                                        BPMI18N.PAAS_FLOW_BPM_CHILD_OBJECT.text(),
                                        relatedEntityName,
                                        masterDetail.get(BPMConstants.MetadataKey.label)));
                    }
                    return ValidateResult.ok();
                }
            }
        }
        return ValidateResult.fail(
                BPMI18N.PAAS_FLOW_BPM_NODE_LOOKUP_OR_SLAVE_FIELD_NOT_EXIST.text(name,
                        relatedEntityName,
                        entityName,
                        name));
    }


    //BPMConstants.MetadataKey.objectReference
    //BPMConstants.MetadataKey.masterDetail
    private List<Map<String, Object>> getObjectRefAndMasterDetail(Map<String, Object> desc, String referenceFieldApiName, String type) {
        List<Map<String, Object>> rst = Lists.newArrayList();
        ((Map<String, Object>) desc.get(BPMConstants.MetadataKey.fields)).forEach((key, value) -> {
            Map<String, Object> fieldDesc = (Map<String, Object>) value;
            if (fieldDesc.get(BPMConstants.MetadataKey.type).equals(type) && fieldDesc.get(BPMConstants.MetadataKey.targetApiName).equals(referenceFieldApiName)) {
                rst.add(fieldDesc);
            }
        });
        return rst;
    }
}
