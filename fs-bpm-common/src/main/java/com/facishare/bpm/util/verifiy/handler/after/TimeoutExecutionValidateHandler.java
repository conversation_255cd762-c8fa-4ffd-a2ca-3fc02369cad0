package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMUserTaskRemindException;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.bean.CustomFunction;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 超时任务完成后动作校验
 */
@Slf4j
public class TimeoutExecutionValidateHandler extends UserTaskRemindValidateHandler {

    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        UserTaskExt userTask = (UserTaskExt) activity;
        try {
            validateTimeoutExecution(userTask, workflow);
        } catch (BPMUserTaskRemindException e) {
            return ValidateResult.fail(e.getMessage());
        }
        return ValidateResult.ok();
    }


    /**
     * 任务超时后动作校验
     *
     * []
     *
     * @param userTaskExt
     */
    private void validateTimeoutExecution(UserTaskExt userTaskExt, Workflow workflow) {
        Object timeoutExecutionObject = userTaskExt.getTimeoutExecution();
        if (Objects.isNull(timeoutExecutionObject)) {
            return;
        }
        if (!(timeoutExecutionObject instanceof List)) {
            log.error("{} timeoutExecution 结构有误,", userTaskExt.getName());
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PLEASES_MODIFY_TIME_OUT_EXECUTION, userTaskExt.getName());
        }

        List<Map<String, Object>> timeoutExecution = (List<Map<String, Object>>) timeoutExecutionObject;
        if (CollectionUtils.isEmpty(timeoutExecution)) {
            return;
        }

        //是否配置了超时时间
        Boolean remind = userTaskExt.getRemind();
        //允许停留时长
        Object remindLatency = userTaskExt.getRemindLatency();

        if (!remind || Objects.isNull(remindLatency)) {
            log.info("{} 节点允许停留时长未设置", userTaskExt.getName());
            throw new BPMUserTaskRemindException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_THE_WAITING_TIME_SET_ERROR, userTaskExt.getName());
        }

        if (remindLatency instanceof Integer || remindLatency instanceof Double) {
            remindLatencyNumberValidate(remindLatency, userTaskExt);
        } else {
            remindLatencyExpressionValidate(remindLatency, userTaskExt, workflow.getServiceManager());
        }

        for (Map<String, Object> item : timeoutExecution) {
            Object id = item.get("id");
            Object time = item.get("time");
            Object timeUnit = item.get("timeUnit");
            Object execution = item.get("execution");
            if (Objects.isNull(id) || Objects.isNull(time) || Objects.isNull(timeUnit) || Objects.isNull(execution)) {
                log.error("timeoutExecution 必填参数为空 节点:{}, id:{},time:{},timeUnit:{},execution:{}", userTaskExt.getName(), id, time, timeUnit, execution);
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_PLEASES_MODIFY_TIME_OUT_EXECUTION_PARAMS_IS_NULL, userTaskExt.getName());
            }

            List<Map> executionList = (List<Map>) execution;
            //780 目前只支持函数
            for (Map executionItem : executionList) {
                String taskType = (String) executionItem.get("taskType");
                if("custom_function".equals(taskType)){
                    CustomFunction customFunction = JsonUtil.fromJson(JsonUtil.toJson(executionItem), CustomFunction.class);
                    CustomFunctionValidateActionHandler.validateFunction(userTaskExt, workflow, customFunction);
                }
            }
        }
    }


}
