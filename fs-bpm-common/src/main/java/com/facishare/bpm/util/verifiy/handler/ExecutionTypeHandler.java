package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.util.verifiy.TaskType;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 4
 * 校验taskType和actionCode
 * Created by cuiyongxu on 17/3/15.
 */
@Slf4j
public class ExecutionTypeHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        workflow.getUserTasks().forEach(userTask -> {
            Map<String, Object> bpmExtension = userTask.getExtension();
            String executionType = bpmExtension.get(ExtensionKey.executionType) + "";
            //审批节点的时候,判断 type是anyone 或者 all
            if (ExecutionTypeEnum.approve.name().equals(executionType)) {
                try {
                    TaskType.valueOf(userTask.getTaskType());
                } catch (IllegalArgumentException e) {
                    log.error("[" + userTask.getName() + "] 未设置会签类型taskType");
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_SET_ALL_APPROVE_TYPE,userTask.getName());
                }
            }

            //operation时,action code不能为空
            if (ExecutionTypeEnum.operation.name().equals(executionType)) {
                String actionCode = bpmExtension.get("actionCode") + "";
                if (StringUtils.isEmpty(actionCode)) {
                    log.error("[" + userTask.getName() + "] 未设置actionCode不能为空");
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_SET_OPERATE_TYPE,userTask.getName());
                }
            }
        });

    }

}
