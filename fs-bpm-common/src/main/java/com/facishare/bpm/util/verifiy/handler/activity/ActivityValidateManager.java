package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.util.NumberUtil;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.paas.I18N;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 6.2
 */
@Slf4j
public enum ActivityValidateManager {

    addRelatedObject("选择和新建关联对象", new UserTaskAddRelatedObjectValidateHandler(ExecutionTypeEnum.addRelatedObject)),
    addMDObject("选择和新建从对象", new UserTaskAddRelatedObjectValidateHandler(ExecutionTypeEnum.addMDObject)),
    batchAddRelatedObject("批量新建关联对象", new UserTaskAddRelatedObjectValidateHandler(ExecutionTypeEnum.batchAddRelatedObject)),
    batchEditMasterDetailObject("编辑从对象", new UserTaskAatchEditMasterDetailObjectValidateHandler()),
    approve("审批", new UserTaskApproveValidateHandler()),
    update("更新", new UserTaskUpdateValidateHandler()),
    externalApplyTask("应用节点", new UserTaskExternalApplyValidateHandler()),
    operation("操作节点", new UserTaskOperationValidateHandler()),
    execution("自动节点", new ExecutionTaskValidateHandler()),
    latency("定时节点", new LatencyTaskValidateHandler()),
    updateLookup("编辑关联对象", new UserTaskUpdateValidateHandler()),
    operationMulti("操作节点(多选)", new UserTaskOperationMultiValidateHandler()),
    custom("业务自定义元素", new UserTaskCustomElementValidateHandler());

    private String name;
    private ValidateActivityHandler validateActivityHandler;

    ActivityValidateManager(String name, ValidateActivityHandler validateActionHandler) {
        this.name = name;
        this.validateActivityHandler = validateActionHandler;
    }

    public static ValidateResult validate(Workflow workflow, ActivityExt activity) {
        try {
            ValidateResult latencyValidate = validateUserTaskLatency(activity);
            if(latencyValidate.isNotValid()){
                return latencyValidate;
            }
            return ActivityValidateManager.valueOf(activity.getExtensionType().name()).validateActivityHandler.validate(workflow, activity);
        } catch (IllegalArgumentException e) {
            log.error("", e);
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_MESSAGE_ERROR.text());
        }
    }
    
    // 100年 = 100 * 365 * 24 * 3600 * 1000 毫秒
    // 为避免计算溢出，使用科学计数法表示
    public static final double ONE_HUNDRED_YEAR = 3.1536E12;
    
    public static ValidateResult validateUserTaskLatency(ActivityExt activity){
        if(activity instanceof UserTaskExt){
            Object latency=((UserTaskExt) activity).getRemindLatency();
            if(Objects.nonNull(latency)&& NumberUtils.isCreatable(latency.toString())){
                double latencyDouble=Double.parseDouble(latency.toString());
                int unit = ((UserTaskExt) activity).getLatencyUnit();
                if(NumberUtil.getRemindLatency(unit,latencyDouble)>=ONE_HUNDRED_YEAR){
                    return ValidateResult.fail(I18N.text("activity_latency_over_100_years", activity.getName()));
                }
            }
        }
        return ValidateResult.ok();
    }
}
