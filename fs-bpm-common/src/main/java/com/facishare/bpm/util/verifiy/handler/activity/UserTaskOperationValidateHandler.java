package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.util.MetadataUtils;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import org.apache.commons.collections.MapUtils;

import java.util.Map;


/**
 * <AUTHOR>
 * @Date on 2018/5/3
 * @since 6.3
 */
public class UserTaskOperationValidateHandler implements ValidateActivityHandler {
    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        ValidateResult result = ValidateResult.ok();
        RefServiceManager serviceManager = workflow.getServiceManager();
        //校验userTask 签到/签退
        validateSignInAndSignOut(serviceManager, activity, result);
        return  result;
    }

    /**
     * 根据节点是不是签退/签到操作设置校验错误信息
     * @param serviceManager
     * @param activity
     * @param validateResult
     * {
     *   "executionName": "操作对象",
     *   "entityName": "老糊涂神",
     *   "entityId": "object_29dMW__c",
     *   "executionType": "operation",
     *   "actionCode": "signout",
     *   "objectId": {
     *     "expression": "activity_0##object_29dMW__c"
     *   }
     * }
     */
    private void validateSignInAndSignOut(RefServiceManager serviceManager, ActivityExt activity, ValidateResult validateResult){
        Map extension = activity.getExtension();
        //判断是否是签到/签退操作  bpmExtension.actionCode --- signin(签到)、signout(签退)
        String actionCode = (String) extension.get(ExtensionKey.actionCode);
        if(!(BPMConstants.OperationCodeType.signin.name().equals(actionCode) || BPMConstants.OperationCodeType.signout.name().equals(actionCode))){
            return;
        }
        String entityId = (String) extension.get(ExtensionKey.entityId);
        //查询对象描述
        Map entityDescribe = serviceManager.findDescribe(entityId, true, false);
        //获取对象描述中签到组件描述
        Map signInDescribe = MetadataUtils.getAppointFieldDescribeByFieldCondition(entityDescribe, BPMConstants.MetadataKey.group_type, BPMConstants.MetadataKey.SIGN_IN);
        //是否存在签到组件描述或是否已禁用
        if(MapUtils.isEmpty(signInDescribe) || Boolean.FALSE.equals(signInDescribe.get(BPMConstants.MetadataKey.isActive))){
            validateResult.setValid(false);
            validateResult.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_FORM_OBJECT_SIGN_IN_INVALID.text(activity.getName()));
            return;
        }
        //是签退操作，判断签到组件中是否开启了签退
        if (BPMConstants.OperationCodeType.signout.name().equals(actionCode) && !Boolean.TRUE.equals(signInDescribe.get(BPMConstants.MetadataKey.IS_ENABLE_SIGN_OUT))){
            validateResult.setValid(false);
            validateResult.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_FORM_OBJECT_SIGN_OUT_INVALID.text(activity.getName()));
            return;
        }

    }




}
