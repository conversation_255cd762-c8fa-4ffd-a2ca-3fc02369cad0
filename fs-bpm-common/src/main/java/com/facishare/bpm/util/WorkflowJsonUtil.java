package com.facishare.bpm.util;

import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 03/07/2017.
 * @IgnoreI18n
 */
@Slf4j
public class WorkflowJsonUtil {
    public static String convertExecutableWorkflowToJson(ExecutableWorkflowExt workflow) {
        return workflow.toJson();
    }

    public static Map<String, Object> convertActivityToMap(ActivityExt activity) {
        return activity;
    }

    public static Map<String, Object> convertTransitionToMap(TransitionExt transition) {
        return transition;
    }

    public static Map<String, Object> convertWorkflowToMap(ExecutableWorkflowExt workflow) {
        return workflow;
    }

    public static ExecutableWorkflowExt convertExecutableWorkflowFromJson(String workflowJson) {
        return ExecutableWorkflowExt.fromJson(workflowJson);
    }

    public static void proofreadAndCorrectWorkflow(ExecutableWorkflowExt executableWorkflow) {
        ProofreadAndCorrectWorkflow.proofreadAndCorrect(executableWorkflow);
    }

    enum ProofreadAndCorrectWorkflow implements ProofreadAndCorrect {
        task() {
            Object getRemindLatencyTime(Object remindLatency) {
                Object ret ;
                if (remindLatency instanceof String) {
                    try {
                        //看能够转换成int值
                        BigDecimal bigDecimal = new BigDecimal(remindLatency+"");
                        ret = bigDecimal.doubleValue();
                        log.info("前端传递的允许停留时长为数字类型的字符串: \"1\"");
                    } catch (NumberFormatException e) {
                        //如果是NumberFormatException 则为 ${activity_42738947238##AccountObj##filed_dd7ds__c}
                        ret = remindLatency;
                    }
                } else {
                    ret = remindLatency;
                }
                return ret;
            }

            @Override
            public void execute(ExecutableWorkflowExt workflow) {
                String assignNextTask = WorkflowKey.ActivityKey.assignNextTask;
                List<String> lastUserTask = getLastUserTask(workflow);
                Map<String, ActivityExt> activityMap = Maps.newHashMap();
                workflow.getActivities().stream().filter(activity -> activity.instanceOf( UserTaskExt.class)).forEach(activity -> {
                    String remindLatencyKey = "remindLatency";
                    Object remindLatency = activity.getProperty(remindLatencyKey);

                    //6.5 表达式存在字符串 ${a.b.c}+DAYS(1),不是double ,单一数字的话 前端做了处理,/d  传递数字
                    if (remindLatency != null && (remindLatency instanceof String || remindLatency instanceof Integer||remindLatency instanceof Double)) {
                        activity.put(remindLatencyKey,getRemindLatencyTime(remindLatency));
                        log.info("{},proofreadAndCorrect:{},from {} to {}", workflow.getId(), remindLatencyKey, "string", "integer");
                   }

                    activityMap.put(activity.getId(), activity);
                    if(workflow.isExternalFlow() && activity.instanceOf(UserTaskExt.class) && !lastUserTask.contains(activity.getId())){
                        activity.put(assignNextTask,1);
                        log.info("{}[{}],{}[{}],proofreadAndCorrect:set {} 1",workflow.getId(),workflow.getName(),activity.getId(),activity.getName(), assignNextTask);
                    }
                });
                /**
                 * 最后一个节点去掉指定下一节点处理人的设置
                 */
                lastUserTask.forEach((activityId) -> {
                    ActivityExt activity = activityMap.get(activityId);
                    if (activityMap.get(activityId) != null && Objects.nonNull(activity.getProperty(assignNextTask))) {
                        activity.put(assignNextTask, 0);
                        log.info("{}[{}],{}[{}],proofreadAndCorrect:set {} 0",workflow.getId(),workflow.getName(),activity.getId(),activity.getName(), assignNextTask);
                    }
                });

                setExternalWorkflowFirstTaskProcessors(workflow);

            }

        }, base() {
            @Override
            public void execute(ExecutableWorkflowExt workflow) {

            }
        };

        private static void setExternalWorkflowFirstTaskProcessors(ExecutableWorkflowExt workflow) {
            if (workflow.isExternalFlow()) {
                String startId = null;
                for (ActivityExt activity : workflow.getActivities()) {
                    if (activity.instanceOf(StartEventExt.class)) {
                        startId = activity.getId();
                    }
                }

                List<String> firstActivities = ProofreadAndCorrectWorkflow.getOutgoingIds(workflow.getTransitions(), startId);
                for (ActivityExt activity : workflow.getActivities()) {
                    if (firstActivities.contains(activity.getId())) {
                        if (activity.instanceOf(UserTaskExt.class)) {
                             Map<String,Set<Object>> assignee = new UserTaskExt(activity).getAssignee();
                            if (assignee == null || MapUtils.isEmpty(assignee)) {
                                Map temp = assignee;
                                if(assignee==null){
                                    temp=Maps.newHashMap();
                                    activity.put(WorkflowKey.ActivityKey.assignee,temp);
                                }
                                temp.put(WorkflowKey.ActivityKey.Assignee.extBPM, Lists.newArrayList("instance##owner$$流程发起人"));
                                log.info("{}[{}],{}[{}],proofreadAndCorrect:{}",workflow.getId(),workflow.getName(),activity.getId(),activity.getName(),"add start_person to firstUserTask ");
                            }
                        } else {
                            //throw new BPMWorkflowDefVerifyException("外部流程第一个节点必须是业务节点或审批节点");
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_EXTERNAL_FLOW_FIRST_ACTIVITY_MUST_HANDLER_ACTIVITY);
                        }
                    }
                }
            }
        }

        private static List<String> getLastUserTask(ExecutableWorkflowExt executableWorkflow) {
            List<String> endIds = Lists.newArrayList();
            for (ActivityExt activity : executableWorkflow.getActivities()) {
                if (activity.instanceOf(EndEventExt.class)) {
                    endIds.add(activity.getId());
                }
            }
            List<String> lastUserTasks = Lists.newArrayList();
            List<TransitionExt> transitions = executableWorkflow.getTransitions();
            for (TransitionExt transition : transitions) {
                if (endIds.contains(transition.getToId()) && getOutgoingIds(transitions, transition.getFromId()).size() == 1) {
                    lastUserTasks.add(transition.getFromId());
                }
            }
            return lastUserTasks;
        }

        private static List<String> getOutgoingIds(List<TransitionExt> transitions, String activityId) {
            return transitions.stream().filter(item -> item.getFromId().equals(activityId)).map(TransitionExt::getToId).collect(Collectors.toList());
        }

        static void proofreadAndCorrect(ExecutableWorkflowExt workflow) {
            for (ProofreadAndCorrectWorkflow proofreadAndCorrectWorkflow : ProofreadAndCorrectWorkflow.values()) {
                proofreadAndCorrectWorkflow.execute(workflow);
            }
        }
    }

    private interface ProofreadAndCorrect {
        void execute(ExecutableWorkflowExt workflow);
    }


}
