package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.google.common.base.Joiner;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 1
 * 初始化数据
 * 将所有的userTask提出
 * 将所有activityId提出
 * 将所有的variablesId提出
 * <p>
 * Created by cuiyongxu on 17/3/15.
 */
public class VerifyInitHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        ExecutableWorkflowExt executableWorkflow = workflow.getExecutableWorkflow();
        List<ActivityExt> activityList = executableWorkflow.getActivities();

        activityList.forEach(activity -> {

            if (activity.instanceOf(UserTaskExt.class)) {
                workflow.getUserTasks().add(new UserTaskExt(activity));
            } else if (activity.instanceOf(ExclusiveGatewayExt.class)) {
                workflow.getExclusiveGateWays().add(new ExclusiveGatewayExt(activity));
            } else if (activity.instanceOf(ExecutionTaskExt.class)) {
                workflow.getExecutionTasks().add(new ExecutionTaskExt(activity));
            } else if (activity.instanceOf(LatencyTaskExt.class)) {
                workflow.getLatencyTaskExt().add(new LatencyTaskExt(activity));
            }

            workflow.getActivities().add(activity);
        });
        if (activityList.size() == 0) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_ABSENCE_BUSS_NODE);
        }
        List<VariableExt> varList = executableWorkflow.getVariables();
        varList.forEach(variable -> workflow.getVariablesList().add(variable.getId()));

        workflow.getTransitions().addAll(executableWorkflow.getTransitions());

        List<String> notInMainFlowActivities = workflow.getWorkflowGraph().notContainStartActivities();

        if (CollectionUtils.isNotEmpty(notInMainFlowActivities)) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NOT_FOUND_CONNECT_START_NODE, Joiner.on(",").join(notInMainFlowActivities));
        }

        Map<String, Set<Object>> applicantWhenSystemUser = executableWorkflow.getApplicantWhenSystemUser();
        if(Objects.nonNull(applicantWhenSystemUser)) {
            AssigneeHandler.validateAssignee(BPMI18N.SPECIFY_THE_INITIATOR_WHEN_THE_SYSTEM_TRIGGERS_A_PROCESS.text(), null, applicantWhenSystemUser, workflow);
        }
    }
}
