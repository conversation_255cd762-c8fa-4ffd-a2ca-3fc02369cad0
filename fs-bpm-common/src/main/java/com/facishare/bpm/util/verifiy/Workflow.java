package com.facishare.bpm.util.verifiy;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.util.PreActivityFunction;
import com.facishare.bpm.util.verifiy.util.WorkflowGraph;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by cuiyongxu on 17/3/15.
 */
@Data
@Slf4j
public class Workflow {
    private String name;
    private String tenantId;
    private String entryType;
    private String entryTypeName;
    //储存所有的activity上对面的表达式
    private HashSet<Object> activitiesExpression = Sets.newHashSet();
    //储存所有的variables表达式
    private ArrayList<String> variablesList = Lists.newArrayList();
    //储存transitions conditional
    private Map<String, String> transitionsConditional = Maps.newHashMap();
    //储存所有的userTask
    private List<UserTaskExt> userTasks = Lists.newArrayList();
    //储存所有的 ExecutionTaskExt
    private List<ExecutionTaskExt> executionTasks = Lists.newArrayList();
    //储存所有的exclusiveGateWays
    private List<ExclusiveGatewayExt> exclusiveGateWays = Lists.newArrayList();
    //储存所有的activity
    private List<ActivityExt> activities = Lists.newArrayList();
    //存储所有的transition
    private List<TransitionExt> transitions = Lists.newArrayList();
    //储存所有的 latencyTaskExt,定时节点
    private List<LatencyTaskExt> latencyTaskExt = Lists.newArrayList();
    //存储扩展信息
    private WorkflowExtension extension;

    private ExecutableWorkflowExt executableWorkflow;

    private Map<String, List<ActivityExt>> activityPreNodeMap;

    private Map<String, ActivityExt> activityMap;

    private RefServiceManager serviceManager;

    private WorkflowGraph workflowGraph;

    private WorkflowRule rule;

    private Map<String, Set<Object>> rangeAssignee;

    private Integer externalFlow;

    public Workflow() {

    }

    public Workflow(RefServiceManager serviceManager,
                    String name,
                    String entryType,
                    String entryTypeName,
                    ExecutableWorkflowExt executableWorkflow,
                    WorkflowRule rule,
                    String extension,
                    Integer externalFlow) {
        this.setTenantId(serviceManager.getTenantId());
        this.setExecutableWorkflow(executableWorkflow);
        this.setExtension(JsonUtil.fromJson(extension, WorkflowExtension.class));
        this.setName(name);
        this.setEntryType(entryType);
        this.setEntryTypeName(entryTypeName);
        this.setRule(rule);
        this.setServiceManager(serviceManager);
        this.setExternalFlow(externalFlow);
    }

    /**
     * console使用 用于校验定义是否有效
     * @param serviceManager
     * @param name
     * @param entryType
     * @param entryTypeName
     * @param executableWorkflow
     * @param rule
     * @param extension
     */
    public Workflow(RefServiceManager serviceManager,
                    String name,
                    String entryType,
                    String entryTypeName,
                    ExecutableWorkflowExt executableWorkflow,
                    WorkflowRule rule,
                    String extension) {
        this.setTenantId(serviceManager.getTenantId());
        this.setExecutableWorkflow(executableWorkflow);
        this.setExtension(JsonUtil.fromJson(extension, WorkflowExtension.class));
        this.setName(name);
        this.setEntryType(entryType);
        this.setEntryTypeName(entryTypeName);
        this.setRule(rule);
        this.setServiceManager(serviceManager);
    }

    public static void isBlank(final String cs, BPMBusinessExceptionCode code, Object... args) {
        if (StringUtils.isBlank(cs)) {
            throw new BPMWorkflowDefVerifyException(code, args);
        }
    }

    public static void isAnyBlankCount(Collection... collections) {
        boolean isBlank = Arrays.stream(collections).allMatch(item -> item.size() == 0);
        if (isBlank) {
            log.error("请检查 {rangeCircleIds,rangeEmployeeIds,rangeGroupIds,rangeGroupIds } 是否存在为空项");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_CHOICE_SCOPE);
        }
    }

    private void setExecutableWorkflow(ExecutableWorkflowExt workflow) {
        this.executableWorkflow = workflow;
        this.workflowGraph = new WorkflowGraph(this.getExecutableWorkflow());
        this.setActivityPreNodeMap(this.workflowGraph.getPreActivityMap());
    }

    public boolean isExternalFlow() {
        return executableWorkflow.isExternalFlow();
    }

    public boolean isLinkAppEnable() {
        return executableWorkflow.isLinkAppEnable();
    }

    public String getLinKApp() {
        return executableWorkflow.getLinkApp();
    }

    public String getLinkAppName() {
        return executableWorkflow.getLinkAppName();
    }

    public boolean getLinkAppEnable() {
        return executableWorkflow.isLinkAppEnable();
    }

    public int getLinKAppType() {
        return executableWorkflow.getLinkAppType();
    }

    public Map<String, ActivityExt> getActivityMap() {
        if (MapUtils.isEmpty(this.activityMap)) {
            this.setActivityMap(executableWorkflow.getActivityMaps());
        }
        return this.activityMap;
    }

    public Map<String, UserTaskExt> getUserTaskMap() {
        if (CollectionUtils.isNotEmpty(this.userTasks)) {
            return this.userTasks.stream().collect(Collectors.toMap(k -> (String) k.get("id"), v -> v));
        }
        return Maps.newHashMap();
    }

    public void setRangeAssigneeFromWorkflowOutline(List<Integer> rangeCircleIds, List<String> rangeGroupIds, List<Integer> rangeEmployeeIds, List<String> rangeRoleIds) {
        Map<String, Set<Object>> rangeAssignee = Maps.newHashMap();
        rangeAssignee.put("dept", Sets.newHashSet(rangeCircleIds));
        rangeAssignee.put("group", Sets.newHashSet(rangeGroupIds));
        rangeAssignee.put("person", Sets.newHashSet(rangeEmployeeIds));
        rangeAssignee.put("role", Sets.newHashSet(rangeRoleIds));
        this.rangeAssignee = rangeAssignee;
    }

    public List<ExecutionTaskExt> getDelayExecutionTaskExt(){
        List result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(this.executionTasks)) {
            return result;
        }
        for (ExecutionTaskExt executionItem : this.executionTasks) {
            if (executionItem.getDelay()) {
                result.add(executionItem);
            }
        }
        return result;
    }

    public Map<String,List<String>> getApplicantWhenSystemUser(){
        return (Map)this.executableWorkflow.get("applicantWhenSystemUser");
    }

    @Data
    public static class LaneEntity implements Serializable {
        private String id;
        private String name;
        private List<String> activities;
        private Integer order;
    }

    @Data
    public static class PoolEntity implements Serializable {
        private String id;
        private String name;
        private List<LaneEntity> lanes;

        public PoolEntity() {
        }
    }

    @Data
    public static class WorkflowExtension {
        private String id;
        private List<PoolEntity> pools;
        private Object diagram;
        private String svg;
    }


    public PreActivityFunction getPreActivityFun(ActivityExt activity) {
        return (activityId, entityId, isSelf) -> {
            //处理入是入口对象的情况
            if (BPMConstants.ENTRYACTIVITYID.equals(activityId)) {
                return entityId.equals(entryType);
            }
            log.info("getPreActivityFun:currentActivity:{}({}), activityId:{},entityId:{},isSelf:{}",
                    activity.getName(), activity.getId(), activityId, entityId, isSelf
            );
            //处理前置节点处理人作为变量的情况
            List<ActivityExt> preNodes = this.getActivityPreNodeMap().get(activity.getId());
            boolean isPreActivity = CollectionUtils.isNotEmpty(preNodes) && preNodes.stream().anyMatch(item -> item.getId().equals(activityId));
            if (entityId == null) {
                return isPreActivity;
            }
            if (isSelf) {
                return isPreActivity && this.getActivityMap().get(activityId).getRelatedEntityId().equals(entityId);
            }
            //处理前置节点的对象的变量
            return isPreActivity && BPMConstants.EXECUTIONTYPE.equals(entityId)
                    || isPreActivity && BPMConstants.OPINION.equals(entityId)
                    || isPreActivity && BPMConstants.OPINIONLIST.equals(entityId)
                    || isPreActivity && (entityId.equals(BPMConstants.ApproveResult.RESULT)
                    || this.getActivityMap().get(activityId).getEntityId().equals(entityId)
                    || entityId.equals(this.getActivityMap().get(activityId).getRelatedEntityId()));
        };
    }

    public Map<String, List<ExecutionItem>> getExecution() {
        Map<String, List<Map>> execution = (Map<String, List<Map>>) executableWorkflow.get("execution");
        Map<String, List<ExecutionItem>> rst = Maps.newHashMap();
        if (MapUtils.isNotEmpty(execution)) {
            execution.forEach((key, value) -> rst.put(key, ExecutionItem.from(value)));
        }
        return rst;
    }


}
