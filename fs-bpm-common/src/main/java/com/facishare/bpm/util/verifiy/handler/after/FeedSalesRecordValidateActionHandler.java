package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 6.2
 * 1. 抄送范围不空且合法
 * 2. 类型不能为空
 * 3. 内容不能为空
 */
public class FeedSalesRecordValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        FeedSalesRecord salesRecord = JsonUtil.fromJson(JsonUtil.toJson(executionItem.getActionMapping()), FeedSalesRecord.class);
        String activityName = getActivityName(activity);
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_SEND_SALE_COPY_RANGE.text(activityName), activity, salesRecord.getCarbonCopyEmployeeIds(), workflow);
        if (!UserTaskExt.hasAssignee(salesRecord.carbonCopyEmployeeIds)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SALE_COPY_RANGE_NOT_EMPTY.text(activityName));
        }

        if (Strings.isNullOrEmpty(salesRecord.getTagId())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SALE_TYPE_NOT_EMPTY.text(activityName));
        }

        if (Strings.isNullOrEmpty(salesRecord.getContent())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_SALE_CONTENT_NOT_EMPTY.text(activityName));
        }
        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(workflow, salesRecord.getContent(), workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_SEND_FEED_CONTENT.text(activityName, rst.getMessage()));
            return rst;
        }
        return rst;
    }

    @Data
    class FeedSalesRecord {
        Map<String, Set<Object>> carbonCopyEmployeeIds;
        String content;
        /**
         * 销售记录类型
         */
        String tagId;

    }
}
