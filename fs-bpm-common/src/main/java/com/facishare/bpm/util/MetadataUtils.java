package com.facishare.bpm.util;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMMetaDataException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.resource.metadata.RW;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by wangz on 17-5-19.
 */
@Slf4j
public class MetadataUtils {
    public static boolean isNullOrEmptyObject(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj instanceof List) {
            return CollectionUtils.isEmpty((List) obj);
        }

        if (obj instanceof Map) {
            return MapUtils.isEmpty((Map) obj);
        }

        return Strings.isNullOrEmpty(obj + "");
    }

    public static Map<String, Object> validateAndCorrectDataValue(Map<String, Object> data, Map<String, Object> describe) {
        if (describe == null) {
            throw new BPMMetaDataException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_DESCRIBE_NOT_FOUND);
        }

        Map<String, Map<String, Object>> fields = (Map<String, Map<String, Object>>) describe.get("fields");
        for (String field : data.keySet()) {
            Map<String, Object> fieldDesc = fields.get(field);
            Object value = data.get(field);
            data.put(field, correctDataValue(fieldDesc, value));
        }
        transferInteger(data, Lists.newArrayList(BPMConstants.MetadataKey.VERSION));
        return data;
    }

    private static Object correctDataValue(Map<String, Object> fieldDesc, Object value) {
        return BPMFormUpdateDataType.correctDataValue(fieldDesc, value);
    }

    enum BPMFormUpdateDataType implements MetaDataValueCorrect {
        number {
            @Override
            public Object correct(Map<String, Object> fieldDesc, Object value) {
                if (value == null) {
                    return null;
                }
                if (String.valueOf(value).trim().equals("")) {
                    log.info("correct:label:{},field:{},type:{},value:{}", fieldDesc.get("label")
                            , fieldDesc.get("api_name"), fieldDesc.get("type"), value);
                    return null;
                }
                return value;
            }
        }, time {
            @Override
            public Object correct(Map<String, Object> fieldDesc, Object value) {
                if (value == null) {
                    return null;
                }
                /**
                 * 自定义对象时间字段8:00，时间戳为 0
                 */
                String type = (String) fieldDesc.get(BPMConstants.MetadataKey.type);
                if (time.name().equals(type) && String.valueOf(value).trim().equals("0")) {
                    return value;
                }
                /**
                 * 如果为date_time/date不可能为0
                 */
                if (String.valueOf(value).trim().equals("") || String.valueOf(value).trim().equals("0")) {
                    log.info("correct:label:{},field:{},type:{},value:{}", fieldDesc.get("label")
                            , fieldDesc.get("api_name"), fieldDesc.get("type"), value);
                    return null;
                }
                return value;
            }
        }, date_time {
            @Override
            public Object correct(Map<String, Object> fieldDesc, Object value) {
                return time.correct(fieldDesc, value);
            }
        }, date {
            @Override
            public Object correct(Map<String, Object> fieldDesc, Object value) {
                return time.correct(fieldDesc, value);
            }
        }, formula {
            @Override
            public Object correct(Map<String, Object> fieldDesc, Object value) {
                String returnType = (String) fieldDesc.get("return_type");
                boolean isDateType = (null != returnType && (returnType.equals("date") || returnType.equals("date_time") || returnType.equals("time")));
                if (isDateType) {
                    return number.correct(fieldDesc, value);
                }
                return value;
            }
        };

        public static Object correctDataValue(Map<String, Object> fieldDesc, Object value) {
            try {
                String type = (String) fieldDesc.get(BPMConstants.MetadataKey.type);
                return BPMFormUpdateDataType.valueOf(type).correct(fieldDesc, value);
            } catch (Throwable e) {
                return value;
            }
        }
    }

    /**
     * 使用 Map按key进行排序
     * @param map
     * @return
     */
    public static Map<String, String> sortMapByKey(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return Maps.newHashMap();
        }
        Map<String, String> sortMap = new TreeMap<>(Comparator.naturalOrder());
        sortMap.putAll(map);
        return sortMap;
    }

    interface MetaDataValueCorrect {
        Object correct(Map<String, Object> fieldDesc, Object value);
    }

    public static RW getFieldAccess(Map<String, Integer> permissions, String fieldName) {
        if (Objects.isNull(permissions)) {
            log.warn("GetUser field permission is null fieldName:{}", fieldName);
            return RW.Write;
        }
        Integer access = permissions.get(fieldName);
        return RW.valueOf(access);
    }

    /**
     * 将字段值,转换为int类型
     * //1.0->1
     * // fields = version
     * @param data
     * @param fieldApiNames  需要转换的字段
     */
    public static void transferInteger(Map<String, Object> data, List<String> fieldApiNames) {
        if (MapUtils.isEmpty(data) || CollectionUtils.isEmpty(fieldApiNames)) {
            return;
        }
        fieldApiNames.forEach(fieldApiname -> {
            Object fieldObj = data.get(fieldApiname);
            Integer value = Objects.isNull(fieldObj) ? null : new BigDecimal(String.valueOf(fieldObj)).intValue();
            if (Objects.isNull(value)) {
                return;
            }
            data.put(fieldApiname, value);
        });
    }

    /**
     * 根据对象描述和指定描述类型获取指定描述信息
     * @param matadataDescribes
     * @param fieldAttribute
     * @param fieldAttributeValue
     * @return
     */
    public static Map getAppointFieldDescribeByFieldCondition(Map matadataDescribes, String fieldAttribute, Object fieldAttributeValue){
        Map<String, Map> fields = (Map<String, Map>) matadataDescribes.get(BPMConstants.MetadataKey.fields);
        for(Map fieldAttributeMap : fields.values()){
            if(!Objects.isNull(fieldAttributeMap.get(fieldAttribute)) && fieldAttributeValue.equals(fieldAttributeMap.get(fieldAttribute))){
                return fieldAttributeMap;
            }
        }
        return null;
    }

    /**
     * 根据签到组件描述和数据判断是否已签到
     * @param signInDescribe
     * * @param matadataDescribes
     * @return
     */
    public static boolean objectIsSignIn(Map signInDescribe, Map matadataData){
        boolean  result = Boolean.FALSE;
        if(!Objects.isNull(signInDescribe)){
            //获取签到组件fields
            Map<String, Object> signInFields = (Map<String, Object>) signInDescribe.get(BPMConstants.MetadataKey.fields);
            if(MapUtils.isEmpty(signInFields)){
                return result;
            }
            //从签到组件fields中获取签到状态字段
            String signInStateFieldName = (String) signInFields.get(BPMConstants.MetadataKey.SIGN_IN_STATES_FIELD);
            if(BPMConstants.MetadataKey.SIGN_IN_COMPLETE.equals(matadataData.get(signInStateFieldName))){
                result = Boolean.TRUE;
            }

        }
        return result;
    }

    /**
     * 根据签到组件描述和数据判断是否已签退
     * @param signInDescribe
     * * @param matadataDescribes
     * @return
     */
    public static boolean objectIsSignOut(Map signInDescribe, Map matadataData){
        boolean  result = Boolean.FALSE;
        if(!Objects.isNull(signInDescribe)){
            //获取签到组件fields
            Map<String, Object> signInFields = (Map<String, Object>) signInDescribe.get(BPMConstants.MetadataKey.fields);
            if(MapUtils.isEmpty(signInFields)){
                return result;
            }
            //从签到组件fields中获取签到状态字段
            String signInStateFieldName = (String) signInFields.get(BPMConstants.MetadataKey.SIGN_OUT_STATES_FIELD);
            if(BPMConstants.MetadataKey.SIGN_IN_COMPLETE.equals(matadataData.get(signInStateFieldName))){
                result = Boolean.TRUE;
            }
        }
        return result;
    }

    /**
     * 判断签到组件描述是否禁用了签退
     * @param signInDescribe
     * @return
     */
    public static boolean objectIsDisableSignOut(Map signInDescribe){
        if(!Objects.isNull(signInDescribe)){
            return Boolean.TRUE.equals(signInDescribe.get(BPMConstants.MetadataKey.IS_ENABLE_SIGN_OUT)) ? Boolean.FALSE : Boolean.TRUE;
        }
        return Boolean.TRUE;
    }

}
