package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.TransitionExt;
import com.facishare.bpm.bpmn.condition.ConditionExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 6.2
 */
@Slf4j
public class ConditionComparatorHandler implements ValidateHandler{
    @Override
    public void validate(Workflow workflow) {
        ValidateResult result=ValidateResult.ok();
        Map<String, ActivityExt> activityMap = workflow.getExecutableWorkflow().getActivityMaps();
        List<TransitionExt> transitions = workflow.getTransitions();
        for (TransitionExt transition : transitions) {
            ConditionExt condition = transition.getCondition();
            result = verifyCondition(condition);
            if (!result.isValid()) {
                String from = transition.getFromId();
                String to = transition.getToId();
                //result.setMessage(activityMap.get(from).getName() + " 到 " + activityMap.get(to).getName() + " 节点的 '"+transition.getDescription()+"' 条件" + result.getMessage() + "，请联系流程配置人员");
                result.setMessage(BPMI18N.PAAS_FLOW_BPM_ACTIVITY_TO_ACTIVITY_CONDITION_ERROR.text(
                        activityMap.get(from).getName() ,
                        activityMap.get(to).getName(),
                        transition.getDescription(),
                        result.getMessage()
                        ));
            }
        }
        if(!result.isValid()){
            //throw new BPMWorkflowDefVerifyException(result.getMessage());
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION,result.getMessage());
        }
    }

    static class ConditionValidateConfig {

        Map<String, List<String>> supportTypeOperators;

        {
            ConfigFactory.getConfig("fs-bpmn-validate-condition-config", config -> {
                String content = config.getString();
                if (Strings.isNullOrEmpty(content)) {
                    supportTypeOperators = Maps.newHashMap();
                    supportTypeOperators.put("list", Lists.newArrayList("hasAnyOf"));
                    return;
                }
                Type type = new TypeToken<Map<String, List<String>>>() {
                }.getType();
                supportTypeOperators = JsonUtil.fromJson(config.getString(), type);
            });
        }

        public ValidateResult isValid(String type, String operateType) {
            List<String> typeConfig = supportTypeOperators.get(type);
            log.info("validate condition type:{}, operateType:{},config:{}",type,operateType,typeConfig);
            if (CollectionUtils.isNotEmpty(typeConfig) && typeConfig.contains(operateType)) {
                return ValidateResult.ok();
            }
            return new ValidateResult(false, BPMI18N.PAAS_FLOW_BPM_BPM_FLOW_CONDITION_NOT_SUPPORT.text(type,operateType));
        }

    }

    public static final ConditionValidateConfig validateConfig = new ConditionValidateConfig();

    private static final List<String> hasNoRightValueComparators=Lists.newArrayList("hasValue","hasNoValue");
    public static ValidateResult verifyCondition(ConditionExt condition) {
        if (condition == null) {
            return ValidateResult.ok();
        }
        if (condition.isComparator()) {
            String comparatorTypeName = condition.getType();
            ConditionExt.ValueExt right = condition.getRight();

            if (right != null&&right.getType()!=null) {
                Object value = right.getValue();
                String type = right.getType().getName();
                log.info("type:{},comparator:{},value:{}",type,comparatorTypeName,value);
                if(!hasNoRightValueComparators.contains(comparatorTypeName)){
                    if(value==null){
                        return new ValidateResult(false,BPMI18N.PAAS_FLOW_BPM_VERIFIY_RIGHT_CONFIG_ERROR.text());
                    }
                }
                ValidateResult result = validateConfig.isValid(type, comparatorTypeName);
                if (!result.isValid()) {
                    return result;
                }
            }else{
                log.info("{}:right is null",condition.getLeft().getExpression());
            }

        } else {
            List<ConditionExt> conditions = condition.getConditions();
            if (CollectionUtils.isNotEmpty(conditions)) {
                for (ConditionExt item : conditions) {
                    ValidateResult temp = verifyCondition(item);
                    if (!temp.isValid()) {
                        return temp;
                    }
                }
            }
        }

        return ValidateResult.ok();
    }
}
