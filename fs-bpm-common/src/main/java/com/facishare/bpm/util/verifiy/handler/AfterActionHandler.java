package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.after.AfterActionValidateManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 1 后动作校验
 * Created by <PERSON> on 18/4/24.
 */
@Slf4j
public class AfterActionHandler implements ValidateHandler {

    @Override
    public void validate(Workflow workflow) {
        ValidateResult validateResult = AfterActionValidateManager.validate(workflow);
        if (!validateResult.isValid()) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION,validateResult.getMessage());
        }
    }


}
