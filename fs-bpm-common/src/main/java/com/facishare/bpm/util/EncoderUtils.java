package com.facishare.bpm.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Slf4j
public class EncoderUtils {

    public static String encode(String str) {
        if(StringUtils.isNotEmpty(str)) {
            try {
                return URLEncoder.encode(str, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.warn("URLEncoder encode string error, 参数：{}", str);
                return StringUtils.EMPTY;
            }
        }
        return str;
    }
}
