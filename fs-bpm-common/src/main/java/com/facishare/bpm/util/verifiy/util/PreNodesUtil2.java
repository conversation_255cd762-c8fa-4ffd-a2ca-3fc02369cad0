package com.facishare.bpm.util.verifiy.util;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 31/05/2017.
 */
@Slf4j
@UtilityClass
public class PreNodesUtil2 {

    public Map<String, List<ActivityExt>> getPreActivities(ExecutableWorkflowExt workflow) {
        return new WorkflowGraph(workflow).getPreActivityMap();
    }
}
