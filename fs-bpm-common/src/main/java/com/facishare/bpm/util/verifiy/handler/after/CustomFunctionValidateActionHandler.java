package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.bean.CustomFunction;
import com.facishare.bpm.util.verifiy.handler.bean.FunctionParam;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.paas.I18N;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.bpm.i18n.BPMI18N.*;


/**
 * <AUTHOR>
 * @since 6.2
 * 1. actionMapping不能为空
 * 2. functionApiName不为空
 * 3. 函数名称不能为空
 * 4. 对象类型不能为空
 * 5. 只支持"String", "BigDecimal", "DateTime", "Date", "Time", "Boolean" 变量类型
 */
@Slf4j
public class CustomFunctionValidateActionHandler implements ValidateActionHandler {

    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        Map<String, Object> actionMapping = executionItem.getActionMapping();
        return getValidateResult(activity, workflow, actionMapping);
    }

    public static ValidateResult getValidateResult(ActivityExt activity, Workflow workflow, Map<String, Object> actionMapping) {
        CustomFunction customFunction = JsonUtil.fromJson(JsonUtil.toJson(actionMapping), CustomFunction.class);
        ValidateResult result = validateFunction(activity, workflow, customFunction);
        result.setMessage(I18N.text(PAAS_FLOW_BPM_AFTER_ACTION.key, ActivityExt.getActivityName(activity), result.getMessage()));
        return result;
    }

    public static ValidateResult validateFunction(ActivityExt activity, Workflow workflow, CustomFunction customFunction) {

        if (Objects.isNull(customFunction)) {
            return ValidateResult.fail(I18N.text(PAAS_FLOW_BPM_FUNCTION_CONFIG_IS_EMPTY.key));
        }

        if (Strings.isNullOrEmpty(customFunction.getFunctionApiName())) {
            return ValidateResult.fail(I18N.text(PAAS_FLOW_BPM_FUNCTION_API_NAME_IS_EMPTY.key));
        }

        if (Strings.isNullOrEmpty(customFunction.getEntityId())) {
            return ValidateResult.fail(I18N.text(PAAS_FLOW_BPM_FUNCTION_ENTITY_IS_EMPTY.key));
        }

        if (!Strings.isNullOrEmpty(activity.getEntityId()) && !activity.getEntityId().equals(customFunction.getEntityId())) {
            return ValidateResult.fail(I18N.text(PAAS_FLOW_BPM_FUNCTION_ENTITY_NOT_EQ_NODE_ENTITY_IS_EMPTY.key));
        }

        if (CollectionUtils.isNotEmpty(customFunction.getFuncProperties())) {
            List<FunctionParam> properties = customFunction.getFuncProperties();
            for (FunctionParam functionParam : properties) {
                if (!functionParam.isValidType()) {
                    //函数变量 {0} 的类型({1})不支持
                    return ValidateResult.fail(I18N.text(PAAS_FLOW_BPM_FUNCTION_PARAMS_TYPE_NOT_SUPPORT.key, functionParam.getName(), functionParam.getType()));
                }
                ValidateResult rst = ValidateVariableAndContentUtils.validateContentOfNotNull(
                        workflow,
                        functionParam.getValue(),
                        workflow.getPreActivityFun(activity));
                //不能为空
                if (!rst.isValid()) {
                    //节点 {0} 配置的 {1} 函数 {2} 参数 {3} {4}，请确认
                    //函数配置的参数 {0}:{1}
                    rst.setMessage(I18N.text(PAAS_FLOW_BPM_FUNCTION_CONFIG_PARAMS_MESSAGE.key, functionParam.getName(), rst.getMessage()));
                    return rst;
                }
            }
        }
        return ValidateResult.ok();
    }


}

