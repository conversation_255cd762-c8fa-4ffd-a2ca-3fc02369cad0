package com.facishare.bpm.util;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.AfterAction;
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 9/11/24
 * @apiNote
 **/
@UtilityClass
public class SimpleAfterActionExecutionUtil {

    /**
     * 任务相关的重试忽略下发  下发的时候  需要判断下实例状态是否为cancel 如果是cancel 则不下发任务的重试和忽略
     * @param serviceManager
     * @param instance
     * @param entityId
     */
    public void setAfterActionOperations(RefServiceManager serviceManager,
                                         WorkflowInstance instance, String entityId, AfterActionExecution.SimpleAfterActionExecution execution) {
        if (instance.hasEnd()) {
            execution.setOperation(Lists.newArrayList());
            return;
        }

        if (execution.isError()) {
            setAfterActionOperations(serviceManager, entityId,execution);
        }
    }

    public void setAfterActionOperations(RefServiceManager serviceManager,
                                         String entityId,
                                         AfterActionExecution.SimpleAfterActionExecution execution) {
        Map<String, Boolean> functionPrivilegeMap = serviceManager.hasObjectFunctionPrivilege(entityId);
        List<AfterActionExecution.AfterActionOperation> operations = Lists.newArrayList();
        if (execution.isError()) {
            //获取功能权限，根据功能权限下发按钮
            if(functionPrivilegeMap.get(BPMConstants.FunctionCode.BPM_AFTER_ERROR_RETRY)) {
                operations.add(AfterActionExecution.AfterActionOperation.getRetryAction());
            }
            if(functionPrivilegeMap.get(BPMConstants.FunctionCode.BPM_AFTER_ERROR_IGNORE)) {
                operations.add(AfterActionExecution.AfterActionOperation.getIgnoreAction());
            }
        } else {
            AfterAction timeoutAction = execution.getTimeoutAction();
            if(Objects.nonNull(timeoutAction)) {
                if(functionPrivilegeMap.get(BPMConstants.FunctionCode.BPM_AFTER_ERROR_RETRY)) {
                    operations.add(AfterActionExecution.AfterActionOperation.getRetryAction());
                }
                if(functionPrivilegeMap.get(BPMConstants.FunctionCode.BPM_AFTER_ERROR_IGNORE)) {
                    operations.add(AfterActionExecution.AfterActionOperation.getIgnoreAction());
                }
                timeoutAction.setExecutionState(AfterActionExecution.AfterActionExecutionState.ERROR);
            }
        }
        execution.setOperation(operations);
    }

}
