package com.facishare.bpm.util.memory.page;

import com.facishare.bpm.model.resource.paas.PageResult;
import com.facishare.bpm.utils.helper.StopWatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Created by <PERSON> on 17/2/20.
 */
@Slf4j
public class MemoryPageUtil {

    public static <E> PageResult<E> getPageResult(List<E> datas, Predicate<? super E> filter, Comparator<? super E> sort, int pageSize, int page) {
        StopWatch stopWatch = StopWatch.create("MemoryPageUtil.getPageResult");
        if (page == 0) {
            page = 1;
        }
        if (pageSize == 0) {
            pageSize = 10;
        }
        PageResult<E> pageResult = new PageResult<>();
        if (CollectionUtils.isEmpty(datas)) {
            return pageResult;
        }
        stopWatch.lap("toMapList");
        if (filter != null) {
            datas = datas.stream().filter(filter).collect(Collectors.toList());
        }
        stopWatch.lap("filter");
        if (sort != null) {
            datas.sort(sort);
        }
        stopWatch.lap("sort");
        List<E> result = getPageData(datas, pageSize, page);
        stopWatch.lap("page");
        pageResult.setTotal(datas.size());
        pageResult.setDataList(result);
        stopWatch.log();
        return pageResult;
    }


    private static <E> List<E> getPageData(List<E> temp, int pageSize, int page) {
        int start = (page - 1) * pageSize;
        int end = pageSize * page;
        if (start > temp.size()) {
            return Lists.newArrayList();
        }
        if (end > temp.size()) {
            end = temp.size();
        }
        return temp.subList(start, end);
    }


}
