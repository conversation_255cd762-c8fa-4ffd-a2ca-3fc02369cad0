package com.facishare.bpm.util;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.utils.i18n.CustomI18NHandler;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Tag;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 9/11/24
 * @apiNote
 **/
public class CustomI18nProcessor {

    public static Object custom(CustomI18NHandler type, String tenantId, Object object, Field field) {
        switch (type) {
            case none:
                noneCustom(tenantId, object, field);
                break;
            case outlineWorkflow:
                outlineWorkflow(tenantId, object, field);
                break;
            case entireWorkflow:
                entireWorkflow(tenantId, object, field);
                break;
            case svg:
                svg(tenantId, object, field);
                break;
            case workflowBase:
                return workflowBase(tenantId, object, field);
        }
        return null;
    }
    /**
     * 无需处理
     */

    public static  Object noneCustom(String tenantId, Object object, Field field) {
            return null;
        }
    /**
     * WorkflowOutline中存在 ExecutableWorkflowExt对象 其基类为Map,故需要执行遍历操作
     */
    public static Object outlineWorkflow(String tenantId, Object object, Field field) {
            try {
                Object data = field.get(object);
                if (Objects.isNull(data)) {
                    return null;
                }
                Map workflowOutline = JacksonUtil.fromJson(JacksonUtil.toJson(object), Map.class);
                String workflowId = (String) workflowOutline.get("workflowId");
                String sourceWorkflowId = (String) workflowOutline.get("sourceWorkflowId");
                ExecutableWorkflowExt workflow = (ExecutableWorkflowExt) data;
                List<ActivityExt> activities = workflow.getActivities();
                for (ActivityExt activity : activities) {
                    List<String> nameKeyList = Lists.newArrayList(Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", workflowId, activity.getId(), "name")), Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", sourceWorkflowId, activity.getId(), "name")));
                    String name = I18NParser.parse(tenantId, nameKeyList);
                    if (!Strings.isEmpty(name)) {
                        activity.setName(name);
                    }
                    List<String> descriptionKeyList = Lists.newArrayList(Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", workflowId, activity.getId(), "description")), Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", sourceWorkflowId, activity.getId(), "description")));
                    String description = I18NParser.parse(tenantId, descriptionKeyList);
                    if (!Strings.isEmpty(description)) {
                        activity.setDescription(description);
                    }
                }
            } catch (Exception e) {
                log.warn("outlineWorkflow", e);
                return null;
            }
            return null;
        }
    /**
     * 下发流程日志,需要下发workflow  其为Map 需要对其进行特殊变量
     */
    public static Object entireWorkflow(String tenantId, Object object, Field field) {
            try {
                Object data = field.get(object);
                if (Objects.isNull(data)) {
                    return null;
                }
//                Map workflowOutline = JacksonUtil.fromJson(JacksonUtil.toJson(data), Map.class);
                Map workflow = (Map) data;
                String workflowId = (String) getValue(getField(object.getClass(), "workflowId"), object);
                String sourceWorkflowId = (String) getValue(getField(object.getClass(), "sourceWorkflowId"), object);
                List<Map> activities = (List<Map>) workflow.get(WorkflowKey.activities);
                for (Map activity : activities) {
                    Object id = activity.get("id");
                    List<String> nameKeyList = Lists.newArrayList(Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", workflowId, id, "name")), Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", sourceWorkflowId, id, "name")));
                    String name = I18NParser.parse(tenantId, nameKeyList);
                    if (!Strings.isEmpty(name)) {
                        activity.put("name", name);
                    }
                    List<String> descriptionKeyList = Lists.newArrayList(Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", workflowId, id, "description")), Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", sourceWorkflowId, id, "description")));
                    String description = I18NParser.parse(tenantId, descriptionKeyList);
                    if (!Strings.isEmpty(description)) {
                        activity.put("description", description);
                    }
                }


                Object name = workflow.get("name");
                if (Objects.nonNull(name)) {
                    String parseWorkflowName = I18NParser.parse(tenantId, Lists.newArrayList(Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", workflowId, "name")), Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", sourceWorkflowId, "name"))));
                    if (!Strings.isEmpty(parseWorkflowName)) {
                        workflow.put("name", parseWorkflowName);
                    }
                }

                List<Map> pools = JacksonUtil.fromJson(JacksonUtil.toJson(workflow.get("pools")), List.class);
                if (CollectionUtils.isNotEmpty(pools)) {
                    for (Map pool : pools) {
                        List<Map> lanes = (List<Map>) pool.get("lanes");
                        if (Objects.nonNull(lanes)) {
                            for (Map lane : lanes) {
                                Object laneId = lane.get("id");
                                String laneName = I18NParser.parse(tenantId, Lists.newArrayList(Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", workflowId, laneId, "name")), Joiner.on(".").skipNulls().join(Lists.newArrayList("flow", sourceWorkflowId, laneId, "name"))));
                                if (!Strings.isEmpty(laneName)) {
                                    lane.put("name", laneName);
                                }
                            }
                        }
                    }
                }
                workflow.put("pools",pools);


                field.set(object, workflow);
            } catch (Exception e) {
                log.warn("entireWorkflow", e);
                return object;
            }
            return object;
        }

        public static Object svg(String tenantId, Object object, Field field) {
            try {
                Object data = field.get(object);
                if (Objects.isNull(data)) {
                    return null;
                }
                String svg = (String) field.get(object);

                Class<?> objectClass = object.getClass();
                String workflowId = (String) getValue(getField(objectClass, "workflowId"), object);
                String sourceWorkflowId = (String) getValue(getField(objectClass, "sourceWorkflowId"), object);

                if (Strings.isEmpty(svg) || Strings.isEmpty(workflowId)) {
                    return svg;
                }
                String body = getI18NSvg(tenantId, sourceWorkflowId, workflowId, svg);
                if (Objects.nonNull(body)) {
                    field.set(object, body);
                }
            } catch (Exception e) {
                log.warn("svg", e);
                return "";
            }
            return "";
        }

        public static Object workflowBase(String tenantId, Object object, Field r) {

            if (!(object instanceof List)) {
                return object;
            }
            try {
                ((List<?>) object).forEach(item -> {
                    //LaneStatics,WorkflowInstanceStatics >> TaskStatics >> WorkflowBaseStatics
                    Class<?> clazz = item.getClass();
                    //LaneStatics,WorkflowInstanceStatics,TaskStatics
                    String simpleName = clazz.getSimpleName();

                    Object workflowId = getSupperValue(clazz, item, "workflowId");
                    Object sourceWorkflowId = getSupperValue(clazz, item, "sourceWorkflowId");
                    Object id = getSupperValue(clazz, item, "id");
                    Field name = getSupperField(clazz, "name");


                    List<String> i18nKeyList = null;
                    switch (simpleName) {
                        case "LaneStatics":
                        case "TaskStatics":
                            i18nKeyList = Lists.newArrayList(Joiner.on(".").useForNull("").join("flow", workflowId, id, "name"), Joiner.on(".").useForNull("").join("flow", sourceWorkflowId, id, "name"));
                            break;
                        case "WorkflowInstanceStatics":
                            i18nKeyList = Lists.newArrayList(Joiner.on(".").useForNull("").join("flow", workflowId, "name"), Joiner.on(".").useForNull("").join("flow", sourceWorkflowId, "name"));
                            break;
                        default:
                            break;
                    }

                    try {
                        String i18nName = I18NParser.parse(tenantId, i18nKeyList);
                        log.info("tenantId:{},key:{},value:{}", tenantId, i18nKeyList, i18nName);
                        if (StringUtils.isNotEmpty(i18nName)) {
                            name.set(item, i18nName);
                        }
                    } catch (Exception ignored) {
                        log.error("", ignored);
                    }
                });
                return object;
            } catch (Exception e) {
                log.warn("workflowBase", e);
                return object;
            }
        }


    private static Object getSupperValue(Class<?> clazz, Object data, String fieldName) {
        Field supperField = getSupperField(clazz, fieldName);
        try {
            return getValue(supperField, data);
        } catch (IllegalAccessException e) {
            return data;
        }
    }

    public static Object getValue(Field field, Object data) throws IllegalAccessException {
        return field.get(data);
    }


    private static Field getSupperField(Class<?> clazz, String fieldName) {
        Class<?> clazzClone = clazz;
        Field field = null;
        while (clazzClone != null) {
            Class<?> superclass = clazzClone.getSuperclass();
            try {
                field = getField(superclass, fieldName);
                clazzClone = null;
            } catch (NoSuchFieldException e) {
                clazzClone = superclass;
            }
        }
        return field;
    }

    public static Field getField(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        Field declaredField = clazz.getDeclaredField(fieldName);
        declaredField.setAccessible(true);
        return declaredField;
    }

    /**
     * svg解析器
     *
     * @param tenantId
     * @param workflowId
     * @param svg
     * @return
     */
    public static String getI18NSvg(String tenantId, String sourceWorkflowId, String workflowId, String svg) {
        try {
            //将svg解析为document
            Document parse = Jsoup.parseBodyFragment(svg);
            //通过  [data-id] 获取到所有的节点信息
            Elements elements = parse.select("[data-id]");
            //遍历节点信息
            for (Element element : elements) {
                String text = I18NParser.parse(tenantId, Lists.newArrayList("flow." + workflowId + "." + element.attr("data-id") + ".name", "flow." + sourceWorkflowId + "." + element.attr("data-id") + ".name"));
                if (!Strings.isNullOrEmpty(text)) {
                    Elements divNodeName = element.select("[node-name]");
                    for (Element nodeName : divNodeName) {
                        nodeName.select("tspan").remove();
                        Element i18nElm = new Element(Tag.valueOf("tspan"), "");
                        i18nElm.attr("x", "0");
                        //不同点是 纵坐标未居中
                        i18nElm.attr("y", "0");
                        i18nElm.text(text);
                        nodeName.appendChild(i18nElm);
                    }
                }
            }
            Element body = parse.body();
            return body.html();
        } catch (Exception e) {
            log.warn("{}", "svg", e);
            return svg;
        }
    }


    public static final Logger log = LoggerFactory.getLogger(CustomI18NHandler.class);
}
