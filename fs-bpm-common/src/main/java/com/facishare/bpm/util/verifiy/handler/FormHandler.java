package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpmn.definition.validate.FormValidateHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 5
 * 校验form内容
 * 将form中result的表达式提取出去
 * Created by cuiyongxu on 17/3/15.
 */
@Slf4j
public class FormHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {
        workflow.getUserTasks().forEach(userTask -> {

            Map<String, Object> bpmExtension = userTask.getExtension();
            //获取form中的result,需要校验variables
            List formObj = (List) bpmExtension.get(ExtensionKey.form);
            FormValidateHandler.validate(CollectionUtils.isEmpty(formObj) ? Collections.EMPTY_LIST : (List<Map<String, Object>>) formObj.get(0), userTask.isLinkAppEnable(), userTask.getName());
            if (null != formObj) {
                for (Object obj : formObj) {
                    if (obj instanceof List) {
                        List arrayList = (List) obj;
                        arrayList.stream().filter(item -> BPMConstants.ApproveResult.RESULT.equals(((Map) item).get("name"))).forEach(item -> {
                            workflow.getActivitiesExpression().add("activity_" + userTask.getId() + "##result");
                        });
                    } else {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_FORMAT_ERROR,userTask.getName());
                    }
                }
            }
            Collection activitiesExpressionList = workflow.getActivitiesExpression();
            Collection variablesList = workflow.getVariablesList();
            boolean flag = variablesList.containsAll(activitiesExpressionList);
            if (!flag) {
                log.error("定义中表达式未添加到变量定义中,请检查result");
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_EXPRESSION_NOT_FOUND_VARIABLE);
            }
        });
    }
}
