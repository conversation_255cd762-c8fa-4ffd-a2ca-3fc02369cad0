package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.*;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.after.CustomFunctionValidateActionHandler;
import com.facishare.bpm.util.verifiy.handler.bean.CustomFunction;
import com.facishare.bpm.util.verifiy.util.CustomVariableUtil;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date on 2018/6/1
 * @since 6.3
 */
@Slf4j
public class RuleHandler implements ValidateHandler {
    public static Map<String, TypeOperator> filterTypes;

    static {
        filterTypes = Maps.newHashMap();
        filterTypes.put("string", new StringOperator());
        filterTypes.put("boolean", new BooleanOperator());
        filterTypes.put("number", new NumberOperator());
    }

    @Override
    public void validate(Workflow workflow) {
        ValidateResult rst;
        if (workflow.getRule() != null) {
            rst = validateConditionOperator(workflow,workflow.getRule().getConditions());
            if (!rst.isValid()) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_TRIGGER_CONDITION, rst.getMessage());
            }
            List<String> list = workflow.getRule().getAllFields();
            rst = ValidateVariableAndContentUtils.validateContent(workflow, list,
                    workflow.getPreActivityFun(null));
            if (!rst.isValid()) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_TRIGGER_CONDITION, rst.getMessage());
            }

            WorkflowRule rule = workflow.getRule();
            if (Objects.nonNull(rule)) {
                rule.validateContainSubDept(workflow.getServiceManager());
                validateLeftSideFieldType(rule.getConditions(), workflow.getServiceManager());
            }
        }

        //校验userTask
        for (UserTaskExt userTaskExt : workflow.getUserTasks()) {
            // 节点上同意的rule
            validateUserTaskRule(workflow, userTaskExt, userTaskExt.getPassCompleteRule());
            // 节点上不同意的rule,目前只支持审批节点  2019年01月28日20:26:52
            validateUserTaskRule(workflow, userTaskExt, userTaskExt.getRejectCompleteRule());
            // 同意的functionRule
            validateUserTaskFunctionRule(workflow,userTaskExt, userTaskExt.getPassFunctionRule());
            // 不同意的functionRule
            validateUserTaskFunctionRule(workflow,userTaskExt, userTaskExt.getRejectFunctionRule());
        }
        //校验等待节点rule
        for (ExecutionTaskExt executionTaskExt : workflow.getDelayExecutionTaskExt()) {
            validateUserTaskRule(workflow, executionTaskExt, executionTaskExt.getRule());
            validateUserTaskFunctionRule(workflow, executionTaskExt, executionTaskExt.getFunctionRule());
        }
    }

    private void validateUserTaskFunctionRule(Workflow workflow,ActivityExt activityExt, CustomFunction functionRule) {
        if (functionRule != null) {
            ValidateResult result = CustomFunctionValidateActionHandler.validateFunction(activityExt, workflow, functionRule);
            if (result.isNotValid()) {
                //节点:{0} 完成条件 {2}
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NODE_COMPLETE_CONDITION, activityExt.getName(), result.getMessage());
            }
        }
    }

    private void validateUserTaskRule(Workflow workflow, ActivityExt activityExt, WorkflowRule workflowRule) {
        if (workflowRule != null) {
            ValidateResult rst = validateConditionOperator(workflow,workflowRule.getConditions());
            if (!rst.isValid()) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_COMPLETE_CONDITION, activityExt.getName(), rst.getMessage());
            }
            rst = ValidateVariableAndContentUtils.validateContent(workflow, workflowRule.getAllFields(), workflow.getPreActivityFun(activityExt));
            if (!rst.isValid()) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_NODE_COMPLETE_CONDITION, activityExt.getName(), rst.getMessage());
            }
        }
    }

    public static ValidateResult validateConditionOperator(Workflow workflow,List<WorkflowRule.RuleCondition> conditions) {
        for (WorkflowRule.RuleCondition condition : conditions) {
            ValidateResult result = validateSingleCondition(workflow, condition);
            if (!result.isValid()) {
                return result;
            }
        }
        return ValidateResult.ok();
    }

    /**
     * 验证单个条件
     *
     * @param workflow 工作流
     * @param condition 条件
     * @return 验证结果
     */
    private static ValidateResult validateSingleCondition(Workflow workflow, WorkflowRule.RuleCondition condition) {
        WorkflowRule.ConditionField leftSide = condition.getLeftSide();
        String fieldName = leftSide.getFieldName();
        String fieldSrc = leftSide.getFieldSrc();

        if (CustomVariableUtil.isCustomVariable(fieldSrc)) {
            return validateCustomVariable(workflow, fieldName);
        }

        return validateStandardField(condition, leftSide);
    }

    /**
     * 验证自定义变量
     *
     * @param workflow 工作流
     * @param fieldName 字段名
     * @return 验证结果
     */
    private static ValidateResult validateCustomVariable(Workflow workflow, String fieldName) {
        ExecutableWorkflowExt executableWorkflow = workflow.getExecutableWorkflow();
        if (!executableWorkflow.getCustomVariablesKeys().contains(fieldName)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_APPROVAL_CUSTOM_VARIABLE_UNDEFINE.text());
        }
        return ValidateResult.ok();
    }

    /**
     * 验证标准字段
     *
     * @param condition 条件
     * @param leftSide 左侧字段
     * @return 验证结果
     */
    private static ValidateResult validateStandardField(WorkflowRule.RuleCondition condition, WorkflowRule.ConditionField leftSide) {
        String fieldType = leftSide.getFieldType();
        String operator = condition.getOperator();
        TypeOperator typeOperator = filterTypes.get(fieldType);

        if (!typeOperator.isSupport(operator)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NOT_SUPPORT_OPERATE.text(typeOperator.getName(), operator));
        }

        WorkflowRule.ConditionField rightSide = condition.getRightSide();
        if (rightSide != null) {
            return validateConditionIgnoreCase(rightSide, leftSide.getFieldType());
        }

        return ValidateResult.ok();
    }

    /**
     * 校验过滤器关联类型的统计类型字段
     *
     * @param conditions
     * @param serviceManager
     */
    private void validateLeftSideFieldType(List<WorkflowRule.RuleCondition> conditions, RefServiceManager serviceManager) {
        if (CollectionUtils.isNotEmpty(conditions)) {
            conditions.forEach(condition -> {
                WorkflowRule.ConditionField leftSide = condition.getLeftSide();
                ValidateVariableAndContentUtils.validateFieldType(leftSide.getFieldName(), serviceManager);
            });
        }
    }

    @Data
    static abstract class TypeOperator {
        private String name;
        private List<String> operators = Lists.newArrayList();

        boolean isSupport(String operator) {
            return getOperators().contains(operator);
        }
    }

    static class StringOperator extends TypeOperator {
        public StringOperator() {
            setOperators(Lists.newArrayList("equals", "notEquals", "contains", "notContains", "hasAnyOf", "hasNoneOf", "empty", "notEmpty", "in", "notIn", "containsAll", "notContainsAll","listEquals","listNotEquals", "inStrings", "notInStrings"));
            //setName("字符串类型");
            setName(BPMI18N.PAAS_FLOW_BPM_VERIFIY_STRING_TYPE.text());
        }
    }

    static class BooleanOperator extends TypeOperator {
        public BooleanOperator() {
            setOperators(Lists.newArrayList("isTrue", "isFalse", "empty", "notEmpty"));
//            setName("布尔类型");
            setName(BPMI18N.PAAS_FLOW_BPM_BOOLEAN_TYPE.text());
        }
    }

    static class NumberOperator extends TypeOperator {
        public NumberOperator() {
            setOperators(Lists.newArrayList("==", "!=", ">", "<", ">=", "<=", "empty", "notEmpty", "containsAll", "notContainsAll"));
//            setName("数字类型");
            setName(BPMI18N.PAAS_FLOW_BPM_NUMBER_TYPE.text());
        }
    }

    private static ValidateResult validateConditionIgnoreCase(@NonNull WorkflowRule.ConditionField right, String leftFieldType) {
        if (right.getMetadata() != null){
            Object ignoreCase = right.getMetadata().get("ignoreCase");
            if (ignoreCase != null) {
                boolean ignoreCaseValue = Boolean.TRUE.equals(ignoreCase);
                if (ignoreCaseValue && !leftFieldType.equals("string")) {
                    return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VERIFIY_RIGHT_CONFIG_ERROR.text());
                }
            }
        }
        return ValidateResult.ok();
    }
}
