package com.facishare.bpm.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/5/22 10:17 上午
 */
public class DateUtils {

    private static final String FORMAT_DATETIME = "yyyy-MM-dd HH:mm:ss:SSS";

    public static String currentTimeMilliFormat(Long time) {
        if(Objects.isNull(time)){
            return "";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(FORMAT_DATETIME);
        return dateFormat.format(time);
    }

    public static String getFormatDate(Long time){
        if(Objects.isNull(time)){
            return "";
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date(time));
    }
}
