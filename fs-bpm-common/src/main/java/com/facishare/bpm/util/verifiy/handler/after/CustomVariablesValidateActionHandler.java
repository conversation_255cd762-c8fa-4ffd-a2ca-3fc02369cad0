package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpmn.definition.exception.WorkflowValidateException;
import com.facishare.bpmn.definition.util.I18NKeys;
import com.facishare.paas.I18N;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;


public class CustomVariablesValidateActionHandler implements ValidateActionHandler {

    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        String activityName = activity.getName();
        List customVariables = executionItem.getCustomVariables();
        if (CollectionUtils.isEmpty(customVariables)) {
            return ValidateResult.fail(I18N.text(I18NKeys.ValidateKey.FLOW_BPMN_CUSTOM_VARIABLES_NOT_SETTINGS.getKey(), activityName));
        }
        for (Object customVariable : customVariables) {
            if (customVariable instanceof Map) {
                Map customVariableMap = (Map) customVariable;
                Object id = customVariableMap.get("id");
                if (Objects.isNull(id)) {
                    return ValidateResult.fail(I18N.text(I18NKeys.ValidateKey.FLOW_BPMN_CUSTOM_VARIABLES_ASSIGNMENT_FIELD_NOT_SETTINGS.getKey(), activityName));
                }
                Object type = customVariableMap.get("type");
                if (Objects.isNull(type)) {
                    return ValidateResult.fail(I18N.text(I18NKeys.ValidateKey.FLOW_BPMN_CUSTOM_VARIABLES_ASSIGNMENT_TYPE_NOT_SETTINGS.getKey(), activityName));
                }
                Object value = customVariableMap.get("value");
                if("function".equals(type)){
                    if(Objects.isNull(value)){
                        throw new WorkflowValidateException(I18N.text(I18NKeys.ValidateKey.FLOW_BPMN_CUSTOM_VARIABLES_ASSIGNMENT_VALUE_NOT_SETTINGS.getKey()));
                    }
                    ValidateResult validateResult = CustomFunctionValidateActionHandler.getValidateResult(activity, workflow, (Map) value);
                    if(validateResult.isNotValid()){
                        return validateResult;
                    }
                }
            }
        }

        return ValidateResult.ok();
    }
}
