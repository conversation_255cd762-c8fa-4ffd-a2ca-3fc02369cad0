package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.Objects;

/**
 * @desc:
 * @author: liuch9175
 * @date: 2023/5/18 17:17
 * 1. 内部团队成员和外部团队成员不同时为空
 * 2.
 */
public class EditTeamMemberValidateActionHandler implements ValidateActionHandler {

    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        String activityName = activity.getName();
        Map<String, Object> actionMapping = executionItem.getActionMapping();
        if (MapUtils.isEmpty(actionMapping)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_CREATE_ERROR.text(activityName));
        }
        if (Objects.isNull(actionMapping.get("permissionType"))) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_PERMISSION_TYPE_ERROR.text(activityName));
        }
        if (Objects.isNull(actionMapping.get("type"))) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_TYPE_ERROR.text(activityName));
        }
        if(MapUtils.isEmpty((Map)actionMapping.get("teamMember")) && MapUtils.isEmpty((Map)actionMapping.get("outTeamMember"))){
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EDIT_TEAM_MEMBER_PERSON_NOT_EMPTY.text(activityName));
        }
        return ValidateResult.ok();
    }
}
