package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import lombok.extern.slf4j.Slf4j;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/19 2:46 PM
 */
@Slf4j
public class UserTaskAatchEditMasterDetailObjectValidateHandler implements ValidateActivityHandler {

    private ExecutionTypeEnum executionType;


    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        return ValidateResult.ok();
    }


}
