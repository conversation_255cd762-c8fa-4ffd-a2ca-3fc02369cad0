package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.bpmn.definition.model.FindDescribe;
import com.facishare.bpmn.definition.model.FlowType;
import com.facishare.bpmn.definition.model.ValidBean;
import com.facishare.bpmn.definition.util.ExpressionUtil;
import com.facishare.bpmn.definition.validate.AfterActionValidateHandler;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.ENTITY_FIELD_SPLIT;

/**
 * <AUTHOR>
 * @Date on 2018/4/25
 * @since 6.3
 * 1. 对象类型不能为空
 * 2. 更新的目标字段不能为空
 * 3. 更新的字段值不能为空
 */
public class UpdateValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        String activityName = getActivityName(activity);
        RefServiceManager serviceManager = workflow.getServiceManager();

        List<UpdateField> updateFields = JsonUtil.fromJson(executionItem.getUpdateFieldJson(), new TypeToken<List<UpdateField>>() {
        }.getType());
        if (CollectionUtils.isEmpty(updateFields)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VALIDATE_UPDATE_FORM_IS_BLANK.text(activityName));
        }
        checkRelatedManyExecutionItem(serviceManager, activityName, executionItem, updateFields);

        for (UpdateField updateField : updateFields) {
            if (Strings.isNullOrEmpty(updateField.entityId)) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VALIDATE_UPDATE_OBJECT_TYPE_IS_BLANK.text(activityName));
            }
            if (Strings.isNullOrEmpty(updateField.value + "")) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VALIDATE_UPDATE_FIELD_VALUE_IS_BLANK.text(activityName));
            }
            if (Strings.isNullOrEmpty(updateField.key)) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_VALIDATE_UPDATE_FIELD_NAME_IS_BLANK.text(activityName));
            }
            AfterActionValidateHandler.AfterActionValidates.checkUpdateFieldKey(updateField.key, ValidBean.createByBpmnRefService(executionItem, FlowType.workflow_bpm, serviceManager.getTenantId(), activity.getEntityId(), getFunction(serviceManager), null));
        }
        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                UpdateField.format2BPMFieldVariable(updateFields, executionItem.getRelatedObjectId(), executionItem.getUseRelated()),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_NODE_UPDATE_FIELD.text(activityName, rst.getMessage()));
        }
        return rst;
    }

    /**
     * AfterActionValidateHandler.AfterActionValidates中获取对象描述的方法实现
     * @param serviceManager
     * @return
     */
    private static Function<FindDescribe.DescribeQuery, Map> getFunction(RefServiceManager serviceManager) {
        return describeQuery -> {
            RemoteContext context = new RemoteContext();
            context.setUserId(BPMConstants.CRM_SYSTEM_USER);
            context.setTenantId(describeQuery.getTenantId());
            context.setAppId(BPMConstants.CRM_APP_ID);
            String entityId = describeQuery.getEntityId();
            return serviceManager.findDescribe(entityId, true, false);
        };
    }

    /**
     * 校验字段更新后动作-更新关联对象的场景
     * @param activityName
     * @param executionItem
     */
    public static void checkRelatedManyExecutionItem(RefServiceManager serviceManager, String activityName, ExecutionItem executionItem, List<UpdateField> updateFields) {
        String relatedEntityId = executionItem.getRelatedEntityId();
        String relatedObjectId = executionItem.getRelatedObjectId();
        if(Boolean.TRUE.equals(executionItem.getUseRelated())) {
            String entityId = updateFields.get(0).getEntityId();
            String[] entityIdExpression = entityId.split(ENTITY_FIELD_SPLIT);
            String realEntityId;
            if(entityId.contains(BPMConstants.SELF_REF_KEY)) {  // 自关联的entityId：activity_17483279960151##SelfRef##object_8F03i__c
                realEntityId = entityIdExpression[2];
            } else {
                realEntityId = entityIdExpression[1];
            }
            relatedObjectId = relatedObjectId.replace(entityId, realEntityId);
            if(StringUtils.isEmpty(relatedEntityId)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ACTION_CANNOT_BE_NULL_AFTER_NODE_FIELD_UPDATE, activityName, "relatedEntityId");
            }
            if(StringUtils.isEmpty(relatedObjectId)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ACTION_CANNOT_BE_NULL_AFTER_NODE_FIELD_UPDATE, activityName, "relatedObjectId");
            }
            if(Splitter.on(".").splitToList(relatedObjectId).size() > 6) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ACTION_CANNOT_BE_NULL_AFTER_NODE_FIELD_UPDATE, activityName);
            }

            AfterActionValidateHandler.AfterActionValidates.checkRelatedObjectId(relatedObjectId, ValidBean.createByBpmnRefService(executionItem, FlowType.workflow_bpm, serviceManager.getTenantId(), realEntityId, getFunction(serviceManager), null));
            List<Map<String, Object>> update = JsonUtil.fromJson(executionItem.getUpdateFieldJson(), List.class);
            for (Map<String, Object> item : update) {
                String key = ExpressionUtil.getInternalExpressionByPoint((String) item.get("key")).get(0);
                if(StringUtils.isNotEmpty(key)) {
                    String[] keyList = key.split(BPMConstants.POINT);
                    if (!keyList[0].equals(relatedEntityId)) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.THE_OBJECT_TO_BE_UPDATED_SHOULD_BE_CONSISTENT_WITH_THE_RELATED_ENTITY_ID, activityName);
                    }
                    if(keyList.length > 2) {
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.THE_NUMBER_OF_KEY_LEVELS_CANNOT_EXCEED_3, activityName);
                    }
                }
            }

            Set<String> entityIds = update.stream().map(item -> (String)item.get("entityId")).collect(Collectors.toSet());
            if(entityIds.size() > 1) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.THE_RELATEDENTITYID_DOES_NOT_MATCH_THE_ENTITYID_OF_THE_UPDATED_DATA, activityName);
            }
        } else {
            if(StringUtils.isNotEmpty(relatedEntityId) || StringUtils.isNotEmpty(relatedObjectId)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.RELATED_ENTITY_ID_OR_RELATED_OBJECT_ID_CANNOT_HAVE_A_VALUE, activityName);
            }
        }
    }

    @Data
    public static class UpdateField{
        boolean isCalculate;
        String key;
        Object value;
        Object defaultValue;
        String entityId;

//          "isCalculate":true,
//          "key":"${object_0iOs7__c.field_H2s0q__c}",
//          "value":1520438400000,
//          "defaultValue":"",
//          "entityId":"activity_1521601341848##object_0iOs7__c",
//          "valueLabel":"2018年03月08日"

        //${object_0iOs7__c.field_H2s0q__c}  ->  object_0iOs7__c 替换成  activity_1521601341848##object_0iOs7__c  =  activity_1521601341848##object_0iOs7__c##field_H2s0q__c
        static List<String> format2BPMFieldVariable(List<UpdateField> fields, String relatedObjectId, Boolean useRelated){
            //return fields.stream().map(field->field.key.replace(field.entityId.split(ENTITY_FIELD_SPLIT)[1],field.entityId).replace(".",ENTITY_FIELD_SPLIT)).collect(Collectors.toList());
            if(Boolean.TRUE.equals(useRelated)) {
                return Lists.newArrayList(relatedObjectId);
            }
            return fields.stream().map(field -> {
                String[] entityIdExpression = field.entityId.split(ENTITY_FIELD_SPLIT);
                String entityId = entityIdExpression[1];
                if (BPMConstants.SELF_REF_KEY.equals(entityId)) {
                    entityId = entityIdExpression[2];
                }
                return field.key.replace(entityId, field.entityId).replace(".", ENTITY_FIELD_SPLIT);
            }).collect(Collectors.toList());
        }
    }
}
