package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.bpmn.ExecutionTaskExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.util.BPMExtensionUtils;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.activity.ActivityValidateManager;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 1. 主要对节点配置的信息的 合法性做深度校验
 * 2. 后动作校验 请参考 AfterActionHandler
 * 3. 节点的基本信息校验 请参考 ExtensionHandler
 * Created by Aaron on 18/4/24.
 */
@Slf4j
public class ActivityHandler implements ValidateHandler {

    @Override
    public void validate(Workflow workflow) {
        //validateExecutionActivity(workflow);
        normalValidate(workflow);
        validateLinkApp(workflow);
    }

    /**
     * 校验互联应用相关
     */
    public void validateLinkApp(Workflow workflow) {
        validateLinkAppOfActivityFromUserTask(workflow);
        validateLinkActivityFromUserExecutionTask(workflow);
    }

    /**
     * 校验userTask 配置的前置节点 是否支持互联
     */
    private void validateLinkAppOfActivityFromUserTask(Workflow workflow) {
        List<UserTaskExt> userTasks = workflow.getUserTasks();
        userTasks.forEach(userTaskExt -> {
            validateLinkAppFromUserTaskExt(userTaskExt);
            validatePreNodeFromUserTaskExt(userTaskExt, workflow.getUserTaskMap());
            validateDefaultButtons(userTaskExt);
            addExternalFlowActivityLinkApp(workflow.getExternalFlow(), userTaskExt);
        });
    }

    /**
     * 校验自定义按钮
     *
     * @param userTaskExt
     */
    public void validateDefaultButtons(UserTaskExt userTaskExt) {
        Object defaultButtonObjs = userTaskExt.getDefaultButtons();
        if (Objects.isNull(defaultButtonObjs)) {
            return;
        }
        //defaultButtons 应该是map,不是的话 报错
        if (!(defaultButtonObjs instanceof Map)) {
            log.error("{} defaultButtons 结构有误,", userTaskExt.getName());
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_CUSTOM_BUTTON_DATA_STRUCTURE_IS_INCORRECT, userTaskExt.getName());
        }
        Map<String, Object> defaultButtons = (Map<String, Object>) defaultButtonObjs;
        defaultButtons.forEach((key, value) -> {
//            if (!(value instanceof Map)) {
//                log.error("{} 自定义按钮格式有误:{}", value);
//                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_CUSTOM_BUTTON_DATA_STRUCTURE_IS_INCORRECT);
//            }
            if (value instanceof String) {
                log.warn("{} 自定义按钮格式有误:{}", key, value);
                Map<String, Object> label = Maps.newHashMap();
                label.put("label", value);
                defaultButtons.put(key, label);
            }
        });
    }

    /**
     * 外部流程  需要在节点上添加appId
     *
     * @param userTask
     */
    public void addExternalFlowActivityLinkApp(Integer externalFlow, UserTaskExt userTask) {
        if (Objects.nonNull(externalFlow) && externalFlow == 1) {
            userTask.setProperty(WorkflowKey.linkAppEnable, Boolean.TRUE);
            userTask.setProperty(WorkflowKey.linkApp, BPMConstants.externalFlowLinkApp);
            userTask.setProperty(WorkflowKey.linkAppName, BPMConstants.externalFlowLinkAppName);
            userTask.setProperty(WorkflowKey.linkAppType, BPMConstants.externalFlowLinkAppType);
        }
    }

    /**
     * 若启动了互联应用则linkApp不为空
     * 若外部角色不为空or相关对象的人员字段含有外部负责人，则必须是启动互联应用
     * 若节点启动互联应用，则不能指定下一节点处理人
     */
    private void validateLinkAppFromUserTaskExt(UserTaskExt userTaskExt) {
        if (userTaskExt.isLinkAppEnable()) {
            //若启动了互联应用则linkApp不为空
            if (StringUtils.isBlank(userTaskExt.getLinkApp())) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_BASIC_LINK_APP_CAN_NOT_BE_EMPTY);
            }
            //若节点启动互联应用，则不能指定下一节点处理人
            if (userTaskExt.isAssignNextTask()) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_ASSIGN_NEXT_TASK, userTaskExt.getName());
            }
        } else {
            Set<Object> externalRole = userTaskExt.getAssignee().get(BPMConstants.Assignee.EXTERNAL_ROLE);
            Set<Object> extUserType = userTaskExt.getAssignee().get(BPMConstants.Assignee.EXT_USER_TYPE);

            //若外部角色不为空or相关对象的人员字段含有外部负责人，则必须是启动互联应用
            if (CollectionUtils.isNotEmpty(externalRole) || containsOutOwnerOnExtUserType(extUserType)) {
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NODE_LINK_APP_AND_EXTERNAL_ROLE, userTaskExt.getName());
            }
        }
    }

    /**
     * 业务节点配置的处理人和超时提醒选择的前置节点不支持互联节点
     */
    private void validatePreNodeFromUserTaskExt(UserTaskExt userTaskExt, Map<String, UserTaskExt> userTaskMap) {
        //超时提醒:提醒人为前置节点
        Set<Object> extBpm = userTaskExt.getExtBpmFromReminders();
        //节点处理人:前置节点处理人
        Set<Object> assigneeExtBpm = userTaskExt.getAssignee().get(BPMConstants.Assignee.EXT_BPM);
        if (CollectionUtils.isNotEmpty(assigneeExtBpm)) {
            extBpm.addAll(assigneeExtBpm);
        }
        if (CollectionUtils.isEmpty(extBpm) || MapUtils.isEmpty(userTaskMap)) {
            return;
        }
        //业务节点
        if (!userTaskExt.isLinkAppEnable()) {
            extBpm.forEach(personExpression -> {
                String personExpressionString = String.valueOf(personExpression);
                if (personExpressionString.contains("activity")) {
                    BPMExtensionUtils.Expression expression = BPMExtensionUtils.getExpression(personExpressionString);
                    UserTaskExt preUserTask = userTaskMap.get(expression.getActivityId());
                    //前置节点不支持互联节点
                    if (Objects.nonNull(preUserTask) && preUserTask.isLinkAppEnable()) {
                        //业务活动-超时提醒 ，前置节点： 业务活动-prm 不支持互联应用
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_EXTERNAL_APPLY_TASK_NONSUPPORT_LINK_APP,
                                userTaskExt.getName(), BPMI18N.PAAS_FLOW_BPM_FRONT_NODE.text(), preUserTask.getName());
                    }
                }
            });
        }
    }

    /**
     * 校验自动节点是否配置了互联应用
     */
    private void validateLinkActivityFromUserExecutionTask(Workflow workflow) {
        List<ExecutionTaskExt> executionTasks = workflow.getExecutionTasks();
        if (CollectionUtils.isNotEmpty(executionTasks)) {
            executionTasks.forEach(executionTaskExt -> {
                if (executionTaskExt.isLinkAppEnable()) {
                    if(Objects.isNull(executionTaskExt.getLinkApp())){
                        throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ACTIVITY_NOT_CHOOSE_LINK_APP,
                                executionTaskExt.getName(), StringUtils.EMPTY, StringUtils.EMPTY);
                    }
                }
            });
        }
    }


    /**
     * 是否包含外部负责人
     */
    private boolean containsOutOwnerOnExtUserType(Set<Object> extUserType) {
        return CollectionUtils.isNotEmpty(extUserType) && extUserType.stream().map(String::valueOf).anyMatch(expression -> expression.contains(BPMConstants.Assignee.OUT_OWNER));
    }



    private void normalValidate(Workflow workflow) {
        if (!workflow.getActivities().stream().anyMatch(t-> !("startEvent".equals(t.getType()) ||"endEvent".equals(t.getType())))){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_AVAILABLE_NODE_ERROR);
        }

        //        此处只做 人操作的节点 和 自动节点
        StringBuilder sb = new StringBuilder();

        workflow.getUserTasks().forEach(item -> {
            ValidateResult temp = ActivityValidateManager.validate(workflow, item);
            if (!temp.isValid()) {
                sb.append(temp.getMessage()).append(" ");
            }
        });

        workflow.getExecutionTasks().forEach(item -> {

            ValidateResult temp = ActivityValidateManager.validate(workflow, item);
            if (!temp.isValid()) {
                sb.append(temp.getMessage()).append(" ");
            }
        });
        if (sb.length() > 0) {
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_SERVICE_EXECUTE_EXCEPTION, sb.toString());
        }
    }


}
