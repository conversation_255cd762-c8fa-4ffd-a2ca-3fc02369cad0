package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.model.Pair;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UserTaskOperationMultiValidateHandler implements ValidateActivityHandler{

    @Override
    public ValidateResult validate(Workflow workflow, ActivityExt activity) {
        //校验commonButtonApiNames不能是空，且个数不能超过三个（暂不判断）
        List<String> commonButtonApiNames = activity.getCommonButtonApiNames();
        if(CollectionUtils.isEmpty(commonButtonApiNames)){
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_COMMON_BUTTON_NUMBER_ERROR.text(activity.getName()));
        }
        //判断下按钮是否禁用或删除
        List<FindCustomButtonList.CustomButton> allCustomButtonList = workflow.getServiceManager().findCustomButtonList(activity.getEntityId(), Boolean.TRUE);
        //btnApiName,<btnName,availableFlag>
        Map<String, Pair<String, Boolean>> allCustomButtonMap = allCustomButtonList.stream()
                .collect(Collectors.toMap(FindCustomButtonList.CustomButton::getApi_name, item -> new Pair(item.getLabel(), item.is_active())));
        for (String apiName : commonButtonApiNames) {
            if(!allCustomButtonMap.containsKey(apiName)){
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_COMMON_BUTTON_NOT_EXIST.text(activity.getName(), ""));
            }
            if(Boolean.FALSE.equals(allCustomButtonMap.get(apiName).getValue())){
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_NODE_COMMON_BUTTON_NOT_EXIST.text(activity.getName(), allCustomButtonMap.get(apiName).getKey()));
            }
        }
        return ValidateResult.ok();
    }
}
