package com.facishare.bpm.util;

import lombok.Getter;

/**
 * Created by wangz on 17-7-31.
 */
public interface TransferDataConstants {
    String activityInstances = "activityInstances";
    String activityId = "activityId";
    String activityName = "activityName";
    String APINAME_INSTANCE = "BpmInstance";
    String APINAME_TASK = "BpmTask";
    String APINAME_TASK_OPINION= "ApproverOpinionObj";
    String APINAME_TASK_HANDLE_TIME_DETAIL= "FlowTaskHandleTimeDetailObj";
    String tenantId = "tenantId";
    String id = "_id";
    String completed = "completed";
    String candidateIds = "candidateIds";
    String candidates = "candidates";
    String owner = "owner";
    String recordType = "record_type";
    String APP_ID_LABEL = "appId";
    String FIELD_INSTANCEID = "workflowInstanceId";
    String ID = "id";
    String startTime = "start";
    String TASK_HANDLE_TIME_DETAIL_NORMAL= "normal";
    String TASK_HANDLE_TIME_DETAIL_SUSPEND= "suspend";
    String TASK_HANDLE_TIME_DETAIL_OBJ_TASK_ID_FIELD= "task_id";
    String TASK_HANDLE_TIME_DETAIL_OBJ_TASK_API_NAME_FIELD= "task_api_name";
    String TASK_HANDLE_TIME_DETAIL_OBJ_END_TIME_FIELD= "end_time";
    String TASK_HANDLE_TIME_DETAIL_OBJ_USER_ID_FIELD= "user_id";
    String TASK_HANDLE_TIME_DETAIL_ACTUAL_DURATION_FIELD= "actual_duration";

    enum MDField {
        sourceWorkflowId("sourceWorkflowId"),
        name("name"),
        workflowName("workflowName"),

        entityId("objectApiName"),
        objectId("objectDataId"),
        activityInstanceId("activity_instance_id"),
        taskType("task_type"),

        workflowId("workflowId"),
        state("state"),
        objectIds("objectIds"),
        duration("duration"),

        owner("owner"),
        relevant_team("relevant_team"),
        linkAppType("link_app_type"),
        linkApp("link_app_id");

        @Getter
        private String value;

        MDField(String value) {
            this.value = value;
        }
    }

    enum MDInstanceField {
        stageNames("stageNames"),
        taskNames("taskNames"),
        workflowName("workflowName"),
        start("startTime"),
        end("endTime"),
        modifyTime("lastModifyTime"),
        lastModifyBy("lastModifyBy"),
        applicantId("applicantId"),
        triggerSource("triggerSource"),
        externalFlow("externalFlow"),
        objectApiName("objectApiName"),
        objectDataId("objectDataId"),
        id("_id"),
        state("state"),
        createBy("created_by"),
        current_candidate_ids("current_candidate_ids");


        @Getter
        private String value;

        MDInstanceField(String value) {
            this.value = value;
        }
    }

    /**
     * key: sourceTask  数据同步过来的
     * value:mdTask     需要给自定义对象的key
     */
    enum MDTaskField {
        activityId("activityId"),
        taskName("taskName"),
        workflowName("workflowInstanceName"),
        stageId("stageId"),
        stageName("stageName"),
        workflowInstanceId("workflowInstanceId"),
        createTime("startTime"),
        modifyTime("endTime"),
        remindLatency("remindLatency"),
        latencyUnit("latencyUnit"),
        timeoutTime("timeoutTime"),
        isTimeout("isTimeout"),
        assigneeIds("processorIds"),
        processorIds("processorIds"),
        currentCandidateIds("current_candidate_ids"),
        assignee("assignees"),
        taskType("taskType"),
        opinions("opinions"),//2020年04月13日16:55:32
        candidateIds("candidateIds"),
        //assignees__c的子字段
        person("person"),
        dept("dept"),
        dept_leader("dept_leader"),
        group("group"),
        role("role"),
        ext_bpm("ext_bpm"),

        createBy("created_by"),
        execution_type("execution_type"),
        action_code("action_code"),
        session_key("session_key");

        @Getter
        private String value;

        MDTaskField(String value) {
            this.value = value;
        }
    }
}
