package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 6.3
 *
 * 1. 外部通知标题不为空
 * 2. 外部通知内容不为空
 * 3. 提醒人员 or (下游企业匹配的字段 and 外部角色) 不为空
 * 4. 当下游企业匹配的字段选择联系人时，不需要校验外部角色
 * 5. 除联系人对象外，外部角色必填
 */
public class ExternalMessageValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        ExternalMessage externalMessage = JsonUtil.fromJson(JsonUtil.toJson(executionItem.getActionMapping()), ExternalMessage.class);
        String activityName = getActivityName(activity);

        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_REMINDER.text(activityName), activity, externalMessage.getReceiverIds(), workflow);
        if (Strings.isNullOrEmpty(externalMessage.getTitle())) {
            return new ValidateResult(false, BPMI18N.PAAS_FLOW_BPM_VALIDATE_EXTERNAL_REMIND_IS_BLANK.text(activityName));
        }
        if (Strings.isNullOrEmpty(externalMessage.getContent())) {
            return new ValidateResult(false, BPMI18N.PAAS_FLOW_BPM_VALIDATE_EXTERNAL_CONTENT_IS_BLANK.text(activityName));
        }
        ValidateResult rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                externalMessage.getTitle(),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_TITLE.text(activityName, rst.getMessage()));
            return rst;
        }

        rst = ValidateVariableAndContentUtils.validateContent(
                workflow,
                externalMessage.getContent(),
                workflow.getPreActivityFun(activity));
        if (!rst.isValid()) {
            rst.setMessage(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_CONTENT.text(activityName, rst.getMessage()));
            return rst;
        }

        List<DownObject> downObjects = externalMessage.getDownObjects();
        List<String> roleIds = externalMessage.getRoleIds();
        Map<String, Set<Object>> receiverIds = externalMessage.getReceiverIds();

        if (MapUtils.isEmpty(receiverIds)) {
            return verifyFieldsMatchedByDownstreamEnterprise(downObjects, roleIds);
        } else {
            if (!UserTaskExt.hasAssignee(receiverIds)) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_REMINDER_NOT_NULL.text());
            }

            if (Objects.nonNull(downObjects)) {
                return verifyFieldsMatchedByDownstreamEnterprise(downObjects, roleIds);
            }
        }

        return rst;
    }

    public static ValidateResult verifyFieldsMatchedByDownstreamEnterprise(List<DownObject> downObjects, List<String> roleIds) {

        if (Objects.isNull(downObjects)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_OUT_TENANT_MATCHED_FIELD_REQUIRED.text());
        }
        if (downObjects.size() > 1) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_OUTER_MATCHED_FIELD_IS_ONLY_OPTION.text());
        }

        DownObject downObject = downObjects.get(0);//6.3.2 下游企业匹配的字段只支持单选
        if (BPMConstants.MetadataKey.CONTACTOBJ_API_NAME.equals(downObject.getObjectApiName())) {
            return ValidateResult.ok();
        } else {
            if (CollectionUtils.isEmpty(roleIds)) {
                return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_EXTERNAL_ROLE_NOT_NULL.text());
            }
        }
        return ValidateResult.ok();
    }

    @Data
    class ExternalMessage {
        private String title;
        private String content;
        private Map<String, Set<Object>> receiverIds;//提醒人员
        private List<DownObject> downObjects;//下游企业匹配的字段
        private List<String> roleIds;//外部角色
    }

    @Data
    public static class DownObject {
        private String objectApiName;
        private String objectId;
    }
}
