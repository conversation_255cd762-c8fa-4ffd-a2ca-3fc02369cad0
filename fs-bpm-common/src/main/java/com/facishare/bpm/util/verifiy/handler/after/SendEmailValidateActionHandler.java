package com.facishare.bpm.util.verifiy.handler.after;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutionItem;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.i18n.BPMI18N;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.google.common.base.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 6.2
 * 1. 收件人不能为空,收件人必做是合法的
 * 2. 发件人不能为空
 * 3. 模板不能为空
 * 4. 收件人或收件邮箱必做有一个不为空
 */
public class SendEmailValidateActionHandler implements ValidateActionHandler {
    @Override
    public ValidateResult validate(ActivityExt activity, ExecutionItem executionItem, Workflow workflow) {
        Map<String, Set<Object>> recipients = executionItem.getRecipients();
        String taskName = getActivityName(activity);
        AssigneeHandler.validateAssignee(BPMI18N.PAAS_FLOW_BPM_EMAIL_TARGET.text(taskName), activity, recipients, workflow);
        if (Strings.isNullOrEmpty(executionItem.getTemplate())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_EMAIL_TEMPLATE_IS_EMPTY.text(taskName));
        }
        boolean hasRecipients = (MapUtils.isNotEmpty(recipients) && UserTaskExt.hasAssignee(recipients));
        boolean hasEmailAddress = CollectionUtils.isNotEmpty(executionItem.getEmailAddress());
        if (!(hasRecipients || hasEmailAddress)) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_EMAIL_TARGET_IS_EMPTY.text(taskName));
        }
        if (Strings.isNullOrEmpty(executionItem.getSender())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_EMAIL_SENDER_IS_EMPTY.text(taskName));
        }
        if (Strings.isNullOrEmpty(executionItem.getTitle())) {
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_TEMPLETE_NAME_NOT_NULL.text(taskName));
        }
        if (!workflow.getServiceManager().checkEmailEnable(executionItem.getSender())){
            return ValidateResult.fail(BPMI18N.PAAS_FLOW_BPM_SEND_EMAIL_SENDER_IS_INVALID.text(taskName));
        }
        return ValidateResult.ok();
    }
}
