package com.facishare.bpm.util;

/**
 * <AUTHOR>
 * @date 3/4/25
 * @apiNote
 **/
public class NumberUtil {
    /**
     * 获取毫秒值
     */
    public static Double getRemindLatency(Integer latencyUnit, Double remindLatencyData) {
        Double remindLatency=0D;
        switch (latencyUnit) {
            case 1:
                remindLatency = remindLatencyData * 3600 * 1000L * 24;
                break;
            case 2:
                remindLatency = remindLatencyData * 3600 * 1000L;
                break;
            case 3:
                remindLatency = remindLatencyData * 60 * 1000L;
                break;
        }
        return remindLatency;
    }
}
