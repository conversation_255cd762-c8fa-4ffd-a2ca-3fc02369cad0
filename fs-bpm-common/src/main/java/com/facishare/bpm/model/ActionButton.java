package com.facishare.bpm.model;

import com.facishare.bpm.utils.i18n.I18NAlias;
import com.facishare.bpm.utils.i18n.I18NAliases;
import com.facishare.bpm.utils.i18n.I18NExpression;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActionButton {
    @I18NAliases(value = {
            @I18NAlias(value = "Update", alias = "update"),
            @I18NAlias(value = "BatchEditMasterDetailObject", alias = "batchEditMasterDetailObject"),
            @I18NAlias(value = "AddRelatedObject", alias = "addRelatedObject"),
            @I18NAlias(value = "AddMDObject", alias = "addMDObject"),
            @I18NAlias(value = "BatchAddRelatedObject", alias = "batchAddRelatedObject"),
           // @I18NAlias(value = "UpdateAndComplete", alias = "updateAndComplete")
    })
    private String action;
    @I18NExpression(relation = {"${workflowId.activityId.action}", "${sourceWorkflowId.activityId.action}"})
    private String label;
    private String code;
    private Boolean right;
    /**
     * 780 新增 应用节点跳转用
     */
    private String todoJumpUrl;

    /**
     * 780 新增 应用节点 web需要事件
     */
    private String event;

    /**
     * 780 按钮添加颜色 深研 只针对应用节点
     */
    private String btnClass;

    /**
     * 按钮顺序
     */
    private Integer order;

    private transient boolean i18nConvert = true;

    public String getLabel() {
        return label;
    }

    public ActionButton(String action, String label) {
        this.action = action;
        this.label = label;
    }

    public ActionButton(String action, String code, String label) {
        this.action = action;
        this.code = code;
        this.label = label;
    }

    public ActionButton(String action, String label, Boolean right) {
        this.action = action;
        this.label = label;
        this.right = right;
    }

    public ActionButton(String action, String label, Integer order) {
        this.action = action;
        this.label = label;
        this.order = order;
    }

    public ActionButton(String action, String label, String code, String todoJumpUrl, String event, String btnClass) {
        this.action = action;
        this.label = label;
        this.code = code;
        this.event = event;
        this.todoJumpUrl = todoJumpUrl;
        this.i18nConvert = false;
        this.btnClass = btnClass;
    }

}