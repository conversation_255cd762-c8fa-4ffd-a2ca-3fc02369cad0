package com.facishare.bpm.model;

import com.facishare.rest.core.model.ClientInfo;
import com.facishare.rest.core.model.ClientType;
import lombok.Data;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * desc:承载特殊常量 T
 * version: 6.7
 * Created by cuiyongxu on 2019/10/10 4:33 PM
 */
@Data
public class TaskParams {

    /**
     * 2019年10月10日16:35:00
     *
     * 目前有个逻辑,节点上配置了任务完成时,可以指定下一节点处理人
     * IOS和Android 在点击 保存并完成任务的时候,会弹出框,让用户选择下一节点处理人
     * 但是web端则不同,用户点击填写,弹出框里面,如果有保存并完成该任务,点击保存并完成任务,不会
     *  要求指定下一节点处理人,故添加此属性
     *
     * TaskButtonManagerImpl 更新时使用
     */
    private boolean updateAndCompleteAssignNextNodeProcessor;

    /**
     * 2019年10月18日12:02:48
     *
     * 目前存在一个特殊逻辑
     * 签到组件在终端form中是不需要显示的
     * 而在新版UE需要展示签到组件
     */
    private boolean notNeedSignIn;

    /**
     * 终端保存和保存并完成任务需要在layout中展示,需要将自定义的按钮保存进去
     */
    private Collection<ActionButton> buttons;

    /**
     * 2019年10月24日14:42:26 添加 无特殊含义,只是参数承载,获取laneTasks时,需
     * 要判断当前数据详情页是否和当前任务属于同一条数据,如果不是,则不下发任何按钮
     * 前端 则显示去处理
     */
    private String entityId;
    private String objectId;

    /**
     * 是否为终端调用
     *
     * 针对终端查询任务详情接口存在2个;
     *
     * MTask.GetTask  历史查询任务的接口,如果是终端调用上来的,后端不需要拼装签到组件或者支付组件的layout,完全由终端或h5根据下发的form信息拼接签到组件或者支付组件,故需要设置isMobile=ture
     *
     * MTask.GetTaskInfo  新接口,针对于业务流新版UE及卡梅隆页面设计的新接口,数据结构与MTask.GetTask 接口不同,且下发全量数据,如果是这个接口  这里的值不能为true
     */
    private boolean ignoreSignAndPayGroupLayout;

    /**
     * 和isMobile相同含义,但是唯一不同的是  该字段不会对layout造成影响,如果isMobile为true,则对新接口来说, 不会拼接签到和支付的layout
     * 此值主要应用在 深研  的去处理按钮,  终端调用和web调用时  调用深研接口的入参也随之不同
     *
     * h5的请求  是无法得知是哪个端的
     */
    private boolean isMobile;

    /**
     * 是否是任务落地页  任务落地页 不需要展示完成任务按钮,因为接口三端都在调,故添加此特殊逻辑
     *
     * 终端 会展示  填写  完成任务
     *
     * web端  保存并完成任务  保存
     */
    private boolean isTaskDetail;

    /**
     * 任务下所有数据的负责人
     */
    private Map<String, Map<String, List<String>>> dataListOwner;

    /**
     * 755 新增  用于判断是否是h5  如果是h5  且表单中只有签到组件  需要下发一个填写按钮
     */
    private String source;

    /**
     * 780 新增,应用节点是否下发多个按钮  @丁成
     */
    private boolean applyButtons;

    /**
     * 是否需要设置对象名称
     * 添加原因 :
     *     由于获取ObjectName需要先查询数据的objectDisplayName（需要调用查询对象数据的接口），
     *     当像调用getButtonByTaskId获取button时，并不需要ObjectName，所以添加此标志位，不需要查询objectDisplayName
     */
    private  boolean fromTaskDetail = Boolean.TRUE;

    /**
     * 获取阶段下的任务优化（GetTaskInfoByLaneId） 不查询数据、描述、扩展描述的标识及form信息
     * true-不获取；false或null 获取
     * @return
     */
    private Boolean notGetDatas;

    private Boolean isH5;

    /**
     *  查询业务流任务 不查询对象数据x
     */
    private Boolean isTaskNotGetData;
    /**
     * 是否包含任务Feed详情是否展示的配置
     */
    private Boolean includeTaskFeedDetailConfig;

    public static TaskParams create(){
        return new TaskParams();
    }

    public TaskParams clientInfo(ClientInfo clientInfo) {
        if (ClientType.ios.equals(clientInfo.getClientType()) || ClientType.android.equals(clientInfo.getClientType())) {
            this.isMobile(true);
        }
        return this;
    }

    public Boolean getIncludeTaskFeedDetailConfig() {
        if(Objects.isNull(includeTaskFeedDetailConfig)){
            includeTaskFeedDetailConfig=false;
        }
        return includeTaskFeedDetailConfig;
    }

    public TaskParams source(String source) {
        this.source = source;
        return this;
    }

    public TaskParams applyButtons(boolean applyButtons) {
        this.applyButtons = applyButtons;
        return this;
    }

    public TaskParams includeTaskFeedDetailConfig(Boolean includeTaskFeedDetailConfig) {
        this.includeTaskFeedDetailConfig = Boolean.TRUE.equals(includeTaskFeedDetailConfig);
        return this;
    }

    public TaskParams notGetDatas(Boolean notGetDatas) {
        this.notGetDatas = notGetDatas;
        return this;
    }

    public TaskParams addH5(Boolean isH5) {
        this.isH5 = isH5;
        return this;
    }


    public TaskParams updateAndCompleteAssignNextNodeProcessor(boolean updateAndCompleteAssignNextNodeProcessor) {
        this.updateAndCompleteAssignNextNodeProcessor = updateAndCompleteAssignNextNodeProcessor;
        return this;
    }

    public TaskParams notNeedSignIn(boolean notNeedSignIn) {
        this.notNeedSignIn = notNeedSignIn;
        return this;
    }

    public TaskParams isIgnoreSignAndPayGroupLayout(boolean isMobile) {
        this.ignoreSignAndPayGroupLayout = isMobile;
        return this;
    }

    public TaskParams isMobile(boolean mobileClient) {
        this.isMobile = mobileClient;
        return this;
    }

    //TODO 是web还是端上  具体加下标识
    public TaskParams isTaskDetail(boolean isTaskDetail) {
        this.isTaskDetail = isTaskDetail;
        return this;
    }

    public TaskParams buttons(Collection<ActionButton> buttons) {
        this.buttons = buttons;
        return this;
    }


    public TaskParams dataListOwner(Map<String, Map<String, List<String>>> dataListOwner) {
        this.dataListOwner = dataListOwner;
        return this;
    }

    public boolean isH5(){
        return "h5".equals(source);
    }

    public TaskParams isTaskNotGetData(boolean isTaskNotGetData) {
        this.isTaskNotGetData = isTaskNotGetData;
        return this;
    }

}
