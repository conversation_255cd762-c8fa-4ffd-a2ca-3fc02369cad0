package com.facishare.bpm.aop;

import com.facishare.bpm.exception.BPMBusinessException;
import com.github.trace.aop.ServiceProfiler;
import lombok.extern.slf4j.Slf4j;

/**
 * wansong
 */
@Slf4j
public class ServiceTraceProfiler extends ServiceProfiler {
    @Override
    protected boolean isFail(Throwable e) {

        if (e == null) {
            return false;
        }
        if (e instanceof BPMBusinessException) {
            return false;
        }

        return !BPMBusinessException.class.isInstance(e.getClass());
    }
}
