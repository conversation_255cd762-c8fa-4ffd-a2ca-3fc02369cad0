{"singleInstanceFlow": 0, "externalFlow": 0, "type": "workflow_bpm", "name": "VOS业务", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "*************"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionType": "addRelatedObject", "executionName": "选择或新建关联对象", "entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "relatedEntityName": "验收报告", "relatedObjectId": {"expression": "activity_*************##object_29jIs__c"}, "relatedEntityId": "object_29jIs__c", "target_related_list_name": "target_related_list_H7k2m__c"}, "externalApplyTask": 0, "canSkip": false, "name": "采购单填写", "description": "", "id": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "rule": {"conditionPattern": "(0)", "conditions": [{"leftSide": {"fieldName": "activity_*************##object_29jIs__c##field_n690U__c", "fieldSrc": "activity_*************##object_29jIs__c", "fieldType": "string"}, "operator": "notEmpty", "rightSide": {"value": ""}, "rowNo": 0}]}}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_*************##object_vY12i__c"}, "form": [[{"name": "result", "value": true, "label": "审批结果", "type": "text", "readonly": false, "required": true}]], "entityName": "工单-服务申请", "entityId": "object_vY12i__c"}, "externalApplyTask": 0, "name": ">重定义任务", "description": "进行重新分配或者关闭服务申请", "id": "1512625022974", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "采购单审核", "description": "", "id": "1527607906089", "defaultTransitionId": "1527854123314", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionType": "update", "executionName": "编辑对象", "objectId": {"expression": "activity_*************##object_29jIs__c"}, "entityName": "验收报告", "entityId": "object_29jIs__c"}, "externalApplyTask": 0, "name": "设备采购流程", "description": "", "id": "1527854123288", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionType": "update", "executionName": "编辑对象", "objectId": {"expression": "activity_*************##object_29jIs__c"}, "entityName": "验收报告", "entityId": "object_29jIs__c"}, "externalApplyTask": 0, "name": "VOS采购流程", "description": "", "id": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionType": "update", "executionName": "编辑对象", "objectId": {"expression": "activity_*************##object_29jIs__c"}, "entityName": "验收报告", "entityId": "object_29jIs__c"}, "externalApplyTask": 0, "name": "短信业务采购流程", "description": "", "id": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}, {"type": "endEvent", "name": "结束", "description": "", "id": "1527854123295"}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}], "transitions": [{"id": "1512610864483", "fromId": "*************", "toId": "*************", "serialNumber": 0}, {"id": "1512625022976", "fromId": "1512625022974", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_1512625022974##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}, "serialNumber": 1}, {"id": "1512625022979", "fromId": "1512625022974", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_1512625022974##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}, "serialNumber": 2}, {"id": "1527607906092", "fromId": "*************", "toId": "1527607906089", "serialNumber": 3}, {"id": "1527854123297", "fromId": "1527854123288", "toId": "1527854123295", "serialNumber": 4}, {"description": "采购单填写 / 验收报>告 - 采购类型 等于 设备采购", "id": "1527854123305", "fromId": "1527607906089", "toId": "1527854123288", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##object_29jIs__c##field_n690U__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 5}, {"description": "采购单填写 / 验收报告 - 采购类型 等于 VOS平台采购", "id": "1527854123306", "fromId": "1527607906089", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##object_29jIs__c##field_n690U__c"}, "right": {"value": "dnVvqxbqy", "type": {"name": "text"}}}]}]}, "serialNumber": 6}, {"description": "采购单填写 / 验收报告 - 采购类型 等于 短信平台采购", "id": "1527854123307", "fromId": "1527607906089", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##object_29jIs__c##field_n690U__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 7}, {"description": "采购单填写 / 验收报告 - 采购类型 为空  或 <br>采购单填写 / 验收报告 - 采购清单（手填） 为空  或 <br>采购单填写 / 验收报告 - 项目名称 为空 ", "id": "1527854123314", "fromId": "1527607906089", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "hasNoValue", "left": {"expression": "activity_*************##object_29jIs__c##field_n690U__c"}}]}, {"type": "and", "conditions": [{"type": "hasNoValue", "left": {"expression": "activity_*************##object_29jIs__c##field_P44zr__c"}}]}, {"type": "and", "conditions": [{"type": "hasNoValue", "left": {"expression": "activity_*************##object_29jIs__c##name"}}]}]}, "serialNumber": 8}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 9}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 10}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##object_29jIs__c", "type": {"name": "text"}}, {"id": "activity_*************##object_vY12i__c", "type": {"name": "text"}}, {"id": "activity_1512625022974##result", "type": {"name": "text"}}, {"id": "activity_1527854123288##object_29jIs__c", "type": {"name": "text"}}, {"id": "activity_*************##object_29jIs__c", "type": {"name": "text"}}, {"id": "activity_*************##object_29jIs__c", "type": {"name": "text"}}, {"id": "activity_*************##object_29jIs__c##field_n690U__c", "type": {"name": "text"}}, {"id": "activity_*************##object_29jIs__c##field_P44zr__c", "type": {"name": "text"}}, {"id": "activity_*************##object_29jIs__c##name", "type": {"name": "text"}}], "id": "5b11360e4fa42469f4e548ba", "sourceWorkflowId": "358977988628152320"}