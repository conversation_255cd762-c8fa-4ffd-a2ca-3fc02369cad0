{"singleInstanceFlow": 1, "externalFlow": 0, "type": "workflow_bpm", "name": "【新】活动", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "1523259482009"}, {"type": "exclusiveGateway", "name": "各区PDM人员安排", "description": "", "id": "1523259482048", "defaultTransitionId": "1523292911373", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": true}, {"name": "field_78yWv__c", "label": "所需咨询师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false}, {"name": "field_Gm9zv__c", "label": "所需操作师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false}, {"name": "field_vmd4W__c", "label": "咨询师①确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_hY162__c", "label": "咨询师②确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_r2W8g__c", "label": "咨询师③确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_3pfae__c", "label": "操作师①确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_dV971__c", "label": "操作师②确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_d9hWL__c", "label": "操作师③确认时间", "type": "select_one", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "南区PDM Leader", "description": "", "id": "1523259482056", "assignee": {"person": ["1079", "1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": true}, {"name": "field_78yWv__c", "label": "所需咨询师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false}, {"name": "field_Gm9zv__c", "label": "所需操作师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false}, {"name": "field_vmd4W__c", "label": "咨询师①确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_hY162__c", "label": "咨询师②确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_r2W8g__c", "label": "咨询师③确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_3pfae__c", "label": "操作师①确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_dV971__c", "label": "操作师②确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_d9hWL__c", "label": "操作师③确认时间", "type": "select_one", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "东区PDM Leader", "description": "", "id": "1523259482060", "assignee": {"person": ["1109", "1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": true, "readonly": false}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": true}, {"name": "field_78yWv__c", "label": "所需咨询师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": false}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false}, {"name": "field_Gm9zv__c", "label": "所需操作师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false}, {"name": "field_vmd4W__c", "label": "咨询师①确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_hY162__c", "label": "咨询师②确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_r2W8g__c", "label": "咨询师③确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_3pfae__c", "label": "操作师①确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_dV971__c", "label": "操作师②确认时间", "type": "select_one", "required": false, "readonly": true}, {"name": "field_d9hWL__c", "label": "操作师③确认时间", "type": "select_one", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "北区PDM Leader", "description": "", "id": "1523259482062", "assignee": {"person": ["1055", "1079"]}, "taskType": "anyone"}, {"type": "endEvent", "name": "判断结束", "description": "", "id": "1523285039162"}, {"type": "exclusiveGateway", "name": "是否有PDM咨询师", "description": "", "id": "1523285039191", "defaultTransitionId": "1527732119840", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_2IAL4__c", "label": "所需咨询师人数", "type": "number", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_vmd4W__c", "label": "咨询师①确认时间", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师①确认时间", "description": "咨询师与美导交接完“目标客人铺垫邀约表”后确认", "id": "1523285039208", "assignee": {"extUserType": ["${activity_1523285039208##object_059Bz__c##field_Xcb66__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_2IAL4__c", "label": "所需咨询师人数", "type": "number", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_hY162__c", "label": "咨询师②确认时间", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师②确认时间", "description": "", "id": "1523285039213", "assignee": {"extUserType": ["${activity_1523285039213##object_059Bz__c##field_1V14k__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_2IAL4__c", "label": "所需咨询师人数", "type": "number", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_r2W8g__c", "label": "咨询师③确认时间", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师③确认时间", "description": "", "id": "1523285039216", "assignee": {"extUserType": ["${activity_1523285039216##object_059Bz__c##field_W21hy__c}"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523285039217"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523285039223"}, {"type": "exclusiveGateway", "name": "是否有PDM操作师", "description": "", "id": "1523285039241", "defaultTransitionId": "1527732119844", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_Gm9zv__c", "label": "所需操作师人数", "type": "select_one", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_3pfae__c", "label": "操作师①确认时间", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "操作师①确认时间", "description": "", "id": "1523285039245", "assignee": {"extUserType": ["${activity_1523285039245##object_059Bz__c##field_lqSm1__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_dV971__c", "label": "操作师②确认时间", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "操作师②确认时间", "description": "", "id": "1523285039246", "assignee": {"extUserType": ["${activity_1523285039246##object_059Bz__c##field_20YL1__c}"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523285039253"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_d9hWL__c", "label": "操作师③确认时间", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "操作师③确认时间", "description": "", "id": "1523285039259", "assignee": {"extUserType": ["${activity_1523285039259##object_059Bz__c##field_klffi__c}"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523285039260"}, {"type": "exclusiveGateway", "name": "PDM Leader复核分区", "description": "", "id": "1523338414346", "defaultTransitionId": "1523338414375", "gatewayType": 0}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523338414355"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_eo0lK__c", "label": "是否需要调整活动时间？-PDM Leader复核", "type": "select_one", "required": true}, {"name": "field_dw2ss__c", "label": "活动调整原因？", "type": "select_one", "required": false}, {"name": "field_34211__c", "label": "调整备注-PDM Leader复核", "type": "long_text", "required": false, "readonly": false}, {"name": "field_b7MqS__c", "label": "请美导上传“目标客人铺垫邀约表”", "type": "image", "required": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": false, "readonly": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_SO1ED__c", "label": "目标老客消耗金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_6FTBf__c", "label": "目标老客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_po2S5__c", "label": "目标消耗疗程备注", "type": "long_text", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "南区PDM Leader复核", "description": "南区PDM Leader在活动一周前复核；\n如活动筹备有异常，可要求更改活动时间、人员安排等；同时活动流程终止，业务同事需重新发起；\n如活动筹备无异常，则由仓库、设备管理员等安排物料运输", "id": "1523338414371", "assignee": {"person": ["1079", "1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_eo0lK__c", "label": "是否需要调整活动时间？-PDM Leader复核", "type": "select_one", "required": true}, {"name": "field_dw2ss__c", "label": "活动调整原因？", "type": "select_one", "required": false}, {"name": "field_34211__c", "label": "调整备注-PDM Leader复核", "type": "long_text", "required": false}, {"name": "field_b7MqS__c", "label": "请美导上传“目标客人铺垫邀约表”", "type": "image", "required": false, "readonly": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": false, "readonly": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_6FTBf__c", "label": "目标老客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_po2S5__c", "label": "目标消耗疗程备注", "type": "long_text", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "东区PDM Leader复核", "description": "", "id": "1523338414408", "assignee": {"person": ["1109", "1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_eo0lK__c", "label": "是否需要调整活动时间？-PDM Leader复核", "type": "select_one", "required": true}, {"name": "field_dw2ss__c", "label": "活动调整原因？", "type": "select_one", "required": false}, {"name": "field_34211__c", "label": "调整备注-PDM Leader复核", "type": "long_text", "required": false}, {"name": "field_b7MqS__c", "label": "请美导上传“目标客人铺垫邀约表”", "type": "image", "required": false, "readonly": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": false, "readonly": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_6FTBf__c", "label": "目标老客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_po2S5__c", "label": "目标消耗疗程备注", "type": "long_text", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "北区PDM Leader复核", "description": "", "id": "1523338414434", "assignee": {"person": ["1079", "1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": true}, {"name": "field_u0zRs__c", "label": "设备管理员确认", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "南区设备管理员", "description": "预约设备时间，PDM Leader复核确认时间后，再安排运输", "id": "1523342451179", "assignee": {"person": ["1055"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "设备管理分区", "description": "", "id": "1523342451180", "defaultTransitionId": "1527672220159", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": true}, {"name": "field_u0zRs__c", "label": "设备管理员确认", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "北区设备管理员", "description": "预约设备时间，PDM Leader复核确认时间后，再安排运输", "id": "1523342451197", "assignee": {"person": ["1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": true}, {"name": "field_u0zRs__c", "label": "设备管理员确认", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "东区设备管理员", "description": "预约设备时间，PDM Leader复核确认时间后，再安排运输", "id": "1523342451205", "assignee": {"person": ["1055"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "操作师上传物料清单", "description": "", "id": "1523342451213", "defaultTransitionId": "1527822453046", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_fwkN7__c", "label": "活动物料清单-操作师①", "type": "image", "required": false}, {"name": "field_3SNps__c", "label": "活动物料清单-操作师②", "type": "image", "required": false}, {"name": "field_y0v48__c", "label": "活动物料清单-操作师③", "type": "image", "required": false}, {"name": "field_8Qvmb__c", "label": "仓库负责人", "type": "employee", "required": true}, {"name": "field_w2t07__c", "label": "仓库管理员确认", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "南区仓库备耗材", "description": "确定仓库库存充足，PDM Leader复核确认时间后，再安排运输", "id": "1523342451215", "assignee": {"person": ["1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_fwkN7__c", "label": "活动物料清单-操作师①", "type": "image", "required": false}, {"name": "field_3SNps__c", "label": "活动物料清单-操作师②", "type": "image", "required": false}, {"name": "field_y0v48__c", "label": "活动物料清单-操作师③", "type": "image", "required": false}, {"name": "field_8Qvmb__c", "label": "仓库负责人", "type": "employee", "required": true}, {"name": "field_w2t07__c", "label": "仓库管理员确认", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "东区仓库备耗材", "description": "确定仓库库存充足，PDM Leader复核确认时间后，再安排运输", "id": "1523342451219", "assignee": {"person": ["1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_fwkN7__c", "label": "活动物料清单-操作师①", "type": "image", "required": false}, {"name": "field_3SNps__c", "label": "活动物料清单-操作师②", "type": "image", "required": false}, {"name": "field_y0v48__c", "label": "活动物料清单-操作师③", "type": "image", "required": false}, {"name": "field_8Qvmb__c", "label": "仓库负责人", "type": "employee", "required": true}, {"name": "field_w2t07__c", "label": "仓库管理员确认", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "北区仓库备耗材", "description": "确定仓库库存充足，PDM Leader复核确认时间后，再安排运输", "id": "1523345137218", "assignee": {"person": ["1055"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "是否物料/设备安排出库", "description": "", "id": "1523345137226", "defaultTransitionId": "1523345137237", "gatewayType": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "1523345137236"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": false, "readonly": true}, {"name": "field_p7h23__c", "label": "请输入设备编号", "type": "text", "required": true}, {"name": "field_hQg1A__c", "label": "请上传设备打包前后照片", "type": "image", "required": true}]]}, "externalApplyTask": 0, "name": "设备运输安排", "description": "", "id": "1523345917582", "assignee": {"extUserType": ["${activity_1523345917582##object_059Bz__c##field_y920l__c}"], "person": ["1055"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523345917583"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": false, "readonly": true}, {"name": "field_8Qvmb__c", "label": "仓库负责人", "type": "employee", "required": false, "readonly": true}, {"name": "field_4LLlp__c", "label": "请上传耗材打包前后照片", "type": "image", "required": true}]]}, "externalApplyTask": 0, "name": "耗材运输安排", "description": "", "id": "1523353142390", "assignee": {"extUserType": ["${activity_1523353142390##object_059Bz__c##field_8Qvmb__c}"], "person": ["1055"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523353490840"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_pqlhw__c", "label": "所有咨询师已上传【客人档案】到逍客", "type": "select_one", "required": true}, {"name": "field_sh0lG__c", "label": "所有咨询师已上传【皮肤检测仪照片】到逍客", "type": "select_one", "required": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_arV4o__c", "label": "所有操作师已上传【回访表】到逍客", "type": "select_one", "required": true}, {"name": "field_q7zM9__c", "label": "所有操作师已上传【术前术后照】到逍客", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "总结/资料上传复核", "description": "Ada确认资料是否已上传到网盘", "id": "1523353490843", "assignee": {"person": ["1083", "1055"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "是否需要咨询师报告", "description": "", "id": "1523353490857", "defaultTransitionId": "1523353490866", "gatewayType": 0}, {"type": "userTask", "reminders": [{"remindStrategy": 1, "remindContent": "${workflowName} 的任务 ${taskName}  剩余处理时间还有 ${remindTime}  小时，请尽快处理。", "remindTime": -24, "remindTitle": "业务流程超时提醒", "remindTargets": {"ext_bpm": ["activity_1523353490859##assigneeId$$节点处理人"]}}], "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_218ON__c", "label": "实际接诊新客数-咨询师①", "type": "number", "required": true, "readonly": false}, {"name": "field_j77S1__c", "label": "实际接诊老客数-咨询师①", "type": "number", "required": true, "readonly": false}, {"name": "field_g1e3Y__c", "label": "新客开单实际成交人数-咨询师①", "type": "number", "required": true, "readonly": false}, {"name": "field_eb2kE__c", "label": "老客升单实际成交人数-咨询师①", "type": "number", "required": true, "readonly": false}, {"name": "field_bPMF5__c", "label": "美导邀约铺垫是否到位？-咨询师①", "type": "select_one", "required": true}, {"name": "field_T8f2Y__c", "label": "业务与客户对接是否充分？-咨询师①", "type": "select_one", "required": true, "readonly": false}, {"name": "field_Todse__c", "label": "顾客未成交原因分析-咨询师①", "type": "long_text", "required": true}, {"name": "field_MJ6f1__c", "label": "未成交顾客后续跟进计划-咨询师①", "type": "long_text", "required": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_hO7Ih__c", "label": "咨询师①签名确认", "type": "signature", "required": true}]]}, "externalApplyTask": 0, "remindLatency": 48, "remind": true, "name": "咨询师①活动总结", "description": "", "id": "1523353490859", "assignee": {"extUserType": ["${activity_1523353490859##object_059Bz__c##field_Xcb66__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_PNtZw__c", "label": "实际接诊新客数-咨询师②", "type": "number", "required": true, "readonly": false}, {"name": "field_Bum8C__c", "label": "实际接诊老客数-咨询师②", "type": "currency", "required": true, "readonly": false}, {"name": "field_cakaA__c", "label": "新客开单实际成交人数-咨询师②", "type": "number", "required": true, "readonly": false}, {"name": "field_XFBw5__c", "label": "老客升单实际成交人数-咨询师②", "type": "number", "required": true, "readonly": false}, {"name": "field_RWp89__c", "label": "美导邀约铺垫是否到位？-咨询师②", "type": "select_one", "required": true, "readonly": false}, {"name": "field_zrJ36__c", "label": "业务与客户对接是否充分？-咨询师②", "type": "select_one", "required": true}, {"name": "field_36eId__c", "label": "顾客未成交原因分析-咨询师②", "type": "long_text", "required": true}, {"name": "field_lo57G__c", "label": "未成交顾客后续跟进计划-咨询师②", "type": "long_text", "required": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_jqNv8__c", "label": "咨询师②签名确认", "type": "signature", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师②活动总结", "description": "", "id": "1523353490860", "assignee": {"extUserType": ["${activity_1523353490860##object_059Bz__c##field_1V14k__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_fauIk__c", "label": "实际接诊新客数-咨询师③", "type": "number", "required": true, "readonly": false}, {"name": "field_79zp2__c", "label": "实际接诊老客数-咨询师③", "type": "currency", "required": true, "readonly": false}, {"name": "field_uioa1__c", "label": "新客开单实际成交人数-咨询师③", "type": "number", "required": true, "readonly": false}, {"name": "field_Nsg1a__c", "label": "老客升单实际成交人数-咨询师③", "type": "number", "required": true, "readonly": false}, {"name": "field_Hw20m__c", "label": "美导邀约铺垫是否到位？-咨询师③", "type": "select_one", "required": true, "readonly": false}, {"name": "field_L2N53__c", "label": "业务与客户对接是否充分？-咨询师③", "type": "select_one", "required": true}, {"name": "field_iznbC__c", "label": "顾客未成交原因分析-咨询师③", "type": "long_text", "required": true}, {"name": "field_2gg7p__c", "label": "未成交顾客后续跟进计划-咨询师③", "type": "long_text", "required": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_vgU8q__c", "label": "咨询师③签名确认", "type": "signature", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师③活动总结", "description": "", "id": "1523353490861", "assignee": {"extUserType": ["${activity_1523353490861##object_059Bz__c##field_W21hy__c}"]}, "taskType": "anyone"}, {"type": "endEvent", "name": "结束", "description": "", "id": "1523353490865"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523353490869"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523353490875"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false}]]}, "externalApplyTask": 0, "name": "业务上传“回款单”及“销售清单”", "description": "上传前需客户签名", "id": "1523365561268", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523365561273"}, {"type": "exclusiveGateway", "name": "是否需要操作师报告", "description": "", "id": "1523365561284", "defaultTransitionId": "1523365561286", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false, "readonly": true}, {"name": "field_s3TAl__c", "label": "剩余物料清点-操作师①", "type": "image", "required": true, "readonly": false}, {"name": "field_sizHf__c", "label": "物料准备是否充足？-操作师①", "type": "select_one", "required": true}, {"name": "field_wBW40__c", "label": "设备使用是否正常-操作师①", "type": "select_one", "required": true}, {"name": "field_aTg82__c", "label": "治疗后需特别关注的顾客-操作师①", "type": "long_text", "required": true}, {"name": "field_owazq__c", "label": "操作师①签名确认", "type": "signature", "required": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "操作师①活动总结", "description": "", "id": "1523365561289", "assignee": {"extUserType": ["${activity_1523365561289##object_059Bz__c##field_lqSm1__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false, "readonly": true}, {"name": "field_4Db71__c", "label": "剩余物料清点-操作师②", "type": "image", "required": true}, {"name": "field_j0OVy__c", "label": "物料准备是否充足？-操作师②", "type": "select_one", "required": true}, {"name": "field_jnn0q__c", "label": "设备使用是否正常-操作师②", "type": "select_one", "required": true}, {"name": "field_f3Xyx__c", "label": "治疗后需特别关注的顾客-操作师②", "type": "long_text", "required": true}, {"name": "field_ziI12__c", "label": "操作师②签名确认", "type": "signature", "required": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}]], "objectId": {"expression": "activity_0##object_059Bz__c"}}, "externalApplyTask": 0, "name": "操作师②活动总结", "description": "", "id": "1523365561291", "assignee": {"extUserType": ["${activity_1523365561291##object_059Bz__c##field_20YL1__c}"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523365561292"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523365561298"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "actionCode": "", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_a72q5__c", "label": "请业务上传“活动回款单”", "type": "image", "required": false, "readonly": true}, {"name": "field_1CaS1__c", "label": "请业务上传“销售清单”", "type": "image", "required": false, "readonly": true}, {"name": "field_PnPld__c", "label": "剩余物料清点-操作师③", "type": "image", "required": true}, {"name": "field_dkh9t__c", "label": "物料准备是否充足？-操作师③", "type": "select_one", "required": true}, {"name": "field_q11yD__c", "label": "设备使用是否正常-操作师③", "type": "select_one", "required": true}, {"name": "field_v1da5__c", "label": "治疗后需特别关注的顾客-操作师③", "type": "long_text", "required": true}, {"name": "field_kQ2Z0__c", "label": "操作师③签名确认", "type": "signature", "required": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}]]}, "externalApplyTask": 0, "name": "操作师③活动总结", "description": "", "id": "1523365561302", "assignee": {"extUserType": ["${activity_1523365561302##object_059Bz__c##field_klffi__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 1, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_p7h23__c", "label": "请输入设备编号", "type": "text", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": false, "readonly": true}, {"name": "field_3425i__c", "label": "设备打包前后照片-活动后", "type": "image", "required": true}, {"name": "field_QLy1k__c", "label": "设备去向", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "设备回程安排", "description": "若设备直接到下一场活动，请指定下一场活动交接同事为下一节点处理人", "id": "1523412869123", "assignee": {"extUserType": ["${activity_1523412869123##object_059Bz__c##field_y920l__c}"], "person": ["1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 1, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_s3TAl__c", "label": "剩余物料清点-操作师①", "type": "image", "required": false, "readonly": true}, {"name": "field_4Db71__c", "label": "剩余物料清点-操作师②", "type": "image", "required": false, "readonly": true}, {"name": "field_PnPld__c", "label": "剩余物料清点-操作师③", "type": "image", "required": false, "readonly": true}, {"name": "field_dL176__c", "label": "耗材去向", "type": "select_one", "required": true}]]}, "externalApplyTask": 0, "name": "耗材回程运输", "description": "若耗材直接到下一场活动，请指定下一场活动交接同事为下一节点处理人", "id": "1523429033277", "assignee": {"extUserType": ["${activity_1523429033277##object_059Bz__c##field_y920l__c}"], "person": ["1055"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_y920l__c", "label": "设备管理员", "type": "employee", "required": false, "readonly": true}, {"name": "field_p7h23__c", "label": "请输入设备编号", "type": "text", "required": false, "readonly": true}, {"name": "field_viZzy__c", "label": "设备验收异常照片", "type": "image", "required": false, "readonly": false}, {"name": "field_82uUj__c", "label": "设备验收备注", "type": "long_text", "required": false}]]}, "externalApplyTask": 0, "name": "设备活动/入库验收", "description": "", "id": "1523429033288", "assignee": {"extUserType": ["${activity_1523429033288##object_059Bz__c##field_y920l__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_s3TAl__c", "label": "剩余物料清点-操作师①", "type": "image", "required": false}, {"name": "field_4Db71__c", "label": "剩余物料清点-操作师②", "type": "image", "required": false}, {"name": "field_PnPld__c", "label": "剩余物料清点-操作师③", "type": "image", "required": false}, {"name": "field_fAx31__c", "label": "耗材验收异常照片", "type": "image", "required": false}, {"name": "field_p1iHO__c", "label": "耗材验收备注", "type": "long_text", "required": false}, {"name": "field_yu31p__c", "label": "耗材验收签名确认", "type": "signature", "required": true}]]}, "externalApplyTask": 0, "name": "耗材活动/入库验收", "description": "", "id": "1523429033297", "assignee": {"extUserType": ["${activity_1523429033297##object_059Bz__c##field_8Qvmb__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "选择或新建关联对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "addRelatedObject", "objectId": {"expression": "activity_0##object_059Bz__c"}, "relatedEntityName": "销售订单", "relatedObjectId": {"expression": "activity_1523429033307##SalesOrderObj"}, "relatedEntityId": "SalesOrderObj", "target_related_list_name": "target_related_list_Jxtr4__c"}, "externalApplyTask": 0, "name": "业务录入业绩", "description": "", "id": "1523429033307", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "rule": {"conditionPattern": "(0)", "conditions": [{"leftSide": {"fieldName": "activity_1523429033307##SalesOrderObj##UDSSel1__c", "fieldSrc": "activity_1523429033307##SalesOrderObj", "fieldType": "string"}, "operator": "equals", "rightSide": {"value": "2"}, "rowNo": 0}]}}, {"type": "endEvent", "name": "结束", "description": "", "id": "1523429033318"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523591514073"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "选择或新建关联对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "回款", "relatedObjectId": {"expression": "activity_1523602527177##PaymentObj"}, "executionType": "addRelatedObject", "relatedEntityId": "PaymentObj", "target_related_list_name": "target_related_list_F4z5c__c", "objectId": {"expression": "activity_0##object_059Bz__c"}}, "externalApplyTask": 0, "name": "上传回款凭证", "description": "", "id": "1523602527177", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "rule": {"conditionPattern": "(0)", "conditions": [{"leftSide": {"fieldName": "activity_1523602527177##PaymentObj##field_o43dd__c", "fieldSrc": "activity_1523602527177##PaymentObj", "fieldType": "string"}, "operator": "equals", "rightSide": {"value": "0Bs47hms6"}, "rowNo": 0}]}}, {"type": "endEvent", "name": "结束", "description": "", "id": "1523603152511"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523930176361"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1523930176362"}, {"type": "exclusiveGateway", "name": "时间确认状态", "description": "", "id": "1527732119717", "defaultTransitionId": "1527732119719", "gatewayType": 0}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527755169731"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527755169745"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527755169840"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527755169849"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453043##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_fwkN7__c", "label": "活动物料清单-操作师①", "type": "image", "required": true}]]}, "externalApplyTask": 0, "name": "操作师①物料清单", "description": "", "id": "1527822453043", "assignee": {"extUserType": ["${activity_1527822453043##object_059Bz__c##field_lqSm1__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453044##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_3SNps__c", "label": "活动物料清单-操作师②", "type": "image", "required": true}]]}, "externalApplyTask": 0, "name": "操作师②物料清单", "description": "", "id": "1527822453044", "assignee": {"extUserType": ["${activity_1527822453044##object_059Bz__c##field_20YL1__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453045##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_t6B4c__c", "label": "活动地点定位", "type": "location", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_3167s__c", "label": "活动相关设备", "type": "select_many", "required": false, "readonly": true}, {"name": "field_y0v48__c", "label": "活动物料清单-操作师③", "type": "image", "required": true, "readonly": false}]]}, "externalApplyTask": 0, "name": "操作师③物料清单", "description": "", "id": "1527822453045", "assignee": {"extUserType": ["${activity_1527822453045##object_059Bz__c##field_klffi__c}"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453053"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453061"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453071"}, {"type": "exclusiveGateway", "name": "各区仓库备耗材", "description": "", "id": "1527822453075", "defaultTransitionId": "1527822453077", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453087##"}, "relatedEntityId": "", "actionCode": "", "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": true}, {"name": "field_6FTBf__c", "label": "目标老客升单回款金额", "type": "currency", "required": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": true}, {"name": "field_FiaLK__c", "label": "目标老客消耗人数", "type": "number", "required": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": true}]]}, "externalApplyTask": 0, "name": "业务上传《活动管理表》", "description": "", "id": "1527822453087", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453094"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "executionType": "update", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453100##"}, "relatedEntityId": "", "actionCode": "", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_b7MqS__c", "label": "请美导上传“顾客分析表”", "type": "image", "required": true}]]}, "externalApplyTask": 0, "name": "上传《顾客分析表》", "description": "", "id": "1527822453100", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "咨询交接", "description": "", "id": "1527822453102", "defaultTransitionId": "1527822453109", "gatewayType": 0}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453106##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_6FTBf__c", "label": "目标老客升单回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_FiaLK__c", "label": "目标老客消耗人数", "type": "number", "required": false, "readonly": true}, {"name": "field_b7MqS__c", "label": "请美导上传“顾客分析表”", "type": "image", "required": false, "readonly": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": false, "readonly": true}, {"name": "field_GAaWL__c", "label": "咨询师①确认交接", "type": "signature", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师①交接", "description": "", "id": "1527822453106", "assignee": {"extUserType": ["${activity_1527822453106##object_059Bz__c##field_Xcb66__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453107##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_6FTBf__c", "label": "目标老客升单回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_FiaLK__c", "label": "目标老客消耗人数", "type": "number", "required": false, "readonly": true}, {"name": "field_b7MqS__c", "label": "请美导上传“顾客分析表”", "type": "image", "required": false, "readonly": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": false, "readonly": true}, {"name": "field_JeI2g__c", "label": "咨询师②确认交接", "type": "signature", "required": true}]]}, "externalApplyTask": 0, "name": "咨询师②交接", "description": "", "id": "1527822453107", "assignee": {"extUserType": ["${activity_1527822453107##object_059Bz__c##field_1V14k__c}"]}, "taskType": "anyone"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1527822453108##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_D8z8J__c", "label": "活动开始时间", "type": "time", "required": false, "readonly": true}, {"name": "field_210Hg__c", "label": "目标老客升单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_SO1ED__c", "label": "目标老客消耗金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_Pt7F1__c", "label": "目标新客开单人数", "type": "number", "required": false, "readonly": true}, {"name": "field_2I6I6__c", "label": "目标新客回款金额", "type": "currency", "required": false, "readonly": true}, {"name": "field_b7MqS__c", "label": "请美导上传“顾客分析表”", "type": "image", "required": false, "readonly": true}, {"name": "field_czL26__c", "label": "请业务上传“活动管理表”", "type": "image", "required": false, "readonly": true}, {"name": "field_35G2J__c", "label": "咨询师③确认交接", "type": "signature", "required": true, "readonly": false}]]}, "externalApplyTask": 0, "name": "咨询师③交接", "description": "", "id": "1527822453108", "assignee": {"extUserType": ["${activity_1527822453108##object_059Bz__c##field_W21hy__c}"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453113"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453119"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1527822453126"}, {"type": "userTask", "assignNextTask": 0, "bpmExtension": {"executionName": "编辑对象", "entityName": "【测试中】活动预约", "entityId": "object_059Bz__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1528076968657##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_059Bz__c"}, "form": [[{"name": "field_y7e61__c", "label": "关联客户档案", "type": "object_reference", "required": false, "readonly": true}, {"name": "field_T33l3__c", "label": "活动日期", "type": "date", "required": false, "readonly": true}, {"name": "field_Xcb66__c", "label": "咨询师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_218ON__c", "label": "实际接诊新客数-咨询师①", "type": "number", "required": false, "readonly": true}, {"name": "field_j77S1__c", "label": "实际接诊老客数-咨询师①", "type": "number", "required": false, "readonly": true}, {"name": "field_g1e3Y__c", "label": "新客开单实际成交人数-咨询师①", "type": "number", "required": false, "readonly": true}, {"name": "field_eb2kE__c", "label": "老客升单实际成交人数-咨询师①", "type": "number", "required": false, "readonly": true}, {"name": "field_1V14k__c", "label": "咨询师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_PNtZw__c", "label": "实际接诊新客数-咨询师②", "type": "number", "required": false, "readonly": true}, {"name": "field_Bum8C__c", "label": "实际接诊老客数-咨询师②", "type": "currency", "required": false, "readonly": true}, {"name": "field_cakaA__c", "label": "新客开单实际成交人数-咨询师②", "type": "number", "required": false, "readonly": true}, {"name": "field_XFBw5__c", "label": "老客升单实际成交人数-咨询师②", "type": "number", "required": false, "readonly": true}, {"name": "field_W21hy__c", "label": "咨询师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_fauIk__c", "label": "实际接诊新客数-咨询师③", "type": "number", "required": false, "readonly": true}, {"name": "field_79zp2__c", "label": "实际接诊老客数-咨询师③", "type": "currency", "required": false, "readonly": true}, {"name": "field_uioa1__c", "label": "新客开单实际成交人数-咨询师③", "type": "number", "required": false, "readonly": true}, {"name": "field_Nsg1a__c", "label": "老客升单实际成交人数-咨询师③", "type": "number", "required": false, "readonly": true}, {"name": "field_lqSm1__c", "label": "操作师①", "type": "employee", "required": false, "readonly": true}, {"name": "field_t13ZF__c", "label": "实际操作总客数-操作师①", "type": "number", "required": false, "readonly": true}, {"name": "field_20YL1__c", "label": "操作师②", "type": "employee", "required": false, "readonly": true}, {"name": "field_lvK1g__c", "label": "实际操作总客数-操作师②", "type": "number", "required": false, "readonly": true}, {"name": "field_klffi__c", "label": "操作师③", "type": "employee", "required": false, "readonly": true}, {"name": "field_OnL21__c", "label": "实际操作总客数-操作师③", "type": "number", "required": false, "readonly": true}, {"name": "field_g28AP__c", "label": "咨询师数据复核情况", "type": "select_many", "required": false}, {"name": "field_z3GkR__c", "label": "操作师数据复核情况", "type": "select_many", "required": false}]]}, "externalApplyTask": 0, "name": "业务复核活动数据", "description": "", "id": "1528076968657", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "复核情况", "description": "", "id": "1528076968659", "defaultTransitionId": "1528076968738", "gatewayType": 0}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1528076968755"}, {"type": "parallelGateway", "name": "＋", "description": "", "id": "1528076968762"}], "transitions": [{"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 南区 且 <br>流程发起数据 / 【测试中】活动预约 - 设备管理员确认 等于 设备可按时送达活动现场", "id": "1523259482057", "fromId": "1523259482048", "toId": "1523259482056", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "1rRfy<PERSON><PERSON>", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_u0zRs__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 0}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 东区 且 <br>流程发起数据 / 【测试中】活动预约 - 设备管理员确认 等于 设备可按时送达活动现场", "id": "1523259482061", "fromId": "1523259482048", "toId": "1523259482060", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "mSrztCsQL", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_u0zRs__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 1}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 北区 且 <br>流程发起数据 / 【测试中】活动预约 - 设备管理员确认 等于 设备可按时送达活动现场", "id": "1523259482063", "fromId": "1523259482048", "toId": "1523259482062", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "3l2ct6f69", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_u0zRs__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 2}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 1 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 1 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 为空  或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 确认可准时出席活动 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 确认可准时出席活动", "id": "1523285039209", "fromId": "1523285039191", "toId": "1523285039208", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "ryo7G422p", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "ryo7G422p", "type": {"name": "text"}}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 3}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 为空  且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 为空  或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 确认可准时出席活动", "id": "1523285039218", "fromId": "1523285039191", "toId": "1523285039217", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 4}, {"id": "1523285039219", "fromId": "1523285039217", "toId": "1523285039208", "serialNumber": 5}, {"id": "1523285039220", "fromId": "1523285039217", "toId": "1523285039213", "serialNumber": 6}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 为空  且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 为空  且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 为空 ", "id": "1523285039224", "fromId": "1523285039191", "toId": "1523285039223", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}}]}]}, "serialNumber": 7}, {"id": "1523285039225", "fromId": "1523285039223", "toId": "1523285039208", "serialNumber": 8}, {"id": "1523285039226", "fromId": "1523285039223", "toId": "1523285039213", "serialNumber": 9}, {"id": "1523285039227", "fromId": "1523285039223", "toId": "1523285039216", "serialNumber": 10}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 1 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 1 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 为空  或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 确认可准时出席活动", "id": "1523285039247", "fromId": "1523285039241", "toId": "1523285039245", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "Cl7kvPvk4", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "Cl7kvPvk4", "type": {"name": "text"}}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "0BJvbJe1Y", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 11}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 为空  且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 为空  或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 确认可准时出席活动", "id": "1523285039254", "fromId": "1523285039241", "toId": "1523285039253", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "0BJvbJe1Y", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "0BJvbJe1Y", "type": {"name": "text"}}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 12}, {"id": "1523285039257", "fromId": "1523285039253", "toId": "1523285039245", "serialNumber": 13}, {"id": "1523285039258", "fromId": "1523285039253", "toId": "1523285039246", "serialNumber": 14}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 为空  且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 为空  且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 为空  或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是：", "id": "1523285039261", "fromId": "1523285039241", "toId": "1523285039260", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}}, {"type": "hasNoValue", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 15}, {"id": "1523285039262", "fromId": "1523285039260", "toId": "1523285039245", "serialNumber": 16}, {"id": "1523285039263", "fromId": "1523285039260", "toId": "1523285039246", "serialNumber": 17}, {"id": "1523285039264", "fromId": "1523285039260", "toId": "1523285039259", "serialNumber": 18}, {"description": "流程发起数据 / 【测试中】活动预约 - 设备管理员确认 等于 设备无法按时送达现场，原因是", "id": "1523292911373", "fromId": "1523259482048", "toId": "1523285039162", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_u0zRs__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 19}, {"id": "1523338414356", "fromId": "1523285039246", "toId": "1523338414355", "serialNumber": 20}, {"id": "1523338414357", "fromId": "1523285039245", "toId": "1523338414355", "serialNumber": 21}, {"id": "1523338414358", "fromId": "1523285039259", "toId": "1523338414355", "serialNumber": 22}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 南区", "id": "1523338414375", "fromId": "1523338414346", "toId": "1523338414371", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "1rRfy<PERSON><PERSON>", "type": {"name": "text"}}}]}]}, "serialNumber": 23}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 东区", "id": "1523338414409", "fromId": "1523338414346", "toId": "1523338414408", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "mSrztCsQL", "type": {"name": "text"}}}]}]}, "serialNumber": 24}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 北区", "id": "1523338414435", "fromId": "1523338414346", "toId": "1523338414434", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "3l2ct6f69", "type": {"name": "text"}}}]}]}, "serialNumber": 25}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 南区 且 <br>流程发起数据 / 【测试中】活动预约 - 是否需要使用公司设备 等于 是", "id": "1523342451182", "fromId": "1523342451180", "toId": "1523342451179", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "1rRfy<PERSON><PERSON>", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_rlEyl__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 26}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 北区 且 <br>流程发起数据 / 【测试中】活动预约 - 是否需要使用公司设备 等于 是", "id": "1523342451198", "fromId": "1523342451180", "toId": "1523342451197", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "3l2ct6f69", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_rlEyl__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 27}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 东区 且 <br>流程发起数据 / 【测试中】活动预约 - 是否需要使用公司设备 等于 是", "id": "1523342451206", "fromId": "1523342451180", "toId": "1523342451205", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "mSrztCsQL", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_rlEyl__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 28}, {"description": "流程发起数据 / 【测试中】活动预约 - 是否需要调整活动时间？-PDM Leader复核 等于 调整时间-【不安排】设备及物料出库", "id": "1523345137237", "fromId": "1523345137226", "toId": "1523345137236", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_eo0lK__c"}, "right": {"value": "3405wfo1G", "type": {"name": "text"}}}]}]}, "serialNumber": 29}, {"description": "流程发起数据 / 【测试中】活动预约 - 是否需要调整活动时间？-PDM Leader复核 等于 无需调整时间 或 <br>流程发起数据 / 【测试中】活动预约 - 是否需要调整活动时间？-PDM Leader复核 等于 调整时间-【安排】设备及物料出库", "id": "1523345917584", "fromId": "1523345137226", "toId": "1523345917583", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_eo0lK__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_eo0lK__c"}, "right": {"value": "j1WlZ529e", "type": {"name": "text"}}}]}]}, "serialNumber": 30}, {"id": "1523345917585", "fromId": "1523345917583", "toId": "1523345917582", "serialNumber": 31}, {"id": "1523353142391", "fromId": "1523345917583", "toId": "1523353142390", "serialNumber": 32}, {"id": "1523353490841", "fromId": "1523345917582", "toId": "1523353490840", "serialNumber": 33}, {"id": "1523353490842", "fromId": "1523353142390", "toId": "1523353490840", "serialNumber": 34}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 1 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 1 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 包含 咨询师①数据有误 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 不包含 咨询师②数据有误 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 不包含 咨询师②数据有误,咨询师③数据有误", "id": "1523353490862", "fromId": "1523353490857", "toId": "1523353490859", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "ryo7G422p", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "ryo7G422p", "type": {"name": "text"}}}, {"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["4871yrffW"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["4871yrffW", "nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 35}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 0", "id": "1523353490866", "fromId": "1523353490857", "toId": "1523353490865", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 36}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 包含 咨询师①数据有误,咨询师②数据有误 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 不包含 咨询师③数据有误", "id": "1523353490870", "fromId": "1523353490857", "toId": "1523353490869", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1", "4871yrffW"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 37}, {"id": "1523353490871", "fromId": "1523353490869", "toId": "1523353490859", "serialNumber": 38}, {"id": "1523353490872", "fromId": "1523353490869", "toId": "1523353490860", "serialNumber": 39}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 包含 咨询师①数据有误,咨询师②数据有误,咨询师③数据有误", "id": "1523353490876", "fromId": "1523353490857", "toId": "1523353490875", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1", "4871yrffW", "nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 40}, {"id": "1523353490877", "fromId": "1523353490875", "toId": "1523353490859", "serialNumber": 41}, {"id": "1523353490878", "fromId": "1523353490875", "toId": "1523353490860", "serialNumber": 42}, {"id": "1523353490879", "fromId": "1523353490875", "toId": "1523353490861", "serialNumber": 43}, {"id": "1523365561270", "fromId": "1523353490840", "toId": "1523365561268", "serialNumber": 44}, {"id": "1523365561274", "fromId": "1523365561268", "toId": "1523365561273", "serialNumber": 45}, {"id": "1523365561275", "fromId": "1523365561273", "toId": "1523353490857", "serialNumber": 46}, {"id": "1523365561285", "fromId": "1523365561273", "toId": "1523365561284", "serialNumber": 47}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 0", "id": "1523365561286", "fromId": "1523365561284", "toId": "1523353490865", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_y5bsW__c"}, "right": {"value": 0, "type": {"name": "number"}}}]}]}, "serialNumber": 48}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 1", "id": "1523365561290", "fromId": "1523365561284", "toId": "1523365561289", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "Cl7kvPvk4", "type": {"name": "text"}}}]}]}, "serialNumber": 49}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 2", "id": "1523365561293", "fromId": "1523365561284", "toId": "1523365561292", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "0BJvbJe1Y", "type": {"name": "text"}}}]}]}, "serialNumber": 50}, {"id": "1523365561294", "fromId": "1523365561292", "toId": "1523365561289", "serialNumber": 51}, {"id": "1523365561295", "fromId": "1523365561292", "toId": "1523365561291", "serialNumber": 52}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3", "id": "1523365561299", "fromId": "1523365561284", "toId": "1523365561298", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}]}]}, "serialNumber": 53}, {"id": "1523365561300", "fromId": "1523365561298", "toId": "1523365561289", "serialNumber": 54}, {"id": "1523365561301", "fromId": "1523365561298", "toId": "1523365561291", "serialNumber": 55}, {"id": "1523365561303", "fromId": "1523365561298", "toId": "1523365561302", "serialNumber": 56}, {"id": "1523412869124", "fromId": "1523353490840", "toId": "1523412869123", "serialNumber": 57}, {"id": "1523429033292", "fromId": "1523412869123", "toId": "1523429033288", "serialNumber": 58}, {"id": "1523429033298", "fromId": "1523429033277", "toId": "1523429033297", "serialNumber": 59}, {"id": "1523591514074", "fromId": "1523259482056", "toId": "1523591514073", "serialNumber": 60}, {"id": "1523591514075", "fromId": "1523259482060", "toId": "1523591514073", "serialNumber": 61}, {"id": "1523591514076", "fromId": "1523259482062", "toId": "1523591514073", "serialNumber": 62}, {"id": "1523591514078", "fromId": "1523591514073", "toId": "1523285039191", "serialNumber": 63}, {"id": "1523591514079", "fromId": "1523591514073", "toId": "1523285039241", "serialNumber": 64}, {"id": "1523603152510", "fromId": "1523429033307", "toId": "1523353490843", "serialNumber": 65}, {"id": "1523603152513", "fromId": "1523429033288", "toId": "1523603152511", "serialNumber": 66}, {"id": "1523603152514", "fromId": "1523353490843", "toId": "1523602527177", "serialNumber": 67}, {"id": "1523603152515", "fromId": "1523602527177", "toId": "1523429033318", "serialNumber": 68}, {"id": "1523716677458", "fromId": "1523285039208", "toId": "1523338414355", "serialNumber": 69}, {"id": "1523716677459", "fromId": "1523285039213", "toId": "1523338414355", "serialNumber": 70}, {"id": "1523716677460", "fromId": "1523285039216", "toId": "1523338414355", "serialNumber": 71}, {"id": "1523930176363", "fromId": "1523353490859", "toId": "1523930176361", "serialNumber": 72}, {"id": "1523930176364", "fromId": "1523353490860", "toId": "1523930176361", "serialNumber": 73}, {"id": "1523930176365", "fromId": "1523353490861", "toId": "1523930176361", "serialNumber": 74}, {"id": "1523930176366", "fromId": "1523365561289", "toId": "1523930176362", "serialNumber": 75}, {"id": "1523930176367", "fromId": "1523365561291", "toId": "1523930176362", "serialNumber": 76}, {"id": "1523930176368", "fromId": "1523365561302", "toId": "1523930176362", "serialNumber": 77}, {"id": "1527672220150", "fromId": "1523259482009", "toId": "1523342451180", "serialNumber": 78}, {"id": "1527672220153", "fromId": "1523342451179", "toId": "1523259482048", "serialNumber": 79}, {"id": "1527672220154", "fromId": "1523342451197", "toId": "1523259482048", "serialNumber": 80}, {"id": "1527672220155", "fromId": "1523342451205", "toId": "1523259482048", "serialNumber": 81}, {"description": "流程发起数据 / 【测试中】活动预约 - 是否需要使用公司设备 等于 否", "id": "1527672220159", "fromId": "1523342451180", "toId": "1523259482048", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_rlEyl__c"}, "right": {"value": "2rov0IKk5", "type": {"name": "text"}}}]}]}, "serialNumber": 82}, {"id": "1527732119718", "fromId": "1523338414355", "toId": "1527732119717", "serialNumber": 83}, {"description": "流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 确认可准时出席活动", "id": "1527732119719", "fromId": "1527732119717", "toId": "1523342451213", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 84}, {"description": "流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 或 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是", "id": "1527732119720", "fromId": "1527732119717", "toId": "1523259482048", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 85}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 0", "id": "1527732119840", "fromId": "1523285039191", "toId": "1523338414355", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 86}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 0", "id": "1527732119844", "fromId": "1523285039241", "toId": "1523338414355", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 87}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 确认可准时出席活动", "id": "1527755169732", "fromId": "1523285039191", "toId": "1527755169731", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 88}, {"id": "1527755169733", "fromId": "1527755169731", "toId": "1523285039208", "serialNumber": 89}, {"id": "1527755169734", "fromId": "1527755169731", "toId": "1523285039216", "serialNumber": 90}, {"id": "1527755169746", "fromId": "1527755169745", "toId": "1523285039213", "serialNumber": 91}, {"id": "1527755169747", "fromId": "1527755169745", "toId": "1523285039216", "serialNumber": 92}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 无法出席，原因是：", "id": "1527755169748", "fromId": "1523285039191", "toId": "1527755169745", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 93}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 确认可准时出席活动", "id": "1527755169755", "fromId": "1523285039191", "toId": "1523285039213", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 94}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师③确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师②确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师①确认时间 等于 确认可准时出席活动", "id": "1527755169765", "fromId": "1523285039191", "toId": "1523285039216", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_r2W8g__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_hY162__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_vmd4W__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 95}, {"id": "1527755169841", "fromId": "1527755169840", "toId": "1523285039245", "serialNumber": 96}, {"id": "1527755169842", "fromId": "1527755169840", "toId": "1523285039259", "serialNumber": 97}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 无法出席，原因是 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 确认可准时出席活动", "id": "1527755169843", "fromId": "1523285039241", "toId": "1527755169840", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 98}, {"id": "1527755169850", "fromId": "1527755169849", "toId": "1523285039246", "serialNumber": 99}, {"id": "1527755169851", "fromId": "1527755169849", "toId": "1523285039259", "serialNumber": 100}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是：", "id": "1527755169852", "fromId": "1523285039241", "toId": "1527755169849", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 101}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是： 或 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 无法出席，原因是：", "id": "1527755169874", "fromId": "1523285039241", "toId": "1523285039246", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "0BJvbJe1Y", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "other", "type": {"name": "text"}}}]}]}, "serialNumber": 102}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 无法出席，原因是： 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 确认可准时出席活动", "id": "1527755169884", "fromId": "1523285039241", "toId": "1523285039259", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "other", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 103}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 1 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动", "id": "1527822453046", "fromId": "1523342451213", "toId": "1527822453043", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "Cl7kvPvk4", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 104}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 确认可准时出席活动", "id": "1527822453054", "fromId": "1523342451213", "toId": "1527822453053", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "0BJvbJe1Y", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 105}, {"id": "1527822453055", "fromId": "1527822453053", "toId": "1527822453043", "serialNumber": 106}, {"id": "1527822453056", "fromId": "1527822453053", "toId": "1527822453044", "serialNumber": 107}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师①确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师②确认时间 等于 确认可准时出席活动 且 <br>流程发起数据 / 【测试中】活动预约 - 操作师③确认时间 等于 确认可准时出席活动", "id": "1527822453062", "fromId": "1523342451213", "toId": "1527822453061", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "EkOc77Oe0", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_3pfae__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_dV971__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_d9hWL__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 108}, {"id": "1527822453063", "fromId": "1527822453061", "toId": "1527822453043", "serialNumber": 109}, {"id": "1527822453064", "fromId": "1527822453061", "toId": "1527822453044", "serialNumber": 110}, {"id": "1527822453065", "fromId": "1527822453061", "toId": "1527822453045", "serialNumber": 111}, {"id": "1527822453072", "fromId": "1527822453043", "toId": "1527822453071", "serialNumber": 112}, {"id": "1527822453073", "fromId": "1527822453044", "toId": "1527822453071", "serialNumber": 113}, {"id": "1527822453074", "fromId": "1527822453045", "toId": "1527822453071", "serialNumber": 114}, {"id": "1527822453076", "fromId": "1527822453071", "toId": "1527822453075", "serialNumber": 115}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 南区", "id": "1527822453077", "fromId": "1527822453075", "toId": "1523342451215", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "1rRfy<PERSON><PERSON>", "type": {"name": "text"}}}]}]}, "serialNumber": 116}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 东区", "id": "1527822453078", "fromId": "1527822453075", "toId": "1523342451219", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "mSrztCsQL", "type": {"name": "text"}}}]}]}, "serialNumber": 117}, {"description": "流程发起数据 / 【测试中】活动预约 - 活动区域 等于 北区", "id": "1527822453079", "fromId": "1527822453075", "toId": "1523345137218", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_9Ll2p__c"}, "right": {"value": "3l2ct6f69", "type": {"name": "text"}}}]}]}, "serialNumber": 118}, {"id": "1527822453088", "fromId": "1523342451215", "toId": "1527822453087", "serialNumber": 119}, {"id": "1527822453089", "fromId": "1523342451219", "toId": "1527822453087", "serialNumber": 120}, {"id": "1527822453090", "fromId": "1523345137218", "toId": "1527822453087", "serialNumber": 121}, {"id": "1527822453091", "fromId": "1527822453087", "toId": "1523338414346", "serialNumber": 122}, {"id": "1527822453095", "fromId": "1523338414371", "toId": "1527822453094", "serialNumber": 123}, {"id": "1527822453096", "fromId": "1523338414408", "toId": "1527822453094", "serialNumber": 124}, {"id": "1527822453097", "fromId": "1523338414434", "toId": "1527822453094", "serialNumber": 125}, {"id": "1527822453098", "fromId": "1527822453094", "toId": "1523345137226", "serialNumber": 126}, {"id": "1527822453103", "fromId": "1523345917583", "toId": "1527822453100", "serialNumber": 127}, {"id": "1527822453104", "fromId": "1527822453100", "toId": "1527822453102", "serialNumber": 128}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 1", "id": "1527822453109", "fromId": "1527822453102", "toId": "1527822453106", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "ryo7G422p", "type": {"name": "text"}}}]}]}, "serialNumber": 129}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2", "id": "1527822453114", "fromId": "1527822453102", "toId": "1527822453113", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}]}]}, "serialNumber": 130}, {"id": "1527822453115", "fromId": "1527822453113", "toId": "1527822453106", "serialNumber": 131}, {"id": "1527822453116", "fromId": "1527822453113", "toId": "1527822453107", "serialNumber": 132}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3", "id": "1527822453120", "fromId": "1527822453102", "toId": "1527822453119", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}]}]}, "serialNumber": 133}, {"id": "1527822453121", "fromId": "1527822453119", "toId": "1527822453106", "serialNumber": 134}, {"id": "1527822453122", "fromId": "1527822453119", "toId": "1527822453107", "serialNumber": 135}, {"id": "1527822453123", "fromId": "1527822453119", "toId": "1527822453108", "serialNumber": 136}, {"id": "1527822453127", "fromId": "1527822453106", "toId": "1527822453126", "serialNumber": 137}, {"id": "1527822453129", "fromId": "1527822453107", "toId": "1527822453126", "serialNumber": 138}, {"id": "1527822453130", "fromId": "1527822453108", "toId": "1527822453126", "serialNumber": 139}, {"id": "1527822453133", "fromId": "1527822453126", "toId": "1523345917583", "serialNumber": 140}, {"id": "1528076968652", "fromId": "1523930176362", "toId": "1523429033277", "serialNumber": 141}, {"id": "1528076968655", "fromId": "1523930176362", "toId": "1523930176361", "serialNumber": 142}, {"id": "1528076968656", "fromId": "1523429033297", "toId": "1523603152511", "serialNumber": 143}, {"id": "1528076968658", "fromId": "1523930176361", "toId": "1528076968657", "serialNumber": 144}, {"id": "1528076968660", "fromId": "1528076968657", "toId": "1528076968659", "serialNumber": 145}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 2 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 不包含 咨询师①数据有误 或 <br>流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 不包含 咨询师①数据有误,咨询师③数据有误", "id": "1528076968702", "fromId": "1523353490857", "toId": "1523353490860", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "34aVgOhu3", "type": {"name": "text"}}}, {"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}, {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1", "nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 146}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 数据复核情况 不包含 咨询师①数据有误,咨询师②数据有误", "id": "1528076968710", "fromId": "1523353490857", "toId": "1523353490861", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1", "4871yrffW"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 147}, {"description": "流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 不包含 咨询师①数据有误,咨询师②数据有误,咨询师③数据有误", "id": "1528076968738", "fromId": "1528076968659", "toId": "1523429033307", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "hasNoneOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1", "4871yrffW", "nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 148}, {"description": "流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 包含 咨询师①数据有误 或 <br>流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 包含 咨询师②数据有误 或 <br>流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 包含 咨询师③数据有误", "id": "1528076968741", "fromId": "1528076968659", "toId": "1523353490857", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}, {"type": "and", "conditions": [{"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["4871yrffW"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}, {"type": "and", "conditions": [{"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 149}, {"id": "1528076968756", "fromId": "1528076968755", "toId": "1523353490860", "serialNumber": 150}, {"id": "1528076968757", "fromId": "1528076968755", "toId": "1523353490861", "serialNumber": 151}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 包含 咨询师②数据有误,咨询师③数据有误", "id": "1528076968758", "fromId": "1523353490857", "toId": "1528076968755", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["4871yrffW", "nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 152}, {"id": "1528076968763", "fromId": "1528076968762", "toId": "1523353490859", "serialNumber": 153}, {"id": "1528076968764", "fromId": "1528076968762", "toId": "1523353490861", "serialNumber": 154}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 3 且 <br>流程发起数据 / 【测试中】活动预约 - 咨询师数据复核情况 包含 咨询师①数据有误,咨询师③数据有误", "id": "1528076968765", "fromId": "1523353490857", "toId": "1528076968762", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "Aep3Upm5D", "type": {"name": "text"}}}, {"type": "hasAnyOf", "left": {"expression": "activity_0##object_059Bz__c##field_g28AP__c"}, "right": {"value": ["option1", "nOgQw1c3C"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 155}, {"description": "流程发起数据 / 【测试中】活动预约 - 所需咨询师人数 等于 0 且 <br>流程发起数据 / 【测试中】活动预约 - 所需操作师人数 等于 0", "id": "1528164631891", "fromId": "1523259482048", "toId": "1523345917583", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_78yWv__c"}, "right": {"value": "option1", "type": {"name": "text"}}}, {"type": "equals", "left": {"expression": "activity_0##object_059Bz__c##field_Gm9zv__c"}, "right": {"value": "option1", "type": {"name": "text"}}}]}]}, "serialNumber": 156}], "variables": [{"id": "activity_0##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523259482056##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523259482060##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523259482062##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523285039208##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523285039213##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523285039216##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523285039245##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523285039246##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523285039259##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523338414371##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523338414408##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523338414434##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523342451179##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523342451197##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523342451205##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523342451215##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523342451219##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523345137218##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523345917582##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523353142390##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523353490843##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523353490859##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523353490860##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523353490861##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523365561268##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523365561289##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523365561291##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523365561302##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523412869123##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523429033277##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523429033288##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523429033297##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523429033307##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523429033307##SalesOrderObj", "type": {"name": "text"}}, {"id": "activity_1523602527177##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1523602527177##PaymentObj", "type": {"name": "text"}}, {"id": "activity_1527822453043##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453044##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453045##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453087##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453100##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453106##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453107##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1527822453108##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_1528076968657##object_059Bz__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_9Ll2p__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_u0zRs__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_78yWv__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_vmd4W__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_hY162__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_r2W8g__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_Gm9zv__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_3pfae__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_dV971__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_d9hWL__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_rlEyl__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_eo0lK__c", "type": {"name": "text"}}, {"id": "activity_0##object_059Bz__c##field_g28AP__c", "type": {"name": "list", "elementType": {"name": "text"}}}, {"id": "activity_0##object_059Bz__c##field_y5bsW__c", "type": {"name": "number"}}], "id": "5b1650ce4fa424fb48eb1bd0", "sourceWorkflowId": "346297631772606464"}