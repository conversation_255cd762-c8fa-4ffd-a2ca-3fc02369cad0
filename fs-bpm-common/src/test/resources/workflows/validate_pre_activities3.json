{"creator": "1000", "externalFlow": 0, "modifier": "1000", "type": "workflow_bpm", "singleInstanceFlow": 0, "modifyTime": *************, "appId": "BPM", "tenantId": "428392", "name": "客户跟单流程", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "id": "*************"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "update", "executionName": "编辑对象", "entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "form": [[{"name": "UDMText1__c", "label": "客户意向产品", "type": "long_text", "required": false}, {"name": "UDMSel2__c", "label": "客户想要风格", "type": "select_many", "required": false}, {"name": "UDDate1__c", "label": "上门量房时间", "type": "date", "required": false}, {"name": "UDAttach7__c", "label": "预算单附件", "type": "file_attachment", "required": false}, {"name": "UDSText4__c", "label": "客户是否了解过竞品及价格", "type": "text", "required": false}, {"name": "remark", "label": "备注", "type": "long_text", "required": false}]]}, "canSkip": false, "name": "交意向金客户", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##AccountObj##owner$$数据相关人员-数据负责人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "exclusiveGateway", "name": "客户类型判断", "description": "", "id": "*************", "defaultTransitionId": "*************", "gatewayType": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}}, "name": "免费测量客户", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##AccountObj##owner$$数据相关人员-数据负责人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}, "form": [[{"name": "UDAttach1__c", "label": "沟通后图纸", "type": "file_attachment", "required": false}, {"name": "UDAttach2__c", "label": "客户确认后的初测水电图", "type": "file_attachment", "required": false}, {"name": "UDImg1__c", "label": "确认凭据（qq、微信方案确认截图）", "type": "image", "required": false}]]}, "name": "上门量房初测", "description": "确认凭据：如微信、qq截图，提醒业主购买厨房电器尺寸并提供产品尺寸范围", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##AccountObj##group$$数据相关人员-数据相关团队成员"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}, "form": [[{"name": "UDSSel1__c", "label": "吊顶是否吊好", "type": "select_one", "required": false}, {"name": "UDSSel2__c", "label": "墙地砖是否铺好", "type": "select_one", "required": false}, {"name": "UDSSel3__c", "label": "地面是否找平", "type": "select_one", "required": false}, {"name": "UDSSel4__c", "label": "护墙基层是否打好", "type": "select_one", "required": false}, {"name": "UDSSel5__c", "label": "厨房电器是否购买到位", "type": "select_one", "required": false}, {"name": "UDSSel6__c", "label": "水槽台盆是否到位", "type": "select_one", "required": false}]]}, "name": "上门量房复测", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##assigneeId$$前置业务处理人员-上门量房初测-节点处理人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}, "form": [[{"name": "UDAttach3__c", "label": "合同（协议正联/价格清单/最终图纸）", "type": "file_attachment", "required": false}, {"name": "UDAttach4__c", "label": "下单图/五金清单", "type": "file_attachment", "required": false}, {"name": "UDSSel7__c", "label": "付款方式", "type": "select_one", "required": false}, {"name": "UDSText2__c", "label": "尾款多少", "type": "text", "required": false}, {"name": "UDSText1__c", "label": "预付多少", "type": "text", "required": false}, {"name": "UDSText3__c", "label": "送客户的礼品", "type": "text", "required": false}, {"name": "UDAttach9__c", "label": "未完成安装下单附件", "type": "file_attachment", "required": false}]]}, "name": "签订合同付款", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##assigneeId$$前置业务处理人员-上门量房复测-节点处理人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_*************##AccountObj"}, "entityName": "客户", "entityId": "AccountObj", "form": [[{"name": "UDAttach3__c", "label": "合同（协议正联/价格清单/最终图纸）", "type": "file_attachment", "required": false}, {"name": "UDAttach4__c", "label": "下单图/五金清单", "type": "file_attachment", "required": false}]]}, "name": "审查图纸", "description": "", "id": "*************", "assignee": {"person": ["1037"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}, "form": [[{"name": "UDAttach5__c", "label": "厂家回传单", "type": "file_attachment", "required": false}]]}, "name": "外部回单审核", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##assigneeId$$前置业务处理人员-内部下单-节点处理人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}, "form": [[{"name": "UDSSel9__c", "label": "是否货到齐", "type": "select_one", "required": false}, {"name": "UDSSel10__c", "label": "五金是否配好", "type": "select_one", "required": false}]]}, "name": "跟踪到货", "description": "", "id": "*************", "assignee": {"person": ["1044"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}, {"type": "userTask", "bpmExtension": {"executionName": "选择或新建关联对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "销售订单", "relatedObjectId": {"expression": "activity_*************##SalesOrderObj"}, "executionType": "addRelatedObject", "objectId": {"expression": "activity_*************##AccountObj"}, "relatedEntityId": "SalesOrderObj", "target_related_list_name": "account_sales_order_list"}, "name": "内部建立销售订单", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##assigneeId$$前置业务处理人员-审查图纸-节点处理人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_0##AccountObj"}, "entityId": "AccountObj", "entityName": "客户", "form": [[{"name": "account_source", "label": "来源", "type": "select_one", "required": true}, {"name": "account_type", "label": "客户类型", "type": "select_one", "required": false}, {"name": "UDAttach7__c", "label": "预算单附件", "type": "file_attachment", "required": false}, {"name": "UDDate1__c", "label": "上门量房时间", "type": "date", "required": false}, {"name": "UDImg1__c", "label": "确认凭据（qq、微信方案确认截图）", "type": "image", "required": false}, {"name": "UDMSel2__c", "label": "客户想要风格", "type": "select_many", "required": false}, {"name": "UDMText1__c", "label": "客户意向产品", "type": "long_text", "required": false}, {"name": "UDSText4__c", "label": "客户是否了解过竞品及价格", "type": "text", "required": false}]]}, "name": "分配设计师", "description": "", "id": "*************", "assignee": {"person": ["1000", "1002"]}, "taskType": "anyone", "assignNextTask": 1, "externalApplyTask": 0}, {"type": "endEvent", "name": "结束", "description": "", "id": "*************"}, {"type": "userTask", "bpmExtension": {"executionName": "选择或新建关联对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "回款", "relatedObjectId": {"expression": "activity_*************##PaymentObj"}, "executionType": "addRelatedObject", "relatedEntityId": "PaymentObj", "target_related_list_name": "related_list_a_payment", "objectId": {"expression": "activity_*************##AccountObj"}}, "name": "财务核实合同回款", "description": "", "id": "*************", "assignee": {"role": ["00000000000000000000000000000002"]}, "taskType": "anyone", "assignNextTask": 1, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_*************##AccountObj"}, "entityName": "客户", "entityId": "AccountObj", "form": [[{"name": "UDAttach5__c", "label": "厂家回传单", "type": "file_attachment", "required": false}]]}, "name": "审批", "description": "", "id": "*************", "assignee": {"person": ["1000"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "executionTask", "bpmExtension": {"executionType": "execution", "entityName": "回款", "entityId": "PaymentObj", "objectId": {"expression": "activity_*************##PaymentObj"}}, "name": "自动节点", "description": "", "id": "*************", "itemList": [{"title": "厂家货已到，请核实是否有尾款！", "content": "厂家已到货，如果客户有尾款，请及时联系客户并收取尾款后，在上门安装！\r\n【本客户已回款${activity_*************##PaymentObj##payment_amount}】${activity_*************##PaymentObj##account_id##UDSText2__c}", "taskType": "send_qixin", "recipients": {"person": ["1019"], "dept": ["1034", "1032"]}}]}, {"type": "userTask", "reminders": [{"remindStrategy": 2, "remindContent": "以约到客户安装时间，请大家知晓！！${activity_*************##AccountObj##UDDate2__c} ", "remindTitle": "任务完成通知", "remindTargets": {"ext_bpm": ["activity_*************##AccountObj##group$$数据相关团队成员"]}}], "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}, "form": [[{"name": "UDDate2__c", "label": "安装时间", "type": "date", "required": false}]]}, "name": "预约安装", "description": "", "id": "*************", "assignee": {"person": ["1019"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_*************##AccountObj"}, "form": [[{"name": "UDAttach8__c", "label": "安装回执单及安装完毕照片", "type": "file_attachment", "required": false}, {"name": "UDSSel11__c", "label": "客户是否安装完毕", "type": "select_one", "required": true}]]}, "name": "安装完毕", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##assigneeId$$前置业务处理人员-预约安装-节点处理人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "客户", "entityId": "AccountObj", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_*************##"}, "executionType": "update", "objectId": {"expression": "activity_0##AccountObj"}, "form": [[{"name": "UDMSel1__c", "label": "未安装完毕责任人", "type": "select_many", "required": false}, {"name": "UDAttach9__c", "label": "未完成安装下单附件", "type": "file_attachment", "required": true}, {"name": "UDMText3__c", "label": "问题详情", "type": "long_text", "required": true}]]}, "name": "未安装完毕", "description": "", "id": "*************", "assignee": {"ext_bpm": ["activity_*************##assigneeId$$前置业务处理人员-预约安装-节点处理人"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "exclusiveGateway", "name": "安装结果", "description": "", "id": "*************", "defaultTransitionId": "*************", "gatewayType": 0}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_*************##AccountObj"}, "entityName": "客户", "entityId": "AccountObj", "form": [[{"name": "UDAttach8__c", "label": "安装回执单及安装完毕照片", "type": "file_attachment", "required": false}, {"name": "UDMSel1__c", "label": "未安装完毕责任人", "type": "select_many", "required": false}, {"name": "UDMText3__c", "label": "问题详情", "type": "long_text", "required": false}]]}, "name": "审批", "description": "", "id": "*************", "assignee": {"person": ["1046"]}, "taskType": "anyone", "assignNextTask": 0, "externalApplyTask": 0}, {"type": "exclusiveGateway", "name": "分支节点", "description": "", "id": "*************", "defaultTransitionId": "*************", "gatewayType": 0}], "transitions": [{"description": "流程发起数据 / 客户 - 客户类型 等于 交意向金客户", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##AccountObj##account_type"}, "right": {"value": "1", "type": {"name": "text"}}}]}]}, "serialNumber": 0}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 1}, {"description": "流程发起数据 / 客户 - 客户类型 等于 免费测量客户", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##AccountObj##account_type"}, "right": {"value": "2", "type": {"name": "text"}}}]}]}, "serialNumber": 2}, {"description": "流程发起数据 / 客户 - 客户类型 等于 其它", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_0##AccountObj##account_type"}, "right": {"value": "3", "type": {"name": "text"}}}]}]}, "serialNumber": 3}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 4}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 5}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 6}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 7}, {"id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}, "serialNumber": 8}, {"id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}, "serialNumber": 9}, {"id": "1529465601952", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}, "serialNumber": 10}, {"id": "1529465601953", "fromId": "*************", "toId": "*************", "serialNumber": 11}, {"id": "1529465602011", "fromId": "*************", "toId": "*************", "serialNumber": 12}, {"id": "1529465602012", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}, "serialNumber": 13}, {"id": "1529465602017", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}, "serialNumber": 14}, {"id": "1529465602084", "fromId": "*************", "toId": "*************", "serialNumber": 15}, {"id": "1529465602085", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}, "serialNumber": 16}, {"id": "1529465602086", "fromId": "*************", "toId": "*************", "serialNumber": 17}, {"id": "1529465602088", "fromId": "*************", "toId": "*************", "serialNumber": 18}, {"id": "1529465602103", "fromId": "*************", "toId": "*************", "serialNumber": 19}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 20}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 21}, {"description": "安装完毕 / 客户 - 客户是否安装完毕 等于 是", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##AccountObj##UDSSel11__c"}, "right": {"value": "1", "type": {"name": "text"}}}]}]}, "serialNumber": 22}, {"description": "安装完毕 / 客户 - 客户是否安装完毕 等于 否", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##AccountObj##UDSSel11__c"}, "right": {"value": "2", "type": {"name": "text"}}}]}]}, "serialNumber": 23}, {"id": "*************", "fromId": "*************", "toId": "*************", "serialNumber": 24}, {"id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}, "serialNumber": 25}, {"id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}, "serialNumber": 26}, {"description": "未安装完毕 / 客户 - 未安装完毕责任人 包含 厂家,其他", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "hasAnyOf", "left": {"expression": "activity_*************##AccountObj##UDMSel1__c"}, "right": {"value": ["2", "6"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 27}, {"description": "未安装完毕 / 客户 - 未安装完毕责任人 包含 设计师,物流,安装,客户", "id": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "hasAnyOf", "left": {"expression": "activity_*************##AccountObj##UDMSel1__c"}, "right": {"value": ["1", "3", "4", "5"], "type": {"name": "list", "elementType": {"name": "text"}}}}]}]}, "serialNumber": 28}], "variables": [{"id": "activity_0##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##result", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##SalesOrderObj", "type": {"name": "text"}}, {"id": "activity_*************##result", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##PaymentObj", "type": {"name": "text"}}, {"id": "activity_*************##result", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj", "type": {"name": "text"}}, {"id": "activity_*************##result", "type": {"name": "text"}}, {"id": "activity_0##AccountObj##account_type", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj##UDSSel11__c", "type": {"name": "text"}}, {"id": "activity_*************##AccountObj##UDMSel1__c", "type": {"name": "list", "elementType": {"name": "text"}}}], "id": "5b2a36adcc9da301a06fe334", "sourceWorkflowId": "367062486905389056", "createTime": *************}