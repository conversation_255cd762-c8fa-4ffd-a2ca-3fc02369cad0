{"activities": [{"id": "1529912568685", "type": "startEvent", "name": "开始", "description": ""}, {"id": "1529912568699", "name": "分支节点", "description": "", "type": "exclusiveGateway", "defaultTransitionId": "1529912568707"}, {"id": "1529912568700", "name": "结束", "description": "", "type": "endEvent"}, {"id": "1529912568702", "name": "自动节点", "description": "", "bpmExtension": {"executionType": "execution", "entityName": "家乐福", "entityId": "object_v2c61__c", "objectId": {"expression": "activity_0##object_v2c61__c"}}, "itemList": [{"taskType": "updates", "updateFieldJson": "[{\"isCalculate\":true,\"key\":\"${object_v2c61__c.field_6ptio__c.field_4k28D__c}\",\"value\":\"${object_v2c61__c.field_4k28D__c}\",\"defaultValue\":\"\",\"entityId\":\"activity_1529912568702##object_v2c61__c\"}]"}], "type": "executionTask"}, {"id": "1529912568705", "name": "结束", "description": "", "type": "endEvent"}, {"id": "1529912568718", "name": "业务活动", "description": "", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone", "bpmExtension": {"executionName": "编辑对象", "entityName": "家乐福", "entityId": "object_v2c61__c", "relatedEntityName": "请选择", "relatedObjectId": {"expression": "activity_1529912568718##"}, "executionType": "update", "objectId": {"expression": "activity_0##object_v2c61__c"}}, "type": "userTask", "assignNextTask": 0}], "variables": [{"id": "activity_0##object_v2c61__c", "type": {"name": "text"}}, {"id": "activity_1529912568718##object_v2c61__c", "type": {"name": "text"}}, {"id": "activity_0##object_v2c61__c##field_0x2Jt__c", "type": {"name": "text"}}], "transitions": [{"id": "1529912568703", "fromId": "1529912568685", "toId": "1529912568699", "serialNumber": 0}, {"id": "1529912568704", "fromId": "1529912568699", "toId": "1529912568702", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "contains", "left": {"expression": "activity_0##object_v2c61__c##field_0x2Jt__c"}, "right": {"type": {"name": "text"}, "value": 1}}]}]}, "description": "流程发起数据 / 家乐福 - 统计字段 包含 1", "serialNumber": 1}, {"id": "1529912568707", "fromId": "1529912568699", "toId": "1529912568700", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "contains", "left": {"expression": "activity_0##object_v2c61__c##field_0x2Jt__c"}, "right": {"type": {"name": "text"}, "value": 2}}]}]}, "description": "流程发起数据 / 家乐福 - 统计字段 包含 2", "serialNumber": 2}, {"id": "1529912568719", "fromId": "1529912568702", "toId": "1529912568718", "serialNumber": 3}, {"id": "1529912568720", "fromId": "1529912568718", "toId": "1529912568705", "serialNumber": 4}]}