package com.facishare.bpm.util.verifiy


import com.facishare.bpm.bpmn.ExecutionItem
import com.facishare.bpm.util.verifiy.handler.after.UpdateValidateActionHandler
import com.facishare.bpmn.definition.util.JsonUtil
import com.google.gson.reflect.TypeToken
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> ( <PERSON> )
 * @Date on 2018/5/3
 * @since 6.3
 */
class UpdateValidateActionHandlerTest extends Specification implements BaseValidateHandlerTest {

    @Unroll("#desc")
    def "校验UpdateField"() {
        when:
        def result
        try {
            result = UpdateValidateActionHandler.UpdateField.format2BPMFieldVariable(com.facishare.rest.core.util.JsonUtil.fromJson(updateFieldJson, new TypeToken<List<UpdateValidateActionHandler.UpdateField>>() {
            }.getType()), relatedObjectId, useRelated);
        } catch (Exception e) {
            result = []
        }
        then:
        rst == result

        where:
        desc           | updateFieldJson                                                                                                                                                   | relatedObjectId | useRelated  || rst
        "更新当前对象" | "[{\"isCalculate\":false,\"key\":\"\${object_x15Ou__c.name}\",\"value\":\"11\",\"defaultValue\":\"\",\"entityId\":\"activity_17231045064931##object_x15Ou__c\"}]" | null            | false     || ["\${activity_17231045064931##object_x15Ou__c##name}"]
        "更新当前对象-自关联" | "[{\"isCalculate\":false,\"key\":\"\${NewOpportunityObj.leads_id.source}\",\"value\":\"2\",\"defaultValue\":\"\",\"entityId\":\"activity_17226592500411##SelfRef##NewOpportunityObj\"}]" | null            | false     || ["\${activity_17226592500411##SelfRef##NewOpportunityObj##leads_id##source}"]
        "更新关联对象" | "[{\"isCalculate\":false,\"key\":\"\${object_B1bC5__c.name}\",\"value\":\"11\",\"defaultValue\":\"\",\"entityId\":\"activity_17231045064931##object_x15Ou__c\"}]" | "activity_17231045064931##object_x15Ou__c.field_K27lZ__c.field_Bvrg2__c.field_8gfU5__c.field_eHhcC__c"            | true     || ["activity_17231045064931##object_x15Ou__c.field_K27lZ__c.field_Bvrg2__c.field_8gfU5__c.field_eHhcC__c"]
    }

    @Unroll("#desc")
    def "校验字段更新后动作"() {
        when:
        boolean result
        try {
            UpdateValidateActionHandler.checkRelatedManyExecutionItem(null, getExecutionItem(name));
            result = true
        } catch (Exception e) {
            result = false
        }
        then:
        rst == result

        where:
        desc                                              | name                                              || rst
        "更新关联对象-正常"                               | "更新关联对象-正常"                               || false
        "更新关联对象-缺少relatedEntityId"                | "更新关联对象-缺少relatedEntityId"                || false
        "更新关联对象-缺少relatedObjectId"                | "更新关联对象-缺少relatedObjectId"                || false
        "更新关联对象-级数超过5级"                        | "更新关联对象-级数超过5级"                        || false
        "更新关联对象-key中的对象和relatedEntityId不一致" | "更新关联对象-key中的对象和relatedEntityId不一致" || false
        "更新关联对象-key的级数超过2"                     | "更新关联对象-key的级数超过2"                     || false
        "更新关联对象-updateJson中有多个entity"           | "更新关联对象-updateJson中有多个entity"           || false
        "更新当前对象-正常"                               | "更新当前对象-正常"                               || false
        "更新当前对象-有relatedEntityId"                  | "更新当前对象-有relatedEntityId"                  || false
        "更新当前对象-有relatedObjectId"                  | "更新当前对象-有relatedObjectId"                  || false
    }

    def getExecutionItem(String name) {
        if(name == "更新关联对象-正常") {
            String json = "{\n" +
                    "\t\t\"relatedEntityId\": \"object_B1bC5__c\",\n" +
                    "\t\t\"relatedObjectId\": \"activity_17231045064931##object_x15Ou__c.field_K27lZ__c.field_Bvrg2__c.field_8gfU5__c.field_eHhcC__c\",\n" +
                    "\t\t\"taskType\": \"updates\",\n" +
                    "\t\t\"updateFieldJson\": \"[{\\\"isCalculate\\\":false,\\\"key\\\":\\\"\${object_B1bC5__c.name}\\\",\\\"value\\\":\\\"11\\\",\\\"defaultValue\\\":\\\"\\\",\\\"entityId\\\":\\\"activity_17231045064931##object_x15Ou__c\\\"}]\",\n" +
                    "\t\t\"useRelated\": true\n" +
                    "\t}"
            return JsonUtil.fromJson(json, ExecutionItem.class)
        }

        if(name == "更新关联对象-key的级数超过2") {
            String json = "{\n" +
                    "\t\t\"relatedEntityId\": \"object_B1bC5__c\",\n" +
                    "\t\t\"relatedObjectId\": \"activity_17231045064931##object_x15Ou__c.field_K27lZ__c.field_Bvrg2__c.field_8gfU5__c.field_eHhcC__c\",\n" +
                    "\t\t\"taskType\": \"updates\",\n" +
                    "\t\t\"updateFieldJson\": \"[{\\\"isCalculate\\\":false,\\\"key\\\":\\\"\${object_B1bC5__c.field_reference__c.name}\\\",\\\"value\\\":\\\"11\\\",\\\"defaultValue\\\":\\\"\\\",\\\"entityId\\\":\\\"activity_17231045064931##object_x15Ou__c\\\"}]\",\n" +
                    "\t\t\"useRelated\": true\n" +
                    "\t}"
            return JsonUtil.fromJson(json, ExecutionItem.class)
        }
        if(name == "更新关联对象-缺少relatedEntityId") {
            ExecutionItem executionItem = getExecutionItem("更新关联对象-正常")
            executionItem.setRelatedEntityId(null)
            return executionItem
        }
        if(name == "更新关联对象-缺少relatedObjectId") {
            ExecutionItem executionItem = getExecutionItem("更新关联对象-正常")
            executionItem.setRelatedObjectId(null)
            return executionItem
        }
        if(name == "更新关联对象-级数超过5级") {
            ExecutionItem executionItem = getExecutionItem("更新关联对象-正常")
            executionItem.setRelatedObjectId("activity_17231045064931##object_x15Ou__c.field_K27lZ__c.field_K27lZ__c.field_K27lZ__c.field_Bvrg2__c.field_8gfU5__c.field_eHhcC__c")
            return executionItem
        }
        if(name == "更新关联对象-key中的对象和relatedEntityId不一致") {
            ExecutionItem executionItem = getExecutionItem("更新关联对象-正常")
            executionItem.setUpdateFieldJson("[{\"isCalculate\":false,\"key\":\"\${object_x15Ou__c.name}\",\"value\":\"11\",\"defaultValue\":\"\",\"entityId\":\"activity_17231045064931##object_x15Ou__c\"},{\"isCalculate\":false,\"key\":\"\${object_B1bC5__c.name}\",\"value\":\"11\",\"defaultValue\":\"\",\"entityId\":\"activity_17231045064931##object_B1bC5__c\"}]")
            return executionItem
        }
        if(name == "更新关联对象-updateJson中有多个entity") {
            ExecutionItem executionItem = getExecutionItem("更新关联对象-正常")
            executionItem.setUpdateFieldJson("[{\"isCalculate\":false,\"key\":\"\${object_B1bC5__c.name}\",\"value\":\"11\",\"defaultValue\":\"\",\"entityId\":\"activity_17231045064931##object_x15Ou__c\"},{\"isCalculate\":false,\"key\":\"\${object_B1bC5__c.name}\",\"value\":\"11\",\"defaultValue\":\"\",\"entityId\":\"activity_17231045064931##object_B1bC5__c\"}]")
            return executionItem
        }
        if(name == "更新当前对象-正常") {
            String json = "{\n" +
                    "    \"0\": {\n" +
                    "        \"id\": 1723635790954,\n" +
                    "        \"taskType\": \"updates\",\n" +
                    "        \"updateFieldJson\": \"[{\\\"isCalculate\\\":false,\\\"key\\\":\\\"\${object_x15Ou__c.field_2K7oc__c}\\\",\\\"value\\\":\\\"dsf\\\",\\\"defaultValue\\\":\\\"\\\",\\\"entityId\\\":\\\"activity_0##object_x15Ou__c\\\"}]\",\n" +
                    "        \"useRelated\": false\n" +
                    "    }\n" +
                    "}"
            return JsonUtil.fromJson(json, ExecutionItem.class)
        }
        if(name == "更新当前对象-有relatedEntityId") {
            ExecutionItem executionItem = getExecutionItem("更新当前对象-正常")
            executionItem.setRelatedEntityId("object_B1bC5__c")
            return executionItem
        }
        if(name == "更新当前对象-有relatedObjectId") {
            ExecutionItem executionItem = getExecutionItem("更新当前对象-正常")
            executionItem.setRelatedObjectId("activity_17231045064931##object_x15Ou__c.field_K27lZ__c.field_Bvrg2__c.field_8gfU5__c.field_eHhcC__c")
            return executionItem
        }
    }


}
