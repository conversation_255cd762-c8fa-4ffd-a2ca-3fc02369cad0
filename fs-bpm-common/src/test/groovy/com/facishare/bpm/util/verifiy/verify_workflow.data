{"type": "workflow_bpm", "modifyTime": *************.0, "appId": "BPM", "tenantId": "515012", "name": "培训测试流程", "description": "", "activities": [{"type": "startEvent", "name": "开始", "description": "", "ruleId": "*************"}, {"type": "userTask", "bpmExtension": {"executionType": "addRelatedObject", "executionName": "选择或新建关联对象", "entityId": "AccountObj", "entityName": "客户", "objectId": {"expression": "activity_0##AccountObj"}, "relatedEntityName": "工单alin测试", "relatedObjectId": {"expression": "activity_*************##object_9u4__c"}, "relatedEntityId": "object_9u4__c", "target_related_list_name": "target_related_list_1lm__c"}, "canSkip": false, "name": "创建工单", "description": "", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "exclusiveGateway", "name": "分支节点", "description": "", "ruleId": "*************", "defaultTransitionId": "*************"}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "工单alin测试", "entityId": "object_9u4__c", "executionType": "update", "objectId": {"expression": "activity_*************##object_9u4__c"}, "form": [[{"name": "field_2l1__c", "label": "投诉内容", "type": "text", "required": true, "readonly": false}]]}, "name": "客户投诉", "description": "", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "工单alin测试", "entityId": "object_9u4__c", "executionType": "update", "objectId": {"expression": "activity_*************##object_9u4__c"}, "form": [[{"name": "field_1ka__c", "label": "咨询内容", "type": "text", "required": true, "readonly": false}]]}, "name": "客户反馈", "description": "", "ruleId": "1496900510853", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "bpmExtension": {"executionName": "操作对象", "entityName": "工单alin测试", "entityId": "object_9u4__c", "executionType": "operation", "actionCode": "addteammember", "objectId": {"expression": "activity_*************##object_9u4__c"}}, "name": "处理客户投诉", "description": "", "ruleId": "1496900510868", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "bpmExtension": {"executionName": "操作对象", "entityName": "工单alin测试", "entityId": "object_9u4__c", "executionType": "operation", "objectId": {"expression": "activity_1496900510853##object_9u4__c"}, "actionCode": "changeowner"}, "name": "处理客户反馈", "description": "", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_*************##object_9u4__c"}, "form": [[{"name": "result", "value": true, "label": "会签结果", "type": "text", "readonly": false, "required": true}]], "entityName": "工单alin测试", "entityId": "object_9u4__c"}, "name": "会签", "description": "", "ruleId": "1496900510877", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "all"}, {"type": "parallelGateway", "name": "＋", "description": "", "ruleId": "1496900510880"}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "工单alin测试", "entityId": "object_9u4__c", "executionType": "update", "objectId": {"expression": "activity_*************##object_9u4__c"}, "form": [[{"name": "field_2l1__c", "label": "投诉内容", "type": "text", "required": false, "readonly": false}]]}, "name": "回访", "description": "", "ruleId": "1496900510881", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "userTask", "bpmExtension": {"executionName": "编辑对象", "entityName": "工单alin测试", "entityId": "object_9u4__c", "executionType": "update", "objectId": {"expression": "activity_1496900510868##object_9u4__c"}, "form": [[{"name": "field_1ka__c", "label": "咨询内容", "type": "text", "required": false, "readonly": false}]]}, "name": "危机处理", "description": "", "ruleId": "1496900510882", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}, {"type": "parallelGateway", "name": "＋", "description": "", "ruleId": "1496900510894"}, {"type": "endEvent", "name": "结束", "description": "", "ruleId": "*************"}, {"type": "userTask", "bpmExtension": {"actionCode": "", "executionType": "approve", "executionName": "", "objectId": {"expression": "activity_*************##object_9u4__c"}, "form": [[{"name": "result", "value": true, "label": "审批结果", "type": "text", "readonly": false, "required": true}]], "entityName": "工单alin测试", "entityId": "object_9u4__c"}, "name": "审批", "description": "", "ruleId": "*************", "assignee": {"ext_bpm": ["instance##owner$$流程发起人"]}, "taskType": "anyone"}], "transitions": [{"ruleId": "1496900510847", "fromId": "*************", "toId": "*************"}, {"ruleId": "1496900510850", "fromId": "*************", "toId": "*************"}, {"description": "创建工单 / 工单alin测试 - 业务类型 等于 客户咨询", "ruleId": "*************", "fromId": "*************", "toId": "1496900510853", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##object_9u4__c##record_type"}, "right": {"value": "default__c", "type": {"name": "text"}}}]}]}}, {"description": "创建工单 / 工单alin测试 - 业务类型 等于 客户投诉", "ruleId": "1496900510858", "fromId": "*************", "toId": "*************", "condition": {"type": "or", "conditions": [{"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##object_9u4__c##record_type"}, "right": {"value": "field_5oi__c", "type": {"name": "text"}}}]}]}}, {"ruleId": "1496900510869", "fromId": "*************", "toId": "1496900510868"}, {"ruleId": "1496900510873", "fromId": "1496900510853", "toId": "*************"}, {"ruleId": "1496900510878", "fromId": "1496900510868", "toId": "1496900510877"}, {"ruleId": "1496900510883", "fromId": "1496900510877", "toId": "1496900510880", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_1496900510877##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}}, {"ruleId": "1496900510888", "fromId": "1496900510880", "toId": "1496900510881"}, {"ruleId": "1496900510889", "fromId": "1496900510880", "toId": "1496900510882"}, {"ruleId": "1496900510892", "fromId": "1496900510877", "toId": "1496900510868", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_1496900510877##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}}, {"ruleId": "1496900510895", "fromId": "1496900510881", "toId": "1496900510894"}, {"ruleId": "1496900510896", "fromId": "1496900510882", "toId": "1496900510894"}, {"ruleId": "1496900510898", "fromId": "1496900510894", "toId": "*************"}, {"ruleId": "1496900510902", "fromId": "*************", "toId": "*************"}, {"ruleId": "1496900510903", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "reject", "type": {"name": "text"}}}]}}, {"ruleId": "*************", "fromId": "*************", "toId": "*************", "condition": {"type": "and", "conditions": [{"type": "equals", "left": {"expression": "activity_*************##result"}, "right": {"value": "agree", "type": {"name": "text"}}}]}}], "variables": [{"ruleId": "activity_0##AccountObj", "type": {"name": "text"}}, {"ruleId": "activity_*************##AccountObj", "type": {"name": "text"}}, {"ruleId": "activity_*************##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_*************##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_1496900510853##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_1496900510868##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_*************##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_1496900510877##result", "type": {"name": "text"}}, {"ruleId": "activity_1496900510881##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_1496900510882##object_9u4__c", "type": {"name": "text"}}, {"ruleId": "activity_*************##result", "type": {"name": "text"}}, {"ruleId": "activity_*************##object_9u4__c##record_type", "type": {"name": "text"}}], "ruleId": "593907f5f63d00771052b98b", "sourceWorkflowId": "257830286520025088", "createTime": *************.0}