package com.facishare.bpm.util


import spock.lang.Specification
import spock.lang.Unroll

/**
 * Created by cuiyongx<PERSON> on 17/6/12.
 */
class BPMExtensionUtilsTest extends Specification {

    @Unroll
    def "测试 getExpression 方法 - #desc"() {
        when:
        def expression = BPMExtensionUtils.getExpression(expStr)

        then:
        expression.activityId == expectedActivityId
        expression.descApiName == expectedDescApiName
        expression.instanceVariableKey == expectedInstanceVariableKey
        expression.fieldName == expectedFieldName

        where:
        desc | expStr | expectedActivityId | expectedDescApiName | expectedInstanceVariableKey | expectedFieldName
        "基本表达式" | "activity_123##AccountObj" | "123" | "AccountObj" | "activity_123##AccountObj" | null
        "带字段名的表达式" | "activity_123##AccountObj##name" | "123" | "AccountObj" | "activity_123##AccountObj" | "name"
        "自引用表达式" | "activity_123##SelfRef##AccountObj" | "123" | "AccountObj" | "activity_123##SelfRef##AccountObj" | null
        "自引用带字段表达式" | "activity_123##SelfRef##AccountObj##field" | "123" | "AccountObj" | "activity_123##SelfRef##AccountObj" | "field"
    }

    @Unroll
    def "测试 transferExpression 方法 - #desc"() {
        given:
        def objectIdExp = input

        when:
        def expression = BPMExtensionUtils.transferExpression(objectIdExp)

        then:
        if (expectedNull) {
            assert expression == null
        } else {
            assert expression.activityId == expectedActivityId
            assert expression.descApiName == expectedDescApiName
        }

        where:
        desc | input | expectedNull | expectedActivityId | expectedDescApiName
        "空输入" | null | true | null | null
        "空Map" | [:] | true | null | null
        "有效表达式" | [expression: "activity_123##AccountObj"] | false | "123" | "AccountObj"
    }

    def "测试 getActivityRealId 方法"() {
        expect:
        BPMExtensionUtils.getActivityRealId(expression) == expectedId

        where:
        expression | expectedId
        "activity_123##AccountObj" | "123"
        "activity_456##SelfRef##AccountObj" | "456"
    }
}
