package com.facishare.bpm.util


import spock.lang.Specification
import spock.lang.Unroll

/**
 * Created by cuiyongx<PERSON> on 17/6/12.
 */
class BPMExtensionUtilsTest extends Specification {

    @Unroll
    def "测试 getExpression 方法 - #desc"() {
        when:
        def expression = BPMExtensionUtils.getExpression(expStr)

        then:
        expression.activityId == expectedActivityId
        expression.descApiName == expectedDescApiName
        expression.instanceVariableKey == expectedInstanceVariableKey
        expression.fieldName == expectedFieldName

        where:
        desc | expStr | expectedActivityId | expectedDescApiName | expectedInstanceVariableKey | expectedFieldName
        "基本表达式" | "activity_123##AccountObj" | "123" | "AccountObj" | "activity_123##AccountObj" | null
        "带字段名的表达式" | "activity_123##AccountObj##name" | "123" | "AccountObj" | "activity_123##AccountObj" | "name"
        "自引用表达式" | "activity_123##SelfRef##AccountObj" | "123" | "AccountObj" | "activity_123##SelfRef##AccountObj" | null
        "自引用带字段表达式" | "activity_123##SelfRef##AccountObj##field" | "123" | "AccountObj" | "activity_123##SelfRef##AccountObj" | "field"
    }

    @Unroll
    def "测试 transferExpression 方法 - #desc"() {
        given:
        def objectIdExp = input

        when:
        def expression = BPMExtensionUtils.transferExpression(objectIdExp)

        then:
        if (expectedNull) {
            assert expression == null
        } else {
            assert expression.activityId == expectedActivityId
            assert expression.descApiName == expectedDescApiName
        }

        where:
        desc | input | expectedNull | expectedActivityId | expectedDescApiName
        "空输入" | null | true | null | null
        "空Map" | [:] | true | null | null
        "有效表达式" | [expression: "activity_123##AccountObj"] | false | "123" | "AccountObj"
    }

    def "测试 getActivityRealId 方法"() {
        expect:
        BPMExtensionUtils.getActivityRealId(expression) == expectedId

        where:
        expression | expectedId
        "activity_123##AccountObj" | "123"
        "activity_456##SelfRef##AccountObj" | "456"
    }

    def "测试transferExpression有效情况"() {
        given:
        def validMap = [expression: "activity_123##AccountObj##field"]

        when:
        def validResult = BPMExtensionUtils.transferExpression(validMap)

        then:
        validResult != null
        validResult.activityId == "123"
        validResult.descApiName == "AccountObj"
        validResult.fieldName == "field"
    }

    def "测试getExpression复杂情况"() {
        given:
        def complexExp = "activity_**********##SelfRef##ComplexObjectName##complexFieldName"

        when:
        def result = BPMExtensionUtils.getExpression(complexExp)

        then:
        result.activityId == "**********"
        result.descApiName == "ComplexObjectName"
        result.fieldName == "complexFieldName"
        result.instanceVariableKey == "activity_**********##SelfRef##ComplexObjectName"
    }

    def "测试表达式解析的各种格式"() {
        expect:
        // 测试各种长度的活动ID
        BPMExtensionUtils.getExpression("activity_1##Obj").activityId == "1"
        BPMExtensionUtils.getExpression("activity_12345##Obj").activityId == "12345"

        // 测试各种对象名称
        BPMExtensionUtils.getExpression("activity_123##A").descApiName == "A"
        BPMExtensionUtils.getExpression("activity_123##VeryLongObjectName").descApiName == "VeryLongObjectName"
    }
}
