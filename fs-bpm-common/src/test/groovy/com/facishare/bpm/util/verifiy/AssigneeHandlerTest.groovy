package com.facishare.bpm.util.verifiy

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra
import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions
import com.facishare.bpm.model.resource.newmetadata.UpdateData
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch
import com.facishare.bpm.remote.model.org.Dept
import com.facishare.bpm.util.RefServiceManagerAbs
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler
import com.facishare.flow.element.plugin.api.FlowElement
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper
import com.facishare.rest.core.model.RemoteContext
import com.fxiaoke.i18n.SupportLanguage
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Localization
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.i18n.util.LangIndex
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification

import java.util.function.Function
import java.util.regex.Pattern

/**
 * Created by Aaron on 09/06/2017.
 */
class AssigneeHandlerTest extends Specification implements BaseValidateHandlerTest {

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
        stub.get(_ as String, _ as Long) >> {
            String key, Long tenantId ->
                return new Localization();
        }
        LangIndex.getInstance().supportLanguageList = [new SupportLanguage()]
        LangIndex.getInstance().languageCodeMapping = [:]
    }

    RefServiceManager getServiceRef() {
        new RefServiceManagerAbs(){
            @Override
            boolean instanceListSkipDataPrivilege() {
                return false
            }

            @Override
            Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
                return null
            }

            @Override
            Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
                return null
            }

            @Override
            Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData, boolean skipRelevantTeam) {
                return null
            }

            @Override
            Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
                return null
            }

            @Override
            Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
                return null
            }

            @Override
            boolean isDataOwnerByQueryMetadata(String apiName, String objectId) {
                return false
            }

            @Override
            Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
                return null
            }

            @Override
            boolean workflowIsExists(String tenantId, String outLineId, String sourceWorkflowId) {
                return false
            }

            @Override
            Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
                return null
            }

            @Override
            boolean getDeliveryNoteEnable() {
                return false
            }

            @Override
            GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
                return null
            }

            @Override
            Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
                return null
            }

            @Override
            GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
                return null
            }

            @Override
            boolean dataPrivilege(String entityId, String objectId) {
                return false
            }

            @Override
            String getFsPeerDisplayName() {
                return null
            }

            @Override
            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }

            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck) {
                return null
            }

            @Override
            Map<String, String> getAppActions(String entityId, Boolean isExternalFlow, String appId, Integer appType) {
                return null
            }

            @Override
            GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
                return null
            }

            @Override
            Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
                return null
            }

            @Override
            def <E> E getObjectFromCache(String key, Function<String, E> function) {
                return null
            }

            @Override
            Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet) {
                return null
            }

            @Override
            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction) {
                return null
            }

            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName) {
                return null
            }

            @Override
            Map<String, String> findDataExhibitButton(String apiName, String objectId, String usePageType) {
                return null
            }

            @Override
            boolean checkEmailEnable(String sender) {
                return false
            }

            @Override
            FlowElementWrapper getFlowElementWrapper(String elementApiName) {
                return null
            }

            @Override
            FlowElement getFlowElement(String elementApiName) {
                return null
            }

            @Override
            String getI18nLinkAppName(String linkApp, String linkAppName) {
                return null
            }

            @Override
            boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
                return false
            }

            @Override
            Map<String, Object> getFlowConfig(String terminal, List<String> types) {
                return null
            }

            @Override
            boolean isNeedDiscussButton() {
                return false
            }

            @Override
            UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }
        }
    }

    def "判断处理人是否为空"() {
        given: "初始化流程"
        initI18nClient()
        def workflow = new Workflow()
        workflow.setEntryType("AccountObj")
        def extension = Maps.newHashMap()
        extension.put("entityId", "AccountObj")
        Map<String, ActivityExt> activityMap = Maps.newHashMap()
        ActivityExt activityExt = new ActivityExt(new HashMap());
        activityExt.setProperty("entityId", "AccountObj")
        activityExt.setProperty("id", "123")
        activityExt.setProperty("bpmExtension", extension)

        ActivityExt current = new ActivityExt(new HashMap());
        current.setProperty("entityId", "AccountObj")
        current.setProperty("id", "124")
        current.setProperty("bpmExtension", extension)


        activityMap.put("123", activityExt)
        workflow.setActivityMap(activityMap)
        Map<String, List<ActivityExt>> preNodeMap = Maps.newHashMap()
        preNodeMap.put("124", Lists.newArrayList(activityExt))
        workflow.setActivityPreNodeMap(preNodeMap)
        workflow.setServiceManager(getServiceRef())
        when: "校验节点中是否有处理人"

        AssigneeHandler.validateAssignee("创建工单处理人 ", current, assignee, workflow)
        then: "结果校验"

        where: "条件设置 "
        assignee                                                                                                       || result
        ["ext_bpm": Sets.newHashSet("instance##owner\$\$流程发起人")]                                       || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("activity_123##assigneeId\$\$节点处理人")]                               || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("activity_123##assigneeId##leader\$\$节点处理人上级")]                     || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("activity_123##assigneeId##dept_leader\$\$节点处理人所属主部门负责人")]          || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("activity_123##processorId\$\$节点执行人")]                              || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("activity_123##processorId##leader\$\$节点执行人上级")]                    || "创建工单 节点审批人不能为空"
        //["ext_bpm": Sets.newHashSet("activity_123##AccountObj##owner\$\$记录负责人")]                        || "创建工单 节点审批人不能为空"
        //["ext_bpm": Sets.newHashSet("activity_123##AccountObj##leader\$\$记录负责人上级")]                     || "创建工单 节点审批人不能为空"
        //["ext_bpm": Sets.newHashSet("activity_123##AccountObj##group\$\$记录相关团队成员")]                     || "创建工单 节点审批人不能为空"
        //["ext_bpm": Sets.newHashSet("activity_123##AccountObj##group_leader\$\$记录相关团队成员上级")]            || "创建工单 节点审批人不能为空"
        //["ext_bpm": Sets.newHashSet("activity_123##AccountObj##dept_leader\$\$记录负责人所在主部门负责人")]          || "创建工单 节点审批人不能为空"
        //["ext_bpm": Sets.newHashSet("activity_123##AccountObj##group_dept_leader\$\$记录相关团队成员所在主部门负责人")] || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("\${instance##owner}",
                "\${activity_123##assigneeId}",
                "\${activity_123##processorId}",
                "\${activity_123##assigneeId##leader}",
                "\${activity_123##assigneeId##dept_leader}",
                "\${activity_123##processorId##leader}")] || "创建工单 节点审批人不能为空"
        /*["ext_process": Sets.newHashSet("\${activity_123##AccountObj##owner}",
                "\${activity_123##AccountObj##group}",
                "\${activity_123##AccountObj##leader}",
                "\${activity_123##AccountObj##group_leader}",
                "\${activity_123##AccountObj##dept_leader}",
                "\${activity_123##AccountObj##group_dept_leader}")] || "创建工单 节点审批人不能为空"
        ["ext_bpm": Sets.newHashSet("\${activity_123##AccountObj##owner}",
                "\${activity_123##AccountObj##group}",
                "\${activity_123##AccountObj##owner_leader}",
                "\${activity_123##AccountObj##group_leader}",
                "\${activity_123##AccountObj##owner_main_dept_leader}",
                "\${activity_123##AccountObj##group_main_dept_leader}")] || "创建工单 节点审批人不能为空"
        ["ext_process": Sets.newHashSet("\${activity_123##AccountObj##owner}",
                "\${activity_123##AccountObj##group}",
                "\${activity_123##AccountObj##owner_leader}",
                "\${activity_123##AccountObj##group_leader}",
                "\${activity_123##AccountObj##owner_main_dept_leader}",
                "\${activity_123##AccountObj##group_main_dept_leader}")] || "创建工单 节点审批人不能为空"
        ["extUserType": Sets.newHashSet("\${activity_123##AccountObj##field_3ENpk__c}")] || "创建工单 节点审批人不能为空"*/
    }
    private static final Pattern pattern = Pattern.compile("^\\\$\\{([#@a-zA-Z_\\d]+)\\}");
    def "test 表达式"() {
        given:
        def expression="\${activity_123##AccountObj##\${activity_123##AccountObj##field_3ENpk__c}}"
        when:
        def match=pattern.matcher(expression)
        while (match.find()){
            print(match.group(1))
        }
        then:
        match.find()==false
    }
}
