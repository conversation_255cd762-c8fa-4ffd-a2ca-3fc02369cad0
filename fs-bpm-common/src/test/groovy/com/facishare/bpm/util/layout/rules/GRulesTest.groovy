package com.facishare.bpm.util.layout.rules

import spock.lang.Specification

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/6/5 11:45 AM
 */
class GRulesTest extends Specification {

    def "获取规则"() {

        when:
        List<Rules> rules = Rules.getRequiredRule(ruleArg)
        then:
        rst == rules
        where:
        ruleArg                         || rst
        [Rules.display, Rules.required] || null
        null || [Rules.display]
        [Rules.display] || [Rules.display]
    }
}
