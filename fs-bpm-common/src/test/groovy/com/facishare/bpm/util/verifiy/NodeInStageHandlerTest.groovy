package com.facishare.bpm.util.verifiy

import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.util.verifiy.handler.NodeInStageHandler
import com.fxiaoke.i18n.SupportLanguage
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Localization
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.i18n.util.LangIndex
import spock.lang.Specification

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2019/1/11 10:15 AM
 */
class NodeInStageHandlerTest extends Specification {

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
        stub.get(_ as String, _ as Long) >> {
            String key, Long tenantId ->
                return new Localization();
        }
        LangIndex.getInstance().supportLanguageList = [new SupportLanguage()]
        LangIndex.getInstance().languageCodeMapping = [:]
    }

    def "泳池中没有设置activities"() {
        given:
        initI18nClient()
        Workflow workflow = new Workflow();
        workflow.activities = [new ActivityExt(id: "jiedian", name: "节点1")]
        workflow.extension = new Workflow.WorkflowExtension(pools: [new Workflow.PoolEntity(
                id: "yongchi",
                name: "泳池",
                lanes: [new Workflow.LaneEntity(id: "yongdao", name: "泳道", activities: [])]
        )])

        ValidateHandler validate = new NodeInStageHandler();

        when: "verify workflow pre nodes"
        validate.validate(workflow)

        then:
        Exception e = thrown()
        e.printStackTrace()
        e instanceof BPMWorkflowDefVerifyException
        e.getMessage() == ""

    }

}
