package com.facishare.bpm.util.verifiy

import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.util.verifiy.handler.NodeTransitionHandler
import com.facishare.rest.core.util.JsonUtil
import com.fxiaoke.i18n.SupportLanguage
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Localization
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.i18n.util.LangIndex
import spock.lang.Specification


/**
 * Created by <PERSON> on 09/06/2017.
 */
class NodeTransitionHandlerTest extends Specification {

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
        stub.get(_ as String, _ as Long) >> {
            String key, Long tenantId ->
                return new Localization();
        }
        LangIndex.getInstance().supportLanguageList = [new SupportLanguage()]
        LangIndex.getInstance().languageCodeMapping = [:]
    }

    def "默认分支测试"() {
        given: "设置一个线上问题工作流"
        initI18nClient()
        def workflowJson = "{\"type\":\"workflow_bpm\",\"name\":\"万盈农业地块作业流程\",\"description\":\"万盈农业地块作业流程。\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"*************\"},{\"type\":\"userTask\",\"reminders\":[{\"remindStrategy\":1,\"remindContent\":\"的任务剩余处理时间还有小时，请尽快处理。\",\"remindTime\":-1}],\"bpmExtension\":{\"actionCode\":\"\",\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"AccountObj\",\"entityName\":\"客户\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"},\"form\":[[{\"name\":\"UDSText1__c\",\"label\":\"农户姓名\",\"type\":\"text\",\"required\":true},{\"name\":\"address\",\"label\":\"详细地址\",\"type\":\"text\",\"required\":true},{\"name\":\"location\",\"label\":\"定位\",\"type\":\"location\",\"required\":true},{\"name\":\"UDSText4__c\",\"label\":\"垄数\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText5__c\",\"label\":\"垄长\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText3__c\",\"label\":\"地块面积（㎡）\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText7__c\",\"label\":\"土地级别\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText8__c\",\"label\":\"土壤性质\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText9__c\",\"label\":\"主要杂草\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText6__c\",\"label\":\"垄宽\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText11__c\",\"label\":\"地上害虫\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText12__c\",\"label\":\"地下害虫\",\"type\":\"text\",\"required\":true},{\"name\":\"UDSText10__c\",\"label\":\"近年病害\",\"type\":\"text\",\"required\":true}]]},\"remindLatency\":10,\"canSkip\":false,\"remind\":true,\"name\":\"地块调查\",\"description\":\"填写地块调查实情。\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1037\"]},\"taskType\":\"anyone\"},{\"type\":\"exclusiveGateway\",\"name\":\"整地判断\",\"description\":\"\",\"id\":\"*************\",\"defaultTransitionId\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"},\"actionCode\":\"changeowner\"},\"name\":\"秸秆处理\",\"description\":\"处理秸秆\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1037\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"},\"form\":[[{\"name\":\"UDSSel6__c\",\"label\":\"是否上传播种作业确认单\",\"type\":\"select_one\",\"required\":false}]]},\"name\":\"播种作业\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1038\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"actionCode\":\"changeowner\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"}},\"name\":\"深松OR旋耕\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1037\"]},\"taskType\":\"anyone\"},{\"type\":\"exclusiveGateway\",\"name\":\"质量检查\",\"description\":\"\",\"id\":\"*************\",\"defaultTransitionId\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"actionCode\":\"changeowner\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"}},\"name\":\"苗前除草\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"activity_*************##assigneeId\$前置业务处理人员-播种作业-节点处理人\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"actionCode\":\"changeowner\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"}},\"name\":\"苗后除草\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1\"]},\"taskType\":\"anyone\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"},\"form\":[[{\"name\":\"UDSSel7__c\",\"label\":\"防虫叶肥质量是否合格\",\"type\":\"select_one\",\"required\":false}]]},\"name\":\"防虫叶肥\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1\"]},\"taskType\":\"anyone\"},{\"type\":\"exclusiveGateway\",\"name\":\"质量检查\",\"description\":\"\",\"id\":\"*************\",\"defaultTransitionId\":\"*************\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"entityId\":\"AccountObj\",\"executionType\":\"operation\",\"actionCode\":\"changeowner\",\"objectId\":{\"expression\":\"activity_*************##AccountObj\"}},\"name\":\"收获\",\"description\":\"\",\"id\":\"*************\",\"assignee\":{\"person\":[\"1\"]},\"taskType\":\"anyone\"},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"*************\"}],\"transitions\":[{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"description\":\"地块调查/客户-是否需要处理秸秆等于不需要且<br>地块调查/客户-是否需要深松OR旋耕等于不需要\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel3__c\"},\"right\":{\"value\":\"2\",\"type\":{\"name\":\"text\"}}},{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel4__c\"},\"right\":{\"value\":\"2\",\"type\":{\"name\":\"text\"}}}]}]}},{\"description\":\"地块调查/客户-是否需要处理秸秆等于需要\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel3__c\"},\"right\":{\"value\":\"1\",\"type\":{\"name\":\"text\"}}}]}]}},{\"description\":\"地块调查/客户-是否需要深松OR旋耕等于需要\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel4__c\"},\"right\":{\"value\":\"1\",\"type\":{\"name\":\"text\"}}}]}]}},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"description\":\"播种作业/客户-播种质量等于合格\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel5__c\"},\"right\":{\"value\":\"1\",\"type\":{\"name\":\"text\"}}}]}]}},{\"description\":\"播种作业/客户-播种质量等于不合格\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel5__c\"},\"right\":{\"value\":\"2\",\"type\":{\"name\":\"text\"}}}]}]}},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"},{\"description\":\"防虫叶肥/客户->防虫叶肥质量是否合格等于合格\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel7__c\"},\"right\":{\"value\":\"1\",\"type\":{\"name\":\"text\"}}}]}]}},{\"description\":\"防虫叶肥/客户-防虫叶肥质量是否合格等于不合格\",\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_*************##AccountObj##UDSSel7__c\"},\"right\":{\"value\":\"2\",\"type\":{\"name\":\"text\"}}}]}]}},{\"id\":\"*************\",\"fromId\":\"*************\",\"toId\":\"*************\"}],\"variables\":[{\"id\":\"activity_0##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj##UDSSel3__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj##UDSSel4__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj##UDSSel5__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj##UDSSel7__c\",\"type\":{\"name\":\"text\"}}],\"sourceWorkflowId\":\"258022090062266368\"}";
        def executableWorkflow =JsonUtil.fromJson(workflowJson, ExecutableWorkflowExt.class)
        when: "校验"
        NodeTransitionHandler.verifyDefaultTransition(executableWorkflow)
        then: "结果"
        BPMWorkflowDefVerifyException e = thrown()
        e.message.contains("默认") || e.message.contains("分支") || e.message.contains("连线")
    }

}