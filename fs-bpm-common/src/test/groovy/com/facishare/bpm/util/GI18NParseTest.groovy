package com.facishare.bpm.util


import com.facishare.bpm.utils.bean.BeanUtils
import com.facishare.bpm.utils.i18n.I18NAlias
import com.facishare.bpm.utils.i18n.I18NAliases
import com.facishare.bpm.utils.i18n.I18NExpression
import com.facishare.bpm.utils.i18n.I18NParseContext
import com.facishare.paas.I18N
import com.fxiaoke.i18n.client.I18nClient
import com.google.common.base.Joiner
import com.google.common.collect.Lists
import lombok.Data
import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification
import spock.lang.Unroll

/**
 * @Description : 
 * <AUTHOR> cuiyongxu
 * @Date : 2022/1/5-2:07 下午
 * */
class GI18NParseTest extends Specification {


    public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";


    @Unroll
    def "object #desc"() {
        setup:
        I18N.setContext(tenantId, language)
        I18nClient.getInstance().realTime(true)
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW)

        expect:
        I18NParser.parse(tenantId, object)

        boolean flag = true
        fields.forEach({ it ->
            it

            def objectValue = object.getProperty(it)
            def resultValue = rst.getProperty(it)

            if ((objectValue == null || resultValue == null) && (objectValue == resultValue)) {
                flag = true
            } else {

                if ((objectValue == null || resultValue == null) && (objectValue != resultValue)) {
                    flag = false
                } else {
                    if (objectValue instanceof Collection && resultValue instanceof Collection) {
                        flag = flag && (listFieldToString(objectValue) == listFieldToString(resultValue))
                    } else {
                        flag = flag && (object.getProperty(it) == rst.getProperty(it))
                    }
                }
            }
            if (!flag) {
                println(tenantId + "_" + desc + "_" + language)
                return
            }
        })
        flag

        where:
        desc                           | tenantId | language | object                                                                                                                                                     | fields           || rst
        //"英文,国际化平台英文已设置"                | "71557"  | "en"     | new Example(workflowId: "61c99ddb03b840464e2e70d0")                                                                                                        | ["name"]         || put(object, [name: "flow name 1"])
        "中文,国际化平台未设置"                  | "71554"  | "cn"     | new Example(workflowId: "61c99cea03b840464e2e70b0")                                                                                                        | ["name"]         || new Example(workflowId: "61c99cea03b840464e2e70b0")
        "未知语言,国际化平台中文已设置,activityId为空" | "71557"  | ""       | new Example(workflowId: "61c9a2f703b840464e2e710e")                                                                                                        | ["activityName"] || new Example(workflowId: "61c9a2f703b840464e2e710e")
        "未知语言,国际化平台中文已设置,workflowId为空" | "71557"  | ""       | new Example(activityId: "61c9a2f703b840464e2e710e")                                                                                                        | ["activityName"] || new Example(workflowId: "61c9a2f703b840464e2e710e")
        //"全语言,国际化平台中文已设置"               | "71557"  | ""       | new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")                                                                           | ["longText"]     || put(object, [longText: "哈哈哈"])
        //"全语言,国际化平台英文已设置"               | "71557"  | "en"     | new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")                                                                           | ["longText"]     || put(object, [longText: "哈哈哈英文"])
        //"全语言,国际化平台繁体已设置"               | "71557"  | "zh_tw"  | new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")                                                                           | ["longText"]     || put(object, [longText: "哈哈哈繁体"])
        //"英文,国际化平台英文已设置-列表下钻"           | "71557"  | "en"     | new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))) | ["actions"]      || put(object, [actions: Lists.newArrayList(new ActionCode(code: "Update", label: "save3"))])
        "中文,国际化平台未设置-列表下钻"             | "71557"  | "cn"     | new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))) | ["actions"]      || new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写")))
    }

    @Unroll
    def "object count #desc"() {
        setup:
        I18N.setContext(tenantId, language)
        I18nClient.getInstance().realTime(true)
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW)

        expect:

        I18NParseContext context = I18NParseContext.of()
        I18NParser.parse(tenantId, object, context)

        context.getCount() == rst

        where:
        desc                           | tenantId | language | object                                                                                                                                                     || rst
        "英文,国际化平台英文已设置"                | "71557"  | "en"     | new Example(workflowId: "61c99ddb03b840464e2e70d0")                                                                                                        || 1
        "中文,国际化平台未设置"                  | "71554"  | "cn"     | new Example(workflowId: "61c99cea03b840464e2e70b0")                                                                                                        || 1
        "未知语言,国际化平台中文已设置,activityId为空" | "71557"  | ""       | new Example(workflowId: "61c9a2f703b840464e2e710e")                                                                                                        || 1
        "未知语言,国际化平台中文已设置,workflowId为空" | "71557"  | ""       | new Example(activityId: "61c9a2f703b840464e2e710e")                                                                                                        || 1
        "全语言,国际化平台中文已设置"               | "71557"  | ""       | new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")                                                                           || 1
        "全语言,国际化平台英文已设置"               | "71557"  | "en"     | new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")                                                                           || 1
        "全语言,国际化平台繁体已设置"               | "71557"  | "zh_tw"  | new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")                                                                           || 1
        "英文,国际化平台英文已设置-列表下钻"           | "71557"  | "en"     | new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))) || 1
        "中文,国际化平台未设置-列表下钻"             | "71557"  | "cn"     | new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))) || 1
    }


    //@Unroll
    /*def "collection #desc"() {
        setup:
        I18N.setContext(tenantId, language)
        I18nClient.getInstance().realTime(true)
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW)

        expect:
        I18NParser.parse(tenantId, object)


        StringBuffer objectString = new StringBuffer()
        StringBuffer rstString = new StringBuffer()


        boolean flag = true
        if ((object == null && rst == null)) {
            flag = true
        } else {
            object.forEach({ item ->
                item
                fields.forEach({ field ->
                    field
                    Object objectFieldValue = item.getProperty(field)
                    if (objectFieldValue != null) {
                        if (objectFieldValue instanceof Collection) {
                            objectString.append(listFieldToString(objectFieldValue))
                        } else {
                            objectString.append(objectFieldValue)
                        }
                    } else {
                        objectString.append(null)
                    }
                })
            })


            rst.forEach({ item ->
                item
                fields.forEach({ field ->
                    field
                    Object rstFieldValue = item.getProperty(field)
                    if (rstFieldValue != null) {
                        if (rstFieldValue instanceof Collection) {
                            rstString.append(listFieldToString(rstFieldValue))
                        } else {
                            rstString.append(rstFieldValue)
                        }
                    } else {
                        rstString.append(null)
                    }
                })
            })
//            println objectString+":"+rstString
            flag = flag && (objectString.toString().equals(rstString.toString()))
        }

        flag

        where:
        desc                           | tenantId | language | object                                                                                                                                                                         | fields           || rst
        "英文,国际化平台未设置"                  | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))                                                                                                        | ["name"]         || Lists.newArrayList(new Example(name: "flow name 1"))
        "英文,国际化平台未设置"                  | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "333"))                                                                                                                             | ["name"]         || Lists.newArrayList(new Example(workflowId: "333"))
        "未知语言,国际化平台未设置"                | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))                                                                                                        | ["name"]         || Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))
        "未知语言,国际化平台中文已设置"              | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           | ["activityName"] || Lists.newArrayList(new Example(activityName: "中文_任务333"))
        "未知语言,国际化平台中文已设置,activityId为空" | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e"))                                                                                                        | ["activityName"] || Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))
        "未知语言,国际化平台中文已设置,workflowId为空" | "71557"  | ""       | Lists.newArrayList(new Example(activityId: "1640599268277"))                                                                                                                   | ["activityName"] || Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))
        "英文,国家化平台英文已设置-列表下钻"           | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写")))) | ["actions"]      || Lists.newArrayList(new Example(actions: Lists.newArrayList(new ActionCode(code: "Update", label: "save3"))))
        "英文,国家化平台英文已设置-多列表下钻"          | "71557"  | "en"     | Lists.newArrayList(
                new Example(workflowId: "61c99ddb03b840464e2e70d0",
                        activityId: "1640599268277",
                        actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))),//flow.61c99ddb03b840464e2e70d0.1640599268277.Update
                new Example(workflowId: "61c9a2f703b840464e2e710e",
                        activityId: "1640599268273",//flow.61c9a2f703b840464e2e710e.1640599268273.updateAndComplete
                        actions: Lists.newArrayList(new ActionCode(code: "UpdateAndComplete", label: "保存并完成"))))                                                                                                                              | ["actions"]      || Lists.newArrayList(new Example(actions: Lists.newArrayList(new ActionCode(code: "Update", label: "save3"))), new Example(actions: Lists.newArrayList(new ActionCode(code: "UpdateAndComplete", label: "4"))))
        "全语言,国际化平台中文已设置"               | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           | ["longText"]     || Lists.newArrayList(new Example(longText: "哈哈哈"))
        "全语言,国际化平台英文已设置"               | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           | ["longText"]     || Lists.newArrayList(new Example(longText: "哈哈哈英文"))
        "全语言,国际化平台繁体已设置"               | "71557"  | "zh_tw"  | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           | ["longText"]     || Lists.newArrayList(new Example(longText: "哈哈哈繁体"))
        "三级嵌套"                         | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277", groups: [new Group(id: "111", name: "333")]))                             |["name"]||Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277", groups: [new Group(id: "111", name: "333")]))
    }*/


    @Unroll
    def "collection count  #desc"() {
        setup:
        I18N.setContext(tenantId, language)
        I18nClient.getInstance().realTime(true)
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW)

        expect:
        I18NParseContext context = I18NParseContext.of()
        I18NParser.parse(tenantId, object, context)

        context.getCount() == rst

        where:
        desc                           | tenantId | language | object                                                                                                                                                                         || rst
        "英文,国际化平台未设置"                  | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))                                                                                                        || 1
        "英文,国际化平台未设置"                  | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "333"))                                                                                                                             || 1
        "未知语言,国际化平台未设置"                | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))                                                                                                        || 1
        "未知语言,国际化平台中文已设置"              | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           || 1
        "未知语言,国际化平台中文已设置,activityId为空" | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e"))                                                                                                        || 1
        "未知语言,国际化平台中文已设置,workflowId为空" | "71557"  | ""       | Lists.newArrayList(new Example(activityId: "1640599268277"))                                                                                                                   || 1
        "英文,国家化平台英文已设置-列表下钻"           | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写")))) || 1
        "英文,国家化平台英文已设置-列表下钻(1次下钻)=50条" | "71557"  | "en"     | getDepthFieldList(50)                                                                                                                                                          || 50
        "英文,国家化平台英文已设置-列表下钻(2次下钻)=50条" | "71557"  | "en"     | getSecondDepthFieldList(50)                                                                                                                                                    || 50
        "全语言,国际化平台中文已设置"               | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           || 1
        "全语言,国际化平台英文已设置"               | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           || 1
        "全语言,国际化平台繁体已设置"               | "71557"  | "zh_tw"  | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277"))                                                                           || 1
    }


    @Unroll
    def "depth  #desc"() {
        setup:
        I18N.setContext(tenantId, language)
        I18nClient.getInstance().realTime(true)
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW)

        expect:
        I18NParseContext context = I18NParseContext.of()
        I18NParser.parse(tenantId, object, context)

        context.getDepth() == rst

        where:
        desc                           | tenantId | language | object                                                                                               || rst
        "英文,国际化平台未设置"                  | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))                              || 0
        "英文,国际化平台未设置"                  | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "333"))                                                   || 0
        "未知语言,国际化平台未设置"                | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0"))                              || 0
        "未知语言,国际化平台中文已设置"              | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")) || 0
        "未知语言,国际化平台中文已设置,activityId为空" | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e"))                              || 0
        "未知语言,国际化平台中文已设置,workflowId为空" | "71557"  | ""       | Lists.newArrayList(new Example(activityId: "1640599268277"))                                         || 0
//        "英文,国家化平台英文已设置-列表下钻"              | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写")))) || 1
//        "??英文,国家化平台英文已设置-列表下钻(1个字段下钻)=50条,如果max=50,则只解析了前25条和前25条的一个button"  | "71557"  | "en"     | getDepthFieldList(50)                                                                                                                                                          || 50
//        "??英文,国家化平台英文已设置-列表下钻(2个字段下钻)=50条,如果max=50,((当前对象+2字段)*50=150)/2=75,只解析了前"  | "71557"  | "en"     | getSecondDepthFieldList(50)                                                                                                                                                    || 75
        "全语言,国际化平台中文已设置"               | "71557"  | ""       | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")) || 0
        "全语言,国际化平台英文已设置"               | "71557"  | "en"     | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")) || 0
        "全语言,国际化平台繁体已设置"               | "71557"  | "zh_tw"  | Lists.newArrayList(new Example(workflowId: "61c9a2f703b840464e2e710e", activityId: "1640599268277")) || 0
    }

    def put(Example object, Map<String, Object> values) {
        Example clone = BeanUtils.transfer(object, Example.class);
        values.keySet().forEach({ it ->
            it
            clone.setProperty(it, values.get(it))
        })
        return clone
    }

    def listToString(list) {
        if (CollectionUtils.isNotEmpty(list)) {
            Collections.sort(list)
            return Joiner.on("-").join(list);
        }
        return null;
    }

    def listFieldToString(list) {
        if (CollectionUtils.isNotEmpty(list)) {
            Collections.sort(list)
            StringBuffer stringBuffer = new StringBuffer()
            list.forEach({
                it ->
                    it
                    if (it instanceof ActionCode) {
                        ActionCode actionCode = (ActionCode) it
                        stringBuffer.append(actionCode.getCode()).append(actionCode.getLabel())
                    } else {
                        stringBuffer.append(it)
                    }
            })
            return stringBuffer.toString()
        }
        return null;
    }

    /**
     * 存在一次下钻的列表
     * @param integer
     * @return
     */
    List<Example> getSecondDepthFieldList(int integer) {
        List<Example> dataList = []
        for (i in 0..<integer) {
            dataList.add(new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写")), action2s: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))))
        }
        return dataList
    }

    /**
     * 存在两次下钻的列表
     * @param integer
     * @return
     */
    List<Example> getDepthFieldList(int integer) {
        List<Example> dataList = []
        for (i in 0..<integer) {
            dataList.add(new Example(workflowId: "61c99ddb03b840464e2e70d0", activityId: "1640599268277", actions: Lists.newArrayList(new ActionCode(code: "Update", label: "填写"))))
        }
        return dataList
    }
}

@Data
class Example {

    private String workflowId;
    @I18NExpression(relation = "\${workflowId}.name")
    private String name;

    private String activityId;

    @I18NExpression(relation = "\${workflowId.activityId}.name")
    private String activityName;

    @I18NExpression(relation = "\${workflowId.activityId}.test.name")
    private String longText;

    @I18NExpression(relation = "\${workflowId.activityId.longCode}.name")
    private String longCode;

    @I18NExpression(drill = true)
    private Model model;

    @I18NExpression(drill = true)
    private List<ActionCode> actions;

    @I18NExpression(drill = true)
    private List<Group> groups;

    @I18NExpression(drill = true)
    private List<ActionCode> action2s;

    @I18NExpression(drill = true)
    private List<Model> models;

    @I18NExpression(drill = true)
    private List items;


    @I18NExpression(drill = true)
    private Map<String, Model> modelMaps;
}

@Data
class Group {
    String id
    @I18NExpression(relation = "\${workflowId.activityId.id}.name")
    String name;
}

class ActionCode {
    @I18NAliases(value = [
            @I18NAlias(value = "Update", alias = "update"),
            @I18NAlias(value = "UpdateAndComplete", alias = "updateAndComplete")
    ])
    private String code;
    @I18NExpression(relation = "\${workflowId.activityId.code}")
    private String label;

    String getCode() {
        return code
    }

    void setCode(String code) {
        this.code = code
    }

    String getLabel() {
        return label
    }

    void setLabel(String label) {
        this.label = label
    }
}
