package com.facishare.bpm.util;

import com.facishare.bpm.utils.NumberFormatUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 6.2
 */
public class BPMNumberUtilsTest {
    @Test
    public void testGetInteger() {
        Assert.assertEquals(2, NumberFormatUtil.instance.getIntegerByString("2").intValue());
        Assert.assertEquals(2, NumberFormatUtil.instance.getIntegerByString("2.0").intValue());
        Assert.assertEquals(2, NumberFormatUtil.instance.getIntegerByString("2.2").intValue());
        Assert.assertNull(NumberFormatUtil.instance.getIntegerByString(""));
        Assert.assertNull(NumberFormatUtil.instance.getIntegerByString(null));
    }

    @Test
    public void testGetLong() {
        Assert.assertEquals(2L, NumberFormatUtil.instance.getLongByString("2").longValue());
        Assert.assertEquals(2L, NumberFormatUtil.instance.getLongByString("2.0").longValue());
        Assert.assertEquals(2L, NumberFormatUtil.instance.getLongByString("2.2").longValue());
        Assert.assertNull(NumberFormatUtil.instance.getLongByString(""));
        Assert.assertNull(NumberFormatUtil.instance.getLongByString(null));
    }


}
