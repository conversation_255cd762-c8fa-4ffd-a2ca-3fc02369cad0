package com.facishare.bpm.util

import com.facishare.bpm.util.verifiy.handler.bean.BPMFieldBean
import spock.lang.Specification

/**
 * BPMFieldBean 测试
 */
class BPMFieldBeanTest extends Specification {

    def "测试BPMFieldBean基本功能"() {
        given:
        def fieldBean = new BPMFieldBean()

        when:
        fieldBean.setEntityId("AccountObj")
        fieldBean.setMainField("name")
        fieldBean.setLookupField("owner")
        fieldBean.setActivityIdFull("activity_123")
        fieldBean.setActivityId("123")
        fieldBean.setSelfRef(true)

        then:
        fieldBean.getEntityId() == "AccountObj"
        fieldBean.getMainField() == "name"
        fieldBean.getLookupField() == "owner"
        fieldBean.getActivityIdFull() == "activity_123"
        fieldBean.getActivityId() == "123"
        fieldBean.isSelfRef() == true
    }

    def "测试BPMFieldBean默认值"() {
        given:
        def fieldBean = new BPMFieldBean()

        expect:
        fieldBean.getEntityId() == null
        fieldBean.getMainField() == null
        fieldBean.getLookupField() == null
        fieldBean.getActivityIdFull() == null
        fieldBean.getActivityId() == null
        fieldBean.isSelfRef() == false
    }

    def "测试BPMFieldBean create方法"() {
        when:
        def fieldBean = BPMFieldBean.create("AccountObj", "name", "owner", "activity_123", "123", true)

        then:
        fieldBean.getEntityId() == "AccountObj"
        fieldBean.getMainField() == "name"
        fieldBean.getLookupField() == "owner"
        fieldBean.getActivityIdFull() == "activity_123"
        fieldBean.getActivityId() == "123"
        fieldBean.isSelfRef() == true
    }

    def "测试BPMFieldBean toString方法"() {
        given:
        def fieldBean = BPMFieldBean.create("AccountObj", "name", "owner", "activity_123", "123", true)

        when:
        def result = fieldBean.toString()

        then:
        result.contains("AccountObj")
        result.contains("name")
        result.contains("owner")
    }

    def "测试BPMFieldBean equals和hashCode"() {
        given:
        def fieldBean1 = BPMFieldBean.create("AccountObj", "name", "owner", "activity_123", "123", true)
        def fieldBean2 = BPMFieldBean.create("AccountObj", "name", "owner", "activity_123", "123", true)
        def fieldBean3 = BPMFieldBean.create("SalesOrderObj", "name", "owner", "activity_123", "123", true)

        expect:
        fieldBean1.equals(fieldBean2)
        !fieldBean1.equals(fieldBean3)
        fieldBean1.hashCode() == fieldBean2.hashCode()
    }
}
