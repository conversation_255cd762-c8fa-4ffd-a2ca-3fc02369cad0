package com.facishare.bpm.util

import com.facishare.bpm.utils.model.Pair
import spock.lang.Specification


/**
 * Created by <PERSON> on 20/04/2017.
 */
class PairTest extends Specification{

    def "Pair Equal"() {
        when:"设置数值"
        def a = new Pair<>("one", "two")
        def b = new Pair<>("one", "two")
        def c = new Pair("one", "t")
        then:"进行校验"
        b == a
        b != c

    }
}
