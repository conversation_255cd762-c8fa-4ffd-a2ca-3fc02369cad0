package com.facishare.bpm.util

import com.facishare.bpm.utils.model.Pair
import spock.lang.Specification


/**
 * Created by <PERSON> on 20/04/2017.
 */
class PairTest extends Specification{

    def "Pair Equal"() {
        when:"设置数值"
        def a = new Pair<>("one", "two")
        def b = new Pair<>("one", "two")
        def c = new Pair("one", "t")
        then:"进行校验"
        b == a
        b != c
    }

    def "Pair构造函数测试"() {
        when:"测试不同构造函数"
        def pair1 = new Pair<String, String>(null, null)
        def pair2 = new Pair<>("key", "value")

        then:"验证初始值"
        pair1.getKey() == null
        pair1.getValue() == null
        pair2.getKey() == "key"
        pair2.getValue() == "value"
    }

    def "Pair setter方法测试"() {
        given:"创建空Pair"
        def pair = new Pair<String, Integer>("", 0)

        when:"设置值"
        pair.setKey("testKey")
        pair.setValue(123)

        then:"验证设置结果"
        pair.getKey() == "testKey"
        pair.getValue() == 123
    }

    def "Pair null值处理测试"() {
        when:"创建包含null值的Pair"
        def pair1 = new Pair<>(null, null)
        def pair2 = new Pair<>(null, null)
        def pair3 = new Pair<>("key", null)
        def pair4 = new Pair<>(null, "value")

        then:"验证null值处理"
        pair1.getKey() == null
        pair1.getValue() == null
        pair1 == pair2
        pair1 != pair3
        pair1 != pair4
        pair3 != pair4
    }

    def "Pair hashCode测试"() {
        when:"创建相同和不同的Pair"
        def pair1 = new Pair<>("key", "value")
        def pair2 = new Pair<>("key", "value")
        def pair3 = new Pair<>("key", "different")
        def pair4 = new Pair<>(null, null)
        def pair5 = new Pair<>(null, null)

        then:"验证hashCode"
        pair1.hashCode() == pair2.hashCode()
        pair1.hashCode() != pair3.hashCode()
        pair4.hashCode() == pair5.hashCode()
    }

    def "Pair toString测试"() {
        when:"创建不同类型的Pair"
        def pair1 = new Pair<>("key", "value")
        def pair2 = new Pair<>(123, 456)
        def pair3 = new Pair<>(null, null)

        then:"验证toString输出"
        pair1.toString() != null
        pair1.toString().length() > 0
        pair2.toString() != null
        pair2.toString().length() > 0
        pair3.toString() != null
    }

    def "Pair边界情况测试"() {
        when:"测试边界情况"
        def pair1 = new Pair<>("", "")
        def pair2 = new Pair<>("", "")
        def pair3 = new Pair<>(0, 0)
        def pair4 = new Pair<>(false, false)

        then:"验证边界情况"
        pair1.getKey() == ""
        pair1.getValue() == ""
        pair1 == pair2
        pair3.getKey() == 0
        pair3.getValue() == 0
        pair4.getKey() == false
        pair4.getValue() == false
    }

    def "Pair equals方法详细测试"() {
        given:"创建测试Pair"
        def pair = new Pair<>("key", "value")

        expect:"验证equals方法的各种情况"
        pair.equals(pair) == true
        pair.equals(null) == false
        pair.equals("not a pair") == false
        pair.equals(new Pair<>("key", "value")) == true
        pair.equals(new Pair<>("different", "value")) == false
        pair.equals(new Pair<>("key", "different")) == false
    }

    def "Pair不同类型测试"() {
        when:"创建不同类型的Pair"
        def stringPair = new Pair<String, String>("str1", "str2")
        def intPair = new Pair<Integer, Integer>(1, 2)
        def mixedPair = new Pair<String, Integer>("key", 123)
        def listPair = new Pair<String, List<String>>("list", ["a", "b"])

        then:"验证不同类型"
        stringPair.getKey() instanceof String
        stringPair.getValue() instanceof String
        intPair.getKey() instanceof Integer
        intPair.getValue() instanceof Integer
        mixedPair.getKey() instanceof String
        mixedPair.getValue() instanceof Integer
        listPair.getValue() instanceof List
    }
}
