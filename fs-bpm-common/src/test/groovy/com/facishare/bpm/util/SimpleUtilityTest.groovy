package com.facishare.bpm.util

import com.facishare.bpm.util.verifiy.handler.bean.BPMFieldBean
import spock.lang.Specification

/**
 * 简单工具类测试
 */
class SimpleUtilityTest extends Specification {

    def "测试BPMFieldBean MainObjOrField内部类"() {
        when:
        def mainObjOrField = new BPMFieldBean.MainObjOrField()
        
        then:
        mainObjOrField != null
        mainObjOrField.getEntityId() == null
        mainObjOrField.getField() == null
    }

    def "测试BPMFieldBean MainObjOrField设置值"() {
        given:
        def mainObjOrField = new BPMFieldBean.MainObjOrField()
        
        when:
        mainObjOrField.setEntityId("TestEntity")
        mainObjOrField.setField("testField")
        
        then:
        mainObjOrField.getEntityId() == "TestEntity"
        mainObjOrField.getField() == "testField"
    }

    def "测试BPMFieldBean MainObjOrField边界情况"() {
        given:
        def mainObjOrField = new BPMFieldBean.MainObjOrField()
        
        when:
        mainObjOrField.setEntityId("")
        mainObjOrField.setField("")
        
        then:
        mainObjOrField.getEntityId() == ""
        mainObjOrField.getField() == ""
    }

    def "测试BPMFieldBean MainObjOrField null值"() {
        given:
        def mainObjOrField = new BPMFieldBean.MainObjOrField()
        
        when:
        mainObjOrField.setEntityId(null)
        mainObjOrField.setField(null)
        
        then:
        mainObjOrField.getEntityId() == null
        mainObjOrField.getField() == null
    }

    def "测试BPMFieldBean toString方法"() {
        given:
        def bean = BPMFieldBean.create("TestEntity", "testField", "testLookup", "activity123", "123", false)
        
        when:
        def result = bean.toString()
        
        then:
        result != null
        result instanceof String
    }

    def "测试BPMFieldBean不同参数组合"() {
        when:
        def bean1 = BPMFieldBean.create("E1", "f1", "l1", "a1", "1", true)
        def bean2 = BPMFieldBean.create("E2", "f2", "l2", "a2", "2", false)
        def bean3 = BPMFieldBean.create("", "", "", "", "", true)
        
        then:
        bean1.getEntityId() == "E1"
        bean1.getMainField() == "f1"
        bean1.getLookupField() == "l1"
        bean1.isSelfRef() == true
        
        bean2.getEntityId() == "E2"
        bean2.getMainField() == "f2"
        bean2.getLookupField() == "l2"
        bean2.isSelfRef() == false
        
        bean3.getEntityId() == ""
        bean3.getMainField() == ""
        bean3.getLookupField() == ""
        bean3.isSelfRef() == true
    }

    def "测试BPMFieldBean create方法边界情况"() {
        when:
        def bean1 = BPMFieldBean.create(null, null, null, null, null, true)
        def bean2 = BPMFieldBean.create("", "", "", "", "", false)
        
        then:
        bean1.getEntityId() == null
        bean1.getMainField() == null
        bean1.getLookupField() == null
        bean1.getActivityIdFull() == null
        bean1.getActivityId() == null
        bean1.isSelfRef() == true
        
        bean2.getEntityId() == ""
        bean2.getMainField() == ""
        bean2.getLookupField() == ""
        bean2.getActivityIdFull() == ""
        bean2.getActivityId() == ""
        bean2.isSelfRef() == false
    }

    def "测试对象创建和基本操作"() {
        when:
        def bean = new BPMFieldBean()
        def mainObj = new BPMFieldBean.MainObjOrField()
        
        then:
        bean != null
        mainObj != null
        
        // 测试基本getter返回值
        bean.getEntityId() == null
        bean.getMainField() == null
        bean.getLookupField() == null
        bean.getActivityIdFull() == null
        bean.getActivityId() == null
        bean.isSelfRef() == false
        
        mainObj.getEntityId() == null
        mainObj.getField() == null
    }

    def "测试BPMFieldBean复杂操作"() {
        given:
        def bean = new BPMFieldBean()
        
        when:
        bean.setEntityId("ComplexEntity")
        bean.setMainField("complexField")
        bean.setLookupField("complexLookup")
        bean.setActivityIdFull("activity_complex_123")
        bean.setActivityId("complex_123")
        bean.setSelfRef(true)
        
        then:
        bean.getEntityId() == "ComplexEntity"
        bean.getMainField() == "complexField"
        bean.getLookupField() == "complexLookup"
        bean.getActivityIdFull() == "activity_complex_123"
        bean.getActivityId() == "complex_123"
        bean.isSelfRef() == true
    }

    def "测试BPMFieldBean修改操作"() {
        given:
        def bean = BPMFieldBean.create("Initial", "initial", "initial", "initial", "initial", false)
        
        when:
        bean.setEntityId("Modified")
        bean.setMainField("modified")
        bean.setLookupField("modified")
        bean.setSelfRef(true)
        
        then:
        bean.getEntityId() == "Modified"
        bean.getMainField() == "modified"
        bean.getLookupField() == "modified"
        bean.isSelfRef() == true
    }

    def "测试MainObjOrField复杂操作"() {
        given:
        def mainObj1 = new BPMFieldBean.MainObjOrField()
        def mainObj2 = new BPMFieldBean.MainObjOrField()
        
        when:
        mainObj1.setEntityId("Entity1")
        mainObj1.setField("field1")
        mainObj2.setEntityId("Entity2")
        mainObj2.setField("field2")
        
        then:
        mainObj1.getEntityId() == "Entity1"
        mainObj1.getField() == "field1"
        mainObj2.getEntityId() == "Entity2"
        mainObj2.getField() == "field2"
        mainObj1.getEntityId() != mainObj2.getEntityId()
        mainObj1.getField() != mainObj2.getField()
    }

    def "测试BPMFieldBean长字符串处理"() {
        given:
        def longString = "a" * 1000
        def bean = new BPMFieldBean()
        
        when:
        bean.setEntityId(longString)
        bean.setMainField(longString)
        bean.setLookupField(longString)
        
        then:
        bean.getEntityId() == longString
        bean.getMainField() == longString
        bean.getLookupField() == longString
        bean.getEntityId().length() == 1000
    }

    def "测试BPMFieldBean特殊字符处理"() {
        given:
        def specialChars = "!@#\$%^&*()_+-=[]{}|;':\",./<>?"
        def bean = new BPMFieldBean()

        when:
        bean.setEntityId(specialChars)
        bean.setMainField(specialChars)
        bean.setLookupField(specialChars)

        then:
        bean.getEntityId() == specialChars
        bean.getMainField() == specialChars
        bean.getLookupField() == specialChars
    }
}
