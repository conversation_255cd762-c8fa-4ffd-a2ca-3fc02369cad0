package com.facishare.bpm.util.verifiy

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import com.facishare.bpm.bpmn.ExecutionTaskExt
import com.facishare.bpm.exception.BPMBusinessException
import com.facishare.bpm.exception.BPMBusinessExceptionCode
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra
import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions
import com.facishare.bpm.model.resource.newmetadata.UpdateData
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch
import com.facishare.bpm.remote.model.org.Dept
import com.facishare.bpm.util.RefServiceManagerAbs
import com.facishare.bpm.util.verifiy.handler.BasicHandler
import com.facishare.bpm.util.verifiy.util.WorkflowGraph
import com.facishare.flow.element.plugin.api.FlowElement
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper
import com.facishare.rest.core.model.RemoteContext
import com.fxiaoke.i18n.SupportLanguage
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Localization
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.i18n.util.LangIndex
import com.google.common.collect.Maps
import spock.lang.Specification

import java.util.function.Function

/**
 * Created by Aaron on 16/06/2017.
 */
class VerifyTest extends Specification implements BaseValidateHandlerTest {

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
        stub.get(_ as String, _ as Long) >> {
            String key, Long tenantId ->
                return new Localization();
        }
        LangIndex.getInstance().supportLanguageList = [new SupportLanguage()]
        LangIndex.getInstance().languageCodeMapping = [:]
    }

    RefServiceManager getServiceRef() {
        new RefServiceManagerAbs() {
            static final Map<String, Map<String, Object>> cache = Maps.newConcurrentMap()
            static {
                def fields = [:]
                fields.put("lookup1", [type: "object_reference", target_api_name: "object0iOs7_other__c"])
                fields.put("field1", [label: "field1", type: "text", "is_active": true])
                fields.put("field2", [label: "field2", type: "text", "is_active": true])
                fields.put("field3", [label: "field3", type: "text", "is_active": false])
                cache.put("SalesOrderObj", ["fields": fields, "display_name": "主对象"])


                fields = [:]
                fields.put("lookupfield1", [label: "lookupfield1", type: "text", "is_active": true])
                fields.put("lookupfield2", [label: "lookupfield2", type: "text", "is_active": true])
                fields.put("lookupfield3", [label: "lookupfield3", type: "text", "is_active": true])
                cache.put("object0iOs7_other__c", ["fields": fields, "display_name": "被lookup对象"])


                def fields2 = [:]
                fields2.put("owner", [label: "owner", type: "text", "is_active": true])
                fields2.put("field_dsd3__c", [label: "field_dsd3__c", type: "text", "is_active": true])
                cache.put("object_eiei7__c", ["fields": fields2, "display_name": "被lookup对象"])
            }

            @Override
            Map<String, Object> getDescribe(String entityId) {
                def rst = cache.get(entityId)
                if (rst == null) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_DELETE_DISABLE,entityId)
                }
                return rst
            }

            @Override
            Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
                return null
            }

            @Override
            Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
                return null
            }

            @Override
            Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData, boolean skipRelevantTeam) {
                return null
            }

            @Override
            Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
                return null
            }

            @Override
            Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
                return null
            }

            @Override
            boolean isDataOwnerByQueryMetadata(String apiName, String objectId) {
                return false
            }

            @Override
            Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
                return null
            }

            @Override
            boolean workflowIsExists(String tenantId, String outLineId, String sourceWorkflowId) {
                return false
            }

            @Override
            Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
                return null
            }

            @Override
            boolean getDeliveryNoteEnable() {
                return false
            }

            @Override
            GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
                return null
            }

            @Override
            Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
                return null
            }

            @Override
            GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
                return null
            }

            @Override
            boolean dataPrivilege(String entityId, String objectId) {
                return false
            }

            @Override
            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }

            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck) {
                return null
            }

            @Override
            Map<String, String> getAppActions(String entityId, Boolean isExternalFlow, String appId, Integer appType) {
                return null
            }

            @Override
            GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
                return null
            }

            @Override
            Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
                return null
            }

            @Override
            def <E> E getObjectFromCache(String key, Function<String, E> function) {
                return null
            }

            @Override
            Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet) {
                return null
            }

            @Override
            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction) {
                return null
            }

            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName) {
                return null
            }

            @Override
            Map<String, String> findDataExhibitButton(String apiName, String objectId, String usePageType) {
                return null
            }

            @Override
            boolean checkEmailEnable(String sender) {
                return false
            }

            @Override
            FlowElementWrapper getFlowElementWrapper(String elementApiName) {
                return null
            }

            @Override
            FlowElement getFlowElement(String elementApiName) {
                return null
            }

            @Override
            String getI18nLinkAppName(String linkApp, String linkAppName) {
                return null
            }

            @Override
            boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
                return false
            }

            @Override
            Map<String, Object> getFlowConfig(String terminal, List<String> types) {
                return null
            }

            @Override
            boolean isNeedDiscussButton() {
                return false
            }

            @Override
            UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }
        }
    }

    def "只有一个条件分支报分支条件错误，但实例上没有错误"() {
        given:
        initI18nClient()
        def workflow = getWorkflowByJson(getServiceRef(),"测试用的123", "object_7prLH__c", "油焖大虾",
                getWorkflow("{\"creator\":\"1002\",\"externalFlow\":0,\"modifier\":\"1002\",\"entityId\":\"object_7prLH__c\",\"history\":false,\"singleInstanceFlow\":0,\"modifyTime\":*************,\"enable\":true,\"linkAppEnable\":false,\"name\":\"测试用的123\",\"description\":\"\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"1730277886154\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"object_7prLH__c\",\"entityName\":\"油焖大虾\",\"objectId\":{\"expression\":\"activity_0##object_7prLH__c\"},\"objectName\":\"流程发起数据/油焖大虾\",\"layoutType\":\"none\",\"defaultButtons\":{},\"outerTenantType\":\"outerTenantField\"},\"canSkip\":false,\"linkAppEnable\":false,\"name\":\"业务活动\",\"id\":\"17302778861541\",\"assignee\":{\"person\":[\"1007\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0,\"custom\":false,\"customCandidateConfig\":false,\"importObject\":false},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"1730277917441\"}],\"transitions\":[{\"id\":\"1730277886154111\",\"fromId\":\"1730277886154\",\"toId\":\"17302778861541\",\"serialNumber\":0},{\"id\":\"1730277922505\",\"fromId\":\"17302778861541\",\"toId\":\"1730277917441\",\"serialNumber\":0}],\"variables\":[{\"id\":\"activity_0##object_7prLH__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_17302778861541##object_7prLH__c\",\"type\":{\"name\":\"text\"}}],\"id\":\"6721f226d3bc5a0001e6b408\",\"sourceWorkflowId\":\"6721f226d3bc5a0001e6b407\",\"createTime\":1730277926455,\"tenantId\":\"71557\",\"appId\":\"BPM\",\"type\":\"workflow_bpm\"}"),
                null,
                "{\"id\":null,\"pools\":[{\"id\":null,\"name\":null,\"lanes\":[{\"id\":\"173027788615411\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"1730277886154\",\"17302778861541\",\"1730277917441\"],\"order\":1}]}],\"diagram\":[{\"id\":\"173027788615411\",\"attr\":{\"x\":80.0,\"y\":60.0,\"width\":220.0,\"height\":540.0}},{\"id\":\"1730277886154111\",\"attr\":{\"d\":\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277922505\",\"attr\":{\"d\":\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277886154\",\"attr\":{\"x\":160.0,\"y\":125.0}},{\"id\":\"1730277917441\",\"attr\":{\"x\":120.0,\"y\":430.0}},{\"id\":\"17302778861541\",\"attr\":{\"x\":110.0,\"y\":260.0}}],\"svg\":\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"100%\\\" height=\\\"100%\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" class=\\\"x6-graph-svg\\\" viewBox=\\\"80 60 220 540\\\"><defs style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><marker refX=\\\"-1\\\" id=\\\"marker-v54-2980955686\\\" overflow=\\\"visible\\\" orient=\\\"auto\\\" markerUnits=\\\"userSpaceOnUse\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path stroke=\\\"#545861\\\" fill=\\\"#545861\\\" transform=\\\"rotate(180)\\\" d=\\\"M 0 0 L 12 -4 L 12 4 Z\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></marker></defs><g class=\\\"x6-graph-svg-viewport\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-graph-svg-primer\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-graph-svg-stage\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g data-cell-id=\\\"173027788615411\\\" data-shape=\\\"lane\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(80,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" height=\\\"16\\\" width=\\\"220\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#dee1e8\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" x=\\\"1\\\" y=\\\"1\\\" stroke-opacity=\\\"0\\\" width=\\\"32\\\" height=\\\"16\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"start\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" width=\\\"270\\\" transform=\\\"matrix(1,0,0,1,35,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</tspan></text><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#181c25\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,15,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">1</tspan></text><rect fill=\\\"none\\\" stroke=\\\"#368dff\\\" stroke-width=\\\"1\\\" fill-opacity=\\\"0\\\" width=\\\"220\\\" height=\\\"540\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><image style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g data-cell-id=\\\"1730277886154111\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,189.78128051757812,234.9930419921875)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g data-cell-id=\\\"1730277922505\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,180,366)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><!--z-index:2--><g data-cell-id=\\\"1730277886154\\\" data-shape=\\\"startEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(160,125)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#16B4AB\\\" stroke=\\\"#16B4AB\\\" stroke-width=\\\"1\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"1730277917441\\\" data-shape=\\\"endEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(120,430)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#ffffff\\\" stroke=\\\"#737C8C\\\" stroke-width=\\\"4\\\" org-stroke=\\\"#737C8C\\\" fill-opacity=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><circle fill=\\\"#737C8C\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"25\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"17302778861541\\\" data-shape=\\\"business\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(110,260)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#368DFF\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" class=\\\"x6-hightlight-body\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"left\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" text=\\\"业务活动\\\" transform=\\\"matrix(1,0,0,1,37,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</tspan></text><image x=\\\"10\\\" y=\\\"15\\\" width=\\\"24\\\" height=\\\"24\\\" xlink:href=\\\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzI2ODI0NzAyNzQwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ3MjA3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik04OTYgMTI4YzIzLjU1MiAwIDQyLjY4OCAxOS4wNzIgNDIuNjg4IDQyLjY4OHY2ODIuNjI0QTQyLjY4OCA0Mi42ODggMCAwIDEgODk2IDg5NkgxMjhhNDIuNjg4IDQyLjY4OCAwIDAgMS00Mi42ODgtNDIuNjg4VjE3MC42ODhDODUuMzEyIDE0Ny4wNzIgMTA0LjQ0OCAxMjggMTI4IDEyOGg3Njh6IG0tNDIuNjg4IDg1LjMxMkgxNzAuNjg4djU5Ny4zNzZoNjgyLjYyNFYyMTMuMzEyek00MzIgMzIwYzYwLjk5MiAwIDkwLjg4IDQxLjY2NCA5MC44OCAxMDcuMjY0IDAgMzAuMDgtMTYuODk2IDU4LjI0LTM1LjEzNiA3OC4zMzYtNi4yMDggMTEuMi05LjkyIDIyLjQtOS45MiAzMi41MTIgMCA4LjcwNCA5Ljg1NiAxOC42ODggMjMuOTM2IDIxLjQ0bDEzLjE4NCAyLjQ5NiAyLjYyNCAwLjY0YzM4LjQgOS42IDgwLjQ0OCAyOC42MDggODguODk2IDU5LjUybDEuMzQ0IDcuMzYgMC4xOTIgNC4wOTZ2MTkuNTg0YTI4LjAzMiAyOC4wMzIgMCAwIDEtMjAuMjg4IDI3LjI2NGwtOC40NDggMi4zMDQtMjAuMjI0IDUuMDU2QzUyNS40NCA2OTUuNjggNDc4LjcyIDcwNCA0MzIgNzA0Yy00MS42IDAtODMuMDcyLTYuNTkyLTExNS4zOTItMTMuNTA0bC0yMi4zMzYtNS4xODRjLTYuNzItMS43MjgtMTIuOC0zLjMyOC0xNy45Mi00LjhhMjcuODQgMjcuODQgMCAwIDEtMTkuODQtMjEuOTUyTDI1NiA2NTMuMjQ4VjYzMy42YzAtMzQuODE2IDQxLjQwOC01Ny4wODggODEuNzkyLTY4LjczNmw4LjY0LTIuMzA0IDE1LjgwOC0zLjA3MmMxMy45NTItMi43NTIgMjMuOTM2LTExLjk2OCAyMy45MzYtMjEuNDRhNjkuMTIgNjkuMTIgMCAwIDAtOS45Mi0zMi40NDhjLTE4LjI0LTIwLjIyNC0zNS4yLTQ4LjM4NC0zNS4yLTc4LjRDMzQxLjEyIDM2MS42NjQgMzcxLjA3MiAzMjAgNDMyIDMyMHoiIGZpbGw9IiM3MzdDOEMiIHAtaWQ9IjQ3MjA4Ij48L3BhdGg+PC9zdmc+\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,70,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,140,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,70,56)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><!--z-index:3--></g><g class=\\\"x6-graph-svg-decorator\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tools x6-node-tools\\\" data-cell-id=\\\"173027788615411\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: nw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: ne-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: sw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: se-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g class=\\\"x6-graph-svg-overlay\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></svg>\",\"workflowId\":null}",
                0)
        when:
        VerifyManager.instance.execute(workflow)
        then:
        BPMBusinessException e = thrown()
    }

    def "发起人为系统时，校验其指定人员的合理性"() {
        given:
        initI18nClient()
        def basicHandler=new BasicHandler();
        when:
        basicHandler.validateApplicantWhenSystemUser(["person":["1","2"],"extUserType":["a"],"k":["a","b"]])
        then:
        BPMBusinessException e = thrown()
    }

    def "任务节点的问题"() {
        given:
        //ExecutableWorkflowExt workflow = getWorkflow("{\"sourceWorkflowId\":\"350350556483190784\",\"variables\":[{\"id\":\"activity_0##AccountObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_*************##AccountObj\",\"type\":{\"name\":\"text\"}}],\"singleInstanceFlow\":0,\"externalFlow\":0,\"activities\":[{\"name\":\"开始\",\"description\":\"\",\"id\":\"*************\",\"type\":\"startEvent\"},{\"reminders\":[{\"remindTitle\":\"业务流程超时提醒\",\"remindStrategy\":1,\"remindTime\":-1,\"remindTargets\":{\"ext_bpm\":[\"activity_*************##AccountObj##owner数据负责人\"]},\"remindContent\":\"{workflowName}的任务 {taskName} 剩余处理时间还有 {remindTime} 小时，请尽快处理。\"}],\"execution\":{\"pass\":[{\"taskType\":\"updates\",\"updateFieldJson\":\"[{\\\"isCalculate\\\":true,\\\"key\\\":\\\"{AccountObj.UDLookUp1__c.expected_income}\\\",\\\"value\\\":\\\"{AccountObj.total_refund_amount} \\\",\\\"defaultValue\\\":\\\"\\\",\\\"entityId\\\":\\\"activity_*************##AccountObj\\\"}]\"},{\"template\":\"5975bd7ea700109b82371099\",\"taskType\":\"send_email\",\"emailAddress\":[],\"sender\":\"<EMAIL>\",\"recipients\":{\"person\":[\"2000\",\"2019\"]},\"title\":\"测试模板01\"}]},\"bpmExtension\":{\"executionName\":\"操作对象\",\"entityName\":\"客户\",\"executionType\":\"operation\",\"entityId\":\"AccountObj\",\"actionCode\":\"changeowner\",\"objectId\":{\"expression\":\"activity_0##AccountObj\"}},\"remindLatency\":2,\"description\":\"\",\"rule\":{\"conditionPattern\":\"(0)\",\"conditions\":[{\"rowNo\":0,\"leftSide\":{\"fieldName\":\"activity_*************##AccountObj##UDMoney1__c\",\"fieldSrc\":\"activity_*************##AccountObj\",\"fieldType\":\"number\"},\"rightSide\":{\"value\":1000000},\"operator\":\"\\u003d\\u003d\"}]},\"canSkip\":false,\"type\":\"userTask\",\"remind\":true,\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0,\"name\":\"usertaskname\",\"id\":\"*************\",\"assignee\":{\"ext_bpm\":[\"activity_*************##AccountObj##owner数据相关人员-数据负责人\"]}},{\"name\":\"结束\",\"description\":\"\",\"id\":\"*************\",\"type\":\"endEvent\"}],\"name\":\"demo-test\",\"description\":\"\",\"id\":\"5addc3ba3db71dc4c6c76b77\",\"transitions\":[{\"toId\":\"*************\",\"serialNumber\":0,\"id\":\"*************\",\"fromId\":\"*************\"},{\"toId\":\"*************\",\"serialNumber\":1,\"id\":\"*************\",\"fromId\":\"*************\"}],\"type\":\"workflow_bpm\"}");
        initI18nClient()
        def workflow = getWorkflowByJson(getServiceRef(),"测试用的123", "object_7prLH__c", "油焖大虾",
                getWorkflow("{\"creator\":\"1002\",\"externalFlow\":0,\"modifier\":\"1002\",\"entityId\":\"object_7prLH__c\",\"history\":false,\"singleInstanceFlow\":0,\"modifyTime\":*************,\"enable\":true,\"linkAppEnable\":false,\"name\":\"测试用的123\",\"description\":\"\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"1730277886154\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"object_7prLH__c\",\"entityName\":\"油焖大虾\",\"objectId\":{\"expression\":\"activity_0##object_7prLH__c\"},\"objectName\":\"流程发起数据/油焖大虾\",\"layoutType\":\"none\",\"defaultButtons\":{},\"outerTenantType\":\"outerTenantField\"},\"canSkip\":false,\"linkAppEnable\":false,\"name\":\"业务活动\",\"id\":\"17302778861541\",\"assignee\":{\"person\":[\"1007\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0,\"custom\":false,\"customCandidateConfig\":false,\"importObject\":false},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"1730277917441\"}],\"transitions\":[{\"id\":\"1730277886154111\",\"fromId\":\"1730277886154\",\"toId\":\"17302778861541\",\"serialNumber\":0},{\"id\":\"1730277922505\",\"fromId\":\"17302778861541\",\"toId\":\"1730277917441\",\"serialNumber\":0}],\"variables\":[{\"id\":\"activity_0##object_7prLH__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_17302778861541##object_7prLH__c\",\"type\":{\"name\":\"text\"}}],\"id\":\"6721f226d3bc5a0001e6b408\",\"sourceWorkflowId\":\"6721f226d3bc5a0001e6b407\",\"createTime\":1730277926455,\"tenantId\":\"71557\",\"appId\":\"BPM\",\"type\":\"workflow_bpm\"}"),
                null,
                "{\"id\":null,\"pools\":[{\"id\":null,\"name\":null,\"lanes\":[{\"id\":\"173027788615411\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"1730277886154\",\"17302778861541\",\"1730277917441\"],\"order\":1}]}],\"diagram\":[{\"id\":\"173027788615411\",\"attr\":{\"x\":80.0,\"y\":60.0,\"width\":220.0,\"height\":540.0}},{\"id\":\"1730277886154111\",\"attr\":{\"d\":\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277922505\",\"attr\":{\"d\":\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277886154\",\"attr\":{\"x\":160.0,\"y\":125.0}},{\"id\":\"1730277917441\",\"attr\":{\"x\":120.0,\"y\":430.0}},{\"id\":\"17302778861541\",\"attr\":{\"x\":110.0,\"y\":260.0}}],\"svg\":\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"100%\\\" height=\\\"100%\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" class=\\\"x6-graph-svg\\\" viewBox=\\\"80 60 220 540\\\"><defs style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><marker refX=\\\"-1\\\" id=\\\"marker-v54-2980955686\\\" overflow=\\\"visible\\\" orient=\\\"auto\\\" markerUnits=\\\"userSpaceOnUse\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path stroke=\\\"#545861\\\" fill=\\\"#545861\\\" transform=\\\"rotate(180)\\\" d=\\\"M 0 0 L 12 -4 L 12 4 Z\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></marker></defs><g class=\\\"x6-graph-svg-viewport\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-graph-svg-primer\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-graph-svg-stage\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g data-cell-id=\\\"173027788615411\\\" data-shape=\\\"lane\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(80,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" height=\\\"16\\\" width=\\\"220\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#dee1e8\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" x=\\\"1\\\" y=\\\"1\\\" stroke-opacity=\\\"0\\\" width=\\\"32\\\" height=\\\"16\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"start\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" width=\\\"270\\\" transform=\\\"matrix(1,0,0,1,35,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</tspan></text><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#181c25\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,15,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">1</tspan></text><rect fill=\\\"none\\\" stroke=\\\"#368dff\\\" stroke-width=\\\"1\\\" fill-opacity=\\\"0\\\" width=\\\"220\\\" height=\\\"540\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><image style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g data-cell-id=\\\"1730277886154111\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,189.78128051757812,234.9930419921875)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g data-cell-id=\\\"1730277922505\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,180,366)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><!--z-index:2--><g data-cell-id=\\\"1730277886154\\\" data-shape=\\\"startEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(160,125)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#16B4AB\\\" stroke=\\\"#16B4AB\\\" stroke-width=\\\"1\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"1730277917441\\\" data-shape=\\\"endEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(120,430)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#ffffff\\\" stroke=\\\"#737C8C\\\" stroke-width=\\\"4\\\" org-stroke=\\\"#737C8C\\\" fill-opacity=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><circle fill=\\\"#737C8C\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"25\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"17302778861541\\\" data-shape=\\\"business\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(110,260)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#368DFF\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" class=\\\"x6-hightlight-body\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"left\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" text=\\\"业务活动\\\" transform=\\\"matrix(1,0,0,1,37,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</tspan></text><image x=\\\"10\\\" y=\\\"15\\\" width=\\\"24\\\" height=\\\"24\\\" xlink:href=\\\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzI2ODI0NzAyNzQwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ3MjA3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik04OTYgMTI4YzIzLjU1MiAwIDQyLjY4OCAxOS4wNzIgNDIuNjg4IDQyLjY4OHY2ODIuNjI0QTQyLjY4OCA0Mi42ODggMCAwIDEgODk2IDg5NkgxMjhhNDIuNjg4IDQyLjY4OCAwIDAgMS00Mi42ODgtNDIuNjg4VjE3MC42ODhDODUuMzEyIDE0Ny4wNzIgMTA0LjQ0OCAxMjggMTI4IDEyOGg3Njh6IG0tNDIuNjg4IDg1LjMxMkgxNzAuNjg4djU5Ny4zNzZoNjgyLjYyNFYyMTMuMzEyek00MzIgMzIwYzYwLjk5MiAwIDkwLjg4IDQxLjY2NCA5MC44OCAxMDcuMjY0IDAgMzAuMDgtMTYuODk2IDU4LjI0LTM1LjEzNiA3OC4zMzYtNi4yMDggMTEuMi05LjkyIDIyLjQtOS45MiAzMi41MTIgMCA4LjcwNCA5Ljg1NiAxOC42ODggMjMuOTM2IDIxLjQ0bDEzLjE4NCAyLjQ5NiAyLjYyNCAwLjY0YzM4LjQgOS42IDgwLjQ0OCAyOC42MDggODguODk2IDU5LjUybDEuMzQ0IDcuMzYgMC4xOTIgNC4wOTZ2MTkuNTg0YTI4LjAzMiAyOC4wMzIgMCAwIDEtMjAuMjg4IDI3LjI2NGwtOC40NDggMi4zMDQtMjAuMjI0IDUuMDU2QzUyNS40NCA2OTUuNjggNDc4LjcyIDcwNCA0MzIgNzA0Yy00MS42IDAtODMuMDcyLTYuNTkyLTExNS4zOTItMTMuNTA0bC0yMi4zMzYtNS4xODRjLTYuNzItMS43MjgtMTIuOC0zLjMyOC0xNy45Mi00LjhhMjcuODQgMjcuODQgMCAwIDEtMTkuODQtMjEuOTUyTDI1NiA2NTMuMjQ4VjYzMy42YzAtMzQuODE2IDQxLjQwOC01Ny4wODggODEuNzkyLTY4LjczNmw4LjY0LTIuMzA0IDE1LjgwOC0zLjA3MmMxMy45NTItMi43NTIgMjMuOTM2LTExLjk2OCAyMy45MzYtMjEuNDRhNjkuMTIgNjkuMTIgMCAwIDAtOS45Mi0zMi40NDhjLTE4LjI0LTIwLjIyNC0zNS4yLTQ4LjM4NC0zNS4yLTc4LjRDMzQxLjEyIDM2MS42NjQgMzcxLjA3MiAzMjAgNDMyIDMyMHoiIGZpbGw9IiM3MzdDOEMiIHAtaWQ9IjQ3MjA4Ij48L3BhdGg+PC9zdmc+\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,70,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,140,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,70,56)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><!--z-index:3--></g><g class=\\\"x6-graph-svg-decorator\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tools x6-node-tools\\\" data-cell-id=\\\"173027788615411\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: nw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: ne-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: sw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: se-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g class=\\\"x6-graph-svg-overlay\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></svg>\",\"workflowId\":null}",
                0)
        when:
        VerifyManager.instance.execute(workflow)
        then:
        BPMBusinessException e = thrown()
    }

    def "任务节点的问题2"() {
        given:
        //ExecutableWorkflowExt workflow = getWorkflow("{\"sourceWorkflowId\":\"351120591103361024\",\"variables\":[{\"id\":\"activity_0##object_yM61w__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1524712155689##object_yM61w__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1524712155699##result\",\"type\":{\"name\":\"text\"}}],\"singleInstanceFlow\":0,\"externalFlow\":0,\"activities\":[{\"name\":\"开始\",\"description\":\"\",\"id\":\"1524712155688\",\"type\":\"startEvent\"},{\"execution\":{\"pass\":[{\"taskType\":\"external_message\",\"actionMapping\":{\"referenceApiName\":\"\",\"receiverIds\":{\"ext_bpm\":[\"activity_1524712155689##object_yM61w__c##leader数据负责人上级\"]},\"forwardType\":\"0\",\"appType\":\"FSAID_11490cb1-1\",\"title\":\"sdf\",\"content\":\"sdfs\"}}]},\"taskType\":\"anyone\",\"assignNextTask\":0,\"bpmExtension\":{\"executionName\":\"编辑对象\",\"form\":[[{\"used_in\":\"component\",\"name\":\"field_WsA2c__c\",\"label\":\"国家\",\"type\":\"country\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_I9ght__c\",\"label\":\"详细地址\",\"type\":\"text\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_gwNQw__c\",\"label\":\"市\",\"type\":\"city\",\"cascade_parent_api_name\":\"field_dJa85__c\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_dJa85__c\",\"label\":\"省\",\"type\":\"province\",\"cascade_parent_api_name\":\"field_WsA2c__c\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_wN8sb__c\",\"label\":\"区\",\"type\":\"district\",\"cascade_parent_api_name\":\"field_gwNQw__c\",\"required\":false},{\"name\":\"field_t8SCe__c\",\"label\":\"地区定位\",\"group_type\":\"area\",\"type\":\"group\",\"fields\":{\"area_country\":\"field_WsA2c__c\",\"area_location\":\"field_3u02C__c\",\"area_detail_address\":\"field_I9ght__c\",\"area_city\":\"field_gwNQw__c\",\"area_province\":\"field_dJa85__c\",\"area_district\":\"field_wN8sb__c\"},\"required\":false}]],\"entityName\":\"家乐福\",\"executionType\":\"update\",\"actionCode\":\"\",\"entityId\":\"object_yM61w__c\",\"objectId\":{\"expression\":\"activity_0##object_yM61w__c\"}},\"externalApplyTask\":0,\"name\":\"业务活动\",\"description\":\"\",\"id\":\"1524712155689\",\"canSkip\":false,\"assignee\":{\"ext_bpm\":[\"instance##owner流程发起人\"]},\"type\":\"userTask\"},{\"taskType\":\"anyone\",\"assignNextTask\":0,\"bpmExtension\":{\"executionName\":\"\",\"form\":[[{\"used_in\":\"component\",\"name\":\"field_WsA2c__c\",\"label\":\"国家\",\"type\":\"country\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_I9ght__c\",\"label\":\"详细地址\",\"type\":\"text\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_gwNQw__c\",\"label\":\"市\",\"type\":\"city\",\"cascade_parent_api_name\":\"field_dJa85__c\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_dJa85__c\",\"label\":\"省\",\"type\":\"province\",\"cascade_parent_api_name\":\"field_WsA2c__c\",\"required\":false},{\"used_in\":\"component\",\"name\":\"field_wN8sb__c\",\"label\":\"区\",\"type\":\"district\",\"cascade_parent_api_name\":\"field_gwNQw__c\",\"required\":false},{\"name\":\"field_t8SCe__c\",\"label\":\"地区定位\",\"group_type\":\"area\",\"type\":\"group\",\"fields\":{\"area_country\":\"field_WsA2c__c\",\"area_location\":\"field_3u02C__c\",\"area_detail_address\":\"field_I9ght__c\",\"area_city\":\"field_gwNQw__c\",\"area_province\":\"field_dJa85__c\",\"area_district\":\"field_wN8sb__c\"},\"required\":false}]],\"entityName\":\"家乐福\",\"executionType\":\"approve\",\"actionCode\":\"\",\"entityId\":\"object_yM61w__c\",\"objectId\":{\"expression\":\"activity_1524712155689##object_yM61w__c\"}},\"externalApplyTask\":0,\"name\":\"审批\",\"description\":\"\",\"id\":\"1524712155699\",\"assignee\":{\"ext_bpm\":[\"instance##owner流程发起人\"]},\"type\":\"userTask\"},{\"name\":\"结束\",\"description\":\"\",\"id\":\"1524712155700\",\"type\":\"endEvent\"}],\"name\":\"sgl-group\",\"description\":\"\",\"id\":\"5ae14b773db71dae1d9fcbd8\",\"transitions\":[{\"toId\":\"1524712155689\",\"serialNumber\":0,\"id\":\"1524712155691\",\"fromId\":\"1524712155688\"},{\"toId\":\"1524712155699\",\"serialNumber\":1,\"id\":\"1524712155701\",\"fromId\":\"1524712155689\"},{\"toId\":\"1524712155700\",\"condition\":{\"type\":\"and\",\"conditions\":[{\"left\":{\"expression\":\"activity_1524712155699##result\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"agree\"},\"type\":\"equals\"}]},\"serialNumber\":2,\"id\":\"1524712155702\",\"fromId\":\"1524712155699\"},{\"toId\":\"1524712155689\",\"condition\":{\"type\":\"and\",\"conditions\":[{\"left\":{\"expression\":\"activity_1524712155699##result\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"reject\"},\"type\":\"equals\"}]},\"serialNumber\":3,\"id\":\"1524712155703\",\"fromId\":\"1524712155699\"}],\"type\":\"workflow_bpm\"}");
        initI18nClient()
        def workflow = getWorkflowByJson(getServiceRef(),"测试用的123", "object_7prLH__c", "油焖大虾",
                getWorkflow("{\"creator\":\"1002\",\"externalFlow\":0,\"modifier\":\"1002\",\"entityId\":\"object_7prLH__c\",\"history\":false,\"singleInstanceFlow\":0,\"modifyTime\":*************,\"enable\":true,\"linkAppEnable\":false,\"name\":\"测试用的123\",\"description\":\"\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"1730277886154\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"object_7prLH__c\",\"entityName\":\"油焖大虾\",\"objectId\":{\"expression\":\"activity_0##object_7prLH__c\"},\"objectName\":\"流程发起数据/油焖大虾\",\"layoutType\":\"none\",\"defaultButtons\":{},\"outerTenantType\":\"outerTenantField\"},\"canSkip\":false,\"linkAppEnable\":false,\"name\":\"业务活动\",\"id\":\"17302778861541\",\"assignee\":{\"person\":[\"1007\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0,\"custom\":false,\"customCandidateConfig\":false,\"importObject\":false},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"1730277917441\"}],\"transitions\":[{\"id\":\"1730277886154111\",\"fromId\":\"1730277886154\",\"toId\":\"17302778861541\",\"serialNumber\":0},{\"id\":\"1730277922505\",\"fromId\":\"17302778861541\",\"toId\":\"1730277917441\",\"serialNumber\":0}],\"variables\":[{\"id\":\"activity_0##object_7prLH__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_17302778861541##object_7prLH__c\",\"type\":{\"name\":\"text\"}}],\"id\":\"6721f226d3bc5a0001e6b408\",\"sourceWorkflowId\":\"6721f226d3bc5a0001e6b407\",\"createTime\":1730277926455,\"tenantId\":\"71557\",\"appId\":\"BPM\",\"type\":\"workflow_bpm\"}"),
                null,
                "{\"id\":null,\"pools\":[{\"id\":null,\"name\":null,\"lanes\":[{\"id\":\"173027788615411\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"1730277886154\",\"17302778861541\",\"1730277917441\"],\"order\":1}]}],\"diagram\":[{\"id\":\"173027788615411\",\"attr\":{\"x\":80.0,\"y\":60.0,\"width\":220.0,\"height\":540.0}},{\"id\":\"1730277886154111\",\"attr\":{\"d\":\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277922505\",\"attr\":{\"d\":\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277886154\",\"attr\":{\"x\":160.0,\"y\":125.0}},{\"id\":\"1730277917441\",\"attr\":{\"x\":120.0,\"y\":430.0}},{\"id\":\"17302778861541\",\"attr\":{\"x\":110.0,\"y\":260.0}}],\"svg\":\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"100%\\\" height=\\\"100%\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" class=\\\"x6-graph-svg\\\" viewBox=\\\"80 60 220 540\\\"><defs style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><marker refX=\\\"-1\\\" id=\\\"marker-v54-2980955686\\\" overflow=\\\"visible\\\" orient=\\\"auto\\\" markerUnits=\\\"userSpaceOnUse\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path stroke=\\\"#545861\\\" fill=\\\"#545861\\\" transform=\\\"rotate(180)\\\" d=\\\"M 0 0 L 12 -4 L 12 4 Z\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></marker></defs><g class=\\\"x6-graph-svg-viewport\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-graph-svg-primer\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-graph-svg-stage\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g data-cell-id=\\\"173027788615411\\\" data-shape=\\\"lane\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(80,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" height=\\\"16\\\" width=\\\"220\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#dee1e8\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" x=\\\"1\\\" y=\\\"1\\\" stroke-opacity=\\\"0\\\" width=\\\"32\\\" height=\\\"16\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"start\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" width=\\\"270\\\" transform=\\\"matrix(1,0,0,1,35,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</tspan></text><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#181c25\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,15,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">1</tspan></text><rect fill=\\\"none\\\" stroke=\\\"#368dff\\\" stroke-width=\\\"1\\\" fill-opacity=\\\"0\\\" width=\\\"220\\\" height=\\\"540\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><image style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g data-cell-id=\\\"1730277886154111\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,189.78128051757812,234.9930419921875)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g data-cell-id=\\\"1730277922505\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,180,366)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><!--z-index:2--><g data-cell-id=\\\"1730277886154\\\" data-shape=\\\"startEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(160,125)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#16B4AB\\\" stroke=\\\"#16B4AB\\\" stroke-width=\\\"1\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"1730277917441\\\" data-shape=\\\"endEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(120,430)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#ffffff\\\" stroke=\\\"#737C8C\\\" stroke-width=\\\"4\\\" org-stroke=\\\"#737C8C\\\" fill-opacity=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><circle fill=\\\"#737C8C\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"25\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"17302778861541\\\" data-shape=\\\"business\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(110,260)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#368DFF\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" class=\\\"x6-hightlight-body\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"left\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" text=\\\"业务活动\\\" transform=\\\"matrix(1,0,0,1,37,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</tspan></text><image x=\\\"10\\\" y=\\\"15\\\" width=\\\"24\\\" height=\\\"24\\\" xlink:href=\\\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzI2ODI0NzAyNzQwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ3MjA3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik04OTYgMTI4YzIzLjU1MiAwIDQyLjY4OCAxOS4wNzIgNDIuNjg4IDQyLjY4OHY2ODIuNjI0QTQyLjY4OCA0Mi42ODggMCAwIDEgODk2IDg5NkgxMjhhNDIuNjg4IDQyLjY4OCAwIDAgMS00Mi42ODgtNDIuNjg4VjE3MC42ODhDODUuMzEyIDE0Ny4wNzIgMTA0LjQ0OCAxMjggMTI4IDEyOGg3Njh6IG0tNDIuNjg4IDg1LjMxMkgxNzAuNjg4djU5Ny4zNzZoNjgyLjYyNFYyMTMuMzEyek00MzIgMzIwYzYwLjk5MiAwIDkwLjg4IDQxLjY2NCA5MC44OCAxMDcuMjY0IDAgMzAuMDgtMTYuODk2IDU4LjI0LTM1LjEzNiA3OC4zMzYtNi4yMDggMTEuMi05LjkyIDIyLjQtOS45MiAzMi41MTIgMCA4LjcwNCA5Ljg1NiAxOC42ODggMjMuOTM2IDIxLjQ0bDEzLjE4NCAyLjQ5NiAyLjYyNCAwLjY0YzM4LjQgOS42IDgwLjQ0OCAyOC42MDggODguODk2IDU5LjUybDEuMzQ0IDcuMzYgMC4xOTIgNC4wOTZ2MTkuNTg0YTI4LjAzMiAyOC4wMzIgMCAwIDEtMjAuMjg4IDI3LjI2NGwtOC40NDggMi4zMDQtMjAuMjI0IDUuMDU2QzUyNS40NCA2OTUuNjggNDc4LjcyIDcwNCA0MzIgNzA0Yy00MS42IDAtODMuMDcyLTYuNTkyLTExNS4zOTItMTMuNTA0bC0yMi4zMzYtNS4xODRjLTYuNzItMS43MjgtMTIuOC0zLjMyOC0xNy45Mi00LjhhMjcuODQgMjcuODQgMCAwIDEtMTkuODQtMjEuOTUyTDI1NiA2NTMuMjQ4VjYzMy42YzAtMzQuODE2IDQxLjQwOC01Ny4wODggODEuNzkyLTY4LjczNmw4LjY0LTIuMzA0IDE1LjgwOC0zLjA3MmMxMy45NTItMi43NTIgMjMuOTM2LTExLjk2OCAyMy45MzYtMjEuNDRhNjkuMTIgNjkuMTIgMCAwIDAtOS45Mi0zMi40NDhjLTE4LjI0LTIwLjIyNC0zNS4yLTQ4LjM4NC0zNS4yLTc4LjRDMzQxLjEyIDM2MS42NjQgMzcxLjA3MiAzMjAgNDMyIDMyMHoiIGZpbGw9IiM3MzdDOEMiIHAtaWQ9IjQ3MjA4Ij48L3BhdGg+PC9zdmc+\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,70,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,140,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,70,56)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><!--z-index:3--></g><g class=\\\"x6-graph-svg-decorator\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tools x6-node-tools\\\" data-cell-id=\\\"173027788615411\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: nw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: ne-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: sw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: se-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g class=\\\"x6-graph-svg-overlay\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></svg>\",\"workflowId\":null}",
                0)
        when:
        VerifyManager.instance.execute(workflow)
        then:
        BPMBusinessException e = thrown()
    }

    /*def "任务节点的问题3"() {
        given:
        initI18nClient()
        ExecutableWorkflowExt workflowExt = getWorkflow("{\"creator\":\"1007\",\"variables\":[{\"id\":\"activity_0##ContractObj\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1525685725663##ContractObj\",\"type\":{\"name\":\"text\"}}],\"externalFlow\":0,\"modifier\":\"1007\",\"description\":\"\",\"type\":\"workflow_bpm\",\"transitions\":[{\"toId\":\"1525685725663\",\"serialNumber\":0,\"id\":\"1525685725665\",\"fromId\":\"1525685725662\"},{\"toId\":\"1525685725668\",\"serialNumber\":1,\"id\":\"1525685725669\",\"fromId\":\"1525685725663\"}],\"sourceWorkflowId\":\"354399462049349632\",\"singleInstanceFlow\":1,\"modifyTime\":1525964275386,\"createTime\":1525689670900,\"activities\":[{\"name\":\"开始\",\"description\":\"\",\"id\":\"1525685725662\",\"type\":\"startEvent\"},{\"execution\":{\"pass\":[{\"rowNo\":0,\"taskType\":\"updates\",\"modifyTime\":0,\"updateFieldJson\":\"[{\\\"isCalculate\\\":true,\\\"key\\\":\\\"\${ContractObj.expired_time}\\\",\\\"value\\\":1525881600000,\\\"defaultValue\\\":\\\"\\\",\\\"entityId\\\":\\\"activity_1525685725663##ContractObj\\\"}]\"},{\"rowNo\":0,\"taskType\":\"trigger_bpm\",\"modifyTime\":0,\"triggerParam\":{\"id\":\"354399462619774976\",\"objectId\":\"activity_1525685725663##ContractObj\"}},{\"rowNo\":0,\"taskType\":\"external_message\",\"actionMapping\":{\"referenceApiName\":\"\",\"receiverIds\":{\"extUserType\":[\"activity_1525685725663##ContractObj##created_by\",\"activity_1525685725663##ContractObj##out_owner\"],\"ext_bpm\":[\"activity_1525685725663##ContractObj##owner\$\$数据负责人\",\"activity_1525685725663##ContractObj##leader\$\$数据负责人上级\",\"activity_1525685725663##ContractObj##group_leader\$\$数据相关团队成员上级\",\"activity_1525685725663##ContractObj##group\$\$数据相关团队成>员\",\"activity_1525685725663##ContractObj##assigneeId\$\$节点处理人\",\"activity_1525685725663##ContractObj##assigneeId##leader\$\$节点处理人上级\"]},\"forwardType\":\"0\",\"appType\":\"FSAID_11490cbb-1\",\"title\":\"\${activity_1525685725663 # # ContractObj # # status}\",\"content\":\"\${activity_1525685725663 # # ContractObj # # status}\"},\"modifyTime\":0},{\"rowNo\":0,\"taskType\":\"custom_function\",\"actionMapping\":{\"funcProperties\":[{\"name\":\"a\",\"remark\":\"\",\"default_value\":\"0\",\"type\":\"String\",\"value\":\"\${activity_1525685725663 # # ContractObj # # status}\"}],\"functionName\":\"example\",\"functionApiName\":\"func_H8lXx__c\",\"entityId\":\"ContractObj\"},\"modifyTime\":0},{\"rowNo\":0,\"taskType\":\"feed_task\",\"actionMapping\":{\"carbonCopyEmployeeIds\":{\"person\":[\"1030\"]},\"attenderEmployeeIds\":{\"dept_leader\":[\"1001\",\"1002\"],\"extUserType\":[\"activity_1525685725663##ContractObj##last_modified_by\",\"activity_1525685725663##ContractObj##created_by\"],\"person\":[\"1033\"],\"ext_bpm\":[\"activity_1525685725663##ContractObj##assigneeId##dept_leader\$\$节点处理人所属主部门负责人\",\"activity_1525685725663##ContractObj##assigneeId##leader\$\$节>点处理人上级\",\"activity_1525685725663##ContractObj##assigneeId\$\$节点处理人\"]},\"title\":\"\${activity_1525685725663 # # ContractObj # # status}\",\"content\":\"\${activity_1525685725663 # # ContractObj # # contract_title}\${activity_1525685725663 # # ContractObj # # created_by}\",\"deadLine\":1526659226572},\"modifyTime\":0},{\"rowNo\":0,\"taskType\":\"feed_schedule\",\"actionMapping\":{\"allDay\":true,\"attenderEmployeeIds\":{\"role\":[\"00000000000000000000000000000009\"],\"dept_leader\":[\"1000\"],\"extUserType\":[\"activity_1525685725663##ContractObj##last_modified_by\"],\"person\":[\"1032\"],\"dept\":[\"1000\"],\"ext_bpm\":[\"activity_1525685725663##ContractObj##leader\$\$数据负责人上级\"],\"group\":[\"5acf15cb3db71d334124ba57\"]},\"receipt\":false,\"beginTime\":1525968037177,\"endTime\":1527696031799,\"content\":\"\${activity_1525685725663 # # ContractObj # # status}\",\"remindTypes\":[{\"time\":\"\",\"value\":10}]},\"modifyTime\":0}]},\"taskType\":\"anyone\",\"assignNextTask\":0,\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"合同\",\"executionType\":\"update\",\"actionCode\":\"\",\"entityId\":\"ContractObj\",\"objectId\":{\"expression\":\"activity_0##ContractObj\"},\"form\":[[{\"name\":\"contract_amount\",\"label\":\"合同金额\",\"type\":\"currency\",\"required\":true},{\"name\":\"started_time\",\"label\":\"开始日期\",\"type\":\"date\",\"required\":false},{\"name\":\"expired_time\",\"label\":\"截止日期\",\"type\":\"date\",\"required\":false},{\"name\":\"UDAttach1__c\",\"label\":\"附件\",\"type\":\"file_attachment\",\"required\":false},{\"name\":\"UDSText1__c\",\"label\":\"22\",\"type\":\"text\",\"required\":false}]]},\"externalApplyTask\":0,\"name\":\"业务活动\",\"description\":\"\",\"canSkip\":false,\"id\":\"1525685725663\",\"assignee\":{\"ext_bpm\":[\"instance##owner\$\$流程>发起人\"]},\"type\":\"userTask\"},{\"name\":\"结束\",\"description\":\"\",\"id\":\"1525685725668\",\"type\":\"endEvent\"}],\"appId\":\"BPM\",\"tenantId\":\"55732\",\"name\":\"测试测试业务流程12-万松\",\"id\":\"5af45df33db71d87fc3a429a\"}")
        workflowExt.put("entityId", "ContractObj")
        workflowExt.put("entryTypeName", "ContractObj")
        Workflow workflow = new Workflow(workflowExt, "{\"pools\":[{\"lanes\":[{\"id\":\"1525685725664\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"1525685725662\",\"1525685725663\",\"1525685725668\"]}]}]}")
        when:
        VerifyManager.instance.execute(workflow)
        then:
        1 == 1
    }*/
    /*def "获取前置节点3"(){
        given:
        def time1=System.currentTimeMillis()
        ExecutableWorkflowExt workflow2=getWorkflowFromFile("validate_pre_activities.json")
        when:
        def rst=PreNodesUtil.getPreActivities("1",workflow2)
        def time2=System.currentTimeMillis()
        then:
        rst!=null
        (time2-time1)<5*1000
    }*/

    def "获取前置节点4"(){
        given:
        def time1=System.currentTimeMillis()
        ExecutableWorkflowExt workflow2=getWorkflowFromFile("validate_pre_activities.json")
        def graph=new WorkflowGraph(workflow2)
        when:
        graph.print()
        def time2=System.currentTimeMillis()
        then:
        (time2-time1)<5*1000
    }


    def "不包含直接到自动节点问题判断"(){
        given:
        def time1=System.currentTimeMillis()
        ExecutableWorkflowExt workflow2=getWorkflowFromFile("validate_pre_activities3.json")
        def graph=new WorkflowGraph(workflow2)
        def activities=workflow2.getActivities()
        for(ActivityExt activity:activities){
            if(activity.instanceOf(ExecutionTaskExt.class)){
            if(graph.toExecutionTaskNoUserTask(activity.getId())){
                throw new BPMWorkflowDefVerifyException(activity.getName()+"节点到开始节点之间必须审批或业务节点,请确认.");
            }}
        }
        when:
        def time2=System.currentTimeMillis()
        then:
        (time2-time1)<5*1000
    }

}
