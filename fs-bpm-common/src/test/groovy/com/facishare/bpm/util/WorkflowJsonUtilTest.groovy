package com.facishare.bpm.util

import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import org.apache.commons.io.IOUtils
import spock.lang.Specification

/**
 *
 *
 * @since 5.7
 * <AUTHOR> ( <PERSON> )
 */
class WorkflowJsonUtilTest extends Specification {
    def "流程定义处理"(){
        given:"参数处理"

        when:""
        WorkflowJsonUtil.proofreadAndCorrectWorkflow(workflow)
        then:"校验"
        WorkflowJsonUtil.convertExecutableWorkflowToJson(workflow)==WorkflowJsonUtil.convertExecutableWorkflowToJson(result)
        where:"参数设置"
        workflow||result
        getWorkflow1()||getWorkflow1Result()
    }

    def getWorkflow1Result() {
        WorkflowJsonUtil.convertExecutableWorkflowFromJson(IOUtils.toString(getClass().getClassLoader().getResourceAsStream("workflows/workflowResult")))
    }

    def getWorkflow1() {
        WorkflowJsonUtil.convertExecutableWorkflowFromJson(IOUtils.toString(getClass().getClassLoader().getResourceAsStream("workflows/workflow1")))
    }

    def "测试 proofreadAndCorrectWorkflow"() {
        given:
        def workflow = new ExecutableWorkflowExt([
            activities: [
                [id: "1", type: "userTask", name: "任务1"],
                [id: "2", type: "userTask", name: "任务2"]
            ],
            transitions: [
                [fromId: "1", toId: "2"]
            ]
        ])

        when:
        WorkflowJsonUtil.proofreadAndCorrectWorkflow(workflow)

        then:
        workflow.activities.size() == 2
        workflow.transitions.size() == 1
    }

    def "测试 convertExecutableWorkflowToJson"() {
        given:
        def workflow = new ExecutableWorkflowExt([
            id: "test",
            name: "测试流程",
            activities: []
        ])

        when:
        def json = WorkflowJsonUtil.convertExecutableWorkflowToJson(workflow)

        then:
        json.contains("test")
        json.contains("测试流程")
    }

    def "测试 convertExecutableWorkflowFromJson"() {
        given:
        def json = '''
        {
            "id": "test",
            "name": "测试流程",
            "activities": [],
            "transitions": []
        }
        '''

        when:
        def workflow = WorkflowJsonUtil.convertExecutableWorkflowFromJson(json)

        then:
        workflow.id == "test"
        workflow.name == "测试流程"
        workflow.activities.isEmpty()
        workflow.transitions.isEmpty()
    }
}