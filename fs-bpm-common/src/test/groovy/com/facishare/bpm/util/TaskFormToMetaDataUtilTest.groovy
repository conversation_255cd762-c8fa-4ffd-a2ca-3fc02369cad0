package com.facishare.bpm.util


import spock.lang.Specification

/**
 * Created by <PERSON> on 20/04/2017.
 */
class TaskFormToMetaDataUtilTest extends Specification {

    /*def "transferTaskFormToMetaDataDesc"() {
        def taskForm = '''[[
                        {"name":"UDSText11__c","label":"040701(可删)","type":"text","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"city","label":"040701(可删)","type":"city","cascade_parent_api_name":"province","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"UDTel2__c","label":"电话11","type":"phone_number","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"UDSText3__c","label":"C_0331","type":"text","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"UDSText12__c","label":"C_03312","type":"object_reference","required":false,targetApiName:"targetApiName",targetListName:"targetListName","value":"","readonly":false,"hidden":false},
                        {"name":"UDDec1__c","label":"小数测试","type":"number","required":false,"value":"0","readonly":false,"hidden":false},
                        {"name":"UDSText5__c","label":"C_03244444","type":"text","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"UDSText7__c","label":"子行业1","type":"text","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"UDSSel1__c","label":"网","type":"select_one","required":false,"value":"","options":[
                        {"label":"12","value":"1","resource_bundle_key":""},
                        {"label":"111","value":"2","resource_bundle_key":""}],"readonly":false,"hidden":false},
                        {"name":"UDMSel1__c","label":"多项选择测试字段","type":"select_many","required":false,"value":[""],"options":[
                        {"label":"111","value":"1","resource_bundle_key":""},
                        {"label":"222","value":"2","resource_bundle_key":""}
                        ],"readonly":false,"hidden":false},
                        {"name":"UDSSel4__c","label":"测妹纸","type":"select_one","required":false,"value":"","options":
                        [
                        {"label":"1","value":"1","resource_bundle_key":""},
                        {"label":"2","value":"2","resource_bundle_key":""},
                        {"label":"3","value":"3","resource_bundle_key":""}],"readonly":false,"hidden":false},
                        {"name":"UDMSel2__c","label":"测妹纸1","type":"select_many","required":false,"value":[""],
                        "options":[{"label":"1","value":"1","resource_bundle_key":""},
                        {"label":"2","value":"2","resource_bundle_key":""},
                        {"label":"3","value":"3","resource_bundle_key":""}],"readonly":false,"hidden":false},
                        {"name":"UDSText13__c","label":"测妹纸2","type":"text","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"country","label":"国家","type":"country","required":false,"value":"","readonly":false,"hidden":false},
                        {"name":"province","label":"省","type":"province","required":true,"value":"","readonly":false,"hidden":false,"cascade_parent_api_name":"country"},
                        {"name":"city","label":"市","type":"city","required":true,"value":"","readonly":false,"hidden":false,"cascade_parent_api_name":"province"},
                        {"name":"district","label":"区","type":"district","required":true,"value":"","readonly":false,"hidden":false,"cascade_parent_api_name":"city"},
                        
                        ]]
                    '''
        when:
        def forms = JsonUtil.fromJson(taskForm, List.class)


        then:

        def result = TaskFormToMetaDataUtil.
                format2MetaData("AccountObj", "8f21b164c1094faa870c1e544150784b",[:], ["fields":[_id:[:],city:[:]]], forms)
        println JsonUtil.toPrettyJson(result.two)
        expect:
        result.getOne() != null
        result.getOne().get("_id") != null
        ((HashMap) result.getTwo().get("fields")).get("_id") != null
        ((Map) ((HashMap) result.getTwo().get("fields")).get("_id")).get("api_name") == "_id"
        ((Map) ((HashMap) result.getTwo().get("fields")).get("city")).get("cascade_parent_api_name") == "province"
        result.getThree() != null
    }



    def testFormWithGroup(){
        given:
        def formData='''[[
            {"name":"name","label":"单行文本","type":"text","required":true,"value":"甲3","default_is_expression":false,"is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"name","is_unique":true,"default_value":"","default_to_zero":false,"define_type":"system","_id":"5a73dc650959b620e4d1b511","is_index_field":false,"is_single":false,"index_name":"name","help_text":"","max_length":100,"status":"new","readonly":false,"hidden":false},
            {"name":"field_3L72c__c","label":"国家","type":"select_one","required":false,"used_in":"component","is_index":true,"is_active":true,"create_time":1519826724374,"is_unique":false,"field_num":19,"define_type":"custom","_id":"5a96b724f125ae373ef79072","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"help_text":"","status":"new","readonly":false,"hidden":false},
            {"name":"field_Ckf0a__c","label":"定位","type":"location","required":false,"used_in":"component","is_index":true,"is_active":true,"create_time":1519826724375,"is_unique":false,"field_num":18,"define_type":"custom","_id":"5a96b724f125ae373ef79077","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"label":1,"help_text":1}},"help_text":"","status":"new","readonly":false,"hidden":false},
            {"name":"field_jj2tc__c","label":"详细地址","type":"text","required":false,"used_in":"component","default_is_expression":false,"is_index":true,"is_active":true,"create_time":1519826724375,"pattern":"","is_unique":false,"default_value":"","field_num":15,"default_to_zero":false,"define_type":"custom","_id":"5a96b724f125ae373ef79076","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"help_text":"","max_length":100,"status":"new","readonly":false,"hidden":false},
            {"name":"field_CcunN__c","label":"市","type":"select_one","required":false,"cascade_parent_api_name":"province","used_in":"component","is_index":true,"is_active":true,"create_time":1519826724375,"is_unique":false,"field_num":14,"define_type":"custom","_id":"5a96b724f125ae373ef79075","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"is_unique":1,"default_value":1,"label":1,"help_text":1}},"help_text":"","status":"new","readonly":false,"hidden":false},
            {"name":"field_4638A__c","label":"地区定位","type":"group","required":false,"fields":{"area_country":"field_3L72c__c","area_location":"field_Ckf0a__c","area_detail_address":"field_jj2tc__c","area_city":"field_CcunN__c","area_province":"field_1vmse__c","area_district":"field_27K2i__c"},"group_type":"area","is_index":true,"area_location":"field_Ckf0a__c","is_active":true,"area_detail_address":"field_jj2tc__c","create_time":1519826724336,"area_city":"field_CcunN__c","is_unique":false,"area_district":"field_27K2i__c","area_country":"field_3L72c__c","define_type":"custom","area_province":"field_1vmse__c","_id":"5a96b724f125ae373ef79078","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"display":1,"remove":1,"attrs":{"api_name":1,"header":1}},"help_text":"","status":"new","readonly":false,"hidden":false}
        ]]'''
        when:

        def forms=JsonUtil.fromJson(formData,List.class)
        then:
        def result=TaskFormToMetaDataUtil.format2MetaData("AccountObj","",forms)
        println JsonUtil.toPrettyJson(result.three)

    }*/

}
