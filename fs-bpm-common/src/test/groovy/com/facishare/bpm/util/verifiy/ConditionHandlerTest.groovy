package com.facishare.bpm.util.verifiy

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import com.facishare.bpm.bpmn.VariableExt
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra
import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions
import com.facishare.bpm.model.resource.newmetadata.UpdateData
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch
import com.facishare.bpm.remote.model.org.Dept
import com.facishare.bpm.util.RefServiceManagerAbs
import com.facishare.bpm.util.verifiy.handler.ConditionHandler
import com.facishare.flow.element.plugin.api.FlowElement
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper
import com.facishare.rest.core.model.RemoteContext
import com.facishare.rest.core.util.JsonUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

import java.util.function.Function

/**
 * Created by wangz on 17-6-12.
 */
class ConditionHandlerTest extends Specification {
    def "validate"() {
        given: "mock workflow"
        def conditionHandler = new ConditionHandler()
        ExecutableWorkflowExt executableWorkflow = generateExecutableWorkflow()
        Workflow workflow = Mock()
        workflow.getExecutableWorkflow() >> executableWorkflow
        workflow.getActivities() >> executableWorkflow.getActivities()
        workflow.getActivityMap() >> executableWorkflow.getActivityMaps()
        workflow.getVariablesList() >> generateVariables(executableWorkflow.getVariables())
        workflow.getTransitions() >> executableWorkflow.getTransitions()
        workflow.getActivityPreNodeMap() >> generatePreNodesMap(executableWorkflow.getActivities())
        workflow.getServiceManager()>>getServiceRef()
        workflow.getPreActivityFun(_) >> { activity ->
            return { activityId, entity, isSelf -> true }
        }
        when: "verify workflow conditions"
        conditionHandler.validate(workflow)

        then:
        Exception e = thrown()
        e.printStackTrace()
        e instanceof BPMWorkflowDefVerifyException
        e.getMessage().contains("分支节点") || e.getMessage().contains("条件")
        print e

    }
    RefServiceManager getServiceRef() {
        return new RefServiceManagerAbs(){
            static final Map<String,Map<String,Object>> cache=new java.util.concurrent.ConcurrentHashMap<String,Map<String,Object>>()
            static {
                def fields=[:]
                fields.put("account_source",[label:"来源",type:"text","is_active":true])
                fields.put("tel",[label:"电话",type:"text","is_active":true])
                cache.put("AccountObj",["fields":fields,"display_name":"客户"])
            }

            @Override
            Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
                return null
            }
            
            @Override
            Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
                return null
            }
            
            @Override
            Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData, boolean skipRelevantTeam) {
                return null
            }

            @Override
            Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
                return null
            }

            @Override
            Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
                return null
            }

            @Override
            boolean isDataOwnerByQueryMetadata(String apiName, String objectId) {
                return false
            }

            @Override
            Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
                return null
            }

            @Override
            boolean workflowIsExists(String tenantId, String outLineId, String sourceWorkflowId) {
                return false
            }

            @Override
            Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
                return null
            }

            @Override
            boolean getDeliveryNoteEnable() {
                return false
            }

            @Override
            GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
                return null
            }

            @Override
            Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
                return null
            }

            @Override
            GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
                return null
            }

            @Override
            boolean dataPrivilege(String entityId, String objectId) {
                return false
            }

            @Override
            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }

            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck) {
                return null
            }

            @Override
            Map<String, String> getAppActions(String entityId, Boolean isExternalFlow, String appId, Integer appType) {
                return null
            }

            @Override
            boolean isOuterMainOwner() {
                return false
            }

            @Override
            GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
                return null
            }

            @Override
            Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
                return null
            }

            @Override
            def <E> E getObjectFromCache(String key, Function<String, E> function) {
                return null
            }

            @Override
            Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet) {
                return null
            }

            @Override
            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction) {
                return null
            }

            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName) {
                return null
            }

            @Override
            Map<String, String> findDataExhibitButton(String apiName, String objectId, String usePageType) {
                return null
            }

            @Override
            boolean checkEmailEnable(String sender) {
                return false
            }

            @Override
            FlowElementWrapper getFlowElementWrapper(String elementApiName) {
                return null
            }

            @Override
            FlowElement getFlowElement(String elementApiName) {
                return null
            }

            @Override
            String getI18nLinkAppName(String linkApp, String linkAppName) {
                return null
            }

            @Override
            boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
                return false
            }

            @Override
            Map<String, Object> getFlowConfig(String terminal, List<String> types) {
                return null
            }

            @Override
            boolean isNeedDiscussButton() {
                return false
            }

            @Override
            UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }

            @Override
            Map<String, Object> getDescribe(String entityId) {
                def rst = cache.get(entityId)
                if (rst == null) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_DELETE_DISABLE,entityId)
                }
                return rst
            }

            @Override
            Map<String, Map<String, Object>> getDescribes(Collection<String> entityIds) {
                Map<String, Map<String, Object>> rst = new HashMap<String, Map<String, Object>>();
                for (String entityId : entityIds) {
                    try {
                        rst.put(entityId, getDescribe(entityId));
                    } catch (Exception e) {
                        // 忽略错误，继续处理其他实体
                    }
                }
                return rst;
            }
        }
    }
    def generatePreNodesMap(List<ActivityExt> activities) {
        Map<String, ActivityExt> activityMap = new HashMap<String, ActivityExt>()
        for (ActivityExt activity : activities) {
            activityMap.put(activity.getId(), activity)
        }

        Map<String, List<ActivityExt>> ret = new HashMap<String, List<ActivityExt>>()
        ret.put("*************", new ArrayList<ActivityExt>())
        ret.put("*************", [activityMap.get("*************")])
        ret.put("*************", [activityMap.get("*************"), activityMap.get("*************")])

        return ret
    }

    def generateVariables(List<VariableExt> variables) {
        ArrayList<String> ret = new ArrayList<String>()
        for (VariableExt v : variables) {
            ret.add(v.getId())
        }

        return ret
    }

    def generateExecutableWorkflow() {
        def workflowJson = "{\n" +
                "    \"type\": \"workflow_bpm\",\n" +
                "    \"name\": \"前置节点测试\",\n" +
                "    \"description\": \"\",\n" +
                "    \"activities\": [\n" +
                "        {\n" +
                "            \"type\": \"startEvent\",\n" +
                "            \"name\": \"开始\",\n" +
                "            \"description\": \"\",\n" +
                "            \"id\": \"*************\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"userTask\",\n" +
                "            \"bpmExtension\": {\n" +
                "                \"actionCode\": \"\",\n" +
                "                \"executionType\": \"update\",\n" +
                "                \"executionName\": \"编辑对象\",\n" +
                "                \"entityId\": \"AccountObj\",\n" +
                "                \"entityName\": \"客户\",\n" +
                "                \"objectId\": {\n" +
                "                    \"expression\": \"activity_0##AccountObj\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"canSkip\": false,\n" +
                "            \"name\": \"业务活动1\",\n" +
                "            \"description\": \"\",\n" +
                "            \"id\": \"*************\",\n" +
                "            \"assignee\": {\n" +
                "                \"ext_bpm\": [\n" +
                "                    \"instance##owner\$\$流程发起人\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"taskType\": \"anyone\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"userTask\",\n" +
                "            \"bpmExtension\": {\n" +
                "                \"executionName\": \"编辑对象\",\n" +
                "                \"entityName\": \"客户\",\n" +
                "                \"entityId\": \"AccountObj\",\n" +
                "                \"executionType\": \"update\",\n" +
                "                \"objectId\": {\n" +
                "                    \"expression\": \"activity_0##AccountObj\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"name\": \"业务活动2\",\n" +
                "            \"description\": \"\",\n" +
                "            \"id\": \"*************\",\n" +
                "            \"assignee\": {\n" +
                "                \"ext_bpm\": [\n" +
                "                    \"instance##owner\$\$流程发起人\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"taskType\": \"anyone\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"exclusiveGateway\",\n" +
                "            \"name\": \"分支节点\",\n" +
                "            \"description\": \"\",\n" +
                "            \"id\": \"*************\",\n" +
                "            \"defaultTransitionId\": \"*************\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"userTask\",\n" +
                "            \"bpmExtension\": {\n" +
                "                \"executionName\": \"编辑对象\",\n" +
                "                \"entityName\": \"客户\",\n" +
                "                \"entityId\": \"AccountObj\",\n" +
                "                \"executionType\": \"update\",\n" +
                "                \"objectId\": {\n" +
                "                    \"expression\": \"activity_*************##AccountObj\"\n" +
                "                }\n" +
                "            },\n" +
                "            \"name\": \"业务活动3\",\n" +
                "            \"description\": \"\",\n" +
                "            \"id\": \"*************\",\n" +
                "            \"assignee\": {\n" +
                "                \"ext_bpm\": [\n" +
                "                    \"instance##owner\$\$流程发起人\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"taskType\": \"anyone\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"endEvent\",\n" +
                "            \"name\": \"结束\",\n" +
                "            \"description\": \"\",\n" +
                "            \"id\": \"*************\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"transitions\": [\n" +
                "        {\n" +
                "            \"description\": \"业务活动 / 客户 - 来源 等于 转介绍\",\n" +
                "            \"id\": \"1497258335471\",\n" +
                "            \"fromId\": \"*************\",\n" +
                "            \"toId\": \"*************\",\n" +
                "            \"condition\": {\n" +
                "                \"type\": \"or\",\n" +
                "                \"conditions\": [\n" +
                "                    {\n" +
                "                        \"type\": \"and\",\n" +
                "                        \"conditions\": [\n" +
                "                            {\n" +
                "                                \"type\": \"equals\",\n" +
                "                                \"left\": {\n" +
                "                                    \"expression\": \"activity_*************##AccountObj##account_source\"\n" +
                "                                },\n" +
                "                                \"right\": {\n" +
                "                                    \"value\": \"1\",\n" +
                "                                    \"type\": {\n" +
                "                                        \"name\": \"text\"\n" +
                "                                    }\n" +
                "                                }\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"description\": \"业务活动1 / 客户 - 电话 包含 123\",\n" +
                "            \"id\": \"*************\",\n" +
                "            \"fromId\": \"*************\",\n" +
                "            \"toId\": \"*************\",\n" +
                "            \"condition\": {\n" +
                "                \"type\": \"or\",\n" +
                "                \"conditions\": [\n" +
                "                    {\n" +
                "                        \"type\": \"and\",\n" +
                "                        \"conditions\": [\n" +
                "                            {\n" +
                "                                \"type\": \"contains\",\n" +
                "                                \"left\": {\n" +
                "                                    \"expression\": \"activity_*************##AccountObj##tel\"\n" +
                "                                },\n" +
                "                                \"right\": {\n" +
                "                                    \"value\": \"123\",\n" +
                "                                    \"type\": {\n" +
                "                                        \"name\": \"text\"\n" +
                "                                    }\n" +
                "                                }\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"*************\",\n" +
                "            \"fromId\": \"*************\",\n" +
                "            \"toId\": \"*************\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"1497258335469\",\n" +
                "            \"fromId\": \"*************\",\n" +
                "            \"toId\": \"*************\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"1497258335470\",\n" +
                "            \"fromId\": \"*************\",\n" +
                "            \"toId\": \"*************\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"*************\",\n" +
                "            \"fromId\": \"*************\",\n" +
                "            \"toId\": \"*************\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"variables\": [\n" +
                "        {\n" +
                "            \"id\": \"activity_0##AccountObj\",\n" +
                "            \"type\": {\n" +
                "                \"name\": \"text\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"activity_*************##AccountObj\",\n" +
                "            \"type\": {\n" +
                "                \"name\": \"text\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"activity_*************##AccountObj\",\n" +
                "            \"type\": {\n" +
                "                \"name\": \"text\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"activity_*************##AccountObj\",\n" +
                "            \"type\": {\n" +
                "                \"name\": \"text\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"activity_*************##AccountObj##account_source\",\n" +
                "            \"type\": {\n" +
                "                \"name\": \"text\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"activity_*************##AccountObj##tel\",\n" +
                "            \"type\": {\n" +
                "                \"name\": \"text\"\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"id\": \"593e83bd3db71db794fa0990\",\n" +
                "    \"sourceWorkflowId\": \"257823906307145728\"\n" +
                "}"

        ExecutableWorkflowExt executableWorkflow = JsonUtil.fromJson(workflowJson,
                ExecutableWorkflowExt.class)

        return executableWorkflow
    }
}
