package com.facishare.bpm.util

import spock.lang.Specification


/**
 * Created by <PERSON> on 26/05/2017.
 */
class MetaDataToWorkflowViriableUtilTest extends Specification {
    def "验证变量的获取"() {

        when: "获取结果"
        def result = MetaDataToWorkflowVariableUtil.parseValue(value, type)
        then: "对结果进行判断"
        result == rst
        where: "设置参数"
        msg      | value  | type   || rst
        "测试text" | "1000" | "text" || "1000"
        "测试text" | ["1000"] | "list" || ["1000"]
        "测试text" | "100" | "number" || 100

    }

    def "测试parseValue更多类型"() {
        expect:
        MetaDataToWorkflowVariableUtil.parseValue("true", "boolean") == true
        MetaDataToWorkflowVariableUtil.parseValue("false", "boolean") == false
        MetaDataToWorkflowVariableUtil.parseValue("123.45", "number") == 123.45
        MetaDataToWorkflowVariableUtil.parseValue(null, "text") == null
        MetaDataToWorkflowVariableUtil.parseValue("", "number") == null
        MetaDataToWorkflowVariableUtil.parseValue("", "boolean") == null
    }

    def "测试convertValueType方法"() {
        given:
        def data = ["field1": "100", "field2": "true", "field3": "text"]
        def variableList = [
            new com.facishare.bpm.bpmn.VariableExt("activity_0##AccountObj##field1", "number"),
            new com.facishare.bpm.bpmn.VariableExt("activity_0##AccountObj##field2", "boolean")
        ]

        when:
        def result = MetaDataToWorkflowVariableUtil.convertValueType("AccountObj", data, variableList)

        then:
        result["field1"] == 100L
        result["field2"] == true
        result["field3"] == "text"  // 没有对应变量定义，保持原值
    }
}