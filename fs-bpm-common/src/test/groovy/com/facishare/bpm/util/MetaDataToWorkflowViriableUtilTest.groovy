package com.facishare.bpm.util

import spock.lang.Specification


/**
 * Created by <PERSON> on 26/05/2017.
 */
class MetaDataToWorkflowViriableUtilTest extends Specification {
    def "验证变量的获取"() {

        when: "获取结果"
        def result = MetaDataToWorkflowVariableUtil.parseValue(value, type)
        then: "对结果进行判断"
        result == rst
        where: "设置参数"
        msg      | value  | type   || rst
        "测试text" | "1000" | "text" || "1000"
        "测试text" | ["1000"] | "list" || ["1000"]
        "测试text" | "100" | "number" || 100

    }
}