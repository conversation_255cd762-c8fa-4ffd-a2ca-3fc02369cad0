package com.facishare.bpm.util.bpmn

import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import spock.lang.Specification

/**
 * <AUTHOR>
 * @since 6.2
 */
 class ExecutableWorkflowExtTest extends Specification {

    def "验证整个流程的正确性"(){
        given:"字符串"
        def workflowJson="{\"variables\":[{\"id\":\"activity_0##object_RcqWq__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1523275206278##object_RcqWq__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_0##object_RcqWq__c##field_RS9h7__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_0##object_RcqWq__c##name\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_1523275206278##object_RcqWq__c##field_RS9h7__c\",\"type\":{\"name\":\"text\"}}],\"activities\":[{\"name\":\"开始\",\"description\":\"\",\"id\":\"1523275206277\",\"type\":\"startEvent\"},{\"execution\":{\"pass\":[{\"taskType\":\"feed_task\",\"actionMapping\":{\"carbonCopyEmployeeIds\":{},\"attenderEmployeeIds\":{\"person\":[\"2000\"]},\"title\":\"444\",\"content\":\"444\",\"deadLine\":1524514831504}}]},\"taskType\":\"anyone\",\"assignNextTask\":0,\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"自行车(INAG)\",\"executionType\":\"update\",\"actionCode\":\"\",\"entityId\":\"object_RcqWq__c\",\"objectId\":{\"expression\":\"activity_0##object_RcqWq__c\"}},\"externalApplyTask\":0,\"name\":\"业务活动\",\"description\":\"\",\"id\":\"1523275206278\",\"canSkip\":false,\"assignee\":{\"ext_bpm\":[\"instance##owner\"]},\"type\":\"userTask\"},{\"name\":\"结束\",\"description\":\"\",\"id\":\"1523275206282\",\"type\":\"endEvent\"},{\"id\":\"1523365289392\",\"name\":\"分支节点\",\"description\":\"\",\"type\":\"exclusiveGateway\",\"defaultTransitionId\":\"1523365289396\"}],\"name\":\"发送任务2\",\"description\":\"发送任务2\",\"transitions\":[{\"toId\":\"1523275206278\",\"serialNumber\":0,\"id\":\"1523275206280\",\"fromId\":\"1523275206277\"},{\"id\":\"1523365289394\",\"fromId\":\"1523275206278\",\"toId\":\"1523365289392\",\"serialNumber\":1},{\"id\":\"1523365289395\",\"fromId\":\"1523365289392\",\"toId\":\"1523275206278\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"contains\",\"left\":{\"expression\":\"activity_0##object_RcqWq__c##field_RS9h7__c\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"1\"}},{\"type\":\"contains\",\"left\":{\"expression\":\"activity_0##object_RcqWq__c##name\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"123\"}},{\"type\":\"contains\",\"left\":{\"expression\":\"activity_1523275206278##object_RcqWq__c##field_RS9h7__c\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"678\"}}]},{\"type\":\"and\",\"conditions\":[{\"type\":\"contains\",\"left\":{\"expression\":\"activity_0##object_RcqWq__c##field_RS9h7__c\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"6778\"}}]}]},\"description\":\"流程发起数据 / 自行车(INAG) - 多行文本 包含 1 且 <br>流程发起数据 / 自行车(INAG) - 品牌 包含 123 且 <br>业务活动 / 自行车(INAG) - 多行文本 包含 678 或 <br>流程发起数据 / 自行车(INAG) - 多行文本 包含 6778\",\"serialNumber\":2},{\"id\":\"1523365289396\",\"fromId\":\"1523365289392\",\"toId\":\"1523275206282\",\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"contains\",\"left\":{\"expression\":\"activity_0##object_RcqWq__c##field_RS9h7__c\"},\"right\":{\"type\":{\"name\":\"text\"},\"value\":\"123\"}}]}]},\"description\":\"流程发起数据 / 自行车(INAG) - 多行文本 包含 123\",\"serialNumber\":3}],\"sourceWorkflowId\":\"346301025395310593\",\"externalFlow\":0,\"type\":\"workflow_bpm\",\"singleInstanceFlow\":0,\"id\":\"5acb59743db71d97d99c80de\"}"

        when:"转换"
        def workflow=ExecutableWorkflowExt.fromJson(workflowJson)
        then:"验证"
        def transitions=workflow.getTransitions()
        transitions.size()>0

    }
}
