package com.facishare.bpm.util.verifiy;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.WorkflowRule;
import com.facishare.rest.core.util.JsonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.io.IOUtils;

import java.io.FileInputStream;
import java.io.IOException;

/**
 * Created by <PERSON> on 09/06/2017.
 */
public interface BaseValidateHandlerTest {
    default ExecutableWorkflowExt getWorkflow() {
        String workflowJson= null;
        try {
            workflowJson = IOUtils.toString(new FileInputStream(BaseValidateHandlerTest.class.getResource("verify_workflow.data").getPath()));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return getWorkflow(workflowJson);
    }

    default ExecutableWorkflowExt getWorkflow(String json){
        ExecutableWorkflowExt executableWorkflow = ExecutableWorkflowExt.fromJson(json);
        return executableWorkflow;
    }

    default Workflow getWorkflowByJson(RefServiceManager serviceManager,String name,
                                       String entryType,
                                       String entryTypeName,
                                       ExecutableWorkflowExt executableWorkflow,
                                       WorkflowRule rule,
                                       String extension,
                                       Integer externalFlow){
        Workflow workflow = new Workflow(serviceManager, name, entryType, entryTypeName, executableWorkflow, rule,extension, externalFlow);
        return workflow;
    }

    default ExecutableWorkflowExt getWorkflowFromFile(String path) {
        String workflowJson= null;
        try {
            workflowJson = IOUtils.toString(new FileInputStream(BaseValidateHandlerTest.class.getResource("/workflows/"+path).getPath()));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return getWorkflow(workflowJson);
    }


    default <T> T getModelFromFile(String fileName) {
        T t = null;
        try {
            t = JsonUtil.fromJson(
                    IOUtils.toString(getClass().getClassLoader().getResourceAsStream("workflows/" + fileName)),
                    new TypeToken<T>() {
                    }.getType());

        } catch (IOException e) {
            e.printStackTrace();
        }
        return t;
    }

    default Workflow getModelFromFile() {
        Workflow t = null;
        try {
            t = JsonUtil.fromJson(
                    IOUtils.toString(getClass().getClassLoader().getResourceAsStream("workflows/validate_external_message_workflow.json")),
                    new TypeToken<Workflow>() {
                    }.getType());

        } catch (IOException e) {
            e.printStackTrace();
        }
        return t;
    }

    default <T> T getModelFromFile(String fileName,Class<T> clazz){
        T t = null;
        try {
            t = JsonUtil.fromJson(IOUtils.toString(getClass().getClassLoader().getResourceAsStream("workflows/"+fileName)),clazz);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return t;
    }
}
