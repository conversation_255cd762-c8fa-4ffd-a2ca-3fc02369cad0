package com.facishare.bpm.util.verifiy.handler

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.bpmn.UserTaskExt
import com.facishare.bpm.bpmn.WorkflowRule
import com.facishare.bpm.exception.BPMBusinessException
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId
import com.facishare.bpm.util.ExpressionModel
import com.facishare.bpm.util.verifiy.Workflow
import com.facishare.bpm.util.verifiy.handler.AssigneeHandler
import com.facishare.bpm.util.verifiy.handler.after.CustomFunctionValidateActionHandler
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * AssigneeHandler 扩展测试
 */
class AssigneeHandlerExtendedTest extends Specification {

    def assigneeHandler = new AssigneeHandler()
    def mockWorkflow = Mock(Workflow)
    def mockServiceManager = Mock(RefServiceManager)
    def mockUserTask = Mock(UserTaskExt)
    def mockActivity = Mock(ActivityExt)

    def setup() {
        mockWorkflow.getServiceManager() >> mockServiceManager
        mockWorkflow.isExternalFlow() >> false
        mockWorkflow.getUserTasks() >> [mockUserTask]
        mockUserTask.getName() >> "测试节点"
        mockUserTask.isCustomCandidateConfig() >> false
        mockUserTask.getExtensionType() >> ExecutionTypeEnum.userTask
    }

    def "测试外部流程跳过验证"() {
        given:
        mockWorkflow.isExternalFlow() >> true

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        noExceptionThrown()
    }

    def "测试自定义候选配置跳过验证"() {
        given:
        mockUserTask.isCustomCandidateConfig() >> true

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        noExceptionThrown()
    }

    def "测试外部申请任务跳过验证"() {
        given:
        mockUserTask.getExtensionType() >> ExecutionTypeEnum.externalApplyTask

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        noExceptionThrown()
    }

    @Unroll
    def "测试处理人和分组处理人共存异常 - #desc"() {
        given:
        mockUserTask.get(WorkflowKey.ActivityKey.assignee) >> assignee
        mockUserTask.get(WorkflowKey.ActivityKey.groupHandler) >> groupHandler

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)

        where:
        desc                | assignee              | groupHandler
        "都有值"            | ["person": ["123"]]   | [["order": 1]]
        "assignee有值"      | ["person": ["123"]]   | [["order": 1]]
    }

    def "测试处理人类型为assignee但无处理人"() {
        given:
        mockUserTask.get(WorkflowKey.ActivityKey.assigneeType) >> WorkflowKey.ActivityKey.assignee
        mockUserTask.get(WorkflowKey.ActivityKey.assignee) >> ["person": []]
        mockUserTask.get(WorkflowKey.ActivityKey.groupHandler) >> null
        mockUserTask.getAssignee() >> ["person": []]

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试处理人类型为assignee且有有效处理人"() {
        given:
        def assignee = ["person": Sets.newHashSet("123")]
        mockUserTask.get(WorkflowKey.ActivityKey.assigneeType) >> WorkflowKey.ActivityKey.assignee
        mockUserTask.get(WorkflowKey.ActivityKey.assignee) >> assignee
        mockUserTask.get(WorkflowKey.ActivityKey.groupHandler) >> null
        mockUserTask.getAssignee() >> assignee
        mockServiceManager.validateAssignee(_, _, _, _, _, _) >> {}

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        noExceptionThrown()
    }

    def "测试处理人类型为assigneeFunction但函数为空"() {
        given:
        mockUserTask.get(WorkflowKey.ActivityKey.assigneeType) >> WorkflowKey.ActivityKey.assigneeFunction
        mockUserTask.getAssigneeFunction() >> null

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试分组处理人为空"() {
        given:
        mockUserTask.get(WorkflowKey.ActivityKey.assigneeType) >> null
        mockUserTask.get(WorkflowKey.ActivityKey.assignee) >> null
        mockUserTask.get(WorkflowKey.ActivityKey.groupHandler) >> null

        when:
        assigneeHandler.validate(mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    @Unroll
    def "测试validateAssignee方法 - 不支持的处理人类型 #key"() {
        given:
        def assignees = [(key): Sets.newHashSet("value")]

        when:
        AssigneeHandler.validateAssignee("测试", mockActivity, assignees, mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)

        where:
        key << ["invalid_type", "unknown", "custom"]
    }

    @Unroll
    def "测试validateAssignee方法 - 支持的处理人类型 #key"() {
        given:
        def assignees = [(key): Sets.newHashSet("value")]
        mockServiceManager.validateAssignee(_, _, _, _, _, _) >> {}
        mockWorkflow.getPreActivityFun(_) >> { activityId, entityId, flag -> true }

        when:
        AssigneeHandler.validateAssignee("测试", mockActivity, assignees, mockWorkflow)

        then:
        noExceptionThrown()

        where:
        key << ["person", "role", "dept", "dept_leader", "group"]
    }

    def "测试validateAssignee方法 - ext_bpm类型"() {
        given:
        def assignees = ["ext_bpm": Sets.newHashSet("instance##owner")]
        mockWorkflow.getPreActivityFun(_) >> { activityId, entityId, flag -> true }

        when:
        AssigneeHandler.validateAssignee("测试", mockActivity, assignees, mockWorkflow)

        then:
        noExceptionThrown()
    }

    def "测试validateAssignee方法 - ext_bpm类型配置错误"() {
        given:
        def assignees = ["ext_bpm": Sets.newHashSet("invalid_config")]

        when:
        AssigneeHandler.validateAssignee("测试", mockActivity, assignees, mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateAssignee方法 - external_role类型"() {
        given:
        def assignees = ["external_role": Sets.newHashSet("role123")]
        def roles = [new GetOutRolesByTenantId.SimpleRoleResult(roleId: "role123")]
        mockActivity.getLinkApp() >> "testApp"
        mockActivity.getLinkAppType() >> "testType"
        mockServiceManager.getRolesByAppId("testApp", "testType") >> roles

        when:
        AssigneeHandler.validateAssignee("测试", mockActivity, assignees, mockWorkflow)

        then:
        noExceptionThrown()
    }

    def "测试validateAssignee方法 - external_role类型角色不存在"() {
        given:
        def assignees = ["external_role": Sets.newHashSet("nonexistent_role")]
        def roles = [new GetOutRolesByTenantId.SimpleRoleResult(roleId: "role123")]
        mockActivity.getLinkApp() >> "testApp"
        mockActivity.getLinkAppType() >> "testType"
        mockServiceManager.getRolesByAppId("testApp", "testType") >> roles

        when:
        AssigneeHandler.validateAssignee("测试", mockActivity, assignees, mockWorkflow)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateExtBPMSupport方法 - instance##owner"() {
        expect:
        AssigneeHandler.validateExtBPMSupport("instance##owner", mockActivity, mockWorkflow) == true
    }

    def "测试validateExtBPMSupport方法 - applicant##dept_leader"() {
        expect:
        AssigneeHandler.validateExtBPMSupport("applicant##dept_leader", mockActivity, mockWorkflow) == true
    }

    def "测试validateExtBPMSupport方法 - 节点处理人"() {
        given:
        mockWorkflow.getPreActivityFun(_) >> { activityId, entityId, flag -> true }

        expect:
        AssigneeHandler.validateExtBPMSupport("activity_123##assigneeId##leader", mockActivity, mockWorkflow) == true
    }

    def "测试validateExtBPMSupport方法 - 数据相关"() {
        given:
        mockWorkflow.getPreActivityFun(_) >> { activityId, entityId, flag -> true }
        mockWorkflow.getServiceManager() >> mockServiceManager
        mockServiceManager.getDescribe("AccountObj") >> ["name": "客户"]

        expect:
        AssigneeHandler.validateExtBPMSupport("activity_123##AccountObj##owner", mockActivity, mockWorkflow) == true
    }

    def "测试validateExtBPMSupport方法 - 无效配置"() {
        expect:
        AssigneeHandler.validateExtBPMSupport("invalid_config", mockActivity, mockWorkflow) == false
    }

    def "测试checkAssigneeItSelfField方法 - 白名单字段"() {
        given:
        def expressionModel = new ExpressionModel()
        expressionModel.setEntityId("AccountObj")
        expressionModel.setField("owner")
        mockServiceManager.getDescribe("AccountObj") >> ["name": "客户"]

        expect:
        AssigneeHandler.checkAssigneeItSelfField(expressionModel, mockWorkflow) == true
    }

    def "测试checkAssigneeItSelfField方法 - 对象不存在"() {
        given:
        def expressionModel = new ExpressionModel()
        expressionModel.setEntityId("NonExistentObj")
        expressionModel.setField("owner")
        mockServiceManager.getDescribe("NonExistentObj") >> null

        expect:
        AssigneeHandler.checkAssigneeItSelfField(expressionModel, mockWorkflow) == false
    }

    def "测试validateGroupHandler方法 - 空分组处理人"() {
        when:
        AssigneeHandler.validateGroupHandler(mockWorkflow, null, mockUserTask)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateGroupHandler方法 - 空列表"() {
        when:
        AssigneeHandler.validateGroupHandler(mockWorkflow, [], mockUserTask)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateGroupHandler方法 - 缺少order字段"() {
        given:
        def groupHandler = [["assignee": ["person": ["123"]]]]

        when:
        AssigneeHandler.validateGroupHandler(mockWorkflow, groupHandler, mockUserTask)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateGroupHandler方法 - order字段类型错误"() {
        given:
        def groupHandler = [["order": "invalid", "assignee": ["person": ["123"]]]]

        when:
        AssigneeHandler.validateGroupHandler(mockWorkflow, groupHandler, mockUserTask)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateGroupHandler方法 - 缺少默认规则"() {
        given:
        def groupHandler = [
            ["order": 1.0, "assignee": ["person": ["123"]], "rule": new WorkflowRule()]
        ]
        mockServiceManager.validateAssignee(_, _, _, _, _, _) >> {}

        when:
        AssigneeHandler.validateGroupHandler(mockWorkflow, groupHandler, mockUserTask)

        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def "测试validateGroupHandler方法 - 有效的分组处理人"() {
        given:
        def groupHandler = [
            ["order": 0.0, "assignee": ["person": ["123"]]] // 默认规则
        ]
        mockServiceManager.validateAssignee(_, _, _, _, _, _) >> {}
        mockUserTask.getAssignee() >> ["person": ["123"]]

        when:
        AssigneeHandler.validateGroupHandler(mockWorkflow, groupHandler, mockUserTask)

        then:
        noExceptionThrown()
    }
}
