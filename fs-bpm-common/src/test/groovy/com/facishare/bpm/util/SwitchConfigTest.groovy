package com.facishare.bpm.util

import spock.lang.Specification
import spock.lang.Unroll

class SwitchConfigTest extends Specification {
    @Unroll
    def "ignoreDataVersion 测试 #desc"() {
        when:
        def switchVO = new SwitchConfigManager.SwitchConfig(ignoreDataVersion: config)
        then:
        switchVO.ignoreDataVersion(tenantId, entityId) == result
        where:
        desc                      | config                    | tenantId | entityId     || result
        "企业配置为空列表时不忽略版本"   | ["71557": []]             | "71557"  | "AccountObj" || false
        "企业配置指定对象时忽略版本"    | ["71557": ["AccountObj"]] | "71557"  | "AccountObj" || true  
        "企业配置通配符时忽略所有对象版本" | ["71557": ["*"]]          | "71557"  | "AccountObj" || true
        "企业ID不匹配时不忽略版本"     | ["71557": []]             | "71554"  | "AccountObj" || false
        "企业ID不匹配时不忽略版本"     | ["71557": ["AccountObj"]] | "71554"  | "AccountObj" || false
        "企业ID不匹配时不忽略版本"     | ["71557": ["*"]]          | "71554"  | "AccountObj" || false
        "配置为null时不忽略版本"      | null                      | "71554"  | "AccountObj" || false
        "配置为空Map时不忽略版本"     | new HashMap<>()           | "71554"  | "AccountObj" || false
    }
    @Unroll
    def "ignoreDataVersion EntityTenant 测试 #desc"() {
        when:
        def switchVO = new SwitchConfigManager.SwitchConfig(ignoreDataVersionEntityIdAndTenantIds: config)
        then:
        switchVO.ignoreDataVersion(tenantId, entityId) == result
        where:
        desc        | config                    | tenantId | entityId     || result
        "全局配置但企业不匹配"   | ["*": ["71554"]]             | "71557"  | "AccountObj" || false
        "全局配置且企业匹配"   | ["*": ["71557"]]             | "71557"  | "AccountObj" || true
        "对象和企业都匹配" | ["AccountObj": ["71557"]] | "71557"  | "AccountObj" || true
        "对象匹配但企业列表为空"   | ["AccountObj": []]             | "71554"  | "AccountObj" || false
        "配置为null"    | null                      | "71554"  | "AccountObj" || false
    }
}
