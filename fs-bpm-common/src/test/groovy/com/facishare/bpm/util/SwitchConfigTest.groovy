package com.facishare.bpm.util

import spock.lang.Specification
import spock.lang.Unroll

class SwitchConfigTest extends Specification {
    @Unroll
    def "ignoreDataVersion 测试 #desc"() {
        when:
        def switchVO = new SwitchConfigManager.SwitchConfig(ignoreDataVersion: config)
        then:
        switchVO.ignoreDataVersion(tenantId, entityId) == result
        where:
        desc                      | config                    | tenantId | entityId     || result
        "企业配置为空列表时不忽略版本"   | ["71557": []]             | "71557"  | "AccountObj" || false
        "企业配置指定对象时忽略版本"    | ["71557": ["AccountObj"]] | "71557"  | "AccountObj" || true  
        "企业配置通配符时忽略所有对象版本" | ["71557": ["*"]]          | "71557"  | "AccountObj" || true
        "企业ID不匹配时不忽略版本"     | ["71557": []]             | "71554"  | "AccountObj" || false
        "企业ID不匹配时不忽略版本"     | ["71557": ["AccountObj"]] | "71554"  | "AccountObj" || false
        "企业ID不匹配时不忽略版本"     | ["71557": ["*"]]          | "71554"  | "AccountObj" || false
        "配置为null时不忽略版本"      | null                      | "71554"  | "AccountObj" || false
        "配置为空Map时不忽略版本"     | new HashMap<>()           | "71554"  | "AccountObj" || false
    }
    @Unroll
    def "ignoreDataVersion EntityTenant 测试 #desc"() {
        when:
        def switchVO = new SwitchConfigManager.SwitchConfig(ignoreDataVersionEntityIdAndTenantIds: config)
        then:
        switchVO.ignoreDataVersion(tenantId, entityId) == result
        where:
        desc        | config                    | tenantId | entityId     || result
        "全局配置但企业不匹配"   | ["*": ["71554"]]             | "71557"  | "AccountObj" || false
        "全局配置且企业匹配"   | ["*": ["71557"]]             | "71557"  | "AccountObj" || true
        "对象和企业都匹配" | ["AccountObj": ["71557"]] | "71557"  | "AccountObj" || true
        "对象匹配但企业列表为空"   | ["AccountObj": []]             | "71554"  | "AccountObj" || false
        "配置为null"    | null                      | "71554"  | "AccountObj" || false
    }

    def "测试SwitchConfig基本方法"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setOpenRefreshData(true)

        then:
        switchConfig.getOpenRefreshData() == true
    }

    def "测试GrayType枚举"() {
        expect:
        SwitchConfigManager.GrayType.gray.gray() == true
        SwitchConfigManager.GrayType.full.gray() == true
        SwitchConfigManager.GrayType.history.gray() == false
    }

    def "测试KidConfig基本功能"() {
        given:
        def kidConfig = new SwitchConfigManager.KidConfig()
        def detail = new SwitchConfigManager.Detail()
        detail.setEnable(true)
        detail.setTenantIds(["tenant1"])

        when:
        kidConfig.setGrayEnable(true)
        kidConfig.setDetail(detail)

        then:
        kidConfig.isGrayEnable() == true
        kidConfig.getDetail() == detail
    }

    def "测试SwitchConfig更多属性"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setOpenRefreshData(true)
        switchConfig.setSyncDataFailRemindEmpleyee([1, 2, 3])
        switchConfig.setSyncDataFailRetryCount(5)
        switchConfig.setOperateFailNoticeSessionUserId([100, 200])

        then:
        switchConfig.getOpenRefreshData() == true
        switchConfig.getSyncDataFailRemindEmpleyee() == [1, 2, 3]
        switchConfig.getSyncDataFailRetryCount() == 5
        switchConfig.getOperateFailNoticeSessionUserId() == [100, 200]
    }

    def "测试Detail类更多功能"() {
        given:
        def detail = new SwitchConfigManager.Detail()

        when:
        detail.setEnable(false)
        detail.setTenantIds(["tenant1", "tenant2", "tenant3"])

        then:
        detail.isEnable() == false
        detail.getTenantIds() == ["tenant1", "tenant2", "tenant3"]
        detail.getTenantIds().size() == 3
    }

    def "测试Detail类边界情况"() {
        given:
        def detail = new SwitchConfigManager.Detail()

        when:
        detail.setTenantIds([])
        detail.setEnable(true)

        then:
        detail.getTenantIds() == []
        detail.getTenantIds().isEmpty()
        detail.isEnable() == true
    }

    def "测试Detail类null值处理"() {
        given:
        def detail = new SwitchConfigManager.Detail()

        when:
        detail.setTenantIds(null)

        then:
        detail.getTenantIds() == null
    }

    def "测试KidConfig边界情况"() {
        given:
        def kidConfig = new SwitchConfigManager.KidConfig()

        when:
        kidConfig.setGrayEnable(false)
        kidConfig.setDetail(null)

        then:
        kidConfig.isGrayEnable() == false
        kidConfig.getDetail() == null
    }

    def "测试SwitchConfig默认值"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        expect:
        switchConfig.getOpenRefreshData() == false  // 默认值是false，不是null
        switchConfig.getSyncDataFailRemindEmpleyee() == null
        switchConfig.getSyncDataFailRetryCount() == null
        switchConfig.getOperateFailNoticeSessionUserId() == null
    }

    def "测试SwitchConfig空列表处理"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setSyncDataFailRemindEmpleyee([])
        switchConfig.setOperateFailNoticeSessionUserId([])

        then:
        switchConfig.getSyncDataFailRemindEmpleyee() == []
        switchConfig.getOperateFailNoticeSessionUserId() == []
        switchConfig.getSyncDataFailRemindEmpleyee().isEmpty()
        switchConfig.getOperateFailNoticeSessionUserId().isEmpty()
    }

    def "测试SwitchConfig大数值处理"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setSyncDataFailRetryCount(Integer.MAX_VALUE)

        then:
        switchConfig.getSyncDataFailRetryCount() == Integer.MAX_VALUE
    }

    def "测试SwitchConfig负数处理"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setSyncDataFailRetryCount(-1)

        then:
        switchConfig.getSyncDataFailRetryCount() == -1
    }

    def "测试SwitchConfig零值处理"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setSyncDataFailRetryCount(0)

        then:
        switchConfig.getSyncDataFailRetryCount() == 0
    }

    def "测试GrayType所有枚举值"() {
        expect:
        SwitchConfigManager.GrayType.values().length == 3
        SwitchConfigManager.GrayType.valueOf("gray") == SwitchConfigManager.GrayType.gray
        SwitchConfigManager.GrayType.valueOf("full") == SwitchConfigManager.GrayType.full
        SwitchConfigManager.GrayType.valueOf("history") == SwitchConfigManager.GrayType.history
    }

    def "测试复杂对象组合"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()
        def kidConfig = new SwitchConfigManager.KidConfig()
        def detail = new SwitchConfigManager.Detail()

        when:
        detail.setEnable(true)
        detail.setTenantIds(["tenant1", "tenant2"])
        kidConfig.setGrayEnable(true)
        kidConfig.setDetail(detail)
        switchConfig.setOpenRefreshData(true)
        switchConfig.setSyncDataFailRetryCount(3)

        then:
        detail.isEnable() == true
        detail.getTenantIds().size() == 2
        kidConfig.isGrayEnable() == true
        kidConfig.getDetail().isEnable() == true
        switchConfig.getOpenRefreshData() == true
        switchConfig.getSyncDataFailRetryCount() == 3
    }
}
