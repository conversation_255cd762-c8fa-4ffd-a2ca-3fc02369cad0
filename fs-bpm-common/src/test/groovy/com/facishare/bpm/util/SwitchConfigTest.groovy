package com.facishare.bpm.util

import spock.lang.Specification
import spock.lang.Unroll

class SwitchConfigTest extends Specification {
    @Unroll
    def "ignoreDataVersion 测试 #desc"() {
        when:
        def switchVO = new SwitchConfigManager.SwitchConfig(ignoreDataVersion: config)
        then:
        switchVO.ignoreDataVersion(tenantId, entityId) == result
        where:
        desc                      | config                    | tenantId | entityId     || result
        "企业配置为空列表时不忽略版本"   | ["71557": []]             | "71557"  | "AccountObj" || false
        "企业配置指定对象时忽略版本"    | ["71557": ["AccountObj"]] | "71557"  | "AccountObj" || true  
        "企业配置通配符时忽略所有对象版本" | ["71557": ["*"]]          | "71557"  | "AccountObj" || true
        "企业ID不匹配时不忽略版本"     | ["71557": []]             | "71554"  | "AccountObj" || false
        "企业ID不匹配时不忽略版本"     | ["71557": ["AccountObj"]] | "71554"  | "AccountObj" || false
        "企业ID不匹配时不忽略版本"     | ["71557": ["*"]]          | "71554"  | "AccountObj" || false
        "配置为null时不忽略版本"      | null                      | "71554"  | "AccountObj" || false
        "配置为空Map时不忽略版本"     | new HashMap<>()           | "71554"  | "AccountObj" || false
    }
    @Unroll
    def "ignoreDataVersion EntityTenant 测试 #desc"() {
        when:
        def switchVO = new SwitchConfigManager.SwitchConfig(ignoreDataVersionEntityIdAndTenantIds: config)
        then:
        switchVO.ignoreDataVersion(tenantId, entityId) == result
        where:
        desc        | config                    | tenantId | entityId     || result
        "全局配置但企业不匹配"   | ["*": ["71554"]]             | "71557"  | "AccountObj" || false
        "全局配置且企业匹配"   | ["*": ["71557"]]             | "71557"  | "AccountObj" || true
        "对象和企业都匹配" | ["AccountObj": ["71557"]] | "71557"  | "AccountObj" || true
        "对象匹配但企业列表为空"   | ["AccountObj": []]             | "71554"  | "AccountObj" || false
        "配置为null"    | null                      | "71554"  | "AccountObj" || false
    }

    def "测试SwitchConfig基本方法"() {
        given:
        def switchConfig = new SwitchConfigManager.SwitchConfig()

        when:
        switchConfig.setOpenRefreshData(true)

        then:
        switchConfig.getOpenRefreshData() == true
    }

    def "测试GrayType枚举"() {
        expect:
        SwitchConfigManager.GrayType.gray.gray() == true
        SwitchConfigManager.GrayType.full.gray() == true
        SwitchConfigManager.GrayType.history.gray() == false
    }

    def "测试KidConfig基本功能"() {
        given:
        def kidConfig = new SwitchConfigManager.KidConfig()
        def detail = new SwitchConfigManager.Detail()
        detail.setEnable(true)
        detail.setTenantIds(["tenant1"])

        when:
        kidConfig.setGrayEnable(true)
        kidConfig.setDetail(detail)

        then:
        kidConfig.isGrayEnable() == true
        kidConfig.getDetail() == detail
    }
}
