package com.facishare.bpm.util

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.exception.BPMBusinessExceptionCode
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra
import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions
import com.facishare.bpm.model.resource.newmetadata.UpdateData
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch
import com.facishare.bpm.remote.model.org.Dept
import com.facishare.bpm.util.verifiy.BaseValidateHandlerTest
import com.facishare.bpm.util.verifiy.ValidateResult
import com.facishare.bpm.util.verifiy.Workflow
import com.facishare.bpm.util.verifiy.util.PreActivityFunction
import com.facishare.bpm.util.verifiy.util.ValidateVariableAndContentUtils
import com.facishare.bpm.utils.model.UtilConstans
import com.facishare.flow.element.plugin.api.FlowElement
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper
import com.facishare.rest.core.model.RemoteContext
import com.fxiaoke.i18n.SupportLanguage
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.api.Localization
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.i18n.util.LangIndex
import com.google.common.collect.Maps
import org.apache.commons.collections.MapUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import spock.lang.Specification
import spock.lang.Unroll

import java.util.function.Function

import static com.facishare.bpm.model.paas.engine.bpm.BPMConstants.ENTITY_FIELD_SPLIT

/**
 *
 *
 * @since 6.3
 * <AUTHOR> ( Aaron )
 * @Date on 2018/5/25
 */
class ValidateVariableAndContentUtilsTest extends Specification implements BaseValidateHandlerTest {

    protected initI18nClient() {
        def field = I18nClient.class.getDeclaredField("impl")
        field.setAccessible(true)

        def stub = Stub(I18nServiceImpl)
        field.set(I18nClient.getInstance(), stub)
        stub.getOrDefault(_ as String, _ as Long, _ as String, _ as String) >> {
            String key, Long tenantId, String lang, String defaultVal ->
                return defaultVal
        }
        stub.get(_ as String, _ as Long) >> {
            String key, Long tenantId ->
                return new Localization();
        }
        LangIndex.getInstance().supportLanguageList = [new SupportLanguage()]
        LangIndex.getInstance().languageCodeMapping = [:]
    }

    def "对象校验"() {
        when:
        def result = ValidateVariableAndContentUtils.validateEntities(getServiceRef(), entityIds, new PreActivityFunction() {
            @Override
            boolean apply(String activityId, String entity, boolean isSelf) {
                return true
            }
        })
        then:
        result == rst
        where:
        entityIds                                          || rst
        ["activity_0##object_0iOs7__c"]                    || ValidateResult.ok()
        ["activity_0##object_0iOs7__c##lookup1"]           || ValidateResult.ok()
        ["activity_0##SelfRef##object_0iOs7__c##lookup1"]  || ValidateResult.ok()
        ["activity_0##SelfRef##object_0iOs7__c"]           || ValidateResult.ok()
        ["activity_1527755187475##object_0iOs7__c##owner"] || ValidateResult.ok()
        ["activity_0##result"]                             || ValidateResult.ok()

    }

    @Unroll
    def "字段校验或流程变量"() {
        when:
        initI18nClient()
        Workflow workflow = getWorkflowByJson(getServiceRef(),"测试用的123", "object_7prLH__c", "油焖大虾",
                getWorkflow("{\"creator\":\"1002\",\"externalFlow\":0,\"modifier\":\"1002\",\"entityId\":\"object_7prLH__c\",\"history\":false,\"singleInstanceFlow\":0,\"modifyTime\":1730277938043,\"enable\":true,\"linkAppEnable\":false,\"name\":\"测试用的123\",\"description\":\"\",\"activities\":[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":\"1730277886154\"},{\"type\":\"userTask\",\"bpmExtension\":{\"executionType\":\"update\",\"executionName\":\"编辑对象\",\"entityId\":\"object_7prLH__c\",\"entityName\":\"油焖大虾\",\"objectId\":{\"expression\":\"activity_0##object_7prLH__c\"},\"objectName\":\"流程发起数据/油焖大虾\",\"layoutType\":\"none\",\"defaultButtons\":{},\"outerTenantType\":\"outerTenantField\"},\"canSkip\":false,\"linkAppEnable\":false,\"name\":\"业务活动\",\"id\":\"17302778861541\",\"assignee\":{\"person\":[\"1007\"]},\"assigneeType\":\"assignee\",\"taskType\":\"anyone\",\"assignNextTask\":0,\"externalApplyTask\":0,\"custom\":false,\"customCandidateConfig\":false,\"importObject\":false},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":\"1730277917441\"}],\"transitions\":[{\"id\":\"1730277886154111\",\"fromId\":\"1730277886154\",\"toId\":\"17302778861541\",\"serialNumber\":0},{\"id\":\"1730277922505\",\"fromId\":\"17302778861541\",\"toId\":\"1730277917441\",\"serialNumber\":0}],\"variables\":[{\"id\":\"activity_0##object_7prLH__c\",\"type\":{\"name\":\"text\"}},{\"id\":\"activity_17302778861541##object_7prLH__c\",\"type\":{\"name\":\"text\"}}],\"id\":\"6721f226d3bc5a0001e6b408\",\"sourceWorkflowId\":\"6721f226d3bc5a0001e6b407\",\"createTime\":1730277926455,\"tenantId\":\"71557\",\"appId\":\"BPM\",\"type\":\"workflow_bpm\"}"),
                null,
                "{\"id\":null,\"pools\":[{\"id\":null,\"name\":null,\"lanes\":[{\"id\":\"173027788615411\",\"name\":\"阶段\",\"description\":\"\",\"activities\":[\"1730277886154\",\"17302778861541\",\"1730277917441\"],\"order\":1}]}],\"diagram\":[{\"id\":\"173027788615411\",\"attr\":{\"x\":80.0,\"y\":60.0,\"width\":220.0,\"height\":540.0}},{\"id\":\"1730277886154111\",\"attr\":{\"d\":\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277922505\",\"attr\":{\"d\":\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\",\"fromPosition\":\"bottom\",\"toPosition\":\"top\"}},{\"id\":\"1730277886154\",\"attr\":{\"x\":160.0,\"y\":125.0}},{\"id\":\"1730277917441\",\"attr\":{\"x\":120.0,\"y\":430.0}},{\"id\":\"17302778861541\",\"attr\":{\"x\":110.0,\"y\":260.0}}],\"svg\":\"<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" width=\\\"100%\\\" height=\\\"100%\\\" xmlns:xlink=\\\"http://www.w3.org/1999/xlink\\\" class=\\\"x6-graph-svg\\\" viewBox=\\\"80 60 220 540\\\"><defs style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><marker refX=\\\"-1\\\" id=\\\"marker-v54-2980955686\\\" overflow=\\\"visible\\\" orient=\\\"auto\\\" markerUnits=\\\"userSpaceOnUse\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path stroke=\\\"#545861\\\" fill=\\\"#545861\\\" transform=\\\"rotate(180)\\\" d=\\\"M 0 0 L 12 -4 L 12 4 Z\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></marker></defs><g class=\\\"x6-graph-svg-viewport\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-graph-svg-primer\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-graph-svg-stage\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g data-cell-id=\\\"173027788615411\\\" data-shape=\\\"lane\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(80,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" height=\\\"16\\\" width=\\\"220\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#dee1e8\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" x=\\\"1\\\" y=\\\"1\\\" stroke-opacity=\\\"0\\\" width=\\\"32\\\" height=\\\"16\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"start\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" width=\\\"270\\\" transform=\\\"matrix(1,0,0,1,35,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</tspan></text><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#181c25\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,15,10)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">阶段</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">1</tspan></text><rect fill=\\\"none\\\" stroke=\\\"#368dff\\\" stroke-width=\\\"1\\\" fill-opacity=\\\"0\\\" width=\\\"220\\\" height=\\\"540\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><image style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g data-cell-id=\\\"1730277886154111\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 190 185 L 190 232 C 190 237.33 188.33 240 185 240 L 185 240 C 181.67 240 180 242.67 180 248 L 180 258.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,189.78128051757812,234.9930419921875)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g data-cell-id=\\\"1730277922505\\\" data-shape=\\\"edge\\\" class=\\\"x6-cell x6-edge\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><path fill=\\\"none\\\" cursor=\\\"pointer\\\" stroke=\\\"transparent\\\" stroke-linecap=\\\"round\\\" stroke-linejoin=\\\"round\\\" stroke-width=\\\"10\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><path fill=\\\"none\\\" pointer-events=\\\"none\\\" stroke-linejoin=\\\"round\\\" stroke=\\\"#545861\\\" stroke-width=\\\"1.5\\\" d=\\\"M 180 316 L 180 402 C 180 407.33 177.33 410 172 410 L 158 410 C 152.67 410 150 412.67 150 418 L 150 428.5\\\" marker-end=\\\"url(#marker-v54-2980955686)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-edge-labels\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-edge-label\\\" data-index=\\\"0\\\" cursor=\\\"default\\\" transform=\\\"matrix(1,0,0,1,180,366)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#fff\\\" rx=\\\"3\\\" ry=\\\"3\\\" width=\\\"0\\\" height=\\\"0\\\" transform=\\\"matrix(1,0,0,1,0,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text fill=\\\"#000\\\" font-size=\\\"14\\\" text-anchor=\\\"middle\\\" text-vertical-anchor=\\\"middle\\\" pointer-events=\\\"none\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><!--z-index:2--><g data-cell-id=\\\"1730277886154\\\" data-shape=\\\"startEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(160,125)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#16B4AB\\\" stroke=\\\"#16B4AB\\\" stroke-width=\\\"1\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">开始</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"1730277917441\\\" data-shape=\\\"endEvent\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(120,430)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle fill=\\\"#ffffff\\\" stroke=\\\"#737C8C\\\" stroke-width=\\\"4\\\" org-stroke=\\\"#737C8C\\\" fill-opacity=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"30\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><circle fill=\\\"#737C8C\\\" stroke=\\\"#333333\\\" stroke-width=\\\"0\\\" cx=\\\"30\\\" cy=\\\"30\\\" r=\\\"25\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#ffffff\\\" text-anchor=\\\"middle\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" transform=\\\"matrix(1,0,0,1,30,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">结束</tspan></text><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,30,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,60,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,30,60)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,30)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><g data-cell-id=\\\"17302778861541\\\" data-shape=\\\"business\\\" class=\\\"x6-cell x6-node\\\" transform=\\\"translate(110,260)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#368DFF\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><rect fill=\\\"#E6F4FF\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" rx=\\\"2\\\" class=\\\"x6-hightlight-body\\\" width=\\\"140\\\" height=\\\"56\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><text font-size=\\\"14\\\" xml:space=\\\"preserve\\\" fill=\\\"#000000\\\" text-anchor=\\\"left\\\" font-family=\\\"Arial, helvetica, sans-serif\\\" text=\\\"业务活动\\\" transform=\\\"matrix(1,0,0,1,37,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\"><title style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</title><tspan dy=\\\"0.3em\\\" class=\\\"v-line\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px Arial, helvetica, sans-serif; outline: rgb(96, 98, 102) none 0px;\\\">业务活动</tspan></text><image x=\\\"10\\\" y=\\\"15\\\" width=\\\"24\\\" height=\\\"24\\\" xlink:href=\\\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNzI2ODI0NzAyNzQwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQ3MjA3IiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiPjxwYXRoIGQ9Ik04OTYgMTI4YzIzLjU1MiAwIDQyLjY4OCAxOS4wNzIgNDIuNjg4IDQyLjY4OHY2ODIuNjI0QTQyLjY4OCA0Mi42ODggMCAwIDEgODk2IDg5NkgxMjhhNDIuNjg4IDQyLjY4OCAwIDAgMS00Mi42ODgtNDIuNjg4VjE3MC42ODhDODUuMzEyIDE0Ny4wNzIgMTA0LjQ0OCAxMjggMTI4IDEyOGg3Njh6IG0tNDIuNjg4IDg1LjMxMkgxNzAuNjg4djU5Ny4zNzZoNjgyLjYyNFYyMTMuMzEyek00MzIgMzIwYzYwLjk5MiAwIDkwLjg4IDQxLjY2NCA5MC44OCAxMDcuMjY0IDAgMzAuMDgtMTYuODk2IDU4LjI0LTM1LjEzNiA3OC4zMzYtNi4yMDggMTEuMi05LjkyIDIyLjQtOS45MiAzMi41MTIgMCA4LjcwNCA5Ljg1NiAxOC42ODggMjMuOTM2IDIxLjQ0bDEzLjE4NCAyLjQ5NiAyLjYyNCAwLjY0YzM4LjQgOS42IDgwLjQ0OCAyOC42MDggODguODk2IDU5LjUybDEuMzQ0IDcuMzYgMC4xOTIgNC4wOTZ2MTkuNTg0YTI4LjAzMiAyOC4wMzIgMCAwIDEtMjAuMjg4IDI3LjI2NGwtOC40NDggMi4zMDQtMjAuMjI0IDUuMDU2QzUyNS40NCA2OTUuNjggNDc4LjcyIDcwNCA0MzIgNzA0Yy00MS42IDAtODMuMDcyLTYuNTkyLTExNS4zOTItMTMuNTA0bC0yMi4zMzYtNS4xODRjLTYuNzItMS43MjgtMTIuOC0zLjMyOC0xNy45Mi00LjhhMjcuODQgMjcuODQgMCAwIDEtMTkuODQtMjEuOTUyTDI1NiA2NTMuMjQ4VjYzMy42YzAtMzQuODE2IDQxLjQwOC01Ny4wODggODEuNzkyLTY4LjczNmw4LjY0LTIuMzA0IDE1LjgwOC0zLjA3MmMxMy45NTItMi43NTIgMjMuOTM2LTExLjk2OCAyMy45MzYtMjEuNDRhNjkuMTIgNjkuMTIgMCAwIDAtOS45Mi0zMi40NDhjLTE4LjI0LTIwLjIyNC0zNS4yLTQ4LjM4NC0zNS4yLTc4LjRDMzQxLjEyIDM2MS42NjQgMzcxLjA3MiAzMjAgNDMyIDMyMHoiIGZpbGw9IiM3MzdDOEMiIHAtaWQ9IjQ3MjA4Ij48L3BhdGg+PC9zdmc+\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/><g class=\\\"x6-port x6-port-top\\\" transform=\\\"matrix(1,0,0,1,70,0)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"top\\\" port-group=\\\"top\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-right\\\" transform=\\\"matrix(1,0,0,1,140,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"right\\\" port-group=\\\"right\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-bottom\\\" transform=\\\"matrix(1,0,0,1,70,56)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"bottom\\\" port-group=\\\"bottom\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g><g class=\\\"x6-port x6-port-left\\\" transform=\\\"matrix(1,0,0,1,0,28)\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: move; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><circle r=\\\"6\\\" fill=\\\"#fff\\\" stroke=\\\"#5498ff\\\" port=\\\"left\\\" port-group=\\\"left\\\" class=\\\"x6-port-body\\\" magnet=\\\"true\\\" stroke-width=\\\"1\\\" style=\\\"visibility: hidden; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: crosshair; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px; transition: opacity 0.3s;\\\"/></g></g><!--z-index:3--></g><g class=\\\"x6-graph-svg-decorator\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tools x6-node-tools\\\" data-cell-id=\\\"173027788615411\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: nw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: ne-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,60)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: sw-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,80,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g><g class=\\\"x6-cell-tool x6-node-tool x6-cell-tool-resize\\\" data-cell-id=\\\"173027788615411\\\" data-tool-name=\\\"resize\\\" style=\\\"cursor: se-resize; border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\" transform=\\\"matrix(1,0,0,1,300,600)\\\"><circle r=\\\"6\\\" magnet=\\\"true\\\" stroke=\\\"#5498ff\\\" stroke-width=\\\"1\\\" fill=\\\"#fff\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></g></g><g class=\\\"x6-graph-svg-overlay\\\" style=\\\"border: 0px none rgb(96, 98, 102); color: rgb(96, 98, 102); cursor: grab; font: 14px / 21px arial, tahoma, &quot;Hiragino Sans GB&quot;, &quot;Hiragino Sans GB W3&quot;, &quot;Microsoft Yahei&quot;, 宋体; outline: rgb(96, 98, 102) none 0px;\\\"/></g></svg>\",\"workflowId\":null}",
                0)
        def result = ValidateVariableAndContentUtils.validateContent(workflow, fields, new PreActivityFunction() {
            @Override
            boolean apply(String activityId, String entity, boolean isSelf) {
                return true
            }
        })
        then:
        result == rst
        where:
        fields||rst
        ["\${activity_0##object_0iOs7__c##field1}","\${activity_0##object_0iOs7__c##field2}","\${activity_0##object_0iOs7__c##lookup1##lookupfield1}","\${activity_0##object_0iOs7__c##lookup1##lookupfield2"]||ValidateResult.ok()
        ["\${activity_0##SelfRef##object_0iOs7__c##field1}","\${activity_0##SelfRef##object_0iOs7__c##field2}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield1}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield2"]||ValidateResult.ok()
        ["\${activity_0##SelfRef##object_0iOs7__c##field1}","\${activity_0##SelfRef##object_0iOs7__c##field3}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield1}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield2"]||ValidateResult.fail("主对象 的field3 已被禁用，请确认")
        ["\${activity_0##SelfRef##object_0i__c##field1}"]||ValidateResult.fail("对象不存在或已禁用")
        ["\${a}","\${activity_0##SelfRef##object_0iOs7__c##field1}","\${activity_0##SelfRef##object_0iOs7__c##field2}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield1}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield2}"]||ValidateResult.fail("业务流程不支变量 a ")
        ["\${workflowName}","\${activity_0##SelfRef##object_0iOs7__c##field1}","\${activity_0##SelfRef##object_0iOs7__c##field2}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield1}","\${activity_0##SelfRef##object_0iOs7__c##lookup1##lookupfield2}"]||ValidateResult.ok()
        ["\${activity_0##SelfRef##object_0iOs7__c##@OWNER_MAIN_DEPT_PATH}"]||ValidateResult.ok()
        ["\${activity_0##result}"]||ValidateResult.ok()
        ["\${activity_1527755187475##object_0iOs7__c##owner##sex}","\${activity_1527755187475##object_0iOs7__c##field1}"]||ValidateResult.ok()
        ["\${object_0iOs7__c.owner.sex}"]||ValidateResult.fail("业务流程不支变量 object_0iOs7__c.owner.sex ")
        ["\${activity_1527755187475##opinion}"]||ValidateResult.ok()
    }


    RefServiceManager getServiceRef() {
        return new RefServiceManagerAbs() {

            Logger log = LoggerFactory.getLogger(this.getClass());

            @Override
            Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
                return null
            }
            
            @Override
            Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
                return null
            }
            
            @Override
            Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData, boolean skipRelevantTeam) {
                return null
            }

            @Override
            Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
                return null
            }

            @Override
            Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
                return null
            }

            @Override
            boolean isDataOwnerByQueryMetadata(String apiName, String objectId) {
                return false
            }

            @Override
            Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
                return null
            }

            @Override
            Map<String, Object> getDescribe(String entityId) {
                Map<String, Map<String, Object>> cache = Maps.newConcurrentMap()

                def fields = [:]
                fields.put("lookup1", [type: "object_reference", target_api_name: "object0iOs7_other__c"])
                fields.put("field1", [label: "field1", type: "text", "is_active": true])
                fields.put("field2", [label: "field2", type: "text", "is_active": true])
                fields.put("field3", [label: "field3", type: "text", "is_active": false])
                fields.put("owner", [label: "field3", type: "employee", "is_active": false])
                cache.put("object_0iOs7__c", ["fields": fields, "display_name": "主对象"])


                fields = [:]
                fields.put("lookupfield1", [label: "lookupfield1", type: "text", "is_active": true])
                fields.put("lookupfield2", [label: "lookupfield2", type: "text", "is_active": true])
                fields.put("lookupfield3", [label: "lookupfield3", type: "text", "is_active": true])
                cache.put("object0iOs7_other__c", ["fields": fields, "display_name": "被lookup对象"])

                fields = [:]
                fields.put("lookupfield1", [label: "lookupfield1", type: "text", "is_active": true])
                fields.put("lookupfield2", [label: "lookupfield2", type: "text", "is_active": true])
                fields.put("sex", [label: "性别", type: "text", "is_active": true])
                cache.put("PersonnelObj", ["fields": fields, "display_name": "人员对象化"])


                def rst = cache.get(entityId)
                if (rst == null) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_OBJECT_DELETE_DISABLE,entityId)
                }
                return rst
            }

            @Override
             Map<String, Map<String, Object>> getDescribes(Collection<String> entityIds) {
                Map<String, Map<String, Object>> rst = Maps.newHashMap();
                for (String entityId : entityIds) {
                    log.info("entityId:{}", entityId);
                    if (entityId.contains(ENTITY_FIELD_SPLIT)) {
                        String[] ids = entityId.split(ENTITY_FIELD_SPLIT);
                        String rootEntityId = ids[0];
                        String lookupField = ids[1];
                        Map<String, Object> desc = getDescribe(rootEntityId);
                        rst.put(rootEntityId, desc);
                        Map<String, Object> fieldDesc = (Map<String, Object>) ((Map) desc.get(BPMConstants.MetadataKey.fields)).get(lookupField);
                        if (MapUtils.isEmpty(fieldDesc)) {
                            // throw new BPMWorkflowDefVerifyException(desc.get(BPMConstants.MetadataKey.displayName) + " 的字段 " + lookupField + " 已经删除或停用,请确认");
                            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_ENTITY_FIELD_DELETE_DISABLE, desc.get(BPMConstants.MetadataKey.displayName), lookupField);
                        }
                        log.info("entityId:{},field:{},fieldType:{}", entityId, lookupField, fieldDesc.get(BPMConstants.MetadataKey.type));
                        if (BPMConstants.MetadataKey.objectReference.equals(fieldDesc.get(BPMConstants.MetadataKey.type))) {
                            //将 RootEntityId##lookupField 作为key放入缓存
                            rst.put(entityId, getDescribe((String) fieldDesc.get(BPMConstants.MetadataKey.targetApiName)));
                        } else if (fieldDesc.get(BPMConstants.MetadataKey.type).equals(UtilConstans.EMPLOYEE)) {
                            //将 RootEntityId##lookupField 作为key放入缓存
                            rst.put(entityId, getDescribe(UtilConstans.ORG_USER));
                        } else {
                            log.info("getDescribes missing:{} {}", entityId, desc);
                        }
                    } else if (BPMConstants.SPECIAL_ENTITYIDS.contains(entityId)) {
                        log.info("WEB Transitive EntityId :{}", entityId);
                    } else {
                        Map<String, Object> desc = getDescribe(entityId);
                        rst.put(entityId, desc);
                    }
                }
                return rst;
            }

            @Override
            boolean workflowIsExists(String tenantId, String outLineId, String sourceWorkflowId) {
                return false
            }

            @Override
            Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
                return null
            }

            @Override
            boolean getDeliveryNoteEnable() {
                return false
            }

            @Override
            GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
                return null
            }

            @Override
            Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
                return null
            }

            @Override
            GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
                return null
            }

            @Override
            boolean dataPrivilege(String entityId, String objectId) {
                return false
            }

            @Override
            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }

            Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck) {
                return null
            }

            @Override
            Map<String, String> getAppActions(String entityId, Boolean isExternalFlow, String appId, Integer appType) {
                return null
            }

            @Override
            boolean isOuterMainOwner() {
                return false
            }

            @Override
            GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
                return null
            }

            @Override
            Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
                return null
            }

            @Override
            def <E> E getObjectFromCache(String key, Function<String, E> function) {
                return null
            }

            @Override
            Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet) {
                return null
            }

            @Override
            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction) {
                return null
            }

            List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName) {
                return null
            }

            @Override
            Map<String, String> findDataExhibitButton(String apiName, String objectId, String usePageType) {
                return null
            }

            @Override
            boolean checkEmailEnable(String sender) {
                return false
            }

            @Override
            FlowElementWrapper getFlowElementWrapper(String elementApiName) {
                return null
            }

            @Override
            FlowElement getFlowElement(String elementApiName) {
                return null
            }

            @Override
            String getI18nLinkAppName(String linkApp, String linkAppName) {
                return null
            }

            @Override
            boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
                return false
            }

            @Override
            Map<String, Object> getFlowConfig(String terminal, List<String> types) {
                return null
            }

            @Override
            boolean isNeedDiscussButton() {
                return false
            }

            @Override
            UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
                return null
            }
        }
    }
}
