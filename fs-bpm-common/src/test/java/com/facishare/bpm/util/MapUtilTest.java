package com.facishare.bpm.util;

import com.facishare.bpm.utils.MapUtil;
import com.facishare.bpm.utils.ParallelUtils;
import com.facishare.rest.core.util.JacksonUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class MapUtilTest {

    private Map<Object, Object> data = Maps.newHashMap();

    @Before
    public void setup() {
        data.put("a", "my map");
        data.put("b", 392332423);
        data.put("c", 9999L);

        data.put("d", 9999.99);
        data.put("e", 9999.99d);
        data.put("f", 9999.99f);
        data.put("g", '2');
        data.put("h", Lists.newArrayList("1", "2", "3", "4"));
        data.put("i", Lists.newArrayList());
        data.put("j", Maps.newHashMap());
        Map<String, String> k = Maps.newHashMap();
        k.put("k1", "v1");
        k.put("k2", "v2");
        k.put("k3", "v3");
        data.put("k", k);

        Map<String, Boolean> l = Maps.newHashMap();
        l.put("k1", null);
        l.put("k2", true);
        l.put("k3", false);
        data.put("l", l);
        data.put("m", null);
        data.put("n", Lists.newArrayList(1, "2", false, "4"));

    }

    @Test
    public void getString() {
    }

    @Test
    public void getInteger() {
    }

    @Test
    public void getBool() {
    }

    @Test
    public void getMap() {
    }

    @Test
    public void getListOfGeneric() {
        List<String> ih = MapUtil.instance.getListOfGeneric(data, "h");
        System.out.println(JacksonUtil.toJson(ih));
        List<String> in = MapUtil.instance.getListOfGeneric(data, "n");
        System.out.println(JacksonUtil.toJson(in));
    }

    @Test
    public void getMapOfGeneric() {
        Map<String, Boolean> rst = MapUtil.instance.getMapOfGeneric(data, "k");
        System.out.println(JsonUtil.toJson(rst));
        Map<String, String> rst2 = MapUtil.instance.getMapOfGeneric(data, "l");
        System.out.println(JsonUtil.toJson(rst2));
        Map<String, String> rst3 = MapUtil.instance.getMapOfGeneric(data, "m");
        System.out.println(rst3);

        //float rst4 = MapUtil.instance.getMapOfGeneric(data, "e");
        //System.out.println(rst4);
    }

    @Test
    public void getMapUtil() {
        for (int i = 0; i < 100; i++) {
            try {
                ParallelUtils.createParallelTask().submit("getMapUtil", () -> {
                    System.out.println("CurThreadId:" + Thread.currentThread().getId() + ":" + MapUtil.instance.hashCode());
                }).await(100, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                e.printStackTrace();
            }
        }
    }


    @Test
    public void getList() {
    }
}
