package com.facishare.bpm.util;

import com.facishare.bpm.bpmn.VariableExt;
import com.facishare.bpm.exception.BPMTaskExecuteException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * MetaDataToWorkflowVariableUtil 单元测试
 */
public class MetaDataToWorkflowVariableUtilTest {

    @Test
    public void testConvertValueTypeWithValidData() {
        // 准备测试数据
        String apiName = "AccountObj";
        Map<String, Object> data = Maps.newHashMap();
        data.put("name", "测试客户");
        data.put("amount", "1000.50");
        data.put("count", "100");
        data.put("isActive", "true");
        data.put("description", "描述信息");

        List<VariableExt> variableList = Lists.newArrayList();
        variableList.add(new VariableExt("activity_0##AccountObj##name", "text"));
        variableList.add(new VariableExt("activity_0##AccountObj##amount", "number"));
        variableList.add(new VariableExt("activity_0##AccountObj##count", "number"));
        variableList.add(new VariableExt("activity_0##AccountObj##isActive", "boolean"));
        variableList.add(new VariableExt("activity_0##SalesOrderObj##orderId", "text")); // 不同 apiName

        // 执行转换
        Map<String, Object> result = MetaDataToWorkflowVariableUtil.convertValueType(apiName, data, variableList);

        // 验证结果
        assertEquals("测试客户", result.get("name"));
        assertEquals(1000.5, result.get("amount"));
        assertEquals(100L, result.get("count"));
        assertEquals(true, result.get("isActive"));
        assertEquals("描述信息", result.get("description")); // 没有对应变量定义，保持原值
    }

    @Test
    public void testConvertValueTypeWithEmptyVariableList() {
        String apiName = "AccountObj";
        Map<String, Object> data = Maps.newHashMap();
        data.put("name", "测试客户");
        data.put("amount", "1000");

        List<VariableExt> variableList = Lists.newArrayList();

        Map<String, Object> result = MetaDataToWorkflowVariableUtil.convertValueType(apiName, data, variableList);

        // 没有变量定义，所有值应该保持原样
        assertEquals("测试客户", result.get("name"));
        assertEquals("1000", result.get("amount"));
    }

    @Test(expected = BPMTaskExecuteException.class)
    public void testConvertValueTypeWithInvalidNumberValue() {
        String apiName = "AccountObj";
        Map<String, Object> data = Maps.newHashMap();
        data.put("amount", "invalid_number");

        List<VariableExt> variableList = Lists.newArrayList();
        variableList.add(new VariableExt("activity_0##AccountObj##amount", "number"));

        MetaDataToWorkflowVariableUtil.convertValueType(apiName, data, variableList);
    }

    @Test
    public void testParseValueWithTextType() {
        // 测试文本类型
        assertEquals("hello", MetaDataToWorkflowVariableUtil.parseValue("hello", "text"));
        assertEquals("123", MetaDataToWorkflowVariableUtil.parseValue(123, "text"));
        assertEquals("123", MetaDataToWorkflowVariableUtil.parseValue(123.0, "text"));
        assertEquals("", MetaDataToWorkflowVariableUtil.parseValue("", "text"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "text"));
    }

    @Test
    public void testParseValueWithNumberType() {
        // 测试数字类型 - 整数
        assertEquals(123L, MetaDataToWorkflowVariableUtil.parseValue("123", "number"));
        assertEquals(123L, MetaDataToWorkflowVariableUtil.parseValue(123, "number"));
        
        // 测试数字类型 - 小数
        assertEquals(123.45, MetaDataToWorkflowVariableUtil.parseValue("123.45", "number"));
        assertEquals(123.45, MetaDataToWorkflowVariableUtil.parseValue(123.45, "number"));
        
        // 测试空值
        assertNull(MetaDataToWorkflowVariableUtil.parseValue("", "number"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "number"));
    }

    @Test
    public void testParseValueWithBooleanType() {
        // 测试布尔类型
        assertEquals(true, MetaDataToWorkflowVariableUtil.parseValue("true", "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue("false", "boolean"));
        assertEquals(true, MetaDataToWorkflowVariableUtil.parseValue(true, "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue(false, "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue("invalid", "boolean"));
        
        // 测试空值
        assertNull(MetaDataToWorkflowVariableUtil.parseValue("", "boolean"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "boolean"));
    }

    @Test
    public void testParseValueWithListType() {
        // 测试列表类型
        List<String> list = Lists.newArrayList("item1", "item2");
        assertEquals(list, MetaDataToWorkflowVariableUtil.parseValue(list, "list"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "list"));
    }

    @Test
    public void testParseValueWithNullValue() {
        // 测试 null 值对所有类型都返回 null
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "text"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "number"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "boolean"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue(null, "list"));
    }

    @Test
    public void testParseValueWithEmptyString() {
        // 测试空字符串
        assertEquals("", MetaDataToWorkflowVariableUtil.parseValue("", "text"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue("", "number"));
        assertNull(MetaDataToWorkflowVariableUtil.parseValue("", "boolean"));
    }

    @Test
    public void testParseValueWithDoubleToText() {
        // 测试 Double 转换为文本时的特殊处理
        assertEquals("123", MetaDataToWorkflowVariableUtil.parseValue(123.0, "text"));
        assertEquals("123.5", MetaDataToWorkflowVariableUtil.parseValue(123.5, "text"));
        assertEquals("0", MetaDataToWorkflowVariableUtil.parseValue(0.0, "text"));
    }

    @Test
    public void testParseValueWithUnknownType() {
        // 测试未知类型，应该返回原始字符串值
        assertEquals("test", MetaDataToWorkflowVariableUtil.parseValue("test", "unknown"));
        assertEquals("123", MetaDataToWorkflowVariableUtil.parseValue(123, "unknown"));
    }

    @Test
    public void testConvertValueTypeWithComplexVariableIds() {
        // 测试复杂的变量 ID 格式
        String apiName = "AccountObj";
        Map<String, Object> data = Maps.newHashMap();
        data.put("field1", "100");
        data.put("field2", "200");

        List<VariableExt> variableList = Lists.newArrayList();
        variableList.add(new VariableExt("activity_1##AccountObj##field1", "number"));
        variableList.add(new VariableExt("activity_0##AccountObj##field2##subfield", "number")); // 4个部分，不匹配
        variableList.add(new VariableExt("activity_0##OtherObj##field1", "number")); // 不同 apiName
        variableList.add(new VariableExt("invalid_format", "number")); // 无效格式

        Map<String, Object> result = MetaDataToWorkflowVariableUtil.convertValueType(apiName, data, variableList);

        // field1 应该被转换为数字，因为 activity_1 匹配变量定义
        assertEquals(100L, result.get("field1"));
        // field2 应该保持字符串，因为变量 ID 格式不匹配
        assertEquals("200", result.get("field2"));
    }

    @Test
    public void testConvertValueTypeWithEmptyData() {
        String apiName = "AccountObj";
        Map<String, Object> data = Maps.newHashMap();
        List<VariableExt> variableList = Lists.newArrayList();
        variableList.add(new VariableExt("activity_0##AccountObj##field1", "number"));

        Map<String, Object> result = MetaDataToWorkflowVariableUtil.convertValueType(apiName, data, variableList);

        assertTrue(result.isEmpty());
    }

    @Test
    public void testParseValueWithSpecialNumbers() {
        // 测试特殊数字格式
        assertEquals(0L, MetaDataToWorkflowVariableUtil.parseValue("0", "number"));
        assertEquals(-123L, MetaDataToWorkflowVariableUtil.parseValue("-123", "number"));
        assertEquals(-123.45, MetaDataToWorkflowVariableUtil.parseValue("-123.45", "number"));
        assertEquals(1.23E10, MetaDataToWorkflowVariableUtil.parseValue("1.23E10", "number"));
    }

    @Test
    public void testParseValueWithBooleanEdgeCases() {
        // 测试布尔值的边界情况
        assertEquals(true, MetaDataToWorkflowVariableUtil.parseValue("TRUE", "boolean"));
        assertEquals(true, MetaDataToWorkflowVariableUtil.parseValue("True", "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue("FALSE", "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue("False", "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue("0", "boolean"));
        assertEquals(false, MetaDataToWorkflowVariableUtil.parseValue("1", "boolean"));
    }

    @Test
    public void testConvertValueTypePreservesOriginalDataStructure() {
        // 测试转换不会修改原始数据结构
        String apiName = "AccountObj";
        Map<String, Object> originalData = Maps.newHashMap();
        originalData.put("field1", "100");
        originalData.put("field2", "text");

        List<VariableExt> variableList = Lists.newArrayList();
        variableList.add(new VariableExt("activity_0##AccountObj##field1", "number"));

        Map<String, Object> result = MetaDataToWorkflowVariableUtil.convertValueType(apiName, originalData, variableList);

        // 原始数据应该保持不变
        assertEquals("100", originalData.get("field1"));
        assertEquals("text", originalData.get("field2"));

        // 结果数据应该被转换
        assertEquals(100L, result.get("field1"));
        assertEquals("text", result.get("field2"));
    }
}
