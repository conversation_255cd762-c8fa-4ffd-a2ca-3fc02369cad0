package com.facishare.bpm.util;

import com.facishare.bpm.utils.i18n.I18NExpression;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Description :
 * <AUTHOR> cuiyongxu
 * @Date : 2021/12/30-3:14 下午
 **/
public class I18NParseTest {


    /*public static final String TAG_SERVER = "server";
    public static final String TAG_PRE = "pre_object";
    public static final String TAG_CUSTOM = "custom_object";
    public static final String TAG_OPTIONS = "metadata_options";
    public static final String TAG_FLOW = "flow";


    @BeforeClass
    public static void init2() {
        I18N.setContext("71557", "en");
        I18nClient.getInstance().realTime(true);
        I18nClient.getInstance().initWithTags(TAG_SERVER, TAG_PRE, TAG_CUSTOM, TAG_OPTIONS, TAG_FLOW);
    }

    *//**
     * 多对象嵌套 不会存在异常
     *//*
    @Test
    public void multiObjectNesting() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");


        Model model2 = new Model();
        model2.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model2.setName("test2");


        model.setModel(model2);


        I18NParser.parse("71557", model);
        System.out.println(model.toString());
    }


    *//**
     * 内部嵌套自己,本身会出现异常
     *//*
    @Test(expected = StackOverflowError.class)
    public void multiObjectSelf() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");
        model.setModel(model);
        System.out.println(model.toString());
    }


    *//**
     * 解决自身死循环
     *//*
    @Test
    public void multiObjectSelfI18N() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");
        model.setModel(model);
        I18NParser.parse("71557", model);
    }


    @Test
    public void multiObjectNestingList() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");


        Model model2 = new Model();
        model2.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model2.setName("test2");


        model.setModels(Lists.newArrayList(model2));


        I18NParser.parse("71557", model);
        System.out.println(JsonUtil.toJson(model));
    }

    @Test
    public void i18ndepth2() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");
        model.setModel(model);

        I18NParser.parse("71557",model);
        System.out.println(JsonUtil.toJson(model));
    }

    @Test
    public void i18ndepth3() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");
        model.setModel(model);

        I18NParser.parse("71557", model);
        System.out.println(JsonUtil.toJson(model));
//        System.out.println(JacksonUtil.toJson(model));
    }


    @Test
    public void i18ndepth4() {
        Model model = new Model();
        model.setWorkflowId("61c99ddb03b840464e2e70d0");//flow.61c99ddb03b840464e2e70d0.name
        model.setName("test1");
        model.setItems(Lists.newArrayList());

        model.getItems().add(model.getItems());

        //I18NParser.parse("71557", model, I18NEntity.of(model.getWorkflowId()));
//        System.out.println(JacksonUtil.toJson(model));
        System.out.println(JsonUtil.toJson(model));
        List a = Lists.newArrayList();
        List b = Lists.newArrayList(a);
        a.add(b);
    }*/

}


@Data
class Model {

    private String workflowId;
    @I18NExpression(relation = "${workflowId}.name")
    private String name;

    @I18NExpression(drill = true)
    private Model model;


    @I18NExpression(drill = true)
    private List<Model> models;

    @I18NExpression(drill = true)
    private List items;


    @I18NExpression(drill = true)
    private Map<String, Model> modelMaps;
}
