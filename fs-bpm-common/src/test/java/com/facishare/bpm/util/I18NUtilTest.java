package com.facishare.bpm.util;

import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.i18n.BPMI18N;
import com.github.houbb.opencc4j.core.Segment;
import com.github.houbb.opencc4j.core.ZhConvert;
import com.github.houbb.opencc4j.core.impl.CharSegment;
import com.github.houbb.opencc4j.core.impl.HuabanSegment;
import com.github.houbb.opencc4j.core.impl.ToTraditonZhConvert;
import com.github.houbb.paradise.common.util.CollectionUtil;
import com.github.houbb.paradise.common.util.StringUtil;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * https://github.com/houbb/opencc4j
 */
public class I18NUtilTest {


    /**
     * 读取本地的cvs文件和all.cvs文件进行比较,打印出差异化的
     */
    @Test
    public void readLocalI18nCSV() {
        ZhConvert zhConvert = new ToTraditonZhConvert();
        Segment segment = getSegment(true);

        LocalI18NCSVResource localI18NCSVResource = new LocalI18NCSVResource();
        System.out.println("key,zh-CN,zh-TW,en,tags,tenant_id");
        for (BPMI18N bpmi18N : BPMI18N.values()) {
            String key = bpmi18N.key;
            String value = localI18NCSVResource.getText(key, "tenant_id");
            if (Strings.isNullOrEmpty(value)) {
                String zh = bpmi18N.zh_CN.replace(",", "，");
                System.out.println(bpmi18N.key + ","
                        + zh + ","
                        + convert(zh, segment, zhConvert) + ","
                        + ","
                        + "server,"
                        + "0");
            }
        }

    }


    @Test
    public void readLocalI18n() {
        for (BPMBusinessExceptionCode code : BPMBusinessExceptionCode.values()) {
            try {
                BPMI18N.valueOf(code.name());
            }catch (Exception e) {

            }

        }
    }



    /**
     * 通过BPMI18N 对象,生成csv文件
     */
    @Test
    public void createBPMI18NCSV() {
        ZhConvert zhConvert = new ToTraditonZhConvert();
        Segment segment = getSegment(true);


        System.out.println("key,zh-CN,zh-TW,en,tags,tenant_id");
        for (BPMI18N bpmi18N : BPMI18N.values()) {

            String zh = bpmi18N.zh_CN.replace(",", "，");

            System.out.println(bpmi18N.key + ","
                    + zh + ","
                    + convert(zh, segment, zhConvert) + ","
                    + ","
                    + "server,"
                    + "0");
            //System.out.println(bpmi18N.key + "," + bpmi18N.zh_CN);

        }
    }

    private static Segment getSegment(boolean huabanSegment) {
        return (Segment) (huabanSegment ? new HuabanSegment() : new CharSegment());
    }

    private static String convert(String original, Segment segment, ZhConvert zhConvert) {
        if (StringUtil.isEmpty(original)) {
            return original;
        } else {
            List<String> stringList = segment.seg(original);
            if (CollectionUtil.isEmpty(stringList)) {
                return original;
            } else {
                StringBuilder stringBuilder = new StringBuilder();
                Iterator var5 = stringList.iterator();

                while (var5.hasNext()) {
                    String string = (String) var5.next();
                    String result = zhConvert.convert(string);
                    stringBuilder.append(result);
                }

                return stringBuilder.toString();
            }
        }
    }

    /**
     * 通过BPMI18N对象,重新生成内部枚举,主要解决存在英文符号的问题
     */
    @Test
    public void resetI18NEnum() {
        for (BPMI18N bpmi18N : BPMI18N.values()) {

            StringBuilder stringBuilder = new StringBuilder();
            String name = bpmi18N.name();
            String key = bpmi18N.key;
            String zh_CN = formatExpression(bpmi18N.zh_CN.replace(",", "，"));
            String since = bpmi18N.since;

            stringBuilder.append(name)
                    .append("(")
                    .append("\"")
                    .append(key)
                    .append("\"")
                    .append(",")
                    .append("\"")
                    .append(zh_CN)
                    .append("\"")
                    .append(",")
                    .append("\"")
                    .append(since)
                    .append("\"")
                    .append(")")
                    .append(",")
                    .append("\n");
            System.out.println(stringBuilder.toString());
        }
    }


    /**
     * 将 %s 修改为递增的{...}
     */
    private String formatExpression(String source) {
        String newString = "";
        String symbol = "%s";
        if (source.contains(symbol)) {
            for (int i = 0; i < appearNumber(source, symbol); i++) {
                if (i == 0) {
                    newString = source.replaceFirst(symbol, "{" + i + "}");
                } else {
                    newString = newString.replaceFirst(symbol, "{" + i + "}");
                }
            }
        } else {
            newString = source;
        }
        return newString;
    }

    /**
     * 获取字符串中有几个 findText出现
     *
     * @param srcText  %s%s我爱你中国
     * @param findText %s
     * @return 2
     */
    private static int appearNumber(String srcText, String findText) {
        int count = 0;
        Pattern p = Pattern.compile(findText);
        Matcher m = p.matcher(srcText);
        while (m.find()) {
            count++;
        }
        return count;
    }

    /**
     * 将 %s 修改为递增的{...}
     */
    @Test
    public void placeholderAssignment() {
        System.out.println(formatExpression("我爱你中国%s"));
        System.out.println(formatExpression("%s我爱你中国"));
        System.out.println(formatExpression("%s%s我爱你中国"));
        System.out.println(formatExpression("我爱你%s中国"));
        System.out.println(formatExpression("我%s爱你%s中国"));
        System.out.println(formatExpression("我%s爱你%s中国%s"));
        System.out.println(formatExpression("我%s爱你%s中国%s%s%s%s"));
    }
}

class LocalI18NCSVResource {
    private static final Logger log = LoggerFactory.getLogger(com.facishare.paas.I18NCSVResource.class);
    private Map<String, CSVRecord> i18nMap = Maps.newHashMap();

    public LocalI18NCSVResource() {
        this.init("key", new String[0], "classpath:all.csv");
    }

    public String getText(String key, String language) {
        CSVRecord record = (CSVRecord) this.i18nMap.get(key);
        if (record == null) {
            return "";
        } else {
            try {
                return record.get(language);
            } catch (Exception var6) {
                return "";
            }
        }
    }


    public void init(String i18key, String[] headers, String resourceFilePath) {
        log.warn("scan dir {} language resource", resourceFilePath);

        try {
            PathMatchingResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resourcePatternResolver.getResources(resourceFilePath);
            Resource[] var6 = resources;
            int var7 = resources.length;

            for (int var8 = 0; var8 < var7; ++var8) {
                Resource resource = var6[var8];
                //log.warn("scan language resource:{}", resource.getFilename());
                InputStream in = resource.getInputStream();
                CSVFormat format = CSVFormat.DEFAULT.withHeader(headers).withSkipHeaderRecord().withIgnoreHeaderCase().withTrim();

                try {
                    CSVParser csvParser = CSVParser.parse(in, Charsets.UTF_8, format);
                    List<CSVRecord> records = csvParser.getRecords();
                    records.forEach((record) -> {

                        CSVRecord old = i18nMap.get(record.get(i18key));
                        if (old != null) {
                           // log.warn("重复的key:{}", record.get(i18key));
                        } else {
                            this.i18nMap.put(record.get(i18key), record);
                        }
                        //log.info("scan language resource:{}", record.toMap().values());
                    });
                } catch (IOException var14) {
                    throw new RuntimeException("loading csv i18n resources error!", var14);
                }
            }
        } catch (IOException var15) {
            log.warn("init language resource:{} failed，", resourceFilePath, var15);
        }

    }
}

