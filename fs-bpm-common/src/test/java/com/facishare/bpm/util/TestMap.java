package com.facishare.bpm.util;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.ExpressionServiceImpl;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import groovy.json.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Created by <PERSON> on 26/05/2017.
 */
public class TestMap {

    @Test
    public void testIterator() {

        List<Integer> queryResult = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8, 9, 9);

//        queryResult.forEach(query -> {
//            if (query == 6) {
//                System.out.println("return:" + query);
//                return;
//            }
//            System.out.println("-->" + query);
//        });

        for (Integer query : queryResult) {
            if (query == 6) {
                System.out.println("return:" + query);
                return;
            }
            System.out.println("-->" + query);
        }
        ;
    }


    @Test
    public void aadddd() {
        Map<String, Object> bindings = Maps.newHashMap();
        bindings.put("a", 11.2);
        bindings.put("b", 60);
        ExpressionService service = new ExpressionServiceImpl();
        BigDecimal ev = service.evaluate("a/b", bindings);
        System.out.println(ev.doubleValue());
    }


    @Test
    public void aadddd111() {
        Map<String, List<String>> rst = Maps.newHashMap();
        rst.computeIfAbsent("object1", s -> Lists.newArrayList()).add("123");
        rst.computeIfAbsent("object1", s -> Lists.newArrayList()).add("123");
        System.out.println(JsonUtil.toPrettyJson(rst));
        rst.computeIfAbsent("object2", new Function<String, List<String>>() {
            @Override
            public List<String> apply(String s) {
                System.out.println(s);
                return Lists.newArrayList();
            }
        }).add("1234");
        System.out.println(JsonUtil.toPrettyJson(rst));


        rst.computeIfPresent("object1", (s, strings) -> {
            strings.add("a");
            return strings;
        });
//
//        Map<String,String> testMap2=Maps.newHashMap();
//
//        System.out.println(testMap2.getOrDefault("1","1"));//1 null  1
//        testMap2.put("1",null);
//        System.out.println(testMap2.getOrDefault("1","1"));//1 1  null
    }

    @Test
    public void testSet() {
        List<String> gray = Lists.newArrayList("fs", "fktest", "489068", "484603", "421080", "fstesthui", "539168", "539158", "424259", "259335", "gzlzchem", "gdsbjt", "gzcsmcs", "zy2017", "442746", "fxiaokehz08", "fxiaokehz06", "484265", "181273", "185394", "195597", "195727", "205685", "353127", "470324", "472546", "475400", "476619", "488857", "488983", "492507", "495462", "495524", "497273", "499153", "501066", "501743", "502024", "505433", "506016", "510665", "513719", "514870", "515374", "515739", "517576", "519496", "519710", "525311", "531065", "532928", "535841", "537653", "537844", "537847", "537921", "537983", "538199", "538207", "538209", "538211", "538212", "cocokou", "262265", "bjlxqx", "bjyxyl", "bjzddfxxk", "chenyiyanshi", "chigojy", "csmkhyyz", "dalianbeileimao", "destiny", "dttjblm", "dwjfxtest", "feihu9", "fengyunnc", "fenxiangxiaoke30", "festfs", "fhzd88", "488048", "fshzcsm2", "fskhyyb", "488065", "fstest00", "fstest0208", "fstest112358", "fstest18", "fstest336", "fstest911", "fstestfw", "fstesthehui", "fstestjijinzb", "fstestjscm", "fstestliuyi", "fstestmz", "fstestnmzb", "fstestny", "fstestpx", "fstestrisun", "fstestsy123", "fstestwusheng", "fstestzdh", "fswzz19", "fsxszx", "fszhang", "fxiaokehz05", "fxiaokevip", "fxnanchang", "fxxkjs", "gfnczh", "gktddykw", "grn283002", "gxnnrz", "gxxjrny", "gxzgwlkj", "gzfxhzp", "gzsdld", "gzzscq", "handan", "haofenxiangcs", "hemeili1745", "hzbtkj", "hzfgskhysb", "hzfxxk998", "ihengyou888", "jmzerol", "joeysie0824", "juming", "jxyaxun", "kangli", "liushen", "ljwl8848", "luleitest", "lxblgzs", "nbfxiaoke", "nmghtsy", "nomuzhuanyong", "nongmuhangye", "qingyun", "qudaocsm1", "qudaocsm2", "sales2017", "sdqsjt", "sdzswl", "sheryi", "shkabm", "shkat1", "fshzcsm1", "support2", "testcnl", "testfengtao", "testwhbj1988", "testwhbj6013", "testwhbj6231", "tianxingyanshi", "tjkhyb", "wangqijun88", "whyeasy", "wjing123", "xty213", "xydjwl", "yanzi2017", "yiyibu", "yuanjiancs", "yybhuidu", "zhaochengwei", "zhaoyu11", "zqw792979465", "zsfsxr", "zzfujia", "465578");
        List<String> gray5_5 = Lists.newArrayList("fenxiangxiaoke1", "fenxiangxiaoke2", "fenxiangxiaoke3", "fenxiangxiaoke4", "fenxiangxiaoke5", "fenxiangxiaoke6", "fenxiangxiaoke7", "fenxiangxiaoke8", "fenxiangxiaoke9", "fenxiangxiaoke10", "fenxiangxiaoke11", "fenxiangxiaoke12", "fenxiangxiaoke13", "fenxiangxiaoke14", "fenxiangxiaoke15", "fenxiangxiaoke16", "fenxiangxiaoke17", "fenxiangxiaoke18", "fenxiangxiaoke19", "fenxiangxiaoke20", "fenxiangxiaoke21", "fenxiangxiaoke22", "fenxiangxiaoke23", "fenxiangxiaoke24", "fenxiangxiaoke25", "fenxiangxiaoke26", "fenxiangxiaoke27", "fenxiangxiaoke28", "fenxiangxiaoke29", "fenxiangxiaoke30", "dalianbeileimao", "lxblgzs", "nongmuhangye", "support2", "qudaocsm1", "fstestnc", "fstestpx", "fxnanchang", "fktest", "489068", "443199", "418398", "253937", "fstesthui", "484603", "428580", "514378", "465578", "wxcszh", "wxcszh1", "wxcszh2", "wxcszh3", "wxcszh4", "wxcszh5", "wxcszh6", "wxcszh7", "353127", "fstestnmzb", "524745", "523945", "cqhlxcs", "tcyyxx", "sales2017", "khcggl", "fstestbaoya", "195597", "gsdlbkj", "fs", "bjlbejy", "353127", "fstestnmzb", "524745", "bjlbejy");
        gray5_5.removeAll(gray);
        System.out.println(StringUtils.join(gray5_5, ","));
    }


    final static String fieldStrTemplate = "{\n" +
            "            \"type\": \"%s\",\n" +
            "                    \"define_type\": \"%s\",\n" +
            "                    \"is_required\": %s,\n" +
            "                    \"is_readonly\": %s,\n" +
            "                    \"api_name\": \"%s\",\n" +
            "                    \"label\": \"%s\",\n" +
            "                    \"description\": \"%s\"" +
            "        }";

    @Test
    public void aaa() {
        Map<String, Object> field = Maps.newHashMap();
        field.put("label", "未成交客户项目描述/直接描述成\"新客户拓展，推广XX产品/方案\"");
        String fieldStr = String.format(fieldStrTemplate, field.get(BPMConstants.MetadataKey.type),
                field.getOrDefault(BPMConstants.MetadataKey.defineType, BPMConstants.MetadataKey.define_type_custom),
                field.get(WorkflowKey.ActivityKey.ExtensionKey.required), field.get(WorkflowKey.ActivityKey.ExtensionKey.readonly),
                field.get(WorkflowKey.ActivityKey.ExtensionKey.name), StringEscapeUtils.escapeJava((String) field.get(WorkflowKey.ActivityKey.ExtensionKey.label)),
                StringEscapeUtils.escapeJava((String) field.get(WorkflowKey.ActivityKey.ExtensionKey.label)));
        Map result = JsonUtil.fromJson(fieldStr, Map.class);
        System.out.println(JsonUtil.toJson(result));
    }

    @Test
    public void bbb() {
        Map<String, Object> field = Maps.newHashMap();
        field.put("label", "未成交客户项目描述/直接描述成\"新客户拓展，推广XX产品/方案\"");
        field.put("description", "未成交客户项目描述/直接描述成\"新客户拓展，推广XX产品/方案\"");
        field.put(BPMConstants.MetadataKey.type, "number");
        field.put(BPMConstants.MetadataKey.defineType, "package");
        field.put(WorkflowKey.ActivityKey.ExtensionKey.required, "true");


        Map result = JsonUtil.fromJson(fieldStrTemplate, Map.class);
        result.put(BPMConstants.MetadataKey.type, field.get(BPMConstants.MetadataKey.type));
        result.put(BPMConstants.MetadataKey.defineType, field.getOrDefault(BPMConstants.MetadataKey.defineType, BPMConstants.MetadataKey.define_type_custom));
        result.put(WorkflowKey.ActivityKey.ExtensionKey.required, field.get(WorkflowKey.ActivityKey.ExtensionKey.required));
        result.put(WorkflowKey.ActivityKey.ExtensionKey.readonly, field.get(WorkflowKey.ActivityKey.ExtensionKey.readonly));
        result.put(WorkflowKey.ActivityKey.ExtensionKey.name, field.get(WorkflowKey.ActivityKey.ExtensionKey.name));
        result.put(WorkflowKey.ActivityKey.ExtensionKey.label, field.get(WorkflowKey.ActivityKey.ExtensionKey.label));
        result.put(WorkflowKey.ActivityKey.ExtensionKey.description, field.get(WorkflowKey.ActivityKey.ExtensionKey.label));


        System.out.println(JsonUtil.toJson(result));
    }

    enum aaaa {
        aa, cc
    }

    @Test
    public void testenum() {
        System.out.println(aaaa.valueOf("aa"));
    }
}
