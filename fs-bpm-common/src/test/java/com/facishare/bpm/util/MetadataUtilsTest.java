package com.facishare.bpm.util;

import com.facishare.bpm.exception.BPMMetaDataException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.resource.metadata.RW;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MetadataUtils的单元测试类
 * 
 * <AUTHOR>
 * @since 7.0.0
 */
public class MetadataUtilsTest {

    @Test
    public void testIsNullOrEmptyObject() {
        // 测试null值
        Assert.assertTrue(MetadataUtils.isNullOrEmptyObject(null));

        // 测试空List
        Assert.assertTrue(MetadataUtils.isNullOrEmptyObject(new ArrayList<>()));

        // 测试非空List
        ArrayList<String> nonEmptyList = new ArrayList<>();
        nonEmptyList.add("test");
        Assert.assertFalse(MetadataUtils.isNullOrEmptyObject(nonEmptyList));

        // 测试空Map
        Assert.assertTrue(MetadataUtils.isNullOrEmptyObject(new HashMap<>()));

        // 测试非空Map
        HashMap<String, String> nonEmptyMap = new HashMap<>();
        nonEmptyMap.put("key", "value");
        Assert.assertFalse(MetadataUtils.isNullOrEmptyObject(nonEmptyMap));

        // 测试空字符串
        Assert.assertTrue(MetadataUtils.isNullOrEmptyObject(""));

        // 测试非空字符串
        Assert.assertFalse(MetadataUtils.isNullOrEmptyObject("test"));
    }

    @Test
    public void testValidateAndCorrectDataValue() {
        // 准备测试数据
        Map<String, Object> data = new HashMap<>();
        data.put("numberField", "123.45");
        data.put("timeField", "1708999200000");
        data.put("version", "1.0");

        Map<String, Object> describe = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();
        
        // 设置number类型字段描述
        Map<String, Object> numberFieldDesc = new HashMap<>();
        numberFieldDesc.put("type", "number");
        fields.put("numberField", numberFieldDesc);

        // 设置time类型字段描述
        Map<String, Object> timeFieldDesc = new HashMap<>();
        timeFieldDesc.put("type", "time");
        fields.put("timeField", timeFieldDesc);

        // 设置version字段描述
        Map<String, Object> versionFieldDesc = new HashMap<>();
        versionFieldDesc.put("type", "number");
        fields.put("version", versionFieldDesc);

        describe.put("fields", fields);

        // 执行测试
        Map<String, Object> result = MetadataUtils.validateAndCorrectDataValue(data, describe);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals("123.45", result.get("numberField").toString());
        Assert.assertEquals("1708999200000", result.get("timeField").toString());
        Assert.assertEquals(1, result.get("version")); // 验证version被转换为整数
    }

    @Test(expected = BPMMetaDataException.class)
    public void testValidateAndCorrectDataValueWithNullDescribe() {
        Map<String, Object> data = new HashMap<>();
        MetadataUtils.validateAndCorrectDataValue(data, null);
    }

    @Test
    public void testSortMapByKey() {
        // 准备测试数据
        Map<String, String> unsortedMap = new HashMap<>();
        unsortedMap.put("c", "value3");
        unsortedMap.put("a", "value1");
        unsortedMap.put("b", "value2");

        // 执行测试
        Map<String, String> sortedMap = MetadataUtils.sortMapByKey(unsortedMap);

        // 验证结果
        Assert.assertEquals(3, sortedMap.size());
        Assert.assertEquals("value1", sortedMap.get("a"));
        Assert.assertEquals("value2", sortedMap.get("b"));
        Assert.assertEquals("value3", sortedMap.get("c"));
    }

    @Test
    public void testGetFieldAccess() {
        // 准备测试数据
        Map<String, Integer> permissions = new HashMap<>();
        permissions.put("field1", RW.Read.value);
        permissions.put("field2", RW.Write.value);

        // 测试Read权限
        Assert.assertEquals(RW.Read, MetadataUtils.getFieldAccess(permissions, "field1"));

        // 测试Write权限
        Assert.assertEquals(RW.Write, MetadataUtils.getFieldAccess(permissions, "field2"));

        // 测试未定义权限的字段
        Assert.assertEquals(RW.Write, MetadataUtils.getFieldAccess(permissions, "field3"));

        // 测试null权限
        Assert.assertEquals(RW.Write, MetadataUtils.getFieldAccess(null, "field1"));
    }

    @Test
    public void testTransferInteger() {
        // 准备测试数据
        Map<String, Object> data = new HashMap<>();
        data.put("version", "1.0");
        data.put("count", "2.0");
        data.put("normal", "3");

        List<String> fieldApiNames = Lists.newArrayList("version", "count", "normal");

        // 执行测试
        MetadataUtils.transferInteger(data, fieldApiNames);

        // 验证结果
        Assert.assertEquals(1, data.get("version"));
        Assert.assertEquals(2, data.get("count"));
        Assert.assertEquals(3, data.get("normal"));
    }

    @Test
    public void testGetAppointFieldDescribeByFieldCondition() {
        // 准备测试数据
        Map<String, Object> matadataDescribes = new HashMap<>();
        Map<String, Map<String, Object>> fields = new HashMap<>();

        Map<String, Object> field1 = new HashMap<>();
        field1.put("type", "text");
        field1.put("label", "Field 1");

        Map<String, Object> field2 = new HashMap<>();
        field2.put("type", "number");
        field2.put("label", "Field 2");

        fields.put("field1", field1);
        fields.put("field2", field2);

        matadataDescribes.put(BPMConstants.MetadataKey.fields, fields);

        // 执行测试
        Map result = MetadataUtils.getAppointFieldDescribeByFieldCondition(
            matadataDescribes, "type", "number");

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals("Field 2", result.get("label"));
    }

    @Test
    public void testSignInRelatedMethods() {
        // 准备签到组件描述数据
        Map<String, Object> signInDescribe = new HashMap<>();
        Map<String, Object> signInFields = new HashMap<>();
        signInFields.put(BPMConstants.MetadataKey.SIGN_IN_STATES_FIELD, "signInState");
        signInFields.put(BPMConstants.MetadataKey.SIGN_OUT_STATES_FIELD, "signOutState");
        signInDescribe.put(BPMConstants.MetadataKey.fields, signInFields);
        signInDescribe.put(BPMConstants.MetadataKey.IS_ENABLE_SIGN_OUT, true);

        // 准备数据
        Map<String, Object> matadataData = new HashMap<>();
        matadataData.put("signInState", BPMConstants.MetadataKey.SIGN_IN_COMPLETE);
        matadataData.put("signOutState", BPMConstants.MetadataKey.SIGN_IN_COMPLETE);

        // 测试签到状态
        Assert.assertTrue(MetadataUtils.objectIsSignIn(signInDescribe, matadataData));

        // 测试签退状态
        Assert.assertTrue(MetadataUtils.objectIsSignOut(signInDescribe, matadataData));

        // 测试签退禁用状态
        Assert.assertFalse(MetadataUtils.objectIsDisableSignOut(signInDescribe));
    }

    @Test
    public void testBPMFormUpdateDataTypeNumber() {
        // 测试 number 类型的数据修正
        Map<String, Object> fieldDesc = new HashMap<>();
        fieldDesc.put("type", "number");
        fieldDesc.put("label", "数字字段");
        fieldDesc.put("api_name", "number_field");

        // 测试正常数值
        Assert.assertEquals("123", MetadataUtils.BPMFormUpdateDataType.number.correct(fieldDesc, "123"));
        
        // 测试null值
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.number.correct(fieldDesc, null));
        
        // 测试空字符串
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.number.correct(fieldDesc, "  "));
    }

    @Test
    public void testBPMFormUpdateDataTypeTime() {
        // 测试 time 类型的数据修正
        Map<String, Object> fieldDesc = new HashMap<>();
        fieldDesc.put("type", "time");
        fieldDesc.put("label", "时间字段");
        fieldDesc.put("api_name", "time_field");

        // 测试正常时间戳
        Assert.assertEquals("1708999200000", 
            MetadataUtils.BPMFormUpdateDataType.time.correct(fieldDesc, "1708999200000"));
        
        // 测试特殊值 0（自定义对象时间字段8:00的情况）
        Assert.assertEquals("0", 
            MetadataUtils.BPMFormUpdateDataType.time.correct(fieldDesc, "0"));
        
        // 测试null值
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.time.correct(fieldDesc, null));
        
        // 测试空字符串
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.time.correct(fieldDesc, ""));
    }

    @Test
    public void testBPMFormUpdateDataTypeDateTime() {
        // 测试 date_time 类型的数据修正
        Map<String, Object> fieldDesc = new HashMap<>();
        fieldDesc.put("type", "date_time");
        fieldDesc.put("label", "日期时间字段");
        fieldDesc.put("api_name", "datetime_field");

        // 测试正常时间戳
        Assert.assertEquals("1708999200000", 
            MetadataUtils.BPMFormUpdateDataType.date_time.correct(fieldDesc, "1708999200000"));
        
        // 测试null值
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.date_time.correct(fieldDesc, null));
        
        // 测试空字符串
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.date_time.correct(fieldDesc, ""));
        
        // 测试0值（对于date_time类型，0是无效值）
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.date_time.correct(fieldDesc, "0"));
    }

    @Test
    public void testBPMFormUpdateDataTypeDate() {
        // 测试 date 类型的数据修正
        Map<String, Object> fieldDesc = new HashMap<>();
        fieldDesc.put("type", "date");
        fieldDesc.put("label", "日期字段");
        fieldDesc.put("api_name", "date_field");

        // 测试正常日期时间戳
        Assert.assertEquals("1708999200000", 
            MetadataUtils.BPMFormUpdateDataType.date.correct(fieldDesc, "1708999200000"));
        
        // 测试null值
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.date.correct(fieldDesc, null));
        
        // 测试空字符串
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.date.correct(fieldDesc, ""));
    }

    @Test
    public void testBPMFormUpdateDataTypeFormula() {
        // 测试 formula 类型的数据修正
        Map<String, Object> fieldDesc = new HashMap<>();
        fieldDesc.put("type", "formula");
        fieldDesc.put("label", "公式字段");
        fieldDesc.put("api_name", "formula_field");

        // 测试日期类型的返回值
        fieldDesc.put("return_type", "date");
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, ""));
        Assert.assertEquals("1708999200000", 
            MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, "1708999200000"));

        // 测试日期时间类型的返回值
        fieldDesc.put("return_type", "date_time");
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, ""));
        Assert.assertEquals("1708999200000", 
            MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, "1708999200000"));

        // 测试时间类型的返回值
        fieldDesc.put("return_type", "time");
        Assert.assertNull(MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, ""));
        Assert.assertEquals("1708999200000", 
            MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, "1708999200000"));

        // 测试其他类型的返回值
        fieldDesc.put("return_type", "text");
        Assert.assertEquals("test", 
            MetadataUtils.BPMFormUpdateDataType.formula.correct(fieldDesc, "test"));
    }

    @Test
    public void testBPMFormUpdateDataTypeCorrectDataValue() {
        // 测试数据类型修正的工厂方法
        Map<String, Object> fieldDesc = new HashMap<>();
        
        // 测试正常类型
        fieldDesc.put("type", "number");
        Assert.assertEquals("123", 
            MetadataUtils.BPMFormUpdateDataType.correctDataValue(fieldDesc, "123"));

        // 测试未知类型
        fieldDesc.put("type", "unknown_type");
        Assert.assertEquals("test", 
            MetadataUtils.BPMFormUpdateDataType.correctDataValue(fieldDesc, "test"));

        // 测试类型为null
        fieldDesc.put("type", null);
        Assert.assertEquals("test", 
            MetadataUtils.BPMFormUpdateDataType.correctDataValue(fieldDesc, "test"));
    }

    @Test
    public void testSignInMethodsWithNullInput() {
        // 测试签到相关方法的null输入处理
        Assert.assertFalse(MetadataUtils.objectIsSignIn(null, new HashMap<>()));
        Assert.assertFalse(MetadataUtils.objectIsSignOut(null, new HashMap<>()));
        Assert.assertTrue(MetadataUtils.objectIsDisableSignOut(null));
    }

    @Test
    public void testSignInMethodsWithIncompleteData() {
        Map<String, Object> signInDescribe = new HashMap<>();
        Map<String, Object> matadataData = new HashMap<>();
        
        // 测试不完整的签到数据
        Assert.assertFalse(MetadataUtils.objectIsSignIn(signInDescribe, matadataData));
        Assert.assertFalse(MetadataUtils.objectIsSignOut(signInDescribe, matadataData));
        
        // 添加fields但不添加状态字段
        signInDescribe.put(BPMConstants.MetadataKey.fields, new HashMap<>());
        Assert.assertFalse(MetadataUtils.objectIsSignIn(signInDescribe, matadataData));
        Assert.assertFalse(MetadataUtils.objectIsSignOut(signInDescribe, matadataData));
    }
} 