package com.facishare.bpm.util;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.paas.engine.bpm.AfterAction;
import com.facishare.bpm.model.paas.engine.bpm.AfterActionExecution;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class SimpleAfterActionExecutionUtilTest {

    private RefServiceManager serviceManager;
    private WorkflowInstance instance;
    private AfterActionExecution.SimpleAfterActionExecution execution;

    @Before
    public void setUp() {
        // 手动创建测试对象
        serviceManager = new RefServiceManagerWrapper(){
            @Override
            public Map<String, Boolean> hasObjectFunctionPrivilege(String entityId) {
                Map<String, Boolean> privileges = new HashMap<>();
                privileges.put(BPMConstants.FunctionCode.BPM_AFTER_ERROR_RETRY, true);
                privileges.put(BPMConstants.FunctionCode.BPM_AFTER_ERROR_IGNORE, true);
                return privileges;
            }

            @Override
            public String getFsPeerDisplayName() {
                return "";
            }
        };
        instance = new TestWorkflowInstance();
        execution = new AfterActionExecution.SimpleAfterActionExecution();
    }

    @Test
    public void testSetAfterActionOperationsWithEndedInstance() {
        // 准备数据
        ((TestWorkflowInstance)instance).setHasEnd(true);

        // 执行测试
        SimpleAfterActionExecutionUtil.setAfterActionOperations(serviceManager, instance, "testEntityId", execution);

        // 验证结果
        assertTrue(execution.getOperation().isEmpty());
    }

    @Test
    public void testSetAfterActionOperationsWithErrorExecution() {
        // 准备数据
        ((TestWorkflowInstance)instance).setHasEnd(false);

        // 创建一个错误状态的 AfterAction
        AfterAction errorAction = new AfterAction();
        errorAction.setExecutionState(AfterActionExecution.AfterActionExecutionState.ERROR);
        execution.setActions(Lists.newArrayList(errorAction));

        // 直接调用第二个方法，因为第一个方法需要 execution.isError() 返回 true
        SimpleAfterActionExecutionUtil.setAfterActionOperations(serviceManager, "testEntityId", execution);

        // 验证结果
        List<AfterActionExecution.AfterActionOperation> operations = execution.getOperation();
        assertEquals(2, operations.size());
        // 检查操作类型：1=重试，0=忽略
        boolean hasRetry = operations.stream().anyMatch(op -> "1".equals(op.getExecuteType()));
        boolean hasIgnore = operations.stream().anyMatch(op -> "0".equals(op.getExecuteType()));
        assertTrue("Should have retry operation", hasRetry);
        assertTrue("Should have ignore operation", hasIgnore);
    }

    @Test
    public void testSetAfterActionOperationsWithTimeoutAction() {
        // 准备数据
        AfterAction timeoutAction = new AfterAction();
        timeoutAction.setExecutionState(AfterActionExecution.AfterActionExecutionState.WAITING);
        timeoutAction.setModifyTime(System.currentTimeMillis() - 400000L); // 设置修改时间为5分钟前

        // 创建一个自定义的 execution 来模拟 getTimeoutAction 返回超时动作
        AfterActionExecution.SimpleAfterActionExecution customExecution = new AfterActionExecution.SimpleAfterActionExecution() {
            @Override
            public AfterAction getTimeoutAction() {
                return timeoutAction;
            }
        };
        customExecution.setActions(Lists.newArrayList(timeoutAction));

        // 执行测试
        SimpleAfterActionExecutionUtil.setAfterActionOperations(serviceManager, "testEntityId", customExecution);

        // 验证结果
        List<AfterActionExecution.AfterActionOperation> operations = customExecution.getOperation();
        assertEquals(2, operations.size());
        // 检查操作类型：1=重试，0=忽略
        boolean hasRetry = operations.stream().anyMatch(op -> "1".equals(op.getExecuteType()));
        boolean hasIgnore = operations.stream().anyMatch(op -> "0".equals(op.getExecuteType()));
        assertTrue("Should have retry operation", hasRetry);
        assertTrue("Should have ignore operation", hasIgnore);
        assertEquals(AfterActionExecution.AfterActionExecutionState.ERROR, timeoutAction.getExecutionState());
    }

    @Test
    public void testSetAfterActionOperationsWithNoPrivileges() {
        // 准备数据 - 创建一个没有权限的 ServiceManager
        RefServiceManager noPrivilegeManager = new RefServiceManagerWrapper(){
            @Override
            public Map<String, Boolean> hasObjectFunctionPrivilege(String entityId) {
                Map<String, Boolean> privileges = new HashMap<>();
                privileges.put(BPMConstants.FunctionCode.BPM_AFTER_ERROR_RETRY, false);
                privileges.put(BPMConstants.FunctionCode.BPM_AFTER_ERROR_IGNORE, false);
                return privileges;
            }

            @Override
            public String getFsPeerDisplayName() {
                return "";
            }
        };

        AfterAction errorAction = new AfterAction();
        errorAction.setExecutionState(AfterActionExecution.AfterActionExecutionState.ERROR);
        execution.setActions(Lists.newArrayList(errorAction));

        // 执行测试
        SimpleAfterActionExecutionUtil.setAfterActionOperations(noPrivilegeManager, "testEntityId", execution);

        // 验证结果
        assertTrue(execution.getOperation().isEmpty());
    }



    private static class TestWorkflowInstance extends WorkflowInstance {
        private boolean hasEnd;

        public void setHasEnd(boolean hasEnd) {
            this.hasEnd = hasEnd;
        }

        @Override
        public boolean hasEnd() {
            return hasEnd;
        }
    }
} 