package com.facishare.bpm.util.verifiy.handler.activity;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.util.verifiy.ValidateResult;
import com.fxiaoke.i18n.SupportLanguage;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.fxiaoke.i18n.util.LangIndex;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

public class ActivityValidateManagerTest {

    @Before
    public void setUp() throws Exception {
        initI18nClient();
    }

    protected void initI18nClient() throws Exception {
        Field field = I18nClient.class.getDeclaredField("impl");
        field.setAccessible(true);

        I18nServiceImpl mockImpl = Mockito.mock(I18nServiceImpl.class);
        field.set(I18nClient.getInstance(), mockImpl);

        doAnswer(invocation -> invocation.getArguments()[3])
            .when(mockImpl)
            .getOrDefault(anyString(), anyLong(), anyString(), anyString());

        when(mockImpl.get(anyString(), anyLong())).thenReturn(new Localization());

        LangIndex.getInstance().setSupportLanguageList(Collections.singletonList(new SupportLanguage()));
        LangIndex.getInstance().setLanguageCodeMapping(new HashMap<>());
    }

    @Test
    public void testValidateUserTaskLatency_NonUserTaskExt() {
        ActivityExt activity = Mockito.mock(ActivityExt.class);
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_NullLatency() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(null);
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_NonNumericLatency() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn("non-numeric");
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_Days() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(365.0); // 1年
        when(activity.getLatencyUnit()).thenReturn(1); // 单位：天
        when(activity.getName()).thenReturn("测试节点");
        
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_Hours() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(8760.0); // 1年的小时数
        when(activity.getLatencyUnit()).thenReturn(2); // 单位：小时
        when(activity.getName()).thenReturn("测试节点");
        
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_Minutes() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(525600.0); // 1年的分钟数
        when(activity.getLatencyUnit()).thenReturn(3); // 单位：分钟
        when(activity.getName()).thenReturn("测试节点");
        
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_AlmostHundredYears() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(36499.0); // 差一天到100年
        when(activity.getLatencyUnit()).thenReturn(1); // 单位：天
        when(activity.getName()).thenReturn("测试节点");
        
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertTrue(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_ExactlyHundredYears() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(36500.0); // 正好100年
        when(activity.getLatencyUnit()).thenReturn(1); // 单位：天
        when(activity.getName()).thenReturn("测试节点");
        
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertFalse(result.isValid());
    }

    @Test
    public void testValidateUserTaskLatency_MoreThanHundredYears() {
        UserTaskExt activity = Mockito.mock(UserTaskExt.class);
        when(activity.getRemindLatency()).thenReturn(36501.0); // 超过100年
        when(activity.getLatencyUnit()).thenReturn(1); // 单位：天
        when(activity.getName()).thenReturn("测试节点");
        
        ValidateResult result = ActivityValidateManager.validateUserTaskLatency(activity);
        assertFalse(result.isValid());
    }
} 