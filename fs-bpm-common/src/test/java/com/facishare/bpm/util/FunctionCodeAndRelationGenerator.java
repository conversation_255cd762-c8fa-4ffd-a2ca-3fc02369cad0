package com.facishare.bpm.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 预制对象 功能权限生成器   见 document/7.6.0 案例
 * 功能权限初始化详细教程  http://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
 *
 * 112刷库(深研和权限组都需要支持)
 * 灰度刷库(深研和权限组都需要支持)
 * 全网刷库(深研和权限组都需要支持) TODO 不要忘记专属云
 */
@Slf4j
public class FunctionCodeAndRelationGenerator {

    /**
     * 预制对象列表,不同流程支持的对象不同,TODO 需要酌情修改
     */
    private Set<String> entities = Sets.newHashSet(
            "ContactObj",
            "QuoteLinesObj",
            "ReturnedGoodsInvoiceObj",
            "AccountObj",
            "LeadsObj",
            "MarketingEventObj",
            "SalesOrderObj",
            "ContractObj",
            "OpportunityObj",
            "CasesCheckinsObj",
            "ElectronicSignObj",
            "StockCheckNoteObj",
            "QuoteObj",
            "DeliveryNoteProductObj",
            "WechatFanObj",
            "PaymentObj",
            "PaymentPlanObj",
            "GoodsReceivedNoteObj",
            "DeliveryNoteObj",
            "PromotionObj",
            "CasesObj",
            "OutboundDeliveryNoteObj",
            "PartnerObj",
            "RequisitionNoteObj",
            "DeviceObj",
            "CreditFileObj",
            "RebateUseRuleObj",
            "AdvertisementObj",
            "CheckRecordObj",
            "StatementObj",
            "NewOpportunityObj",
            "NewOpportunityLinesObj",
            "NewOpportunityContactsObj",
            "StockCheckNoteObj",
            "RefundMaterialBillObj",
            "ReceiveMaterialBillObj",
            "SupplierObj",
            "PurchaseOrderObj",
            "AppraiseObj",
            "FeeDetailObj",
            "FeeSettlementBillObj",
            "FeeSettlementDetailObj",
            "CasesAccessoryUseInfoObj",
            "ExchangeGoodsNoteObj",
            "FundReturnBackObj",
            "ExchangeReturnNoteObj",
            "ProductObj",
            "ServiceKnowledgeObj",
            "FeeSettlementBillObj",
            "PreventiveMaintenanceObj",
            "RefundObj",
            "InvoiceApplicationObj",
            "ForecastSumObj",
            "ForecastRecordObj",
            "LeaveMessageObj",
            "EnterpriseInfoObj",
            "CasesDeviceObj",
            "CasesServiceProjectObj",
            "ServiceLogObj",
            "EmployeeWarehouseObj",
            "CostAdjustmentNoteObj",
            "TPMActivityItemObj",
            "TPMActivityObj",
            "TPMActivityDetailObj",
            "TPMActivityStoreObj",
            "TPMActivityAgreementObj",
            "TPMActivityAgreementDetailObj",
            "TPMActivityProofObj",
            "TPMActivityProofDetailObj",
            "TPMDealerActivityObj",
            "TPMActivityProofAuditObj",
            "TPMActivityProofAuditDetailObj",
            "TPMDealerActivityCostObj",
            "ShelfReportObj",
            "KeywordServingPlanObj",
            "MarketingKeywordObj"

    );
    /**
     * 预制的角色 默认情况下无需修改
     */
    private final List<String> roleCodes = Lists.newArrayList(
            "00000000000000000000000000000006",//CRM管理员
            "00000000000000000000000000000009",
            "00000000000000000000000000000015",
            "00000000000000000000000000000014",
            "00000000000000000000000000000010",
            "00000000000000000000000000000024",
            "00000000000000000000000000000020",
            "00000000000000000000000000000016",
            "00000000000000000000000000000003",
            "00000000000000000000000000000002",
            "00000000000000000000000000000005",
            "00000000000000000000000000000004",
            "00000000000000000000000000000017",
            "00000000000000000000000000000018",
            "00000000000000000000000000000019",
            "00000000000000000000000000000026",
            "goalManagerRole"
    );

    /**
     * entityId||functionCode 无需修改
     * label
     */
    private String template = "{\n" +
            "    \"appId\": \"CRM\",\n" +
            "    \"functionCode\": \"%s||%s\",\n" +
            "    \"name\": \"%s\",\n" +
            "    \"parentCode\": \"00000000000000000000000000000000\"\n" +
            "  }";

    private final Map<String, String> codeMapping = Maps.newHashMap();

    /**
     * 需要将要添加的功能权限code添加上 TODO 需要修改
     */
    @Before
    public void init() {
//        codeMapping.put("ViewApprovalInstanceLog", "查看审批意见");//审批流
//        codeMapping.put("ViewApprovalConfig", "查看审批配置");//审批流
//        codeMapping.put("ViewBPMInstanceLog", "查看业务流流程日志");//业务流
        codeMapping.put("StageReactivation", "阶段重新激活");//阶段推进器
    }


    /**
     * 生成function code 模板,直接运行,复制并创建功能权限  [预制功能func]
     *
     * 112环境地址:http://oss.firstshare.cn/paas-console/paas-auth/system
     * 线上环境地址:http://oss.foneshare.cn/paas-console/paas-auth/system
     * 专属云地址:请与console自行查找   ${context}/paas-console/paas-auth/system
     */
    @Test
    public void genFunctionCodeTemplate() {

        StringBuilder sb = new StringBuilder();
        sb.append("\n");
        sb.append("[");
        for (String entityId : entities) {
            codeMapping.forEach((code, lable) -> {
                String codeData = String.format(template, entityId, code, lable);
                sb.append(codeData).append(",");
            });

        }
        sb.append("]");
        sb.append("\n");
        log.info("{}", sb.toString());

    }


    /**
     * 生成功能权限及预制角色的关系模板,直接运行,复制并创建功能权限关系 [预制角色功能关系funcAccess]
     *
     * 112环境地址:http://oss.firstshare.cn/paas-console/paas-auth/system
     * 线上环境地址:http://oss.foneshare.cn/paas-console/paas-auth/system
     * 专属云地址:请与console自行查找   ${context}/paas-console/paas-auth/system
     */
    @Test
    public void genFunctionCodeRelationTemplate() {
        StringBuilder sb = new StringBuilder();
        sb.append("\n");
        sb.append("[");

        for (int j = 0; j < roleCodes.size(); j++) {

            String roleCode = roleCodes.get(j);
            sb.append("{");
            sb.append("\"appId\": \"CRM\",");
            sb.append("\"functionCodes\":[");
            sb.append("\n");
            for (String entityId : entities) {
                codeMapping.forEach((code, lable) -> {
                    sb.append("\"").append(entityId).append("||").append(code).append("\"").append(",");
                });
            }
            sb.append("\n");
            sb.append("],");
            sb.append("\"roleCode\": \"").append(roleCode).append("\"");
            if (j == roleCodes.size() - 1) {
                sb.append("}");
            } else {
                sb.append("},");
            }


        }
        sb.append("]");
        log.info("{}", sb.toString());
    }
}
