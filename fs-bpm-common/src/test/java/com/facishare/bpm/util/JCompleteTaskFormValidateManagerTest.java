package com.facishare.bpm.util;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.rest.core.util.JacksonUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/7/11 10:13 AM
 */
@Slf4j
public class JCompleteTaskFormValidateManagerTest {

    @Test
    public void dataFilter() {
        String str = "{\n" +
                "    \"field_91sF1__c\":\"B19306144\",\n" +
                "    \"field_1tkju__c\":1562342400000,\n" +
                "    \"field_2ppvd__c\":\"0000173\",\n" +
                "    \"field_D1b0f__c\":1562641560000,\n" +
                "    \"field_0LG2s__c\":\"n1zQO29Br\",\n" +
                "    \"field_875K4__c\":\"双波\",\n" +
                "    \"field_f24ww__c\":[\n" +
                "        {\n" +
                "            \"ext\":\"jpg\",\n" +
                "            \"path\":\"N_201907_10_500ae0bc4ca94a7db37119212d8ad73e\",\n" +
                "            \"filename\":\"TN_285881f0f06c47ffabc310e83dc5bd8a.jpg\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"field_Ji4sS__c\":\"新机调试\",\n" +
                "    \"field_1RnJa__c\":\"VH11D000510 VH11D000511\",\n" +
                "    \"field_pdN1L__c\":\"vh11\",\n" +
                "    \"object_describe_api_name\":\"object_LMs1B__c\",\n" +
                "    \"field_tgnm8__c\":\"option1\",\n" +
                "    \"field_q821t__c\":1562641560000,\n" +
                "    \"field_Vo7e6__c\":\"option1\",\n" +
                "    \"field_yAkb7__c\":[\n" +
                "        {\n" +
                "            \"ext\":\"jpg\",\n" +
                "            \"path\":\"N_201907_10_73bec6c84ded4a34b979e020e05e139e\",\n" +
                "            \"filename\":\"TN_0693d12e25b740a685adfea6f47b852c.jpg\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"version\":6,\n" +
                "    \"field_S2PoV__c\":\"现场调试\",\n" +
                "    \"field_krdeD__c\":\"other\",\n" +
                "    \"field_0zOe5__c\":\"王明华\",\n" +
                "    \"_id\":\"5d241e9c0af3b20001dfe514\",\n" +
                "    \"field_l9Wf5__c\":\"option1\"\n" +
                "}";
        Map<String, Object> updateData = JacksonUtil.fromJson(str, Map.class);

        Set<String> formKeys = Sets.newHashSet("field_D1b0f__c", "field_q821t__c", "field_1tkju__c", "field_1RnJa__c", "field_pdN1L__c", "field_91sF1__c", "field_l9Wf5__c", "field_Ji4sS__c", "field_S2PoV__c", "field_875K4__c", "field_Vo7e6__c", "field_tgnm8__c", "field_0zOe5__c", "field_f24ww__c", "field_c6qS6__c", "field_MA5p3__c", "field_9cH0R__c", "field_t88w8__c", "field_k84en__c", "field_n3r4i__c", "field_49BZJ__c", "field_izWqs__c", "field_x9S9B__c", "field_vF2pY__c", "field_767T2__c", "field_6lx34__c", "field_6wlx7__c", "field_ftomy__c", "field_Pkbd1__c", "field_4Hn0e__c", "field_Lxo2a__c", "field_0LG2s__c", "field_mZFf3__c", "field_yAkb7__c", "field_krdeD__c", "field_dVuf1__c", "field_2ppvd__c");
        updateData.keySet().stream().filter(key -> {
            boolean flag = (formKeys.contains(key) && Objects.nonNull(updateData.get(key))) ||
                    key.contains(BPMConstants.MetadataKey.lookupFieldNameValuePostfix) ||
                    key.contains(BPMConstants.MetadataKey.otherFieldNameValuePostfix);
            if (!flag) {
                log.warn(" 数据更新过滤: 字段 {} 被过滤", key);
            }
            return flag;
        }).collect(Collectors.toMap(key -> key, value -> {
            log.info("key:{},value:{}", value, updateData.get(value));
            return updateData.get(value);
        }, (v1, v2) -> v1));
    }
}
