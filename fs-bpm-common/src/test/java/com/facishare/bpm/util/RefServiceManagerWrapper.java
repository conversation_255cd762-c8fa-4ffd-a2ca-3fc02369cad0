package com.facishare.bpm.util;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.model.TaskParams;
import com.facishare.bpm.model.resource.custommetadata.FindCustomButtonList;
import com.facishare.bpm.model.resource.enterpriserelation.GetOutRolesByTenantId;
import com.facishare.bpm.model.resource.eservice.GetBpmSupportInfo;
import com.facishare.bpm.model.resource.newmetadata.FindDataBySearchTemplate;
import com.facishare.bpm.model.resource.newmetadata.FindDescribeExtra;
import com.facishare.bpm.model.resource.newmetadata.GetCountryAreaOptions;
import com.facishare.bpm.model.resource.newmetadata.UpdateData;
import com.facishare.bpm.model.resource.paas.org.GetDeptByBatch;
import com.facishare.bpm.remote.model.org.*;
import com.facishare.bpm.utils.model.Pair;
import com.facishare.flow.element.plugin.api.FlowElement;
import com.facishare.flow.element.plugin.api.wrapper.FlowElementWrapper;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.model.ClientInfo;
import com.facishare.rest.core.model.RemoteContext;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2/27/25
 * @apiNote
 **/
public abstract class RefServiceManagerWrapper implements RefServiceManager {
    @Override
    public boolean instanceListSkipDataPrivilege() {
        return false;
    }

    @Override
    public Map findDataById(String entityId, String objectId) {
        return null;
    }

    @Override
    public Object findDataById(String entityId, String objectId, String field, boolean includeDescribe) {
        return null;
    }

    @Override
    public Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe) {
        return null;
    }

    @Override
    public Map findDataById(String entityId, String objectId, boolean includeLookupName, boolean includeDescribe, boolean formatData, boolean skipRelevantTeam) {
        return null;
    }

    @Override
    public Map findDataById(RemoteContext cnt, String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
        return null;
    }

    @Override
    public Map getDataBySocketConfig(String entityId, String objectId, boolean includeLookupName, boolean incluedeDescribe) {
        return null;
    }

    @Override
    public Map findDescribe(String entityId, boolean containAllFields, boolean includeStatistics) {
        return null;
    }

    @Override
    public Map<String, Object> findDescribeExtra(String apiName, FindDescribeExtra.FindDescribeExtraType describeExtraType) {
        return null;
    }

    @Override
    public Map<String, Object> getFields(String entityId) {
        return null;
    }

    @Override
    public FindDataBySearchTemplate.Result findDataBySearchTemplate(String apiName, SearchTemplateQuery query) {
        return null;
    }

    @Override
    public Map<String, String> getSimpleEntityNames() {
        return null;
    }

    @Override
    public Map<String, String> getSimpleEntityNamesBySocketConfig() {
        return null;
    }

    @Override
    public Map<String, Object> getFieldDesc(String entityId, String field) {
        return null;
    }

    @Override
    public String getFieldType(String entityId, String field) {
        return null;
    }

    @Override
    public String getKey(String entityId, String objectId) {
        return null;
    }

    @Override
    public String getDescDisplayName(String entityId) {
        return null;
    }

    @Override
    public Map<String, String> getPaaSObjectNames(Collection<Pair<String, String>> entityIdAndObjectIdList) {
        return null;
    }

    @Override
    public String getPaaSObjectName(String entityId, String objectId) {
        return null;
    }

    @Override
    public String getActionNameByActionCode(String entityId, String actionCode) {
        return null;
    }

    @Override
    public Map<String, String> getRefEntityIdByEntityId(String entityId) {
        return null;
    }

    @Override
    public Map<String, Object> findDataByIdWithEmptyMap(String entityId, String dataId) {
        return null;
    }

    @Override
    public List<String> getDataOwner(String entityId, String id) {
        return null;
    }

    @Override
    public boolean isDataOwnerByQueryMetadata(String apiName, String objectId) {
        return false;
    }

    @Override
    public Map<String, List<String>> getDataOwners(String entityId, Set<String> ids) {
        return null;
    }

    @Override
    public RemoteContext getContext() {
        return null;
    }

    @Override
    public String getTenantId() {
        return null;
    }

    @Override
    public String getUserId() {
        return null;
    }

    @Override
    public RemoteContext getNotExistEAContext() {
        return null;
    }

    @Override
    public List<String> getValue(boolean convert, String entityId, String objectId, List<String> extUserType, String instanceId, Map<String, Object> variables) {
        return null;
    }

    @Override
    public List<Integer> getDeptIdsByUserId() {
        return null;
    }

    @Override
    public List<String> getGroupByUserId() {
        return null;
    }

    @Override
    public List getMembersByGroupIds(List groups) {
        return null;
    }

    @Override
    public List<String> getMembersByDeptIds(List<Object> deptIds) {
        return null;
    }

    @Override
    public List<String> getDeptLeaders(List<Object> deptIds) {
        return null;
    }

    @Override
    public List<String> getDeptLeadersByUserIds(List<String> userIds) {
        return null;
    }

    @Override
    public List<String> getLeaders(List<String> userIds) {
        return null;
    }

    @Override
    public List<String> getCRMUserOfRoles(List<Object> roles) {
        return null;
    }

    @Override
    public RemoteContext getAdminContext() {
        return null;
    }

    @Override
    public boolean isAdmin() {
        return false;
    }

    @Override
    public Map<String, Object> getDescribe(String entityId) {
        return null;
    }

    @Override
    public Map<String, Map<String, Object>> getDescribes(Collection<String> entityIds) {
        return null;
    }

    @Override
    public boolean workflowIsExists(String tenantId, String outLineId, String sourceWorkflowId) {
        return false;
    }

    @Override
    public boolean isFieldInactive(String entityId, String field) {
        return false;
    }

    @Override
    public List<String> getRoleByUserId() {
        return null;
    }

    @Override
    public Map<String, Object> getAreaOption(String fieldType) {
        return null;
    }

    @Override
    public void validateAssignee(Set<Object> deptIds, Set<Object> groupIdList, Set<Object> userIds, Set<Object> roleCodes, Set<Object> deptLeader, String tipName) {

    }

    @Override
    public List<String> getEmployeesByReportingObjectId(String userId) {
        return null;
    }

    @Override
    public Map<String, Role> getRoleByCode(List<String> roleList) {
        return null;
    }

    @Override
    public Map<Integer, Employee> getMembersByIds(List personList) {
        return null;
    }

    @Override
    public Map<String, CRMGroup> getGroupByIds(List crmGroup) {
        return null;
    }

    @Override
    public Map<Integer, Department> getDeptByIDs(List deptIds) {
        return null;
    }

    @Override
    public Set<String> getPersons(Map<String, Object> nextTaskAssigneeScope) {
        return null;
    }

    @Override
    public Map<String, Dept> getDeptByDeptIds(List<String> deptIds) {
        return null;
    }

    @Override
    public boolean getDeliveryNoteEnable() {
        return false;
    }

    @Override
    public GetCountryAreaOptions.GetCountryAreaOptionsResultDetail getCountryAreaOptions() {
        return null;
    }

    @Override
    public Map<String, String> getAreaLabelByCodes(String tenantId, List<String> codes) {
        return null;
    }

    @Override
    public GetDeptByBatch.Result getMainDeptsByUserIds(List<String> ownerId) {
        return null;
    }

    @Override
    public Map<String, Boolean> hasObjectFunctionPrivilege(String entityId) {
        return null;
    }

    @Override
    public boolean dataPrivilege(String entityId, String objectId) {
        return false;
    }

    @Override
    public String getAppId() {
        return null;
    }

    @Override
    public ClientInfo getClientInfo() {
        return null;
    }

    @Override
    public Map updateData(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
        return null;
    }

    @Override
    public Map<String, Object> findDataHandlePermissionsById(String entityId, String objectId) {
        return null;
    }

    @Override
    public Map<String, String> getAppActions(String entityId, Boolean isExternalFlow, String appId, Integer appType) {
        return null;
    }

    @Override
    public Map<String, Object> getFieldDesc(String entityId, String field, boolean includeCountFieldLookupType) {
        return null;
    }

    @Override
    public Map<Integer, Employee> getExternalUserIdInfo(List externalRole) {
        return null;
    }

    @Override
    public Map<String, Integer> getFieldPermissions(String entityId) {
        return null;
    }

    @Override
    public boolean isOuterUserId() {
        return false;
    }

    @Override
    public List<Long> getOuterMainOwner() {
        return null;
    }

    @Override
    public Map<String, Employee> getEmployeeInfo(List<Object> userIds) {
        return null;
    }

    @Override
    public String getUserIdWithOuterUserId() {
        return null;
    }

    @Override
    public boolean isOuterMainOwner() {
        return false;
    }

    @Override
    public List<String> getExternalRole(List externalRole, String lowerTenantId, String linkAppId, Integer linkAppType) {
        return null;
    }

    @Override
    public List<GetOutRolesByTenantId.SimpleRoleResult> getRolesByAppId(String appId, int appType) {
        return null;
    }

    @Override
    public GetBpmSupportInfo.Result getActionCodeSupportInfo(String appCode, String actionCode, TaskParams taskParams, String entityId, String objectId, String taskId) {
        return null;
    }

    @Override
    public Map<String, String> getPaaSObjectNames(String entityId, List<String> ids) {
        return null;
    }

    @Override
    public Map<String, String> getObjectNameAndDisplayName(String entityId, List<String> ids) {
        return null;
    }

    @Override
    public <E> E getObjectFromCache(String key, Function<String, E> function) {
        return null;
    }

    @Override
    public Set<String> flowLayoutIsNoExist(String entityId, Set<String> entityLayoutApiNameSet) {
        return null;
    }

    @Override
    public List<FindCustomButtonList.CustomButton> findCustomButtonList(String apiName, Boolean includeUIAction) {
        return null;
    }

    @Override
    public Map<String, String> findDataExhibitButton(String apiName, String objectId, String usePageType) {
        return null;
    }

    @Override
    public boolean checkEmailEnable(String sender) {
        return false;
    }

    @Override
    public FlowElementWrapper getFlowElementWrapper(String elementApiName) {
        return null;
    }

    @Override
    public FlowElement getFlowElement(String elementApiName) {
        return null;
    }

    @Override
    public String getI18nLinkAppName(String linkApp, String linkAppName) {
        return null;
    }

    @Override
    public boolean convertRuleIsEnable(String ruleName, String ruleApiName) {
        return false;
    }

    @Override
    public Map<String, Object> getFlowConfig(String terminal, List<String> types) {
        return null;
    }

    @Override
    public boolean isNeedDiscussButton() {
        return false;
    }

    @Override
    public UpdateData.UpdateDataResultDetail updateDataCheckConflicts(RemoteContext context, String entityId, String objectId, String data, boolean applyValidationRule, boolean applyDataPrivilegeCheck, String modelName) {
        return null;
    }

    @Override
    public Map<String, Object> findRecordFieldMapping(String entityId, String recordType) {
        return null;
    }

    @Override
    public Map<String, String> getDimensionObjDataList(RemoteContext context, Object query) {
        return null;
    }
}
