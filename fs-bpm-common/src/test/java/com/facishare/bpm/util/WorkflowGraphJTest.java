package com.facishare.bpm.util;

import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.ExecutableWorkflowExt;
import com.facishare.bpm.bpmn.TransitionExt;
import com.facishare.rest.core.util.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import org.apache.commons.collections.Bag;
import org.apache.commons.collections.bag.HashBag;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * desc:
 * version: 6.7
 * Created by cuiyongxu on 2019/6/27 11:22 AM
 */
public class WorkflowGraphJTest {

    private String transtion = "[{\"id\":1561409112550,\"fromId\":1561018538389,\"toId\":1561409112548,\"serialNumber\":0.0},{\"id\":1561409112551,\"fromId\":1561409112548,\"toId\":1561409112549,\"serialNumber\":1.0},{\"description\":\"\",\"id\":1561409112557,\"fromId\":1561409112546,\"toId\":1561409112556,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_m41Gz__c\"},\"right\":{\"value\":\"zy3O2t4d9\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":2.0},{\"description\":\"\",\"id\":1561409112558,\"fromId\":1561409112546,\"toId\":1561409112555,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_m41Gz__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":3.0},{\"id\":1561409112569,\"fromId\":1561409112555,\"toId\":1561409112568,\"serialNumber\":4.0},{\"id\":1561409112571,\"fromId\":1561409112568,\"toId\":1561409112570,\"serialNumber\":5.0},{\"id\":1561409112573,\"fromId\":1561409112556,\"toId\":1561409112572,\"serialNumber\":6.0},{\"id\":1561409738239,\"fromId\":1561409112572,\"toId\":1561409738238,\"serialNumber\":7.0},{\"id\":1561409738246,\"fromId\":1561409112563,\"toId\":1561409112546,\"serialNumber\":8.0},{\"id\":1561409738250,\"fromId\":1561409112549,\"toId\":1561409738247,\"serialNumber\":9.0},{\"description\":\"\",\"id\":1561409738251,\"fromId\":1561409738247,\"toId\":1561409112563,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"notEquals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_3q2S9__c\"},\"right\":{\"value\":\"R5l5116AF\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]},{\"type\":\"and\",\"conditions\":[{\"type\":\"notEquals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_3q2S9__c\"},\"right\":{\"value\":\"F5034nm8K\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":10.0},{\"description\":\"\",\"id\":1561409738253,\"fromId\":1561409738247,\"toId\":1561409738252,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_3q2S9__c\"},\"right\":{\"value\":\"R5l5116AF\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]},{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_3q2S9__c\"},\"right\":{\"value\":\"F5034nm8K\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":11.0},{\"description\":\"\",\"id\":1561409738254,\"fromId\":1561409738238,\"toId\":1561409112555,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_gBaq0__c\"},\"right\":{\"value\":\"JI8574X21\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":12.0},{\"description\":\"\",\"id\":1561409738256,\"fromId\":1561409738238,\"toId\":1561409738255,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_gBaq0__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":13.0},{\"description\":\"\",\"id\":1561409738258,\"fromId\":1561409112570,\"toId\":1561409738257,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_l6Etc__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":14.0},{\"id\":1561409738259,\"fromId\":1561409738257,\"toId\":1561409738255,\"serialNumber\":15.0},{\"id\":1561409738261,\"fromId\":1561409738255,\"toId\":1561409738260,\"serialNumber\":16.0},{\"id\":1561409738262,\"fromId\":1561409738252,\"toId\":1561409738260,\"serialNumber\":17.0},{\"id\":1561409738264,\"fromId\":1561409738260,\"toId\":1561409738263,\"serialNumber\":18.0},{\"id\":1561409738266,\"fromId\":1561409738263,\"toId\":1561409738265,\"serialNumber\":19.0},{\"description\":\"\",\"id\":1561409738270,\"fromId\":1561409738265,\"toId\":1561409738269,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_1SzLC__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":20.0},{\"id\":1561409738274,\"fromId\":1561409738272,\"toId\":1561409738273,\"serialNumber\":21.0},{\"description\":\"\",\"id\":1561409738275,\"fromId\":1561409738273,\"toId\":1561409738269,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_20Jpn__c\"},\"right\":{\"value\":\"2aZV23vZ2\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":22.0},{\"id\":1561409738277,\"fromId\":1561409738269,\"toId\":1561409738276,\"serialNumber\":23.0},{\"description\":\"\",\"id\":1561409738278,\"fromId\":1561409738265,\"toId\":1561409738272,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_1SzLC__c\"},\"right\":{\"value\":\"Vp1I5ro19\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]},{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_1SzLC__c\"},\"right\":{\"value\":\"e309vopKG\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":24.0},{\"description\":\"\",\"id\":1561409738279,\"fromId\":1561409738273,\"toId\":1561409112563,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_20Jpn__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":25.0},{\"description\":\"\",\"id\":1561409738298,\"fromId\":1561409112570,\"toId\":1561409738260,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_l6Etc__c\"},\"right\":{\"value\":\"IpCpb9Vbh\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":26.0},{\"description\":\"\",\"id\":1561534422018,\"fromId\":1561409112546,\"toId\":1561534422017,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_m41Gz__c\"},\"right\":{\"value\":\"g87q2re5B\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":27.0},{\"id\":1561534422020,\"fromId\":1561534422017,\"toId\":1561534422019,\"serialNumber\":28.0},{\"description\":\"\",\"id\":1561534422021,\"fromId\":1561534422019,\"toId\":1561409738260,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_W4TiS__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":29.0},{\"description\":\"\",\"id\":1561534422022,\"fromId\":1561534422019,\"toId\":1561409112563,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_W4TiS__c\"},\"right\":{\"value\":\"k2ZBEj7fD\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":30.0},{\"description\":\"\",\"id\":1561536206629,\"fromId\":1561534422019,\"toId\":1561536206628,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_W4TiS__c\"},\"right\":{\"value\":\"68mnCdu64\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":31.0},{\"id\":1561536206633,\"fromId\":1561536206628,\"toId\":1561536206632,\"serialNumber\":32.0},{\"description\":\"\",\"id\":1561536206635,\"fromId\":1561536206632,\"toId\":1561409738260,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_a58Le__c\"},\"right\":{\"value\":\"option1\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]},{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_a58Le__c\"},\"right\":{\"value\":\"51Pb1fyql\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":33.0},{\"description\":\"\",\"id\":1561536815457,\"fromId\":1561536206632,\"toId\":1561409112563,\"condition\":{\"type\":\"or\",\"conditions\":[{\"type\":\"and\",\"conditions\":[{\"type\":\"equals\",\"left\":{\"expression\":\"activity_0##CasesObj##field_a58Le__c\"},\"right\":{\"value\":\"uV8bkd4g7\",\"type\":{\"name\":\"text\"},\"metadata\":{\"containSubDept\":false}}}]}]},\"serialNumber\":34.0},{\"id\":1561536815499,\"fromId\":1561536815469,\"toId\":1561534422017,\"serialNumber\":35.0}]";
    private String activitie = "[{\"type\":\"startEvent\",\"name\":\"开始\",\"description\":\"\",\"id\":1561018538389},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561409112546,\"defaultTransitionId\":1561409112558,\"gatewayType\":0.0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"信息确认\",\"description\":\"\",\"id\":1561409112548,\"assignee\":{\"extUserType\":[\"$[activity_1561409112548##CasesObj##created_by\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[{\"taskType\":\"custom_function\",\"rowNo\":0.0,\"modifyTime\":0.0,\"actionMapping\":{\"funcProperties\":[],\"id\":\"5cd8e5df0959b69477d736b4\",\"functionApiName\":\"func_y1tHr__c\",\"entityId\":\"CasesObj\"}},{\"taskType\":\"custom_function\",\"rowNo\":0.0,\"modifyTime\":0.0,\"actionMapping\":{\"funcProperties\":[],\"id\":\"5d0ede65f59f4f00019f24ca\",\"functionApiName\":\"func_n0x7d__c\",\"entityId\":\"CasesObj\"}}]},\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"信息补充\",\"description\":\"\",\"id\":1561409112549,\"assignee\":{\"extUserType\":[\"$[activity_1561409112549##CasesObj##field_23YvY__c]\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[]},\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"需要购买零件信息\",\"description\":\"\",\"id\":1561409112555,\"assignee\":{\"extUserType\":[\"$[activity_1561409112555##CasesObj##field_23YvY__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"零件寄回检测\",\"description\":\"\",\"id\":1561409112556,\"assignee\":{\"extUserType\":[\"$[activity_1561409112556##CasesObj##field_1sLf4__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"处理方式\",\"description\":\"\",\"id\":1561409112563,\"assignee\":{\"extUserType\":[\"$[activity_1561409112563##CasesObj##field_23YvY__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"客户购买意愿\",\"description\":\"\",\"id\":1561409112568,\"assignee\":{\"extUserType\":[\"$[activity_1561409112568##CasesObj##field_1sLf4__c]\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[{\"taskType\":\"custom_function\",\"rowNo\":0.0,\"modifyTime\":0.0,\"actionMapping\":{\"funcProperties\":[],\"id\":\"5d0ddeca65f06300013ce023\",\"functionApiName\":\"func_9fSrN__c\",\"entityId\":\"CasesObj\"}}]},\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561409112570,\"defaultTransitionId\":1561409738258,\"gatewayType\":0.0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"寄回零件处理信息\",\"description\":\"\",\"id\":1561409112572,\"assignee\":{\"extUserType\":[\"$[activity_1561409112572##CasesObj##field_23YvY__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561409738238,\"defaultTransitionId\":1561409738254,\"gatewayType\":0.0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561409738247,\"defaultTransitionId\":1561409738251,\"gatewayType\":0.0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"鉴丰人员评估结果反馈\",\"description\":\"\",\"id\":1561409738252,\"assignee\":{\"extUserType\":[\"$[activity_1561409738252##CasesObj##field_23YvY__c]\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[{\"taskType\":\"custom_function\",\"rowNo\":0.0,\"modifyTime\":0.0,\"actionMapping\":{\"funcProperties\":[],\"id\":\"5d0ddeca65f06300013ce023\",\"functionApiName\":\"func_9fSrN__c\",\"entityId\":\"CasesObj\"}}]},\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"维修结果及发回信息\",\"description\":\"\",\"id\":1561409738255,\"assignee\":{\"extUserType\":[\"$[activity_1561409738255##CasesObj##field_23YvY__c]\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[{\"taskType\":\"custom_function\",\"rowNo\":0.0,\"modifyTime\":0.0,\"actionMapping\":{\"funcProperties\":[],\"id\":\"5d0ddeca65f06300013ce023\",\"functionApiName\":\"func_9fSrN__c\",\"entityId\":\"CasesObj\"}}]},\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"打款信息\",\"description\":\"\",\"id\":1561409738257,\"assignee\":{\"extUserType\":[\"$[activity_1561409738257##CasesObj##field_1sLf4__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"回访安排\",\"description\":\"\",\"id\":1561409738260,\"assignee\":{\"extUserType\":[\"$[activity_1561409738260##CasesObj##field_Q10iW__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"回访详情\",\"description\":\"\",\"id\":1561409738263,\"assignee\":{\"extUserType\":[\"$[activity_1561409738263##CasesObj##field_Q10iW__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561409738265,\"defaultTransitionId\":1561409738278,\"gatewayType\":0.0},{\"type\":\"executionTask\",\"bpmExtension\":{\"executionType\":\"execution\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"自动节点\",\"description\":\"\",\"id\":1561409738269},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"是否关闭工单\",\"description\":\"\",\"id\":1561409738272,\"assignee\":{\"extUserType\":[\"$[activity_1561409738272##CasesObj##field_Q10iW__c]\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561409738273,\"defaultTransitionId\":1561409738279,\"gatewayType\":0.0},{\"type\":\"endEvent\",\"name\":\"结束\",\"description\":\"\",\"id\":1561409738276},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"沟通结果\",\"description\":\"\",\"id\":1561534422017,\"assignee\":{\"extUserType\":[\"$[activity_1561534422017##CasesObj##field_23YvY__c\"]},\"taskType\":\"anyone\",\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561534422019,\"defaultTransitionId\":1561534422021,\"gatewayType\":0.0},{\"type\":\"userTask\",\"bpmExtension\":{\"executionName\":\"编辑对象\",\"entityName\":\"工单\",\"entityId\":\"CasesObj\",\"executionType\":\"update\",\"actionCode\":\"\",\"objectId\":{\"expression\":\"activity_0##CasesObj\"}},\"name\":\"留言详情\",\"description\":\"\",\"id\":1561536206628,\"assignee\":{\"extUserType\":[\"$[activity_1561536206628##CasesObj##field_1sLf4__c\"]},\"taskType\":\"anyone\",\"execution\":{\"pass\":[{\"taskType\":\"custom_function\",\"rowNo\":0.0,\"modifyTime\":0.0,\"actionMapping\":{\"funcProperties\":[],\"id\":\"5d0ddeca65f06300013ce023\",\"functionApiName\":\"func_9fSrN__c\",\"entityId\":\"CasesObj\"}}]},\"assignNextTask\":0.0,\"externalApplyTask\":0},{\"type\":\"exclusiveGateway\",\"name\":\"分支节点\",\"description\":\"\",\"id\":1561536206632,\"defaultTransitionId\":1561536206635,\"gatewayType\":0.0}]";
    private int[] v;
    private int e;
    private Bag[] adj;
    private String[] v2Activities;
    private Map<String, Integer> activities2v;
    private ExecutableWorkflowExt workflowExt;
    private int start;


    @Test
    public void test() {
        List<ActivityExt> activities = JacksonUtil.fromJson(activitie, new TypeReference<List<ActivityExt>>() {
        });

        List<TransitionExt> transitionExts = JacksonUtil.fromJson(transtion, new TypeReference<List<TransitionExt>>() {
        });
        v = new int[activities.size()];
        v2Activities = new String[v.length];
        activities2v = Maps.newHashMap();
        adj = new HashBag[activities.size()];
        for (int i = 0; i < activities.size(); i++) {
            v2Activities[i] = activities.get(i).getId();
            activities2v.put(v2Activities[i], i);
            v[i] = i;
            if (activities.get(i).getType().equals("startEvent")) {
                start = i;
            }
        }
        for (TransitionExt transition : transitionExts) {
            String from = transition.getFromId();
            String to = transition.getToId();
            try {
                addEdge(activities2v.get(from), activities2v.get(to));
            }catch (NullPointerException e){
                e.printStackTrace();
            }
        }
    }

    private void addEdge(int v, int w) {
        if (adj[v] == null) {
            adj[v] = new HashBag();
        }
        adj[v].add(w);
        e++;
    }
}
