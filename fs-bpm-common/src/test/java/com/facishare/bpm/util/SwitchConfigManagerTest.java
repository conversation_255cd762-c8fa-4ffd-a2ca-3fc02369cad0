package com.facishare.bpm.util;

import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.Assert.*;

public class SwitchConfigManagerTest {

    private SwitchConfigManager.SwitchConfig switchConfig;

    @Before
    public void setUp() {
        switchConfig = new SwitchConfigManager.SwitchConfig();
    }

    @Test
    public void testGetCustomElementSessionKey() {
        // 测试空输入
        assertEquals("", SwitchConfigManager.getCustomElementSessionKey(""));
        assertEquals("", SwitchConfigManager.getCustomElementSessionKey(null));

        // TODO: 需要配置实际的自定义元素配置进行测试
    }

    @Test
    public void testGetCustomElementCategory() {
        // 测试空输入
        assertEquals("", SwitchConfigManager.getCustomElementCategory("entityId", ""));
        assertEquals("", SwitchConfigManager.getCustomElementCategory("entityId", null));

        // TODO: 需要配置实际的自定义元素类别进行测试
    }

    @Test
    public void testIsSupportAllFormDescribe() {
        // 准备测试数据
        SwitchConfigManager.SwitchConfig.GrayConfig grayConfig = new SwitchConfigManager.SwitchConfig.GrayConfig();
        grayConfig.setGray(true);
        grayConfig.setTenantIds(new HashSet<>(Arrays.asList("tenant1", "tenant2")));
        switchConfig.setSupportAllFormDescribe(grayConfig);

        // 测试灰度租户
        assertTrue(grayConfig.supportTenantId("tenant1"));
        assertTrue(grayConfig.supportTenantId("tenant2"));
        assertFalse(grayConfig.supportTenantId("tenant3"));

        // 测试非灰度模式
        grayConfig.setGray(false);
        assertTrue(grayConfig.supportTenantId("anyTenant"));
    }

    @Test
    public void testInstanceListSkipDataPrivelige() {
        // 准备测试数据
        SwitchConfigManager.KidConfig kidConfig = new SwitchConfigManager.KidConfig();
        kidConfig.setGrayEnable(true);
        
        SwitchConfigManager.Detail detail = new SwitchConfigManager.Detail();
        detail.setEnable(true);
        detail.setTenantIds(Arrays.asList("tenant1", "tenant2"));
        kidConfig.setDetail(detail);

        // 测试灰度租户
        assertEquals(SwitchConfigManager.GrayType.gray, kidConfig.needFilterMethod("tenant1"));
        assertEquals(SwitchConfigManager.GrayType.gray, kidConfig.needFilterMethod("tenant2"));
        assertEquals(SwitchConfigManager.GrayType.history, kidConfig.needFilterMethod("tenant3"));

        // 测试全网发布
        kidConfig.setGrayEnable(false);
        assertEquals(SwitchConfigManager.GrayType.full, kidConfig.needFilterMethod("anyTenant"));
    }

    @Test
    public void testIgnoreDataVersion() {
        // 准备测试数据
        Map<String, List<Object>> ignoreDataVersion = new HashMap<>();
        ignoreDataVersion.put("tenant1", Arrays.asList("AccountObj", "*"));
        switchConfig.setIgnoreDataVersion(ignoreDataVersion);

        // 测试忽略版本
        assertTrue(switchConfig.ignoreDataVersion("tenant1", "AccountObj"));
        assertTrue(switchConfig.ignoreDataVersion("tenant1", "anyObject")); // 因为有通配符 *
        assertFalse(switchConfig.ignoreDataVersion("tenant2", "AccountObj"));
    }

    @Test
    public void testGetCommonSystemButtons() {
        // 准备测试数据
        Map<String, List<String>> objectCustomSystemBtns = new HashMap<>();
        objectCustomSystemBtns.put("common", Arrays.asList("btn1", "btn2"));
        objectCustomSystemBtns.put("AccountObj", Arrays.asList("btn3", "btn4"));
        switchConfig.setObjectCustomSystemBtns(objectCustomSystemBtns);

        // 测试获取按钮
        List<String> buttons = switchConfig.getCommonSystemButtons("AccountObj");
        assertEquals(4, buttons.size());
        assertTrue(buttons.containsAll(Arrays.asList("btn1", "btn2", "btn3", "btn4")));

        // 测试未配置对象
        buttons = switchConfig.getCommonSystemButtons("UnknownObj");
        assertEquals(2, buttons.size());
        assertTrue(buttons.containsAll(Arrays.asList("btn1", "btn2")));
    }

    @Test
    public void testExternalApplyTaskChangeCandidates() {
        // 准备测试数据
        Map<String, List<String>> changeCandidate = new HashMap<>();
        changeCandidate.put("action1", Arrays.asList("tenant1", "*"));
        switchConfig.setExternalApplyTaskChangeCandidate(changeCandidate);

        // 测试任务候选人变更
        assertTrue(switchConfig.externalApplyTaskChangeCandidates("tenant1", "action1"));
        assertTrue(switchConfig.externalApplyTaskChangeCandidates("tenant2", "action1")); // 因为有通配符 *
        assertFalse(switchConfig.externalApplyTaskChangeCandidates("tenant1", "action2"));
    }

    @Test
    public void testGetTaskNotGetTask() {
        // 准备测试数据
        switchConfig.setGetTaskNotGetTask(Arrays.asList("tenant1", "*"));

        // 测试任务获取限制
        assertTrue(switchConfig.getTaskNotGetTask("tenant1"));
        assertTrue(switchConfig.getTaskNotGetTask("anyTenant")); // 因为有通配符 *
    }

    @Test
    public void testGetOpenRefreshData() {
        // 测试数据刷新开关
        switchConfig.setOpenRefreshData(true);
        assertTrue(switchConfig.getOpenRefreshData());

        switchConfig.setOpenRefreshData(false);
        assertFalse(switchConfig.getOpenRefreshData());
    }

    @Test
    public void testStaticMethods() throws Exception {
        // 设置静态配置用于测试
        setupStaticConfig();

        // 测试 getSwitchConfig
        assertNotNull(SwitchConfigManager.getSwitchConfig());

        // 测试 syncDataFailRemindEmpleyee
        List<Integer> employees = SwitchConfigManager.syncDataFailRemindEmpleyee();
        assertEquals(3, employees.size());
        assertTrue(employees.contains(1));

        // 测试 syncDataFailRetryCount
        assertEquals(Integer.valueOf(15), SwitchConfigManager.syncDataFailRetryCount());

        // 测试 operateFailNoticeSessionUserId
        List<Integer> userIds = SwitchConfigManager.operateFailNoticeSessionUserId();
        assertEquals(2, userIds.size());
        assertTrue(userIds.contains(100));

        // 测试 getSessionKeyCombine
        assertTrue(SwitchConfigManager.getSessionKeyCombine("tenant1"));
        assertTrue(SwitchConfigManager.getSessionKeyCombine("anyTenant")); // * 匹配
        assertFalse(SwitchConfigManager.getSessionKeyCombine(""));

        // 测试 ignoreDataVersion
        assertTrue(SwitchConfigManager.ignoreDataVersion("tenant1", "AccountObj"));
        assertFalse(SwitchConfigManager.ignoreDataVersion("tenant999", "AccountObj"));

        // 测试 getCommonSystemButtons
        List<String> buttons = SwitchConfigManager.getCommonSystemButtons("AccountObj");
        assertTrue(buttons.size() >= 2);

        // 测试 externalApplyTaskChangeCandidates
        assertTrue(SwitchConfigManager.externalApplyTaskChangeCandidates("tenant1", "action1"));
        assertFalse(SwitchConfigManager.externalApplyTaskChangeCandidates("tenant1", ""));

        // 测试 getTaskNotGetTask
        assertTrue(SwitchConfigManager.getTaskNotGetTask("tenant1"));

        // 测试 getOpenRefreshData
        assertTrue(SwitchConfigManager.getOpenRefreshData());

        // 测试 getSupportExternalApplyBusinessCode
        List<String> codes = SwitchConfigManager.getSupportExternalApplyBusinessCode("AccountObj");
        assertNotNull(codes);

        // 测试 getSupportExternalApplyBusinessCodeFilter
        Map<String, List<String>> filter = SwitchConfigManager.getSupportExternalApplyBusinessCodeFilter();
        assertNotNull(filter);

        // 测试 isSupportAllFormDescribe
        assertTrue(SwitchConfigManager.isSupportAllFormDescribe("tenant1"));

        // 测试 instanceListSkipDataPrivelige
        assertTrue(SwitchConfigManager.instanceListSkipDataPrivelige("tenant1"));

        // 测试 isUpgradeTaskInfo
        assertTrue(SwitchConfigManager.isUpgradeTaskInfo("tenant1"));

        // 测试 isMergeTasksByLaneDataAndDesc
        assertEquals(SwitchConfigManager.GrayType.gray, SwitchConfigManager.isMergeTasksByLaneDataAndDesc("tenant1"));
    }

    @Test
    public void testCustomElementMethods() throws Exception {
        // 设置自定义元素配置
        setupCustomElementConfig();

        // 测试 getCustomElementSessionKey
        try (MockedStatic<BPMConstants> mockedBPMConstants = Mockito.mockStatic(BPMConstants.class)) {
            List<String> exclusiveKeys = Lists.newArrayList("session1", "session3");
            mockedBPMConstants.when(() -> BPMConstants.BPM_EXCLUSIVE_SESSION_KEY).thenReturn(exclusiveKeys);

            assertEquals("session1", SwitchConfigManager.getCustomElementSessionKey("element1"));
            assertEquals("", SwitchConfigManager.getCustomElementSessionKey("element2"));
            assertEquals("", SwitchConfigManager.getCustomElementSessionKey("nonexistent"));
        }

        // 测试 getCustomElementCategory
        assertEquals("category1", SwitchConfigManager.getCustomElementCategory("AccountObj", "element1"));
        assertEquals("", SwitchConfigManager.getCustomElementCategory("AccountObj", "element2"));
    }

    @Test
    public void testGrayTypeEnum() {
        // 测试 GrayType 枚举
        assertTrue(SwitchConfigManager.GrayType.gray.gray());
        assertTrue(SwitchConfigManager.GrayType.full.gray());
        assertFalse(SwitchConfigManager.GrayType.history.gray());
    }

    @Test
    public void testKidConfigEdgeCases() {
        SwitchConfigManager.KidConfig kidConfig = new SwitchConfigManager.KidConfig();

        // 测试空租户ID
        assertEquals(SwitchConfigManager.GrayType.history, kidConfig.needFilterMethod(""));
        assertEquals(SwitchConfigManager.GrayType.history, kidConfig.needFilterMethod(null));

        // 测试 grayEnable = false
        kidConfig.setGrayEnable(false);
        assertEquals(SwitchConfigManager.GrayType.full, kidConfig.needFilterMethod("anyTenant"));

        // 测试 grayEnable = true 但 detail 为 null
        kidConfig.setGrayEnable(true);
        kidConfig.setDetail(null);
        assertEquals(SwitchConfigManager.GrayType.history, kidConfig.needFilterMethod("tenant1"));

        // 测试 detail.enable = false
        SwitchConfigManager.Detail detail = new SwitchConfigManager.Detail();
        detail.setEnable(false);
        kidConfig.setDetail(detail);
        assertEquals(SwitchConfigManager.GrayType.history, kidConfig.needFilterMethod("tenant1"));
    }

    @Test
    public void testSwitchConfigEdgeCases() {
        // 测试空配置的情况
        SwitchConfigManager.SwitchConfig emptyConfig = new SwitchConfigManager.SwitchConfig();

        // 测试 ignoreDataVersion 为空
        assertFalse(emptyConfig.ignoreDataVersion("tenant1", "AccountObj"));

        // 测试 getCommonSystemButtons 为空
        List<String> buttons = emptyConfig.getCommonSystemButtons("AccountObj");
        assertTrue(buttons.isEmpty());

        // 测试 externalApplyTaskChangeCandidates 为空
        assertFalse(emptyConfig.externalApplyTaskChangeCandidates("tenant1", "action1"));

        // 测试 getTaskNotGetTask 为空
        assertFalse(emptyConfig.getTaskNotGetTask("tenant1"));

        // 测试 getOpenRefreshData 默认值
        assertFalse(emptyConfig.getOpenRefreshData());
    }

    private void setupStaticConfig() throws Exception {
        SwitchConfigManager.SwitchConfig mockConfig = new SwitchConfigManager.SwitchConfig();
        mockConfig.setSyncDataFailRemindEmpleyee(Lists.newArrayList(1, 2, 3));
        mockConfig.setSyncDataFailRetryCount(15);
        mockConfig.setOperateFailNoticeSessionUserId(Lists.newArrayList(100, 200));
        mockConfig.setSessionKeyCombine(Lists.newArrayList("tenant1", "*"));
        mockConfig.setOpenRefreshData(true);
        mockConfig.setSupportExternalApplyBusinessCode(Lists.newArrayList("code1", "code2"));

        // 设置 ignoreDataVersion
        Map<String, List<Object>> ignoreDataVersion = Maps.newHashMap();
        ignoreDataVersion.put("tenant1", Lists.newArrayList("AccountObj"));
        mockConfig.setIgnoreDataVersion(ignoreDataVersion);

        // 设置 objectCustomSystemBtns
        Map<String, List<String>> objectCustomSystemBtns = Maps.newHashMap();
        objectCustomSystemBtns.put("common", Lists.newArrayList("btn1", "btn2"));
        objectCustomSystemBtns.put("AccountObj", Lists.newArrayList("btn3"));
        mockConfig.setObjectCustomSystemBtns(objectCustomSystemBtns);

        // 设置 externalApplyTaskChangeCandidate
        Map<String, List<String>> externalApplyTaskChangeCandidate = Maps.newHashMap();
        externalApplyTaskChangeCandidate.put("action1", Lists.newArrayList("tenant1"));
        mockConfig.setExternalApplyTaskChangeCandidate(externalApplyTaskChangeCandidate);

        // 设置 getTaskNotGetTask
        mockConfig.setGetTaskNotGetTask(Lists.newArrayList("tenant1"));

        // 设置 supportExternalApplyBusinessCodeFilter
        Map<String, List<String>> filter = Maps.newHashMap();
        filter.put("AccountObj", Lists.newArrayList("filter1"));
        mockConfig.setSupportExternalApplyBusinessCodeFilter(filter);

        // 设置 GrayConfig
        SwitchConfigManager.SwitchConfig.GrayConfig grayConfig = new SwitchConfigManager.SwitchConfig.GrayConfig();
        grayConfig.setGray(true);
        grayConfig.setTenantIds(Sets.newHashSet("tenant1"));
        mockConfig.setSupportAllFormDescribe(grayConfig);

        // 设置 KidConfig
        SwitchConfigManager.KidConfig kidConfig = new SwitchConfigManager.KidConfig();
        kidConfig.setGrayEnable(true);
        SwitchConfigManager.Detail detail = new SwitchConfigManager.Detail();
        detail.setEnable(true);
        detail.setTenantIds(Lists.newArrayList("tenant1"));
        kidConfig.setDetail(detail);
        mockConfig.setInstanceListSkipDataPrivelige(kidConfig);
        mockConfig.setMergeTasksByLaneDataAndDesc(kidConfig);
        mockConfig.setUpgradeTaskInfo(kidConfig);

        // 通过反射设置静态字段
        Field switchConfigField = SwitchConfigManager.class.getDeclaredField("switchConfig");
        switchConfigField.setAccessible(true);
        switchConfigField.set(null, mockConfig);
    }

    private void setupCustomElementConfig() throws Exception {
        Map<String, SwitchConfigManager.CustomElementConfig> customElementConfig = Maps.newHashMap();
        customElementConfig.put("element1", new SwitchConfigManager.CustomElementConfig("session1", "category1"));
        customElementConfig.put("element2", new SwitchConfigManager.CustomElementConfig("session2", "category2"));

        Field customElementConfigField = SwitchConfigManager.class.getDeclaredField("customElementConfig");
        customElementConfigField.setAccessible(true);
        customElementConfigField.set(null, customElementConfig);
    }
}