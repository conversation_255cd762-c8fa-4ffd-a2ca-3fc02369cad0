package com.facishare.bpm.util;

import org.junit.Before;
import org.junit.Test;

import java.util.*;

import static org.junit.Assert.*;

public class SwitchConfigManagerTest {

    private SwitchConfigManager.SwitchConfig switchConfig;

    @Before
    public void setUp() {
        switchConfig = new SwitchConfigManager.SwitchConfig();
    }

    @Test
    public void testGetCustomElementSessionKey() {
        // 测试空输入
        assertEquals("", SwitchConfigManager.getCustomElementSessionKey(""));
        assertEquals("", SwitchConfigManager.getCustomElementSessionKey(null));

        // TODO: 需要配置实际的自定义元素配置进行测试
    }

    @Test
    public void testGetCustomElementCategory() {
        // 测试空输入
        assertEquals("", SwitchConfigManager.getCustomElementCategory("entityId", ""));
        assertEquals("", SwitchConfigManager.getCustomElementCategory("entityId", null));

        // TODO: 需要配置实际的自定义元素类别进行测试
    }

    @Test
    public void testIsSupportAllFormDescribe() {
        // 准备测试数据
        SwitchConfigManager.SwitchConfig.GrayConfig grayConfig = new SwitchConfigManager.SwitchConfig.GrayConfig();
        grayConfig.setGray(true);
        grayConfig.setTenantIds(new HashSet<>(Arrays.asList("tenant1", "tenant2")));
        switchConfig.setSupportAllFormDescribe(grayConfig);

        // 测试灰度租户
        assertTrue(grayConfig.supportTenantId("tenant1"));
        assertTrue(grayConfig.supportTenantId("tenant2"));
        assertFalse(grayConfig.supportTenantId("tenant3"));

        // 测试非灰度模式
        grayConfig.setGray(false);
        assertTrue(grayConfig.supportTenantId("anyTenant"));
    }

    @Test
    public void testInstanceListSkipDataPrivelige() {
        // 准备测试数据
        SwitchConfigManager.KidConfig kidConfig = new SwitchConfigManager.KidConfig();
        kidConfig.setGrayEnable(true);
        
        SwitchConfigManager.Detail detail = new SwitchConfigManager.Detail();
        detail.setEnable(true);
        detail.setTenantIds(Arrays.asList("tenant1", "tenant2"));
        kidConfig.setDetail(detail);

        // 测试灰度租户
        assertEquals(SwitchConfigManager.GrayType.gray, kidConfig.needFilterMethod("tenant1"));
        assertEquals(SwitchConfigManager.GrayType.gray, kidConfig.needFilterMethod("tenant2"));
        assertEquals(SwitchConfigManager.GrayType.history, kidConfig.needFilterMethod("tenant3"));

        // 测试全网发布
        kidConfig.setGrayEnable(false);
        assertEquals(SwitchConfigManager.GrayType.full, kidConfig.needFilterMethod("anyTenant"));
    }

    @Test
    public void testIgnoreDataVersion() {
        // 准备测试数据
        Map<String, List<Object>> ignoreDataVersion = new HashMap<>();
        ignoreDataVersion.put("tenant1", Arrays.asList("AccountObj", "*"));
        switchConfig.setIgnoreDataVersion(ignoreDataVersion);

        // 测试忽略版本
        assertTrue(switchConfig.ignoreDataVersion("tenant1", "AccountObj"));
        assertTrue(switchConfig.ignoreDataVersion("tenant1", "anyObject")); // 因为有通配符 *
        assertFalse(switchConfig.ignoreDataVersion("tenant2", "AccountObj"));
    }

    @Test
    public void testGetCommonSystemButtons() {
        // 准备测试数据
        Map<String, List<String>> objectCustomSystemBtns = new HashMap<>();
        objectCustomSystemBtns.put("common", Arrays.asList("btn1", "btn2"));
        objectCustomSystemBtns.put("AccountObj", Arrays.asList("btn3", "btn4"));
        switchConfig.setObjectCustomSystemBtns(objectCustomSystemBtns);

        // 测试获取按钮
        List<String> buttons = switchConfig.getCommonSystemButtons("AccountObj");
        assertEquals(4, buttons.size());
        assertTrue(buttons.containsAll(Arrays.asList("btn1", "btn2", "btn3", "btn4")));

        // 测试未配置对象
        buttons = switchConfig.getCommonSystemButtons("UnknownObj");
        assertEquals(2, buttons.size());
        assertTrue(buttons.containsAll(Arrays.asList("btn1", "btn2")));
    }

    @Test
    public void testExternalApplyTaskChangeCandidates() {
        // 准备测试数据
        Map<String, List<String>> changeCandidate = new HashMap<>();
        changeCandidate.put("action1", Arrays.asList("tenant1", "*"));
        switchConfig.setExternalApplyTaskChangeCandidate(changeCandidate);

        // 测试任务候选人变更
        assertTrue(switchConfig.externalApplyTaskChangeCandidates("tenant1", "action1"));
        assertTrue(switchConfig.externalApplyTaskChangeCandidates("tenant2", "action1")); // 因为有通配符 *
        assertFalse(switchConfig.externalApplyTaskChangeCandidates("tenant1", "action2"));
    }

    @Test
    public void testGetTaskNotGetTask() {
        // 准备测试数据
        switchConfig.setGetTaskNotGetTask(Arrays.asList("tenant1", "*"));

        // 测试任务获取限制
        assertTrue(switchConfig.getTaskNotGetTask("tenant1"));
        assertTrue(switchConfig.getTaskNotGetTask("anyTenant")); // 因为有通配符 *
    }

    @Test
    public void testGetOpenRefreshData() {
        // 测试数据刷新开关
        switchConfig.setOpenRefreshData(true);
        assertTrue(switchConfig.getOpenRefreshData());

        switchConfig.setOpenRefreshData(false);
        assertFalse(switchConfig.getOpenRefreshData());
    }
} 