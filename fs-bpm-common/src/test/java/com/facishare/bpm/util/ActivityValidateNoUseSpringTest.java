package com.facishare.bpm.util;

import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.util.verifiy.handler.ActivityHandler;
import com.facishare.bpm.utils.JsonUtil;
import com.google.common.collect.Maps;
import org.junit.Test;

import java.util.Map;

/**
 * desc:
 * version: 7.0.0
 * Created by cuiyongxu on 2020/8/5 7:46 PM
 */
public class ActivityValidateNoUseSpringTest {

    @Test
    public void test1(){
        ActivityHandler activityHandler = new ActivityHandler();

        Map<String,Object> bpmExtension = Maps.newHashMap();
        //bpmExtension.put("defaultButtons",null );
        //bpmExtension.put("defaultButtons", Lists.newArrayList() );
//        bpmExtension.put("defaultButtons", "" );


//        Map<String,Map> update = Maps.newHashMap();
//        Map<String,String> label = Maps.newHashMap();
//        label.put("Update","保存" );
//        update.put("update",label );
//        bpmExtension.put("defaultButtons", update );



        Map<String,String> update = Maps.newHashMap();
        update.put("update","保存" );
        bpmExtension.put("defaultButtons", update );




        UserTaskExt userTaskExt = new UserTaskExt();
        userTaskExt.setName("测试节点");
        userTaskExt.setProperty("bpmExtension",bpmExtension );
        activityHandler.validateDefaultButtons(userTaskExt);

        System.out.println(JsonUtil.toJsonWithNull(userTaskExt));
    }
}
