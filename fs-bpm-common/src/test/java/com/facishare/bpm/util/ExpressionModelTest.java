package com.facishare.bpm.util;

import com.facishare.bpm.util.verifiy.handler.AssigneeHandler;
import com.facishare.rest.core.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * desc:
 * version: 6.5
 * Created by cuiyongxu on 2018/12/13 4:00 PM
 */
@Slf4j
public class ExpressionModelTest {

    /**
     * //activity_1526626713317##object_jw9Lf__c##field_dids__c##group$$数据相关人员-**** => {activity_1526626713317,object_jw9Lf__c,field_dids__c,group$$数据相关人员-****}
     * //activity_1526626713317##object_jw9Lf__c##owner => {activity_1526626713317,object_jw9Lf__c,owner$$数据相关人员-****}
     * //activity_1526626713317##object_jw9Lf__c##dept=> {activity_1526626713317,object_jw9Lf__c,dept$$数据相关人员-****}
     */
    @Test
    public void expressionModel() {
        log.info(JacksonUtil.toJson(new AssigneeHandler.ExpressionModel().getExpressionModel("activity_1526626713317##object_jw9Lf__c##field_dids__c##group$$数据相关人员")));
        log.info(JacksonUtil.toJson(new AssigneeHandler.ExpressionModel().getExpressionModel("activity_1526626713317##object_jw9Lf__c##owner")));
        log.info(JacksonUtil.toJson(new AssigneeHandler.ExpressionModel().getExpressionModel("activity_1526626713317##object_jw9Lf__c##dept")));
        log.info(JacksonUtil.toJson(new AssigneeHandler.ExpressionModel().getExpressionModel("activity_1543313205151##SelfRef##object_Xp51d__c##owner$$数据负责人")));
        // 修复：移除无效的表达式调用，因为 "activity_1526626713317##object_jw9Lf__c" 只有2个部分，不符合要求
        // log.info(JacksonUtil.toJson(new AssigneeHandler.ExpressionModel().getExpressionModel("activity_1526626713317##object_jw9Lf__c")));
    }
}
