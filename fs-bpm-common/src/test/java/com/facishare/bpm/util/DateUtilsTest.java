package com.facishare.bpm.util;

import com.facishare.flow.test.I18nTestHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * DateUtils的单元测试类
 * 
 * <AUTHOR>
 * @since 7.0.0
 */
public class DateUtilsTest {
    @Before
    public void init() {
        I18nTestHelper.setupI18nClientStub();
    }
    @Test
    public void testCurrentTimeMilliFormat() {
        // 测试指定时间
        long testTime = 1708999200000L; // 2024-02-27 10:00:00:000
        String formattedTime = DateUtils.currentTimeMilliFormat(testTime);
        Assert.assertEquals("2024-02-27 10:00:00:000", formattedTime);

        // 测试空值
        Assert.assertEquals("", DateUtils.currentTimeMilliFormat(null));

        // 测试边界时间
        Assert.assertNotEquals("", DateUtils.currentTimeMilliFormat(0L));
        Assert.assertNotEquals("", DateUtils.currentTimeMilliFormat(Long.MAX_VALUE));
    }

    @Test
    public void testGetFormatDate() {
        // 测试指定时间
        long testTime = 1708999200000L; // 2024-02-27 10:00
        String formattedDate = DateUtils.getFormatDate(testTime);
        Assert.assertEquals("2024-02-27 10:00", formattedDate);

        // 测试空值
        Assert.assertEquals("", DateUtils.getFormatDate(null));

        // 测试当前时间
        long currentTime = System.currentTimeMillis();
        String currentFormatted = DateUtils.getFormatDate(currentTime);
        Assert.assertNotNull(currentFormatted);
        Assert.assertFalse(currentFormatted.isEmpty());

        // 测试格式是否符合预期
        SimpleDateFormat expectedFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        String expected = expectedFormat.format(new Date(testTime));
        Assert.assertEquals(expected, DateUtils.getFormatDate(testTime));
    }

    @Test
    public void testDateFormatThreadSafety() throws InterruptedException {
        // 测试多线程并发情况下的日期格式化
        Runnable dateFormatTask = () -> {
            for (int i = 0; i < 100; i++) {
                long time = System.currentTimeMillis();
                String formatted = DateUtils.getFormatDate(time);
                Assert.assertNotNull(formatted);
                Assert.assertFalse(formatted.isEmpty());
            }
        };

        // 创建多个线程同时执行日期格式化
        Thread[] threads = new Thread[10];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(dateFormatTask);
            threads[i].start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
    }

    @Test
    public void testFormatConsistency() {
        // 测试同一时间戳多次格式化的一致性
        long testTime = System.currentTimeMillis();
        String firstFormat = DateUtils.getFormatDate(testTime);
        
        for (int i = 0; i < 10; i++) {
            Assert.assertEquals("格式化结果应该保持一致", 
                firstFormat, 
                DateUtils.getFormatDate(testTime));
        }
    }
}
