package com.facishare.bpm.util;

import com.facishare.bpm.model.paas.engine.bpm.Reminder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.List;

@Slf4j
public class ReminderValidateTest {

    @Test
    public void testRemind() {
        Reminder model = new Reminder();
        model.setRemindContent("提醒内容");
        model.setRemindStrategy(1);
        model.setRemindTime(-24);// 单位永远是小时


        List<Reminder> reminders = Lists.newArrayList(model);
        Object remindLatency = 1;
        //unit:1-天；2-小时；3-分钟
        Integer finalLatencyUnit = 1;

        reminders.forEach(reminder -> {
            //超时前 多少小时
            int remindTime = reminder.getRemindTime();
            // 超时后 超时前 小时大于0
            //只校验超时前(负数)；不需校验超时后(正数),完成时(0)
            if (reminder.getRemindStrategy() == 1 && remindTime < 0) {
                if (remindLatency instanceof Integer) {
                    //节点上允许停留时长
                    Integer remindLatencyNumber = (Integer) remindLatency;

                    // 允许停留时长不能小于0
                    if (remindLatencyNumber <= 0) {
                        throw new RuntimeException(" 允许停留时长设置有误,请检查");
                    }

                    //弹出框内设置的,只能是小时
                    int count = Math.abs(remindTime);
                    int intactCount = 0;
                    // 校验小时
                    if (finalLatencyUnit == 2) {
                        intactCount = remindLatencyNumber;
                    } else if (finalLatencyUnit == 1) {
                        //天,remindLatencyNumber为设置允许停留时长的数值,乘以24 表示1天24个小时
                        intactCount = remindLatencyNumber * 24;
                    } else if (finalLatencyUnit == 3) {
                        //分钟
                        intactCount = remindLatencyNumber / 60;
                    }
                    if (count > intactCount) {
                        throw new RuntimeException(" 超时提醒 提醒时间设置超出允许停留时间,请检查");
                    }
                }
            }


        });
    }
}
