package com.facishare.bpm.util;

import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;
import org.apache.commons.io.IOUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

/**
 * desc:
 * version: 6.6
 * Created by cuiyongxu on 2019/6/5 3:18 PM
 */
public class CurlGenerate {


    String curlMSG = "curl -X POST \\\n" +
            "  http://paas.nsvc.foneshare.cn/fs-paas-workflow/paas/bpm/cancel/type \\\n" +
            "  -H 'accept-language: zh' \\\n" +
            "  -H 'cache-control: no-cache' \\\n" +
            "  -H 'content-type: application/json' \\\n" +
            "  -H 'fsr-gray_value: {0}' \\\n" +
            "  -H 'postman-token: 6720fb9a-6ef2-95db-5fbd-0a66ad7a8356' \\\n" +
            "  -H 'x-fs-peer-host: ************' \\\n" +
            "  -H 'x-fs-peer-name: fs-bpm-cancel-custom' \\\n" +
            "  -H 'x-trace: traceId={1};userId=worldnyjx.2496;color=false;rpcId=0.3.3;jaegerTraceId=-7747523566154829049;jaegerSpanId=-7747523566154829049' \\\n" +
            "  -d '{\"workflowInstanceId\":\"{2}\",\"cancelReason\":\"由于数据已作废,系统自动种植该流程\",\"context\":{\"tenantId\":\"{0}\",\"appId\":\"BPM\",\"userId\":\"-10000\"}}'";

    @Test
    public void generate() {
        try {
            Arg arg = JsonUtil.fromJson(IOUtils.toString(getClass().getResourceAsStream("/data/error_data.json")), Arg.class);
            String tenantId = arg.getTenantId();
            System.out.println("企业Id:" + tenantId);
            arg.getData().forEach(data -> {
                String instanceId = data.get(0);
                String desc = data.get(1);

                String curlMSG = "curl -X POST \\\n" +
                        "  http://paas.nsvc.foneshare.cn/fs-paas-workflow/paas/bpm/cancel/type \\\n" +
                        "  -H 'accept-language: zh' \\\n" +
                        "  -H 'cache-control: no-cache' \\\n" +
                        "  -H 'content-type: application/json' \\\n" +
                        "  -H 'fsr-gray_value: " + tenantId + "' \\\n" +
                        "  -H 'postman-token: 6720fb9a-6ef2-95db-5fbd-0a66ad7a8356' \\\n" +
                        "  -H 'x-fs-peer-host: ************' \\\n" +
                        "  -H 'x-fs-peer-name: fs-bpm-cancel-custom' \\\n" +
                        "  -H 'x-trace: traceId=" + UUID.randomUUID() + ";userId=" + tenantId + ".-10000;color=false;rpcId=0.3.3;jaegerTraceId=-7747523566154829049;jaegerSpanId=-7747523566154829049' \\\n" +
                        "  -d '{\"workflowInstanceId\":\"" + instanceId + "\",\"cancelReason\":\"由于数据已作废,系统自动终止该流程\",\"context\":{\"tenantId\":\"" + tenantId + "\",\"appId\":\"BPM\",\"userId\":\"-10000\"}}'";
                System.out.println(curlMSG);
                System.out.println();
                //System.out.println("instanceId:" + instanceId + ",desc:" + desc);
            });

        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}

@Data
class Arg {
    private List<List<String>> data;
    private String tenantId;
}