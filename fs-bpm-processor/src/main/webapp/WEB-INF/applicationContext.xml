<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <bean class="com.facishare.bpm.utils.SpringUtils" lazy-init="false"/>
    <context:component-scan base-package="com.facishare.bpm,com.facishare.bpmn.syn,com.facishare.flow,com.facishare.flow.element"/>

    <context:annotation-config/>
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <context:property-placeholder location="classpath:application.properties"/>

    <import resource="classpath:spring/fs-bpm-rest-proxy.xml"/>
    <import resource="classpath:spring/fs-bpm-aop.xml"/>
    <import resource="classpath:spring/fs-bpm-fsi-proxy.xml"/>
    <import resource="classpath:spring/fs-bpm-mq.xml"/>
    <import resource="classpath:spring/fs-bpm-fail-over.xml"/>
    <import resource="classpath:spring/license-client.xml"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <import resource="classpath*:spring/fs-bpm-element-extension.xml"/>
    <import resource="classpath:spring/flow-public-db-config.xml"/>

    <bean id="bpmMetadataServiceImpl" class="com.facishare.bpm.remote.metadata.impl.MetadataServiceImpl"/>
    <import resource="classpath:spring/spring-flow-redis.xml"/>

</beans>
