<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">
    <!--   增加 角色拍平的处理 http://wiki.firstshare.cn/pages/viewpage.action?pageId=152183001 -->
    <bean id="erRetrofitFactory" class="com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory" p:configNames="fs-enterpriserelation-rest-api" init-method="init">
        <property name="headers">
            <map>
                <entry key="x-eip-appid" value="defaultApp"/>
            </map>
        </property>
        <property name="okHttpSupport" ref="erOkHttpSupport"/>
    </bean>
    <bean id="erOkHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-rest-api-http-support"/>

    <bean id="enterpriseCardService" class="com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean" p:type="com.fxiaoke.enterpriserelation2.service.EnterpriseCardService" p:factory-ref="erRetrofitFactory"/>

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-flow-rest-proxy-config" init-method="init"/>

    <!-- paas权限服务 -->
    <bean id="paasAuthResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasAuthResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="authServiceProxy" class="com.facishare.bpm.proxy.AuthServiceProxy">
        <property name="paasAuthResource" ref="paasAuthResource"/>
    </bean>

    <!-- 元数据权限服务 -->
    <bean id="metaDataAuthResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.MetaDataAuthResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="metaDataAuthProxy" class="com.facishare.bpm.proxy.MetaDataAuthProxy">
        <property name="metaDataAuthResource" ref="metaDataAuthResource"/>
        <property name="newPaasMetadataOthersResource" ref="newPaasMetadataOthersResource"/>
        <property name="apiBusPaasMetadata" ref="apiBusPaasMetadata"/>
        <property name="enterpriseCardService" ref="enterpriseCardService"/>
    </bean>


    <!-- 元数据服务 -->
    <bean id="paasMetadataResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasMetadataResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="metadataOptionsProxy" class="com.facishare.bpm.proxy.MetadataOptionsProxy">
        <property name="paasMetadataResource" ref="paasMetadataResource"/>
    </bean>
    <bean id="paasMetadataProxy" class="com.facishare.bpm.proxy.PaasMetadataProxy">
        <property name="paasMetadataResource" ref="paasMetadataResource"/>
        <property name="paasCustomObjectResource" ref="paasCustomObjectResource"/>
    </bean>

    <!-- 新元数据服务 -->
    <bean id="paasCustomObjectResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasCustomObjectResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasNewMetadataResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.NewPaasMetadataResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasNewMetadataProxy" class="com.facishare.bpm.proxy.NewPaasMetadataProxy">
        <property name="paasCustomObjectResource" ref="paasCustomObjectResource"/>
        <property name="newPaasMetadataResource" ref="paasNewMetadataResource"/>
        <property name="enterpriseCardService" ref="enterpriseCardService"/>
    </bean>

    <!-- 老对象相关接口 -->
    <bean id="crmResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.CRMResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <!-- license服务 -->
    <bean id="paasLicense" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasLicenseResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasLicenseProxy" class="com.facishare.bpm.proxy.PaasLicenseProxy">
        <property name="paasLicenseResource" ref="paasLicense"/>
    </bean>

    <!-- workflow engine -->
    <bean id="paasWorkflowResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.remote.BPMPaasWorkflowResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <!--后动作服务 -->
    <bean id="afterResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.AfterResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <!-- organization proxy-->
    <bean id="paasOrgResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasOrgResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="organizationServiceProxy" class="com.facishare.bpm.proxy.OrganizationServiceProxy">
        <property name="paasOrgResource" ref="paasOrgResource"/>
        <property name="enterpriseEditionProxy" ref="enterpriseEditionProxy"/>
        <property name="enterpriseProxy" ref="enterpriseProxy"/>
    </bean>


    <!-- app -->
    <bean id="appResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean" p:type="com.facishare.bpm.resource.AppResource" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="newPaasMetadataOthersResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.NewPaasMetadataOthersResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="sfaBusinessResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.SFABusinessResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="sfaBusinessProxy" class="com.facishare.bpm.proxy.SFABusinessProxy">
        <property name="sfaBusinessResource" ref="sfaBusinessResource"/>
    </bean>

    <bean id="apiBusPaasMetadata" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.ApiBusPaasMetadata">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <!-- 深研服务 -->
    <bean id="enterpriseRelationResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.EnterpriseRelationResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="enterpriseProxy" class="com.facishare.bpm.proxy.EnterpriseProxy">
        <property name="enterpriseRelationResource" ref="enterpriseRelationResource"/>
    </bean>

    <!--服务通服务-->
    <bean id="eServiceResourceProxy" class="com.facishare.bpm.proxy.EServiceResourceProxy">
        <property name="EServiceResource" ref="eServiceResource"/>
    </bean>
    <bean id="eServiceResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.EServiceResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="paasLogResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasLogResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="paasLogProxy" class="com.facishare.bpm.proxy.PaasLogProxy">
        <property name="paasLogResource" ref="paasLogResource"/>
    </bean>
    <bean id="flowServiceSessionProxy" class="com.facishare.bpm.proxy.FlowServiceSessionProxy">
        <property name="sessionResource" ref="sessionResource"/>
    </bean>
    <bean id="sessionResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.SessionResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="enterpriseEditionProxy" class="com.facishare.bpm.proxy.EnterpriseEditionProxy">
        <property name="enterpriseConfigService" ref="orgAdapterApiEnterpriseConfigService"/>
    </bean>
    <!--fs-orgainzation-adapter-api -->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>

    <bean id="dataPrivilegeProxy" class="com.facishare.bpm.proxy.DataPrivilegeProxy">
        <property name="paasDataPrivilegeResource" ref="paasDataPrivilegeResource"/>
        <property name="enterpriseCardService" ref="enterpriseCardService"/>
    </bean>
    <bean id="paasDataPrivilegeResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.PaasDataPrivilegeResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="manageGroupProxy" class="com.facishare.bpm.proxy.ManageGroupProxy">
        <property name="manageGroupResource" ref="manageGroupResource"/>
    </bean>

    <bean id="manageGroupResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.ManageGroupResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="openEmailProxy" class="com.facishare.bpm.proxy.OpenEmailProxy">
        <property name="openEmailResource" ref="crmTemplateResource"/>
    </bean>
    <bean id="crmTemplateResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.OpenEmailResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="webPageResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.bpm.resource.WebPageResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="webPageProxy" class="com.facishare.bpm.proxy.WebPageProxy">
        <property name="webPageResource" ref="webPageResource"/>
    </bean>
    <bean id="i18nServiceResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean" p:type="com.facishare.bpm.resource.I18nServiceResource" >
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="i18NServiceProxy" class="com.facishare.bpm.proxy.I18NServiceProxy">
        <property name="i18nServiceResource" ref="i18nServiceResource"/>
    </bean>
</beans>
