package com.facishare.bpm.service;

import com.google.common.collect.Maps;

import com.facishare.bpm.model.WorkflowOutline;
import com.facishare.bpm.remote.metadata.MetadataService;
import com.facishare.bpm.model.paas.engine.bpm.TriggerSource;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.rest.core.model.RemoteContext;
import com.facishare.rest.core.util.JsonUtil;

import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by <PERSON> on 28/12/2016.
 */
@Slf4j
public class BaseTest extends BPMBaseService{

    static {
//        System.setProperty("-Dspring.profiles.active","ceshi113");
        System.setProperty("-Dspring.profiles.active","fstest");

    }

    @Autowired
    protected BPMDefinitionService definitionService;
    @Autowired
    protected BPMInstanceService instanceService;
    @Autowired
    protected MetadataService metadataService;
    protected String tenantId = "54821";
    protected String ea = "54821";
    protected String userId = "2003";
    protected String entityId = "AccountObj";
    protected RemoteContext context = new RemoteContext(ea, tenantId, BPMConstants.APP_ID, userId);

    protected String startNewWorkflow() {
        String outlineId = deploySimpleProcess();
        String dataId = createNewAccount();

        String instanceId = instanceService.startWorkflow(getServiceManager(context), TriggerSource.approval, outlineId, dataId);
        log.debug("=============================instanceId={}", instanceId);
        return instanceId;
    }

    protected String createNewAccount(){
        Map<String, String> account = Maps.newHashMap();
        String name = String.valueOf(System.currentTimeMillis());
        account.put("remark","bpm简单测试数据");
        account.put("tel", name);
        account.put("email","bpm_"+name+"@bpm.cn");
        account.put("tenant_id", tenantId);
        account.put("url","www.bpm.cn");
        account.put("name","bpm_"+name);
        account.put("detail_address","bpm701");
        String id = (String)metadataService.createData(context, entityId, JsonUtil.toJson(account)).get("_id");
        log.debug("================================data id={}", id);
        return id;
    }

    protected String deploySimpleProcess(){
        String outlineId = deployProcess("SimpleTestProcess.json");
        log.debug("==========================outlineId={}", outlineId);
        return outlineId;
    }

    protected String deployProcess(String processFile){
        String processJson = getOutlineJsonFromFile(processFile);
        WorkflowOutline outline = JsonUtil.fromJson(processJson, WorkflowOutline.class);
        return definitionService.deployWorkflow(getServiceManager(context), outline,true,true);
    }

    protected String getOutlineJsonFromFile(String processFile) {
        String path = BaseTest.class.getResource("/").getPath() + "/doc/" + processFile;

        FileInputStream is = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        StringBuilder sb = null;
        try {
            is = new FileInputStream(path);
            isr = new InputStreamReader(is);
            br = new BufferedReader(isr);
            sb = new StringBuilder();
            while (true) {
                String temp = br.readLine();
                if (temp == null) {
                    break;
                }
                sb.append(temp.trim());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (isr != null) {
                    br.close();
                }
                if (is != null) {
                    br.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return sb.toString();
    }

    void deleteWorkflowOutline(String outlineId){
        definitionService.deleteWorkflowById(getServiceManager(context), outlineId);
    }
}
